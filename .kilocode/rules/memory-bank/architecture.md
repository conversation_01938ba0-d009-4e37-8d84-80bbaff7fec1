# CloudQuery 云查询平台 - 架构概览

## 1. 系统架构
CloudQuery 前端是一个基于 React 的单页应用程序 (SPA)，采用模块化设计，旨在提供一个安全、高效且可扩展的数据库查询与管理界面。

### 1.1 技术栈概览
- **前端框架**：React 16.13.1
- **编程语言**：TypeScript
- **UI 组件库**：Ant Design 4.6.5
- **状态管理**：Redux Toolkit (RTK) with Redux Persist
- **路由**：React Router DOM
- **构建工具**：Create React App (CRA) with CRACO (用于扩展 webpack 配置)
- **SQL 编辑器**：Monaco Editor (支持 MySQL, SQL, PostgreSQL, Redis 语法高亮和核心命令)
- **工作流引擎集成**：BPMN.js
- **数据表格**：AG Grid (通过样式文件推断)
- **图表**：Echarts, G2 (通过 `package.json` 依赖推断)
- **网络通信**：RESTful API (通过 `craco.config.js` 配置代理) 和 WebSocket (用于实时通信)
- **国际化**：i18next

### 1.2 部署架构 (前端部分)
- **单页应用**：编译后生成静态文件，可部署在任何静态文件服务器或 CDN 上。
- **API 交互**：通过 `craco.config.js` 中配置的代理规则 (`/dms`, `/user`, `/audit` 等) 转发请求到后端服务。WebSocket 连接也通过代理建立。

## 2. 源代码路径与模块划分

### 2.1 核心入口
- [`src/index.tsx`](src/index.tsx)：应用入口文件，负责 Redux store 和 Redux Persist 的初始化，并渲染根组件 `App`。
- [`src/App.tsx`](src/App.tsx)：主应用容器，处理全局配置（如 Ant Design 语言包、i18n）、路由管理，并根据用户登录状态条件渲染 `Login` 或 `Main` 组件。
- [`src/appPages/main/Main.tsx`](src/appPages/main/Main.tsx)：主应用程序布局，包含 `HeaderNav`、`QueryPoolPane` 等核心组件，并根据路由动态加载 `pageTabs` 下的各个功能模块。

### 2.2 模块结构
- [`src/appPages/`](src/appPages/)：存放主要的应用页面，如登录页 (`Login`)、主页 (`Main`)、错误页 (`403`) 等。
- [`src/pageTabs/`](src/pageTabs/)：存放各个业务功能模块的入口组件，每个子目录代表一个独立的业务功能，例如：
    - `queryPage/`：SQL 查询和结果展示模块。
    - `settingPage/`：系统设置模块。
    - `audit/`：审计功能模块。
    - `access-request/`：访问请求管理模块。
    - `database-management/`：数据库连接管理模块。
    - `flowPages/`：工作流与审批模块。
    - `dataProtection/`：数据脱敏与保护模块。
    - `dataTransfer/`：数据传输模块。
    - `monitorPages/`：监控模块。
    - `organizationPage/`：组织与用户管理模块。
- [`src/components/`](src/components/)：存放可复用的 UI 组件和通用功能组件，例如 `ErrorBoundary`、`DiffEditor`、`Bpmn` 等。
- [`src/store/`](src/store/)：Redux 状态管理模块，包含各个业务模块的 slice 定义和根 reducer。
- [`src/api/`](src/api/)：封装后端 API 接口调用。
- [`src/hook/`](src/hook/)：自定义 React Hook，封装可复用的逻辑。
- [`src/util/`](src/util/) 和 [`src/utils/`](src/utils/)：通用工具函数库。
- [`src/styles/`](src/styles/)：全局样式文件 (SCSS, Less)。
- [`src/features/`](src/features/)：特定功能实现，如 WebSocket 连接 (`websocket/`)、权限管理 (`perm/`)、向导 (`wizards/`)。
- [`src/constants/`](src/constants/)：存放应用常量。
- [`src/projectConfig/`](src/projectConfig/)：项目配置，例如路由菜单配置。

## 3. 关键技术决策
- **CRACO 扩展 CRA**：允许在不 eject 的情况下自定义 Webpack 配置，例如集成 Monaco Editor 和 Bundle Analyzer。
- **Monaco Editor**：为 SQL 查询提供了强大的代码编辑能力，支持多种数据库语言的语法高亮。
- **Redux Toolkit + Redux Persist**：简化 Redux 开发，提供高效的状态管理，并通过 Redux Persist 实现状态的持久化存储。
- **多语言支持**：通过 `i18next` 实现应用国际化。
- **代理配置**：开发环境中通过 `craco.config.js` 配置代理，解决跨域问题，并统一管理后端服务接口。

## 4. 设计模式
- **组件化**：基于 React 的组件化开发，提高代码复用性和可维护性。
- **单向数据流**：遵循 React 和 Redux 的单向数据流原则。
- **模块化**：清晰的目录结构和模块划分，使各功能模块职责单一，易于管理。
- **自定义 Hook**：封装复杂逻辑，提高组件的复用性。
- **全局状态管理**：关键组件如 `QueryPoolPane` 在应用顶层全局渲染，确保跨页面的状态一致性。

## 5. 核心业务流程与实现路径

### 5.1 主要业务流程
- **用户认证流程**：`src/appPages/login` 模块负责用户登录、认证、重置密码及相关逻辑。
- **SQL 查询与执行**：`src/pageTabs/queryPage` 模块负责 SQL 查询编辑（集成 Monaco Editor）、查询结果展示、查询历史管理等。
- **数据库连接管理**：`src/pageTabs/connectionManagementPage` 模块与 `src/api/databaseManagement` 交互，负责数据库连接的配置与管理。
- **工作流审批**：`src/pageTabs/flowPages` 模块集成 `src/components/Bpmn` 组件，实现危险 SQL 操作的审批流程。
- **数据安全与合规**：`src/pageTabs/dataProtection` 和 `src/pageTabs/audit` 模块负责数据脱敏、审计日志记录等功能。

### 5.2 SQL 查询轮询机制 (核心架构)
**实现位置**: `src/components/queryPoolPane/QueryPoolPane.tsx`
**设计特点**:
- **全局轮询管理**: `QueryPoolPane` 在 `Main.tsx` 中全局渲染，永不卸载，管理所有查询标签的轮询状态
- **分段查询机制**: 后端将复杂查询分解为多个执行片段 (`execSegment`)，前端通过轮询 `getSegmentResults` API 获取执行状态
- **递归轮询**: 使用 `recursiveQuery` 函数通过 `setTimeout` 实现递增延迟的轮询（100ms→3000ms）
- **内存管理策略**: 数据传递给 Redux 后立即清理本地缓存，避免永不卸载组件的内存积累问题

**关键数据流**:
```
SQL执行请求 → 后端分段处理 → 轮询getSegmentResults → moreOperationAfterExecute → Redux存储 → 本地缓存清理
```

### 5.3 内存管理架构设计
由于 CloudQuery 采用全局组件永不卸载的设计，内存管理需要特殊考虑：

**设计原则**:
- **主动清理**: 数据使用完成后立即清理，不依赖组件卸载
- **生命周期管理**: 轮询数据有明确的生命周期（开始→处理→清理）
- **状态隔离**: 不同查询标签的状态完全隔离，避免互相影响
- **避免第三方库缓存**: 优先使用原生 API 调用，避免第三方库的内部缓存机制

**实现策略**:
- `moreOperationAfterExecute` 完成后立即清理 `results.current`
- 轮询结束时清理相关状态（`params`、`highRiskResultInfoList`）
- 避免不必要的深拷贝操作，减少内存开销
- 正确配置 React Hook 依赖数组，避免闭包引用问题
- **关键修复**: MonacoPane.tsx 中使用原生 async/await 替代 useRequest，避免内部缓存引用

**内存泄漏修复案例**:
```typescript
// ❌ 问题代码：useRequest 内部缓存导致内存泄漏
const { runAsync: execSegment } = useRequest(executeSqlSegment, {
  manual: true,
  cacheTime: 0,
  staleTime: 0,
  onSuccess: handleExecuteSuccess,
  onError: handleExecuteError,
})

// ✅ 修复代码：原生 async/await 避免内部缓存
const execSegment = useCallback(async (params: any) => {
  try {
    const data = await executeSqlSegment({ ...params });
    // 手动处理成功回调
    if (data?.messageId) {
      dispatch(saveExecuteActiveTabInfo({ key: data.messageId, data }));
    }
    return data;
  } catch (error) {
    // 手动处理错误回调
    dispatch(setTabExecutionStatus({ key: activePaneInfo?.key, pending: false }));
    throw error;
  }
}, [dispatch, activePaneInfo?.key, t]);
```

## 6. 性能优化关键点

### 6.1 内存优化
- **数据清理时机**: 数据传递给 Redux 后立即清理本地缓存
- **深拷贝优化**: 移除不必要的 `cloneDeep` 操作
- **引用链管理**: 避免长期持有大对象引用
- **第三方库缓存**: 避免使用有内部缓存机制的第三方库（如 useRequest）
- **原生 API 优先**: 优先使用原生 fetch/async-await，避免额外的抽象层

### 6.2 轮询优化
- **递增延迟**: 轮询延迟从 100ms 递增到 3000ms，减少服务器压力
- **状态管理**: 轮询结束时及时清理相关状态

### 6.3 React Hook 优化
- **依赖数组精确性**: 确保 useCallback/useEffect 依赖数组的准确性
- **闭包引用控制**: 避免在回调中持有大对象的引用
- **函数稳定性**: 使用 useCallback 确保函数引用稳定，避免不必要的重新创建

## 7. Mermaid 流程图示例 (抽象化工作流审批流程)
```mermaid
graph TD
    A[用户提交SQL操作] --> B{是否需要审批?};
    B -- 是 --> C[创建审批请求];
    C --> D[通知审批人];
    D --> E{审批人审批};
    E -- 拒绝 --> F[审批结束 (拒绝)];
    E -- 批准 --> G[执行SQL操作];
    B -- 否 --> G;
    G --> H[记录审计日志];
    H --> I[完成];