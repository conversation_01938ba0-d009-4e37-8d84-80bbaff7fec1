# CloudQuery 云查询平台 - 当前开发状态

## 当前工作重点
- **✅ 内存泄漏修复已完成**：成功解决了 MonacoPane.tsx 中 useRequest 导致的内存泄漏问题
- **✅ 方案3实施成功**：通过原生 async/await 替代 useRequest，彻底避免了内部缓存和闭包引用问题
- **架构理解深化**：完善了对 CloudQuery 全局组件架构和内存管理策略的理解
- **Memory Bank 更新完成**：项目架构文档和任务记录已更新
- **✅ Alert 组件内存泄漏修复**：已将 Alert 组件重构为 [`AlertManager`](src/pageTabs/queryPage/queryTabs/monacoPane/AlertManager.tsx)，并在 [`MonacoPane.tsx`](src/pageTabs/queryPage/queryTabs/monacoPane/MonacoPane.tsx) 中实现受控生命周期管理，彻底解决了 Alert 相关的内存泄漏问题。
- **✅ 资源清理机制完善**：所有资源（Monaco Editor model、装饰器、定时器等）已集中清理，标签页关闭/切换时均调用清理函数。
- **引用链排查持续进行中**：已完成对主要引用链的排查与断开，下一步将继续针对其他潜在引用链进行分析和优化。

## 最近的变更
- 已完成对项目 `package.json` 文件的分析，了解了核心依赖和基本信息。
- 已初步探索了项目的整体架构和文件组织结构，特别是 `src` 目录下的主要模块划分。
- 已分析了 `src/App.tsx`, `src/index.tsx`, `src/appPages/main/Main.tsx` 等核心入口文件，识别了应用的主要组件和路由管理。
- 已检查了 `tsconfig.json` 和 `craco.config.js` 等配置文件，了解了开发环境设置和构建配置。
- 已创建了 `product.md` 文件，描述了 CloudQuery 的产品愿景、核心功能模块和用户体验目标。
- 已创建了 `architecture.md` 文件，记录了系统的架构概览、源代码路径与模块划分、关键技术决策、设计模式和核心业务流程。
- 已创建了 `tech.md` 文件，详细列出了项目的技术栈、开发环境与工具链以及关键依赖。
- **已分析 `execSegment` 引用链的来源和产生原因：**
    - `execSegment` 并非源代码中明确定义的变量名，而是在浏览器调试工具中显示的一个运行时对象。它代表了 SQL 查询执行过程中的一个“执行片段”或“执行分段”的信息。
    - 其原始数据来源于后端 API 的响应，具体通过 `getSegmentResults` 函数获取（该函数返回 `MultiQueryResult` 类型的数据）。
    - 产生原因是 CloudQuery 平台为了处理复杂 SQL 查询、实现实时状态更新和结果展示而设计的一套机制。后端将查询分解为多个分段，前端通过轮询 `getSegmentResults` 获取每个分段的执行状态和结果。
    - `execSegment` 及其嵌套结构是前端为了管理和渲染这些查询执行片段而创建的内部数据结构，并与 Redux 和 React Context 结合使用。
- **已深入分析 `fetch` 请求在 SQL 查询轮询中保持引用的原因：**
    - 在 `src/components/queryPoolPane/QueryPoolPane.tsx` 的 `recursiveQuery` 函数中，通过 `setTimeout` 实现递归调用和轮询 `src/api/query.ts` 中的 `getSegmentResults` 函数。
    - `getSegmentResults` 函数内部使用 `fetch` 发起网络请求，并返回一个 `Promise`。
    - 如果 `recursiveQuery` 的递归调用链在 `tab` 关闭时未能及时停止，`setTimeout` 的回调会持续存在于事件队列中，形成闭包，并捕获 `recursiveQuery` 函数实例及其 `Promise` 链。
    - 这条 `Promise` 链会持续持有对 `getSegmentResults` 返回的 `Promise`（代表 `fetch` 请求）的引用，进而间接持有对底层 `fetch` 响应对象和其内部数据的引用。即使 `fetch` 请求本身已完成，只要 `Promise` 链未被垃圾回收，相关内存便无法释放，导致内存泄漏。图片中 `state: Fetch` 的存在正是这一现象的体现。
    - 即使 Redux 不是 `fetch` 引用产生的直接原因，但如果 `fetch` 返回的数据被存储到 Redux store 且未被清理，也会加剧内存泄漏。

## 已完成的内存优化工作
- **✅ 数据及时清理**：在 `moreOperationAfterExecute` 执行完成后立即清理 `results.current`
- **✅ 移除不必要的深拷贝**：移除了 `cloneDeep(data)` 操作，减少内存开销
- **✅ useRequest 内存泄漏修复**：通过方案3彻底解决了 MonacoPane.tsx 中的内存泄漏问题
- **✅ 原生 API 调用**：将 execSegment 从 useRequest 改为原生 async/await 实现

## 最新解决的关键问题
- **✅ useRequest 闭包引用**：useRequest 内部缓存机制导致的组件上下文引用已解决
- **✅ execSegment 多实例问题**：通过原生实现避免了 useRequest 的内部状态管理
- **✅ QueryPool 调用优化**：改为异步调用，避免了 Promise.resolve() 的额外包装

## 完成的重要工作
- **✅ 内存泄漏根因分析**：识别出永不卸载组件的内存管理挑战
- **✅ 数据清理策略**：实现了数据传递给 Redux 后立即清理本地缓存的策略
- **✅ 轮询优化**：优化了轮询结束时的状态清理逻辑
- **✅ 性能优化**：移除不必要的深拷贝操作，修复了 React Hook 依赖问题
- **✅ 架构文档更新**：完善了 architecture.md 中关于 SQL 查询轮询机制和内存管理的描述

## 项目架构关键洞察
- **全局组件设计**：`QueryPoolPane` 永不卸载的设计需要主动内存管理
- **分段查询机制**：后端分段处理复杂查询，前端递增延迟轮询获取状态
- **内存管理原则**：数据使用完成后立即清理，不依赖组件卸载