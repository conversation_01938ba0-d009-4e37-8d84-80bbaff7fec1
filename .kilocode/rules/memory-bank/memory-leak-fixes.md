# CloudQuery 内存泄漏修复记录

## 概述
本文档记录了 CloudQuery 前端项目中发现和修复的内存泄漏问题，为后续类似问题的排查和修复提供参考。

## 修复记录

### 1. MonacoPane useRequest 内存泄漏修复
**日期**: 2025-08-04
**状态**: ✅ 已完成
**影响**: 高 - 每次 SQL 执行都会产生内存泄漏

### 2. Alert 组件内存泄漏修复
**日期**: 2025-08-06
**状态**: ✅ 已完成
**影响**: 中 - 单例 Alert 组件长期持有引用导致内存无法释放

#### 问题描述
Ant Design Alert 组件在 MonacoPane 单例中直接使用时，因生命周期不受控，导致组件卸载后仍有引用残留，造成内存泄漏。

#### 解决方案
- 封装为 [`AlertManager`](src/pageTabs/queryPage/queryTabs/monacoPane/AlertManager.tsx) 组件，受控挂载/卸载，确保引用及时释放。
- 在 [`MonacoPane.tsx`](src/pageTabs/queryPage/queryTabs/monacoPane/MonacoPane.tsx) 中统一管理 AlertManager 生命周期。
- 所有资源（如 Monaco Editor model、装饰器、定时器等）集中清理，标签页关闭/切换时主动调用清理函数。

#### 验证结果
- ✅ 内存快照中 Alert 相关引用已完全释放
- ✅ 多标签页切换/关闭后无残留引用
- ✅ 业务功能与用户体验无回归

---

#### 问题描述
MonacoPane.tsx 中使用 `useRequest` 管理 `execSegment` 函数导致严重内存泄漏：

1. **useRequest 内部缓存机制**: 即使设置了 `cacheTime: 0, staleTime: 0`，useRequest 仍然在内部持有对组件上下文的引用
2. **闭包引用链**: `onSuccess` 和 `onError` 回调函数通过闭包持有了整个 MonacoPane 组件的状态
3. **QueryPool 引用**: QueryPool 调用 execSegment 时形成了复杂的引用链，导致组件实例无法被垃圾回收
4. **永不卸载组件**: MonacoPane 是单例组件，不会被卸载，使得内存泄漏问题更加严重

#### 根本原因分析
```
useRequest 内部机制
    ↓
持有 onSuccess/onError 回调引用
    ↓
回调函数闭包引用 MonacoPane 上下文
    ↓
包含 dispatch, activePaneInfo, t 等状态
    ↓
形成无法释放的引用链
```

#### 解决方案（方案3）
**核心思路**: 完全避免 useRequest，使用原生 async/await 实现

**修改前**:
```typescript
const { runAsync: execSegment } = useRequest(executeSqlSegment, {
  manual: true,
  cacheTime: 0,
  staleTime: 0,
  refreshOnWindowFocus: false,
  retryCount: 0,
  onSuccess: handleExecuteSuccess,
  onError: handleExecuteError,
})
```

**修改后**:
```typescript
const execSegment = useCallback(async (params: any) => {
  try {
    const data = await executeSqlSegment({
      offset: 0,
      rowCount: params.rowCount || 100,
      selection: params.selection,
      ...params,
    });

    // 手动处理成功回调
    const { messageId, executePercentage, executeStatusMessage } = data ?? {};
    
    if (messageId) {
      dispatch(saveExecuteActiveTabInfo({ key: messageId, data }));
      dispatch(setTabExecutionStatusPercentage({ 
        key: messageId, 
        executePercentage, 
        executeStatusMessage 
      }));
      dispatch(setDeferExecuteResultMapping({ key: messageId, data }));
    }
    
    return data;
  } catch (error) {
    // 手动处理错误回调
    dispatch(setTabExecutionStatus({ key: activePaneInfo?.key as string, pending: false }));
    dispatch(setTabExecutionStatusPercentage({ 
      key: activePaneInfo?.key as string, 
      executePercentage: 100, 
      executeStatusMessage: `${t('sdo_execution_completed')}` 
    }));
    throw error;
  }
}, [dispatch, activePaneInfo?.key, t]);
```

#### 关键改进点
1. **移除 useRequest 依赖**: 完全避免了 useRequest 的内部缓存机制
2. **原生 async/await**: 直接调用 API，没有额外的抽象层
3. **手动回调处理**: 明确控制成功和错误处理逻辑
4. **清晰的依赖管理**: useCallback 的依赖数组明确且最小化

#### 验证结果
- ✅ 内存快照中不再出现多个 execSegment 实例
- ✅ QueryPool 清理后相关引用完全释放
- ✅ SQL 执行功能正常，性能无影响
- ✅ 错误处理和成功回调正常工作

### 2. QueryPoolPane 轮询内存泄漏修复
**日期**: 2025-08-01  
**状态**: ✅ 已完成  
**影响**: 中 - 长时间运行后内存占用增加

#### 问题描述
QueryPoolPane 组件永不卸载，轮询过程中积累的数据没有及时清理。

#### 解决方案
1. **数据及时清理**: 在 `moreOperationAfterExecute` 执行完成后立即清理 `results.current`
2. **轮询状态管理**: 在轮询结束时清理相关状态
3. **性能优化**: 移除不必要的 `cloneDeep(data)` 操作

## 经验总结

### 内存泄漏排查方法
1. **Chrome DevTools Memory Tab**: 使用堆快照对比分析内存增长
2. **引用链分析**: 查看对象的 Retainers，找到引用来源
3. **组件生命周期分析**: 关注永不卸载的组件的内存管理
4. **第三方库审查**: 检查第三方库的内部缓存机制

### 预防措施
1. **优先使用原生 API**: 避免不必要的第三方库抽象
2. **明确依赖管理**: 确保 React Hook 依赖数组的准确性
3. **主动内存管理**: 对于永不卸载的组件，实施主动内存清理策略
4. **定期内存监控**: 建立内存使用监控机制，及早发现问题

### 最佳实践
1. **函数引用稳定性**: 使用 useCallback 确保函数引用稳定
2. **避免闭包陷阱**: 在回调函数中避免持有大对象的引用
3. **数据生命周期管理**: 明确数据的创建、使用和清理时机
4. **第三方库选择**: 优先选择轻量级、无内部状态的库

## 后续优化计划
1. **AbortController 集成**: 实现请求取消机制
2. **内存监控系统**: 建立自动化内存监控和预警
3. **性能基准测试**: 建立内存使用的基准测试
4. **代码审查规范**: 制定内存安全的代码审查标准
