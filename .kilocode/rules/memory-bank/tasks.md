# 重复任务文档

## SQL 查询轮询内存泄漏修复
**最后执行时间**: 2025-08-04
**状态**: 已完成 ✅

## MonacoPane useRequest 内存泄漏修复
**最后执行时间**: 2025-08-04
**状态**: 已完成 ✅

## Alert 组件内存泄漏修复
**最后执行时间**: 2025-08-06
**状态**: 已完成 ✅

### 问题描述
CloudQuery 平台的 SQL 查询功能使用轮询机制获取执行结果，但存在严重的内存泄漏问题：
1. `QueryPoolPane` 组件永不卸载（在 Main.tsx 中全局渲染）
2. 轮询过程中积累的数据没有及时清理
3. `setTimeout` 创建的递归调用链在适当时机没有停止

### MonacoPane 内存泄漏问题描述
MonacoPane.tsx 中使用 useRequest 管理 execSegment 函数导致严重内存泄漏：
1. useRequest 内部缓存机制持有对组件上下文的引用
2. execSegment 函数通过闭包引用了整个 MonacoPane 组件的状态
3. QueryPool 调用 execSegment 时形成了复杂的引用链
4. 即使 QueryPool 清理完成，useRequest 的内部引用仍然存在

### Alert 组件内存泄漏问题描述
Ant Design Alert 组件在 MonacoPane 单例中直接使用时，因生命周期不受控，导致组件卸载后仍有引用残留，造成内存泄漏。

### 涉及文件
- `/src/pageTabs/queryPage/queryTabs/monacoPane/MonacoPane.tsx` - 主要修复文件
- `/src/pageTabs/queryPage/queryTabs/monacoPane/AlertManager.tsx` - AlertManager 封装与受控挂载
- `/src/components/queryPoolPane/QueryPoolPane.tsx` - 轮询机制修复文件
- `/src/api/query.ts` - API 层面支持 AbortSignal
- `/src/appPages/main/Main.tsx` - 全局 QueryPoolPane 渲染
- `/src/pageTabs/queryPage/resultTabs/resultTabsSlice.ts` - Redux 状态清理

### 已完成的修复
1. **数据及时清理**: 在 `moreOperationAfterExecute` 执行完成后立即清理 `results.current`
2. **性能优化**: 移除不必要的 `cloneDeep(data)` 操作
3. **状态清理**: 添加轮询结束时的状态清理逻辑
4. **AlertManager 封装**: 将 Alert 组件封装为 AlertManager，受控挂载/卸载，确保引用及时释放
5. **资源集中清理**: 所有资源（Monaco Editor model、装饰器、定时器等）集中清理，标签页关闭/切换时主动调用清理函数

### MonacoPane 最终解决方案（方案3）
1. **移除 useRequest**: 完全移除 useRequest 的 execSegment 实现
2. **原生 async/await**: 使用原生 async/await 直接调用 executeSqlSegment API
3. **手动回调处理**: 手动实现成功和错误回调，避免 useRequest 的内部缓存
4. **异步 QueryPool 调用**: 将 QueryPool 中的调用改为异步，避免额外的 Promise 包装

### AlertManager 解决方案
1. **封装为独立组件**: 将 Alert 组件封装为 AlertManager，受控挂载/卸载
2. **生命周期管理**: 在 MonacoPane.tsx 中统一管理 AlertManager 生命周期
3. **资源清理**: 标签页关闭/切换时主动调用清理函数，确保无残留引用

### QueryPoolPane 解决方案
1. **数据及时清理**: 在 `moreOperationAfterExecute` 执行完成后立即清理 `results.current`
2. **轮询状态管理**: 在轮询结束时清理相关状态（`params`、`highRiskResultInfoList`）
3. **性能优化**: 移除不必要的 `cloneDeep(data)` 操作，减少内存拷贝
4. **Hook 依赖修复**: 修复所有 `useCallback` 和 `useEffect` 的依赖数组问题

### 重要发现
- **永不卸载组件的内存管理**: 传统的组件卸载清理方案无效，需要主动管理内存
- **轮询数据生命周期**: 数据传递给 Redux 后必须立即清理本地缓存
- **React Hook 依赖的重要性**: 依赖数组配置错误会导致严重的内存泄漏
- **useRequest 内部缓存问题**: useRequest 即使设置 cacheTime: 0, staleTime: 0，仍可能持有内部引用
- **闭包引用链复杂性**: useRequest 的 onSuccess/onError 回调会形成复杂的闭包引用链
- **原生 API 调用的优势**: 直接使用原生 async/await 可以完全避免第三方库的内部机制问题
- **AlertManager 生命周期受控**: 组件受控挂载/卸载可彻底释放引用，避免内存泄漏
- **资源集中清理最佳实践**: 所有资源统一清理，避免遗留引用链

### 长期优化计划
- 实现 AbortController 机制支持请求取消
- 设计更健壮的轮询管理架构
- 添加内存使用监控和预警机制