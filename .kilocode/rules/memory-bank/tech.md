# CloudQuery 云查询平台 - 技术栈与工具

## 1. 技术栈概览

-   **前端框架**: React 16.13.1, TypeScript
-   **UI 组件库**: Ant Design 4.6.5
-   **状态管理**: Redux Toolkit (RTK), Redux Persist
-   **路由**: React Router DOM
-   **构建工具**: Create React App (CRA), CRACO (用于扩展 Webpack 配置)
-   **SQL 编辑器**: Monaco Editor (支持 MySQL, SQL, PostgreSQL, Redis 语法高亮和核心命令)
-   **工作流引擎集成**: BPMN.js
-   **数据表格**: AG Grid
-   **图表**: Echarts, G2
-   **网络通信**: RESTful API (通过 `craco.config.js` 配置代理), WebSocket (用于实时通信)
-   **国际化**: i18next
-   **实用工具库**: lodash, dayjs, resize-observer-polyfill

## 2. 开发环境与工具链

-   **项目初始化**: Create React App
-   **Webpack 配置扩展**: CRACO
    -   **Monaco Editor 集成**: `MonacoWebpackPlugin` 用于支持多种 SQL 语言。
    -   **性能分析**: `webpack-bundle-analyzer` 用于分析打包体积。
    -   **代码优化**: `TerserPlugin` 用于生产环境代码压缩。
    -   **代理配置**: `craco.config.js` 中配置了 `/dms`, `/user`, `/audit` 等后端服务接口的代理，以及 `/socket.io`, `/terminal` 等 WebSocket 连接的代理，解决跨域问题并统一管理接口。
-   **TypeScript 配置**: `tsconfig.json` 定义了编译选项，包括 `baseUrl: "."` 用于模块路径解析。
-   **状态持久化**: Redux Persist 用于 Redux 状态的本地存储。
-   **样式**: SCSS, Less (通过 `craco-antd` 插件定制 Ant Design 主题)
-   **图标库**: 引入了 `iconfont.js` 和 `iconfont_enterprise.js` 等自定义图标库。

## 3. 部署相关
-   **静态文件部署**: 编译后的单页应用生成静态文件，可部署在任何静态文件服务器或 CDN 上。
-   **API 交互**: 前端通过代理与后端 API 进行通信。

## 4. 关键依赖 (从 `package.json` 推断)

-   `@ant-design/icons`
-   `@reduxjs/toolkit`
-   `ag-grid-community`, `ag-grid-react`
-   `antd`
-   `axios`
-   `bpmn-js`
-   `dayjs`
-   `echarts`, `echarts-for-react`
-   `i18next`, `react-i18next`
-   `lodash`
-   `monaco-editor`, `@monaco-editor/react`
-   `react`, `react-dom`, `react-redux`, `react-router-dom`
-   `redux-persist`
-   `web-vitals`

这反映了项目在数据查询、管理、可视化、工作流、国际化以及安全方面的全面技术选型。