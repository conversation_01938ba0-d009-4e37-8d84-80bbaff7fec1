SCRIPT_DIR=$(cd $(dirname $0);pwd)
SCRIPT_NAME=$(basename $0)
SCRIPT_PATH=${SCRIPT_DIR}/${SCRIPT_NAME}

GitSource=${1}
BranchTag=${2}
Is_ARM=${3}
Is_US=${4}

echo "cq-enterprise-frontend 构建脚本开始运行"

branch_short=`echo ${BranchTag}`
echo ${branch_short}

cd ${GitSource}/cq-enterprise-frontend/
git status
git log -5
git branch

cd ${GitSource}/cq-enterprise-frontend/

if [ "$Is_ARM" = "true" ]; then
  if [ "$Skip_frontend_arm" = "true" ]; then
    # 115网络有问题,暂时构建好放这里
    cp /root/arm244/base_web.tar ./
  else

    if [ -n "$(docker buildx ls | grep mybuilder | grep running)" ]; then
        echo "其他arm构建任务正在运行,本任务终止."
        exit 1
    else
        docker buildx rm mybuilder || true
        docker run --privileged --rm tonistiigi/binfmt --install all
        docker buildx create --use --name=mybuilder --config /etc/buildkit/buildkitd.toml --use --driver docker-container --driver-opt image=dockerpracticesig/buildkit:master
        docker buildx inspect mybuilder --bootstrap
        # docker buildx build -f Dockerfile -t base-web:${branch_short} --platform=linux/arm64 . --load
        docker buildx build -f Dockerfile -t base-web:${branch_short} --platform=linux/arm64 --build-arg Is_US=${Is_US:-true} . --load
        docker save -o base_web.tar base-web:${branch_short}
        docker buildx rm mybuilder

    fi
  fi
else
  # docker build -t base-web:${branch_short} .
  docker build -t base-web:${branch_short} --build-arg Is_US=${Is_US:-true} .
  docker save -o base_web.tar base-web:${branch_short}
fi


ls ./