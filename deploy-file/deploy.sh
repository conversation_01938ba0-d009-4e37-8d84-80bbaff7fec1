SCRIPT_DIR=$(cd $(dirname $0);pwd)
SCRIPT_NAME=$(basename $0)
SCRIPT_PATH=${SCRIPT_DIR}/${SCRIPT_NAME}

GitSource=${1}
BranchTag=${2}
IP_Addr=${3}
SSH_Password=${4}

echo "cq-enterprise-frontend 部署脚本开始运行"

branch_short=`echo ${BranchTag}`
echo ${branch_short}

sshpass -p ${SSH_Password} ssh -o StrictHostKeyChecking=no root@${IP_Addr}<<remotessh
rm -rf /opt/CloudQuery/base_web.tar
rm -rf /opt/CloudQuery/compose_template/base-web
mkdir -p /opt/CloudQuery
mkdir -p /opt/CloudQuery/compose_template/base-web
remotessh

cd ${GitSource}/cq-enterprise-frontend/
sshpass -p ${SSH_Password} scp -o StrictHostKeyChecking=no base_web.tar root@${IP_Addr}:/opt/CloudQuery/

cd ${GitSource}/cq-enterprise-frontend/deploy-file/
sshpass -p ${SSH_Password} scp -o StrictHostKeyChecking=no -r docker-compose.yml root@${IP_Addr}:/opt/CloudQuery/compose_template/base-web/

sshpass -p ${SSH_Password} ssh -o StrictHostKeyChecking=no root@${IP_Addr}<<remotessh
# 执行出错则退出
set -e

#停止前端服务，如果文件不存在继续往下执行命令
docker-compose -f /opt/cloudquery/base-web/docker-compose.yml down -v || true

#加载前端新镜像
cd /opt/CloudQuery/
docker load -i base_web.tar

#替换参数
cp -rf /opt/CloudQuery/compose_template/base-web/docker-compose.yml /opt/cloudquery/base-web/docker-compose.yml
sed -i 's|{TAG_NAME}|'"${branch_short}"'|g' /opt/cloudquery/base-web/docker-compose.yml

#启动服务
docker-compose -f /opt/cloudquery/base-web/docker-compose.yml up -d

#docker images -f "dangling=true" -q | xargs -n1 -I {} docker rmi -f {}

#启动告警功能
if [ -f "/opt/Install_Base/base_suport/audit_warringStart.sh" ];then
  cd /opt/Install_Base/base_suport
  chmod +x audit_warringStart.sh
  ./audit_warringStart.sh
else
  echo "老版本"
fi

exit;

remotessh

echo END