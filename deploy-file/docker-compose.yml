version: '3'

networks:
  baseWebNet:
    external:
      name: resource_cloudquerynet

services:
  cloudquery-web:
    restart: always
    image: base-web:{TAG_NAME}
    container_name: enterprise_web
    environment:
      - REACT_APP_I18N=zh-CN
    ports:
      - "80:9898"
      - "81:9999"
    volumes:
    - ./nginx/config/nginx.conf:/etc/nginx/nginx.conf
    - ./dq:/opt/cloudquery/dq
    - ./drivers:/usr/share/nginx/html/drivers
    - ./ssh:/usr/share/nginx/html/ssh
    networks:
      baseWebNet:
