SCRIPT_DIR=$(cd $(dirname $0);pwd)
SCRIPT_NAME=$(basename $0)
SCRIPT_PATH=${SCRIPT_DIR}/${SCRIPT_NAME}

GitSource=${1}
BranchTag=${2}
Is_Default_English=${3}

echo "cq-enterprise-frontend language替换脚本开始运行"

branch_short=`echo ${BranchTag}`
echo ${branch_short}

cd ${GitSource}/cq-enterprise-frontend/

if [ "${Is_Default_English}" = "true" ]; then
  echo "选择了english,替换getCurrentLanguage中的zh为en"
  sed -i 's|zh|en|g' src/util/getCurrentLanguage.ts
  echo "选择了english,替换i18n.ts中的zh为en"
  sed -i 's/fallbackLng: "zh"/fallbackLng: "en"/g' src/i18n.ts
  echo "选择了english,替换docker-compose中的zh为en"
  sed -i 's/zh-CN/en-US/g' deploy-file/docker-compose.yml
fi