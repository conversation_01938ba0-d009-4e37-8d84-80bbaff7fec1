SCRIPT_DIR=$(cd $(dirname $0);pwd)
SCRIPT_NAME=$(basename $0)
SCRIPT_PATH=${SCRIPT_DIR}/${SCRIPT_NAME}

GitSource=${1}
BranchTag=${2}
Oem_Comp=${3}
OemCompNameTab=${4}

echo "cq-enterprise-frontend OEM文件替换脚本开始运行"

branch_short=`echo ${BranchTag}`
echo ${branch_short}

cd ${GitSource}/cq-enterprise-frontend/

echo "替换tab图标favicon.ico"
cp oem-comps/${Oem_Comp}/favicon.ico public/favicon.ico
echo "替换tab CloudQuery"
sed -i "s|CloudQuery|${OemCompNameTab}|g" public/index.html
echo "替换LOGO"
cp oem-comps/${Oem_Comp}/logo_xd.png src/assets/img/logo_xd.png
cp oem-comps/${Oem_Comp}/logo-text.svg src/assets/logo-text.svg
echo "替换登录"
sed -i 's|CQ 登录|'"登录"'|g' src/appPages/login/Login.tsx