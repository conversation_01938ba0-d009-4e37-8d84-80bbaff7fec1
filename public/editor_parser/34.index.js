(self.webpackChunkeditor_parser=self.webpackChunkeditor_parser||[]).push([[34],{5810:(t,e,r)=>{"use strict";r.d(e,{c:()=>n});var i=r(352);class s{constructor(t,e,r){this.startOffset=-1,this.endOffset=-1,this.startOffset=t,this.endOffset=e,this.promptInfo=r}}class n extends i.ConsoleErrorListener{syntaxError(t,e,r,i,n,h){if(void 0===this.errorMark){let e=t.currentToken;this.errorMark=new s(e.startIndex+1,e.stopIndex+1,n)}}}},9034:(t,e,r)=>{"use strict";r.r(e),r.d(e,{default:()=>ee});var i=r(352),s=r(5737),n=r(5810),h=r(5822);class a{}a.keywords=["db","find()","findOne()","sort()","limit()","skip()","insert()","insertMany()","insertOne()","update()","updateOne()","updateMany()","replaceOne()","findAndModify()","findOneAndUpdate()","findOneAndReplace()","save()","bulkWrite()","deleteOne()","deleteMany()","remove()","findOneAndDelete()","findAndModify()","createIndex()","aggregate()","mapReduce()","distinct()","createIndexes()","drop()","dropIndex()","dropIndexes()","renameCollection()","count()","help()","getCollection()"];var o=r(6862),l=r(4037),u=r(7717);class c{constructor(){this.symbolTable=new o.d}getSymbolTable(){return this.symbolTable}enterCollection(t){var e;let r=new l.o;r.startIndex=t.start.startIndex,r.stopIndex=null===(e=t.stop)||void 0===e?void 0:e.stopIndex,r.text=t.text,r.symbolType=u.R.TABLE,this.symbolTable.insertElement(r)}}var d=r(9963),E=r(9557),R=r(2178),_=r(6763),O=r(5103);class x extends E.Lexer{constructor(t){super(t),this._interp=new R.LexerATNSimulator(x._ATN,this)}get vocabulary(){return x.VOCABULARY}get grammarFileName(){return"MongoLexer.g4"}get ruleNames(){return x.ruleNames}get serializedATN(){return x._serializedATN}get channelNames(){return x.channelNames}get modeNames(){return x.modeNames}static get _ATN(){return x.__ATN||(x.__ATN=(new d.ATNDeserializer).deserialize(O.toCharArray(x._serializedATN))),x.__ATN}}x.WS=1,x.DB=2,x.INSERT=3,x.INSERTONE=4,x.INSERTMANY=5,x.UPDATE=6,x.UPDATEONE=7,x.UPDATEMANY=8,x.REPLACEONE=9,x.FIND=10,x.FINDONE=11,x.DELETEONE=12,x.DELETEMANY=13,x.REMOVE=14,x.STATS=15,x.DISTINCT=16,x.AGGREGATE=17,x.COUNTDOCUMENTS=18,x.CREATEINDEX=19,x.CREATEINDEXES=20,x.DROP=21,x.DROPINDEX=22,x.DROPINDEXES=23,x.FINDONEANDDELETE=24,x.RENAMECOLLECTION=25,x.ESTIMATEDDOCUMENTCOUNT=26,x.COUNT=27,x.FINDONEANDUPDATE=28,x.FINDONEANDREPLACE=29,x.CREATECOLLECTION=30,x.GETCOLLECTION=31,x.GETNAME=32,x.DROPDATABASE=33,x.CREATEVIEW=34,x.ISCAPPED=35,x.DATASIZE=36,x.STORAGESIZE=37,x.TOTALINDEXSIZE=38,x.TOTALSIZE=39,x.GETINDEXES=40,x.VALIDATE=41,x.LATENCYSTATS=42,x.USE=43,x.SHOW=44,x.DBS=45,x.HELP=46,x.LIMIT=47,x.SORT=48,x.VERSION=49,x.SERVERBUILDINFO=50,x.SERVERSTATUS=51,x.ISMASTER=52,x.HOSTINFO=53,x.SERVERCMDLINEOPTS=54,x.GETPROFILINGSTATUS=55,x.GETPROFILINGLEVEL=56,x.IDENTIFIER_=57,x.HEX_DIGIT_=58,x.BIT_NUM_=59,x.STRING=60,x.NUMBER=61,x.TRUE=62,x.FLASE=63,x.NULL=64,x.SKIPINFO=65,x.AND_=66,x.OR_=67,x.NOT_=68,x.TILDE_=69,x.VERTICAL_BAR_=70,x.AMPERSAND_=71,x.SIGNED_LEFT_SHIFT_=72,x.SIGNED_RIGHT_SHIFT_=73,x.CARET_=74,x.MOD_=75,x.COLON_=76,x.PLUS_=77,x.MINUS_=78,x.ASTERISK_=79,x.SLASH_=80,x.BACKSLASH_=81,x.DOT_=82,x.DOT_ASTERISK_=83,x.SAFE_EQ_=84,x.DEQ_=85,x.EQ_=86,x.NEQ_=87,x.GT_=88,x.GTE_=89,x.LT_=90,x.LTE_=91,x.POUND_=92,x.LP_=93,x.RP_=94,x.LBE_=95,x.RBE_=96,x.LBT_=97,x.RBT_=98,x.COMMA_=99,x.DQ_=100,x.SQ_=101,x.BQ_=102,x.QUESTION_=103,x.AT_=104,x.SEMI_=105,x.FOR_GENERATOR=106,x.channelNames=["DEFAULT_TOKEN_CHANNEL","HIDDEN"],x.modeNames=["DEFAULT_MODE"],x.ruleNames=["WS","DB","INSERT","INSERTONE","INSERTMANY","UPDATE","UPDATEONE","UPDATEMANY","REPLACEONE","FIND","FINDONE","DELETEONE","DELETEMANY","REMOVE","STATS","DISTINCT","AGGREGATE","COUNTDOCUMENTS","CREATEINDEX","CREATEINDEXES","DROP","DROPINDEX","DROPINDEXES","FINDONEANDDELETE","RENAMECOLLECTION","ESTIMATEDDOCUMENTCOUNT","COUNT","FINDONEANDUPDATE","FINDONEANDREPLACE","CREATECOLLECTION","GETCOLLECTION","GETNAME","DROPDATABASE","CREATEVIEW","ISCAPPED","DATASIZE","STORAGESIZE","TOTALINDEXSIZE","TOTALSIZE","GETINDEXES","VALIDATE","LATENCYSTATS","USE","SHOW","DBS","HELP","LIMIT","SORT","VERSION","SERVERBUILDINFO","SERVERSTATUS","ISMASTER","HOSTINFO","SERVERCMDLINEOPTS","GETPROFILINGSTATUS","GETPROFILINGLEVEL","IDENTIFIER_","HEX_DIGIT_","BIT_NUM_","INT_","HEX_","INT","EXP","ESC","UNICODE","HEX","SAFECODEPOINT","STRING","NUMBER","TRUE","FLASE","NULL","SKIPINFO","AND_","OR_","NOT_","TILDE_","VERTICAL_BAR_","AMPERSAND_","SIGNED_LEFT_SHIFT_","SIGNED_RIGHT_SHIFT_","CARET_","MOD_","COLON_","PLUS_","MINUS_","ASTERISK_","SLASH_","BACKSLASH_","DOT_","DOT_ASTERISK_","SAFE_EQ_","DEQ_","EQ_","NEQ_","GT_","GTE_","LT_","LTE_","POUND_","LP_","RP_","LBE_","RBE_","LBT_","RBT_","COMMA_","DQ_","SQ_","BQ_","QUESTION_","AT_","SEMI_","FOR_GENERATOR","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z","UL_"],x._LITERAL_NAMES=[void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,"'true'","'false'","'null'",void 0,"'&&'","'||'","'!'","'~'","'|'","'&'","'<<'","'>>'","'^'","'%'","':'","'+'","'-'","'*'","'/'","'\\'","'.'","'.*'","'<=>'","'=='","'='",void 0,"'>'","'>='","'<'","'<='","'#'","'('","')'","'{'","'}'","'['","']'","','","'\"'","'''","'`'","'?'","'@'","';'","'DO NOT MATCH ANY THING, JUST FOR GENERATOR'"],x._SYMBOLIC_NAMES=[void 0,"WS","DB","INSERT","INSERTONE","INSERTMANY","UPDATE","UPDATEONE","UPDATEMANY","REPLACEONE","FIND","FINDONE","DELETEONE","DELETEMANY","REMOVE","STATS","DISTINCT","AGGREGATE","COUNTDOCUMENTS","CREATEINDEX","CREATEINDEXES","DROP","DROPINDEX","DROPINDEXES","FINDONEANDDELETE","RENAMECOLLECTION","ESTIMATEDDOCUMENTCOUNT","COUNT","FINDONEANDUPDATE","FINDONEANDREPLACE","CREATECOLLECTION","GETCOLLECTION","GETNAME","DROPDATABASE","CREATEVIEW","ISCAPPED","DATASIZE","STORAGESIZE","TOTALINDEXSIZE","TOTALSIZE","GETINDEXES","VALIDATE","LATENCYSTATS","USE","SHOW","DBS","HELP","LIMIT","SORT","VERSION","SERVERBUILDINFO","SERVERSTATUS","ISMASTER","HOSTINFO","SERVERCMDLINEOPTS","GETPROFILINGSTATUS","GETPROFILINGLEVEL","IDENTIFIER_","HEX_DIGIT_","BIT_NUM_","STRING","NUMBER","TRUE","FLASE","NULL","SKIPINFO","AND_","OR_","NOT_","TILDE_","VERTICAL_BAR_","AMPERSAND_","SIGNED_LEFT_SHIFT_","SIGNED_RIGHT_SHIFT_","CARET_","MOD_","COLON_","PLUS_","MINUS_","ASTERISK_","SLASH_","BACKSLASH_","DOT_","DOT_ASTERISK_","SAFE_EQ_","DEQ_","EQ_","NEQ_","GT_","GTE_","LT_","LTE_","POUND_","LP_","RP_","LBE_","RBE_","LBT_","RBT_","COMMA_","DQ_","SQ_","BQ_","QUESTION_","AT_","SEMI_","FOR_GENERATOR"],x.VOCABULARY=new _.VocabularyImpl(x._LITERAL_NAMES,x._SYMBOLIC_NAMES,[]),x._serializedATNSegments=3,x._serializedATNSegment0="줝쪺֍꾺体؇쉁lӦ\b\t\t\t\t\t\t\b\t\b\t\t\t\n\t\n\v\t\v\f\t\f\r\t\r\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t \t !\t!\"\t\"#\t#$\t$%\t%&\t&'\t'(\t()\t)*\t*+\t+,\t,-\t-.\t./\t/0\t01\t12\t23\t34\t45\t56\t67\t78\t89\t9:\t:;\t;<\t<=\t=>\t>?\t?@\t@A\tAB\tBC\tCD\tDE\tEF\tFG\tGH\tHI\tIJ\tJK\tKL\tLM\tMN\tNO\tOP\tPQ\tQR\tRS\tST\tTU\tUV\tVW\tWX\tXY\tYZ\tZ[\t[\\\t\\]\t]^\t^_\t_`\t`a\tab\tbc\tcd\tde\tef\tfg\tgh\thi\tij\tjk\tkl\tlm\tmn\tno\top\tpq\tqr\trs\tst\ttu\tuv\tvw\twx\txy\tyz\tz{\t{|\t|}\t}~\t~\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tğ\n\rĠ\b\b\b\b\b\b\b\b\b\b\t\t\t\t\t\t\t\t\t\t\t\n\n\n\n\n\n\n\n\n\n\n\v\v\v\v\v\f\f\f\f\f\f\f\f\r\r\r\r\r\r\r\r\r\r              !!!!!!!!\"\"\"\"\"\"\"\"\"\"\"\"\"###########$$$$$$$$$%%%%%%%%%&&&&&&&&&&&&'''''''''''''''(((((((((()))))))))))*********+++++++++++++,,,,-----..../////0000001111122222222333333333333333344444444444445555555556666666667777777777777777778888888888888888888999999999999999999::Ͷ\n:\f::͹\v:::ͼ\n:\r::ͽ::΁\n:\f::΄\v::::Έ\n:\r::Ή:::::::::Δ\n:\f::Η\v::::Λ\n:;;;;;Ρ\n;\r;;΢;;;;Ψ\n;\r;;Ω;;;ή\n;<<<<<δ\n<\r<<ε<<<<λ\n<\r<<μ<<<ρ\n<==τ\n=\r==υ>>????ύ\n?\f??ϐ\v??ϒ\n?@@@ϖ\n@@@AAAAϝ\nABBBBBBCCDDEEEEϬ\nE\fEEϯ\vEEEEEEE϶\nE\fEEϹ\vEEEEϽ\nEFFЀ\nFFFFFЅ\nF\rFFІFЉ\nFFFЌ\nFGGGGGHHHHHHIIIIIJJJJJJJJJЦ\nJKKKLLLMMNNOOPPQQQRRRSSTTUUVVWWXXYYZZ[[\\\\\\]]]]^^^__`````ў\n`aabbbccdddeeffgghhiijjkkllmmnnooppqqrrsssssssssssssssssssssssssssssssssssssssssssttuuvvwwxxyyzz{{||}}~~ͷͽ\t\v\r\b\t\n\v\f\r!#%')+-/13579;= ?!A\"C#E$G%I&K'M(O)Q*S+U,W-Y.[/]0_1a2c3e4g5i6k7m8o9q:s;u<w=y{}>?@ABCDEFGHI¡J£K¥L§M©N«O­P¯Q±R³SµT·U¹V»W½X¿YÁZÃ[Å\\Ç]É^Ë_Í`ÏaÑbÓcÕd×eÙfÛgÝhßiájãkålçéëíïñóõ÷ùûýÿāăąćĉċčďđēĕėęě'\v\f\"\"&&2;C\\aac|&&C\\aac|bb$$^^2;2;CHch3;GGgg--//\n$$11^^ddhhppttvv!$$^^CCccDDddEEeeFFffHHhhIIiiJJjjKKkkLLllMMmmNNnnOOooPPppQQqqRRrrSSssTTttUUuuVVvvWWwwXXxxYYyyZZzz[[{{\\\\||Ӣ\t\v\r!#%')+-/13579;=?ACEGIKMOQSUWY[]_acegikmoqsuw¡£¥§©«­¯±³µ·¹»½¿ÁÃÅÇÉËÍÏÑÓÕ×ÙÛÝßáãåĞĤħ\tĮ\vĸ\rŃŊŔşŪůŷƁƌƓ!ƙ#Ƣ%Ƭ'ƻ)Ǉ+Ǖ-ǚ/Ǥ1ǰ3ȁ5Ȓ7ȩ9ȯ;ɀ=ɒ?ɣAɱCɹEʆGʑIʚKʣMʯOʾQˈS˓U˜W˩Y˭[˲]˶_˻ác̆e̎g̞i̫k̴m̽o͏q͢sΚuέwπyσ{χ}ϑϓϙϞϤϦϼϿЍВИХЧЪЭЯбг¡е£и¥л§н©п«с­у¯х±ч³щµы·э¹ѐ»є½ї¿ѝÁџÃѡÅѤÇѦÉѩËѫÍѭÏѯÑѱÓѳÕѵ×ѷÙѹÛѻÝѽßѿáҁã҃å҅çҰéҲëҴíҶïҸñҺóҼõҾ÷ӀùӂûӄýӆÿӈāӊăӌąӎćӐĉӒċӔčӖďӘđӚēӜĕӞėӠęӢěӤĝğ\tĞĝğĠĠĞĠġġĢĢģ\bģĤĥíwĥĦéuĦħĨ÷|ĨĩāĩĪċĪīïxīĬĉĬĭčĭ\bĮį÷|įİāİıċıĲïxĲĳĉĳĴčĴĵăĵĶāĶķïxķ\nĸĹ÷|ĹĺāĺĻċĻļïxļĽĉĽľčľĿÿĿŀçtŀŁāŁłėł\fŃńďńŅąŅņíwņŇçtŇňčňŉïxŉŊŋď",x._serializedATNSegment1="ŋŌąŌōíwōŎçtŎŏčŏŐïxŐőăőŒāŒœïxœŔŕďŕŖąŖŗíwŗŘçtŘřčřŚïxŚśÿśŜçtŜŝāŝŞėŞşŠĉŠšïxšŢąŢţýţŤçtŤťëvťŦïxŦŧăŧŨāŨũïxũŪūñyūŬ÷|ŬŭāŭŮíwŮůŰñyŰű÷|űŲāŲųíwųŴăŴŵāŵŶïxŶŷŸíwŸŹïxŹźýźŻïxŻżčżŽïxŽžăžſāſƀïxƀƁƂíwƂƃïxƃƄýƄƅïxƅƆčƆƇïxƇƈÿƈƉçtƉƊāƊƋėƋƌƍĉƍƎïxƎƏÿƏƐăƐƑđƑƒïxƒƓƔċƔƕčƕƖçtƖƗčƗƘċƘ ƙƚíwƚƛ÷|ƛƜċƜƝčƝƞ÷|ƞƟāƟƠëvƠơčơ\"ƢƣçtƣƤózƤƥózƥƦĉƦƧïxƧƨózƨƩçtƩƪčƪƫïxƫ$ƬƭëvƭƮăƮƯďƯưāưƱčƱƲíwƲƳăƳƴëvƴƵďƵƶÿƶƷïxƷƸāƸƹčƹƺċƺ&ƻƼëvƼƽĉƽƾïxƾƿçtƿǀčǀǁïxǁǂ÷|ǂǃāǃǄíwǄǅïxǅǆĕǆ(ǇǈëvǈǉĉǉǊïxǊǋçtǋǌčǌǍïxǍǎ÷|ǎǏāǏǐíwǐǑïxǑǒĕǒǓïxǓǔċǔ*ǕǖíwǖǗĉǗǘăǘǙąǙ,ǚǛíwǛǜĉǜǝăǝǞąǞǟ÷|ǟǠāǠǡíwǡǢïxǢǣĕǣ.ǤǥíwǥǦĉǦǧăǧǨąǨǩ÷|ǩǪāǪǫíwǫǬïxǬǭĕǭǮïxǮǯċǯ0ǰǱñyǱǲ÷|ǲǳāǳǴíwǴǵăǵǶāǶǷïxǷǸçtǸǹāǹǺíwǺǻíwǻǼïxǼǽýǽǾïxǾǿčǿȀïxȀ2ȁȂĉȂȃïxȃȄāȄȅçtȅȆÿȆȇïxȇȈëvȈȉăȉȊýȊȋýȋȌïxȌȍëvȍȎčȎȏ÷|ȏȐăȐȑāȑ4ȒȓïxȓȔċȔȕčȕȖ÷|ȖȗÿȗȘçtȘșčșȚïxȚțíwțȜíwȜȝăȝȞëvȞȟďȟȠÿȠȡïxȡȢāȢȣčȣȤëvȤȥăȥȦďȦȧāȧȨčȨ6ȩȪëvȪȫăȫȬďȬȭāȭȮčȮ8ȯȰñyȰȱ÷|ȱȲāȲȳíwȳȴăȴȵāȵȶïxȶȷçtȷȸāȸȹíwȹȺďȺȻąȻȼíwȼȽçtȽȾčȾȿïxȿ:ɀɁñyɁɂ÷|ɂɃāɃɄíwɄɅăɅɆāɆɇïxɇɈçtɈɉāɉɊíwɊɋĉɋɌïxɌɍąɍɎýɎɏçtɏɐëvɐɑïxɑ<ɒɓëvɓɔĉɔɕïxɕɖçtɖɗčɗɘïxɘəëvəɚăɚɛýɛɜýɜɝïxɝɞëvɞɟčɟɠ÷|ɠɡăɡɢāɢ>ɣɤózɤɥïxɥɦčɦɧëvɧɨăɨɩýɩɪýɪɫïxɫɬëvɬɭčɭɮ÷|ɮɯăɯɰāɰ@ɱɲózɲɳïxɳɴčɴɵāɵɶçtɶɷÿɷɸïxɸBɹɺíwɺɻĉɻɼăɼɽąɽɾíwɾɿçtɿʀčʀʁçtʁʂéuʂʃçtʃʄċʄʅïxʅDʆʇëvʇʈĉʈʉïxʉʊçtʊʋčʋʌïxʌʍđʍʎ÷|ʎʏïxʏʐēʐFʑʒ÷|ʒʓċʓʔëvʔʕçtʕʖąʖʗąʗʘïxʘʙíwʙHʚʛíwʛʜçtʜʝčʝʞçtʞʟċʟʠ÷|ʠʡęʡʢïxʢJʣʤċʤʥčʥʦăʦʧĉʧʨçtʨʩózʩʪïxʪʫċʫʬ÷|ʬʭęʭʮïxʮLʯʰčʰʱăʱʲčʲʳçtʳʴýʴʵ÷|ʵʶāʶʷíwʷʸïxʸʹĕʹʺċʺʻ÷|ʻʼęʼʽïxʽNʾʿčʿˀăˀˁčˁ˂çt˂˃ý˃˄ċ˄˅÷|˅ˆęˆˇïxˇPˈˉózˉˊïxˊˋčˋˌ÷|ˌˍāˍˎíwˎˏïxˏːĕːˑïxˑ˒ċ˒R˓˔đ˔˕çt˕˖ý˖˗÷|˗˘íw˘˙çt˙˚č˚˛ïx˛T˜˝ý˝˞çt˞˟č˟ˠïxˠˡāˡˢëvˢˣėˣˤċˤ˥č˥˦çt˦˧č˧˨ċ˨V˩˪ď˪˫ċ˫ˬïxˬX˭ˮċˮ˯õ{˯˰ă˰˱ē˱Z˲˳íw˳˴éu˴˵ċ˵\\˶˷õ{˷˸ïx˸˹ý˹˺ą˺^˻˼ý˼˽÷|˽˾ÿ˾˿÷|˿̀č̀`́̂ċ̂̃ă̃̄ĉ̄̅č̅b̆̇đ̇̈ïx̈̉ĉ̉̊ċ̊̋÷|̋̌ă̌̍ā̍d̎̏ċ̏̐ïx̐̑ĉ̑̒đ̒̓ïx̓̔ĉ̔̕éu̖̕ď̖̗÷|̗̘ý̘̙íw̙̚÷|̛̚ā̛̜ñy̜̝ă̝f̞̟ċ̟̠ïx̡̠ĉ̡̢đ̢̣ïx̣̤ĉ̤̥ċ̥̦č̧̦çt̧̨č̨̩ď̩̪ċ̪h̫̬÷|̬̭ċ̭̮ÿ̮̯çt̯̰ċ̰̱č̱̲ïx̲̳ĉ̳j̴̵õ{̵̶ă̶̷ċ̷̸č̸̹÷|̹̺ā̺̻ñy̻̼ă̼l̽̾ċ̾̿ïx̿̀ĉ̀́đ́͂ïx͂̓ĉ̓̈́ëv̈́ͅÿ͆ͅíw͇͆ý͇͈÷|͈͉ā͉͊ïx͊͋ă͋͌ą͍͌č͍͎ċ͎n͏͐óz͐͑ïx͑͒č͓͒ą͓͔ĉ͔͕ă͕͖ñy͖͗÷|͗͘ý͙͘÷|͙͚ā͚͛óz͛͜ċ͜͝č͝͞çt͟͞č͟͠ď͠͡ċ͡pͣ͢ózͣͤïxͤͥčͥͦąͦͧĉͧͨăͨͩñyͩͪ÷|ͪͫýͫͬ÷|ͬͭāͭͮózͮͯýͯͰïxͰͱđͱͲïxͲͳýͳrʹͶ\t͵ʹͶ͹ͷ͸ͷ͵͸ͻ͹ͷͺͼ\tͻͺͼͽͽ;ͽͻ;΂Ϳ΁\t΀Ϳ΁΄΂΀΂΃΃Λ΄΂΅·ÝoΆΈ\n·ΆΈΉΉ·ΉΊΊ΋΋ΌÝoΌΛ΍ΕÙmΎΏ^ΏΔ\vΐΑ$ΑΔ$ΒΔ\nΓΎΓΐΓΒΔΗΕΓΕΖΖΘΗΕΘΙÙmΙΛΚͷΚ΅Κ΍ΛtΜΝ2ΝΞzΞΠΟΡ{>ΠΟΡ΢΢Π΢ΣΣήΤΥZΥΧÛnΦΨ{>ΧΦΨΩΩΧΩΪΪΫΫάÛnάήέΜέΤήvίΰ2ΰαdαγβδ23γβδεεγεζζρηθéuθκÛnιλ23κιλμμκμννξξοÛnορπίπηρxςτ\tσςτυυσυφφzχψ\t\bψ|ωϒ2ϊώ\t\tϋύ\tόϋύϐώόώϏϏϒϐώϑωϑϊϒ~ϓϕ\t\nϔϖ\t\vϕϔϕϖϖϗϗϘ}?ϘϙϜ^Ϛϝ\t\fϛϝBϜϚϜϛϝϞϟwϟϠCϠϡCϡϢCϢϣCϣϤϥ\t\bϥϦϧ\n\rϧϨϭÙmϩϬAϪϬDϫϩϫϪϬϯϭϫϭϮϮϰϯϭϰϱÙmϱϽϲϷÛnϳ϶Aϴ϶Dϵϳϵϴ϶ϹϷϵϷϸϸϺϹϷϺϻÛnϻϽϼϨϼϲϽϾЀ/ϿϾϿЀЀЁЁЈ}?ЂЄ0ЃЅ\tЄЃЅІІЄІЇЇЉЈЂЈЉЉЋЊЌ@ЋЊЋЌЌЍЎvЎЏtЏАwАБgБВГhГДcДЕnЕЖuЖЗgЗИЙpЙКwКЛnЛМnМНОUОПMПРKРЦRСТuТУmУФkФЦrХНХСЦЧШ(ШЩ(ЩЪЫ~ЫЬ~ЬЭЮ#ЮЯаабв~вгд(д еж>жз>з¢ий@йк@к¤лм`м¦но'о¨пр<рªст-т¬уф/ф®хц,ц°чш1ш²щъ^ъ´ыь0ь¶эю0юя,я¸ѐё>ёђ?ђѓ@ѓºєѕ?ѕі?і¼їј?ј¾љњ>њў@ћќ#ќў?ѝљѝћўÀџѠ@ѠÂѡѢ@Ѣѣ?ѣÄѤѥ>ѥÆѦѧ>ѧѨ?ѨÈѩѪ%ѪÊѫѬ*ѬÌѭѮ",x._serializedATNSegment2='+ѮÎѯѰ}ѰÐѱѲѲÒѳѴ]ѴÔѵѶ_ѶÖѷѸ.ѸØѹѺ$ѺÚѻѼ)ѼÜѽѾbѾÞѿҀAҀàҁ҂B҂â҃҄=҄ä҅҆F҆҇Q҇҈"҈҉P҉ҊQҊҋVҋҌ"ҌҍOҍҎCҎҏVҏҐEҐґJґҒ"ҒғCғҔPҔҕ[ҕҖ"ҖҗVҗҘJҘҙKҙҚPҚқIқҜ.Ҝҝ"ҝҞLҞҟWҟҠUҠҡVҡҢ"ҢңHңҤQҤҥTҥҦ"ҦҧIҧҨGҨҩPҩҪGҪҫTҫҬCҬҭVҭҮQҮүTүæҰұ\tұèҲҳ\tҳêҴҵ\tҵìҶҷ\tҷîҸҹ\t\nҹðҺһ\tһòҼҽ\tҽôҾҿ\tҿöӀӁ\tӁøӂӃ\tӃúӄӅ\tӅüӆӇ\tӇþӈӉ\tӉĀӊӋ\tӋĂӌӍ\tӍĄӎӏ\tӏĆӐӑ\tӑĈӒӓ\tӓĊӔӕ\tӕČӖӗ\t ӗĎӘә\t!әĐӚӛ\t"ӛĒӜӝ\t#ӝĔӞӟ\t$ӟĖӠӡ\t%ӡĘӢӣ\t&ӣĚӤӥaӥĜ!Ġͷͽ΂ΉΓΕΚ΢ΩέεμπυώϑϕϜϫϭϵϷϼϿІЈЋХѝ\b',x._serializedATN=O.join([x._serializedATNSegment0,x._serializedATNSegment1,x._serializedATNSegment2],"");var T=r(4609),p=r(2824),C=r(3208),A=r(4584),L=r(3998),I=r(4966);class g extends p.Parser{constructor(t){super(t),this._interp=new A.ParserATNSimulator(g._ATN,this)}get vocabulary(){return g.VOCABULARY}get grammarFileName(){return"MongoParser.g4"}get ruleNames(){return g.ruleNames}get serializedATN(){return g._serializedATN}execute(){let t=new D(this._ctx,this.state);this.enterRule(t,0,g.RULE_execute);try{switch(this.enterOuterAlt(t,1),this.state=192,this._errHandler.sync(this),this.interpreter.adaptivePredict(this._input,0,this._ctx)){case 1:this.state=162,this.insert();break;case 2:this.state=163,this.update();break;case 3:this.state=164,this.select();break;case 4:this.state=165,this.delete();break;case 5:this.state=166,this.stats();break;case 6:this.state=167,this.distinct();break;case 7:this.state=168,this.aggregate();break;case 8:this.state=169,this.countDocuments();break;case 9:this.state=170,this.createIndex();break;case 10:this.state=171,this.drop();break;case 11:this.state=172,this.findOneAndDelete();break;case 12:this.state=173,this.renameCollection();break;case 13:this.state=174,this.estimatedDocount();break;case 14:this.state=175,this.count();break;case 15:this.state=176,this.findOneAndUpdate();break;case 16:this.state=177,this.findOneAndReplace();break;case 17:this.state=178,this.dbAggregate();break;case 18:this.state=179,this.createCollection();break;case 19:this.state=180,this.getCollection();break;case 20:this.state=181,this.getName();break;case 21:this.state=182,this.dropDataBase();break;case 22:this.state=183,this.createView();break;case 23:this.state=184,this.iscapped();break;case 24:this.state=185,this.dataSize();break;case 25:this.state=186,this.validate();break;case 26:this.state=187,this.latencyStats();break;case 27:this.state=188,this.use();break;case 28:this.state=189,this.showDbs();break;case 29:this.state=190,this.dbStats();break;case 30:this.state=191,this.mongoBefore()}}catch(e){if(!(e instanceof L.RecognitionException))throw e;t.exception=e,this._errHandler.reportError(this,e),this._errHandler.recover(this,e)}finally{this.exitRule()}return t}mongoBefore(){let t,e=new S(this._ctx,this.state);this.enterRule(e,2,g.RULE_mongoBefore);try{this.enterOuterAlt(e,1),this.state=194,this.databaseBefore(),this.state=195,this.collection(),this.state=197,this._errHandler.sync(this),t=this._input.LA(1),t===g.DOT_&&(this.state=196,this.biaoji())}catch(t){if(!(t instanceof L.RecognitionException))throw t;e.exception=t,this._errHandler.reportError(this,t),this._errHandler.recover(this,t)}finally{this.exitRule()}return e}databaseBefore(){let t=new N(this._ctx,this.state);this.enterRule(t,4,g.RULE_databaseBefore);try{this.enterOuterAlt(t,1),this.state=199,this.match(g.DB),this.state=200,this.biaoji()}catch(e){if(!(e instanceof L.RecognitionException))throw e;t.exception=e,this._errHandler.reportError(this,e),this._errHandler.recover(this,e)}finally{this.exitRule()}return t}biaoji(){let t=new v(this._ctx,this.state);this.enterRule(t,6,g.RULE_biaoji);try{this.enterOuterAlt(t,1),this.state=202,this.match(g.DOT_)}catch(e){if(!(e instanceof L.RecognitionException))throw e;t.exception=e,this._errHandler.reportError(this,e),this._errHandler.recover(this,e)}finally{this.exitRule()}return t}optional(){let t=new U(this._ctx,this.state);this.enterRule(t,8,g.RULE_optional);try{this.enterOuterAlt(t,1),this.state=204,this.jsonValue()}catch(e){if(!(e instanceof L.RecognitionException))throw e;t.exception=e,this._errHandler.reportError(this,e),this._errHandler.recover(this,e)}finally{this.exitRule()}return t}booleanOptional(){let t,e=new m(this._ctx,this.state);this.enterRule(e,10,g.RULE_booleanOptional);try{this.enterOuterAlt(e,1),this.state=206,t=this._input.LA(1),t!==g.TRUE&&t!==g.FLASE?this._errHandler.recoverInline(this):(this._input.LA(1)===I.Token.EOF&&(this.matchedEOF=!0),this._errHandler.reportMatch(this),this.consume())}catch(t){if(!(t instanceof L.RecognitionException))throw t;e.exception=t,this._errHandler.reportError(this,t),this._errHandler.recover(this,t)}finally{this.exitRule()}return e}simpleChar(){let t=new P(this._ctx,this.state);this.enterRule(t,12,g.RULE_simpleChar);try{this.enterOuterAlt(t,1),this.state=208,this.match(g.STRING)}catch(e){if(!(e instanceof L.RecognitionException))throw e;t.exception=e,this._errHandler.reportError(this,e),this._errHandler.recover(this,e)}finally{this.exitRule()}return t}simpleArray(){let t=new f(this._ctx,this.state);this.enterRule(t,14,g.RULE_simpleArray);try{this.enterOuterAlt(t,1),this.state=210,this.array()}catch(e){if(!(e instanceof L.RecognitionException))throw e;t.exception=e,this._errHandler.reportError(this,e),this._errHandler.recover(this,e)}finally{this.exitRule()}return t}searchValue(){let t=new y(this._ctx,this.state);this.enterRule(t,16,g.RULE_searchValue);try{this.enterOuterAlt(t,1),this.state=212,this.jsonValue(),this.state=213,this.match(g.COMMA_)}catch(e){if(!(e instanceof L.RecognitionException))throw e;t.exception=e,this._errHandler.reportError(this,e),this._errHandler.recover(this,e)}finally{this.exitRule()}return t}updateValue(){let t=new M(this._ctx,this.state);this.enterRule(t,18,g.RULE_updateValue);try{this.enterOuterAlt(t,1),this.state=215,this.jsonValue()}catch(e){if(!(e instanceof L.RecognitionException))throw e;t.exception=e,this._errHandler.reportError(this,e),this._errHandler.recover(this,e)}finally{this.exitRule()}return t}aggregateOrder(){let t=new G(this._ctx,this.state);this.enterRule(t,20,g.RULE_aggregateOrder);try{this.enterOuterAlt(t,1),this.state=217,this.match(g.AGGREGATE)}catch(e){if(!(e instanceof L.RecognitionException))throw e;t.exception=e,this._errHandler.reportError(this,e),this._errHandler.recover(this,e)}finally{this.exitRule()}return t}collection(){let t=new H(this._ctx,this.state);this.enterRule(t,22,g.RULE_collection);try{this.enterOuterAlt(t,1),this.state=219,this.match(g.IDENTIFIER_)}catch(e){if(!(e instanceof L.RecognitionException))throw e;t.exception=e,this._errHandler.reportError(this,e),this._errHandler.recover(this,e)}finally{this.exitRule()}return t}jsonValue(){let t=new w(this._ctx,this.state);this.enterRule(t,24,g.RULE_jsonValue);try{switch(this.state=228,this._errHandler.sync(this),this._input.LA(1)){case g.STRING:this.enterOuterAlt(t,1),this.state=221,this.match(g.STRING);break;case g.NUMBER:this.enterOuterAlt(t,2),this.state=222,this.match(g.NUMBER);break;case g.LBE_:this.enterOuterAlt(t,3),this.state=223,this.obj();break;case g.LBT_:this.enterOuterAlt(t,4),this.state=224,this.array();break;case g.TRUE:this.enterOuterAlt(t,5),this.state=225,this.match(g.TRUE);break;case g.FALSE:this.enterOuterAlt(t,6),this.state=226,this.match(g.FALSE);break;case g.NULL:this.enterOuterAlt(t,7),this.state=227,this.match(g.NULL);break;default:throw new T.NoViableAltException(this)}}catch(e){if(!(e instanceof L.RecognitionException))throw e;t.exception=e,this._errHandler.reportError(this,e),this._errHandler.recover(this,e)}finally{this.exitRule()}return t}obj(){let t,e=new B(this._ctx,this.state);this.enterRule(e,26,g.RULE_obj);try{switch(this.state=243,this._errHandler.sync(this),this.interpreter.adaptivePredict(this._input,4,this._ctx)){case 1:for(this.enterOuterAlt(e,1),this.state=230,this.match(g.LBE_),this.state=231,this.pair(),this.state=236,this._errHandler.sync(this),t=this._input.LA(1);t===g.COMMA_;)this.state=232,this.match(g.COMMA_),this.state=233,this.pair(),this.state=238,this._errHandler.sync(this),t=this._input.LA(1);this.state=239,this.match(g.RBE_);break;case 2:this.enterOuterAlt(e,2),this.state=241,this.match(g.LBE_),this.state=242,this.match(g.RBE_)}}catch(t){if(!(t instanceof L.RecognitionException))throw t;e.exception=t,this._errHandler.reportError(this,t),this._errHandler.recover(this,t)}finally{this.exitRule()}return e}pair(){let t=new V(this._ctx,this.state);this.enterRule(t,28,g.RULE_pair);try{this.enterOuterAlt(t,1),this.state=245,this.match(g.STRING),this.state=246,this.match(g.COLON_),this.state=247,this.jsonValue()}catch(e){if(!(e instanceof L.RecognitionException))throw e;t.exception=e,this._errHandler.reportError(this,e),this._errHandler.recover(this,e)}finally{this.exitRule()}return t}array(){let t,e=new b(this._ctx,this.state);this.enterRule(e,30,g.RULE_array);try{switch(this.state=262,this._errHandler.sync(this),this.interpreter.adaptivePredict(this._input,6,this._ctx)){case 1:for(this.enterOuterAlt(e,1),this.state=249,this.match(g.LBT_),this.state=250,this.jsonValue(),this.state=255,this._errHandler.sync(this),t=this._input.LA(1);t===g.COMMA_;)this.state=251,this.match(g.COMMA_),this.state=252,this.jsonValue(),this.state=257,this._errHandler.sync(this),t=this._input.LA(1);this.state=258,this.match(g.RBT_);break;case 2:this.enterOuterAlt(e,2),this.state=260,this.match(g.LBT_),this.state=261,this.match(g.RBT_)}}catch(t){if(!(t instanceof L.RecognitionException))throw t;e.exception=t,this._errHandler.reportError(this,t),this._errHandler.recover(this,t)}finally{this.exitRule()}return e}dbAggregate(){let t,e=new k(this._ctx,this.state);this.enterRule(e,32,g.RULE_dbAggregate);try{this.enterOuterAlt(e,1),this.state=264,this.match(g.DB),this.state=265,this.match(g.DOT_),this.state=266,this.dbAggregateOrder(),this.state=267,this.match(g.LP_),this.state=268,this.jsonValue(),this.state=271,this._errHandler.sync(this),t=this._input.LA(1),t===g.COMMA_&&(this.state=269,this.match(g.COMMA_),this.state=270,this.optional()),this.state=273,this.match(g.RP_)}catch(t){if(!(t instanceof L.RecognitionException))throw t;e.exception=t,this._errHandler.reportError(this,t),this._errHandler.recover(this,t)}finally{this.exitRule()}return e}dbAggregateOrder(){let t=new F(this._ctx,this.state);this.enterRule(t,34,g.RULE_dbAggregateOrder);try{this.enterOuterAlt(t,1),this.state=275,this.match(g.AGGREGATE)}catch(e){if(!(e instanceof L.RecognitionException))throw e;t.exception=e,this._errHandler.reportError(this,e),this._errHandler.recover(this,e)}finally{this.exitRule()}return t}createCollection(){let t,e=new j(this._ctx,this.state);this.enterRule(e,36,g.RULE_createCollection);try{this.enterOuterAlt(e,1),this.state=277,this.match(g.DB),this.state=278,this.match(g.DOT_),this.state=279,this.createCollectionOrder(),this.state=280,this.match(g.LP_),this.state=281,this.simpleChar(),this.state=284,this._errHandler.sync(this),t=this._input.LA(1),t===g.COMMA_&&(this.state=282,this.match(g.COMMA_),this.state=283,this.optional()),this.state=286,this.match(g.RP_)}catch(t){if(!(t instanceof L.RecognitionException))throw t;e.exception=t,this._errHandler.reportError(this,t),this._errHandler.recover(this,t)}finally{this.exitRule()}return e}createCollectionOrder(){let t=new Q(this._ctx,this.state);this.enterRule(t,38,g.RULE_createCollectionOrder);try{this.enterOuterAlt(t,1),this.state=288,this.match(g.CREATECOLLECTION)}catch(e){if(!(e instanceof L.RecognitionException))throw e;t.exception=e,this._errHandler.reportError(this,e),this._errHandler.recover(this,e)}finally{this.exitRule()}return t}getCollection(){let t=new z(this._ctx,this.state);this.enterRule(t,40,g.RULE_getCollection);try{this.enterOuterAlt(t,1),this.state=290,this.match(g.DB),this.state=291,this.match(g.DOT_),this.state=292,this.getCollectionOrder(),this.state=293,this.match(g.LP_),this.state=294,this.simpleChar(),this.state=295,this.match(g.RP_)}catch(e){if(!(e instanceof L.RecognitionException))throw e;t.exception=e,this._errHandler.reportError(this,e),this._errHandler.recover(this,e)}finally{this.exitRule()}return t}getCollectionOrder(){let t=new X(this._ctx,this.state);this.enterRule(t,42,g.RULE_getCollectionOrder);try{this.enterOuterAlt(t,1),this.state=297,this.match(g.GETCOLLECTION)}catch(e){if(!(e instanceof L.RecognitionException))throw e;t.exception=e,this._errHandler.reportError(this,e),this._errHandler.recover(this,e)}finally{this.exitRule()}return t}getName(){let t=new Y(this._ctx,this.state);this.enterRule(t,44,g.RULE_getName);try{this.enterOuterAlt(t,1),this.state=299,this.match(g.DB),this.state=300,this.match(g.DOT_),this.state=301,this.nameOrder(),this.state=302,this.match(g.LP_),this.state=303,this.match(g.RP_)}catch(e){if(!(e instanceof L.RecognitionException))throw e;t.exception=e,this._errHandler.reportError(this,e),this._errHandler.recover(this,e)}finally{this.exitRule()}return t}nameOrder(){let t=new K(this._ctx,this.state);this.enterRule(t,46,g.RULE_nameOrder);try{this.enterOuterAlt(t,1),this.state=305,this.match(g.GETNAME)}catch(e){if(!(e instanceof L.RecognitionException))throw e;t.exception=e,this._errHandler.reportError(this,e),this._errHandler.recover(this,e)}finally{this.exitRule()}return t}dropDataBase(){let t=new Z(this._ctx,this.state);this.enterRule(t,48,g.RULE_dropDataBase);try{this.enterOuterAlt(t,1),this.state=307,this.match(g.DB),this.state=308,this.match(g.DOT_),this.state=309,this.dropHelpOrder(),this.state=310,this.match(g.LP_),this.state=311,this.match(g.RP_)}catch(e){if(!(e instanceof L.RecognitionException))throw e;t.exception=e,this._errHandler.reportError(this,e),this._errHandler.recover(this,e)}finally{this.exitRule()}return t}dropHelpOrder(){let t,e=new W(this._ctx,this.state);this.enterRule(e,50,g.RULE_dropHelpOrder);try{this.enterOuterAlt(e,1),this.state=313,t=this._input.LA(1),0!=(t-33&-32)||0==(1<<t-33&(1<<g.DROPDATABASE-33|1<<g.HELP-33|1<<g.VERSION-33|1<<g.SERVERBUILDINFO-33|1<<g.SERVERSTATUS-33|1<<g.ISMASTER-33|1<<g.HOSTINFO-33|1<<g.SERVERCMDLINEOPTS-33|1<<g.GETPROFILINGSTATUS-33|1<<g.GETPROFILINGLEVEL-33))?this._errHandler.recoverInline(this):(this._input.LA(1)===I.Token.EOF&&(this.matchedEOF=!0),this._errHandler.reportMatch(this),this.consume())}catch(t){if(!(t instanceof L.RecognitionException))throw t;e.exception=t,this._errHandler.reportError(this,t),this._errHandler.recover(this,t)}finally{this.exitRule()}return e}createView(){let t,e=new J(this._ctx,this.state);this.enterRule(e,52,g.RULE_createView);try{this.enterOuterAlt(e,1),this.state=315,this.match(g.DB),this.state=316,this.match(g.DOT_),this.state=317,this.createViewOrder(),this.state=318,this.match(g.LP_),this.state=319,this.viewChar(),this.state=320,this.sourceChar(),this.state=321,this.jsonValue(),this.state=324,this._errHandler.sync(this),t=this._input.LA(1),t===g.COMMA_&&(this.state=322,this.match(g.COMMA_),this.state=323,this.optional()),this.state=326,this.match(g.RP_)}catch(t){if(!(t instanceof L.RecognitionException))throw t;e.exception=t,this._errHandler.reportError(this,t),this._errHandler.recover(this,t)}finally{this.exitRule()}return e}createViewOrder(){let t=new $(this._ctx,this.state);this.enterRule(t,54,g.RULE_createViewOrder);try{this.enterOuterAlt(t,1),this.state=328,this.match(g.CREATEVIEW)}catch(e){if(!(e instanceof L.RecognitionException))throw e;t.exception=e,this._errHandler.reportError(this,e),this._errHandler.recover(this,e)}finally{this.exitRule()}return t}viewChar(){let t=new q(this._ctx,this.state);this.enterRule(t,56,g.RULE_viewChar);try{this.enterOuterAlt(t,1),this.state=330,this.simpleChar(),this.state=331,this.match(g.COMMA_)}catch(e){if(!(e instanceof L.RecognitionException))throw e;t.exception=e,this._errHandler.reportError(this,e),this._errHandler.recover(this,e)}finally{this.exitRule()}return t}sourceChar(){let t=new tt(this._ctx,this.state);this.enterRule(t,58,g.RULE_sourceChar);try{this.enterOuterAlt(t,1),this.state=333,this.simpleChar(),this.state=334,this.match(g.COMMA_)}catch(e){if(!(e instanceof L.RecognitionException))throw e;t.exception=e,this._errHandler.reportError(this,e),this._errHandler.recover(this,e)}finally{this.exitRule()}return t}use(){let t=new et(this._ctx,this.state);this.enterRule(t,60,g.RULE_use);try{this.enterOuterAlt(t,1),this.state=336,this.useOrder(),this.state=337,this.simpleChar()}catch(e){if(!(e instanceof L.RecognitionException))throw e;t.exception=e,this._errHandler.reportError(this,e),this._errHandler.recover(this,e)}finally{this.exitRule()}return t}useOrder(){let t=new rt(this._ctx,this.state);this.enterRule(t,62,g.RULE_useOrder);try{this.enterOuterAlt(t,1),this.state=339,this.match(g.USE)}catch(e){if(!(e instanceof L.RecognitionException))throw e;t.exception=e,this._errHandler.reportError(this,e),this._errHandler.recover(this,e)}finally{this.exitRule()}return t}showDbs(){let t=new it(this._ctx,this.state);this.enterRule(t,64,g.RULE_showDbs);try{this.enterOuterAlt(t,1),this.state=341,this.match(g.SHOW),this.state=342,this.match(g.DBS)}catch(e){if(!(e instanceof L.RecognitionException))throw e;t.exception=e,this._errHandler.reportError(this,e),this._errHandler.recover(this,e)}finally{this.exitRule()}return t}dbStats(){let t,e=new st(this._ctx,this.state);this.enterRule(e,66,g.RULE_dbStats);try{this.enterOuterAlt(e,1),this.state=344,this.match(g.DB),this.state=345,this.match(g.DOT_),this.state=346,this.dbStatsOrder(),this.state=347,this.match(g.LP_),this.state=349,this._errHandler.sync(this),t=this._input.LA(1),(0==(t-60&-32)&&0!=(1<<t-60&(1<<g.STRING-60|1<<g.NUMBER-60|1<<g.TRUE-60|1<<g.NULL-60))||0==(t-95&-32)&&0!=(1<<t-95&(1<<g.LBE_-95|1<<g.LBT_-95|1<<g.FALSE-95)))&&(this.state=348,this.optional()),this.state=351,this.match(g.RP_)}catch(t){if(!(t instanceof L.RecognitionException))throw t;e.exception=t,this._errHandler.reportError(this,t),this._errHandler.recover(this,t)}finally{this.exitRule()}return e}dbStatsOrder(){let t=new nt(this._ctx,this.state);this.enterRule(t,68,g.RULE_dbStatsOrder);try{this.enterOuterAlt(t,1),this.state=353,this.match(g.STATS)}catch(e){if(!(e instanceof L.RecognitionException))throw e;t.exception=e,this._errHandler.reportError(this,e),this._errHandler.recover(this,e)}finally{this.exitRule()}return t}insert(){let t=new ht(this._ctx,this.state);this.enterRule(t,70,g.RULE_insert);try{this.enterOuterAlt(t,1),this.state=355,this.mongoBefore(),this.state=356,this.insertOrder()}catch(e){if(!(e instanceof L.RecognitionException))throw e;t.exception=e,this._errHandler.reportError(this,e),this._errHandler.recover(this,e)}finally{this.exitRule()}return t}insertOrder(){let t,e=new at(this._ctx,this.state);this.enterRule(e,72,g.RULE_insertOrder);try{this.enterOuterAlt(e,1),this.state=358,t=this._input.LA(1),0!=(-32&t)||0==(1<<t&(1<<g.INSERT|1<<g.INSERTONE|1<<g.INSERTMANY))?this._errHandler.recoverInline(this):(this._input.LA(1)===I.Token.EOF&&(this.matchedEOF=!0),this._errHandler.reportMatch(this),this.consume()),this.state=359,this.match(g.LP_),this.state=360,this.insertValues(),this.state=363,this._errHandler.sync(this),t=this._input.LA(1),t===g.COMMA_&&(this.state=361,this.match(g.COMMA_),this.state=362,this.insertOptional()),this.state=365,this.match(g.RP_)}catch(t){if(!(t instanceof L.RecognitionException))throw t;e.exception=t,this._errHandler.reportError(this,t),this._errHandler.recover(this,t)}finally{this.exitRule()}return e}insertValues(){let t=new ot(this._ctx,this.state);this.enterRule(t,74,g.RULE_insertValues);try{this.enterOuterAlt(t,1),this.state=367,this.jsonValue()}catch(e){if(!(e instanceof L.RecognitionException))throw e;t.exception=e,this._errHandler.reportError(this,e),this._errHandler.recover(this,e)}finally{this.exitRule()}return t}insertOptional(){let t=new lt(this._ctx,this.state);this.enterRule(t,76,g.RULE_insertOptional);try{this.enterOuterAlt(t,1),this.state=369,this.optional()}catch(e){if(!(e instanceof L.RecognitionException))throw e;t.exception=e,this._errHandler.reportError(this,e),this._errHandler.recover(this,e)}finally{this.exitRule()}return t}update(){let t=new ut(this._ctx,this.state);this.enterRule(t,78,g.RULE_update);try{this.enterOuterAlt(t,1),this.state=371,this.mongoBefore(),this.state=372,this.updateOrder(),this.state=373,this.match(g.LP_),this.state=374,this.searchValue(),this.state=375,this.updateValue(),this.state=376,this.match(g.RP_)}catch(e){if(!(e instanceof L.RecognitionException))throw e;t.exception=e,this._errHandler.reportError(this,e),this._errHandler.recover(this,e)}finally{this.exitRule()}return t}updateOrder(){let t,e=new ct(this._ctx,this.state);this.enterRule(e,80,g.RULE_updateOrder);try{this.enterOuterAlt(e,1),this.state=378,t=this._input.LA(1),0!=(-32&t)||0==(1<<t&(1<<g.UPDATE|1<<g.UPDATEONE|1<<g.UPDATEMANY|1<<g.REPLACEONE))?this._errHandler.recoverInline(this):(this._input.LA(1)===I.Token.EOF&&(this.matchedEOF=!0),this._errHandler.reportMatch(this),this.consume())}catch(t){if(!(t instanceof L.RecognitionException))throw t;e.exception=t,this._errHandler.reportError(this,t),this._errHandler.recover(this,t)}finally{this.exitRule()}return e}delete(){let t=new dt(this._ctx,this.state);this.enterRule(t,82,g.RULE_delete);try{this.enterOuterAlt(t,1),this.state=380,this.mongoBefore(),this.state=381,this.deleteOrder(),this.state=382,this.match(g.LP_),this.state=383,this.deleteValue(),this.state=384,this.match(g.RP_)}catch(e){if(!(e instanceof L.RecognitionException))throw e;t.exception=e,this._errHandler.reportError(this,e),this._errHandler.recover(this,e)}finally{this.exitRule()}return t}deleteOrder(){let t,e=new Et(this._ctx,this.state);this.enterRule(e,84,g.RULE_deleteOrder);try{this.enterOuterAlt(e,1),this.state=386,t=this._input.LA(1),0!=(-32&t)||0==(1<<t&(1<<g.DELETEONE|1<<g.DELETEMANY|1<<g.REMOVE))?this._errHandler.recoverInline(this):(this._input.LA(1)===I.Token.EOF&&(this.matchedEOF=!0),this._errHandler.reportMatch(this),this.consume())}catch(t){if(!(t instanceof L.RecognitionException))throw t;e.exception=t,this._errHandler.reportError(this,t),this._errHandler.recover(this,t)}finally{this.exitRule()}return e}deleteValue(){let t=new Rt(this._ctx,this.state);this.enterRule(t,86,g.RULE_deleteValue);try{this.enterOuterAlt(t,1),this.state=388,this.jsonValue()}catch(e){if(!(e instanceof L.RecognitionException))throw e;t.exception=e,this._errHandler.reportError(this,e),this._errHandler.recover(this,e)}finally{this.exitRule()}return t}createIndex(){let t,e=new _t(this._ctx,this.state);this.enterRule(e,88,g.RULE_createIndex);try{this.enterOuterAlt(e,1),this.state=390,this.mongoBefore(),this.state=391,this.createIndexOrder(),this.state=392,this.match(g.LP_),this.state=393,this.jsonValue(),this.state=396,this._errHandler.sync(this),t=this._input.LA(1),t===g.COMMA_&&(this.state=394,this.match(g.COMMA_),this.state=395,this.optional()),this.state=398,this.match(g.RP_)}catch(t){if(!(t instanceof L.RecognitionException))throw t;e.exception=t,this._errHandler.reportError(this,t),this._errHandler.recover(this,t)}finally{this.exitRule()}return e}createIndexOrder(){let t,e=new Ot(this._ctx,this.state);this.enterRule(e,90,g.RULE_createIndexOrder);try{this.enterOuterAlt(e,1),this.state=400,t=this._input.LA(1),t!==g.CREATEINDEX&&t!==g.CREATEINDEXES?this._errHandler.recoverInline(this):(this._input.LA(1)===I.Token.EOF&&(this.matchedEOF=!0),this._errHandler.reportMatch(this),this.consume())}catch(t){if(!(t instanceof L.RecognitionException))throw t;e.exception=t,this._errHandler.reportError(this,t),this._errHandler.recover(this,t)}finally{this.exitRule()}return e}drop(){let t=new xt(this._ctx,this.state);this.enterRule(t,92,g.RULE_drop);try{switch(this.enterOuterAlt(t,1),this.state=402,this.mongoBefore(),this.state=403,this.dropOrder(),this.state=404,this.match(g.LP_),this.state=408,this._errHandler.sync(this),this.interpreter.adaptivePredict(this._input,13,this._ctx)){case 1:this.state=405,this.simpleChar();break;case 2:this.state=406,this.simpleArray();break;case 3:this.state=407,this.jsonValue()}this.state=410,this.match(g.RP_)}catch(e){if(!(e instanceof L.RecognitionException))throw e;t.exception=e,this._errHandler.reportError(this,e),this._errHandler.recover(this,e)}finally{this.exitRule()}return t}dropOrder(){let t,e=new Tt(this._ctx,this.state);this.enterRule(e,94,g.RULE_dropOrder);try{switch(this.state=414,this._errHandler.sync(this),this._input.LA(1)){case g.DROP:this.enterOuterAlt(e,1),this.state=412,this.match(g.DROP);break;case g.DROPINDEX:case g.DROPINDEXES:this.enterOuterAlt(e,2),this.state=413,t=this._input.LA(1),t!==g.DROPINDEX&&t!==g.DROPINDEXES?this._errHandler.recoverInline(this):(this._input.LA(1)===I.Token.EOF&&(this.matchedEOF=!0),this._errHandler.reportMatch(this),this.consume());break;default:throw new T.NoViableAltException(this)}}catch(t){if(!(t instanceof L.RecognitionException))throw t;e.exception=t,this._errHandler.reportError(this,t),this._errHandler.recover(this,t)}finally{this.exitRule()}return e}renameCollection(){let t,e=new pt(this._ctx,this.state);this.enterRule(e,96,g.RULE_renameCollection);try{this.enterOuterAlt(e,1),this.state=416,this.mongoBefore(),this.state=417,this.renameOrder(),this.state=418,this.match(g.LP_),this.state=419,this.simpleChar(),this.state=422,this._errHandler.sync(this),t=this._input.LA(1),t===g.COMMA_&&(this.state=420,this.match(g.COMMA_),this.state=421,this.booleanOptional()),this.state=424,this.match(g.RP_)}catch(t){if(!(t instanceof L.RecognitionException))throw t;e.exception=t,this._errHandler.reportError(this,t),this._errHandler.recover(this,t)}finally{this.exitRule()}return e}renameOrder(){let t=new Ct(this._ctx,this.state);this.enterRule(t,98,g.RULE_renameOrder);try{this.enterOuterAlt(t,1),this.state=426,this.match(g.RENAMECOLLECTION)}catch(e){if(!(e instanceof L.RecognitionException))throw e;t.exception=e,this._errHandler.reportError(this,e),this._errHandler.recover(this,e)}finally{this.exitRule()}return t}select(){let t,e=new At(this._ctx,this.state);this.enterRule(e,100,g.RULE_select);try{this.enterOuterAlt(e,1),this.state=428,this.mongoBefore(),this.state=429,this.selectOrder(),this.state=430,this.match(g.LP_),this.state=432,this._errHandler.sync(this),t=this._input.LA(1),(0==(t-60&-32)&&0!=(1<<t-60&(1<<g.STRING-60|1<<g.NUMBER-60|1<<g.TRUE-60|1<<g.NULL-60))||0==(t-95&-32)&&0!=(1<<t-95&(1<<g.LBE_-95|1<<g.LBT_-95|1<<g.FALSE-95)))&&(this.state=431,this.queryValue()),this.state=434,this.match(g.RP_),this.state=436,this._errHandler.sync(this),t=this._input.LA(1),t===g.DOT_&&(this.state=435,this.conditionOrder())}catch(t){if(!(t instanceof L.RecognitionException))throw t;e.exception=t,this._errHandler.reportError(this,t),this._errHandler.recover(this,t)}finally{this.exitRule()}return e}selectOrder(){let t,e=new Lt(this._ctx,this.state);this.enterRule(e,102,g.RULE_selectOrder);try{this.enterOuterAlt(e,1),this.state=438,t=this._input.LA(1),t!==g.FIND&&t!==g.FINDONE?this._errHandler.recoverInline(this):(this._input.LA(1)===I.Token.EOF&&(this.matchedEOF=!0),this._errHandler.reportMatch(this),this.consume())}catch(t){if(!(t instanceof L.RecognitionException))throw t;e.exception=t,this._errHandler.reportError(this,t),this._errHandler.recover(this,t)}finally{this.exitRule()}return e}queryValue(){let t=new It(this._ctx,this.state);this.enterRule(t,104,g.RULE_queryValue);try{this.enterOuterAlt(t,1),this.state=440,this.jsonValue()}catch(e){if(!(e instanceof L.RecognitionException))throw e;t.exception=e,this._errHandler.reportError(this,e),this._errHandler.recover(this,e)}finally{this.exitRule()}return t}conditionOrder(){let t,e=new gt(this._ctx,this.state);this.enterRule(e,106,g.RULE_conditionOrder);try{switch(this.enterOuterAlt(e,1),this.state=442,this.match(g.DOT_),this.state=443,this.findOption(),this.state=444,this.match(g.LP_),this.state=447,this._errHandler.sync(this),this.interpreter.adaptivePredict(this._input,18,this._ctx)){case 1:this.state=445,this.numberOrder();break;case 2:this.state=446,this.jsonValue()}this.state=449,this.match(g.RP_),this.state=451,this._errHandler.sync(this),t=this._input.LA(1),t===g.DOT_&&(this.state=450,this.conditionOrder())}catch(t){if(!(t instanceof L.RecognitionException))throw t;e.exception=t,this._errHandler.reportError(this,t),this._errHandler.recover(this,t)}finally{this.exitRule()}return e}findOption(){let t,e=new Dt(this._ctx,this.state);this.enterRule(e,108,g.RULE_findOption);try{this.enterOuterAlt(e,1),this.state=453,t=this._input.LA(1),0!=(t-47&-32)||0==(1<<t-47&(1<<g.LIMIT-47|1<<g.SORT-47|1<<g.SKIPINFO-47))?this._errHandler.recoverInline(this):(this._input.LA(1)===I.Token.EOF&&(this.matchedEOF=!0),this._errHandler.reportMatch(this),this.consume())}catch(t){if(!(t instanceof L.RecognitionException))throw t;e.exception=t,this._errHandler.reportError(this,t),this._errHandler.recover(this,t)}finally{this.exitRule()}return e}numberOrder(){let t=new St(this._ctx,this.state);this.enterRule(t,110,g.RULE_numberOrder);try{this.enterOuterAlt(t,1),this.state=455,this.match(g.NUMBER)}catch(e){if(!(e instanceof L.RecognitionException))throw e;t.exception=e,this._errHandler.reportError(this,e),this._errHandler.recover(this,e)}finally{this.exitRule()}return t}stats(){let t,e=new Nt(this._ctx,this.state);this.enterRule(e,112,g.RULE_stats);try{this.enterOuterAlt(e,1),this.state=457,this.mongoBefore(),this.state=458,this.statsOrder(),this.state=459,this.match(g.LP_),this.state=461,this._errHandler.sync(this),t=this._input.LA(1),(0==(t-60&-32)&&0!=(1<<t-60&(1<<g.STRING-60|1<<g.NUMBER-60|1<<g.TRUE-60|1<<g.NULL-60))||0==(t-95&-32)&&0!=(1<<t-95&(1<<g.LBE_-95|1<<g.LBT_-95|1<<g.FALSE-95)))&&(this.state=460,this.optional()),this.state=463,this.match(g.RP_)}catch(t){if(!(t instanceof L.RecognitionException))throw t;e.exception=t,this._errHandler.reportError(this,t),this._errHandler.recover(this,t)}finally{this.exitRule()}return e}statsOrder(){let t=new vt(this._ctx,this.state);this.enterRule(t,114,g.RULE_statsOrder);try{this.enterOuterAlt(t,1),this.state=465,this.match(g.STATS)}catch(e){if(!(e instanceof L.RecognitionException))throw e;t.exception=e,this._errHandler.reportError(this,e),this._errHandler.recover(this,e)}finally{this.exitRule()}return t}distinct(){let t,e=new Ut(this._ctx,this.state);this.enterRule(e,116,g.RULE_distinct);try{switch(this.enterOuterAlt(e,1),this.state=467,this.mongoBefore(),this.state=468,this.distinctOrder(),this.state=469,this.match(g.LP_),this.state=470,this.simpleChar(),this.state=473,this._errHandler.sync(this),this.interpreter.adaptivePredict(this._input,21,this._ctx)){case 1:this.state=471,this.match(g.COMMA_),this.state=472,this.jsonValue()}this.state=477,this._errHandler.sync(this),t=this._input.LA(1),t===g.COMMA_&&(this.state=475,this.match(g.COMMA_),this.state=476,this.optional()),this.state=479,this.match(g.RP_)}catch(t){if(!(t instanceof L.RecognitionException))throw t;e.exception=t,this._errHandler.reportError(this,t),this._errHandler.recover(this,t)}finally{this.exitRule()}return e}distinctOrder(){let t=new mt(this._ctx,this.state);this.enterRule(t,118,g.RULE_distinctOrder);try{this.enterOuterAlt(t,1),this.state=481,this.match(g.DISTINCT)}catch(e){if(!(e instanceof L.RecognitionException))throw e;t.exception=e,this._errHandler.reportError(this,e),this._errHandler.recover(this,e)}finally{this.exitRule()}return t}aggregate(){let t,e=new Pt(this._ctx,this.state);this.enterRule(e,120,g.RULE_aggregate);try{this.enterOuterAlt(e,1),this.state=483,this.mongoBefore(),this.state=484,this.aggregateOrder(),this.state=485,this.match(g.LP_),this.state=486,this.jsonValue(),this.state=489,this._errHandler.sync(this),t=this._input.LA(1),t===g.COMMA_&&(this.state=487,this.match(g.COMMA_),this.state=488,this.optional()),this.state=491,this.match(g.RP_)}catch(t){if(!(t instanceof L.RecognitionException))throw t;e.exception=t,this._errHandler.reportError(this,t),this._errHandler.recover(this,t)}finally{this.exitRule()}return e}countDocuments(){let t,e=new ft(this._ctx,this.state);this.enterRule(e,122,g.RULE_countDocuments);try{this.enterOuterAlt(e,1),this.state=493,this.mongoBefore(),this.state=494,this.countDocumentsOrder(),this.state=495,this.match(g.LP_),this.state=496,this.jsonValue(),this.state=499,this._errHandler.sync(this),t=this._input.LA(1),t===g.COMMA_&&(this.state=497,this.match(g.COMMA_),this.state=498,this.optional()),this.state=501,this.match(g.RP_)}catch(t){if(!(t instanceof L.RecognitionException))throw t;e.exception=t,this._errHandler.reportError(this,t),this._errHandler.recover(this,t)}finally{this.exitRule()}return e}countDocumentsOrder(){let t=new yt(this._ctx,this.state);this.enterRule(t,124,g.RULE_countDocumentsOrder);try{this.enterOuterAlt(t,1),this.state=503,this.match(g.COUNTDOCUMENTS)}catch(e){if(!(e instanceof L.RecognitionException))throw e;t.exception=e,this._errHandler.reportError(this,e),this._errHandler.recover(this,e)}finally{this.exitRule()}return t}findOneAndDelete(){let t,e=new Mt(this._ctx,this.state);this.enterRule(e,126,g.RULE_findOneAndDelete);try{this.enterOuterAlt(e,1),this.state=505,this.mongoBefore(),this.state=506,this.findOneDeleteOrder(),this.state=507,this.match(g.LP_),this.state=508,this.jsonValue(),this.state=511,this._errHandler.sync(this),t=this._input.LA(1),t===g.COMMA_&&(this.state=509,this.match(g.COMMA_),this.state=510,this.optional()),this.state=513,this.match(g.RP_)}catch(t){if(!(t instanceof L.RecognitionException))throw t;e.exception=t,this._errHandler.reportError(this,t),this._errHandler.recover(this,t)}finally{this.exitRule()}return e}findOneDeleteOrder(){let t=new Gt(this._ctx,this.state);this.enterRule(t,128,g.RULE_findOneDeleteOrder);try{this.enterOuterAlt(t,1),this.state=515,this.match(g.FINDONEANDDELETE)}catch(e){if(!(e instanceof L.RecognitionException))throw e;t.exception=e,this._errHandler.reportError(this,e),this._errHandler.recover(this,e)}finally{this.exitRule()}return t}estimatedDocount(){let t,e=new Ht(this._ctx,this.state);this.enterRule(e,130,g.RULE_estimatedDocount);try{this.enterOuterAlt(e,1),this.state=517,this.mongoBefore(),this.state=518,this.estimatedOrder(),this.state=519,this.match(g.LP_),this.state=521,this._errHandler.sync(this),t=this._input.LA(1),(0==(t-60&-32)&&0!=(1<<t-60&(1<<g.STRING-60|1<<g.NUMBER-60|1<<g.TRUE-60|1<<g.NULL-60))||0==(t-95&-32)&&0!=(1<<t-95&(1<<g.LBE_-95|1<<g.LBT_-95|1<<g.FALSE-95)))&&(this.state=520,this.jsonValue()),this.state=523,this.match(g.RP_)}catch(t){if(!(t instanceof L.RecognitionException))throw t;e.exception=t,this._errHandler.reportError(this,t),this._errHandler.recover(this,t)}finally{this.exitRule()}return e}estimatedOrder(){let t=new wt(this._ctx,this.state);this.enterRule(t,132,g.RULE_estimatedOrder);try{this.enterOuterAlt(t,1),this.state=525,this.match(g.ESTIMATEDDOCUMENTCOUNT)}catch(e){if(!(e instanceof L.RecognitionException))throw e;t.exception=e,this._errHandler.reportError(this,e),this._errHandler.recover(this,e)}finally{this.exitRule()}return t}count(){let t,e=new Bt(this._ctx,this.state);this.enterRule(e,134,g.RULE_count);try{this.enterOuterAlt(e,1),this.state=527,this.mongoBefore(),this.state=528,this.countOrder(),this.state=529,this.match(g.LP_),this.state=531,this._errHandler.sync(this),t=this._input.LA(1),(0==(t-60&-32)&&0!=(1<<t-60&(1<<g.STRING-60|1<<g.NUMBER-60|1<<g.TRUE-60|1<<g.NULL-60))||0==(t-95&-32)&&0!=(1<<t-95&(1<<g.LBE_-95|1<<g.LBT_-95|1<<g.FALSE-95)))&&(this.state=530,this.jsonValue()),this.state=535,this._errHandler.sync(this),t=this._input.LA(1),t===g.COMMA_&&(this.state=533,this.match(g.COMMA_),this.state=534,this.optional()),this.state=537,this.match(g.RP_)}catch(t){if(!(t instanceof L.RecognitionException))throw t;e.exception=t,this._errHandler.reportError(this,t),this._errHandler.recover(this,t)}finally{this.exitRule()}return e}countOrder(){let t=new Vt(this._ctx,this.state);this.enterRule(t,136,g.RULE_countOrder);try{this.enterOuterAlt(t,1),this.state=539,this.match(g.COUNT)}catch(e){if(!(e instanceof L.RecognitionException))throw e;t.exception=e,this._errHandler.reportError(this,e),this._errHandler.recover(this,e)}finally{this.exitRule()}return t}findOneAndUpdate(){let t,e=new bt(this._ctx,this.state);this.enterRule(e,138,g.RULE_findOneAndUpdate);try{this.enterOuterAlt(e,1),this.state=541,this.mongoBefore(),this.state=542,this.findOneUpdateOrder(),this.state=543,this.match(g.LP_),this.state=544,this.searchValue(),this.state=545,this.updateValue(),this.state=548,this._errHandler.sync(this),t=this._input.LA(1),t===g.COMMA_&&(this.state=546,this.match(g.COMMA_),this.state=547,this.optional()),this.state=550,this.match(g.RP_)}catch(t){if(!(t instanceof L.RecognitionException))throw t;e.exception=t,this._errHandler.reportError(this,t),this._errHandler.recover(this,t)}finally{this.exitRule()}return e}findOneUpdateOrder(){let t=new kt(this._ctx,this.state);this.enterRule(t,140,g.RULE_findOneUpdateOrder);try{this.enterOuterAlt(t,1),this.state=552,this.match(g.FINDONEANDUPDATE)}catch(e){if(!(e instanceof L.RecognitionException))throw e;t.exception=e,this._errHandler.reportError(this,e),this._errHandler.recover(this,e)}finally{this.exitRule()}return t}findOneAndReplace(){let t,e=new Ft(this._ctx,this.state);this.enterRule(e,142,g.RULE_findOneAndReplace);try{this.enterOuterAlt(e,1),this.state=554,this.mongoBefore(),this.state=555,this.findOneReplaceOrder(),this.state=556,this.match(g.LP_),this.state=557,this.searchValue(),this.state=558,this.updateValue(),this.state=561,this._errHandler.sync(this),t=this._input.LA(1),t===g.COMMA_&&(this.state=559,this.match(g.COMMA_),this.state=560,this.optional()),this.state=563,this.match(g.RP_)}catch(t){if(!(t instanceof L.RecognitionException))throw t;e.exception=t,this._errHandler.reportError(this,t),this._errHandler.recover(this,t)}finally{this.exitRule()}return e}findOneReplaceOrder(){let t=new jt(this._ctx,this.state);this.enterRule(t,144,g.RULE_findOneReplaceOrder);try{this.enterOuterAlt(t,1),this.state=565,this.match(g.FINDONEANDREPLACE)}catch(e){if(!(e instanceof L.RecognitionException))throw e;t.exception=e,this._errHandler.reportError(this,e),this._errHandler.recover(this,e)}finally{this.exitRule()}return t}iscapped(){let t=new Qt(this._ctx,this.state);this.enterRule(t,146,g.RULE_iscapped);try{this.enterOuterAlt(t,1),this.state=567,this.mongoBefore(),this.state=568,this.isCappedOrder(),this.state=569,this.match(g.LP_),this.state=570,this.match(g.RP_)}catch(e){if(!(e instanceof L.RecognitionException))throw e;t.exception=e,this._errHandler.reportError(this,e),this._errHandler.recover(this,e)}finally{this.exitRule()}return t}isCappedOrder(){let t=new zt(this._ctx,this.state);this.enterRule(t,148,g.RULE_isCappedOrder);try{this.enterOuterAlt(t,1),this.state=572,this.match(g.ISCAPPED)}catch(e){if(!(e instanceof L.RecognitionException))throw e;t.exception=e,this._errHandler.reportError(this,e),this._errHandler.recover(this,e)}finally{this.exitRule()}return t}dataSize(){let t=new Xt(this._ctx,this.state);this.enterRule(t,150,g.RULE_dataSize);try{this.enterOuterAlt(t,1),this.state=574,this.mongoBefore(),this.state=575,this.sizeOrder(),this.state=576,this.match(g.LP_),this.state=577,this.match(g.RP_)}catch(e){if(!(e instanceof L.RecognitionException))throw e;t.exception=e,this._errHandler.reportError(this,e),this._errHandler.recover(this,e)}finally{this.exitRule()}return t}sizeOrder(){let t,e=new Yt(this._ctx,this.state);this.enterRule(e,152,g.RULE_sizeOrder);try{this.enterOuterAlt(e,1),this.state=579,t=this._input.LA(1),0!=(t-36&-32)||0==(1<<t-36&(1<<g.DATASIZE-36|1<<g.STORAGESIZE-36|1<<g.TOTALINDEXSIZE-36|1<<g.TOTALSIZE-36|1<<g.GETINDEXES-36|1<<g.HELP-36))?this._errHandler.recoverInline(this):(this._input.LA(1)===I.Token.EOF&&(this.matchedEOF=!0),this._errHandler.reportMatch(this),this.consume())}catch(t){if(!(t instanceof L.RecognitionException))throw t;e.exception=t,this._errHandler.reportError(this,t),this._errHandler.recover(this,t)}finally{this.exitRule()}return e}validate(){let t,e=new Kt(this._ctx,this.state);this.enterRule(e,154,g.RULE_validate);try{this.enterOuterAlt(e,1),this.state=581,this.mongoBefore(),this.state=582,this.validateOrder(),this.state=583,this.match(g.LP_),this.state=585,this._errHandler.sync(this),t=this._input.LA(1),t!==g.TRUE&&t!==g.FLASE||(this.state=584,this.booleanOptional()),this.state=587,this.match(g.RP_)}catch(t){if(!(t instanceof L.RecognitionException))throw t;e.exception=t,this._errHandler.reportError(this,t),this._errHandler.recover(this,t)}finally{this.exitRule()}return e}validateOrder(){let t=new Zt(this._ctx,this.state);this.enterRule(t,156,g.RULE_validateOrder);try{this.enterOuterAlt(t,1),this.state=589,this.match(g.VALIDATE)}catch(e){if(!(e instanceof L.RecognitionException))throw e;t.exception=e,this._errHandler.reportError(this,e),this._errHandler.recover(this,e)}finally{this.exitRule()}return t}latencyStats(){let t,e=new Wt(this._ctx,this.state);this.enterRule(e,158,g.RULE_latencyStats);try{this.enterOuterAlt(e,1),this.state=591,this.mongoBefore(),this.state=592,this.latencyStatsOrder(),this.state=593,this.match(g.LP_),this.state=595,this._errHandler.sync(this),t=this._input.LA(1),(0==(t-60&-32)&&0!=(1<<t-60&(1<<g.STRING-60|1<<g.NUMBER-60|1<<g.TRUE-60|1<<g.NULL-60))||0==(t-95&-32)&&0!=(1<<t-95&(1<<g.LBE_-95|1<<g.LBT_-95|1<<g.FALSE-95)))&&(this.state=594,this.optional()),this.state=597,this.match(g.RP_)}catch(t){if(!(t instanceof L.RecognitionException))throw t;e.exception=t,this._errHandler.reportError(this,t),this._errHandler.recover(this,t)}finally{this.exitRule()}return e}latencyStatsOrder(){let t=new Jt(this._ctx,this.state);this.enterRule(t,160,g.RULE_latencyStatsOrder);try{this.enterOuterAlt(t,1),this.state=599,this.match(g.LATENCYSTATS)}catch(e){if(!(e instanceof L.RecognitionException))throw e;t.exception=e,this._errHandler.reportError(this,e),this._errHandler.recover(this,e)}finally{this.exitRule()}return t}static get _ATN(){return g.__ATN||(g.__ATN=(new d.ATNDeserializer).deserialize(O.toCharArray(g._serializedATN))),g.__ATN}}g.WS=1,g.DB=2,g.INSERT=3,g.INSERTONE=4,g.INSERTMANY=5,g.UPDATE=6,g.UPDATEONE=7,g.UPDATEMANY=8,g.REPLACEONE=9,g.FIND=10,g.FINDONE=11,g.DELETEONE=12,g.DELETEMANY=13,g.REMOVE=14,g.STATS=15,g.DISTINCT=16,g.AGGREGATE=17,g.COUNTDOCUMENTS=18,g.CREATEINDEX=19,g.CREATEINDEXES=20,g.DROP=21,g.DROPINDEX=22,g.DROPINDEXES=23,g.FINDONEANDDELETE=24,g.RENAMECOLLECTION=25,g.ESTIMATEDDOCUMENTCOUNT=26,g.COUNT=27,g.FINDONEANDUPDATE=28,g.FINDONEANDREPLACE=29,g.CREATECOLLECTION=30,g.GETCOLLECTION=31,g.GETNAME=32,g.DROPDATABASE=33,g.CREATEVIEW=34,g.ISCAPPED=35,g.DATASIZE=36,g.STORAGESIZE=37,g.TOTALINDEXSIZE=38,g.TOTALSIZE=39,g.GETINDEXES=40,g.VALIDATE=41,g.LATENCYSTATS=42,g.USE=43,g.SHOW=44,g.DBS=45,g.HELP=46,g.LIMIT=47,g.SORT=48,g.VERSION=49,g.SERVERBUILDINFO=50,g.SERVERSTATUS=51,g.ISMASTER=52,g.HOSTINFO=53,g.SERVERCMDLINEOPTS=54,g.GETPROFILINGSTATUS=55,g.GETPROFILINGLEVEL=56,g.IDENTIFIER_=57,g.HEX_DIGIT_=58,g.BIT_NUM_=59,g.STRING=60,g.NUMBER=61,g.TRUE=62,g.FLASE=63,g.NULL=64,g.SKIPINFO=65,g.AND_=66,g.OR_=67,g.NOT_=68,g.TILDE_=69,g.VERTICAL_BAR_=70,g.AMPERSAND_=71,g.SIGNED_LEFT_SHIFT_=72,g.SIGNED_RIGHT_SHIFT_=73,g.CARET_=74,g.MOD_=75,g.COLON_=76,g.PLUS_=77,g.MINUS_=78,g.ASTERISK_=79,g.SLASH_=80,g.BACKSLASH_=81,g.DOT_=82,g.DOT_ASTERISK_=83,g.SAFE_EQ_=84,g.DEQ_=85,g.EQ_=86,g.NEQ_=87,g.GT_=88,g.GTE_=89,g.LT_=90,g.LTE_=91,g.POUND_=92,g.LP_=93,g.RP_=94,g.LBE_=95,g.RBE_=96,g.LBT_=97,g.RBT_=98,g.COMMA_=99,g.DQ_=100,g.SQ_=101,g.BQ_=102,g.QUESTION_=103,g.AT_=104,g.SEMI_=105,g.FOR_GENERATOR=106,g.FALSE=107,g.RULE_execute=0,g.RULE_mongoBefore=1,g.RULE_databaseBefore=2,g.RULE_biaoji=3,g.RULE_optional=4,g.RULE_booleanOptional=5,g.RULE_simpleChar=6,g.RULE_simpleArray=7,g.RULE_searchValue=8,g.RULE_updateValue=9,g.RULE_aggregateOrder=10,g.RULE_collection=11,g.RULE_jsonValue=12,g.RULE_obj=13,g.RULE_pair=14,g.RULE_array=15,g.RULE_dbAggregate=16,g.RULE_dbAggregateOrder=17,g.RULE_createCollection=18,g.RULE_createCollectionOrder=19,g.RULE_getCollection=20,g.RULE_getCollectionOrder=21,g.RULE_getName=22,g.RULE_nameOrder=23,g.RULE_dropDataBase=24,g.RULE_dropHelpOrder=25,g.RULE_createView=26,g.RULE_createViewOrder=27,g.RULE_viewChar=28,g.RULE_sourceChar=29,g.RULE_use=30,g.RULE_useOrder=31,g.RULE_showDbs=32,g.RULE_dbStats=33,g.RULE_dbStatsOrder=34,g.RULE_insert=35,g.RULE_insertOrder=36,g.RULE_insertValues=37,g.RULE_insertOptional=38,g.RULE_update=39,g.RULE_updateOrder=40,g.RULE_delete=41,g.RULE_deleteOrder=42,g.RULE_deleteValue=43,g.RULE_createIndex=44,g.RULE_createIndexOrder=45,g.RULE_drop=46,g.RULE_dropOrder=47,g.RULE_renameCollection=48,g.RULE_renameOrder=49,g.RULE_select=50,g.RULE_selectOrder=51,g.RULE_queryValue=52,g.RULE_conditionOrder=53,g.RULE_findOption=54,g.RULE_numberOrder=55,g.RULE_stats=56,g.RULE_statsOrder=57,g.RULE_distinct=58,g.RULE_distinctOrder=59,g.RULE_aggregate=60,g.RULE_countDocuments=61,g.RULE_countDocumentsOrder=62,g.RULE_findOneAndDelete=63,g.RULE_findOneDeleteOrder=64,g.RULE_estimatedDocount=65,g.RULE_estimatedOrder=66,g.RULE_count=67,g.RULE_countOrder=68,g.RULE_findOneAndUpdate=69,g.RULE_findOneUpdateOrder=70,g.RULE_findOneAndReplace=71,g.RULE_findOneReplaceOrder=72,g.RULE_iscapped=73,g.RULE_isCappedOrder=74,g.RULE_dataSize=75,g.RULE_sizeOrder=76,g.RULE_validate=77,g.RULE_validateOrder=78,g.RULE_latencyStats=79,g.RULE_latencyStatsOrder=80,g.ruleNames=["execute","mongoBefore","databaseBefore","biaoji","optional","booleanOptional","simpleChar","simpleArray","searchValue","updateValue","aggregateOrder","collection","jsonValue","obj","pair","array","dbAggregate","dbAggregateOrder","createCollection","createCollectionOrder","getCollection","getCollectionOrder","getName","nameOrder","dropDataBase","dropHelpOrder","createView","createViewOrder","viewChar","sourceChar","use","useOrder","showDbs","dbStats","dbStatsOrder","insert","insertOrder","insertValues","insertOptional","update","updateOrder","delete","deleteOrder","deleteValue","createIndex","createIndexOrder","drop","dropOrder","renameCollection","renameOrder","select","selectOrder","queryValue","conditionOrder","findOption","numberOrder","stats","statsOrder","distinct","distinctOrder","aggregate","countDocuments","countDocumentsOrder","findOneAndDelete","findOneDeleteOrder","estimatedDocount","estimatedOrder","count","countOrder","findOneAndUpdate","findOneUpdateOrder","findOneAndReplace","findOneReplaceOrder","iscapped","isCappedOrder","dataSize","sizeOrder","validate","validateOrder","latencyStats","latencyStatsOrder"],g._LITERAL_NAMES=[void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,"'true'","'false'","'null'",void 0,"'&&'","'||'","'!'","'~'","'|'","'&'","'<<'","'>>'","'^'","'%'","':'","'+'","'-'","'*'","'/'","'\\'","'.'","'.*'","'<=>'","'=='","'='",void 0,"'>'","'>='","'<'","'<='","'#'","'('","')'","'{'","'}'","'['","']'","','","'\"'","'''","'`'","'?'","'@'","';'","'DO NOT MATCH ANY THING, JUST FOR GENERATOR'"],g._SYMBOLIC_NAMES=[void 0,"WS","DB","INSERT","INSERTONE","INSERTMANY","UPDATE","UPDATEONE","UPDATEMANY","REPLACEONE","FIND","FINDONE","DELETEONE","DELETEMANY","REMOVE","STATS","DISTINCT","AGGREGATE","COUNTDOCUMENTS","CREATEINDEX","CREATEINDEXES","DROP","DROPINDEX","DROPINDEXES","FINDONEANDDELETE","RENAMECOLLECTION","ESTIMATEDDOCUMENTCOUNT","COUNT","FINDONEANDUPDATE","FINDONEANDREPLACE","CREATECOLLECTION","GETCOLLECTION","GETNAME","DROPDATABASE","CREATEVIEW","ISCAPPED","DATASIZE","STORAGESIZE","TOTALINDEXSIZE","TOTALSIZE","GETINDEXES","VALIDATE","LATENCYSTATS","USE","SHOW","DBS","HELP","LIMIT","SORT","VERSION","SERVERBUILDINFO","SERVERSTATUS","ISMASTER","HOSTINFO","SERVERCMDLINEOPTS","GETPROFILINGSTATUS","GETPROFILINGLEVEL","IDENTIFIER_","HEX_DIGIT_","BIT_NUM_","STRING","NUMBER","TRUE","FLASE","NULL","SKIPINFO","AND_","OR_","NOT_","TILDE_","VERTICAL_BAR_","AMPERSAND_","SIGNED_LEFT_SHIFT_","SIGNED_RIGHT_SHIFT_","CARET_","MOD_","COLON_","PLUS_","MINUS_","ASTERISK_","SLASH_","BACKSLASH_","DOT_","DOT_ASTERISK_","SAFE_EQ_","DEQ_","EQ_","NEQ_","GT_","GTE_","LT_","LTE_","POUND_","LP_","RP_","LBE_","RBE_","LBT_","RBT_","COMMA_","DQ_","SQ_","BQ_","QUESTION_","AT_","SEMI_","FOR_GENERATOR","FALSE"],g.VOCABULARY=new _.VocabularyImpl(g._LITERAL_NAMES,g._SYMBOLIC_NAMES,[]),g._serializedATNSegments=2,g._serializedATNSegment0='줝쪺֍꾺体؇쉁mɜ\t\t\t\t\t\t\b\t\b\t\t\t\n\t\n\v\t\v\f\t\f\r\t\r\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t \t !\t!"\t"#\t#$\t$%\t%&\t&\'\t\'(\t()\t)*\t*+\t+,\t,-\t-.\t./\t/0\t01\t12\t23\t34\t45\t56\t67\t78\t89\t9:\t:;\t;<\t<=\t=>\t>?\t?@\t@A\tAB\tBC\tCD\tDE\tEF\tFG\tGH\tHI\tIJ\tJK\tKL\tLM\tMN\tNO\tOP\tPQ\tQR\tRÃ\nÈ\n\b\b\t\t\n\n\n\v\v\f\f\r\rç\ní\n\fð\vö\nĀ\n\fă\vĉ\nĒ\nğ\nŇ\n   !!"""######Š\n###$$%%%&&&&&&Ů\n&&&\'\'(()))))))**++++++,,--.......Ə\n...//0000000ƛ\n000111ơ\n12222222Ʃ\n2223344444Ƴ\n4444Ʒ\n45566777777ǂ\n7777ǆ\n78899:::::ǐ\n:::;;<<<<<<<ǜ\n<<<<Ǡ\n<<<==>>>>>>>Ǭ\n>>>???????Ƕ\n???@@AAAAAAAȂ\nAAABBCCCCCȌ\nCCCDDEEEEEȖ\nEEEEȚ\nEEEFFGGGGGGGGȧ\nGGGHHIIIIIIIIȴ\nIIIJJKKKKKLLMMMMMNNOOOOOɌ\nOOOPPQQQQQɖ\nQQQRRRS\b\n\f "$&(*,.02468:<>@BDFHJLNPRTVXZ\\^`bdfhjlnprtvxz|~ ¢\f@A##003:\b\v\f\r12CC&*00ɎÂÄÉ\bÌ\nÎ\fÐÒÔÖÙÛÝæõ÷ Ĉ"Ċ$ĕ&ė(Ģ*Ĥ,ī.ĭ0ĳ2ĵ4Ļ6Ľ8Ŋ:Ō<ŏ>Œ@ŕBŗDŚFţHťJŨLűNųPŵRżTžVƄXƆZƈ\\ƒ^Ɣ`ƠbƢdƬfƮhƸjƺlƼnǇpǉrǋtǓvǕxǣzǥ|ǯ~ǹǻȅȇȏȑȝȟȪȬȷȹȾɀɅɇɏ ɑ¢ə¤ÃH%¥ÃP)¦Ãf4§ÃT+¨Ãr:©Ãv<ªÃz>«Ã|?¬ÃZ.­Ã^0®ÃA¯Ãb2°ÃC±ÃE²ÃG³ÃI´Ã"µÃ&¶Ã*·Ã.¸Ã2¹Ã6ºÃK»ÃM¼ÃO½Ã Q¾Ã> ¿ÃB"ÀÃD#ÁÃÂ¤Â¥Â¦Â§Â¨Â©ÂªÂ«Â¬Â­Â®Â¯Â°Â±Â²Â³Â´ÂµÂ¶Â·Â¸Â¹ÂºÂ»Â¼Â½Â¾Â¿ÂÀÂÁÃÄÅÅÇ\rÆÈ\bÇÆÇÈÈÉÊÊË\bËÌÍTÍ\tÎÏÏ\vÐÑ\tÑ\rÒÓ>ÓÔÕ ÕÖ××ØeØÙÚÚÛÜÜÝÞ;Þßç>àç?áçâç ãç@äçmåçBæßæàæáæâæãæäæåçèéaéîêëeëíìêíðîìîïïñðîñòbòöóôaôöbõèõóö÷ø>øùNùúúûücüāýþeþĀÿýĀăāÿāĂĂĄăāĄądąĉĆćcćĉdĈûĈĆĉ!ĊċċČTČč$čĎ_ĎđďĐeĐĒ\nđďđĒĒēēĔ`Ĕ#ĕĖĖ%ėĘĘęTęĚ(Ěě_ěĞ\bĜĝeĝğ\nĞĜĞğğĠĠġ`ġ\'Ģģ ģ)ĤĥĥĦTĦħ,ħĨ_Ĩĩ\bĩĪ`Ī+īĬ!Ĭ-ĭĮĮįTįİ0İı_ıĲ`Ĳ/ĳĴ"Ĵ1ĵĶĶķTķĸ4ĸĹ_Ĺĺ`ĺ3Ļļ\tļ5ĽľľĿTĿŀ8ŀŁ_Łł:łŃ<ŃņńŅeŅŇ\nņńņŇŇňňŉ`ŉ7Ŋŋ$ŋ9Ōō\bōŎeŎ;ŏŐ\bŐőeő=Œœ@!œŔ\bŔ?ŕŖ-ŖAŗŘ.Řř/řCŚśśŜTŜŝF$ŝş_ŞŠ\nşŞşŠŠššŢ`ŢEţŤŤGťŦŦŧJ&ŧIŨũ\tũŪ_ŪŭL\'ūŬeŬŮN(ŭūŭŮŮůůŰ`ŰKűŲŲMųŴ\nŴOŵŶŶŷR*ŷŸ_ŸŹ\nŹź\vźŻ`ŻQżŽ\tŽSžſſƀV,ƀƁ_ƁƂX-Ƃƃ`ƃUƄƅ\tƅWƆƇƇYƈƉƉƊ\\/ƊƋ_ƋƎƌƍeƍƏ\nƎƌƎƏƏƐƐƑ`Ƒ[ƒƓ\tƓ]ƔƕƕƖ`1Ɩƚ_Ɨƛ\bƘƛ\tƙƛƚƗƚƘƚƙƚƛƛƜƜƝ`Ɲ_ƞơƟơ\t\bƠƞƠƟơaƢƣƣƤd3Ƥƥ_ƥƨ\bƦƧeƧƩ\fƨƦƨƩƩƪƪƫ`ƫcƬƭƭeƮƯƯưh5ưƲ_ƱƳj6ƲƱƲƳƳƴƴƶ`ƵƷl7ƶƵƶƷƷgƸƹ\t\tƹiƺƻƻkƼƽTƽƾn8ƾǁ_ƿǂp9ǀǂǁƿǁǀǂǃǃǅ`Ǆǆl7ǅǄǅǆǆmǇǈ\t\nǈoǉǊ?ǊqǋǌǌǍt;ǍǏ_ǎǐ\nǏǎǏǐǐǑǑǒ`ǒsǓǔǔuǕǖǖǗx=Ǘǘ_ǘǛ\bǙǚeǚǜǛǙǛǜǜǟǝǞeǞǠ\nǟǝǟǠǠǡǡǢ`ǢwǣǤǤyǥǦǦǧ\fǧǨ_ǨǫǩǪeǪǬ\nǫǩǫǬǬǭǭǮ`Ǯ{ǯǰǰǱ~@Ǳǲ_ǲǵǳǴeǴǶ\nǵǳǵǶǶǷǷǸ`Ǹ}ǹǺǺǻǼǼǽBǽǾ_ǾȁǿȀeȀȂ\nȁǿȁȂȂȃȃȄ`ȄȅȆȆȇȈȈȉDȉȋ_ȊȌȋȊȋȌȌȍȍȎ`ȎȏȐȐȑȒȒȓFȓȕ_ȔȖȕȔȕȖȖșȗȘeȘȚ\nșȗșȚȚțțȜ`ȜȝȞȞȟȠȠȡHȡȢ_Ȣȣ\nȣȦ\vȤȥeȥȧ\nȦȤȦȧȧȨȨȩ`ȩȪȫȫȬȭȭȮJȮȯ_ȯȰ\nȰȳ\vȱȲeȲȴ\nȳȱȳȴȴȵȵȶ`ȶȷȸȸȹȺȺȻLȻȼ_ȼȽ`ȽȾȿ%ȿɀɁɁɂNɂɃ_ɃɄ`ɄɅɆ\t\vɆɇɈɈɉPɉɋ_ɊɌ\fɋɊɋɌɌɍɍɎ`Ɏɏɐ+ɐɑɒɒɓ¢Rɓɕ_ɔɖ\nɕɔɕɖɖɗɗɘ`ɘ¡əɚ,ɚ£#ÂÇ',g._serializedATNSegment1="æîõāĈđĞņşŭƎƚƠƨƲƶǁǅǏǛǟǫǵȁȋȕșȦȳɋɕ",g._serializedATN=O.join([g._serializedATNSegment0,g._serializedATNSegment1],"");class D extends C.ParserRuleContext{insert(){return this.tryGetRuleContext(0,ht)}update(){return this.tryGetRuleContext(0,ut)}select(){return this.tryGetRuleContext(0,At)}delete(){return this.tryGetRuleContext(0,dt)}stats(){return this.tryGetRuleContext(0,Nt)}distinct(){return this.tryGetRuleContext(0,Ut)}aggregate(){return this.tryGetRuleContext(0,Pt)}countDocuments(){return this.tryGetRuleContext(0,ft)}createIndex(){return this.tryGetRuleContext(0,_t)}drop(){return this.tryGetRuleContext(0,xt)}findOneAndDelete(){return this.tryGetRuleContext(0,Mt)}renameCollection(){return this.tryGetRuleContext(0,pt)}estimatedDocount(){return this.tryGetRuleContext(0,Ht)}count(){return this.tryGetRuleContext(0,Bt)}findOneAndUpdate(){return this.tryGetRuleContext(0,bt)}findOneAndReplace(){return this.tryGetRuleContext(0,Ft)}dbAggregate(){return this.tryGetRuleContext(0,k)}createCollection(){return this.tryGetRuleContext(0,j)}getCollection(){return this.tryGetRuleContext(0,z)}getName(){return this.tryGetRuleContext(0,Y)}dropDataBase(){return this.tryGetRuleContext(0,Z)}createView(){return this.tryGetRuleContext(0,J)}iscapped(){return this.tryGetRuleContext(0,Qt)}dataSize(){return this.tryGetRuleContext(0,Xt)}validate(){return this.tryGetRuleContext(0,Kt)}latencyStats(){return this.tryGetRuleContext(0,Wt)}use(){return this.tryGetRuleContext(0,et)}showDbs(){return this.tryGetRuleContext(0,it)}dbStats(){return this.tryGetRuleContext(0,st)}mongoBefore(){return this.tryGetRuleContext(0,S)}constructor(t,e){super(t,e)}get ruleIndex(){return g.RULE_execute}enterRule(t){t.enterExecute&&t.enterExecute(this)}exitRule(t){t.exitExecute&&t.exitExecute(this)}accept(t){return t.visitExecute?t.visitExecute(this):t.visitChildren(this)}}class S extends C.ParserRuleContext{databaseBefore(){return this.getRuleContext(0,N)}collection(){return this.getRuleContext(0,H)}biaoji(){return this.tryGetRuleContext(0,v)}constructor(t,e){super(t,e)}get ruleIndex(){return g.RULE_mongoBefore}enterRule(t){t.enterMongoBefore&&t.enterMongoBefore(this)}exitRule(t){t.exitMongoBefore&&t.exitMongoBefore(this)}accept(t){return t.visitMongoBefore?t.visitMongoBefore(this):t.visitChildren(this)}}class N extends C.ParserRuleContext{DB(){return this.getToken(g.DB,0)}biaoji(){return this.getRuleContext(0,v)}constructor(t,e){super(t,e)}get ruleIndex(){return g.RULE_databaseBefore}enterRule(t){t.enterDatabaseBefore&&t.enterDatabaseBefore(this)}exitRule(t){t.exitDatabaseBefore&&t.exitDatabaseBefore(this)}accept(t){return t.visitDatabaseBefore?t.visitDatabaseBefore(this):t.visitChildren(this)}}class v extends C.ParserRuleContext{DOT_(){return this.getToken(g.DOT_,0)}constructor(t,e){super(t,e)}get ruleIndex(){return g.RULE_biaoji}enterRule(t){t.enterBiaoji&&t.enterBiaoji(this)}exitRule(t){t.exitBiaoji&&t.exitBiaoji(this)}accept(t){return t.visitBiaoji?t.visitBiaoji(this):t.visitChildren(this)}}class U extends C.ParserRuleContext{jsonValue(){return this.getRuleContext(0,w)}constructor(t,e){super(t,e)}get ruleIndex(){return g.RULE_optional}enterRule(t){t.enterOptional&&t.enterOptional(this)}exitRule(t){t.exitOptional&&t.exitOptional(this)}accept(t){return t.visitOptional?t.visitOptional(this):t.visitChildren(this)}}class m extends C.ParserRuleContext{TRUE(){return this.tryGetToken(g.TRUE,0)}FLASE(){return this.tryGetToken(g.FLASE,0)}constructor(t,e){super(t,e)}get ruleIndex(){return g.RULE_booleanOptional}enterRule(t){t.enterBooleanOptional&&t.enterBooleanOptional(this)}exitRule(t){t.exitBooleanOptional&&t.exitBooleanOptional(this)}accept(t){return t.visitBooleanOptional?t.visitBooleanOptional(this):t.visitChildren(this)}}class P extends C.ParserRuleContext{STRING(){return this.getToken(g.STRING,0)}constructor(t,e){super(t,e)}get ruleIndex(){return g.RULE_simpleChar}enterRule(t){t.enterSimpleChar&&t.enterSimpleChar(this)}exitRule(t){t.exitSimpleChar&&t.exitSimpleChar(this)}accept(t){return t.visitSimpleChar?t.visitSimpleChar(this):t.visitChildren(this)}}class f extends C.ParserRuleContext{array(){return this.getRuleContext(0,b)}constructor(t,e){super(t,e)}get ruleIndex(){return g.RULE_simpleArray}enterRule(t){t.enterSimpleArray&&t.enterSimpleArray(this)}exitRule(t){t.exitSimpleArray&&t.exitSimpleArray(this)}accept(t){return t.visitSimpleArray?t.visitSimpleArray(this):t.visitChildren(this)}}class y extends C.ParserRuleContext{jsonValue(){return this.getRuleContext(0,w)}COMMA_(){return this.getToken(g.COMMA_,0)}constructor(t,e){super(t,e)}get ruleIndex(){return g.RULE_searchValue}enterRule(t){t.enterSearchValue&&t.enterSearchValue(this)}exitRule(t){t.exitSearchValue&&t.exitSearchValue(this)}accept(t){return t.visitSearchValue?t.visitSearchValue(this):t.visitChildren(this)}}class M extends C.ParserRuleContext{jsonValue(){return this.getRuleContext(0,w)}constructor(t,e){super(t,e)}get ruleIndex(){return g.RULE_updateValue}enterRule(t){t.enterUpdateValue&&t.enterUpdateValue(this)}exitRule(t){t.exitUpdateValue&&t.exitUpdateValue(this)}accept(t){return t.visitUpdateValue?t.visitUpdateValue(this):t.visitChildren(this)}}class G extends C.ParserRuleContext{AGGREGATE(){return this.getToken(g.AGGREGATE,0)}constructor(t,e){super(t,e)}get ruleIndex(){return g.RULE_aggregateOrder}enterRule(t){t.enterAggregateOrder&&t.enterAggregateOrder(this)}exitRule(t){t.exitAggregateOrder&&t.exitAggregateOrder(this)}accept(t){return t.visitAggregateOrder?t.visitAggregateOrder(this):t.visitChildren(this)}}class H extends C.ParserRuleContext{IDENTIFIER_(){return this.getToken(g.IDENTIFIER_,0)}constructor(t,e){super(t,e)}get ruleIndex(){return g.RULE_collection}enterRule(t){t.enterCollection&&t.enterCollection(this)}exitRule(t){t.exitCollection&&t.exitCollection(this)}accept(t){return t.visitCollection?t.visitCollection(this):t.visitChildren(this)}}class w extends C.ParserRuleContext{STRING(){return this.tryGetToken(g.STRING,0)}NUMBER(){return this.tryGetToken(g.NUMBER,0)}obj(){return this.tryGetRuleContext(0,B)}array(){return this.tryGetRuleContext(0,b)}TRUE(){return this.tryGetToken(g.TRUE,0)}FALSE(){return this.tryGetToken(g.FALSE,0)}NULL(){return this.tryGetToken(g.NULL,0)}constructor(t,e){super(t,e)}get ruleIndex(){return g.RULE_jsonValue}enterRule(t){t.enterJsonValue&&t.enterJsonValue(this)}exitRule(t){t.exitJsonValue&&t.exitJsonValue(this)}accept(t){return t.visitJsonValue?t.visitJsonValue(this):t.visitChildren(this)}}class B extends C.ParserRuleContext{LBE_(){return this.getToken(g.LBE_,0)}pair(t){return void 0===t?this.getRuleContexts(V):this.getRuleContext(t,V)}RBE_(){return this.getToken(g.RBE_,0)}COMMA_(t){return void 0===t?this.getTokens(g.COMMA_):this.getToken(g.COMMA_,t)}constructor(t,e){super(t,e)}get ruleIndex(){return g.RULE_obj}enterRule(t){t.enterObj&&t.enterObj(this)}exitRule(t){t.exitObj&&t.exitObj(this)}accept(t){return t.visitObj?t.visitObj(this):t.visitChildren(this)}}class V extends C.ParserRuleContext{STRING(){return this.getToken(g.STRING,0)}COLON_(){return this.getToken(g.COLON_,0)}jsonValue(){return this.getRuleContext(0,w)}constructor(t,e){super(t,e)}get ruleIndex(){return g.RULE_pair}enterRule(t){t.enterPair&&t.enterPair(this)}exitRule(t){t.exitPair&&t.exitPair(this)}accept(t){return t.visitPair?t.visitPair(this):t.visitChildren(this)}}class b extends C.ParserRuleContext{LBT_(){return this.getToken(g.LBT_,0)}jsonValue(t){return void 0===t?this.getRuleContexts(w):this.getRuleContext(t,w)}RBT_(){return this.getToken(g.RBT_,0)}COMMA_(t){return void 0===t?this.getTokens(g.COMMA_):this.getToken(g.COMMA_,t)}constructor(t,e){super(t,e)}get ruleIndex(){return g.RULE_array}enterRule(t){t.enterArray&&t.enterArray(this)}exitRule(t){t.exitArray&&t.exitArray(this)}accept(t){return t.visitArray?t.visitArray(this):t.visitChildren(this)}}class k extends C.ParserRuleContext{DB(){return this.getToken(g.DB,0)}DOT_(){return this.getToken(g.DOT_,0)}dbAggregateOrder(){return this.getRuleContext(0,F)}LP_(){return this.getToken(g.LP_,0)}jsonValue(){return this.getRuleContext(0,w)}RP_(){return this.getToken(g.RP_,0)}COMMA_(){return this.tryGetToken(g.COMMA_,0)}optional(){return this.tryGetRuleContext(0,U)}constructor(t,e){super(t,e)}get ruleIndex(){return g.RULE_dbAggregate}enterRule(t){t.enterDbAggregate&&t.enterDbAggregate(this)}exitRule(t){t.exitDbAggregate&&t.exitDbAggregate(this)}accept(t){return t.visitDbAggregate?t.visitDbAggregate(this):t.visitChildren(this)}}class F extends C.ParserRuleContext{AGGREGATE(){return this.getToken(g.AGGREGATE,0)}constructor(t,e){super(t,e)}get ruleIndex(){return g.RULE_dbAggregateOrder}enterRule(t){t.enterDbAggregateOrder&&t.enterDbAggregateOrder(this)}exitRule(t){t.exitDbAggregateOrder&&t.exitDbAggregateOrder(this)}accept(t){return t.visitDbAggregateOrder?t.visitDbAggregateOrder(this):t.visitChildren(this)}}class j extends C.ParserRuleContext{DB(){return this.getToken(g.DB,0)}DOT_(){return this.getToken(g.DOT_,0)}createCollectionOrder(){return this.getRuleContext(0,Q)}LP_(){return this.getToken(g.LP_,0)}simpleChar(){return this.getRuleContext(0,P)}RP_(){return this.getToken(g.RP_,0)}COMMA_(){return this.tryGetToken(g.COMMA_,0)}optional(){return this.tryGetRuleContext(0,U)}constructor(t,e){super(t,e)}get ruleIndex(){return g.RULE_createCollection}enterRule(t){t.enterCreateCollection&&t.enterCreateCollection(this)}exitRule(t){t.exitCreateCollection&&t.exitCreateCollection(this)}accept(t){return t.visitCreateCollection?t.visitCreateCollection(this):t.visitChildren(this)}}class Q extends C.ParserRuleContext{CREATECOLLECTION(){return this.getToken(g.CREATECOLLECTION,0)}constructor(t,e){super(t,e)}get ruleIndex(){return g.RULE_createCollectionOrder}enterRule(t){t.enterCreateCollectionOrder&&t.enterCreateCollectionOrder(this)}exitRule(t){t.exitCreateCollectionOrder&&t.exitCreateCollectionOrder(this)}accept(t){return t.visitCreateCollectionOrder?t.visitCreateCollectionOrder(this):t.visitChildren(this)}}class z extends C.ParserRuleContext{DB(){return this.getToken(g.DB,0)}DOT_(){return this.getToken(g.DOT_,0)}getCollectionOrder(){return this.getRuleContext(0,X)}LP_(){return this.getToken(g.LP_,0)}simpleChar(){return this.getRuleContext(0,P)}RP_(){return this.getToken(g.RP_,0)}constructor(t,e){super(t,e)}get ruleIndex(){return g.RULE_getCollection}enterRule(t){t.enterGetCollection&&t.enterGetCollection(this)}exitRule(t){t.exitGetCollection&&t.exitGetCollection(this)}accept(t){return t.visitGetCollection?t.visitGetCollection(this):t.visitChildren(this)}}class X extends C.ParserRuleContext{GETCOLLECTION(){return this.getToken(g.GETCOLLECTION,0)}constructor(t,e){super(t,e)}get ruleIndex(){return g.RULE_getCollectionOrder}enterRule(t){t.enterGetCollectionOrder&&t.enterGetCollectionOrder(this)}exitRule(t){t.exitGetCollectionOrder&&t.exitGetCollectionOrder(this)}accept(t){return t.visitGetCollectionOrder?t.visitGetCollectionOrder(this):t.visitChildren(this)}}class Y extends C.ParserRuleContext{DB(){return this.getToken(g.DB,0)}DOT_(){return this.getToken(g.DOT_,0)}nameOrder(){return this.getRuleContext(0,K)}LP_(){return this.getToken(g.LP_,0)}RP_(){return this.getToken(g.RP_,0)}constructor(t,e){super(t,e)}get ruleIndex(){return g.RULE_getName}enterRule(t){t.enterGetName&&t.enterGetName(this)}exitRule(t){t.exitGetName&&t.exitGetName(this)}accept(t){return t.visitGetName?t.visitGetName(this):t.visitChildren(this)}}class K extends C.ParserRuleContext{GETNAME(){return this.getToken(g.GETNAME,0)}constructor(t,e){super(t,e)}get ruleIndex(){return g.RULE_nameOrder}enterRule(t){t.enterNameOrder&&t.enterNameOrder(this)}exitRule(t){t.exitNameOrder&&t.exitNameOrder(this)}accept(t){return t.visitNameOrder?t.visitNameOrder(this):t.visitChildren(this)}}class Z extends C.ParserRuleContext{DB(){return this.getToken(g.DB,0)}DOT_(){return this.getToken(g.DOT_,0)}dropHelpOrder(){return this.getRuleContext(0,W)}LP_(){return this.getToken(g.LP_,0)}RP_(){return this.getToken(g.RP_,0)}constructor(t,e){super(t,e)}get ruleIndex(){return g.RULE_dropDataBase}enterRule(t){t.enterDropDataBase&&t.enterDropDataBase(this)}exitRule(t){t.exitDropDataBase&&t.exitDropDataBase(this)}accept(t){return t.visitDropDataBase?t.visitDropDataBase(this):t.visitChildren(this)}}class W extends C.ParserRuleContext{DROPDATABASE(){return this.tryGetToken(g.DROPDATABASE,0)}HELP(){return this.tryGetToken(g.HELP,0)}VERSION(){return this.tryGetToken(g.VERSION,0)}SERVERBUILDINFO(){return this.tryGetToken(g.SERVERBUILDINFO,0)}SERVERSTATUS(){return this.tryGetToken(g.SERVERSTATUS,0)}ISMASTER(){return this.tryGetToken(g.ISMASTER,0)}HOSTINFO(){return this.tryGetToken(g.HOSTINFO,0)}SERVERCMDLINEOPTS(){return this.tryGetToken(g.SERVERCMDLINEOPTS,0)}GETPROFILINGSTATUS(){return this.tryGetToken(g.GETPROFILINGSTATUS,0)}GETPROFILINGLEVEL(){return this.tryGetToken(g.GETPROFILINGLEVEL,0)}constructor(t,e){super(t,e)}get ruleIndex(){return g.RULE_dropHelpOrder}enterRule(t){t.enterDropHelpOrder&&t.enterDropHelpOrder(this)}exitRule(t){t.exitDropHelpOrder&&t.exitDropHelpOrder(this)}accept(t){return t.visitDropHelpOrder?t.visitDropHelpOrder(this):t.visitChildren(this)}}class J extends C.ParserRuleContext{DB(){return this.getToken(g.DB,0)}DOT_(){return this.getToken(g.DOT_,0)}createViewOrder(){return this.getRuleContext(0,$)}LP_(){return this.getToken(g.LP_,0)}viewChar(){return this.getRuleContext(0,q)}sourceChar(){return this.getRuleContext(0,tt)}jsonValue(){return this.getRuleContext(0,w)}RP_(){return this.getToken(g.RP_,0)}COMMA_(){return this.tryGetToken(g.COMMA_,0)}optional(){return this.tryGetRuleContext(0,U)}constructor(t,e){super(t,e)}get ruleIndex(){return g.RULE_createView}enterRule(t){t.enterCreateView&&t.enterCreateView(this)}exitRule(t){t.exitCreateView&&t.exitCreateView(this)}accept(t){return t.visitCreateView?t.visitCreateView(this):t.visitChildren(this)}}class $ extends C.ParserRuleContext{CREATEVIEW(){return this.getToken(g.CREATEVIEW,0)}constructor(t,e){super(t,e)}get ruleIndex(){return g.RULE_createViewOrder}enterRule(t){t.enterCreateViewOrder&&t.enterCreateViewOrder(this)}exitRule(t){t.exitCreateViewOrder&&t.exitCreateViewOrder(this)}accept(t){return t.visitCreateViewOrder?t.visitCreateViewOrder(this):t.visitChildren(this)}}class q extends C.ParserRuleContext{simpleChar(){return this.getRuleContext(0,P)}COMMA_(){return this.getToken(g.COMMA_,0)}constructor(t,e){super(t,e)}get ruleIndex(){return g.RULE_viewChar}enterRule(t){t.enterViewChar&&t.enterViewChar(this)}exitRule(t){t.exitViewChar&&t.exitViewChar(this)}accept(t){return t.visitViewChar?t.visitViewChar(this):t.visitChildren(this)}}class tt extends C.ParserRuleContext{simpleChar(){return this.getRuleContext(0,P)}COMMA_(){return this.getToken(g.COMMA_,0)}constructor(t,e){super(t,e)}get ruleIndex(){return g.RULE_sourceChar}enterRule(t){t.enterSourceChar&&t.enterSourceChar(this)}exitRule(t){t.exitSourceChar&&t.exitSourceChar(this)}accept(t){return t.visitSourceChar?t.visitSourceChar(this):t.visitChildren(this)}}class et extends C.ParserRuleContext{useOrder(){return this.getRuleContext(0,rt)}simpleChar(){return this.getRuleContext(0,P)}constructor(t,e){super(t,e)}get ruleIndex(){return g.RULE_use}enterRule(t){t.enterUse&&t.enterUse(this)}exitRule(t){t.exitUse&&t.exitUse(this)}accept(t){return t.visitUse?t.visitUse(this):t.visitChildren(this)}}class rt extends C.ParserRuleContext{USE(){return this.getToken(g.USE,0)}constructor(t,e){super(t,e)}get ruleIndex(){return g.RULE_useOrder}enterRule(t){t.enterUseOrder&&t.enterUseOrder(this)}exitRule(t){t.exitUseOrder&&t.exitUseOrder(this)}accept(t){return t.visitUseOrder?t.visitUseOrder(this):t.visitChildren(this)}}class it extends C.ParserRuleContext{SHOW(){return this.getToken(g.SHOW,0)}DBS(){return this.getToken(g.DBS,0)}constructor(t,e){super(t,e)}get ruleIndex(){return g.RULE_showDbs}enterRule(t){t.enterShowDbs&&t.enterShowDbs(this)}exitRule(t){t.exitShowDbs&&t.exitShowDbs(this)}accept(t){return t.visitShowDbs?t.visitShowDbs(this):t.visitChildren(this)}}class st extends C.ParserRuleContext{DB(){return this.getToken(g.DB,0)}DOT_(){return this.getToken(g.DOT_,0)}dbStatsOrder(){return this.getRuleContext(0,nt)}LP_(){return this.getToken(g.LP_,0)}RP_(){return this.getToken(g.RP_,0)}optional(){return this.tryGetRuleContext(0,U)}constructor(t,e){super(t,e)}get ruleIndex(){return g.RULE_dbStats}enterRule(t){t.enterDbStats&&t.enterDbStats(this)}exitRule(t){t.exitDbStats&&t.exitDbStats(this)}accept(t){return t.visitDbStats?t.visitDbStats(this):t.visitChildren(this)}}class nt extends C.ParserRuleContext{STATS(){return this.getToken(g.STATS,0)}constructor(t,e){super(t,e)}get ruleIndex(){return g.RULE_dbStatsOrder}enterRule(t){t.enterDbStatsOrder&&t.enterDbStatsOrder(this)}exitRule(t){t.exitDbStatsOrder&&t.exitDbStatsOrder(this)}accept(t){return t.visitDbStatsOrder?t.visitDbStatsOrder(this):t.visitChildren(this)}}class ht extends C.ParserRuleContext{mongoBefore(){return this.getRuleContext(0,S)}insertOrder(){return this.getRuleContext(0,at)}constructor(t,e){super(t,e)}get ruleIndex(){return g.RULE_insert}enterRule(t){t.enterInsert&&t.enterInsert(this)}exitRule(t){t.exitInsert&&t.exitInsert(this)}accept(t){return t.visitInsert?t.visitInsert(this):t.visitChildren(this)}}class at extends C.ParserRuleContext{LP_(){return this.getToken(g.LP_,0)}insertValues(){return this.getRuleContext(0,ot)}RP_(){return this.getToken(g.RP_,0)}INSERT(){return this.tryGetToken(g.INSERT,0)}INSERTONE(){return this.tryGetToken(g.INSERTONE,0)}INSERTMANY(){return this.tryGetToken(g.INSERTMANY,0)}COMMA_(){return this.tryGetToken(g.COMMA_,0)}insertOptional(){return this.tryGetRuleContext(0,lt)}constructor(t,e){super(t,e)}get ruleIndex(){return g.RULE_insertOrder}enterRule(t){t.enterInsertOrder&&t.enterInsertOrder(this)}exitRule(t){t.exitInsertOrder&&t.exitInsertOrder(this)}accept(t){return t.visitInsertOrder?t.visitInsertOrder(this):t.visitChildren(this)}}class ot extends C.ParserRuleContext{jsonValue(){return this.getRuleContext(0,w)}constructor(t,e){super(t,e)}get ruleIndex(){return g.RULE_insertValues}enterRule(t){t.enterInsertValues&&t.enterInsertValues(this)}exitRule(t){t.exitInsertValues&&t.exitInsertValues(this)}accept(t){return t.visitInsertValues?t.visitInsertValues(this):t.visitChildren(this)}}class lt extends C.ParserRuleContext{optional(){return this.getRuleContext(0,U)}constructor(t,e){super(t,e)}get ruleIndex(){return g.RULE_insertOptional}enterRule(t){t.enterInsertOptional&&t.enterInsertOptional(this)}exitRule(t){t.exitInsertOptional&&t.exitInsertOptional(this)}accept(t){return t.visitInsertOptional?t.visitInsertOptional(this):t.visitChildren(this)}}class ut extends C.ParserRuleContext{mongoBefore(){return this.getRuleContext(0,S)}updateOrder(){return this.getRuleContext(0,ct)}LP_(){return this.getToken(g.LP_,0)}searchValue(){return this.getRuleContext(0,y)}updateValue(){return this.getRuleContext(0,M)}RP_(){return this.getToken(g.RP_,0)}constructor(t,e){super(t,e)}get ruleIndex(){return g.RULE_update}enterRule(t){t.enterUpdate&&t.enterUpdate(this)}exitRule(t){t.exitUpdate&&t.exitUpdate(this)}accept(t){return t.visitUpdate?t.visitUpdate(this):t.visitChildren(this)}}class ct extends C.ParserRuleContext{UPDATE(){return this.tryGetToken(g.UPDATE,0)}UPDATEONE(){return this.tryGetToken(g.UPDATEONE,0)}UPDATEMANY(){return this.tryGetToken(g.UPDATEMANY,0)}REPLACEONE(){return this.tryGetToken(g.REPLACEONE,0)}constructor(t,e){super(t,e)}get ruleIndex(){return g.RULE_updateOrder}enterRule(t){t.enterUpdateOrder&&t.enterUpdateOrder(this)}exitRule(t){t.exitUpdateOrder&&t.exitUpdateOrder(this)}accept(t){return t.visitUpdateOrder?t.visitUpdateOrder(this):t.visitChildren(this)}}class dt extends C.ParserRuleContext{mongoBefore(){return this.getRuleContext(0,S)}deleteOrder(){return this.getRuleContext(0,Et)}LP_(){return this.getToken(g.LP_,0)}deleteValue(){return this.getRuleContext(0,Rt)}RP_(){return this.getToken(g.RP_,0)}constructor(t,e){super(t,e)}get ruleIndex(){return g.RULE_delete}enterRule(t){t.enterDelete&&t.enterDelete(this)}exitRule(t){t.exitDelete&&t.exitDelete(this)}accept(t){return t.visitDelete?t.visitDelete(this):t.visitChildren(this)}}class Et extends C.ParserRuleContext{DELETEONE(){return this.tryGetToken(g.DELETEONE,0)}DELETEMANY(){return this.tryGetToken(g.DELETEMANY,0)}REMOVE(){return this.tryGetToken(g.REMOVE,0)}constructor(t,e){super(t,e)}get ruleIndex(){return g.RULE_deleteOrder}enterRule(t){t.enterDeleteOrder&&t.enterDeleteOrder(this)}exitRule(t){t.exitDeleteOrder&&t.exitDeleteOrder(this)}accept(t){return t.visitDeleteOrder?t.visitDeleteOrder(this):t.visitChildren(this)}}class Rt extends C.ParserRuleContext{jsonValue(){return this.getRuleContext(0,w)}constructor(t,e){super(t,e)}get ruleIndex(){return g.RULE_deleteValue}enterRule(t){t.enterDeleteValue&&t.enterDeleteValue(this)}exitRule(t){t.exitDeleteValue&&t.exitDeleteValue(this)}accept(t){return t.visitDeleteValue?t.visitDeleteValue(this):t.visitChildren(this)}}class _t extends C.ParserRuleContext{mongoBefore(){return this.getRuleContext(0,S)}createIndexOrder(){return this.getRuleContext(0,Ot)}LP_(){return this.getToken(g.LP_,0)}jsonValue(){return this.getRuleContext(0,w)}RP_(){return this.getToken(g.RP_,0)}COMMA_(){return this.tryGetToken(g.COMMA_,0)}optional(){return this.tryGetRuleContext(0,U)}constructor(t,e){super(t,e)}get ruleIndex(){return g.RULE_createIndex}enterRule(t){t.enterCreateIndex&&t.enterCreateIndex(this)}exitRule(t){t.exitCreateIndex&&t.exitCreateIndex(this)}accept(t){return t.visitCreateIndex?t.visitCreateIndex(this):t.visitChildren(this)}}class Ot extends C.ParserRuleContext{CREATEINDEX(){return this.tryGetToken(g.CREATEINDEX,0)}CREATEINDEXES(){return this.tryGetToken(g.CREATEINDEXES,0)}constructor(t,e){super(t,e)}get ruleIndex(){return g.RULE_createIndexOrder}enterRule(t){t.enterCreateIndexOrder&&t.enterCreateIndexOrder(this)}exitRule(t){t.exitCreateIndexOrder&&t.exitCreateIndexOrder(this)}accept(t){return t.visitCreateIndexOrder?t.visitCreateIndexOrder(this):t.visitChildren(this)}}class xt extends C.ParserRuleContext{mongoBefore(){return this.getRuleContext(0,S)}dropOrder(){return this.getRuleContext(0,Tt)}LP_(){return this.getToken(g.LP_,0)}RP_(){return this.getToken(g.RP_,0)}simpleChar(){return this.tryGetRuleContext(0,P)}simpleArray(){return this.tryGetRuleContext(0,f)}jsonValue(){return this.tryGetRuleContext(0,w)}constructor(t,e){super(t,e)}get ruleIndex(){return g.RULE_drop}enterRule(t){t.enterDrop&&t.enterDrop(this)}exitRule(t){t.exitDrop&&t.exitDrop(this)}accept(t){return t.visitDrop?t.visitDrop(this):t.visitChildren(this)}}class Tt extends C.ParserRuleContext{DROP(){return this.tryGetToken(g.DROP,0)}DROPINDEX(){return this.tryGetToken(g.DROPINDEX,0)}DROPINDEXES(){return this.tryGetToken(g.DROPINDEXES,0)}constructor(t,e){super(t,e)}get ruleIndex(){return g.RULE_dropOrder}enterRule(t){t.enterDropOrder&&t.enterDropOrder(this)}exitRule(t){t.exitDropOrder&&t.exitDropOrder(this)}accept(t){return t.visitDropOrder?t.visitDropOrder(this):t.visitChildren(this)}}class pt extends C.ParserRuleContext{mongoBefore(){return this.getRuleContext(0,S)}renameOrder(){return this.getRuleContext(0,Ct)}LP_(){return this.getToken(g.LP_,0)}simpleChar(){return this.getRuleContext(0,P)}RP_(){return this.getToken(g.RP_,0)}COMMA_(){return this.tryGetToken(g.COMMA_,0)}booleanOptional(){return this.tryGetRuleContext(0,m)}constructor(t,e){super(t,e)}get ruleIndex(){return g.RULE_renameCollection}enterRule(t){t.enterRenameCollection&&t.enterRenameCollection(this)}exitRule(t){t.exitRenameCollection&&t.exitRenameCollection(this)}accept(t){return t.visitRenameCollection?t.visitRenameCollection(this):t.visitChildren(this)}}class Ct extends C.ParserRuleContext{RENAMECOLLECTION(){return this.getToken(g.RENAMECOLLECTION,0)}constructor(t,e){super(t,e)}get ruleIndex(){return g.RULE_renameOrder}enterRule(t){t.enterRenameOrder&&t.enterRenameOrder(this)}exitRule(t){t.exitRenameOrder&&t.exitRenameOrder(this)}accept(t){return t.visitRenameOrder?t.visitRenameOrder(this):t.visitChildren(this)}}class At extends C.ParserRuleContext{mongoBefore(){return this.getRuleContext(0,S)}selectOrder(){return this.getRuleContext(0,Lt)}LP_(){return this.getToken(g.LP_,0)}RP_(){return this.getToken(g.RP_,0)}queryValue(){return this.tryGetRuleContext(0,It)}conditionOrder(){return this.tryGetRuleContext(0,gt)}constructor(t,e){super(t,e)}get ruleIndex(){return g.RULE_select}enterRule(t){t.enterSelect&&t.enterSelect(this)}exitRule(t){t.exitSelect&&t.exitSelect(this)}accept(t){return t.visitSelect?t.visitSelect(this):t.visitChildren(this)}}class Lt extends C.ParserRuleContext{FIND(){return this.tryGetToken(g.FIND,0)}FINDONE(){return this.tryGetToken(g.FINDONE,0)}constructor(t,e){super(t,e)}get ruleIndex(){return g.RULE_selectOrder}enterRule(t){t.enterSelectOrder&&t.enterSelectOrder(this)}exitRule(t){t.exitSelectOrder&&t.exitSelectOrder(this)}accept(t){return t.visitSelectOrder?t.visitSelectOrder(this):t.visitChildren(this)}}class It extends C.ParserRuleContext{jsonValue(){return this.getRuleContext(0,w)}constructor(t,e){super(t,e)}get ruleIndex(){return g.RULE_queryValue}enterRule(t){t.enterQueryValue&&t.enterQueryValue(this)}exitRule(t){t.exitQueryValue&&t.exitQueryValue(this)}accept(t){return t.visitQueryValue?t.visitQueryValue(this):t.visitChildren(this)}}class gt extends C.ParserRuleContext{DOT_(){return this.getToken(g.DOT_,0)}findOption(){return this.getRuleContext(0,Dt)}LP_(){return this.getToken(g.LP_,0)}RP_(){return this.getToken(g.RP_,0)}numberOrder(){return this.tryGetRuleContext(0,St)}jsonValue(){return this.tryGetRuleContext(0,w)}conditionOrder(){return this.tryGetRuleContext(0,gt)}constructor(t,e){super(t,e)}get ruleIndex(){return g.RULE_conditionOrder}enterRule(t){t.enterConditionOrder&&t.enterConditionOrder(this)}exitRule(t){t.exitConditionOrder&&t.exitConditionOrder(this)}accept(t){return t.visitConditionOrder?t.visitConditionOrder(this):t.visitChildren(this)}}class Dt extends C.ParserRuleContext{SORT(){return this.tryGetToken(g.SORT,0)}LIMIT(){return this.tryGetToken(g.LIMIT,0)}SKIPINFO(){return this.tryGetToken(g.SKIPINFO,0)}constructor(t,e){super(t,e)}get ruleIndex(){return g.RULE_findOption}enterRule(t){t.enterFindOption&&t.enterFindOption(this)}exitRule(t){t.exitFindOption&&t.exitFindOption(this)}accept(t){return t.visitFindOption?t.visitFindOption(this):t.visitChildren(this)}}class St extends C.ParserRuleContext{NUMBER(){return this.getToken(g.NUMBER,0)}constructor(t,e){super(t,e)}get ruleIndex(){return g.RULE_numberOrder}enterRule(t){t.enterNumberOrder&&t.enterNumberOrder(this)}exitRule(t){t.exitNumberOrder&&t.exitNumberOrder(this)}accept(t){return t.visitNumberOrder?t.visitNumberOrder(this):t.visitChildren(this)}}class Nt extends C.ParserRuleContext{mongoBefore(){return this.getRuleContext(0,S)}statsOrder(){return this.getRuleContext(0,vt)}LP_(){return this.getToken(g.LP_,0)}RP_(){return this.getToken(g.RP_,0)}optional(){return this.tryGetRuleContext(0,U)}constructor(t,e){super(t,e)}get ruleIndex(){return g.RULE_stats}enterRule(t){t.enterStats&&t.enterStats(this)}exitRule(t){t.exitStats&&t.exitStats(this)}accept(t){return t.visitStats?t.visitStats(this):t.visitChildren(this)}}class vt extends C.ParserRuleContext{STATS(){return this.getToken(g.STATS,0)}constructor(t,e){super(t,e)}get ruleIndex(){return g.RULE_statsOrder}enterRule(t){t.enterStatsOrder&&t.enterStatsOrder(this)}exitRule(t){t.exitStatsOrder&&t.exitStatsOrder(this)}accept(t){return t.visitStatsOrder?t.visitStatsOrder(this):t.visitChildren(this)}}class Ut extends C.ParserRuleContext{mongoBefore(){return this.getRuleContext(0,S)}distinctOrder(){return this.getRuleContext(0,mt)}LP_(){return this.getToken(g.LP_,0)}simpleChar(){return this.getRuleContext(0,P)}RP_(){return this.getToken(g.RP_,0)}COMMA_(t){return void 0===t?this.getTokens(g.COMMA_):this.getToken(g.COMMA_,t)}jsonValue(){return this.tryGetRuleContext(0,w)}optional(){return this.tryGetRuleContext(0,U)}constructor(t,e){super(t,e)}get ruleIndex(){return g.RULE_distinct}enterRule(t){t.enterDistinct&&t.enterDistinct(this)}exitRule(t){t.exitDistinct&&t.exitDistinct(this)}accept(t){return t.visitDistinct?t.visitDistinct(this):t.visitChildren(this)}}class mt extends C.ParserRuleContext{DISTINCT(){return this.getToken(g.DISTINCT,0)}constructor(t,e){super(t,e)}get ruleIndex(){return g.RULE_distinctOrder}enterRule(t){t.enterDistinctOrder&&t.enterDistinctOrder(this)}exitRule(t){t.exitDistinctOrder&&t.exitDistinctOrder(this)}accept(t){return t.visitDistinctOrder?t.visitDistinctOrder(this):t.visitChildren(this)}}class Pt extends C.ParserRuleContext{mongoBefore(){return this.getRuleContext(0,S)}aggregateOrder(){return this.getRuleContext(0,G)}LP_(){return this.getToken(g.LP_,0)}jsonValue(){return this.getRuleContext(0,w)}RP_(){return this.getToken(g.RP_,0)}COMMA_(){return this.tryGetToken(g.COMMA_,0)}optional(){return this.tryGetRuleContext(0,U)}constructor(t,e){super(t,e)}get ruleIndex(){return g.RULE_aggregate}enterRule(t){t.enterAggregate&&t.enterAggregate(this)}exitRule(t){t.exitAggregate&&t.exitAggregate(this)}accept(t){return t.visitAggregate?t.visitAggregate(this):t.visitChildren(this)}}class ft extends C.ParserRuleContext{mongoBefore(){return this.getRuleContext(0,S)}countDocumentsOrder(){return this.getRuleContext(0,yt)}LP_(){return this.getToken(g.LP_,0)}jsonValue(){return this.getRuleContext(0,w)}RP_(){return this.getToken(g.RP_,0)}COMMA_(){return this.tryGetToken(g.COMMA_,0)}optional(){return this.tryGetRuleContext(0,U)}constructor(t,e){super(t,e)}get ruleIndex(){return g.RULE_countDocuments}enterRule(t){t.enterCountDocuments&&t.enterCountDocuments(this)}exitRule(t){t.exitCountDocuments&&t.exitCountDocuments(this)}accept(t){return t.visitCountDocuments?t.visitCountDocuments(this):t.visitChildren(this)}}class yt extends C.ParserRuleContext{COUNTDOCUMENTS(){return this.getToken(g.COUNTDOCUMENTS,0)}constructor(t,e){super(t,e)}get ruleIndex(){return g.RULE_countDocumentsOrder}enterRule(t){t.enterCountDocumentsOrder&&t.enterCountDocumentsOrder(this)}exitRule(t){t.exitCountDocumentsOrder&&t.exitCountDocumentsOrder(this)}accept(t){return t.visitCountDocumentsOrder?t.visitCountDocumentsOrder(this):t.visitChildren(this)}}class Mt extends C.ParserRuleContext{mongoBefore(){return this.getRuleContext(0,S)}findOneDeleteOrder(){return this.getRuleContext(0,Gt)}LP_(){return this.getToken(g.LP_,0)}jsonValue(){return this.getRuleContext(0,w)}RP_(){return this.getToken(g.RP_,0)}COMMA_(){return this.tryGetToken(g.COMMA_,0)}optional(){return this.tryGetRuleContext(0,U)}constructor(t,e){super(t,e)}get ruleIndex(){return g.RULE_findOneAndDelete}enterRule(t){t.enterFindOneAndDelete&&t.enterFindOneAndDelete(this)}exitRule(t){t.exitFindOneAndDelete&&t.exitFindOneAndDelete(this)}accept(t){return t.visitFindOneAndDelete?t.visitFindOneAndDelete(this):t.visitChildren(this)}}class Gt extends C.ParserRuleContext{FINDONEANDDELETE(){return this.getToken(g.FINDONEANDDELETE,0)}constructor(t,e){super(t,e)}get ruleIndex(){return g.RULE_findOneDeleteOrder}enterRule(t){t.enterFindOneDeleteOrder&&t.enterFindOneDeleteOrder(this)}exitRule(t){t.exitFindOneDeleteOrder&&t.exitFindOneDeleteOrder(this)}accept(t){return t.visitFindOneDeleteOrder?t.visitFindOneDeleteOrder(this):t.visitChildren(this)}}class Ht extends C.ParserRuleContext{mongoBefore(){return this.getRuleContext(0,S)}estimatedOrder(){return this.getRuleContext(0,wt)}LP_(){return this.getToken(g.LP_,0)}RP_(){return this.getToken(g.RP_,0)}jsonValue(){return this.tryGetRuleContext(0,w)}constructor(t,e){super(t,e)}get ruleIndex(){return g.RULE_estimatedDocount}enterRule(t){t.enterEstimatedDocount&&t.enterEstimatedDocount(this)}exitRule(t){t.exitEstimatedDocount&&t.exitEstimatedDocount(this)}accept(t){return t.visitEstimatedDocount?t.visitEstimatedDocount(this):t.visitChildren(this)}}class wt extends C.ParserRuleContext{ESTIMATEDDOCUMENTCOUNT(){return this.getToken(g.ESTIMATEDDOCUMENTCOUNT,0)}constructor(t,e){super(t,e)}get ruleIndex(){return g.RULE_estimatedOrder}enterRule(t){t.enterEstimatedOrder&&t.enterEstimatedOrder(this)}exitRule(t){t.exitEstimatedOrder&&t.exitEstimatedOrder(this)}accept(t){return t.visitEstimatedOrder?t.visitEstimatedOrder(this):t.visitChildren(this)}}class Bt extends C.ParserRuleContext{mongoBefore(){return this.getRuleContext(0,S)}countOrder(){return this.getRuleContext(0,Vt)}LP_(){return this.getToken(g.LP_,0)}RP_(){return this.getToken(g.RP_,0)}jsonValue(){return this.tryGetRuleContext(0,w)}COMMA_(){return this.tryGetToken(g.COMMA_,0)}optional(){return this.tryGetRuleContext(0,U)}constructor(t,e){super(t,e)}get ruleIndex(){return g.RULE_count}enterRule(t){t.enterCount&&t.enterCount(this)}exitRule(t){t.exitCount&&t.exitCount(this)}accept(t){return t.visitCount?t.visitCount(this):t.visitChildren(this)}}class Vt extends C.ParserRuleContext{COUNT(){return this.getToken(g.COUNT,0)}constructor(t,e){super(t,e)}get ruleIndex(){return g.RULE_countOrder}enterRule(t){t.enterCountOrder&&t.enterCountOrder(this)}exitRule(t){t.exitCountOrder&&t.exitCountOrder(this)}accept(t){return t.visitCountOrder?t.visitCountOrder(this):t.visitChildren(this)}}class bt extends C.ParserRuleContext{mongoBefore(){return this.getRuleContext(0,S)}findOneUpdateOrder(){return this.getRuleContext(0,kt)}LP_(){return this.getToken(g.LP_,0)}searchValue(){return this.getRuleContext(0,y)}updateValue(){return this.getRuleContext(0,M)}RP_(){return this.getToken(g.RP_,0)}COMMA_(){return this.tryGetToken(g.COMMA_,0)}optional(){return this.tryGetRuleContext(0,U)}constructor(t,e){super(t,e)}get ruleIndex(){return g.RULE_findOneAndUpdate}enterRule(t){t.enterFindOneAndUpdate&&t.enterFindOneAndUpdate(this)}exitRule(t){t.exitFindOneAndUpdate&&t.exitFindOneAndUpdate(this)}accept(t){return t.visitFindOneAndUpdate?t.visitFindOneAndUpdate(this):t.visitChildren(this)}}class kt extends C.ParserRuleContext{FINDONEANDUPDATE(){return this.getToken(g.FINDONEANDUPDATE,0)}constructor(t,e){super(t,e)}get ruleIndex(){return g.RULE_findOneUpdateOrder}enterRule(t){t.enterFindOneUpdateOrder&&t.enterFindOneUpdateOrder(this)}exitRule(t){t.exitFindOneUpdateOrder&&t.exitFindOneUpdateOrder(this)}accept(t){return t.visitFindOneUpdateOrder?t.visitFindOneUpdateOrder(this):t.visitChildren(this)}}class Ft extends C.ParserRuleContext{mongoBefore(){return this.getRuleContext(0,S)}findOneReplaceOrder(){return this.getRuleContext(0,jt)}LP_(){return this.getToken(g.LP_,0)}searchValue(){return this.getRuleContext(0,y)}updateValue(){return this.getRuleContext(0,M)}RP_(){return this.getToken(g.RP_,0)}COMMA_(){return this.tryGetToken(g.COMMA_,0)}optional(){return this.tryGetRuleContext(0,U)}constructor(t,e){super(t,e)}get ruleIndex(){return g.RULE_findOneAndReplace}enterRule(t){t.enterFindOneAndReplace&&t.enterFindOneAndReplace(this)}exitRule(t){t.exitFindOneAndReplace&&t.exitFindOneAndReplace(this)}accept(t){return t.visitFindOneAndReplace?t.visitFindOneAndReplace(this):t.visitChildren(this)}}class jt extends C.ParserRuleContext{FINDONEANDREPLACE(){return this.getToken(g.FINDONEANDREPLACE,0)}constructor(t,e){super(t,e)}get ruleIndex(){return g.RULE_findOneReplaceOrder}enterRule(t){t.enterFindOneReplaceOrder&&t.enterFindOneReplaceOrder(this)}exitRule(t){t.exitFindOneReplaceOrder&&t.exitFindOneReplaceOrder(this)}accept(t){return t.visitFindOneReplaceOrder?t.visitFindOneReplaceOrder(this):t.visitChildren(this)}}class Qt extends C.ParserRuleContext{mongoBefore(){return this.getRuleContext(0,S)}isCappedOrder(){return this.getRuleContext(0,zt)}LP_(){return this.getToken(g.LP_,0)}RP_(){return this.getToken(g.RP_,0)}constructor(t,e){super(t,e)}get ruleIndex(){return g.RULE_iscapped}enterRule(t){t.enterIscapped&&t.enterIscapped(this)}exitRule(t){t.exitIscapped&&t.exitIscapped(this)}accept(t){return t.visitIscapped?t.visitIscapped(this):t.visitChildren(this)}}class zt extends C.ParserRuleContext{ISCAPPED(){return this.getToken(g.ISCAPPED,0)}constructor(t,e){super(t,e)}get ruleIndex(){return g.RULE_isCappedOrder}enterRule(t){t.enterIsCappedOrder&&t.enterIsCappedOrder(this)}exitRule(t){t.exitIsCappedOrder&&t.exitIsCappedOrder(this)}accept(t){return t.visitIsCappedOrder?t.visitIsCappedOrder(this):t.visitChildren(this)}}class Xt extends C.ParserRuleContext{mongoBefore(){return this.getRuleContext(0,S)}sizeOrder(){return this.getRuleContext(0,Yt)}LP_(){return this.getToken(g.LP_,0)}RP_(){return this.getToken(g.RP_,0)}constructor(t,e){super(t,e)}get ruleIndex(){return g.RULE_dataSize}enterRule(t){t.enterDataSize&&t.enterDataSize(this)}exitRule(t){t.exitDataSize&&t.exitDataSize(this)}accept(t){return t.visitDataSize?t.visitDataSize(this):t.visitChildren(this)}}class Yt extends C.ParserRuleContext{DATASIZE(){return this.tryGetToken(g.DATASIZE,0)}STORAGESIZE(){return this.tryGetToken(g.STORAGESIZE,0)}TOTALINDEXSIZE(){return this.tryGetToken(g.TOTALINDEXSIZE,0)}TOTALSIZE(){return this.tryGetToken(g.TOTALSIZE,0)}GETINDEXES(){return this.tryGetToken(g.GETINDEXES,0)}HELP(){return this.tryGetToken(g.HELP,0)}constructor(t,e){super(t,e)}get ruleIndex(){return g.RULE_sizeOrder}enterRule(t){t.enterSizeOrder&&t.enterSizeOrder(this)}exitRule(t){t.exitSizeOrder&&t.exitSizeOrder(this)}accept(t){return t.visitSizeOrder?t.visitSizeOrder(this):t.visitChildren(this)}}class Kt extends C.ParserRuleContext{mongoBefore(){return this.getRuleContext(0,S)}validateOrder(){return this.getRuleContext(0,Zt)}LP_(){return this.getToken(g.LP_,0)}RP_(){return this.getToken(g.RP_,0)}booleanOptional(){return this.tryGetRuleContext(0,m)}constructor(t,e){super(t,e)}get ruleIndex(){return g.RULE_validate}enterRule(t){t.enterValidate&&t.enterValidate(this)}exitRule(t){t.exitValidate&&t.exitValidate(this)}accept(t){return t.visitValidate?t.visitValidate(this):t.visitChildren(this)}}class Zt extends C.ParserRuleContext{VALIDATE(){return this.getToken(g.VALIDATE,0)}constructor(t,e){super(t,e)}get ruleIndex(){return g.RULE_validateOrder}enterRule(t){t.enterValidateOrder&&t.enterValidateOrder(this)}exitRule(t){t.exitValidateOrder&&t.exitValidateOrder(this)}accept(t){return t.visitValidateOrder?t.visitValidateOrder(this):t.visitChildren(this)}}class Wt extends C.ParserRuleContext{mongoBefore(){return this.getRuleContext(0,S)}latencyStatsOrder(){return this.getRuleContext(0,Jt)}LP_(){return this.getToken(g.LP_,0)}RP_(){return this.getToken(g.RP_,0)}optional(){return this.tryGetRuleContext(0,U)}constructor(t,e){super(t,e)}get ruleIndex(){return g.RULE_latencyStats}enterRule(t){t.enterLatencyStats&&t.enterLatencyStats(this)}exitRule(t){t.exitLatencyStats&&t.exitLatencyStats(this)}accept(t){return t.visitLatencyStats?t.visitLatencyStats(this):t.visitChildren(this)}}class Jt extends C.ParserRuleContext{LATENCYSTATS(){return this.getToken(g.LATENCYSTATS,0)}constructor(t,e){super(t,e)}get ruleIndex(){return g.RULE_latencyStatsOrder}enterRule(t){t.enterLatencyStatsOrder&&t.enterLatencyStatsOrder(this)}exitRule(t){t.exitLatencyStatsOrder&&t.exitLatencyStatsOrder(this)}accept(t){return t.visitLatencyStatsOrder?t.visitLatencyStatsOrder(this):t.visitChildren(this)}}class $t{constructor(t){this.statement=t}getKeyWord(){return a.keywords}isInputValue(t){return!1}parserSQL(){let t=i.CharStreams.fromString(this.statement),e=new x(t),r=new i.CommonTokenStream(e),a=new g(r);a.removeErrorListeners();const o=new n.c;a.addErrorListener(o);let l=a.execute(),u=new c;return a.notifyErrorListeners("Mongo 解析异常，指定索引位后为异常错误位"),h.ParseTreeWalker.DEFAULT.walk(u,l),new s.$(o.errorMark,r,u.getSymbolTable())}}var qt=r(8130),te=r(2382);function ee(){let t=new qt.n,e=new $t(this.statement);const r=e.parserSQL();return(0,te.yZ)(t,r,e.getKeyWord(),this)}},4037:(t,e,r)=>{"use strict";r.d(e,{o:()=>i});class i{}},6862:(t,e,r)=>{"use strict";r.d(e,{d:()=>s});class i{constructor(){this._items=[]}push(t){this._items.push(t)}pop(){return this._items.pop()}peek(){return this._items[this._items.length-1]}clear(){this._items=[]}get items(){return this._items}}class s{constructor(){this.symbolTableStructure=new i}insertElement(t){this.symbolTableStructure.push(t)}cleanElement(){this.symbolTableStructure.clear()}getPeekElement(){return this.symbolTableStructure.peek()}getAllElement(){return this.symbolTableStructure.items}}},5737:(t,e,r)=>{"use strict";r.d(e,{$:()=>i});class i{constructor(t,e,r){this._parserErrorResult=t,this._commonTokenStream=e,this._symbolTable=r}get parserErrorResult(){return this._parserErrorResult}get commonTokenStream(){return this._commonTokenStream}get symbolTable(){return this._symbolTable}}}}]);
//# sourceMappingURL=34.index.js.map