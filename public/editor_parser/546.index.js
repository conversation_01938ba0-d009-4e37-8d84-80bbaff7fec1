/*! For license information please see 546.index.js.LICENSE.txt */
(self.webpackChunkeditor_parser=self.webpackChunkeditor_parser||[]).push([[546],{1466:(t,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0})},1597:(t,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0})},8623:function(t,e,r){"use strict";var n=this&&this.__decorate||function(t,e,r,n){var o,i=arguments.length,s=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,r,n);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(i<3?o(s):i>3?o(e,r,s):o(e,r))||s);return i>3&&s&&Object.defineProperty(e,r,s),s};Object.defineProperty(e,"__esModule",{value:!0}),e.ANTLRInputStream=void 0;const o=r(9282),i=r(8042),s=r(3227);class a{constructor(t){this.p=0,this.data=t,this.n=t.length}reset(){this.p=0}consume(){if(this.p>=this.n)throw o(this.LA(1)===s.IntStream.EOF),new Error("cannot consume EOF");this.p<this.n&&this.p++}LA(t){return 0===t?0:t<0&&(t++,this.p+t-1<0)||this.p+t-1>=this.n?s.IntStream.EOF:this.data.charCodeAt(this.p+t-1)}LT(t){return this.LA(t)}get index(){return this.p}get size(){return this.n}mark(){return-1}release(t){}seek(t){if(t<=this.p)this.p=t;else for(t=Math.min(t,this.n);this.p<t;)this.consume()}getText(t){let e=t.a,r=t.b;r>=this.n&&(r=this.n-1);let n=r-e+1;return e>=this.n?"":this.data.substr(e,n)}get sourceName(){return this.name?this.name:s.IntStream.UNKNOWN_SOURCE_NAME}toString(){return this.data}}n([i.Override],a.prototype,"consume",null),n([i.Override],a.prototype,"LA",null),n([i.Override],a.prototype,"index",null),n([i.Override],a.prototype,"size",null),n([i.Override],a.prototype,"mark",null),n([i.Override],a.prototype,"release",null),n([i.Override],a.prototype,"seek",null),n([i.Override],a.prototype,"getText",null),n([i.Override],a.prototype,"sourceName",null),n([i.Override],a.prototype,"toString",null),e.ANTLRInputStream=a},9701:function(t,e,r){"use strict";var n=this&&this.__decorate||function(t,e,r,n){var o,i=arguments.length,s=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,r,n);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(i<3?o(s):i>3?o(e,r,s):o(e,r))||s);return i>3&&s&&Object.defineProperty(e,r,s),s};Object.defineProperty(e,"__esModule",{value:!0}),e.BailErrorStrategy=void 0;const o=r(3992),i=r(4837),s=r(8042),a=r(156);class l extends o.DefaultErrorStrategy{recover(t,e){for(let r=t.context;r;r=r.parent)r.exception=e;throw new a.ParseCancellationException(e)}recoverInline(t){let e=new i.InputMismatchException(t);for(let r=t.context;r;r=r.parent)r.exception=e;throw new a.ParseCancellationException(e)}sync(t){}}n([s.Override],l.prototype,"recover",null),n([s.Override],l.prototype,"recoverInline",null),n([s.Override],l.prototype,"sync",null),e.BailErrorStrategy=l},8218:function(t,e,r){"use strict";var n=this&&this.__decorate||function(t,e,r,n){var o,i=arguments.length,s=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,r,n);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(i<3?o(s):i>3?o(e,r,s):o(e,r))||s);return i>3&&s&&Object.defineProperty(e,r,s),s},o=this&&this.__param||function(t,e){return function(r,n){e(r,n,t)}};Object.defineProperty(e,"__esModule",{value:!0}),e.BufferedTokenStream=void 0;const i=r(9282),s=r(824),a=r(8813),l=r(9557),u=r(8042),c=r(4966);let h=class{constructor(t){if(this.tokens=[],this.p=-1,this.fetchedEOF=!1,null==t)throw new Error("tokenSource cannot be null");this._tokenSource=t}get tokenSource(){return this._tokenSource}set tokenSource(t){this._tokenSource=t,this.tokens.length=0,this.p=-1,this.fetchedEOF=!1}get index(){return this.p}mark(){return 0}release(t){}seek(t){this.lazyInit(),this.p=this.adjustSeekIndex(t)}get size(){return this.tokens.length}consume(){let t;if(t=this.p>=0&&(this.fetchedEOF?this.p<this.tokens.length-1:this.p<this.tokens.length),!t&&this.LA(1)===c.Token.EOF)throw new Error("cannot consume EOF");this.sync(this.p+1)&&(this.p=this.adjustSeekIndex(this.p+1))}sync(t){i(t>=0);let e=t-this.tokens.length+1;if(e>0){return this.fetch(e)>=e}return!0}fetch(t){if(this.fetchedEOF)return 0;for(let e=0;e<t;e++){let t=this.tokenSource.nextToken();if(this.isWritableToken(t)&&(t.tokenIndex=this.tokens.length),this.tokens.push(t),t.type===c.Token.EOF)return this.fetchedEOF=!0,e+1}return t}get(t){if(t<0||t>=this.tokens.length)throw new RangeError("token index "+t+" out of range 0.."+(this.tokens.length-1));return this.tokens[t]}getRange(t,e){if(t<0||e<0)return[];this.lazyInit();let r=new Array;e>=this.tokens.length&&(e=this.tokens.length-1);for(let n=t;n<=e;n++){let t=this.tokens[n];if(t.type===c.Token.EOF)break;r.push(t)}return r}LA(t){let e=this.LT(t);return e?e.type:c.Token.INVALID_TYPE}tryLB(t){if(!(this.p-t<0))return this.tokens[this.p-t]}LT(t){let e=this.tryLT(t);if(void 0===e)throw new RangeError("requested lookback index out of range");return e}tryLT(t){if(this.lazyInit(),0===t)throw new RangeError("0 is not a valid lookahead index");if(t<0)return this.tryLB(-t);let e=this.p+t-1;return this.sync(e),e>=this.tokens.length?this.tokens[this.tokens.length-1]:this.tokens[e]}adjustSeekIndex(t){return t}lazyInit(){-1===this.p&&this.setup()}setup(){this.sync(0),this.p=this.adjustSeekIndex(0)}getTokens(t,e,r){if(this.lazyInit(),void 0===t)return i(void 0===e&&void 0===r),this.tokens;if(void 0===e&&(e=this.tokens.length-1),t<0||e>=this.tokens.length||e<0||t>=this.tokens.length)throw new RangeError("start "+t+" or stop "+e+" not in 0.."+(this.tokens.length-1));if(t>e)return[];if(void 0===r)return this.tokens.slice(t,e+1);"number"==typeof r&&(r=(new Set).add(r));let n=r,o=this.tokens.slice(t,e+1);return o=o.filter((t=>n.has(t.type))),o}nextTokenOnChannel(t,e){if(this.sync(t),t>=this.size)return this.size-1;let r=this.tokens[t];for(;r.channel!==e;){if(r.type===c.Token.EOF)return t;t++,this.sync(t),r=this.tokens[t]}return t}previousTokenOnChannel(t,e){if(this.sync(t),t>=this.size)return this.size-1;for(;t>=0;){let r=this.tokens[t];if(r.type===c.Token.EOF||r.channel===e)return t;t--}return t}getHiddenTokensToRight(t,e=-1){if(this.lazyInit(),t<0||t>=this.tokens.length)throw new RangeError(t+" not in 0.."+(this.tokens.length-1));let r,n=this.nextTokenOnChannel(t+1,l.Lexer.DEFAULT_TOKEN_CHANNEL),o=t+1;return r=-1===n?this.size-1:n,this.filterForChannel(o,r,e)}getHiddenTokensToLeft(t,e=-1){if(this.lazyInit(),t<0||t>=this.tokens.length)throw new RangeError(t+" not in 0.."+(this.tokens.length-1));if(0===t)return[];let r=this.previousTokenOnChannel(t-1,l.Lexer.DEFAULT_TOKEN_CHANNEL);if(r===t-1)return[];let n=r+1,o=t-1;return this.filterForChannel(n,o,e)}filterForChannel(t,e,r){let n=new Array;for(let o=t;o<=e;o++){let t=this.tokens[o];-1===r?t.channel!==l.Lexer.DEFAULT_TOKEN_CHANNEL&&n.push(t):t.channel===r&&n.push(t)}return n}get sourceName(){return this.tokenSource.sourceName}getText(t){void 0===t?t=a.Interval.of(0,this.size-1):t instanceof a.Interval||(t=t.sourceInterval);let e=t.a,r=t.b;if(e<0||r<0)return"";this.fill(),r>=this.tokens.length&&(r=this.tokens.length-1);let n="";for(let t=e;t<=r;t++){let e=this.tokens[t];if(e.type===c.Token.EOF)break;n+=e.text}return n.toString()}getTextFromRange(t,e){return this.isToken(t)&&this.isToken(e)?this.getText(a.Interval.of(t.tokenIndex,e.tokenIndex)):""}fill(){this.lazyInit();for(;;){if(this.fetch(1e3)<1e3)return}}isWritableToken(t){return t instanceof s.CommonToken}isToken(t){return t instanceof s.CommonToken}};n([u.NotNull],h.prototype,"_tokenSource",void 0),n([u.Override],h.prototype,"tokenSource",null),n([u.Override],h.prototype,"index",null),n([u.Override],h.prototype,"mark",null),n([u.Override],h.prototype,"release",null),n([u.Override],h.prototype,"seek",null),n([u.Override],h.prototype,"size",null),n([u.Override],h.prototype,"consume",null),n([u.Override],h.prototype,"get",null),n([u.Override],h.prototype,"LA",null),n([u.NotNull,u.Override],h.prototype,"LT",null),n([u.Override],h.prototype,"sourceName",null),n([u.NotNull,u.Override],h.prototype,"getText",null),n([u.NotNull,u.Override],h.prototype,"getTextFromRange",null),h=n([o(0,u.NotNull)],h),e.BufferedTokenStream=h},5699:(t,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0})},3675:(t,e,r)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.CharStreams=void 0;const n=r(5444),o=r(1540),i=r(3227);!function(t){t.fromString=function(t,e){void 0!==e&&0!==e.length||(e=i.IntStream.UNKNOWN_SOURCE_NAME);let r=n.CodePointBuffer.builder(t.length),s=new Uint16Array(t.length);for(let e=0;e<t.length;e++)s[e]=t.charCodeAt(e);return r.append(s),o.CodePointCharStream.fromBuffer(r.build(),e)}}(e.CharStreams||(e.CharStreams={}))},5444:(t,e,r)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.CodePointBuffer=void 0;const n=r(9282),o=r(9363);class i{constructor(t,e){this.buffer=t,this._position=0,this._size=e}static withArray(t){return new i(t,t.length)}get position(){return this._position}set position(t){if(t<0||t>this._size)throw new RangeError;this._position=t}get remaining(){return this._size-this.position}get(t){return this.buffer[t]}array(){return this.buffer.slice(0,this._size)}static builder(t){return new i.Builder(t)}}e.CodePointBuffer=i,function(t){let e;!function(t){t[t.BYTE=0]="BYTE",t[t.CHAR=1]="CHAR",t[t.INT=2]="INT"}(e||(e={}));class r{constructor(t){this.type=0,this.buffer=new Uint8Array(t),this.prevHighSurrogate=-1,this.position=0}build(){return new t(this.buffer,this.position)}static roundUpToNextPowerOfTwo(t){let e=32-Math.clz32(t-1);return Math.pow(2,e)}ensureRemaining(t){switch(this.type){case 0:if(this.buffer.length-this.position<t){let e=r.roundUpToNextPowerOfTwo(this.buffer.length+t),n=new Uint8Array(e);n.set(this.buffer.subarray(0,this.position),0),this.buffer=n}break;case 1:if(this.buffer.length-this.position<t){let e=r.roundUpToNextPowerOfTwo(this.buffer.length+t),n=new Uint16Array(e);n.set(this.buffer.subarray(0,this.position),0),this.buffer=n}break;case 2:if(this.buffer.length-this.position<t){let e=r.roundUpToNextPowerOfTwo(this.buffer.length+t),n=new Int32Array(e);n.set(this.buffer.subarray(0,this.position),0),this.buffer=n}}}append(t){this.ensureRemaining(t.length),this.appendArray(t)}appendArray(t){switch(this.type){case 0:this.appendArrayByte(t);break;case 1:this.appendArrayChar(t);break;case 2:this.appendArrayInt(t)}}appendArrayByte(t){n(-1===this.prevHighSurrogate);let e=t,r=0,i=t.length,s=this.buffer,a=this.position;for(;r<i;){let n=e[r];if(!(n<=255))return t=t.subarray(r,i),this.position=a,o.isHighSurrogate(n)?(this.byteToIntBuffer(t.length),void this.appendArrayInt(t)):(this.byteToCharBuffer(t.length),void this.appendArrayChar(t));s[a]=n,r++,a++}this.position=a}appendArrayChar(t){n(-1===this.prevHighSurrogate);let e=t,r=0,i=t.length,s=this.buffer,a=this.position;for(;r<i;){let n=e[r];if(o.isHighSurrogate(n))return t=t.subarray(r,i),this.position=a,this.charToIntBuffer(t.length),void this.appendArrayInt(t);s[a]=n,r++,a++}this.position=a}appendArrayInt(t){let e=t,r=0,n=t.length,i=this.buffer,s=this.position;for(;r<n;){let t=e[r];r++,-1!==this.prevHighSurrogate?o.isLowSurrogate(t)?(i[s]=String.fromCharCode(this.prevHighSurrogate,t).codePointAt(0),s++,this.prevHighSurrogate=-1):(i[s]=this.prevHighSurrogate,s++,o.isHighSurrogate(t)?this.prevHighSurrogate=t:(i[s]=t,s++,this.prevHighSurrogate=-1)):o.isHighSurrogate(t)?this.prevHighSurrogate=t:(i[s]=t,s++)}-1!==this.prevHighSurrogate&&(i[s]=this.prevHighSurrogate,s++),this.position=s}byteToCharBuffer(t){let e=new Uint16Array(Math.max(this.position+t,this.buffer.length>>1));e.set(this.buffer.subarray(0,this.position),0),this.type=1,this.buffer=e}byteToIntBuffer(t){let e=new Int32Array(Math.max(this.position+t,this.buffer.length>>2));e.set(this.buffer.subarray(0,this.position),0),this.type=2,this.buffer=e}charToIntBuffer(t){let e=new Int32Array(Math.max(this.position+t,this.buffer.length>>1));e.set(this.buffer.subarray(0,this.position),0),this.type=2,this.buffer=e}}t.Builder=r}(i=e.CodePointBuffer||(e.CodePointBuffer={}))},1540:function(t,e,r){"use strict";var n=this&&this.__decorate||function(t,e,r,n){var o,i=arguments.length,s=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,r,n);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(i<3?o(s):i>3?o(e,r,s):o(e,r))||s);return i>3&&s&&Object.defineProperty(e,r,s),s};Object.defineProperty(e,"__esModule",{value:!0}),e.CodePointCharStream=void 0;const o=r(9282),i=r(3227),s=r(8813),a=r(8042);class l{constructor(t,e,r,n){o(0===e),this._array=t,this._size=r,this._name=n,this._position=0}get internalStorage(){return this._array}static fromBuffer(t,e){return void 0!==e&&0!==e.length||(e=i.IntStream.UNKNOWN_SOURCE_NAME),new l(t.array(),t.position,t.remaining,e)}consume(){if(this._size-this._position==0)throw o(this.LA(1)===i.IntStream.EOF),new RangeError("cannot consume EOF");this._position++}get index(){return this._position}get size(){return this._size}mark(){return-1}release(t){}seek(t){this._position=t}get sourceName(){return this._name}toString(){return this.getText(s.Interval.of(0,this.size-1))}LA(t){let e;switch(Math.sign(t)){case-1:return e=this.index+t,e<0?i.IntStream.EOF:this._array[e];case 0:return 0;case 1:return e=this.index+t-1,e>=this.size?i.IntStream.EOF:this._array[e]}throw new RangeError("Not reached")}getText(t){const e=Math.min(t.a,this.size),r=Math.min(t.b-t.a+1,this.size-e);return this._array instanceof Int32Array?String.fromCodePoint(...Array.from(this._array.subarray(e,e+r))):String.fromCharCode(...Array.from(this._array.subarray(e,e+r)))}}n([a.Override],l.prototype,"consume",null),n([a.Override],l.prototype,"index",null),n([a.Override],l.prototype,"size",null),n([a.Override],l.prototype,"mark",null),n([a.Override],l.prototype,"release",null),n([a.Override],l.prototype,"seek",null),n([a.Override],l.prototype,"sourceName",null),n([a.Override],l.prototype,"toString",null),n([a.Override],l.prototype,"LA",null),n([a.Override],l.prototype,"getText",null),e.CodePointCharStream=l},824:function(t,e,r){"use strict";var n=this&&this.__decorate||function(t,e,r,n){var o,i=arguments.length,s=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,r,n);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(i<3?o(s):i>3?o(e,r,s):o(e,r))||s);return i>3&&s&&Object.defineProperty(e,r,s),s},o=this&&this.__param||function(t,e){return function(r,n){e(r,n,t)}};Object.defineProperty(e,"__esModule",{value:!0}),e.CommonToken=void 0;const i=r(8813),s=r(8042),a=r(4966);let l=class t{constructor(e,r,n=t.EMPTY_SOURCE,o=a.Token.DEFAULT_CHANNEL,i=0,s=0){this._line=0,this._charPositionInLine=-1,this._channel=a.Token.DEFAULT_CHANNEL,this.index=-1,this._text=r,this._type=e,this.source=n,this._channel=o,this.start=i,this.stop=s,null!=n.source&&(this._line=n.source.line,this._charPositionInLine=n.source.charPositionInLine)}static fromToken(e){let r=new t(e.type,void 0,t.EMPTY_SOURCE,e.channel,e.startIndex,e.stopIndex);return r._line=e.line,r.index=e.tokenIndex,r._charPositionInLine=e.charPositionInLine,e instanceof t?(r._text=e._text,r.source=e.source):(r._text=e.text,r.source={source:e.tokenSource,stream:e.inputStream}),r}get type(){return this._type}set type(t){this._type=t}get line(){return this._line}set line(t){this._line=t}get text(){if(null!=this._text)return this._text;let t=this.inputStream;if(null==t)return;let e=t.size;return this.start<e&&this.stop<e?t.getText(i.Interval.of(this.start,this.stop)):"<EOF>"}set text(t){this._text=t}get charPositionInLine(){return this._charPositionInLine}set charPositionInLine(t){this._charPositionInLine=t}get channel(){return this._channel}set channel(t){this._channel=t}get startIndex(){return this.start}set startIndex(t){this.start=t}get stopIndex(){return this.stop}set stopIndex(t){this.stop=t}get tokenIndex(){return this.index}set tokenIndex(t){this.index=t}get tokenSource(){return this.source.source}get inputStream(){return this.source.stream}toString(t){let e="";this._channel>0&&(e=",channel="+this._channel);let r=this.text;null!=r?(r=r.replace(/\n/g,"\\n"),r=r.replace(/\r/g,"\\r"),r=r.replace(/\t/g,"\\t")):r="<no text>";let n=String(this._type);return t&&(n=t.vocabulary.getDisplayName(this._type)),"[@"+this.tokenIndex+","+this.start+":"+this.stop+"='"+r+"',<"+n+">"+e+","+this._line+":"+this.charPositionInLine+"]"}};l.EMPTY_SOURCE={source:void 0,stream:void 0},n([s.NotNull],l.prototype,"source",void 0),n([s.Override],l.prototype,"type",null),n([s.Override],l.prototype,"line",null),n([s.Override],l.prototype,"text",null),n([s.Override],l.prototype,"charPositionInLine",null),n([s.Override],l.prototype,"channel",null),n([s.Override],l.prototype,"startIndex",null),n([s.Override],l.prototype,"stopIndex",null),n([s.Override],l.prototype,"tokenIndex",null),n([s.Override],l.prototype,"tokenSource",null),n([s.Override],l.prototype,"inputStream",null),n([s.Override],l.prototype,"toString",null),n([o(0,s.NotNull)],l,"fromToken",null),l=n([o(2,s.NotNull)],l),e.CommonToken=l},8735:function(t,e,r){"use strict";var n=this&&this.__decorate||function(t,e,r,n){var o,i=arguments.length,s=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,r,n);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(i<3?o(s):i>3?o(e,r,s):o(e,r))||s);return i>3&&s&&Object.defineProperty(e,r,s),s};Object.defineProperty(e,"__esModule",{value:!0}),e.CommonTokenFactory=void 0;const o=r(824),i=r(8813),s=r(8042);class a{constructor(t=!1){this.copyText=t}create(t,e,r,n,s,a,l,u){let c=new o.CommonToken(e,r,t,n,s,a);return c.line=l,c.charPositionInLine=u,null==r&&this.copyText&&null!=t.stream&&(c.text=t.stream.getText(i.Interval.of(s,a))),c}createSimple(t,e){return new o.CommonToken(t,e)}}n([s.Override],a.prototype,"create",null),n([s.Override],a.prototype,"createSimple",null),e.CommonTokenFactory=a,function(t){t.DEFAULT=new t}(a=e.CommonTokenFactory||(e.CommonTokenFactory={}))},4321:function(t,e,r){"use strict";var n=this&&this.__decorate||function(t,e,r,n){var o,i=arguments.length,s=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,r,n);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(i<3?o(s):i>3?o(e,r,s):o(e,r))||s);return i>3&&s&&Object.defineProperty(e,r,s),s},o=this&&this.__param||function(t,e){return function(r,n){e(r,n,t)}};Object.defineProperty(e,"__esModule",{value:!0}),e.CommonTokenStream=void 0;const i=r(8218),s=r(8042),a=r(4966);let l=class extends i.BufferedTokenStream{constructor(t,e=a.Token.DEFAULT_CHANNEL){super(t),this.channel=e}adjustSeekIndex(t){return this.nextTokenOnChannel(t,this.channel)}tryLB(t){if(this.p-t<0)return;let e=this.p,r=1;for(;r<=t&&e>0;)e=this.previousTokenOnChannel(e-1,this.channel),r++;return e<0?void 0:this.tokens[e]}tryLT(t){if(this.lazyInit(),0===t)throw new RangeError("0 is not a valid lookahead index");if(t<0)return this.tryLB(-t);let e=this.p,r=1;for(;r<t;)this.sync(e+1)&&(e=this.nextTokenOnChannel(e+1,this.channel)),r++;return this.tokens[e]}getNumberOfOnChannelTokens(){let t=0;this.fill();for(let e of this.tokens)if(e.channel===this.channel&&t++,e.type===a.Token.EOF)break;return t}};n([s.Override],l.prototype,"adjustSeekIndex",null),n([s.Override],l.prototype,"tryLB",null),n([s.Override],l.prototype,"tryLT",null),l=n([o(0,s.NotNull)],l),e.CommonTokenStream=l},4525:(t,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.ConsoleErrorListener=void 0;class r{syntaxError(t,e,r,n,o,i){console.error(`line ${r}:${n} ${o}`)}}e.ConsoleErrorListener=r,r.INSTANCE=new r},8042:(t,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.SuppressWarnings=e.Override=e.Nullable=e.NotNull=void 0,e.NotNull=function(t,e,r){},e.Nullable=function(t,e,r){},e.Override=function(t,e,r){},e.SuppressWarnings=function(t){return(t,e,r)=>{}}},3992:function(t,e,r){"use strict";var n=this&&this.__decorate||function(t,e,r,n){var o,i=arguments.length,s=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,r,n);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(i<3?o(s):i>3?o(e,r,s):o(e,r))||s);return i>3&&s&&Object.defineProperty(e,r,s),s},o=this&&this.__param||function(t,e){return function(r,n){e(r,n,t)}};Object.defineProperty(e,"__esModule",{value:!0}),e.DefaultErrorStrategy=void 0;const i=r(3269),s=r(4700),a=r(2915),l=r(4837),u=r(4405),c=r(4609),h=r(9767),p=r(4966),f=r(8042);class d{constructor(){this.errorRecoveryMode=!1,this.lastErrorIndex=-1,this.nextTokensState=i.ATNState.INVALID_STATE_NUMBER}reset(t){this.endErrorCondition(t)}beginErrorCondition(t){this.errorRecoveryMode=!0}inErrorRecoveryMode(t){return this.errorRecoveryMode}endErrorCondition(t){this.errorRecoveryMode=!1,this.lastErrorStates=void 0,this.lastErrorIndex=-1}reportMatch(t){this.endErrorCondition(t)}reportError(t,e){this.inErrorRecoveryMode(t)||(this.beginErrorCondition(t),e instanceof c.NoViableAltException?this.reportNoViableAlternative(t,e):e instanceof l.InputMismatchException?this.reportInputMismatch(t,e):e instanceof a.FailedPredicateException?this.reportFailedPredicate(t,e):(console.error(`unknown recognition error type: ${e}`),this.notifyErrorListeners(t,e.toString(),e)))}notifyErrorListeners(t,e,r){let n=r.getOffendingToken(t);void 0===n&&(n=null),t.notifyErrorListeners(e,n,r)}recover(t,e){this.lastErrorIndex===t.inputStream.index&&this.lastErrorStates&&this.lastErrorStates.contains(t.state)&&t.consume(),this.lastErrorIndex=t.inputStream.index,this.lastErrorStates||(this.lastErrorStates=new u.IntervalSet),this.lastErrorStates.add(t.state);let r=this.getErrorRecoverySet(t);this.consumeUntil(t,r)}sync(t){let e=t.interpreter.atn.states[t.state];if(this.inErrorRecoveryMode(t))return;let r=t.inputStream.LA(1),n=t.atn.nextTokens(e);if(n.contains(r))return this.nextTokensContext=void 0,void(this.nextTokensState=i.ATNState.INVALID_STATE_NUMBER);if(n.contains(p.Token.EPSILON))void 0===this.nextTokensContext&&(this.nextTokensContext=t.context,this.nextTokensState=t.state);else switch(e.stateType){case s.ATNStateType.BLOCK_START:case s.ATNStateType.STAR_BLOCK_START:case s.ATNStateType.PLUS_BLOCK_START:case s.ATNStateType.STAR_LOOP_ENTRY:if(this.singleTokenDeletion(t))return;throw new l.InputMismatchException(t);case s.ATNStateType.PLUS_LOOP_BACK:case s.ATNStateType.STAR_LOOP_BACK:this.reportUnwantedToken(t);let e=t.getExpectedTokens().or(this.getErrorRecoverySet(t));this.consumeUntil(t,e)}}reportNoViableAlternative(t,e){let r,n=t.inputStream;r=n?e.startToken.type===p.Token.EOF?"<EOF>":n.getTextFromRange(e.startToken,e.getOffendingToken()):"<unknown input>";let o="no viable alternative at input "+this.escapeWSAndQuote(r);this.notifyErrorListeners(t,o,e)}reportInputMismatch(t,e){let r=e.expectedTokens,n=r?r.toStringVocabulary(t.vocabulary):"",o="mismatched input "+this.getTokenErrorDisplay(e.getOffendingToken(t))+" expecting "+n;this.notifyErrorListeners(t,o,e)}reportFailedPredicate(t,e){let r="rule "+t.ruleNames[t.context.ruleIndex]+" "+e.message;this.notifyErrorListeners(t,r,e)}reportUnwantedToken(t){if(this.inErrorRecoveryMode(t))return;this.beginErrorCondition(t);let e=t.currentToken,r="extraneous input "+this.getTokenErrorDisplay(e)+" expecting "+this.getExpectedTokens(t).toStringVocabulary(t.vocabulary);t.notifyErrorListeners(r,e,void 0)}reportMissingToken(t){if(this.inErrorRecoveryMode(t))return;this.beginErrorCondition(t);let e=t.currentToken,r="missing "+this.getExpectedTokens(t).toStringVocabulary(t.vocabulary)+" at "+this.getTokenErrorDisplay(e);t.notifyErrorListeners(r,e,void 0)}recoverInline(t){let e=this.singleTokenDeletion(t);if(e)return t.consume(),e;if(this.singleTokenInsertion(t))return this.getMissingSymbol(t);throw void 0===this.nextTokensContext?new l.InputMismatchException(t):new l.InputMismatchException(t,this.nextTokensState,this.nextTokensContext)}singleTokenInsertion(t){let e=t.inputStream.LA(1),r=t.interpreter.atn.states[t.state].transition(0).target,n=t.interpreter.atn;return!!n.nextTokens(r,h.PredictionContext.fromRuleContext(n,t.context)).contains(e)&&(this.reportMissingToken(t),!0)}singleTokenDeletion(t){let e=t.inputStream.LA(2);if(this.getExpectedTokens(t).contains(e)){this.reportUnwantedToken(t),t.consume();let e=t.currentToken;return this.reportMatch(t),e}}getMissingSymbol(t){let e,r=t.currentToken,n=this.getExpectedTokens(t),o=p.Token.INVALID_TYPE;n.isNil||(o=n.minElement),e=o===p.Token.EOF?"<missing EOF>":"<missing "+t.vocabulary.getDisplayName(o)+">";let i=r,s=t.inputStream.tryLT(-1);return i.type===p.Token.EOF&&null!=s&&(i=s),this.constructToken(t.inputStream.tokenSource,o,e,i)}constructToken(t,e,r,n){let o=t.tokenFactory,i=n.tokenSource,s=i?i.inputStream:void 0;return o.create({source:t,stream:s},e,r,p.Token.DEFAULT_CHANNEL,-1,-1,n.line,n.charPositionInLine)}getExpectedTokens(t){return t.getExpectedTokens()}getTokenErrorDisplay(t){if(!t)return"<no token>";let e=this.getSymbolText(t);return e||(e=this.getSymbolType(t)===p.Token.EOF?"<EOF>":`<${this.getSymbolType(t)}>`),this.escapeWSAndQuote(e)}getSymbolText(t){return t.text}getSymbolType(t){return t.type}escapeWSAndQuote(t){return"'"+(t=(t=(t=t.replace("\n","\\n")).replace("\r","\\r")).replace("\t","\\t"))+"'"}getErrorRecoverySet(t){let e=t.interpreter.atn,r=t.context,n=new u.IntervalSet;for(;r&&r.invokingState>=0;){let t=e.states[r.invokingState].transition(0),o=e.nextTokens(t.followState);n.addAll(o),r=r._parent}return n.remove(p.Token.EPSILON),n}consumeUntil(t,e){let r=t.inputStream.LA(1);for(;r!==p.Token.EOF&&!e.contains(r);)t.consume(),r=t.inputStream.LA(1)}}n([f.Override],d.prototype,"reset",null),n([o(0,f.NotNull)],d.prototype,"beginErrorCondition",null),n([f.Override],d.prototype,"inErrorRecoveryMode",null),n([o(0,f.NotNull)],d.prototype,"endErrorCondition",null),n([f.Override],d.prototype,"reportMatch",null),n([f.Override],d.prototype,"reportError",null),n([o(0,f.NotNull)],d.prototype,"notifyErrorListeners",null),n([f.Override],d.prototype,"recover",null),n([f.Override],d.prototype,"sync",null),n([o(0,f.NotNull),o(1,f.NotNull)],d.prototype,"reportNoViableAlternative",null),n([o(0,f.NotNull),o(1,f.NotNull)],d.prototype,"reportInputMismatch",null),n([o(0,f.NotNull),o(1,f.NotNull)],d.prototype,"reportFailedPredicate",null),n([o(0,f.NotNull)],d.prototype,"reportUnwantedToken",null),n([o(0,f.NotNull)],d.prototype,"reportMissingToken",null),n([f.Override],d.prototype,"recoverInline",null),n([o(0,f.NotNull)],d.prototype,"singleTokenInsertion",null),n([o(0,f.NotNull)],d.prototype,"singleTokenDeletion",null),n([f.NotNull,o(0,f.NotNull)],d.prototype,"getMissingSymbol",null),n([f.NotNull,o(0,f.NotNull)],d.prototype,"getExpectedTokens",null),n([o(0,f.NotNull)],d.prototype,"getSymbolText",null),n([o(0,f.NotNull)],d.prototype,"getSymbolType",null),n([f.NotNull,o(0,f.NotNull)],d.prototype,"escapeWSAndQuote",null),n([f.NotNull,o(0,f.NotNull)],d.prototype,"getErrorRecoverySet",null),n([o(0,f.NotNull),o(1,f.NotNull)],d.prototype,"consumeUntil",null),e.DefaultErrorStrategy=d},4361:(t,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.Dependents=void 0,function(t){t[t.SELF=0]="SELF",t[t.PARENTS=1]="PARENTS",t[t.CHILDREN=2]="CHILDREN",t[t.ANCESTORS=3]="ANCESTORS",t[t.DESCENDANTS=4]="DESCENDANTS",t[t.SIBLINGS=5]="SIBLINGS",t[t.PRECEEDING_SIBLINGS=6]="PRECEEDING_SIBLINGS",t[t.FOLLOWING_SIBLINGS=7]="FOLLOWING_SIBLINGS",t[t.PRECEEDING=8]="PRECEEDING",t[t.FOLLOWING=9]="FOLLOWING"}(e.Dependents||(e.Dependents={}))},7574:function(t,e,r){"use strict";var n=this&&this.__decorate||function(t,e,r,n){var o,i=arguments.length,s=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,r,n);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(i<3?o(s):i>3?o(e,r,s):o(e,r))||s);return i>3&&s&&Object.defineProperty(e,r,s),s},o=this&&this.__param||function(t,e){return function(r,n){e(r,n,t)}};Object.defineProperty(e,"__esModule",{value:!0}),e.DiagnosticErrorListener=void 0;const i=r(5280),s=r(8042),a=r(8813);class l{constructor(t=!0){this.exactOnly=t,this.exactOnly=t}syntaxError(t,e,r,n,o,i){}reportAmbiguity(t,e,r,n,o,i,s){if(this.exactOnly&&!o)return;let l=`reportAmbiguity d=${this.getDecisionDescription(t,e)}: ambigAlts=${this.getConflictingAlts(i,s)}, input='${t.inputStream.getText(a.Interval.of(r,n))}'`;t.notifyErrorListeners(l)}reportAttemptingFullContext(t,e,r,n,o,i){let s=`reportAttemptingFullContext d=${this.getDecisionDescription(t,e)}, input='${t.inputStream.getText(a.Interval.of(r,n))}'`;t.notifyErrorListeners(s)}reportContextSensitivity(t,e,r,n,o,i){let s=`reportContextSensitivity d=${this.getDecisionDescription(t,e)}, input='${t.inputStream.getText(a.Interval.of(r,n))}'`;t.notifyErrorListeners(s)}getDecisionDescription(t,e){let r=e.decision,n=e.atnStartState.ruleIndex,o=t.ruleNames;if(n<0||n>=o.length)return r.toString();let i=o[n];return i?`${r} (${i})`:r.toString()}getConflictingAlts(t,e){if(null!=t)return t;let r=new i.BitSet;for(let t of e)r.set(t.alt);return r}}n([s.Override],l.prototype,"syntaxError",null),n([s.Override,o(0,s.NotNull),o(1,s.NotNull),o(6,s.NotNull)],l.prototype,"reportAmbiguity",null),n([s.Override,o(0,s.NotNull),o(1,s.NotNull),o(5,s.NotNull)],l.prototype,"reportAttemptingFullContext",null),n([s.Override,o(0,s.NotNull),o(1,s.NotNull),o(5,s.NotNull)],l.prototype,"reportContextSensitivity",null),n([o(0,s.NotNull),o(1,s.NotNull)],l.prototype,"getDecisionDescription",null),n([s.NotNull,o(1,s.NotNull)],l.prototype,"getConflictingAlts",null),e.DiagnosticErrorListener=l},2915:function(t,e,r){"use strict";var n=this&&this.__decorate||function(t,e,r,n){var o,i=arguments.length,s=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,r,n);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(i<3?o(s):i>3?o(e,r,s):o(e,r))||s);return i>3&&s&&Object.defineProperty(e,r,s),s},o=this&&this.__param||function(t,e){return function(r,n){e(r,n,t)}};Object.defineProperty(e,"__esModule",{value:!0}),e.FailedPredicateException=void 0;const i=r(3998),s=r(8042),a=r(3233);let l=class t extends i.RecognitionException{constructor(e,r,n){super(e,e.inputStream,e.context,t.formatMessage(r,n));let o=e.interpreter.atn.states[e.state].transition(0);o instanceof a.PredicateTransition?(this._ruleIndex=o.ruleIndex,this._predicateIndex=o.predIndex):(this._ruleIndex=0,this._predicateIndex=0),this._predicate=r,super.setOffendingToken(e,e.currentToken)}get ruleIndex(){return this._ruleIndex}get predicateIndex(){return this._predicateIndex}get predicate(){return this._predicate}static formatMessage(t,e){return e||`failed predicate: {${t}}?`}};n([s.NotNull],l,"formatMessage",null),l=n([o(0,s.NotNull)],l),e.FailedPredicateException=l},4837:function(t,e,r){"use strict";var n=this&&this.__decorate||function(t,e,r,n){var o,i=arguments.length,s=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,r,n);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(i<3?o(s):i>3?o(e,r,s):o(e,r))||s);return i>3&&s&&Object.defineProperty(e,r,s),s},o=this&&this.__param||function(t,e){return function(r,n){e(r,n,t)}};Object.defineProperty(e,"__esModule",{value:!0}),e.InputMismatchException=void 0;const i=r(3998),s=r(8042);let a=class extends i.RecognitionException{constructor(t,e,r){void 0===r&&(r=t.context),super(t,t.inputStream,r),void 0!==e&&this.setOffendingState(e),this.setOffendingToken(t,t.currentToken)}};a=n([o(0,s.NotNull)],a),e.InputMismatchException=a},3227:(t,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.IntStream=void 0,function(t){t.EOF=-1,t.UNKNOWN_SOURCE_NAME="<unknown>"}(e.IntStream||(e.IntStream={}))},4126:function(t,e,r){"use strict";var n=this&&this.__decorate||function(t,e,r,n){var o,i=arguments.length,s=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,r,n);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(i<3?o(s):i>3?o(e,r,s):o(e,r))||s);return i>3&&s&&Object.defineProperty(e,r,s),s};Object.defineProperty(e,"__esModule",{value:!0}),e.InterpreterRuleContext=void 0;const o=r(8042),i=r(3208);class s extends i.ParserRuleContext{constructor(t,e,r){void 0!==r?super(e,r):super(),this._ruleIndex=t}get ruleIndex(){return this._ruleIndex}}n([o.Override],s.prototype,"ruleIndex",null),e.InterpreterRuleContext=s},9557:function(t,e,r){"use strict";var n=this&&this.__decorate||function(t,e,r,n){var o,i=arguments.length,s=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,r,n);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(i<3?o(s):i>3?o(e,r,s):o(e,r))||s);return i>3&&s&&Object.defineProperty(e,r,s),s};Object.defineProperty(e,"__esModule",{value:!0}),e.Lexer=void 0;const o=r(8735),i=r(1350),s=r(8813),a=r(3227),l=r(2178),u=r(5324),c=r(8042),h=r(8610),p=r(4966);class f extends h.Recognizer{constructor(t){super(),this._factory=o.CommonTokenFactory.DEFAULT,this._tokenStartCharIndex=-1,this._tokenStartLine=0,this._tokenStartCharPositionInLine=0,this._hitEOF=!1,this._channel=0,this._type=0,this._modeStack=new i.IntegerStack,this._mode=f.DEFAULT_MODE,this._input=t,this._tokenFactorySourcePair={source:this,stream:t}}static get DEFAULT_TOKEN_CHANNEL(){return p.Token.DEFAULT_CHANNEL}static get HIDDEN(){return p.Token.HIDDEN_CHANNEL}reset(t){(void 0===t||t)&&this._input.seek(0),this._token=void 0,this._type=p.Token.INVALID_TYPE,this._channel=p.Token.DEFAULT_CHANNEL,this._tokenStartCharIndex=-1,this._tokenStartCharPositionInLine=-1,this._tokenStartLine=-1,this._text=void 0,this._hitEOF=!1,this._mode=f.DEFAULT_MODE,this._modeStack.clear(),this.interpreter.reset()}nextToken(){if(null==this._input)throw new Error("nextToken requires a non-null input stream.");let t=this._input.mark();try{t:for(;;){if(this._hitEOF)return this.emitEOF();this._token=void 0,this._channel=p.Token.DEFAULT_CHANNEL,this._tokenStartCharIndex=this._input.index,this._tokenStartCharPositionInLine=this.interpreter.charPositionInLine,this._tokenStartLine=this.interpreter.line,this._text=void 0;do{let t;this._type=p.Token.INVALID_TYPE;try{t=this.interpreter.match(this._input,this._mode)}catch(e){if(!(e instanceof u.LexerNoViableAltException))throw e;this.notifyListeners(e),this.recover(e),t=f.SKIP}if(this._input.LA(1)===a.IntStream.EOF&&(this._hitEOF=!0),this._type===p.Token.INVALID_TYPE&&(this._type=t),this._type===f.SKIP)continue t}while(this._type===f.MORE);return null==this._token?this.emit():this._token}}finally{this._input.release(t)}}skip(){this._type=f.SKIP}more(){this._type=f.MORE}mode(t){this._mode=t}pushMode(t){l.LexerATNSimulator.debug&&console.log("pushMode "+t),this._modeStack.push(this._mode),this.mode(t)}popMode(){if(this._modeStack.isEmpty)throw new Error("EmptyStackException");return l.LexerATNSimulator.debug&&console.log("popMode back to "+this._modeStack.peek()),this.mode(this._modeStack.pop()),this._mode}get tokenFactory(){return this._factory}set tokenFactory(t){this._factory=t}get inputStream(){return this._input}set inputStream(t){this.reset(!1),this._input=t,this._tokenFactorySourcePair={source:this,stream:this._input}}get sourceName(){return this._input.sourceName}emit(t){return t||(t=this._factory.create(this._tokenFactorySourcePair,this._type,this._text,this._channel,this._tokenStartCharIndex,this.charIndex-1,this._tokenStartLine,this._tokenStartCharPositionInLine)),this._token=t,t}emitEOF(){let t=this.charPositionInLine,e=this.line,r=this._factory.create(this._tokenFactorySourcePair,p.Token.EOF,void 0,p.Token.DEFAULT_CHANNEL,this._input.index,this._input.index-1,e,t);return this.emit(r),r}get line(){return this.interpreter.line}set line(t){this.interpreter.line=t}get charPositionInLine(){return this.interpreter.charPositionInLine}set charPositionInLine(t){this.interpreter.charPositionInLine=t}get charIndex(){return this._input.index}get text(){return null!=this._text?this._text:this.interpreter.getText(this._input)}set text(t){this._text=t}get token(){return this._token}set token(t){this._token=t}set type(t){this._type=t}get type(){return this._type}set channel(t){this._channel=t}get channel(){return this._channel}getAllTokens(){let t=[],e=this.nextToken();for(;e.type!==p.Token.EOF;)t.push(e),e=this.nextToken();return t}notifyListeners(t){let e=this._input.getText(s.Interval.of(this._tokenStartCharIndex,this._input.index)),r="token recognition error at: '"+this.getErrorDisplay(e)+"'",n=this.getErrorListenerDispatch();n.syntaxError&&n.syntaxError(this,void 0,this._tokenStartLine,this._tokenStartCharPositionInLine,r,t)}getErrorDisplay(t){if("number"==typeof t){switch(t){case p.Token.EOF:return"<EOF>";case 10:return"\\n";case 9:return"\\t";case 13:return"\\r"}return String.fromCharCode(t)}return t.replace(/\n/g,"\\n").replace(/\t/g,"\\t").replace(/\r/g,"\\r")}getCharErrorDisplay(t){return"'"+this.getErrorDisplay(t)+"'"}recover(t){t instanceof u.LexerNoViableAltException?this._input.LA(1)!==a.IntStream.EOF&&this.interpreter.consume(this._input):this._input.consume()}}f.DEFAULT_MODE=0,f.MORE=-2,f.SKIP=-3,f.MIN_CHAR_VALUE=0,f.MAX_CHAR_VALUE=1114111,n([c.Override],f.prototype,"nextToken",null),n([c.Override],f.prototype,"tokenFactory",null),n([c.Override],f.prototype,"inputStream",null),n([c.Override],f.prototype,"sourceName",null),n([c.Override],f.prototype,"line",null),n([c.Override],f.prototype,"charPositionInLine",null),e.Lexer=f},7301:function(t,e,r){"use strict";var n=this&&this.__decorate||function(t,e,r,n){var o,i=arguments.length,s=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,r,n);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(i<3?o(s):i>3?o(e,r,s):o(e,r))||s);return i>3&&s&&Object.defineProperty(e,r,s),s},o=this&&this.__param||function(t,e){return function(r,n){e(r,n,t)}};Object.defineProperty(e,"__esModule",{value:!0}),e.LexerInterpreter=void 0;const i=r(9557),s=r(2178),a=r(8042),l=r(8042);let u=class extends i.Lexer{constructor(t,e,r,n,o,i,a){if(super(a),0!==i.grammarType)throw new Error("IllegalArgumentException: The ATN must be a lexer ATN.");this._grammarFileName=t,this._atn=i,this._ruleNames=r.slice(0),this._channelNames=n.slice(0),this._modeNames=o.slice(0),this._vocabulary=e,this._interp=new s.LexerATNSimulator(i,this)}get atn(){return this._atn}get grammarFileName(){return this._grammarFileName}get ruleNames(){return this._ruleNames}get channelNames(){return this._channelNames}get modeNames(){return this._modeNames}get vocabulary(){return this._vocabulary}};n([a.NotNull],u.prototype,"_vocabulary",void 0),n([l.Override],u.prototype,"atn",null),n([l.Override],u.prototype,"grammarFileName",null),n([l.Override],u.prototype,"ruleNames",null),n([l.Override],u.prototype,"channelNames",null),n([l.Override],u.prototype,"modeNames",null),n([l.Override],u.prototype,"vocabulary",null),u=n([o(1,a.NotNull)],u),e.LexerInterpreter=u},5324:function(t,e,r){"use strict";var n=this&&this.__decorate||function(t,e,r,n){var o,i=arguments.length,s=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,r,n);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(i<3?o(s):i>3?o(e,r,s):o(e,r))||s);return i>3&&s&&Object.defineProperty(e,r,s),s},o=this&&this.__param||function(t,e){return function(r,n){e(r,n,t)}};Object.defineProperty(e,"__esModule",{value:!0}),e.LexerNoViableAltException=void 0;const i=r(3998),s=r(8042),a=r(8813),l=r(5103);let u=class extends i.RecognitionException{constructor(t,e,r,n){super(t,e),this._startIndex=r,this._deadEndConfigs=n}get startIndex(){return this._startIndex}get deadEndConfigs(){return this._deadEndConfigs}get inputStream(){return super.inputStream}toString(){let t="";return this._startIndex>=0&&this._startIndex<this.inputStream.size&&(t=this.inputStream.getText(a.Interval.of(this._startIndex,this._startIndex)),t=l.escapeWhitespace(t,!1)),`LexerNoViableAltException('${t}')`}};n([s.Override],u.prototype,"inputStream",null),n([s.Override],u.prototype,"toString",null),u=n([o(1,s.NotNull)],u),e.LexerNoViableAltException=u},7683:function(t,e,r){"use strict";var n=this&&this.__decorate||function(t,e,r,n){var o,i=arguments.length,s=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,r,n);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(i<3?o(s):i>3?o(e,r,s):o(e,r))||s);return i>3&&s&&Object.defineProperty(e,r,s),s},o=this&&this.__param||function(t,e){return function(r,n){e(r,n,t)}};Object.defineProperty(e,"__esModule",{value:!0}),e.ListTokenSource=void 0;const i=r(8735),s=r(8042),a=r(4966);let l=class{constructor(t,e){if(this.i=0,this._factory=i.CommonTokenFactory.DEFAULT,null==t)throw new Error("tokens cannot be null");this.tokens=t,this._sourceName=e}get charPositionInLine(){if(this.i<this.tokens.length)return this.tokens[this.i].charPositionInLine;if(null!=this.eofToken)return this.eofToken.charPositionInLine;if(this.tokens.length>0){let t=this.tokens[this.tokens.length-1],e=t.text;if(null!=e){let t=e.lastIndexOf("\n");if(t>=0)return e.length-t-1}return t.charPositionInLine+t.stopIndex-t.startIndex+1}return 0}nextToken(){if(this.i>=this.tokens.length){if(null==this.eofToken){let t=-1;if(this.tokens.length>0){let e=this.tokens[this.tokens.length-1].stopIndex;-1!==e&&(t=e+1)}let e=Math.max(-1,t-1);this.eofToken=this._factory.create({source:this,stream:this.inputStream},a.Token.EOF,"EOF",a.Token.DEFAULT_CHANNEL,t,e,this.line,this.charPositionInLine)}return this.eofToken}let t=this.tokens[this.i];return this.i===this.tokens.length-1&&t.type===a.Token.EOF&&(this.eofToken=t),this.i++,t}get line(){if(this.i<this.tokens.length)return this.tokens[this.i].line;if(null!=this.eofToken)return this.eofToken.line;if(this.tokens.length>0){let t=this.tokens[this.tokens.length-1],e=t.line,r=t.text;if(null!=r)for(let t=0;t<r.length;t++)"\n"===r.charAt(t)&&e++;return e}return 1}get inputStream(){return this.i<this.tokens.length?this.tokens[this.i].inputStream:null!=this.eofToken?this.eofToken.inputStream:this.tokens.length>0?this.tokens[this.tokens.length-1].inputStream:void 0}get sourceName(){if(this._sourceName)return this._sourceName;let t=this.inputStream;return null!=t?t.sourceName:"List"}set tokenFactory(t){this._factory=t}get tokenFactory(){return this._factory}};n([s.Override],l.prototype,"charPositionInLine",null),n([s.Override],l.prototype,"nextToken",null),n([s.Override],l.prototype,"line",null),n([s.Override],l.prototype,"inputStream",null),n([s.Override],l.prototype,"sourceName",null),n([s.Override,s.NotNull,o(0,s.NotNull)],l.prototype,"tokenFactory",null),l=n([o(0,s.NotNull)],l),e.ListTokenSource=l},4609:function(t,e,r){"use strict";var n=this&&this.__decorate||function(t,e,r,n){var o,i=arguments.length,s=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,r,n);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(i<3?o(s):i>3?o(e,r,s):o(e,r))||s);return i>3&&s&&Object.defineProperty(e,r,s),s};Object.defineProperty(e,"__esModule",{value:!0}),e.NoViableAltException=void 0;const o=r(2824),i=r(3998),s=r(8042);class a extends i.RecognitionException{constructor(t,e,r,n,i,s){t instanceof o.Parser&&(void 0===e&&(e=t.inputStream),void 0===r&&(r=t.currentToken),void 0===n&&(n=t.currentToken),void 0===s&&(s=t.context)),super(t,e,s),this._deadEndConfigs=i,this._startToken=r,this.setOffendingToken(t,n)}get startToken(){return this._startToken}get deadEndConfigs(){return this._deadEndConfigs}}n([s.NotNull],a.prototype,"_startToken",void 0),e.NoViableAltException=a},2824:function(t,e,r){"use strict";var n=this&&this.__decorate||function(t,e,r,n){var o,i=arguments.length,s=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,r,n);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(i<3?o(s):i>3?o(e,r,s):o(e,r))||s);return i>3&&s&&Object.defineProperty(e,r,s),s},o=this&&this.__param||function(t,e){return function(r,n){e(r,n,t)}},i=this&&this.__awaiter||function(t,e,r,n){return new(r||(r=Promise))((function(o,i){function s(t){try{l(n.next(t))}catch(t){i(t)}}function a(t){try{l(n.throw(t))}catch(t){i(t)}}function l(t){var e;t.done?o(t.value):(e=t.value,e instanceof r?e:new r((function(t){t(e)}))).then(s,a)}l((n=n.apply(t,e||[])).next())}))};Object.defineProperty(e,"__esModule",{value:!0}),e.Parser=void 0;const s=r(5103),a=r(9704),l=r(9963),u=r(3992),c=r(6912),h=r(1350),p=r(9557),f=r(8042),d=r(6019),y=r(4584),g=r(6454),_=r(8610),m=r(8011),v=r(4966);class S{constructor(t,e){this.ruleNames=t,this.tokenStream=e}enterEveryRule(t){console.log("enter   "+this.ruleNames[t.ruleIndex]+", LT(1)="+this.tokenStream.LT(1).text)}exitEveryRule(t){console.log("exit    "+this.ruleNames[t.ruleIndex]+", LT(1)="+this.tokenStream.LT(1).text)}visitErrorNode(t){}visitTerminal(t){let e=t.parent.ruleContext,r=t.symbol;console.log("consume "+r+" rule "+this.ruleNames[e.ruleIndex])}}n([f.Override],S.prototype,"enterEveryRule",null),n([f.Override],S.prototype,"exitEveryRule",null),n([f.Override],S.prototype,"visitErrorNode",null),n([f.Override],S.prototype,"visitTerminal",null);class N extends _.Recognizer{constructor(t){super(),this._errHandler=new u.DefaultErrorStrategy,this._precedenceStack=new h.IntegerStack,this._buildParseTrees=!0,this._parseListeners=[],this._syntaxErrors=0,this.matchedEOF=!1,this._precedenceStack.push(0),this.inputStream=t}reset(t){(void 0===t||t)&&this.inputStream.seek(0),this._errHandler.reset(this),this._ctx=void 0,this._syntaxErrors=0,this.matchedEOF=!1,this.isTrace=!1,this._precedenceStack.clear(),this._precedenceStack.push(0);let e=this.interpreter;null!=e&&e.reset()}match(t){let e=this.currentToken;return e.type===t?(t===v.Token.EOF&&(this.matchedEOF=!0),this._errHandler.reportMatch(this),this.consume()):(e=this._errHandler.recoverInline(this),this._buildParseTrees&&-1===e.tokenIndex&&this._ctx.addErrorNode(this.createErrorNode(this._ctx,e))),e}matchWildcard(){let t=this.currentToken;return t.type>0?(this._errHandler.reportMatch(this),this.consume()):(t=this._errHandler.recoverInline(this),this._buildParseTrees&&-1===t.tokenIndex&&this._ctx.addErrorNode(this.createErrorNode(this._ctx,t))),t}set buildParseTree(t){this._buildParseTrees=t}get buildParseTree(){return this._buildParseTrees}getParseListeners(){return this._parseListeners}addParseListener(t){if(null==t)throw new TypeError("listener cannot be null");this._parseListeners.push(t)}removeParseListener(t){let e=this._parseListeners.findIndex((e=>e===t));-1!==e&&this._parseListeners.splice(e,1)}removeParseListeners(){this._parseListeners.length=0}triggerEnterRuleEvent(){for(let t of this._parseListeners)t.enterEveryRule&&t.enterEveryRule(this._ctx),this._ctx.enterRule(t)}triggerExitRuleEvent(){for(let t=this._parseListeners.length-1;t>=0;t--){let e=this._parseListeners[t];this._ctx.exitRule(e),e.exitEveryRule&&e.exitEveryRule(this._ctx)}}get numberOfSyntaxErrors(){return this._syntaxErrors}get tokenFactory(){return this._input.tokenSource.tokenFactory}getATNWithBypassAlts(){let t=this.serializedATN;if(null==t)throw new Error("The current parser does not support an ATN with bypass alternatives.");let e=N.bypassAltsAtnCache.get(t);if(null==e){let r=new a.ATNDeserializationOptions;r.isGenerateRuleBypassTransitions=!0,e=new l.ATNDeserializer(r).deserialize(s.toCharArray(t)),N.bypassAltsAtnCache.set(t,e)}return e}compileParseTreePattern(t,e,n){return i(this,void 0,void 0,(function*(){if(!n){if(this.inputStream){let t=this.inputStream.tokenSource;t instanceof p.Lexer&&(n=t)}if(!n)throw new Error("Parser can't discover a lexer to use")}let o=n;return new((yield Promise.resolve().then((()=>r(1293)))).ParseTreePatternMatcher)(o,this).compile(t,e)}))}get errorHandler(){return this._errHandler}set errorHandler(t){this._errHandler=t}get inputStream(){return this._input}set inputStream(t){this.reset(!1),this._input=t}get currentToken(){return this._input.LT(1)}notifyErrorListeners(t,e,r){void 0===e?e=this.currentToken:null===e&&(e=void 0),this._syntaxErrors++;let n=-1,o=-1;null!=e&&(n=e.line,o=e.charPositionInLine);let i=this.getErrorListenerDispatch();i.syntaxError&&i.syntaxError(this,e,n,o,t,r)}consume(){let t=this.currentToken;t.type!==N.EOF&&this.inputStream.consume();let e=0!==this._parseListeners.length;if(this._buildParseTrees||e)if(this._errHandler.inErrorRecoveryMode(this)){let r=this._ctx.addErrorNode(this.createErrorNode(this._ctx,t));if(e)for(let t of this._parseListeners)t.visitErrorNode&&t.visitErrorNode(r)}else{let r=this.createTerminalNode(this._ctx,t);if(this._ctx.addChild(r),e)for(let t of this._parseListeners)t.visitTerminal&&t.visitTerminal(r)}return t}createTerminalNode(t,e){return new m.TerminalNode(e)}createErrorNode(t,e){return new c.ErrorNode(e)}addContextToParseTree(){let t=this._ctx._parent;null!=t&&t.addChild(this._ctx)}enterRule(t,e,r){this.state=e,this._ctx=t,this._ctx._start=this._input.LT(1),this._buildParseTrees&&this.addContextToParseTree(),this.triggerEnterRuleEvent()}enterLeftFactoredRule(t,e,r){if(this.state=e,this._buildParseTrees){let e=this._ctx.getChild(this._ctx.childCount-1);this._ctx.removeLastChild(),e._parent=t,t.addChild(e)}this._ctx=t,this._ctx._start=this._input.LT(1),this._buildParseTrees&&this.addContextToParseTree(),this.triggerEnterRuleEvent()}exitRule(){this.matchedEOF?this._ctx._stop=this._input.LT(1):this._ctx._stop=this._input.tryLT(-1),this.triggerExitRuleEvent(),this.state=this._ctx.invokingState,this._ctx=this._ctx._parent}enterOuterAlt(t,e){if(t.altNumber=e,this._buildParseTrees&&this._ctx!==t){let e=this._ctx._parent;null!=e&&(e.removeLastChild(),e.addChild(t))}this._ctx=t}get precedence(){return this._precedenceStack.isEmpty?-1:this._precedenceStack.peek()}enterRecursionRule(t,e,r,n){this.state=e,this._precedenceStack.push(n),this._ctx=t,this._ctx._start=this._input.LT(1),this.triggerEnterRuleEvent()}pushNewRecursionContext(t,e,r){let n=this._ctx;n._parent=t,n.invokingState=e,n._stop=this._input.tryLT(-1),this._ctx=t,this._ctx._start=n._start,this._buildParseTrees&&this._ctx.addChild(n),this.triggerEnterRuleEvent()}unrollRecursionContexts(t){this._precedenceStack.pop(),this._ctx._stop=this._input.tryLT(-1);let e=this._ctx;if(this._parseListeners.length>0)for(;this._ctx!==t;)this.triggerExitRuleEvent(),this._ctx=this._ctx._parent;else this._ctx=t;e._parent=t,this._buildParseTrees&&null!=t&&t.addChild(e)}getInvokingContext(t){let e=this._ctx;for(;e&&e.ruleIndex!==t;)e=e._parent;return e}get context(){return this._ctx}set context(t){this._ctx=t}precpred(t,e){return e>=this._precedenceStack.peek()}getErrorListenerDispatch(){return new g.ProxyParserErrorListener(this.getErrorListeners())}inContext(t){return!1}isExpectedToken(t){let e=this.interpreter.atn,r=this._ctx,n=e.states[this.state],o=e.nextTokens(n);if(o.contains(t))return!0;if(!o.contains(v.Token.EPSILON))return!1;for(;null!=r&&r.invokingState>=0&&o.contains(v.Token.EPSILON);){let n=e.states[r.invokingState].transition(0);if(o=e.nextTokens(n.followState),o.contains(t))return!0;r=r._parent}return!(!o.contains(v.Token.EPSILON)||t!==v.Token.EOF)}get isMatchedEOF(){return this.matchedEOF}getExpectedTokens(){return this.atn.getExpectedTokens(this.state,this.context)}getExpectedTokensWithinCurrentRule(){let t=this.interpreter.atn,e=t.states[this.state];return t.nextTokens(e)}getRuleIndex(t){let e=this.getRuleIndexMap().get(t);return null!=e?e:-1}get ruleContext(){return this._ctx}getRuleInvocationStack(t=this._ctx){let e=t,r=this.ruleNames,n=[];for(;null!=e;){let t=e.ruleIndex;t<0?n.push("n/a"):n.push(r[t]),e=e._parent}return n}getDFAStrings(){let t=[];for(let e of this._interp.atn.decisionToDFA)t.push(e.toString(this.vocabulary,this.ruleNames));return t}dumpDFA(){let t=!1;for(let e of this._interp.atn.decisionToDFA)e.isEmpty||(t&&console.log(),console.log("Decision "+e.decision+":"),process.stdout.write(e.toString(this.vocabulary,this.ruleNames)),t=!0)}get sourceName(){return this._input.sourceName}get parseInfo(){return Promise.resolve().then((()=>r(2527))).then((t=>{let e=this.interpreter;if(e instanceof t.ProfilingATNSimulator)return new d.ParseInfo(e)}))}setProfile(t){return i(this,void 0,void 0,(function*(){let e=yield Promise.resolve().then((()=>r(2527))),n=this.interpreter;t?n instanceof e.ProfilingATNSimulator||(this.interpreter=new e.ProfilingATNSimulator(this)):n instanceof e.ProfilingATNSimulator&&(this.interpreter=new y.ParserATNSimulator(this.atn,this)),this.interpreter.setPredictionMode(n.getPredictionMode())}))}set isTrace(t){t?(this._tracer?this.removeParseListener(this._tracer):this._tracer=new S(this.ruleNames,this._input),this.addParseListener(this._tracer)):this._tracer&&(this.removeParseListener(this._tracer),this._tracer=void 0)}get isTrace(){return null!=this._tracer}}N.bypassAltsAtnCache=new Map,n([f.NotNull],N.prototype,"_errHandler",void 0),n([f.NotNull],N.prototype,"match",null),n([f.NotNull],N.prototype,"matchWildcard",null),n([f.NotNull],N.prototype,"getParseListeners",null),n([o(0,f.NotNull)],N.prototype,"addParseListener",null),n([f.NotNull],N.prototype,"getATNWithBypassAlts",null),n([f.NotNull,o(0,f.NotNull)],N.prototype,"errorHandler",null),n([f.Override],N.prototype,"inputStream",null),n([f.NotNull],N.prototype,"currentToken",null),n([o(0,f.NotNull)],N.prototype,"enterRule",null),n([f.Override,o(0,f.Nullable)],N.prototype,"precpred",null),n([f.Override],N.prototype,"getErrorListenerDispatch",null),n([f.NotNull],N.prototype,"getExpectedTokens",null),n([f.NotNull],N.prototype,"getExpectedTokensWithinCurrentRule",null),n([f.Override],N.prototype,"parseInfo",null),e.Parser=N},1603:(t,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0})},627:function(t,e,r){"use strict";var n=this&&this.__decorate||function(t,e,r,n){var o,i=arguments.length,s=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,r,n);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(i<3?o(s):i>3?o(e,r,s):o(e,r))||s);return i>3&&s&&Object.defineProperty(e,r,s),s},o=this&&this.__param||function(t,e){return function(r,n){e(r,n,t)}};Object.defineProperty(e,"__esModule",{value:!0}),e.ParserInterpreter=void 0;const i=r(3269),s=r(4700),a=r(5280),l=r(2915),u=r(4837),c=r(4126),h=r(4068),p=r(8042),f=r(8042),d=r(2824),y=r(4584),g=r(3998),_=r(7165),m=r(4966);let v=class t extends d.Parser{constructor(e,r,n,o,i){if(super(e instanceof t?e.inputStream:i),this._parentContextStack=[],this.overrideDecision=-1,this.overrideDecisionInputIndex=-1,this.overrideDecisionAlt=-1,this.overrideDecisionReached=!1,this._overrideDecisionRoot=void 0,e instanceof t){let t=e;this._grammarFileName=t._grammarFileName,this._atn=t._atn,this.pushRecursionContextStates=t.pushRecursionContextStates,this._ruleNames=t._ruleNames,this._vocabulary=t._vocabulary,this.interpreter=new y.ParserATNSimulator(this._atn,this)}else{r=r,n=n,o=o,this._grammarFileName=e,this._atn=o,this._ruleNames=n.slice(0),this._vocabulary=r,this.pushRecursionContextStates=new a.BitSet(o.states.length);for(let t of o.states)t instanceof _.StarLoopEntryState&&t.precedenceRuleDecision&&this.pushRecursionContextStates.set(t.stateNumber);this.interpreter=new y.ParserATNSimulator(o,this)}}reset(t){void 0===t?super.reset():super.reset(t),this.overrideDecisionReached=!1,this._overrideDecisionRoot=void 0}get atn(){return this._atn}get vocabulary(){return this._vocabulary}get ruleNames(){return this._ruleNames}get grammarFileName(){return this._grammarFileName}parse(t){let e=this._atn.ruleToStartState[t];for(this._rootContext=this.createInterpreterRuleContext(void 0,i.ATNState.INVALID_STATE_NUMBER,t),e.isPrecedenceRule?this.enterRecursionRule(this._rootContext,e.stateNumber,t,0):this.enterRule(this._rootContext,e.stateNumber,t);;){let t=this.atnState;switch(t.stateType){case s.ATNStateType.RULE_STOP:if(this._ctx.isEmpty){if(e.isPrecedenceRule){let t=this._ctx,e=this._parentContextStack.pop();return this.unrollRecursionContexts(e[0]),t}return this.exitRule(),this._rootContext}this.visitRuleStopState(t);break;default:try{this.visitState(t)}catch(e){if(!(e instanceof g.RecognitionException))throw e;this.state=this._atn.ruleToStopState[t.ruleIndex].stateNumber,this.context.exception=e,this.errorHandler.reportError(this,e),this.recover(e)}}}}enterRecursionRule(t,e,r,n){this._parentContextStack.push([this._ctx,t.invokingState]),super.enterRecursionRule(t,e,r,n)}get atnState(){return this._atn.states[this.state]}visitState(t){let e=1;t.numberOfTransitions>1&&(e=this.visitDecisionState(t));let r=t.transition(e-1);switch(r.serializationType){case 1:if(this.pushRecursionContextStates.get(t.stateNumber)&&!(r.target instanceof h.LoopEndState)){let e=this._parentContextStack[this._parentContextStack.length-1],r=this.createInterpreterRuleContext(e[0],e[1],this._ctx.ruleIndex);this.pushNewRecursionContext(r,this._atn.ruleToStartState[t.ruleIndex].stateNumber,this._ctx.ruleIndex)}break;case 5:this.match(r._label);break;case 2:case 7:case 8:r.matches(this._input.LA(1),m.Token.MIN_USER_TOKEN_TYPE,65535)||this.recoverInline(),this.matchWildcard();break;case 9:this.matchWildcard();break;case 3:let e=r.target,n=e.ruleIndex,o=this.createInterpreterRuleContext(this._ctx,t.stateNumber,n);e.isPrecedenceRule?this.enterRecursionRule(o,e.stateNumber,n,r.precedence):this.enterRule(o,r.target.stateNumber,n);break;case 4:let i=r;if(!this.sempred(this._ctx,i.ruleIndex,i.predIndex))throw new l.FailedPredicateException(this);break;case 6:let s=r;this.action(this._ctx,s.ruleIndex,s.actionIndex);break;case 10:if(!this.precpred(this._ctx,r.precedence)){let t=r.precedence;throw new l.FailedPredicateException(this,`precpred(_ctx, ${t})`)}break;default:throw new Error("UnsupportedOperationException: Unrecognized ATN transition type.")}this.state=r.target.stateNumber}visitDecisionState(t){let e;this.errorHandler.sync(this);let r=t.decision;return r!==this.overrideDecision||this._input.index!==this.overrideDecisionInputIndex||this.overrideDecisionReached?e=this.interpreter.adaptivePredict(this._input,r,this._ctx):(e=this.overrideDecisionAlt,this.overrideDecisionReached=!0),e}createInterpreterRuleContext(t,e,r){return new c.InterpreterRuleContext(r,t,e)}visitRuleStopState(t){if(this._atn.ruleToStartState[t.ruleIndex].isPrecedenceRule){let t=this._parentContextStack.pop();this.unrollRecursionContexts(t[0]),this.state=t[1]}else this.exitRule();let e=this._atn.states[this.state].transition(0);this.state=e.followState.stateNumber}addDecisionOverride(t,e,r){this.overrideDecision=t,this.overrideDecisionInputIndex=e,this.overrideDecisionAlt=r}get overrideDecisionRoot(){return this._overrideDecisionRoot}recover(t){let e=this._input.index;if(this.errorHandler.recover(this,t),this._input.index===e){let e=t.getOffendingToken();if(!e)throw new Error("Expected exception to have an offending token");let r=e.tokenSource,n={source:r,stream:void 0!==r?r.inputStream:void 0};if(t instanceof u.InputMismatchException){let r=t.expectedTokens;if(void 0===r)throw new Error("Expected the exception to provide expected tokens");let o=m.Token.INVALID_TYPE;r.isNil||(o=r.minElement);let i=this.tokenFactory.create(n,o,e.text,m.Token.DEFAULT_CHANNEL,-1,-1,e.line,e.charPositionInLine);this._ctx.addErrorNode(this.createErrorNode(this._ctx,i))}else{e.tokenSource;let t=this.tokenFactory.create(n,m.Token.INVALID_TYPE,e.text,m.Token.DEFAULT_CHANNEL,-1,-1,e.line,e.charPositionInLine);this._ctx.addErrorNode(this.createErrorNode(this._ctx,t))}}}recoverInline(){return this._errHandler.recoverInline(this)}get rootContext(){return this._rootContext}};n([p.NotNull],v.prototype,"_vocabulary",void 0),n([f.Override],v.prototype,"reset",null),n([f.Override],v.prototype,"atn",null),n([f.Override],v.prototype,"vocabulary",null),n([f.Override],v.prototype,"ruleNames",null),n([f.Override],v.prototype,"grammarFileName",null),n([f.Override],v.prototype,"enterRecursionRule",null),v=n([o(1,p.NotNull)],v),e.ParserInterpreter=v},3208:function(t,e,r){"use strict";var n=this&&this.__decorate||function(t,e,r,n){var o,i=arguments.length,s=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,r,n);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(i<3?o(s):i>3?o(e,r,s):o(e,r))||s);return i>3&&s&&Object.defineProperty(e,r,s),s};Object.defineProperty(e,"__esModule",{value:!0}),e.ParserRuleContext=void 0;const o=r(6912),i=r(8813),s=r(8042),a=r(7423),l=r(8011);class u extends a.RuleContext{constructor(t,e){null==e?super():super(t,e)}static emptyContext(){return u.EMPTY}copyFrom(t){if(this._parent=t._parent,this.invokingState=t.invokingState,this._start=t._start,this._stop=t._stop,t.children){this.children=[];for(let e of t.children)e instanceof o.ErrorNode&&this.addChild(e)}}enterRule(t){}exitRule(t){}addAnyChild(t){return this.children?this.children.push(t):this.children=[t],t}addChild(t){return t instanceof l.TerminalNode?(t.setParent(this),void this.addAnyChild(t)):t instanceof a.RuleContext?void this.addAnyChild(t):(t=new l.TerminalNode(t),this.addAnyChild(t),t.setParent(this),t)}addErrorNode(t){if(t instanceof o.ErrorNode){const e=t;return e.setParent(this),this.addAnyChild(e)}{const e=t;let r=new o.ErrorNode(e);return this.addAnyChild(r),r.setParent(this),r}}removeLastChild(){this.children&&this.children.pop()}get parent(){let t=super.parent;if(void 0===t||t instanceof u)return t;throw new TypeError("Invalid parent type for ParserRuleContext")}getChild(t,e){if(!this.children||t<0||t>=this.children.length)throw new RangeError("index parameter must be between >= 0 and <= number of children.");if(null==e)return this.children[t];let r=this.tryGetChild(t,e);if(void 0===r)throw new Error("The specified node does not exist");return r}tryGetChild(t,e){if(!this.children||t<0||t>=this.children.length)return;let r=-1;for(let n of this.children)if(n instanceof e&&(r++,r===t))return n}getToken(t,e){let r=this.tryGetToken(t,e);if(void 0===r)throw new Error("The specified token does not exist");return r}tryGetToken(t,e){if(!this.children||e<0||e>=this.children.length)return;let r=-1;for(let n of this.children)if(n instanceof l.TerminalNode){if(n.symbol.type===t&&(r++,r===e))return n}}getTokens(t){let e=[];if(!this.children)return e;for(let r of this.children)if(r instanceof l.TerminalNode){r.symbol.type===t&&e.push(r)}return e}get ruleContext(){return this}getRuleContext(t,e){return this.getChild(t,e)}tryGetRuleContext(t,e){return this.tryGetChild(t,e)}getRuleContexts(t){let e=[];if(!this.children)return e;for(let r of this.children)r instanceof t&&e.push(r);return e}get childCount(){return this.children?this.children.length:0}get sourceInterval(){return this._start?!this._stop||this._stop.tokenIndex<this._start.tokenIndex?i.Interval.of(this._start.tokenIndex,this._start.tokenIndex-1):i.Interval.of(this._start.tokenIndex,this._stop.tokenIndex):i.Interval.INVALID}get start(){return this._start}get stop(){return this._stop}toInfoString(t){return"ParserRuleContext"+t.getRuleInvocationStack(this).reverse()+"{start="+this._start+", stop="+this._stop+"}"}}u.EMPTY=new u,n([s.Override],u.prototype,"parent",null),n([s.Override],u.prototype,"childCount",null),n([s.Override],u.prototype,"sourceInterval",null),e.ParserRuleContext=u},9583:function(t,e,r){"use strict";var n=this&&this.__decorate||function(t,e,r,n){var o,i=arguments.length,s=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,r,n);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(i<3?o(s):i>3?o(e,r,s):o(e,r))||s);return i>3&&s&&Object.defineProperty(e,r,s),s},o=this&&this.__param||function(t,e){return function(r,n){e(r,n,t)}};Object.defineProperty(e,"__esModule",{value:!0}),e.ProxyErrorListener=void 0;const i=r(8042);class s{constructor(t){if(this.delegates=t,!t)throw new Error("Invalid delegates")}getDelegates(){return this.delegates}syntaxError(t,e,r,n,o,i){this.delegates.forEach((s=>{s.syntaxError&&s.syntaxError(t,e,r,n,o,i)}))}}n([i.Override,o(0,i.NotNull),o(4,i.NotNull)],s.prototype,"syntaxError",null),e.ProxyErrorListener=s},6454:function(t,e,r){"use strict";var n=this&&this.__decorate||function(t,e,r,n){var o,i=arguments.length,s=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,r,n);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(i<3?o(s):i>3?o(e,r,s):o(e,r))||s);return i>3&&s&&Object.defineProperty(e,r,s),s};Object.defineProperty(e,"__esModule",{value:!0}),e.ProxyParserErrorListener=void 0;const o=r(9583),i=r(8042);class s extends o.ProxyErrorListener{constructor(t){super(t)}reportAmbiguity(t,e,r,n,o,i,s){this.getDelegates().forEach((a=>{a.reportAmbiguity&&a.reportAmbiguity(t,e,r,n,o,i,s)}))}reportAttemptingFullContext(t,e,r,n,o,i){this.getDelegates().forEach((s=>{s.reportAttemptingFullContext&&s.reportAttemptingFullContext(t,e,r,n,o,i)}))}reportContextSensitivity(t,e,r,n,o,i){this.getDelegates().forEach((s=>{s.reportContextSensitivity&&s.reportContextSensitivity(t,e,r,n,o,i)}))}}n([i.Override],s.prototype,"reportAmbiguity",null),n([i.Override],s.prototype,"reportAttemptingFullContext",null),n([i.Override],s.prototype,"reportContextSensitivity",null),e.ProxyParserErrorListener=s},3998:(t,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.RecognitionException=void 0;class r extends Error{constructor(t,e,r,n){super(n),this._offendingState=-1,this._recognizer=t,this.input=e,this.ctx=r,t&&(this._offendingState=t.state)}get offendingState(){return this._offendingState}setOffendingState(t){this._offendingState=t}get expectedTokens(){if(this._recognizer)return this._recognizer.atn.getExpectedTokens(this._offendingState,this.ctx)}get context(){return this.ctx}get inputStream(){return this.input}getOffendingToken(t){if(!t||t===this._recognizer)return this.offendingToken}setOffendingToken(t,e){t===this._recognizer&&(this.offendingToken=e)}get recognizer(){return this._recognizer}}e.RecognitionException=r},8610:function(t,e,r){"use strict";var n=this&&this.__decorate||function(t,e,r,n){var o,i=arguments.length,s=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,r,n);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(i<3?o(s):i>3?o(e,r,s):o(e,r))||s);return i>3&&s&&Object.defineProperty(e,r,s),s},o=this&&this.__param||function(t,e){return function(r,n){e(r,n,t)}};Object.defineProperty(e,"__esModule",{value:!0}),e.Recognizer=void 0;const i=r(4525),s=r(9583),a=r(8042),l=r(4966),u=r(5103);class c{constructor(){this._listeners=[i.ConsoleErrorListener.INSTANCE],this._stateNumber=-1}getTokenTypeMap(){let t=this.vocabulary,e=c.tokenTypeMapCache.get(t);if(null==e){let r=new Map;for(let e=0;e<=this.atn.maxTokenType;e++){let n=t.getLiteralName(e);null!=n&&r.set(n,e);let o=t.getSymbolicName(e);null!=o&&r.set(o,e)}r.set("EOF",l.Token.EOF),e=r,c.tokenTypeMapCache.set(t,e)}return e}getRuleIndexMap(){let t=this.ruleNames;if(null==t)throw new Error("The current recognizer does not provide a list of rule names.");let e=c.ruleIndexMapCache.get(t);return null==e&&(e=u.toMap(t),c.ruleIndexMapCache.set(t,e)),e}getTokenType(t){let e=this.getTokenTypeMap().get(t);return null!=e?e:l.Token.INVALID_TYPE}get serializedATN(){throw new Error("there is no serialized ATN")}get atn(){return this._interp.atn}get interpreter(){return this._interp}set interpreter(t){this._interp=t}get parseInfo(){return Promise.resolve(void 0)}getErrorHeader(t){let e=t.getOffendingToken();return e?"line "+e.line+":"+e.charPositionInLine:""}addErrorListener(t){if(!t)throw new TypeError("listener must not be null");this._listeners.push(t)}removeErrorListener(t){let e=this._listeners.indexOf(t);-1!==e&&this._listeners.splice(e,1)}removeErrorListeners(){this._listeners.length=0}getErrorListeners(){return this._listeners.slice(0)}getErrorListenerDispatch(){return new s.ProxyErrorListener(this.getErrorListeners())}sempred(t,e,r){return!0}precpred(t,e){return!0}action(t,e,r){}get state(){return this._stateNumber}set state(t){this._stateNumber=t}}c.EOF=-1,c.tokenTypeMapCache=new WeakMap,c.ruleIndexMapCache=new WeakMap,n([a.SuppressWarnings("serial"),a.NotNull],c.prototype,"_listeners",void 0),n([a.NotNull],c.prototype,"getTokenTypeMap",null),n([a.NotNull],c.prototype,"getRuleIndexMap",null),n([a.NotNull],c.prototype,"serializedATN",null),n([a.NotNull],c.prototype,"atn",null),n([a.NotNull,o(0,a.NotNull)],c.prototype,"interpreter",null),n([a.NotNull,o(0,a.NotNull)],c.prototype,"getErrorHeader",null),n([o(0,a.NotNull)],c.prototype,"addErrorListener",null),n([o(0,a.NotNull)],c.prototype,"removeErrorListener",null),n([a.NotNull],c.prototype,"getErrorListeners",null),e.Recognizer=c},7423:function(t,e,r){"use strict";var n=this&&this.__decorate||function(t,e,r,n){var o,i=arguments.length,s=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,r,n);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(i<3?o(s):i>3?o(e,r,s):o(e,r))||s);return i>3&&s&&Object.defineProperty(e,r,s),s};Object.defineProperty(e,"__esModule",{value:!0}),e.RuleContext=void 0;const o=r(7949),i=r(8610),s=r(3627),a=r(8813),l=r(8042),u=r(5194),c=r(3208);class h extends s.RuleNode{constructor(t,e){super(),this._parent=t,this.invokingState=null!=e?e:-1}static getChildContext(t,e){return new h(t,e)}depth(){let t=0,e=this;for(;e;)e=e._parent,t++;return t}get isEmpty(){return-1===this.invokingState}get sourceInterval(){return a.Interval.INVALID}get ruleContext(){return this}get parent(){return this._parent}setParent(t){this._parent=t}get payload(){return this}get text(){if(0===this.childCount)return"";let t="";for(let e=0;e<this.childCount;e++)t+=this.getChild(e).text;return t.toString()}get ruleIndex(){return-1}get altNumber(){return o.ATN.INVALID_ALT_NUMBER}set altNumber(t){}getChild(t){throw new RangeError("i must be greater than or equal to 0 and less than childCount")}get childCount(){return 0}accept(t){return t.visitChildren(this)}toStringTree(t){return u.Trees.toStringTree(this,t)}toString(t,e){const r=t instanceof i.Recognizer?t.ruleNames:t;e=e||c.ParserRuleContext.emptyContext();let n="",o=this;for(n+="[";o&&o!==e;){if(r){let t=o.ruleIndex;n+=t>=0&&t<r.length?r[t]:t.toString()}else o.isEmpty||(n+=o.invokingState);!o._parent||!r&&o._parent.isEmpty||(n+=" "),o=o._parent}return n+="]",n.toString()}}n([l.Override],h.prototype,"sourceInterval",null),n([l.Override],h.prototype,"ruleContext",null),n([l.Override],h.prototype,"parent",null),n([l.Override],h.prototype,"setParent",null),n([l.Override],h.prototype,"payload",null),n([l.Override],h.prototype,"text",null),n([l.Override],h.prototype,"getChild",null),n([l.Override],h.prototype,"childCount",null),n([l.Override],h.prototype,"accept",null),n([l.Override],h.prototype,"toStringTree",null),e.RuleContext=h},6599:function(t,e,r){"use strict";var n=this&&this.__decorate||function(t,e,r,n){var o,i=arguments.length,s=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,r,n);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(i<3?o(s):i>3?o(e,r,s):o(e,r))||s);return i>3&&s&&Object.defineProperty(e,r,s),s};Object.defineProperty(e,"__esModule",{value:!0}),e.RuleContextWithAltNum=void 0;const o=r(7949),i=r(8042),s=r(3208);class a extends s.ParserRuleContext{constructor(t,e){void 0!==e?super(t,e):super(),this._altNumber=o.ATN.INVALID_ALT_NUMBER}get altNumber(){return this._altNumber}set altNumber(t){this._altNumber=t}}n([i.Override],a.prototype,"altNumber",null),e.RuleContextWithAltNum=a},3252:(t,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.RuleDependency=void 0,e.RuleDependency=function(t){return(t,e,r)=>{}}},345:(t,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.RuleVersion=void 0,e.RuleVersion=function(t){return(t,e,r)=>{}}},4966:(t,e,r)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.Token=void 0;const n=r(3227);!function(t){t.INVALID_TYPE=0,t.EPSILON=-2,t.MIN_USER_TOKEN_TYPE=1,t.EOF=n.IntStream.EOF,t.DEFAULT_CHANNEL=0,t.HIDDEN_CHANNEL=1,t.MIN_USER_CHANNEL_VALUE=2}(e.Token||(e.Token={}))},2362:(t,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0})},9089:(t,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0})},9293:(t,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0})},929:function(t,e,r){"use strict";var n=this&&this.__decorate||function(t,e,r,n){var o,i=arguments.length,s=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,r,n);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(i<3?o(s):i>3?o(e,r,s):o(e,r))||s);return i>3&&s&&Object.defineProperty(e,r,s),s};Object.defineProperty(e,"__esModule",{value:!0}),e.RewriteOperation=e.TokenStreamRewriter=void 0;const o=r(8813),i=r(8042),s=r(4966);class a{constructor(t){this.tokens=t,this.programs=new Map,this.programs.set(a.DEFAULT_PROGRAM_NAME,[]),this.lastRewriteTokenIndexes=new Map}getTokenStream(){return this.tokens}rollback(t,e=a.DEFAULT_PROGRAM_NAME){let r=this.programs.get(e);null!=r&&this.programs.set(e,r.slice(a.MIN_TOKEN_INDEX,t))}deleteProgram(t=a.DEFAULT_PROGRAM_NAME){this.rollback(a.MIN_TOKEN_INDEX,t)}insertAfter(t,e,r=a.DEFAULT_PROGRAM_NAME){let n;n="number"==typeof t?t:t.tokenIndex;let o=this.getProgram(r),i=new c(this.tokens,n,o.length,e);o.push(i)}insertBefore(t,e,r=a.DEFAULT_PROGRAM_NAME){let n;n="number"==typeof t?t:t.tokenIndex;let o=this.getProgram(r),i=new u(this.tokens,n,o.length,e);o.push(i)}replaceSingle(t,e){this.replace(t,t,e)}replace(t,e,r,n=a.DEFAULT_PROGRAM_NAME){if("number"!=typeof t&&(t=t.tokenIndex),"number"!=typeof e&&(e=e.tokenIndex),t>e||t<0||e<0||e>=this.tokens.size)throw new RangeError(`replace: range invalid: ${t}..${e}(size=${this.tokens.size})`);let o=this.getProgram(n),i=new h(this.tokens,t,e,o.length,r);o.push(i)}delete(t,e,r=a.DEFAULT_PROGRAM_NAME){void 0===e&&(e=t),this.replace(t,e,"",r)}getLastRewriteTokenIndex(t=a.DEFAULT_PROGRAM_NAME){let e=this.lastRewriteTokenIndexes.get(t);return null==e?-1:e}setLastRewriteTokenIndex(t,e){this.lastRewriteTokenIndexes.set(t,e)}getProgram(t){let e=this.programs.get(t);return null==e&&(e=this.initializeProgram(t)),e}initializeProgram(t){let e=[];return this.programs.set(t,e),e}getText(t,e=a.DEFAULT_PROGRAM_NAME){let r;r=t instanceof o.Interval?t:o.Interval.of(0,this.tokens.size-1),"string"==typeof t&&(e=t);let n=this.programs.get(e),i=r.a,l=r.b;if(l>this.tokens.size-1&&(l=this.tokens.size-1),i<0&&(i=0),null==n||0===n.length)return this.tokens.getText(r);let u=[],c=this.reduceToSingleOperationPerIndex(n),h=i;for(;h<=l&&h<this.tokens.size;){let t=c.get(h);c.delete(h);let e=this.tokens.get(h);null==t?(e.type!==s.Token.EOF&&u.push(String(e.text)),h++):h=t.execute(u)}if(l===this.tokens.size-1)for(let t of c.values())t.index>=this.tokens.size-1&&u.push(t.text.toString());return u.join("")}reduceToSingleOperationPerIndex(t){for(let e=0;e<t.length;e++){let r=t[e];if(null==r)continue;if(!(r instanceof h))continue;let n=r,o=this.getKindOfOps(t,u,e);for(let e of o)e.index===n.index?(t[e.instructionIndex]=void 0,n.text=e.text.toString()+(null!=n.text?n.text.toString():"")):e.index>n.index&&e.index<=n.lastIndex&&(t[e.instructionIndex]=void 0);let i=this.getKindOfOps(t,h,e);for(let e of i){if(e.index>=n.index&&e.lastIndex<=n.lastIndex){t[e.instructionIndex]=void 0;continue}let r=e.lastIndex<n.index||e.index>n.lastIndex;if(null!=e.text||null!=n.text||r){if(!r)throw new Error(`replace op boundaries of ${n} overlap with previous ${e}`)}else t[e.instructionIndex]=void 0,n.index=Math.min(e.index,n.index),n.lastIndex=Math.max(e.lastIndex,n.lastIndex)}}for(let e=0;e<t.length;e++){let r=t[e];if(null==r)continue;if(!(r instanceof u))continue;let n=r,o=this.getKindOfOps(t,u,e);for(let e of o)e.index===n.index&&(e instanceof c?(n.text=this.catOpText(e.text,n.text),t[e.instructionIndex]=void 0):e instanceof u&&(n.text=this.catOpText(n.text,e.text),t[e.instructionIndex]=void 0));let i=this.getKindOfOps(t,h,e);for(let r of i)if(n.index!==r.index){if(n.index>=r.index&&n.index<=r.lastIndex)throw new Error(`insert op ${n} within boundaries of previous ${r}`)}else r.text=this.catOpText(n.text,r.text),t[e]=void 0}let e=new Map;for(let r of t)if(null!=r){if(null!=e.get(r.index))throw new Error("should only be one op per index");e.set(r.index,r)}return e}catOpText(t,e){let r="",n="";return null!=t&&(r=t.toString()),null!=e&&(n=e.toString()),r+n}getKindOfOps(t,e,r){let n=[];for(let o=0;o<r&&o<t.length;o++){let r=t[o];null!=r&&(r instanceof e&&n.push(r))}return n}}e.TokenStreamRewriter=a,a.DEFAULT_PROGRAM_NAME="default",a.PROGRAM_INIT_SIZE=100,a.MIN_TOKEN_INDEX=0;class l{constructor(t,e,r,n){this.tokens=t,this.instructionIndex=r,this.index=e,this.text=void 0===n?"":n}execute(t){return this.index}toString(){let t=this.constructor.name,e=t.indexOf("$");return t=t.substring(e+1,t.length),"<"+t+"@"+this.tokens.get(this.index)+':"'+this.text+'">'}}n([i.Override],l.prototype,"toString",null),e.RewriteOperation=l;class u extends l{constructor(t,e,r,n){super(t,e,r,n)}execute(t){return t.push(this.text.toString()),this.tokens.get(this.index).type!==s.Token.EOF&&t.push(String(this.tokens.get(this.index).text)),this.index+1}}n([i.Override],u.prototype,"execute",null);class c extends u{constructor(t,e,r,n){super(t,e+1,r,n)}}class h extends l{constructor(t,e,r,n,o){super(t,e,n,o),this.lastIndex=r}execute(t){return null!=this.text&&t.push(this.text.toString()),this.lastIndex+1}toString(){return null==this.text?"<DeleteOp@"+this.tokens.get(this.index)+".."+this.tokens.get(this.lastIndex)+">":"<ReplaceOp@"+this.tokens.get(this.index)+".."+this.tokens.get(this.lastIndex)+':"'+this.text+'">'}}n([i.Override],h.prototype,"execute",null),n([i.Override],h.prototype,"toString",null)},2499:(t,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0})},6763:function(t,e,r){"use strict";var n=this&&this.__decorate||function(t,e,r,n){var o,i=arguments.length,s=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,r,n);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(i<3?o(s):i>3?o(e,r,s):o(e,r))||s);return i>3&&s&&Object.defineProperty(e,r,s),s};Object.defineProperty(e,"__esModule",{value:!0}),e.VocabularyImpl=void 0;const o=r(8042),i=r(4966);class s{constructor(t,e,r){this.literalNames=t,this.symbolicNames=e,this.displayNames=r,this._maxTokenType=Math.max(this.displayNames.length,Math.max(this.literalNames.length,this.symbolicNames.length))-1}get maxTokenType(){return this._maxTokenType}getLiteralName(t){if(t>=0&&t<this.literalNames.length)return this.literalNames[t]}getSymbolicName(t){return t>=0&&t<this.symbolicNames.length?this.symbolicNames[t]:t===i.Token.EOF?"EOF":void 0}getDisplayName(t){if(t>=0&&t<this.displayNames.length){let e=this.displayNames[t];if(e)return e}let e=this.getLiteralName(t);if(e)return e;let r=this.getSymbolicName(t);return r||String(t)}}s.EMPTY_VOCABULARY=new s([],[],[]),n([o.NotNull],s.prototype,"literalNames",void 0),n([o.NotNull],s.prototype,"symbolicNames",void 0),n([o.NotNull],s.prototype,"displayNames",void 0),n([o.Override],s.prototype,"maxTokenType",null),n([o.Override],s.prototype,"getLiteralName",null),n([o.Override],s.prototype,"getSymbolicName",null),n([o.Override,o.NotNull],s.prototype,"getDisplayName",null),n([o.NotNull],s,"EMPTY_VOCABULARY",void 0),e.VocabularyImpl=s},4955:(t,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0})},7949:function(t,e,r){"use strict";var n=this&&this.__decorate||function(t,e,r,n){var o,i=arguments.length,s=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,r,n);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(i<3?o(s):i>3?o(e,r,s):o(e,r))||s);return i>3&&s&&Object.defineProperty(e,r,s),s},o=this&&this.__param||function(t,e){return function(r,n){e(r,n,t)}};Object.defineProperty(e,"__esModule",{value:!0}),e.ATN=void 0;const i=r(3874),s=r(7055),a=r(4405),l=r(4834),u=r(4334),c=r(8042),h=r(8842),p=r(9767),f=r(4966),d=r(9282);let y=class{constructor(t,e){this.states=[],this.decisionToState=[],this.modeNameToStartState=new Map,this.modeToStartState=[],this.contextCache=new i.Array2DHashMap(h.ObjectEqualityComparator.INSTANCE),this.decisionToDFA=[],this.modeToDFA=[],this.LL1Table=new Map,this.grammarType=t,this.maxTokenType=e}clearDFA(){this.decisionToDFA=new Array(this.decisionToState.length);for(let t=0;t<this.decisionToDFA.length;t++)this.decisionToDFA[t]=new s.DFA(this.decisionToState[t],t);this.modeToDFA=new Array(this.modeToStartState.length);for(let t=0;t<this.modeToDFA.length;t++)this.modeToDFA[t]=new s.DFA(this.modeToStartState[t]);this.contextCache.clear(),this.LL1Table.clear()}get contextCacheSize(){return this.contextCache.size}getCachedContext(t){return p.PredictionContext.getCachedContext(t,this.contextCache,new p.PredictionContext.IdentityHashMap)}getDecisionToDFA(){return d(null!=this.decisionToDFA&&this.decisionToDFA.length===this.decisionToState.length),this.decisionToDFA}nextTokens(t,e){if(e){return new u.LL1Analyzer(this).LOOK(t,e)}return t.nextTokenWithinRule||(t.nextTokenWithinRule=this.nextTokens(t,p.PredictionContext.EMPTY_LOCAL),t.nextTokenWithinRule.setReadonly(!0)),t.nextTokenWithinRule}addState(t){t.atn=this,t.stateNumber=this.states.length,this.states.push(t)}removeState(t){let e=new l.InvalidState;e.atn=this,e.stateNumber=t.stateNumber,this.states[t.stateNumber]=e}defineMode(t,e){this.modeNameToStartState.set(t,e),this.modeToStartState.push(e),this.modeToDFA.push(new s.DFA(e)),this.defineDecisionState(e)}defineDecisionState(t){return this.decisionToState.push(t),t.decision=this.decisionToState.length-1,this.decisionToDFA.push(new s.DFA(t,t.decision)),t.decision}getDecisionState(t){if(this.decisionToState.length>0)return this.decisionToState[t]}get numberOfDecisions(){return this.decisionToState.length}getExpectedTokens(t,e){if(t<0||t>=this.states.length)throw new RangeError("Invalid state number.");let r=e,n=this.states[t],o=this.nextTokens(n);if(!o.contains(f.Token.EPSILON))return o;let i=new a.IntervalSet;for(i.addAll(o),i.remove(f.Token.EPSILON);null!=r&&r.invokingState>=0&&o.contains(f.Token.EPSILON);){let t=this.states[r.invokingState].transition(0);o=this.nextTokens(t.followState),i.addAll(o),i.remove(f.Token.EPSILON),r=r._parent}return o.contains(f.Token.EPSILON)&&i.add(f.Token.EOF),i}};n([c.NotNull],y.prototype,"states",void 0),n([c.NotNull],y.prototype,"decisionToState",void 0),n([c.NotNull],y.prototype,"modeNameToStartState",void 0),n([c.NotNull],y.prototype,"modeToStartState",void 0),n([c.NotNull],y.prototype,"decisionToDFA",void 0),n([c.NotNull],y.prototype,"modeToDFA",void 0),n([c.NotNull],y.prototype,"nextTokens",null),n([o(0,c.NotNull)],y.prototype,"removeState",null),n([o(0,c.NotNull),o(1,c.NotNull)],y.prototype,"defineMode",null),n([o(0,c.NotNull)],y.prototype,"defineDecisionState",null),n([c.NotNull],y.prototype,"getExpectedTokens",null),y=n([o(0,c.NotNull)],y),e.ATN=y,function(t){t.INVALID_ALT_NUMBER=0}(y=e.ATN||(e.ATN={})),e.ATN=y},8595:function(t,e,r){"use strict";var n=this&&this.__decorate||function(t,e,r,n){var o,i=arguments.length,s=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,r,n);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(i<3?o(s):i>3?o(e,r,s):o(e,r))||s);return i>3&&s&&Object.defineProperty(e,r,s),s},o=this&&this.__param||function(t,e){return function(r,n){e(r,n,t)}};Object.defineProperty(e,"__esModule",{value:!0}),e.ATNConfig=void 0;const i=r(3874),s=r(213),a=r(3943),l=r(8042),u=r(8842),c=r(9767),h=r(1888),p=r(9282),f=2147483648;let d=class t{constructor(t,e,r){"number"==typeof e?(p((16777215&e)===e),this._state=t,this.altAndOuterContextDepth=e,this._context=r):(this._state=t,this.altAndOuterContextDepth=e.altAndOuterContextDepth,this._context=r)}static create(e,r,n,o=h.SemanticContext.NONE,i){return o!==h.SemanticContext.NONE?null!=i?new _(i,o,e,r,n,!1):new y(o,e,r,n):null!=i?new g(i,e,r,n,!1):new t(e,r,n)}get state(){return this._state}get alt(){return 16777215&this.altAndOuterContextDepth}get context(){return this._context}set context(t){this._context=t}get reachesIntoOuterContext(){return 0!==this.outerContextDepth}get outerContextDepth(){return this.altAndOuterContextDepth>>>24&127}set outerContextDepth(t){p(t>=0),t=Math.min(t,127),this.altAndOuterContextDepth=t<<24|(-2130706433&this.altAndOuterContextDepth)>>>0}get lexerActionExecutor(){}get semanticContext(){return h.SemanticContext.NONE}get hasPassedThroughNonGreedyDecision(){return!1}clone(){return this.transform(this.state,!1)}transform(t,e,r){return null==r?this.transformImpl(t,this._context,this.semanticContext,e,this.lexerActionExecutor):r instanceof c.PredictionContext?this.transformImpl(t,r,this.semanticContext,e,this.lexerActionExecutor):r instanceof h.SemanticContext?this.transformImpl(t,this._context,r,e,this.lexerActionExecutor):this.transformImpl(t,this._context,this.semanticContext,e,r)}transformImpl(e,r,n,o,i){let s=o&&t.checkNonGreedyDecision(this,e);return n!==h.SemanticContext.NONE?null!=i||s?new _(i,n,e,this,r,s):new y(n,e,this,r):null!=i||s?new g(i,e,this,r,s):new t(e,this,r)}static checkNonGreedyDecision(t,e){return t.hasPassedThroughNonGreedyDecision||e instanceof s.DecisionState&&e.nonGreedy}appendContext(t,e){if("number"==typeof t){let r=this.context.appendSingleContext(t,e);return this.transform(this.state,!1,r)}{let r=this.context.appendContext(t,e);return this.transform(this.state,!1,r)}}contains(t){if(this.state.stateNumber!==t.state.stateNumber||this.alt!==t.alt||!this.semanticContext.equals(t.semanticContext))return!1;let e=[],r=[];for(e.push(this.context),r.push(t.context);;){let t=e.pop(),n=r.pop();if(!t||!n)break;if(t===n)return!0;if(t.size<n.size)return!1;if(n.isEmpty)return t.hasEmpty;for(let o=0;o<n.size;o++){let i=t.findReturnState(n.getReturnState(o));if(i<0)return!1;e.push(t.getParent(i)),r.push(n.getParent(o))}}return!1}get isPrecedenceFilterSuppressed(){return 0!=(this.altAndOuterContextDepth&f)}set isPrecedenceFilterSuppressed(t){t?this.altAndOuterContextDepth|=f:this.altAndOuterContextDepth&=2147483647}equals(e){return this===e||e instanceof t&&(this.state.stateNumber===e.state.stateNumber&&this.alt===e.alt&&this.reachesIntoOuterContext===e.reachesIntoOuterContext&&this.context.equals(e.context)&&this.semanticContext.equals(e.semanticContext)&&this.isPrecedenceFilterSuppressed===e.isPrecedenceFilterSuppressed&&this.hasPassedThroughNonGreedyDecision===e.hasPassedThroughNonGreedyDecision&&u.ObjectEqualityComparator.INSTANCE.equals(this.lexerActionExecutor,e.lexerActionExecutor))}hashCode(){let t=a.MurmurHash.initialize(7);return t=a.MurmurHash.update(t,this.state.stateNumber),t=a.MurmurHash.update(t,this.alt),t=a.MurmurHash.update(t,this.reachesIntoOuterContext?1:0),t=a.MurmurHash.update(t,this.context),t=a.MurmurHash.update(t,this.semanticContext),t=a.MurmurHash.update(t,this.hasPassedThroughNonGreedyDecision?1:0),t=a.MurmurHash.update(t,this.lexerActionExecutor),t=a.MurmurHash.finish(t,7),t}toDotString(){let t="";t+="digraph G {\n",t+="rankdir=LR;\n";let e=new i.Array2DHashMap(c.PredictionContext.IdentityEqualityComparator.INSTANCE),r=[];function n(t){let n=e.size,o=e.putIfAbsent(t,n);return null!=o?o:(r.push(t),n)}for(r.push(this.context),e.put(this.context,0);;){let e=r.pop();if(!e)break;for(let r=0;r<e.size;r++)t+="  s"+n(e),t+="->",t+="s"+n(e.getParent(r)),t+='[label="'+e.getReturnState(r)+'"];\n'}return t+="}\n",t.toString()}toString(t,e,r){null==r&&(r=null!=e),null==e&&(e=!0);let n,o="";n=r?this.context.toStrings(t,this.state.stateNumber):["?"];let i=!0;for(let t of n)i?i=!1:o+=", ",o+="(",o+=this.state,e&&(o+=",",o+=this.alt),this.context&&(o+=",",o+=t),this.semanticContext!==h.SemanticContext.NONE&&(o+=",",o+=this.semanticContext),this.reachesIntoOuterContext&&(o+=",up="+this.outerContextDepth),o+=")";return o.toString()}};n([l.NotNull],d.prototype,"_state",void 0),n([l.NotNull],d.prototype,"_context",void 0),n([l.NotNull],d.prototype,"state",null),n([l.NotNull,o(0,l.NotNull)],d.prototype,"context",null),n([l.NotNull],d.prototype,"semanticContext",null),n([l.Override],d.prototype,"clone",null),n([o(0,l.NotNull),o(2,l.NotNull)],d.prototype,"transformImpl",null),n([l.Override],d.prototype,"equals",null),n([l.Override],d.prototype,"hashCode",null),n([o(0,l.NotNull),o(3,l.NotNull)],d,"create",null),d=n([o(0,l.NotNull),o(2,l.NotNull)],d),e.ATNConfig=d;let y=class extends d{constructor(t,e,r,n){super(e,r,n),this._semanticContext=t}get semanticContext(){return this._semanticContext}};n([l.NotNull],y.prototype,"_semanticContext",void 0),n([l.Override],y.prototype,"semanticContext",null),y=n([o(1,l.NotNull),o(2,l.NotNull)],y);let g=class extends d{constructor(t,e,r,n,o){if("number"==typeof r)super(e,r,n);else if(super(e,r,n),r.semanticContext!==h.SemanticContext.NONE)throw new Error("Not supported");this._lexerActionExecutor=t,this.passedThroughNonGreedyDecision=o}get lexerActionExecutor(){return this._lexerActionExecutor}get hasPassedThroughNonGreedyDecision(){return this.passedThroughNonGreedyDecision}};n([l.Override],g.prototype,"lexerActionExecutor",null),n([l.Override],g.prototype,"hasPassedThroughNonGreedyDecision",null),g=n([o(1,l.NotNull),o(2,l.NotNull)],g);let _=class extends y{constructor(t,e,r,n,o,i){super(e,r,n,o),this._lexerActionExecutor=t,this.passedThroughNonGreedyDecision=i}get lexerActionExecutor(){return this._lexerActionExecutor}get hasPassedThroughNonGreedyDecision(){return this.passedThroughNonGreedyDecision}};n([l.Override],_.prototype,"lexerActionExecutor",null),n([l.Override],_.prototype,"hasPassedThroughNonGreedyDecision",null),_=n([o(1,l.NotNull),o(2,l.NotNull)],_)},7176:function(t,e,r){"use strict";var n=this&&this.__decorate||function(t,e,r,n){var o,i=arguments.length,s=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,r,n);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(i<3?o(s):i>3?o(e,r,s):o(e,r))||s);return i>3&&s&&Object.defineProperty(e,r,s),s};Object.defineProperty(e,"__esModule",{value:!0}),e.ATNConfigSet=void 0;const o=r(3874),i=r(9026),s=r(5192),a=r(7949),l=r(8595),u=r(5280),c=r(8042),h=r(8842),p=r(9767),f=r(604),d=r(1888),y=r(9282),g=r(5103);class _{hashCode(t){return t.state^t.alt}equals(t,e){return t.state===e.state&&t.alt===e.alt}}function m(t){return t?new o.Array2DHashMap(t):new o.Array2DHashMap(_.INSTANCE)}_.INSTANCE=new _;class v{constructor(t,e){this._uniqueAlt=0,this._hasSemanticContext=!1,this._dipsIntoOuterContext=!1,this.outermostConfigSet=!1,this.cachedHashCode=-1,t?(e?(this.mergedConfigs=void 0,this.unmerged=void 0):t.isReadOnly?(this.mergedConfigs=m(),this.unmerged=[]):(this.mergedConfigs=m(t.mergedConfigs),this.unmerged=t.unmerged.slice(0)),this.configs=t.configs.slice(0),this._dipsIntoOuterContext=t._dipsIntoOuterContext,this._hasSemanticContext=t._hasSemanticContext,this.outermostConfigSet=t.outermostConfigSet,!e&&t.isReadOnly||(this._uniqueAlt=t._uniqueAlt,this._conflictInfo=t._conflictInfo)):(this.mergedConfigs=m(),this.unmerged=[],this.configs=[],this._uniqueAlt=a.ATN.INVALID_ALT_NUMBER)}getRepresentedAlternatives(){if(null!=this._conflictInfo)return this._conflictInfo.conflictedAlts.clone();let t=new u.BitSet;for(let e of this)t.set(e.alt);return t}get isReadOnly(){return null==this.mergedConfigs}get isOutermostConfigSet(){return this.outermostConfigSet}set isOutermostConfigSet(t){if(this.outermostConfigSet&&!t)throw new Error("IllegalStateException");y(!t||!this._dipsIntoOuterContext),this.outermostConfigSet=t}getStates(){let t=new i.Array2DHashSet(h.ObjectEqualityComparator.INSTANCE);for(let e of this.configs)t.add(e.state);return t}optimizeConfigs(t){if(0!==this.configs.length)for(let e of this.configs)e.context=t.atn.getCachedContext(e.context)}clone(t){let e=new v(this,t);return!t&&this.isReadOnly&&e.addAll(this.configs),e}get size(){return this.configs.length}get isEmpty(){return 0===this.configs.length}contains(t){if(!(t instanceof l.ATNConfig))return!1;if(this.mergedConfigs&&this.unmerged){let e=t,r=this.getKey(e),n=this.mergedConfigs.get(r);if(null!=n&&this.canMerge(e,r,n))return n.contains(e);for(let e of this.unmerged)if(e.contains(t))return!0}else for(let e of this.configs)if(e.contains(t))return!0;return!1}*[Symbol.iterator](){yield*this.configs}toArray(){return this.configs}add(t,e){if(this.ensureWritable(),!this.mergedConfigs||!this.unmerged)throw new Error("Covered by ensureWritable but duplicated here for strict null check limitation");let r;y(!this.outermostConfigSet||!t.reachesIntoOuterContext),null==e&&(e=f.PredictionContextCache.UNCACHED);let n=this.getKey(t),o=this.mergedConfigs.get(n);if(r=null==o,null!=o&&this.canMerge(t,n,o)){o.outerContextDepth=Math.max(o.outerContextDepth,t.outerContextDepth),t.isPrecedenceFilterSuppressed&&(o.isPrecedenceFilterSuppressed=!0);let r=p.PredictionContext.join(o.context,t.context,e);return this.updatePropertiesForMergedConfig(t),o.context===r?!1:(o.context=r,!0)}for(let o=0;o<this.unmerged.length;o++){let i=this.unmerged[o];if(this.canMerge(t,n,i)){i.outerContextDepth=Math.max(i.outerContextDepth,t.outerContextDepth),t.isPrecedenceFilterSuppressed&&(i.isPrecedenceFilterSuppressed=!0);let s=p.PredictionContext.join(i.context,t.context,e);return this.updatePropertiesForMergedConfig(t),i.context===s?!1:(i.context=s,r&&(this.mergedConfigs.put(n,i),this.unmerged.splice(o,1)),!0)}}return this.configs.push(t),r?this.mergedConfigs.put(n,t):this.unmerged.push(t),this.updatePropertiesForAddedConfig(t),!0}updatePropertiesForMergedConfig(t){this._dipsIntoOuterContext=this._dipsIntoOuterContext||t.reachesIntoOuterContext,y(!this.outermostConfigSet||!this._dipsIntoOuterContext)}updatePropertiesForAddedConfig(t){1===this.configs.length?this._uniqueAlt=t.alt:this._uniqueAlt!==t.alt&&(this._uniqueAlt=a.ATN.INVALID_ALT_NUMBER),this._hasSemanticContext=this._hasSemanticContext||!d.SemanticContext.NONE.equals(t.semanticContext),this._dipsIntoOuterContext=this._dipsIntoOuterContext||t.reachesIntoOuterContext,y(!this.outermostConfigSet||!this._dipsIntoOuterContext)}canMerge(t,e,r){return t.state.stateNumber===r.state.stateNumber&&(e.alt===r.alt&&t.semanticContext.equals(r.semanticContext))}getKey(t){return{state:t.state.stateNumber,alt:t.alt}}containsAll(t){for(let e of t){if(!(e instanceof l.ATNConfig))return!1;if(!this.contains(e))return!1}return!0}addAll(t,e){this.ensureWritable();let r=!1;for(let n of t)this.add(n,e)&&(r=!0);return r}clear(){if(this.ensureWritable(),!this.mergedConfigs||!this.unmerged)throw new Error("Covered by ensureWritable but duplicated here for strict null check limitation");this.mergedConfigs.clear(),this.unmerged.length=0,this.configs.length=0,this._dipsIntoOuterContext=!1,this._hasSemanticContext=!1,this._uniqueAlt=a.ATN.INVALID_ALT_NUMBER,this._conflictInfo=void 0}equals(t){return this===t||t instanceof v&&(this.outermostConfigSet===t.outermostConfigSet&&g.equals(this._conflictInfo,t._conflictInfo)&&s.ArrayEqualityComparator.INSTANCE.equals(this.configs,t.configs))}hashCode(){if(this.isReadOnly&&-1!==this.cachedHashCode)return this.cachedHashCode;let t=1;return t=5*t^(this.outermostConfigSet?1:0),t=5*t^s.ArrayEqualityComparator.INSTANCE.hashCode(this.configs),this.isReadOnly&&(this.cachedHashCode=t),t}toString(t){null==t&&(t=!1);let e="",r=this.configs.slice(0);r.sort(((t,e)=>t.alt!==e.alt?t.alt-e.alt:t.state.stateNumber!==e.state.stateNumber?t.state.stateNumber-e.state.stateNumber:t.semanticContext.toString().localeCompare(e.semanticContext.toString()))),e+="[";for(let n=0;n<r.length;n++)n>0&&(e+=", "),e+=r[n].toString(void 0,!0,t);return e+="]",this._hasSemanticContext&&(e+=",hasSemanticContext="+this._hasSemanticContext),this._uniqueAlt!==a.ATN.INVALID_ALT_NUMBER&&(e+=",uniqueAlt="+this._uniqueAlt),null!=this._conflictInfo&&(e+=",conflictingAlts="+this._conflictInfo.conflictedAlts,this._conflictInfo.isExact||(e+="*")),this._dipsIntoOuterContext&&(e+=",dipsIntoOuterContext"),e.toString()}get uniqueAlt(){return this._uniqueAlt}get hasSemanticContext(){return this._hasSemanticContext}set hasSemanticContext(t){this.ensureWritable(),this._hasSemanticContext=t}get conflictInfo(){return this._conflictInfo}set conflictInfo(t){this.ensureWritable(),this._conflictInfo=t}get conflictingAlts(){if(null!=this._conflictInfo)return this._conflictInfo.conflictedAlts}get isExactConflict(){return null!=this._conflictInfo&&this._conflictInfo.isExact}get dipsIntoOuterContext(){return this._dipsIntoOuterContext}get(t){return this.configs[t]}ensureWritable(){if(this.isReadOnly)throw new Error("This ATNConfigSet is read only.")}}n([c.NotNull],v.prototype,"getRepresentedAlternatives",null),n([c.Override],v.prototype,"size",null),n([c.Override],v.prototype,"isEmpty",null),n([c.Override],v.prototype,"contains",null),n([c.Override],v.prototype,Symbol.iterator,null),n([c.Override],v.prototype,"toArray",null),n([c.Override],v.prototype,"containsAll",null),n([c.Override],v.prototype,"clear",null),n([c.Override],v.prototype,"equals",null),n([c.Override],v.prototype,"hashCode",null),e.ATNConfigSet=v},9704:function(t,e,r){"use strict";var n=this&&this.__decorate||function(t,e,r,n){var o,i=arguments.length,s=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,r,n);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(i<3?o(s):i>3?o(e,r,s):o(e,r))||s);return i>3&&s&&Object.defineProperty(e,r,s),s};Object.defineProperty(e,"__esModule",{value:!0}),e.ATNDeserializationOptions=void 0;const o=r(8042);class i{constructor(t){this.readOnly=!1,t?(this.verifyATN=t.verifyATN,this.generateRuleBypassTransitions=t.generateRuleBypassTransitions,this.optimize=t.optimize):(this.verifyATN=!0,this.generateRuleBypassTransitions=!1,this.optimize=!0)}static get defaultOptions(){return null==i._defaultOptions&&(i._defaultOptions=new i,i._defaultOptions.makeReadOnly()),i._defaultOptions}get isReadOnly(){return this.readOnly}makeReadOnly(){this.readOnly=!0}get isVerifyATN(){return this.verifyATN}set isVerifyATN(t){this.throwIfReadOnly(),this.verifyATN=t}get isGenerateRuleBypassTransitions(){return this.generateRuleBypassTransitions}set isGenerateRuleBypassTransitions(t){this.throwIfReadOnly(),this.generateRuleBypassTransitions=t}get isOptimize(){return this.optimize}set isOptimize(t){this.throwIfReadOnly(),this.optimize=t}throwIfReadOnly(){if(this.isReadOnly)throw new Error("The object is read only.")}}n([o.NotNull],i,"defaultOptions",null),e.ATNDeserializationOptions=i},9963:function(t,e,r){"use strict";var n=this&&this.__decorate||function(t,e,r,n){var o,i=arguments.length,s=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,r,n);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(i<3?o(s):i>3?o(e,r,s):o(e,r))||s);return i>3&&s&&Object.defineProperty(e,r,s),s},o=this&&this.__param||function(t,e){return function(r,n){e(r,n,t)}};Object.defineProperty(e,"__esModule",{value:!0}),e.ATNDeserializer=void 0;const i=r(212),s=r(9026),a=r(7949),l=r(9704),u=r(4700),c=r(4389),h=r(9291),p=r(4743),f=r(5280),d=r(3374),y=r(4136),g=r(213),_=r(7055),m=r(628),v=r(4405),S=r(4834),N=r(8363),x=r(614),T=r(8525),b=r(4222),O=r(3642),E=r(9917),A=r(3609),P=r(5834),C=r(4068),R=r(8042),I=r(3973),w=r(4584),k=r(4604),L=r(8080),j=r(4649),D=r(3233),M=r(2751),F=r(6557),z=r(5337),U=r(4704),B=r(6765),H=r(1069),q=r(8303),V=r(7165),Y=r(4966),W=r(5223),G=r(4117),K=r(8844);var $;!function(t){t[t.UNICODE_BMP=0]="UNICODE_BMP",t[t.UNICODE_SMP=1]="UNICODE_SMP"}($||($={}));class X{constructor(t){void 0===t&&(t=l.ATNDeserializationOptions.defaultOptions),this.deserializationOptions=t}static get SERIALIZED_VERSION(){return 3}static isFeatureSupported(t,e){let r=X.SUPPORTED_UUIDS.findIndex((e=>e.equals(t)));return!(r<0)&&X.SUPPORTED_UUIDS.findIndex((t=>t.equals(e)))>=r}static getUnicodeDeserializer(t){return 0===t?{readUnicode:(t,e)=>X.toInt(t[e]),size:1}:{readUnicode:(t,e)=>X.toInt32(t,e),size:2}}deserialize(t){t=t.slice(0);for(let e=1;e<t.length;e++)t[e]=t[e]-2&65535;let e=0,r=X.toInt(t[e++]);if(r!==X.SERIALIZED_VERSION){let t=`Could not deserialize ATN with version ${r} (expected ${X.SERIALIZED_VERSION}).`;throw new Error(t)}let n=X.toUUID(t,e);if(e+=8,X.SUPPORTED_UUIDS.findIndex((t=>t.equals(n)))<0){let t=`Could not deserialize ATN with UUID ${n} (expected ${X.SERIALIZED_UUID} or a legacy UUID).`;throw new Error(t)}let o=X.isFeatureSupported(X.ADDED_LEXER_ACTIONS,n),l=X.toInt(t[e++]),f=X.toInt(t[e++]),g=new a.ATN(l,f),v=[],N=[],T=X.toInt(t[e++]);for(let r=0;r<T;r++){let r=X.toInt(t[e++]);if(r===u.ATNStateType.INVALID_TYPE){g.addState(new S.InvalidState);continue}let n=X.toInt(t[e++]);65535===n&&(n=-1);let o=this.stateFactory(r,n);if(r===u.ATNStateType.LOOP_END){let r=X.toInt(t[e++]);v.push([o,r])}else if(o instanceof y.BlockStartState){let r=X.toInt(t[e++]);N.push([o,r])}g.addState(o)}for(let t of v)t[0].loopBackState=g.states[t[1]];for(let t of N)t[0].endState=g.states[t[1]];let b=X.toInt(t[e++]);for(let r=0;r<b;r++){let r=X.toInt(t[e++]);g.states[r].nonGreedy=!0}let O=X.toInt(t[e++]);for(let r=0;r<O;r++){let r=X.toInt(t[e++]);g.states[r].sll=!0}let E=X.toInt(t[e++]);for(let r=0;r<E;r++){let r=X.toInt(t[e++]);g.states[r].isPrecedenceRule=!0}let A=X.toInt(t[e++]);0===g.grammarType&&(g.ruleToTokenType=new Int32Array(A)),g.ruleToStartState=new Array(A);for(let r=0;r<A;r++){let o=X.toInt(t[e++]),i=g.states[o];if(i.leftFactored=0!==X.toInt(t[e++]),g.ruleToStartState[r]=i,0===g.grammarType){let o=X.toInt(t[e++]);if(65535===o&&(o=Y.Token.EOF),g.ruleToTokenType[r]=o,!X.isFeatureSupported(X.ADDED_LEXER_ACTIONS,n)){let r=X.toInt(t[e++]);65535===r&&(r=-1)}}}g.ruleToStopState=new Array(A);for(let t of g.states)t instanceof z.RuleStopState&&(g.ruleToStopState[t.ruleIndex]=t,g.ruleToStartState[t.ruleIndex].stopState=t);let P=X.toInt(t[e++]);for(let r=0;r<P;r++){let r=X.toInt(t[e++]);g.modeToStartState.push(g.states[r])}g.modeToDFA=new Array(P);for(let t=0;t<P;t++)g.modeToDFA[t]=new _.DFA(g.modeToStartState[t]);let R=[];e=this.deserializeSets(t,e,R,X.getUnicodeDeserializer(0)),X.isFeatureSupported(X.ADDED_UNICODE_SMP,n)&&(e=this.deserializeSets(t,e,R,X.getUnicodeDeserializer(1)));let I=X.toInt(t[e++]);for(let r=0;r<I;r++){let r=X.toInt(t[e]),n=X.toInt(t[e+1]),o=X.toInt(t[e+2]),i=X.toInt(t[e+3]),s=X.toInt(t[e+4]),a=X.toInt(t[e+5]),l=this.edgeFactory(g,o,r,n,i,s,a,R);g.states[r].addTransition(l),e+=6}let w=new s.Array2DHashSet({hashCode:t=>t.stopState^t.returnState^t.outermostPrecedenceReturn,equals:(t,e)=>t.stopState===e.stopState&&t.returnState===e.returnState&&t.outermostPrecedenceReturn===e.outermostPrecedenceReturn}),j=[];for(let t of g.states){let e=t.ruleIndex>=0&&g.ruleToStartState[t.ruleIndex].leftFactored;for(let r=0;r<t.numberOfTransitions;r++){let n=t.transition(r);if(!(n instanceof U.RuleTransition))continue;let o=n;if(!g.ruleToStartState[o.target.ruleIndex].leftFactored&&e)continue;let i=-1;g.ruleToStartState[o.target.ruleIndex].isPrecedenceRule&&0===o.precedence&&(i=o.target.ruleIndex);let s={stopState:o.target.ruleIndex,returnState:o.followState.stateNumber,outermostPrecedenceReturn:i};w.add(s)&&j.push(s)}}for(let t of j){let e=new m.EpsilonTransition(g.states[t.returnState],t.outermostPrecedenceReturn);g.ruleToStopState[t.stopState].addTransition(e)}for(let t of g.states){if(t instanceof y.BlockStartState){if(void 0===t.endState)throw new Error("IllegalStateException");if(void 0!==t.endState.startState)throw new Error("IllegalStateException");t.endState.startState=t}if(t instanceof L.PlusLoopbackState){let e=t;for(let t=0;t<e.numberOfTransitions;t++){let r=e.transition(t).target;r instanceof k.PlusBlockStartState&&(r.loopBackState=e)}}else if(t instanceof q.StarLoopbackState){let e=t;for(let t=0;t<e.numberOfTransitions;t++){let r=e.transition(t).target;r instanceof V.StarLoopEntryState&&(r.loopBackState=e)}}}let D=X.toInt(t[e++]);for(let r=1;r<=D;r++){let n=X.toInt(t[e++]),o=g.states[n];g.decisionToState.push(o),o.decision=r-1}if(0===g.grammarType)if(o){g.lexerActions=new Array(X.toInt(t[e++]));for(let r=0;r<g.lexerActions.length;r++){let n=X.toInt(t[e++]),o=X.toInt(t[e++]);65535===o&&(o=-1);let i=X.toInt(t[e++]);65535===i&&(i=-1);let s=this.lexerActionFactory(n,o,i);g.lexerActions[r]=s}}else{let t=[];for(let e of g.states)for(let r=0;r<e.numberOfTransitions;r++){let n=e.transition(r);if(!(n instanceof i.ActionTransition))continue;let o=n.ruleIndex,s=n.actionIndex,a=new x.LexerCustomAction(o,s);e.setTransition(r,new i.ActionTransition(n.target,o,t.length,!1)),t.push(a)}g.lexerActions=t}this.markPrecedenceDecisions(g),g.decisionToDFA=new Array(D);for(let t=0;t<D;t++)g.decisionToDFA[t]=new _.DFA(g.decisionToState[t],t);if(this.deserializationOptions.isVerifyATN&&this.verifyATN(g),this.deserializationOptions.isGenerateRuleBypassTransitions&&1===g.grammarType){g.ruleToTokenType=new Int32Array(g.ruleToStartState.length);for(let t=0;t<g.ruleToStartState.length;t++)g.ruleToTokenType[t]=g.maxTokenType+t+1;for(let t=0;t<g.ruleToStartState.length;t++){let e=new h.BasicBlockStartState;e.ruleIndex=t,g.addState(e);let r,n,o=new d.BlockEndState;if(o.ruleIndex=t,g.addState(o),e.endState=o,g.defineDecisionState(e),o.startState=e,g.ruleToStartState[t].isPrecedenceRule){r=void 0;for(let e of g.states){if(e.ruleIndex!==t)continue;if(!(e instanceof V.StarLoopEntryState))continue;let n=e.transition(e.numberOfTransitions-1).target;if(n instanceof C.LoopEndState&&(n.epsilonOnlyTransitions&&n.transition(0).target instanceof z.RuleStopState)){r=e;break}}if(!r)throw new Error("Couldn't identify final state of the precedence rule prefix section.");n=r.loopBackState.transition(0)}else r=g.ruleToStopState[t];for(let t of g.states)for(let e=0;e<t.numberOfTransitions;e++){let i=t.transition(e);i!==n&&(i.target===r&&(i.target=o))}for(;g.ruleToStartState[t].numberOfTransitions>0;){let r=g.ruleToStartState[t].removeTransition(g.ruleToStartState[t].numberOfTransitions-1);e.addTransition(r)}g.ruleToStartState[t].addTransition(new m.EpsilonTransition(e)),o.addTransition(new m.EpsilonTransition(r));let i=new p.BasicState;g.addState(i),i.addTransition(new c.AtomTransition(o,g.ruleToTokenType[t])),e.addTransition(new m.EpsilonTransition(i))}this.deserializationOptions.isVerifyATN&&this.verifyATN(g)}if(this.deserializationOptions.isOptimize){for(;;){let t=0;t+=X.inlineSetRules(g),t+=X.combineChainedEpsilons(g);let e=0===g.grammarType;if(t+=X.optimizeSets(g,e),0===t)break}this.deserializationOptions.isVerifyATN&&this.verifyATN(g)}return X.identifyTailCalls(g),g}deserializeSets(t,e,r,n){let o=X.toInt(t[e++]);for(let i=0;i<o;i++){let o=X.toInt(t[e]);e++;let i=new v.IntervalSet;r.push(i),0!==X.toInt(t[e++])&&i.add(-1);for(let r=0;r<o;r++){let r=n.readUnicode(t,e);e+=n.size;let o=n.readUnicode(t,e);e+=n.size,i.add(r,o)}}return e}markPrecedenceDecisions(t){let e=new Map;for(let r of t.states)if(r instanceof V.StarLoopEntryState&&t.ruleToStartState[r.ruleIndex].isPrecedenceRule){let n=r.transition(r.numberOfTransitions-1).target;n instanceof C.LoopEndState&&n.epsilonOnlyTransitions&&n.transition(0).target instanceof z.RuleStopState&&(e.set(r.ruleIndex,r),r.precedenceRuleDecision=!0,r.precedenceLoopbackStates=new f.BitSet(t.states.length))}for(let r of e)for(let e of t.ruleToStopState[r[0]].getTransitions()){if(1!==e.serializationType)continue;-1===e.outermostPrecedenceReturn&&r[1].precedenceLoopbackStates.set(e.target.stateNumber)}}verifyATN(t){for(let e of t.states)if(this.checkCondition(void 0!==e,"ATN states should not be undefined."),e.stateType!==u.ATNStateType.INVALID_TYPE){if(this.checkCondition(e.onlyHasEpsilonTransitions||e.numberOfTransitions<=1),e instanceof k.PlusBlockStartState&&this.checkCondition(void 0!==e.loopBackState),e instanceof V.StarLoopEntryState){let t=e;if(this.checkCondition(void 0!==t.loopBackState),this.checkCondition(2===t.numberOfTransitions),t.transition(0).target instanceof H.StarBlockStartState)this.checkCondition(t.transition(1).target instanceof C.LoopEndState),this.checkCondition(!t.nonGreedy);else{if(!(t.transition(0).target instanceof C.LoopEndState))throw new Error("IllegalStateException");this.checkCondition(t.transition(1).target instanceof H.StarBlockStartState),this.checkCondition(t.nonGreedy)}}if(e instanceof q.StarLoopbackState&&(this.checkCondition(1===e.numberOfTransitions),this.checkCondition(e.transition(0).target instanceof V.StarLoopEntryState)),e instanceof C.LoopEndState&&this.checkCondition(void 0!==e.loopBackState),e instanceof F.RuleStartState&&this.checkCondition(void 0!==e.stopState),e instanceof y.BlockStartState&&this.checkCondition(void 0!==e.endState),e instanceof d.BlockEndState&&this.checkCondition(void 0!==e.startState),e instanceof g.DecisionState){let t=e;this.checkCondition(t.numberOfTransitions<=1||t.decision>=0)}else this.checkCondition(e.numberOfTransitions<=1||e instanceof z.RuleStopState)}}checkCondition(t,e){if(!t)throw new Error("IllegalStateException: "+e)}static inlineSetRules(t){let e=0,r=new Array(t.ruleToStartState.length);for(let e=0;e<t.ruleToStartState.length;e++){let n=t.ruleToStartState[e];for(;n.onlyHasEpsilonTransitions&&1===n.numberOfOptimizedTransitions&&1===n.getOptimizedTransition(0).serializationType;)n=n.getOptimizedTransition(0).target;if(1!==n.numberOfOptimizedTransitions)continue;let o=n.getOptimizedTransition(0),i=o.target;if(!o.isEpsilon&&i.onlyHasEpsilonTransitions&&1===i.numberOfOptimizedTransitions&&i.getOptimizedTransition(0).target instanceof z.RuleStopState)switch(o.serializationType){case 5:case 2:case 7:r[e]=o;break;case 8:case 9:default:continue}}for(let n of t.states){if(n.ruleIndex<0)continue;let o;for(let i=0;i<n.numberOfOptimizedTransitions;i++){let s=n.getOptimizedTransition(i);if(!(s instanceof U.RuleTransition)){void 0!==o&&o.push(s);continue}let a=s,l=r[a.target.ruleIndex];if(void 0===l){void 0!==o&&o.push(s);continue}if(void 0===o){o=[];for(let t=0;t<i;t++)o.push(n.getOptimizedTransition(i))}e++;let u=a.followState,h=new p.BasicState;switch(h.setRuleIndex(u.ruleIndex),t.addState(h),o.push(new m.EpsilonTransition(h)),l.serializationType){case 5:h.addTransition(new c.AtomTransition(u,l._label));break;case 2:h.addTransition(new M.RangeTransition(u,l.from,l.to));break;case 7:h.addTransition(new B.SetTransition(u,l.label));break;default:throw new Error("UnsupportedOperationException")}}if(void 0!==o){if(n.isOptimized)for(;n.numberOfOptimizedTransitions>0;)n.removeOptimizedTransition(n.numberOfOptimizedTransitions-1);for(let t of o)n.addOptimizedTransition(t)}}return w.ParserATNSimulator.debug&&console.log("ATN runtime optimizer removed "+e+" rule invocations by inlining sets."),e}static combineChainedEpsilons(t){let e=0;for(let r of t.states){if(!r.onlyHasEpsilonTransitions||r instanceof z.RuleStopState)continue;let t;t:for(let n=0;n<r.numberOfOptimizedTransitions;n++){let o=r.getOptimizedTransition(n),i=o.target;if(1===o.serializationType&&-1===o.outermostPrecedenceReturn&&i.stateType===u.ATNStateType.BASIC&&i.onlyHasEpsilonTransitions){for(let e=0;e<i.numberOfOptimizedTransitions;e++)if(1!==i.getOptimizedTransition(e).serializationType||-1!==i.getOptimizedTransition(e).outermostPrecedenceReturn){void 0!==t&&t.push(o);continue t}if(e++,void 0===t){t=[];for(let e=0;e<n;e++)t.push(r.getOptimizedTransition(e))}for(let e=0;e<i.numberOfOptimizedTransitions;e++){let r=i.getOptimizedTransition(e).target;t.push(new m.EpsilonTransition(r))}}else void 0!==t&&t.push(o)}if(void 0!==t){if(r.isOptimized)for(;r.numberOfOptimizedTransitions>0;)r.removeOptimizedTransition(r.numberOfOptimizedTransitions-1);for(let e of t)r.addOptimizedTransition(e)}}return w.ParserATNSimulator.debug&&console.log("ATN runtime optimizer removed "+e+" transitions by combining chained epsilon transitions."),e}static optimizeSets(t,e){if(e)return 0;let r=0,n=t.decisionToState;for(let e of n){let n=new v.IntervalSet;for(let t=0;t<e.numberOfOptimizedTransitions;t++){let r=e.getOptimizedTransition(t);if(!(r instanceof m.EpsilonTransition))continue;if(1!==r.target.numberOfOptimizedTransitions)continue;let o=r.target.getOptimizedTransition(0);o.target instanceof d.BlockEndState&&(o instanceof I.NotSetTransition||(o instanceof c.AtomTransition||o instanceof M.RangeTransition||o instanceof B.SetTransition)&&n.add(t))}if(n.size<=1)continue;let o=[];for(let t=0;t<e.numberOfOptimizedTransitions;t++)n.contains(t)||o.push(e.getOptimizedTransition(t));let i,s=e.getOptimizedTransition(n.minElement).target.getOptimizedTransition(0).target,a=new v.IntervalSet;for(let t of n.intervals)for(let r=t.a;r<=t.b;r++){let t=e.getOptimizedTransition(r).target.getOptimizedTransition(0);if(t instanceof I.NotSetTransition)throw new Error("Not yet implemented.");a.addAll(t.label)}if(1===a.intervals.length)if(1===a.size)i=new c.AtomTransition(s,a.minElement);else{let t=a.intervals[0];i=new M.RangeTransition(s,t.a,t.b)}else i=new B.SetTransition(s,a);let l=new p.BasicState;if(l.setRuleIndex(e.ruleIndex),t.addState(l),l.addTransition(i),o.push(new m.EpsilonTransition(l)),r+=e.numberOfOptimizedTransitions-o.length,e.isOptimized)for(;e.numberOfOptimizedTransitions>0;)e.removeOptimizedTransition(e.numberOfOptimizedTransitions-1);for(let t of o)e.addOptimizedTransition(t)}return w.ParserATNSimulator.debug&&console.log("ATN runtime optimizer removed "+r+" paths by collapsing sets."),r}static identifyTailCalls(t){for(let e of t.states){for(let r=0;r<e.numberOfTransitions;r++){let n=e.transition(r);n instanceof U.RuleTransition&&(n.tailCall=this.testTailCall(t,n,!1),n.optimizedTailCall=this.testTailCall(t,n,!0))}if(e.isOptimized)for(let r=0;r<e.numberOfOptimizedTransitions;r++){let n=e.getOptimizedTransition(r);n instanceof U.RuleTransition&&(n.tailCall=this.testTailCall(t,n,!1),n.optimizedTailCall=this.testTailCall(t,n,!0))}}}static testTailCall(t,e,r){if(!r&&e.tailCall)return!0;if(r&&e.optimizedTailCall)return!0;let n=new f.BitSet(t.states.length),o=[];for(o.push(e.followState);;){let t=o.pop();if(!t)break;if(n.get(t.stateNumber))continue;if(t instanceof z.RuleStopState)continue;if(!t.onlyHasEpsilonTransitions)return!1;let e=r?t.numberOfOptimizedTransitions:t.numberOfTransitions;for(let n=0;n<e;n++){let e=r?t.getOptimizedTransition(n):t.transition(n);if(1!==e.serializationType)return!1;o.push(e.target)}}return!0}static toInt(t){return t}static toInt32(t,e){return(t[e]|t[e+1]<<16)>>>0}static toUUID(t,e){let r=X.toInt32(t,e),n=X.toInt32(t,e+2),o=X.toInt32(t,e+4),i=X.toInt32(t,e+6);return new G.UUID(i,o,n,r)}edgeFactory(t,e,r,n,o,s,a,l){let u=t.states[n];switch(e){case 1:return new m.EpsilonTransition(u);case 2:return 0!==a?new M.RangeTransition(u,Y.Token.EOF,s):new M.RangeTransition(u,o,s);case 3:return new U.RuleTransition(t.states[o],s,a,u);case 4:return new D.PredicateTransition(u,o,s,0!==a);case 10:return new j.PrecedencePredicateTransition(u,o);case 5:return 0!==a?new c.AtomTransition(u,Y.Token.EOF):new c.AtomTransition(u,o);case 6:return new i.ActionTransition(u,o,s,0!==a);case 7:return new B.SetTransition(u,l[o]);case 8:return new I.NotSetTransition(u,l[o]);case 9:return new K.WildcardTransition(u)}throw new Error("The specified transition type is not valid.")}stateFactory(t,e){let r;switch(t){case u.ATNStateType.INVALID_TYPE:return new S.InvalidState;case u.ATNStateType.BASIC:r=new p.BasicState;break;case u.ATNStateType.RULE_START:r=new F.RuleStartState;break;case u.ATNStateType.BLOCK_START:r=new h.BasicBlockStartState;break;case u.ATNStateType.PLUS_BLOCK_START:r=new k.PlusBlockStartState;break;case u.ATNStateType.STAR_BLOCK_START:r=new H.StarBlockStartState;break;case u.ATNStateType.TOKEN_START:r=new W.TokensStartState;break;case u.ATNStateType.RULE_STOP:r=new z.RuleStopState;break;case u.ATNStateType.BLOCK_END:r=new d.BlockEndState;break;case u.ATNStateType.STAR_LOOP_BACK:r=new q.StarLoopbackState;break;case u.ATNStateType.STAR_LOOP_ENTRY:r=new V.StarLoopEntryState;break;case u.ATNStateType.PLUS_LOOP_BACK:r=new L.PlusLoopbackState;break;case u.ATNStateType.LOOP_END:r=new C.LoopEndState;break;default:throw new Error(`The specified state type ${t} is not valid.`)}return r.ruleIndex=e,r}lexerActionFactory(t,e,r){switch(t){case 0:return new N.LexerChannelAction(e);case 1:return new x.LexerCustomAction(e,r);case 2:return new T.LexerModeAction(e);case 3:return b.LexerMoreAction.INSTANCE;case 4:return O.LexerPopModeAction.INSTANCE;case 5:return new E.LexerPushModeAction(e);case 6:return A.LexerSkipAction.INSTANCE;case 7:return new P.LexerTypeAction(e);default:throw new Error(`The specified lexer action type ${t} is not valid.`)}}}X.BASE_SERIALIZED_UUID=G.UUID.fromString("E4178468-DF95-44D0-AD87-F22A5D5FB6D3"),X.ADDED_LEXER_ACTIONS=G.UUID.fromString("AB35191A-1603-487E-B75A-479B831EAF6D"),X.ADDED_UNICODE_SMP=G.UUID.fromString("C23FEA89-0605-4f51-AFB8-058BCAB8C91B"),X.SUPPORTED_UUIDS=[X.BASE_SERIALIZED_UUID,X.ADDED_LEXER_ACTIONS,X.ADDED_UNICODE_SMP],X.SERIALIZED_UUID=X.ADDED_UNICODE_SMP,n([R.NotNull],X.prototype,"deserializationOptions",void 0),n([o(0,R.NotNull)],X.prototype,"deserialize",null),n([o(0,R.NotNull)],X.prototype,"markPrecedenceDecisions",null),n([R.NotNull,o(0,R.NotNull)],X.prototype,"edgeFactory",null),e.ATNDeserializer=X},7643:function(t,e,r){"use strict";var n=this&&this.__decorate||function(t,e,r,n){var o,i=arguments.length,s=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,r,n);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(i<3?o(s):i>3?o(e,r,s):o(e,r))||s);return i>3&&s&&Object.defineProperty(e,r,s),s},o=this&&this.__param||function(t,e){return function(r,n){e(r,n,t)}};Object.defineProperty(e,"__esModule",{value:!0}),e.ATNSimulator=void 0;const i=r(7176),s=r(1186),a=r(8042),l=r(9767);let u=class t{constructor(t){this.atn=t}static get ERROR(){return t._ERROR||(t._ERROR=new s.DFAState(new i.ATNConfigSet),t._ERROR.stateNumber=l.PredictionContext.EMPTY_FULL_STATE_KEY),t._ERROR}clearDFA(){this.atn.clearDFA()}};n([a.NotNull],u.prototype,"atn",void 0),n([a.NotNull],u,"ERROR",null),u=n([o(0,a.NotNull)],u),e.ATNSimulator=u,u=e.ATNSimulator||(e.ATNSimulator={}),e.ATNSimulator=u},3269:function(t,e,r){"use strict";var n=this&&this.__decorate||function(t,e,r,n){var o,i=arguments.length,s=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,r,n);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(i<3?o(s):i>3?o(e,r,s):o(e,r))||s);return i>3&&s&&Object.defineProperty(e,r,s),s};Object.defineProperty(e,"__esModule",{value:!0}),e.ATNState=void 0;const o=r(8042);class i{constructor(){this.stateNumber=i.INVALID_STATE_NUMBER,this.ruleIndex=0,this.epsilonOnlyTransitions=!1,this.transitions=[],this.optimizedTransitions=this.transitions}getStateNumber(){return this.stateNumber}get nonStopStateNumber(){return this.getStateNumber()}hashCode(){return this.stateNumber}equals(t){return t instanceof i&&this.stateNumber===t.stateNumber}get isNonGreedyExitState(){return!1}toString(){return String(this.stateNumber)}getTransitions(){return this.transitions.slice(0)}get numberOfTransitions(){return this.transitions.length}addTransition(t,e){if(0===this.transitions.length)this.epsilonOnlyTransitions=t.isEpsilon;else if(this.epsilonOnlyTransitions!==t.isEpsilon)throw this.epsilonOnlyTransitions=!1,new Error("ATN state "+this.stateNumber+" has both epsilon and non-epsilon transitions.");this.transitions.splice(void 0!==e?e:this.transitions.length,0,t)}transition(t){return this.transitions[t]}setTransition(t,e){this.transitions[t]=e}removeTransition(t){return this.transitions.splice(t,1)[0]}get onlyHasEpsilonTransitions(){return this.epsilonOnlyTransitions}setRuleIndex(t){this.ruleIndex=t}get isOptimized(){return this.optimizedTransitions!==this.transitions}get numberOfOptimizedTransitions(){return this.optimizedTransitions.length}getOptimizedTransition(t){return this.optimizedTransitions[t]}addOptimizedTransition(t){this.isOptimized||(this.optimizedTransitions=new Array),this.optimizedTransitions.push(t)}setOptimizedTransition(t,e){if(!this.isOptimized)throw new Error("This ATNState is not optimized.");this.optimizedTransitions[t]=e}removeOptimizedTransition(t){if(!this.isOptimized)throw new Error("This ATNState is not optimized.");this.optimizedTransitions.splice(t,1)}}n([o.Override],i.prototype,"hashCode",null),n([o.Override],i.prototype,"equals",null),n([o.Override],i.prototype,"toString",null),e.ATNState=i,function(t){t.INVALID_STATE_NUMBER=-1}(i=e.ATNState||(e.ATNState={}))},4700:(t,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.ATNStateType=void 0,function(t){t[t.INVALID_TYPE=0]="INVALID_TYPE",t[t.BASIC=1]="BASIC",t[t.RULE_START=2]="RULE_START",t[t.BLOCK_START=3]="BLOCK_START",t[t.PLUS_BLOCK_START=4]="PLUS_BLOCK_START",t[t.STAR_BLOCK_START=5]="STAR_BLOCK_START",t[t.TOKEN_START=6]="TOKEN_START",t[t.RULE_STOP=7]="RULE_STOP",t[t.BLOCK_END=8]="BLOCK_END",t[t.STAR_LOOP_BACK=9]="STAR_LOOP_BACK",t[t.STAR_LOOP_ENTRY=10]="STAR_LOOP_ENTRY",t[t.PLUS_LOOP_BACK=11]="PLUS_LOOP_BACK",t[t.LOOP_END=12]="LOOP_END"}(e.ATNStateType||(e.ATNStateType={}))},7520:(t,e,r)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.AbstractPredicateTransition=void 0;const n=r(312);class o extends n.Transition{constructor(t){super(t)}}e.AbstractPredicateTransition=o},212:function(t,e,r){"use strict";var n=this&&this.__decorate||function(t,e,r,n){var o,i=arguments.length,s=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,r,n);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(i<3?o(s):i>3?o(e,r,s):o(e,r))||s);return i>3&&s&&Object.defineProperty(e,r,s),s},o=this&&this.__param||function(t,e){return function(r,n){e(r,n,t)}};Object.defineProperty(e,"__esModule",{value:!0}),e.ActionTransition=void 0;const i=r(8042),s=r(312);let a=class extends s.Transition{constructor(t,e,r=-1,n=!1){super(t),this.ruleIndex=e,this.actionIndex=r,this.isCtxDependent=n}get serializationType(){return 6}get isEpsilon(){return!0}matches(t,e,r){return!1}toString(){return"action_"+this.ruleIndex+":"+this.actionIndex}};n([i.Override],a.prototype,"serializationType",null),n([i.Override],a.prototype,"isEpsilon",null),n([i.Override],a.prototype,"matches",null),n([i.Override],a.prototype,"toString",null),a=n([o(0,i.NotNull)],a),e.ActionTransition=a},1305:function(t,e,r){"use strict";var n=this&&this.__decorate||function(t,e,r,n){var o,i=arguments.length,s=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,r,n);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(i<3?o(s):i>3?o(e,r,s):o(e,r))||s);return i>3&&s&&Object.defineProperty(e,r,s),s},o=this&&this.__param||function(t,e){return function(r,n){e(r,n,t)}};Object.defineProperty(e,"__esModule",{value:!0}),e.AmbiguityInfo=void 0;const i=r(112),s=r(8042);let a=class extends i.DecisionEventInfo{constructor(t,e,r,n,o,i){super(t,e,n,o,i,e.useContext),this.ambigAlts=r}get ambiguousAlternatives(){return this.ambigAlts}};n([s.NotNull],a.prototype,"ambigAlts",void 0),n([s.NotNull],a.prototype,"ambiguousAlternatives",null),a=n([o(1,s.NotNull),o(2,s.NotNull),o(3,s.NotNull)],a),e.AmbiguityInfo=a},4389:function(t,e,r){"use strict";var n=this&&this.__decorate||function(t,e,r,n){var o,i=arguments.length,s=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,r,n);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(i<3?o(s):i>3?o(e,r,s):o(e,r))||s);return i>3&&s&&Object.defineProperty(e,r,s),s},o=this&&this.__param||function(t,e){return function(r,n){e(r,n,t)}};Object.defineProperty(e,"__esModule",{value:!0}),e.AtomTransition=void 0;const i=r(4405),s=r(8042),a=r(312);let l=class extends a.Transition{constructor(t,e){super(t),this._label=e}get serializationType(){return 5}get label(){return i.IntervalSet.of(this._label)}matches(t,e,r){return this._label===t}toString(){return String(this.label)}};n([s.Override],l.prototype,"serializationType",null),n([s.Override,s.NotNull],l.prototype,"label",null),n([s.Override],l.prototype,"matches",null),n([s.Override,s.NotNull],l.prototype,"toString",null),l=n([o(0,s.NotNull)],l),e.AtomTransition=l},9291:function(t,e,r){"use strict";var n=this&&this.__decorate||function(t,e,r,n){var o,i=arguments.length,s=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,r,n);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(i<3?o(s):i>3?o(e,r,s):o(e,r))||s);return i>3&&s&&Object.defineProperty(e,r,s),s};Object.defineProperty(e,"__esModule",{value:!0}),e.BasicBlockStartState=void 0;const o=r(4700),i=r(4136),s=r(8042);class a extends i.BlockStartState{get stateType(){return o.ATNStateType.BLOCK_START}}n([s.Override],a.prototype,"stateType",null),e.BasicBlockStartState=a},4743:function(t,e,r){"use strict";var n=this&&this.__decorate||function(t,e,r,n){var o,i=arguments.length,s=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,r,n);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(i<3?o(s):i>3?o(e,r,s):o(e,r))||s);return i>3&&s&&Object.defineProperty(e,r,s),s};Object.defineProperty(e,"__esModule",{value:!0}),e.BasicState=void 0;const o=r(3269),i=r(4700),s=r(8042);class a extends o.ATNState{get stateType(){return i.ATNStateType.BASIC}}n([s.Override],a.prototype,"stateType",null),e.BasicState=a},3374:function(t,e,r){"use strict";var n=this&&this.__decorate||function(t,e,r,n){var o,i=arguments.length,s=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,r,n);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(i<3?o(s):i>3?o(e,r,s):o(e,r))||s);return i>3&&s&&Object.defineProperty(e,r,s),s};Object.defineProperty(e,"__esModule",{value:!0}),e.BlockEndState=void 0;const o=r(3269),i=r(4700),s=r(8042);class a extends o.ATNState{get stateType(){return i.ATNStateType.BLOCK_END}}n([s.Override],a.prototype,"stateType",null),e.BlockEndState=a},4136:(t,e,r)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.BlockStartState=void 0;const n=r(213);class o extends n.DecisionState{}e.BlockStartState=o},9619:function(t,e,r){"use strict";var n=this&&this.__decorate||function(t,e,r,n){var o,i=arguments.length,s=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,r,n);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(i<3?o(s):i>3?o(e,r,s):o(e,r))||s);return i>3&&s&&Object.defineProperty(e,r,s),s};Object.defineProperty(e,"__esModule",{value:!0}),e.ConflictInfo=void 0;const o=r(8042),i=r(5103);class s{constructor(t,e){this._conflictedAlts=t,this.exact=e}get conflictedAlts(){return this._conflictedAlts}get isExact(){return this.exact}equals(t){return t===this||t instanceof s&&(this.isExact===t.isExact&&i.equals(this.conflictedAlts,t.conflictedAlts))}hashCode(){return this.conflictedAlts.hashCode()}}n([o.Override],s.prototype,"equals",null),n([o.Override],s.prototype,"hashCode",null),e.ConflictInfo=s},8862:function(t,e,r){"use strict";var n=this&&this.__decorate||function(t,e,r,n){var o,i=arguments.length,s=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,r,n);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(i<3?o(s):i>3?o(e,r,s):o(e,r))||s);return i>3&&s&&Object.defineProperty(e,r,s),s},o=this&&this.__param||function(t,e){return function(r,n){e(r,n,t)}};Object.defineProperty(e,"__esModule",{value:!0}),e.ContextSensitivityInfo=void 0;const i=r(112),s=r(8042);let a=class extends i.DecisionEventInfo{constructor(t,e,r,n,o){super(t,e,r,n,o,!0)}};a=n([o(1,s.NotNull),o(2,s.NotNull)],a),e.ContextSensitivityInfo=a},112:function(t,e,r){"use strict";var n=this&&this.__decorate||function(t,e,r,n){var o,i=arguments.length,s=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,r,n);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(i<3?o(s):i>3?o(e,r,s):o(e,r))||s);return i>3&&s&&Object.defineProperty(e,r,s),s},o=this&&this.__param||function(t,e){return function(r,n){e(r,n,t)}};Object.defineProperty(e,"__esModule",{value:!0}),e.DecisionEventInfo=void 0;const i=r(8042);let s=class{constructor(t,e,r,n,o,i){this.decision=t,this.fullCtx=i,this.stopIndex=o,this.input=r,this.startIndex=n,this.state=e}};n([i.NotNull],s.prototype,"input",void 0),s=n([o(2,i.NotNull)],s),e.DecisionEventInfo=s},8966:function(t,e,r){"use strict";var n=this&&this.__decorate||function(t,e,r,n){var o,i=arguments.length,s=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,r,n);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(i<3?o(s):i>3?o(e,r,s):o(e,r))||s);return i>3&&s&&Object.defineProperty(e,r,s),s};Object.defineProperty(e,"__esModule",{value:!0}),e.DecisionInfo=void 0;const o=r(8042);class i{constructor(t){this.invocations=0,this.timeInPrediction=0,this.SLL_TotalLook=0,this.SLL_MinLook=0,this.SLL_MaxLook=0,this.LL_TotalLook=0,this.LL_MinLook=0,this.LL_MaxLook=0,this.contextSensitivities=[],this.errors=[],this.ambiguities=[],this.predicateEvals=[],this.SLL_ATNTransitions=0,this.SLL_DFATransitions=0,this.LL_Fallback=0,this.LL_ATNTransitions=0,this.LL_DFATransitions=0,this.decision=t}toString(){return"{decision="+this.decision+", contextSensitivities="+this.contextSensitivities.length+", errors="+this.errors.length+", ambiguities="+this.ambiguities.length+", SLL_lookahead="+this.SLL_TotalLook+", SLL_ATNTransitions="+this.SLL_ATNTransitions+", SLL_DFATransitions="+this.SLL_DFATransitions+", LL_Fallback="+this.LL_Fallback+", LL_lookahead="+this.LL_TotalLook+", LL_ATNTransitions="+this.LL_ATNTransitions+"}"}}n([o.Override],i.prototype,"toString",null),e.DecisionInfo=i},213:(t,e,r)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.DecisionState=void 0;const n=r(3269);class o extends n.ATNState{constructor(){super(...arguments),this.decision=-1,this.nonGreedy=!1,this.sll=!1}}e.DecisionState=o},628:function(t,e,r){"use strict";var n=this&&this.__decorate||function(t,e,r,n){var o,i=arguments.length,s=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,r,n);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(i<3?o(s):i>3?o(e,r,s):o(e,r))||s);return i>3&&s&&Object.defineProperty(e,r,s),s},o=this&&this.__param||function(t,e){return function(r,n){e(r,n,t)}};Object.defineProperty(e,"__esModule",{value:!0}),e.EpsilonTransition=void 0;const i=r(8042),s=r(312);let a=class extends s.Transition{constructor(t,e=-1){super(t),this._outermostPrecedenceReturn=e}get outermostPrecedenceReturn(){return this._outermostPrecedenceReturn}get serializationType(){return 1}get isEpsilon(){return!0}matches(t,e,r){return!1}toString(){return"epsilon"}};n([i.Override],a.prototype,"serializationType",null),n([i.Override],a.prototype,"isEpsilon",null),n([i.Override],a.prototype,"matches",null),n([i.Override,i.NotNull],a.prototype,"toString",null),a=n([o(0,i.NotNull)],a),e.EpsilonTransition=a},291:function(t,e,r){"use strict";var n=this&&this.__decorate||function(t,e,r,n){var o,i=arguments.length,s=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,r,n);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(i<3?o(s):i>3?o(e,r,s):o(e,r))||s);return i>3&&s&&Object.defineProperty(e,r,s),s},o=this&&this.__param||function(t,e){return function(r,n){e(r,n,t)}};Object.defineProperty(e,"__esModule",{value:!0}),e.ErrorInfo=void 0;const i=r(112),s=r(8042);let a=class extends i.DecisionEventInfo{constructor(t,e,r,n,o){super(t,e,r,n,o,e.useContext)}};a=n([o(1,s.NotNull),o(2,s.NotNull)],a),e.ErrorInfo=a},4834:function(t,e,r){"use strict";var n=this&&this.__decorate||function(t,e,r,n){var o,i=arguments.length,s=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,r,n);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(i<3?o(s):i>3?o(e,r,s):o(e,r))||s);return i>3&&s&&Object.defineProperty(e,r,s),s};Object.defineProperty(e,"__esModule",{value:!0}),e.InvalidState=void 0;const o=r(4700),i=r(4743),s=r(8042);class a extends i.BasicState{get stateType(){return o.ATNStateType.INVALID_TYPE}}n([s.Override],a.prototype,"stateType",null),e.InvalidState=a},4334:function(t,e,r){"use strict";var n=this&&this.__decorate||function(t,e,r,n){var o,i=arguments.length,s=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,r,n);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(i<3?o(s):i>3?o(e,r,s):o(e,r))||s);return i>3&&s&&Object.defineProperty(e,r,s),s},o=this&&this.__param||function(t,e){return function(r,n){e(r,n,t)}};Object.defineProperty(e,"__esModule",{value:!0}),e.LL1Analyzer=void 0;const i=r(7520),s=r(9026),a=r(8595),l=r(5280),u=r(4405),c=r(8042),h=r(3973),p=r(8842),f=r(9767),d=r(5337),y=r(4704),g=r(4966),_=r(8844);let m=class t{constructor(t){this.atn=t}getDecisionLookahead(e){if(null==e)return;let r=new Array(e.numberOfTransitions);for(let n=0;n<e.numberOfTransitions;n++){let o=new u.IntervalSet;r[n]=o;let i=new s.Array2DHashSet(p.ObjectEqualityComparator.INSTANCE),a=!1;this._LOOK(e.transition(n).target,void 0,f.PredictionContext.EMPTY_LOCAL,o,i,new l.BitSet,a,!1),(0===o.size||o.contains(t.HIT_PRED))&&(o=void 0,r[n]=o)}return r}LOOK(t,e,r){if(void 0===r){if(null==t.atn)throw new Error("Illegal state");r=t.atn.ruleToStopState[t.ruleIndex]}else null===r&&(r=void 0);let n=new u.IntervalSet;return this._LOOK(t,r,e,n,new s.Array2DHashSet,new l.BitSet,!0,!0),n}_LOOK(e,r,n,o,s,l,c,p){let m=a.ATNConfig.create(e,0,n);if(!s.add(m))return;if(e===r){if(f.PredictionContext.isEmptyLocal(n))return void o.add(g.Token.EPSILON);if(n.isEmpty)return void(p&&o.add(g.Token.EOF))}if(e instanceof d.RuleStopState){if(n.isEmpty&&!f.PredictionContext.isEmptyLocal(n))return void(p&&o.add(g.Token.EOF));let t=l.get(e.ruleIndex);try{l.clear(e.ruleIndex);for(let t=0;t<n.size;t++){if(n.getReturnState(t)===f.PredictionContext.EMPTY_FULL_STATE_KEY)continue;let e=this.atn.states[n.getReturnState(t)];this._LOOK(e,r,n.getParent(t),o,s,l,c,p)}}finally{t&&l.set(e.ruleIndex)}}let v=e.numberOfTransitions;for(let a=0;a<v;a++){let f=e.transition(a);if(f instanceof y.RuleTransition){if(l.get(f.ruleIndex))continue;let t=n.getChild(f.followState.stateNumber);try{l.set(f.ruleIndex),this._LOOK(f.target,r,t,o,s,l,c,p)}finally{l.clear(f.ruleIndex)}}else if(f instanceof i.AbstractPredicateTransition)c?this._LOOK(f.target,r,n,o,s,l,c,p):o.add(t.HIT_PRED);else if(f.isEpsilon)this._LOOK(f.target,r,n,o,s,l,c,p);else if(f instanceof _.WildcardTransition)o.addAll(u.IntervalSet.of(g.Token.MIN_USER_TOKEN_TYPE,this.atn.maxTokenType));else{let t=f.label;null!=t&&(f instanceof h.NotSetTransition&&(t=t.complement(u.IntervalSet.of(g.Token.MIN_USER_TOKEN_TYPE,this.atn.maxTokenType))),o.addAll(t))}}}};m.HIT_PRED=g.Token.INVALID_TYPE,n([c.NotNull],m.prototype,"atn",void 0),n([c.NotNull,o(0,c.NotNull),o(1,c.NotNull)],m.prototype,"LOOK",null),n([o(0,c.NotNull),o(2,c.NotNull),o(3,c.NotNull),o(4,c.NotNull),o(5,c.NotNull)],m.prototype,"_LOOK",null),m=n([o(0,c.NotNull)],m),e.LL1Analyzer=m},2178:function(t,e,r){"use strict";var n=this&&this.__decorate||function(t,e,r,n){var o,i=arguments.length,s=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,r,n);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(i<3?o(s):i>3?o(e,r,s):o(e,r))||s);return i>3&&s&&Object.defineProperty(e,r,s),s},o=this&&this.__param||function(t,e){return function(r,n){e(r,n,t)}};Object.defineProperty(e,"__esModule",{value:!0}),e.LexerATNSimulator=void 0;const i=r(1060),s=r(7949),a=r(8595),l=r(7176),u=r(7643),c=r(1186),h=r(8813),p=r(3227),f=r(9557),d=r(6683),y=r(5324),g=r(8042),_=r(3169),m=r(9767),v=r(5337),S=r(4966),N=r(9282);let x=class t extends u.ATNSimulator{constructor(e,r){super(e),this.optimize_tail_calls=!0,this.startIndex=-1,this._line=1,this._charPositionInLine=0,this.mode=f.Lexer.DEFAULT_MODE,this.prevAccept=new t.SimState,this.recog=r}copyState(t){this._charPositionInLine=t.charPositionInLine,this._line=t._line,this.mode=t.mode,this.startIndex=t.startIndex}match(t,e){this.mode=e;let r=t.mark();try{this.startIndex=t.index,this.prevAccept.reset();let n=this.atn.modeToDFA[e].s0;return null==n?this.matchATN(t):this.execATN(t,n)}finally{t.release(r)}}reset(){this.prevAccept.reset(),this.startIndex=-1,this._line=1,this._charPositionInLine=0,this.mode=f.Lexer.DEFAULT_MODE}matchATN(e){let r=this.atn.modeToStartState[this.mode];t.debug&&console.log(`matchATN mode ${this.mode} start: ${r}`);let n=this.mode,o=this.computeStartState(e,r),i=o.hasSemanticContext;i&&(o.hasSemanticContext=!1);let s=this.addDFAState(o);if(!i){let t=this.atn.modeToDFA[this.mode];t.s0?s=t.s0:t.s0=s}let a=this.execATN(e,s);return t.debug&&console.log(`DFA after matchATN: ${this.atn.modeToDFA[n].toLexerString()}`),a}execATN(e,r){t.debug&&console.log(`start state closure=${r.configs}`),r.isAcceptState&&this.captureSimState(this.prevAccept,e,r);let n=e.LA(1),o=r;for(;;){t.debug&&console.log(`execATN loop starting closure: ${o.configs}`);let r=this.getExistingTargetState(o,n);if(null==r&&(r=this.computeTargetState(e,o,n)),r===u.ATNSimulator.ERROR)break;if(n!==p.IntStream.EOF&&this.consume(e),r.isAcceptState&&(this.captureSimState(this.prevAccept,e,r),n===p.IntStream.EOF))break;n=e.LA(1),o=r}return this.failOrAccept(this.prevAccept,e,o.configs,n)}getExistingTargetState(e,r){let n=e.getTarget(r);return t.debug&&null!=n&&console.log("reuse state "+e.stateNumber+" edge to "+n.stateNumber),n}computeTargetState(t,e,r){let n=new _.OrderedATNConfigSet;return this.getReachableConfigSet(t,e.configs,n,r),n.isEmpty?(n.hasSemanticContext||this.addDFAEdge(e,r,u.ATNSimulator.ERROR),u.ATNSimulator.ERROR):this.addDFAEdge(e,r,n)}failOrAccept(t,e,r,n){if(null!=t.dfaState){let r=t.dfaState.lexerActionExecutor;return this.accept(e,r,this.startIndex,t.index,t.line,t.charPos),t.dfaState.prediction}if(n===p.IntStream.EOF&&e.index===this.startIndex)return S.Token.EOF;throw new y.LexerNoViableAltException(this.recog,e,this.startIndex,r)}getReachableConfigSet(e,r,n,o){let i=s.ATN.INVALID_ALT_NUMBER;for(let s of r){let r=s.alt===i;if(r&&s.hasPassedThroughNonGreedyDecision)continue;t.debug&&console.log(`testing ${this.getTokenName(o)} at ${s.toString(this.recog,!0)}`);let a=s.state.numberOfOptimizedTransitions;for(let t=0;t<a;t++){let a=s.state.getOptimizedTransition(t),l=this.getReachableTarget(a,o);if(null!=l){let t,a=s.lexerActionExecutor;null!=a?(a=a.fixOffsetBeforeMatch(e.index-this.startIndex),t=s.transform(l,!0,a)):(N(null==s.lexerActionExecutor),t=s.transform(l,!0));let u=o===p.IntStream.EOF;if(this.closure(e,t,n,r,!0,u)){i=s.alt;break}}}}}accept(e,r,n,o,i,s){t.debug&&console.log(`ACTION ${r}`),e.seek(o),this._line=i,this._charPositionInLine=s,null!=r&&null!=this.recog&&r.execute(this.recog,e,n)}getReachableTarget(t,e){if(t.matches(e,f.Lexer.MIN_CHAR_VALUE,f.Lexer.MAX_CHAR_VALUE))return t.target}computeStartState(t,e){let r=m.PredictionContext.EMPTY_FULL,n=new _.OrderedATNConfigSet;for(let o=0;o<e.numberOfTransitions;o++){let i=e.transition(o).target,s=a.ATNConfig.create(i,o+1,r);this.closure(t,s,n,!1,!1,!1)}return n}closure(e,r,n,o,i,s){if(t.debug&&console.log("closure("+r.toString(this.recog,!0)+")"),r.state instanceof v.RuleStopState){t.debug&&(null!=this.recog?console.log(`closure at ${this.recog.ruleNames[r.state.ruleIndex]} rule stop ${r}`):console.log(`closure at rule stop ${r}`));let a=r.context;if(a.isEmpty)return n.add(r),!0;a.hasEmpty&&(n.add(r.transform(r.state,!0,m.PredictionContext.EMPTY_FULL)),o=!0);for(let t=0;t<a.size;t++){let l=a.getReturnState(t);if(l===m.PredictionContext.EMPTY_FULL_STATE_KEY)continue;let u=a.getParent(t),c=this.atn.states[l],h=r.transform(c,!1,u);o=this.closure(e,h,n,o,i,s)}return o}r.state.onlyHasEpsilonTransitions||o&&r.hasPassedThroughNonGreedyDecision||n.add(r);let a=r.state;for(let t=0;t<a.numberOfOptimizedTransitions;t++){let l=a.getOptimizedTransition(t),u=this.getEpsilonTarget(e,r,l,n,i,s);null!=u&&(o=this.closure(e,u,n,o,i,s))}return o}getEpsilonTarget(e,r,n,o,i,s){let a;switch(n.serializationType){case 3:let l=n;if(this.optimize_tail_calls&&l.optimizedTailCall&&!r.context.hasEmpty)a=r.transform(n.target,!0);else{let t=r.context.getChild(l.followState.stateNumber);a=r.transform(n.target,!0,t)}break;case 10:throw new Error("Precedence predicates are not supported in lexers.");case 4:let u=n;t.debug&&console.log("EVAL rule "+u.ruleIndex+":"+u.predIndex),o.hasSemanticContext=!0,a=this.evaluatePredicate(e,u.ruleIndex,u.predIndex,i)?r.transform(n.target,!0):void 0;break;case 6:if(r.context.hasEmpty){let t=d.LexerActionExecutor.append(r.lexerActionExecutor,this.atn.lexerActions[n.actionIndex]);a=r.transform(n.target,!0,t);break}a=r.transform(n.target,!0);break;case 1:a=r.transform(n.target,!0);break;case 5:case 2:case 7:if(s&&n.matches(p.IntStream.EOF,f.Lexer.MIN_CHAR_VALUE,f.Lexer.MAX_CHAR_VALUE)){a=r.transform(n.target,!1);break}a=void 0;break;default:a=void 0}return a}evaluatePredicate(t,e,r,n){if(null==this.recog)return!0;if(!n)return this.recog.sempred(void 0,e,r);let o=this._charPositionInLine,i=this._line,s=t.index,a=t.mark();try{return this.consume(t),this.recog.sempred(void 0,e,r)}finally{this._charPositionInLine=o,this._line=i,t.seek(s),t.release(a)}}captureSimState(t,e,r){t.index=e.index,t.line=this._line,t.charPos=this._charPositionInLine,t.dfaState=r}addDFAEdge(e,r,n){if(n instanceof l.ATNConfigSet){let t=n.hasSemanticContext;t&&(n.hasSemanticContext=!1);let o=this.addDFAState(n);return t||this.addDFAEdge(e,r,o),o}t.debug&&console.log("EDGE "+e+" -> "+n+" upon "+String.fromCharCode(r)),null!=e&&e.setTarget(r,n)}addDFAState(t){N(!t.hasSemanticContext);let e=new c.DFAState(t),r=this.atn.modeToDFA[this.mode].states.get(e);if(null!=r)return r;t.optimizeConfigs(this);let n,o=new c.DFAState(t.clone(!0));for(let e of t)if(e.state instanceof v.RuleStopState){n=e;break}if(null!=n){let t=this.atn.ruleToTokenType[n.state.ruleIndex],e=n.lexerActionExecutor;o.acceptStateInfo=new i.AcceptStateInfo(t,e)}return this.atn.modeToDFA[this.mode].addState(o)}getDFA(t){return this.atn.modeToDFA[t]}getText(t){return t.getText(h.Interval.of(this.startIndex,t.index-1))}get line(){return this._line}set line(t){this._line=t}get charPositionInLine(){return this._charPositionInLine}set charPositionInLine(t){this._charPositionInLine=t}consume(t){t.LA(1)==="\n".charCodeAt(0)?(this._line++,this._charPositionInLine=0):this._charPositionInLine++,t.consume()}getTokenName(t){return-1===t?"EOF":"'"+String.fromCharCode(t)+"'"}};n([g.NotNull],x.prototype,"prevAccept",void 0),n([o(0,g.NotNull)],x.prototype,"copyState",null),n([o(0,g.NotNull)],x.prototype,"match",null),n([g.Override],x.prototype,"reset",null),n([o(0,g.NotNull)],x.prototype,"matchATN",null),n([o(0,g.NotNull),o(1,g.NotNull)],x.prototype,"execATN",null),n([o(0,g.NotNull)],x.prototype,"getExistingTargetState",null),n([g.NotNull,o(0,g.NotNull),o(1,g.NotNull)],x.prototype,"computeTargetState",null),n([o(0,g.NotNull),o(1,g.NotNull),o(2,g.NotNull)],x.prototype,"getReachableConfigSet",null),n([o(0,g.NotNull)],x.prototype,"accept",null),n([g.NotNull,o(0,g.NotNull),o(1,g.NotNull)],x.prototype,"computeStartState",null),n([o(0,g.NotNull),o(1,g.NotNull),o(2,g.NotNull)],x.prototype,"closure",null),n([o(0,g.NotNull),o(1,g.NotNull),o(2,g.NotNull),o(3,g.NotNull)],x.prototype,"getEpsilonTarget",null),n([o(0,g.NotNull)],x.prototype,"evaluatePredicate",null),n([o(0,g.NotNull),o(1,g.NotNull),o(2,g.NotNull)],x.prototype,"captureSimState",null),n([g.NotNull,o(0,g.NotNull)],x.prototype,"addDFAState",null),n([g.NotNull],x.prototype,"getDFA",null),n([g.NotNull,o(0,g.NotNull)],x.prototype,"getText",null),n([o(0,g.NotNull)],x.prototype,"consume",null),n([g.NotNull],x.prototype,"getTokenName",null),x=n([o(0,g.NotNull)],x),e.LexerATNSimulator=x,function(t){t.debug=!1,t.dfa_debug=!1;t.SimState=class{constructor(){this.index=-1,this.line=0,this.charPos=-1}reset(){this.index=-1,this.line=0,this.charPos=-1,this.dfaState=void 0}}}(x=e.LexerATNSimulator||(e.LexerATNSimulator={})),e.LexerATNSimulator=x},6683:function(t,e,r){"use strict";var n=this&&this.__decorate||function(t,e,r,n){var o,i=arguments.length,s=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,r,n);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(i<3?o(s):i>3?o(e,r,s):o(e,r))||s);return i>3&&s&&Object.defineProperty(e,r,s),s},o=this&&this.__param||function(t,e){return function(r,n){e(r,n,t)}};Object.defineProperty(e,"__esModule",{value:!0}),e.LexerActionExecutor=void 0;const i=r(5192),s=r(440),a=r(3943),l=r(8042);let u=class t{constructor(t){this._lexerActions=t;let e=a.MurmurHash.initialize();for(let r of t)e=a.MurmurHash.update(e,r);this.cachedHashCode=a.MurmurHash.finish(e,t.length)}static append(e,r){if(!e)return new t([r]);let n=e._lexerActions.slice(0);return n.push(r),new t(n)}fixOffsetBeforeMatch(e){let r;for(let t=0;t<this._lexerActions.length;t++)!this._lexerActions[t].isPositionDependent||this._lexerActions[t]instanceof s.LexerIndexedCustomAction||(r||(r=this._lexerActions.slice(0)),r[t]=new s.LexerIndexedCustomAction(e,this._lexerActions[t]));return r?new t(r):this}get lexerActions(){return this._lexerActions}execute(t,e,r){let n=!1,o=e.index;try{for(let i of this._lexerActions){if(i instanceof s.LexerIndexedCustomAction){let t=i.offset;e.seek(r+t),i=i.action,n=r+t!==o}else i.isPositionDependent&&(e.seek(o),n=!1);i.execute(t)}}finally{n&&e.seek(o)}}hashCode(){return this.cachedHashCode}equals(e){return e===this||e instanceof t&&(this.cachedHashCode===e.cachedHashCode&&i.ArrayEqualityComparator.INSTANCE.equals(this._lexerActions,e._lexerActions))}};n([l.NotNull],u.prototype,"_lexerActions",void 0),n([l.NotNull],u.prototype,"lexerActions",null),n([o(0,l.NotNull)],u.prototype,"execute",null),n([l.Override],u.prototype,"hashCode",null),n([l.Override],u.prototype,"equals",null),n([l.NotNull,o(1,l.NotNull)],u,"append",null),u=n([o(0,l.NotNull)],u),e.LexerActionExecutor=u},8363:function(t,e,r){"use strict";var n=this&&this.__decorate||function(t,e,r,n){var o,i=arguments.length,s=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,r,n);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(i<3?o(s):i>3?o(e,r,s):o(e,r))||s);return i>3&&s&&Object.defineProperty(e,r,s),s},o=this&&this.__param||function(t,e){return function(r,n){e(r,n,t)}};Object.defineProperty(e,"__esModule",{value:!0}),e.LexerChannelAction=void 0;const i=r(3943),s=r(8042);class a{constructor(t){this._channel=t}get channel(){return this._channel}get actionType(){return 0}get isPositionDependent(){return!1}execute(t){t.channel=this._channel}hashCode(){let t=i.MurmurHash.initialize();return t=i.MurmurHash.update(t,this.actionType),t=i.MurmurHash.update(t,this._channel),i.MurmurHash.finish(t,2)}equals(t){return t===this||t instanceof a&&this._channel===t._channel}toString(){return`channel(${this._channel})`}}n([s.Override],a.prototype,"actionType",null),n([s.Override],a.prototype,"isPositionDependent",null),n([s.Override,o(0,s.NotNull)],a.prototype,"execute",null),n([s.Override],a.prototype,"hashCode",null),n([s.Override],a.prototype,"equals",null),n([s.Override],a.prototype,"toString",null),e.LexerChannelAction=a},614:function(t,e,r){"use strict";var n=this&&this.__decorate||function(t,e,r,n){var o,i=arguments.length,s=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,r,n);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(i<3?o(s):i>3?o(e,r,s):o(e,r))||s);return i>3&&s&&Object.defineProperty(e,r,s),s},o=this&&this.__param||function(t,e){return function(r,n){e(r,n,t)}};Object.defineProperty(e,"__esModule",{value:!0}),e.LexerCustomAction=void 0;const i=r(3943),s=r(8042);class a{constructor(t,e){this._ruleIndex=t,this._actionIndex=e}get ruleIndex(){return this._ruleIndex}get actionIndex(){return this._actionIndex}get actionType(){return 1}get isPositionDependent(){return!0}execute(t){t.action(void 0,this._ruleIndex,this._actionIndex)}hashCode(){let t=i.MurmurHash.initialize();return t=i.MurmurHash.update(t,this.actionType),t=i.MurmurHash.update(t,this._ruleIndex),t=i.MurmurHash.update(t,this._actionIndex),i.MurmurHash.finish(t,3)}equals(t){return t===this||t instanceof a&&(this._ruleIndex===t._ruleIndex&&this._actionIndex===t._actionIndex)}}n([s.Override],a.prototype,"actionType",null),n([s.Override],a.prototype,"isPositionDependent",null),n([s.Override,o(0,s.NotNull)],a.prototype,"execute",null),n([s.Override],a.prototype,"hashCode",null),n([s.Override],a.prototype,"equals",null),e.LexerCustomAction=a},440:function(t,e,r){"use strict";var n=this&&this.__decorate||function(t,e,r,n){var o,i=arguments.length,s=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,r,n);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(i<3?o(s):i>3?o(e,r,s):o(e,r))||s);return i>3&&s&&Object.defineProperty(e,r,s),s},o=this&&this.__param||function(t,e){return function(r,n){e(r,n,t)}};Object.defineProperty(e,"__esModule",{value:!0}),e.LexerIndexedCustomAction=void 0;const i=r(3943),s=r(8042);let a=class t{constructor(t,e){this._offset=t,this._action=e}get offset(){return this._offset}get action(){return this._action}get actionType(){return this._action.actionType}get isPositionDependent(){return!0}execute(t){this._action.execute(t)}hashCode(){let t=i.MurmurHash.initialize();return t=i.MurmurHash.update(t,this._offset),t=i.MurmurHash.update(t,this._action),i.MurmurHash.finish(t,2)}equals(e){return e===this||e instanceof t&&(this._offset===e._offset&&this._action.equals(e._action))}};n([s.NotNull],a.prototype,"action",null),n([s.Override],a.prototype,"actionType",null),n([s.Override],a.prototype,"isPositionDependent",null),n([s.Override],a.prototype,"execute",null),n([s.Override],a.prototype,"hashCode",null),n([s.Override],a.prototype,"equals",null),a=n([o(1,s.NotNull)],a),e.LexerIndexedCustomAction=a},8525:function(t,e,r){"use strict";var n=this&&this.__decorate||function(t,e,r,n){var o,i=arguments.length,s=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,r,n);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(i<3?o(s):i>3?o(e,r,s):o(e,r))||s);return i>3&&s&&Object.defineProperty(e,r,s),s},o=this&&this.__param||function(t,e){return function(r,n){e(r,n,t)}};Object.defineProperty(e,"__esModule",{value:!0}),e.LexerModeAction=void 0;const i=r(3943),s=r(8042);class a{constructor(t){this._mode=t}get mode(){return this._mode}get actionType(){return 2}get isPositionDependent(){return!1}execute(t){t.mode(this._mode)}hashCode(){let t=i.MurmurHash.initialize();return t=i.MurmurHash.update(t,this.actionType),t=i.MurmurHash.update(t,this._mode),i.MurmurHash.finish(t,2)}equals(t){return t===this||t instanceof a&&this._mode===t._mode}toString(){return`mode(${this._mode})`}}n([s.Override],a.prototype,"actionType",null),n([s.Override],a.prototype,"isPositionDependent",null),n([s.Override,o(0,s.NotNull)],a.prototype,"execute",null),n([s.Override],a.prototype,"hashCode",null),n([s.Override],a.prototype,"equals",null),n([s.Override],a.prototype,"toString",null),e.LexerModeAction=a},4222:function(t,e,r){"use strict";var n=this&&this.__decorate||function(t,e,r,n){var o,i=arguments.length,s=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,r,n);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(i<3?o(s):i>3?o(e,r,s):o(e,r))||s);return i>3&&s&&Object.defineProperty(e,r,s),s},o=this&&this.__param||function(t,e){return function(r,n){e(r,n,t)}};Object.defineProperty(e,"__esModule",{value:!0}),e.LexerMoreAction=void 0;const i=r(3943),s=r(8042);class a{constructor(){}get actionType(){return 3}get isPositionDependent(){return!1}execute(t){t.more()}hashCode(){let t=i.MurmurHash.initialize();return t=i.MurmurHash.update(t,this.actionType),i.MurmurHash.finish(t,1)}equals(t){return t===this}toString(){return"more"}}n([s.Override],a.prototype,"actionType",null),n([s.Override],a.prototype,"isPositionDependent",null),n([s.Override,o(0,s.NotNull)],a.prototype,"execute",null),n([s.Override],a.prototype,"hashCode",null),n([s.Override],a.prototype,"equals",null),n([s.Override],a.prototype,"toString",null),e.LexerMoreAction=a,function(t){t.INSTANCE=new t}(a=e.LexerMoreAction||(e.LexerMoreAction={}))},3642:function(t,e,r){"use strict";var n=this&&this.__decorate||function(t,e,r,n){var o,i=arguments.length,s=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,r,n);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(i<3?o(s):i>3?o(e,r,s):o(e,r))||s);return i>3&&s&&Object.defineProperty(e,r,s),s},o=this&&this.__param||function(t,e){return function(r,n){e(r,n,t)}};Object.defineProperty(e,"__esModule",{value:!0}),e.LexerPopModeAction=void 0;const i=r(3943),s=r(8042);class a{constructor(){}get actionType(){return 4}get isPositionDependent(){return!1}execute(t){t.popMode()}hashCode(){let t=i.MurmurHash.initialize();return t=i.MurmurHash.update(t,this.actionType),i.MurmurHash.finish(t,1)}equals(t){return t===this}toString(){return"popMode"}}n([s.Override],a.prototype,"actionType",null),n([s.Override],a.prototype,"isPositionDependent",null),n([s.Override,o(0,s.NotNull)],a.prototype,"execute",null),n([s.Override],a.prototype,"hashCode",null),n([s.Override],a.prototype,"equals",null),n([s.Override],a.prototype,"toString",null),e.LexerPopModeAction=a,function(t){t.INSTANCE=new t}(a=e.LexerPopModeAction||(e.LexerPopModeAction={}))},9917:function(t,e,r){"use strict";var n=this&&this.__decorate||function(t,e,r,n){var o,i=arguments.length,s=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,r,n);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(i<3?o(s):i>3?o(e,r,s):o(e,r))||s);return i>3&&s&&Object.defineProperty(e,r,s),s},o=this&&this.__param||function(t,e){return function(r,n){e(r,n,t)}};Object.defineProperty(e,"__esModule",{value:!0}),e.LexerPushModeAction=void 0;const i=r(3943),s=r(8042);class a{constructor(t){this._mode=t}get mode(){return this._mode}get actionType(){return 5}get isPositionDependent(){return!1}execute(t){t.pushMode(this._mode)}hashCode(){let t=i.MurmurHash.initialize();return t=i.MurmurHash.update(t,this.actionType),t=i.MurmurHash.update(t,this._mode),i.MurmurHash.finish(t,2)}equals(t){return t===this||t instanceof a&&this._mode===t._mode}toString(){return`pushMode(${this._mode})`}}n([s.Override],a.prototype,"actionType",null),n([s.Override],a.prototype,"isPositionDependent",null),n([s.Override,o(0,s.NotNull)],a.prototype,"execute",null),n([s.Override],a.prototype,"hashCode",null),n([s.Override],a.prototype,"equals",null),n([s.Override],a.prototype,"toString",null),e.LexerPushModeAction=a},3609:function(t,e,r){"use strict";var n=this&&this.__decorate||function(t,e,r,n){var o,i=arguments.length,s=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,r,n);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(i<3?o(s):i>3?o(e,r,s):o(e,r))||s);return i>3&&s&&Object.defineProperty(e,r,s),s},o=this&&this.__param||function(t,e){return function(r,n){e(r,n,t)}};Object.defineProperty(e,"__esModule",{value:!0}),e.LexerSkipAction=void 0;const i=r(3943),s=r(8042);class a{constructor(){}get actionType(){return 6}get isPositionDependent(){return!1}execute(t){t.skip()}hashCode(){let t=i.MurmurHash.initialize();return t=i.MurmurHash.update(t,this.actionType),i.MurmurHash.finish(t,1)}equals(t){return t===this}toString(){return"skip"}}n([s.Override],a.prototype,"actionType",null),n([s.Override],a.prototype,"isPositionDependent",null),n([s.Override,o(0,s.NotNull)],a.prototype,"execute",null),n([s.Override],a.prototype,"hashCode",null),n([s.Override],a.prototype,"equals",null),n([s.Override],a.prototype,"toString",null),e.LexerSkipAction=a,function(t){t.INSTANCE=new t}(a=e.LexerSkipAction||(e.LexerSkipAction={}))},5834:function(t,e,r){"use strict";var n=this&&this.__decorate||function(t,e,r,n){var o,i=arguments.length,s=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,r,n);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(i<3?o(s):i>3?o(e,r,s):o(e,r))||s);return i>3&&s&&Object.defineProperty(e,r,s),s},o=this&&this.__param||function(t,e){return function(r,n){e(r,n,t)}};Object.defineProperty(e,"__esModule",{value:!0}),e.LexerTypeAction=void 0;const i=r(3943),s=r(8042);class a{constructor(t){this._type=t}get type(){return this._type}get actionType(){return 7}get isPositionDependent(){return!1}execute(t){t.type=this._type}hashCode(){let t=i.MurmurHash.initialize();return t=i.MurmurHash.update(t,this.actionType),t=i.MurmurHash.update(t,this._type),i.MurmurHash.finish(t,2)}equals(t){return t===this||t instanceof a&&this._type===t._type}toString(){return`type(${this._type})`}}n([s.Override],a.prototype,"actionType",null),n([s.Override],a.prototype,"isPositionDependent",null),n([s.Override,o(0,s.NotNull)],a.prototype,"execute",null),n([s.Override],a.prototype,"hashCode",null),n([s.Override],a.prototype,"equals",null),n([s.Override],a.prototype,"toString",null),e.LexerTypeAction=a},5164:function(t,e,r){"use strict";var n=this&&this.__decorate||function(t,e,r,n){var o,i=arguments.length,s=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,r,n);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(i<3?o(s):i>3?o(e,r,s):o(e,r))||s);return i>3&&s&&Object.defineProperty(e,r,s),s},o=this&&this.__param||function(t,e){return function(r,n){e(r,n,t)}};Object.defineProperty(e,"__esModule",{value:!0}),e.LookaheadEventInfo=void 0;const i=r(112),s=r(8042);let a=class extends i.DecisionEventInfo{constructor(t,e,r,n,o,i,s){super(t,e,n,o,i,s),this.predictedAlt=r}};a=n([o(3,s.NotNull)],a),e.LookaheadEventInfo=a},4068:function(t,e,r){"use strict";var n=this&&this.__decorate||function(t,e,r,n){var o,i=arguments.length,s=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,r,n);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(i<3?o(s):i>3?o(e,r,s):o(e,r))||s);return i>3&&s&&Object.defineProperty(e,r,s),s};Object.defineProperty(e,"__esModule",{value:!0}),e.LoopEndState=void 0;const o=r(3269),i=r(4700),s=r(8042);class a extends o.ATNState{get stateType(){return i.ATNStateType.LOOP_END}}n([s.Override],a.prototype,"stateType",null),e.LoopEndState=a},3973:function(t,e,r){"use strict";var n=this&&this.__decorate||function(t,e,r,n){var o,i=arguments.length,s=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,r,n);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(i<3?o(s):i>3?o(e,r,s):o(e,r))||s);return i>3&&s&&Object.defineProperty(e,r,s),s},o=this&&this.__param||function(t,e){return function(r,n){e(r,n,t)}};Object.defineProperty(e,"__esModule",{value:!0}),e.NotSetTransition=void 0;const i=r(8042),s=r(6765);let a=class extends s.SetTransition{constructor(t,e){super(t,e)}get serializationType(){return 8}matches(t,e,r){return t>=e&&t<=r&&!super.matches(t,e,r)}toString(){return"~"+super.toString()}};n([i.Override],a.prototype,"serializationType",null),n([i.Override],a.prototype,"matches",null),n([i.Override],a.prototype,"toString",null),a=n([o(0,i.NotNull),o(1,i.Nullable)],a),e.NotSetTransition=a},3169:function(t,e,r){"use strict";var n=this&&this.__decorate||function(t,e,r,n){var o,i=arguments.length,s=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,r,n);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(i<3?o(s):i>3?o(e,r,s):o(e,r))||s);return i>3&&s&&Object.defineProperty(e,r,s),s};Object.defineProperty(e,"__esModule",{value:!0}),e.OrderedATNConfigSet=void 0;const o=r(7176),i=r(8042);class s extends o.ATNConfigSet{constructor(t,e){null!=t&&null!=e?super(t,e):super()}clone(t){let e=new s(this,t);return!t&&this.isReadOnly&&e.addAll(this),e}getKey(t){return{state:0,alt:t.hashCode()}}canMerge(t,e,r){return t.equals(r)}}n([i.Override],s.prototype,"clone",null),n([i.Override],s.prototype,"getKey",null),n([i.Override],s.prototype,"canMerge",null),e.OrderedATNConfigSet=s},6019:function(t,e,r){"use strict";var n=this&&this.__decorate||function(t,e,r,n){var o,i=arguments.length,s=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,r,n);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(i<3?o(s):i>3?o(e,r,s):o(e,r))||s);return i>3&&s&&Object.defineProperty(e,r,s),s},o=this&&this.__param||function(t,e){return function(r,n){e(r,n,t)}};Object.defineProperty(e,"__esModule",{value:!0}),e.ParseInfo=void 0;const i=r(8042);let s=class{constructor(t){this.atnSimulator=t}getDecisionInfo(){return this.atnSimulator.getDecisionInfo()}getLLDecisions(){let t=this.atnSimulator.getDecisionInfo(),e=[];for(let r=0;r<t.length;r++){t[r].LL_Fallback>0&&e.push(r)}return e}getTotalTimeInPrediction(){let t=this.atnSimulator.getDecisionInfo(),e=0;for(let r of t)e+=r.timeInPrediction;return e}getTotalSLLLookaheadOps(){let t=this.atnSimulator.getDecisionInfo(),e=0;for(let r of t)e+=r.SLL_TotalLook;return e}getTotalLLLookaheadOps(){let t=this.atnSimulator.getDecisionInfo(),e=0;for(let r of t)e+=r.LL_TotalLook;return e}getTotalSLLATNLookaheadOps(){let t=this.atnSimulator.getDecisionInfo(),e=0;for(let r of t)e+=r.SLL_ATNTransitions;return e}getTotalLLATNLookaheadOps(){let t=this.atnSimulator.getDecisionInfo(),e=0;for(let r of t)e+=r.LL_ATNTransitions;return e}getTotalATNLookaheadOps(){let t=this.atnSimulator.getDecisionInfo(),e=0;for(let r of t)e+=r.SLL_ATNTransitions,e+=r.LL_ATNTransitions;return e}getDFASize(t){if(t){return this.atnSimulator.atn.decisionToDFA[t].states.size}{let t=0,e=this.atnSimulator.atn.decisionToDFA;for(let r=0;r<e.length;r++)t+=this.getDFASize(r);return t}}};n([i.NotNull],s.prototype,"getDecisionInfo",null),n([i.NotNull],s.prototype,"getLLDecisions",null),s=n([o(0,i.NotNull)],s),e.ParseInfo=s},4584:function(t,e,r){"use strict";var n=this&&this.__decorate||function(t,e,r,n){var o,i=arguments.length,s=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,r,n);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(i<3?o(s):i>3?o(e,r,s):o(e,r))||s);return i>3&&s&&Object.defineProperty(e,r,s),s},o=this&&this.__param||function(t,e){return function(r,n){e(r,n,t)}};Object.defineProperty(e,"__esModule",{value:!0}),e.ParserATNSimulator=void 0;const i=r(1060),s=r(212),a=r(9026),l=r(6005),u=r(7949),c=r(8595),h=r(7176),p=r(7643),f=r(4700),d=r(4389),y=r(5280),g=r(9619),_=r(213),m=r(1186),v=r(6766),S=r(8813),N=r(3227),x=r(8042),T=r(3973),b=r(4609),O=r(8842),E=r(3208),A=r(9767),P=r(604),C=r(6247),R=r(5337),I=r(4704),w=r(1888),k=r(6765),L=r(1071),j=r(4966),D=r(6763),M=r(9282),F=-2147483648;let z=class t extends p.ATNSimulator{constructor(t,e){super(t),this.predictionMode=C.PredictionMode.LL,this.force_global_context=!1,this.always_try_local_context=!0,this.enable_global_context_dfa=!1,this.optimize_unique_closure=!0,this.optimize_ll1=!0,this.optimize_tail_calls=!0,this.tail_call_preserves_sll=!0,this.treat_sllk1_conflict_as_ambiguity=!1,this.reportAmbiguities=!1,this.userWantsCtxSensitive=!0,this._parser=e}getPredictionMode(){return this.predictionMode}setPredictionMode(t){this.predictionMode=t}reset(){}adaptivePredict(e,r,n,o){void 0===o&&(o=!1);let i,s=this.atn.decisionToDFA[r];if(M(null!=s),this.optimize_ll1&&!s.isPrecedenceDfa&&!s.isEmpty){let t=e.LA(1);if(t>=0&&t<=65535){let e=(r<<16>>>0)+t,n=this.atn.LL1Table.get(e);if(null!=n)return n}}this.dfa=s,this.force_global_context?o=!0:this.always_try_local_context||(o=o||s.isContextSensitive),this.userWantsCtxSensitive=o||this.predictionMode!==C.PredictionMode.SLL&&null!=n&&!this.atn.decisionToState[r].sll,null==n&&(n=E.ParserRuleContext.emptyContext()),s.isEmpty||(i=this.getStartState(s,e,n,o)),null==i&&(null==n&&(n=E.ParserRuleContext.emptyContext()),t.debug&&console.log("ATN decision "+s.decision+" exec LA(1)=="+this.getLookaheadName(e)+", outerContext="+n.toString(this._parser)),i=this.computeStartState(s,n,o));let a=e.mark(),l=e.index;try{let r=this.execDFA(s,e,l,i);return t.debug&&console.log("DFA after predictATN: "+s.toString(this._parser.vocabulary,this._parser.ruleNames)),r}finally{this.dfa=void 0,e.seek(l),e.release(a)}}getStartState(t,e,r,n){if(!n){if(t.isPrecedenceDfa){let e=t.getPrecedenceStartState(this._parser.precedence,!1);if(null==e)return;return new L.SimulatorState(r,e,!1,r)}if(null==t.s0)return;return new L.SimulatorState(r,t.s0,!1,r)}if(!this.enable_global_context_dfa)return;let o,i=r;for(M(null!=r),o=t.isPrecedenceDfa?t.getPrecedenceStartState(this._parser.precedence,!0):t.s0full;null!=i&&null!=o&&o.isContextSensitive;)i=this.skipTailCalls(i),o=o.getContextTarget(this.getReturnState(i)),i.isEmpty?M(null==o||!o.isContextSensitive):i=i.parent;return null!=o?new L.SimulatorState(r,o,n,i):void 0}execDFA(e,r,n,o){let i=o.outerContext;t.dfa_debug&&console.log("DFA decision "+e.decision+" exec LA(1)=="+this.getLookaheadName(r)+", outerContext="+i.toString(this._parser)),t.dfa_debug&&console.log(e.toString(this._parser.vocabulary,this._parser.ruleNames));let s=o.s0,a=r.LA(1),l=o.remainingOuterContext;for(;;){if(t.dfa_debug&&console.log("DFA state "+s.stateNumber+" LA(1)=="+this.getLookaheadName(r)),o.useContext)for(;s.isContextSymbol(a);){let t;if(null!=l&&(l=this.skipTailCalls(l),t=s.getContextTarget(this.getReturnState(l))),null==t){let t=new L.SimulatorState(o.outerContext,s,o.useContext,l);return this.execATN(e,r,n,t)}M(null!=l),l=l.parent,s=t}if(this.isAcceptState(s,o.useContext)){null!=s.predicates?t.dfa_debug&&console.log("accept "+s):t.dfa_debug&&console.log("accept; predict "+s.prediction+" in state "+s.stateNumber);break}M(!this.isAcceptState(s,o.useContext));let u=this.getExistingTargetState(s,a);if(null==u){let u;if(t.dfa_debug&&a>=0&&console.log("no edge for "+this._parser.vocabulary.getDisplayName(a)),t.dfa_debug){let t=S.Interval.of(n,this._parser.inputStream.index);console.log("ATN exec upon "+this._parser.inputStream.getText(t)+" at DFA state "+s.stateNumber)}let c=new L.SimulatorState(i,s,o.useContext,l);return u=this.execATN(e,r,n,c),t.dfa_debug&&console.log("back from DFA update, alt="+u+", dfa=\n"+e.toString(this._parser.vocabulary,this._parser.ruleNames)),t.dfa_debug&&console.log("DFA decision "+e.decision+" predicts "+u),u}if(u===p.ATNSimulator.ERROR){let t=new L.SimulatorState(i,s,o.useContext,l);return this.handleNoViableAlt(r,n,t)}s=u,this.isAcceptState(s,o.useContext)||a===N.IntStream.EOF||(r.consume(),a=r.LA(1))}if(!o.useContext&&null!=s.configs.conflictInfo&&e.atnStartState instanceof _.DecisionState&&!(!this.userWantsCtxSensitive||!s.configs.dipsIntoOuterContext&&s.configs.isExactConflict||this.treat_sllk1_conflict_as_ambiguity&&r.index===n)){let t;M(!o.useContext);let a=s.predicates;if(null!=a){let e=r.index;if(e!==n&&r.seek(n),t=this.evalSemanticContext(a,i,!0),1===t.cardinality())return t.nextSetBit(0);e!==n&&r.seek(e)}if(this.reportAmbiguities){let a=new L.SimulatorState(i,s,o.useContext,l);this.reportAttemptingFullContext(e,t,a,n,r.index)}return r.seek(n),this.adaptivePredict(r,e.decision,i,!0)}let u=s.predicates;if(null!=u){let t=r.index;n!==t&&r.seek(n);let o=this.evalSemanticContext(u,i,this.reportAmbiguities&&this.predictionMode===C.PredictionMode.LL_EXACT_AMBIG_DETECTION);switch(o.cardinality()){case 0:throw this.noViableAlt(r,i,s.configs,n);case 1:return o.nextSetBit(0);default:return n!==t&&r.seek(t),this.reportAmbiguity(e,s,n,t,s.configs.isExactConflict,o,s.configs),o.nextSetBit(0)}}return t.dfa_debug&&console.log("DFA decision "+e.decision+" predicts "+s.prediction),s.prediction}isAcceptState(t,e){return!!t.isAcceptState&&(null==t.configs.conflictingAlts||(!e||this.predictionMode!==C.PredictionMode.LL_EXACT_AMBIG_DETECTION||t.configs.isExactConflict))}execATN(e,r,n,o){t.debug&&console.log("execATN decision "+e.decision+" exec LA(1)=="+this.getLookaheadName(r));let i=o.outerContext,s=o.useContext,a=r.LA(1),l=o,c=new P.PredictionContextCache;for(;;){let o=this.computeReachSet(e,l,a,c);if(null==o)return this.setDFAEdge(l.s0,r.LA(1),p.ATNSimulator.ERROR),this.handleNoViableAlt(r,n,l);let h=o.s0;if(M(h.isAcceptState||h.prediction===u.ATN.INVALID_ALT_NUMBER),M(h.isAcceptState||null==h.configs.conflictInfo),this.isAcceptState(h,s)){let l=h.configs.conflictingAlts,c=null==l?h.prediction:u.ATN.INVALID_ALT_NUMBER;if(c!==u.ATN.INVALID_ALT_NUMBER){if(this.optimize_ll1&&r.index===n&&!e.isPrecedenceDfa&&o.outerContext===o.remainingOuterContext&&e.decision>=0&&!h.configs.hasSemanticContext&&a>=0&&a<=65535){let t=(e.decision<<16>>>0)+a;this.atn.LL1Table.set(t,c)}s&&this.always_try_local_context&&this.reportContextSensitivity(e,c,o,n,r.index)}c=h.prediction;let p=null!=l&&this.userWantsCtxSensitive;if(p&&(p=!s&&(h.configs.dipsIntoOuterContext||!h.configs.isExactConflict)&&(!this.treat_sllk1_conflict_as_ambiguity||r.index!==n)),h.configs.hasSemanticContext){let t=h.predicates;if(null!=t){let e=r.index;switch(e!==n&&r.seek(n),l=this.evalSemanticContext(t,i,p||this.reportAmbiguities),l.cardinality()){case 0:throw this.noViableAlt(r,i,h.configs,n);case 1:return l.nextSetBit(0)}e!==n&&r.seek(e)}}if(p){M(!s),M(this.isAcceptState(h,!1)),t.debug&&console.log("RETRY with outerContext="+i);let a=this.computeStartState(e,i,!0);return this.reportAmbiguities&&this.reportAttemptingFullContext(e,l,o,n,r.index),r.seek(n),this.execATN(e,r,n,a)}return null!=l&&(this.reportAmbiguities&&l.cardinality()>1&&this.reportAmbiguity(e,h,n,r.index,h.configs.isExactConflict,l,h.configs),c=l.nextSetBit(0)),c}l=o,a!==N.IntStream.EOF&&(r.consume(),a=r.LA(1))}}handleNoViableAlt(t,e,r){if(null!=r.s0){let n=new y.BitSet,o=0;for(let t of r.s0.configs)(t.reachesIntoOuterContext||t.state instanceof R.RuleStopState)&&(n.set(t.alt),o=Math.max(o,t.alt));switch(n.cardinality()){case 0:break;case 1:return n.nextSetBit(0);default:if(!r.s0.configs.hasSemanticContext)return n.nextSetBit(0);let i=new h.ATNConfigSet;for(let t of r.s0.configs)(t.reachesIntoOuterContext||t.state instanceof R.RuleStopState)&&i.add(t);let s=this.getPredsForAmbigAlts(n,i,o);if(null!=s){let o=this.getPredicatePredictions(n,s);if(null!=o){let n=t.index;try{t.seek(e);let i=this.evalSemanticContext(o,r.outerContext,!1);if(!i.isEmpty)return i.nextSetBit(0)}finally{t.seek(n)}}}return n.nextSetBit(0)}}throw this.noViableAlt(t,r.outerContext,r.s0.configs,e)}computeReachSet(t,e,r,n){let o=e.useContext,i=e.remainingOuterContext,s=e.s0;if(o)for(;s.isContextSymbol(r);){let t;if(null!=i&&(i=this.skipTailCalls(i),t=s.getContextTarget(this.getReturnState(i))),null==t)break;M(null!=i),i=i.parent,s=t}if(M(!this.isAcceptState(s,o)),this.isAcceptState(s,o))return new L.SimulatorState(e.outerContext,s,o,i);let a=s,l=this.getExistingTargetState(a,r);if(null==l){let e=this.computeTargetState(t,a,i,r,o,n);l=e[0],i=e[1]}return l!==p.ATNSimulator.ERROR?(M(!o||!l.configs.dipsIntoOuterContext),new L.SimulatorState(e.outerContext,l,o,i)):void 0}getExistingTargetState(t,e){return t.getTarget(e)}computeTargetState(e,r,n,o,i,s){let a,l,c=r.configs.toArray(),f=new h.ATNConfigSet;do{let e=!i||null!=n;e||(f.isOutermostConfigSet=!0);let r,p=new h.ATNConfigSet;for(let e of c){if(t.debug&&console.log("testing "+this.getTokenName(o)+" at "+e.toString()),e.state instanceof R.RuleStopState){M(e.context.isEmpty),(i&&!e.reachesIntoOuterContext||o===N.IntStream.EOF)&&(null==r&&(r=[]),r.push(e));continue}let n=e.state.numberOfOptimizedTransitions;for(let t=0;t<n;t++){let r=e.state.getOptimizedTransition(t),n=this.getReachableTarget(e,r,o);null!=n&&p.add(e.transform(n,!1),s)}}if(this.optimize_unique_closure&&null==r&&o!==j.Token.EOF&&p.uniqueAlt!==u.ATN.INVALID_ALT_NUMBER){p.isOutermostConfigSet=f.isOutermostConfigSet,f=p;break}let d=!1,y=o===j.Token.EOF;if(this.closure(p,f,d,e,s,y),l=f.dipsIntoOuterContext,o===N.IntStream.EOF&&(f=this.removeAllConfigsNotInRuleStopState(f,s)),!(null==r||i&&C.PredictionMode.hasConfigInRuleStopState(f))){M(r.length>0);for(let t of r)f.add(t,s)}if(i&&l){f.clear(),n=n,n=this.skipTailCalls(n);let t=this.getReturnState(n);if(null==a&&(a=new v.IntegerList),n=n.isEmpty?void 0:n.parent,a.add(t),t!==A.PredictionContext.EMPTY_FULL_STATE_KEY)for(let e=0;e<c.length;e++)c[e]=c[e].appendContext(t,s)}}while(i&&l);return f.isEmpty?(this.setDFAEdge(r,o,p.ATNSimulator.ERROR),[p.ATNSimulator.ERROR,n]):[this.addDFAEdge(e,r,o,a,f,s),n]}removeAllConfigsNotInRuleStopState(t,e){if(C.PredictionMode.allConfigsInRuleStopStates(t))return t;let r=new h.ATNConfigSet;for(let n of t)n.state instanceof R.RuleStopState&&r.add(n,e);return r}computeStartState(t,e,r){let n=t.isPrecedenceDfa?t.getPrecedenceStartState(this._parser.precedence,r):r?t.s0full:t.s0;if(null!=n){if(!r)return new L.SimulatorState(e,n,r,e);n.setContextSensitive(this.atn)}t.decision;let o=t.atnStartState,i=0,s=e,a=r?A.PredictionContext.EMPTY_FULL:A.PredictionContext.EMPTY_LOCAL,l=new P.PredictionContextCache;if(r){if(!this.enable_global_context_dfa)for(;null!=s;)s.isEmpty?(i=A.PredictionContext.EMPTY_FULL_STATE_KEY,s=void 0):(i=this.getReturnState(s),a=a.appendSingleContext(i,l),s=s.parent);for(;null!=n&&n.isContextSensitive&&null!=s;){let t;if(s=this.skipTailCalls(s),s.isEmpty?(t=n.getContextTarget(A.PredictionContext.EMPTY_FULL_STATE_KEY),i=A.PredictionContext.EMPTY_FULL_STATE_KEY,s=void 0):(i=this.getReturnState(s),t=n.getContextTarget(i),a=a.appendSingleContext(i,l),s=s.parent),null==t)break;n=t}}if(null!=n&&!n.isContextSensitive)return new L.SimulatorState(e,n,r,s);let u=new h.ATNConfigSet;for(;;){let p=new h.ATNConfigSet,f=o.numberOfTransitions;for(let t=0;t<f;t++){let e=o.transition(t).target;p.add(c.ATNConfig.create(e,t+1,a))}let d=null!=s;d||(u.isOutermostConfigSet=!0);let y=!0;this.closure(p,u,y,d,l,!1);let g,_=u.dipsIntoOuterContext;if(r&&!this.enable_global_context_dfa){n=this.addDFAState(t,u,l);break}if(null==n?t.isPrecedenceDfa?(u=this.applyPrecedenceFilter(u,e,l),g=this.addDFAState(t,u,l),t.setPrecedenceStartState(this._parser.precedence,r,g)):(g=this.addDFAState(t,u,l),r?t.s0full?g=t.s0full:t.s0full=g:t.s0?g=t.s0:t.s0=g):(t.isPrecedenceDfa&&(u=this.applyPrecedenceFilter(u,e,l)),g=this.addDFAState(t,u,l),n.setContextTarget(i,g)),n=g,!r||!_)break;g.setContextSensitive(this.atn),s=s,u.clear(),s=this.skipTailCalls(s);let m=this.getReturnState(s);s=s.isEmpty?void 0:s.parent,m!==A.PredictionContext.EMPTY_FULL_STATE_KEY&&(a=a.appendSingleContext(m,l)),i=m}return new L.SimulatorState(e,n,r,s)}applyPrecedenceFilter(t,e,r){let n=new Map,o=new h.ATNConfigSet;for(let i of t){if(1!==i.alt)continue;let t=i.semanticContext.evalPrecedence(this._parser,e);null!=t&&(n.set(i.state.stateNumber,i.context),t!==i.semanticContext?o.add(i.transform(i.state,!1,t),r):o.add(i,r))}for(let e of t)if(1!==e.alt){if(!e.isPrecedenceFilterSuppressed){let t=n.get(e.state.stateNumber);if(null!=t&&t.equals(e.context))continue}o.add(e,r)}return o}getReachableTarget(t,e,r){if(e.matches(r,0,this.atn.maxTokenType))return e.target}predicateDFAState(e,r,n){let o=this.getConflictingAltsFromConfigSet(r);if(!o)throw new Error("This unhandled scenario is intended to be unreachable, but I'm currently not sure of why we know that's the case.");t.debug&&console.log("predicateDFAState "+e);let i,s=this.getPredsForAmbigAlts(o,r,n);return null!=s&&(i=this.getPredicatePredictions(o,s),e.predicates=i),i}getPredsForAmbigAlts(e,r,n){let o=new Array(n+1),i=o.length;for(let t of r)e.get(t.alt)&&(o[t.alt]=w.SemanticContext.or(o[t.alt],t.semanticContext));let s=0;for(let t=0;t<i;t++)null==o[t]?o[t]=w.SemanticContext.NONE:o[t]!==w.SemanticContext.NONE&&s++;let a=o;return 0===s&&(a=void 0),t.debug&&console.log("getPredsForAmbigAlts result "+(a?l.Arrays.toString(a):"undefined")),a}getPredicatePredictions(t,e){let r=[],n=!1;for(let o=1;o<e.length;o++){let i=e[o];M(null!=i),null!=t&&t.get(o)&&i===w.SemanticContext.NONE?r.push(new m.DFAState.PredPrediction(i,o)):i!==w.SemanticContext.NONE&&(n=!0,r.push(new m.DFAState.PredPrediction(i,o)))}if(n)return r}evalSemanticContext(e,r,n){let o=new y.BitSet;for(let i of e){if(i.pred===w.SemanticContext.NONE){if(o.set(i.alt),!n)break;continue}let e=this.evalSemanticContextImpl(i.pred,r,i.alt);if((t.debug||t.dfa_debug)&&console.log("eval pred "+i+"="+e),e&&((t.debug||t.dfa_debug)&&console.log("PREDICT "+i.alt),o.set(i.alt),!n))break}return o}evalSemanticContextImpl(t,e,r){return t.eval(this._parser,e)}closure(t,e,r,n,o,i){null==o&&(o=P.PredictionContextCache.UNCACHED);let s=t,l=new a.Array2DHashSet(O.ObjectEqualityComparator.INSTANCE);for(;s.size>0;){let t=new h.ATNConfigSet;for(let a of s)this.closureImpl(a,e,t,l,r,n,o,0,i);s=t}}closureImpl(e,r,n,o,i,a,l,u,h){if(t.debug&&console.log("closure("+e.toString(this._parser,!0)+")"),e.state instanceof R.RuleStopState)if(e.context.isEmpty){if(!a)return void r.add(e,l);t.debug&&console.log("FALLING off rule "+this.getRuleName(e.state.ruleIndex)),e.context===A.PredictionContext.EMPTY_FULL?e=e.transform(e.state,!1,A.PredictionContext.EMPTY_LOCAL):!e.reachesIntoOuterContext&&A.PredictionContext.isEmptyLocal(e.context)&&r.add(e,l)}else{let t=e.context.hasEmpty,s=e.context.size-(t?1:0);for(let t=0;t<s;t++){let s=e.context.getParent(t),p=this.atn.states[e.context.getReturnState(t)],f=c.ATNConfig.create(p,e.alt,s,e.semanticContext);f.outerContextDepth=e.outerContextDepth,f.isPrecedenceFilterSuppressed=e.isPrecedenceFilterSuppressed,M(u>F),this.closureImpl(f,r,n,o,i,a,l,u-1,h)}if(!t||!a)return;e=e.transform(e.state,!1,A.PredictionContext.EMPTY_LOCAL)}let p=e.state;p.onlyHasEpsilonTransitions||(r.add(e,l),t.debug&&console.log("added config "+r));for(let c=0;c<p.numberOfOptimizedTransitions;c++){if(0===c&&p.stateType===f.ATNStateType.STAR_LOOP_ENTRY&&p.precedenceRuleDecision&&!e.context.hasEmpty){let t=p,r=!0;for(let n=0;n<e.context.size;n++)if(!t.precedenceLoopbackStates.get(e.context.getReturnState(n))){r=!1;break}if(r)continue}let d=p.getOptimizedTransition(c),y=!(d instanceof s.ActionTransition)&&i,g=this.getEpsilonTarget(e,d,y,0===u,l,h);if(null!=g){if(d instanceof I.RuleTransition&&null!=n&&!i){n.add(g,l);continue}let s=u;if(e.state instanceof R.RuleStopState){if(null!=this.dfa&&this.dfa.isPrecedenceDfa){d.outermostPrecedenceReturn===this.dfa.atnStartState.ruleIndex&&(g.isPrecedenceFilterSuppressed=!0)}if(g.outerContextDepth=g.outerContextDepth+1,!o.add(g))continue;M(s>F),s--,t.debug&&console.log("dips into outer ctx: "+g)}else if(d instanceof I.RuleTransition)!this.optimize_tail_calls||!d.optimizedTailCall||this.tail_call_preserves_sll&&A.PredictionContext.isEmptyLocal(e.context)?s>=0&&s++:(M(g.context===e.context),0===s&&(s--,!this.tail_call_preserves_sll&&A.PredictionContext.isEmptyLocal(e.context)&&(g.outerContextDepth=g.outerContextDepth+1)));else if(!d.isEpsilon&&!o.add(g))continue;this.closureImpl(g,r,n,o,y,a,l,s,h)}}}getRuleName(t){return null!=this._parser&&t>=0?this._parser.ruleNames[t]:"<rule "+t+">"}getEpsilonTarget(t,e,r,n,o,i){switch(e.serializationType){case 3:return this.ruleTransition(t,e,o);case 10:return this.precedenceTransition(t,e,r,n);case 4:return this.predTransition(t,e,r,n);case 6:return this.actionTransition(t,e);case 1:return t.transform(e.target,!1);case 5:case 2:case 7:return i&&e.matches(j.Token.EOF,0,1)?t.transform(e.target,!1):void 0;default:return}}actionTransition(e,r){return t.debug&&console.log("ACTION edge "+r.ruleIndex+":"+r.actionIndex),e.transform(r.target,!1)}precedenceTransition(e,r,n,o){let i;if(t.debug&&(console.log("PRED (collectPredicates="+n+") "+r.precedence+">=_p, ctx dependent=true"),null!=this._parser&&console.log("context surrounding pred is "+this._parser.getRuleInvocationStack())),n&&o){let t=w.SemanticContext.and(e.semanticContext,r.predicate);i=e.transform(r.target,!1,t)}else i=e.transform(r.target,!1);return t.debug&&console.log("config from pred transition="+i),i}predTransition(e,r,n,o){let i;if(t.debug&&(console.log("PRED (collectPredicates="+n+") "+r.ruleIndex+":"+r.predIndex+", ctx dependent="+r.isCtxDependent),null!=this._parser&&console.log("context surrounding pred is "+this._parser.getRuleInvocationStack())),n&&(!r.isCtxDependent||r.isCtxDependent&&o)){let t=w.SemanticContext.and(e.semanticContext,r.predicate);i=e.transform(r.target,!1,t)}else i=e.transform(r.target,!1);return t.debug&&console.log("config from pred transition="+i),i}ruleTransition(e,r,n){t.debug&&console.log("CALL rule "+this.getRuleName(r.target.ruleIndex)+", ctx="+e.context);let o,i=r.followState;return o=!this.optimize_tail_calls||!r.optimizedTailCall||this.tail_call_preserves_sll&&A.PredictionContext.isEmptyLocal(e.context)?null!=n?n.getChild(e.context,i.stateNumber):e.context.getChild(i.stateNumber):e.context,e.transform(r.target,!1,o)}isConflicted(e,r){if(e.uniqueAlt!==u.ATN.INVALID_ALT_NUMBER||e.size<=1)return;let n=e.toArray();n.sort(t.STATE_ALT_SORT_COMPARATOR);let o=!e.dipsIntoOuterContext,i=new y.BitSet,s=n[0].alt;i.set(s);let a,l=n[0].state.nonStopStateNumber;for(let t of n){let e=t.state.nonStopStateNumber;if(e!==l){if(t.alt!==s)return;l=e}}if(o){l=n[0].state.nonStopStateNumber,a=new y.BitSet;let t=s;for(let e of n){if(e.state.nonStopStateNumber!==l)break;let r=e.alt;a.set(r),t=r}l=n[0].state.nonStopStateNumber;let e=s;for(let r of n){let n=r.state.nonStopStateNumber,i=r.alt;if(n!==l){if(e!==t){o=!1;break}l=n,e=s}else if(i!==e){if(i!==a.nextSetBit(e+1)){o=!1;break}e=i}}}l=n[0].state.nonStopStateNumber;let c=0,h=0,p=n[0].context;for(let t=1;t<n.length;t++){let e=n[t];if(e.alt!==s)break;if(e.state.nonStopStateNumber!==l)break;h=t,p=r.join(p,n[t].context)}for(let t=h+1;t<n.length;t++){let e=n[t],a=e.state;if(i.set(e.alt),a.nonStopStateNumber!==l){l=a.nonStopStateNumber,c=t,h=t,p=e.context;for(let t=c+1;t<n.length;t++){let e=n[t];if(e.alt!==s)break;if(e.state.nonStopStateNumber!==l)break;h=t,p=r.join(p,e.context)}t=h;continue}let u=e.context,f=e.alt,d=t;for(let t=d+1;t<n.length;t++){let e=n[t];if(e.alt!==f)break;if(e.state.nonStopStateNumber!==l)break;d=t,u=r.join(u,e.context)}t=d;let y=r.join(p,u);if(!p.equals(y))return;o=o&&p.equals(u)}return new g.ConflictInfo(i,o)}getConflictingAltsFromConfigSet(t){let e=t.conflictingAlts;return null==e&&t.uniqueAlt!==u.ATN.INVALID_ALT_NUMBER&&(e=new y.BitSet,e.set(t.uniqueAlt)),e}getTokenName(t){if(t===j.Token.EOF)return"EOF";let e=(null!=this._parser?this._parser.vocabulary:D.VocabularyImpl.EMPTY_VOCABULARY).getDisplayName(t);return e===String(t)?e:e+"<"+t+">"}getLookaheadName(t){return this.getTokenName(t.LA(1))}dumpDeadEndConfigs(t){console.log("dead end configs: ");let e=t.deadEndConfigs;if(e)for(let t of e){let e="no edges";if(t.state.numberOfOptimizedTransitions>0){let r=t.state.getOptimizedTransition(0);if(r instanceof d.AtomTransition)e="Atom "+this.getTokenName(r._label);else if(r instanceof k.SetTransition){e=(r instanceof T.NotSetTransition?"~":"")+"Set "+r.set.toString()}}console.log(t.toString(this._parser,!0)+":"+e)}}noViableAlt(t,e,r,n){return new b.NoViableAltException(this._parser,t,t.get(n),t.LT(1),r,e)}getUniqueAlt(t){let e=u.ATN.INVALID_ALT_NUMBER;for(let r of t)if(e===u.ATN.INVALID_ALT_NUMBER)e=r.alt;else if(r.alt!==e)return u.ATN.INVALID_ALT_NUMBER;return e}configWithAltAtStopState(t,e){for(let r of t)if(r.alt===e&&r.state instanceof R.RuleStopState)return!0;return!1}addDFAEdge(e,r,n,o,i,s){M(null==o||o.isEmpty||e.isContextSensitive);let a=r,l=this.addDFAState(e,i,s);if(null!=o)for(let t of o.toArray()){if(t===A.PredictionContext.EMPTY_FULL_STATE_KEY&&a.configs.isOutermostConfigSet)continue;a.setContextSensitive(this.atn),a.setContextSymbol(n);let r=a.getContextTarget(t);null==r?(r=this.addDFAContextState(e,a.configs,t,s),M(t!==A.PredictionContext.EMPTY_FULL_STATE_KEY||r.configs.isOutermostConfigSet),a.setContextTarget(t,r),a=r):a=r}return t.debug&&console.log("EDGE "+a+" -> "+l+" upon "+this.getTokenName(n)),this.setDFAEdge(a,n,l),t.debug&&console.log("DFA=\n"+e.toString(null!=this._parser?this._parser.vocabulary:D.VocabularyImpl.EMPTY_VOCABULARY,null!=this._parser?this._parser.ruleNames:void 0)),l}setDFAEdge(t,e,r){null!=t&&t.setTarget(e,r)}addDFAContextState(t,e,r,n){if(r!==A.PredictionContext.EMPTY_FULL_STATE_KEY){let o=new h.ATNConfigSet;for(let t of e)o.add(t.appendContext(r,n));return this.addDFAState(t,o,n)}return M(!e.isOutermostConfigSet,"Shouldn't be adding a duplicate edge."),(e=e.clone(!0)).isOutermostConfigSet=!0,this.addDFAState(t,e,n)}addDFAState(e,r,n){let o=this.enable_global_context_dfa||!r.isOutermostConfigSet;if(o){r.isReadOnly||r.optimizeConfigs(this);let t=this.createDFAState(e,r),n=e.states.get(t);if(null!=n)return n}r.isReadOnly||null==r.conflictInfo&&(r.conflictInfo=this.isConflicted(r,n));let s=this.createDFAState(e,r.clone(!0)),a=this.atn.getDecisionState(e.decision),l=this.getUniqueAlt(r);if(l!==u.ATN.INVALID_ALT_NUMBER)s.acceptStateInfo=new i.AcceptStateInfo(l);else if(null!=r.conflictingAlts){let t=r.conflictingAlts;t&&(s.acceptStateInfo=new i.AcceptStateInfo(t.nextSetBit(0)))}if(s.isAcceptState&&r.hasSemanticContext&&this.predicateDFAState(s,r,a.numberOfTransitions),!o)return s;let c=e.addState(s);return t.debug&&c===s&&console.log("adding new DFA state: "+s),c}createDFAState(t,e){return new m.DFAState(e)}reportAttemptingFullContext(e,r,n,o,i){if(t.debug||t.retry_debug){let t=S.Interval.of(o,i);console.log("reportAttemptingFullContext decision="+e.decision+":"+n.s0.configs+", input="+this._parser.inputStream.getText(t))}if(null!=this._parser){let t=this._parser.getErrorListenerDispatch();t.reportAttemptingFullContext&&t.reportAttemptingFullContext(this._parser,e,o,i,r,n)}}reportContextSensitivity(e,r,n,o,i){if(t.debug||t.retry_debug){let t=S.Interval.of(o,i);console.log("reportContextSensitivity decision="+e.decision+":"+n.s0.configs+", input="+this._parser.inputStream.getText(t))}if(null!=this._parser){let t=this._parser.getErrorListenerDispatch();t.reportContextSensitivity&&t.reportContextSensitivity(this._parser,e,o,i,r,n)}}reportAmbiguity(e,r,n,o,i,s,a){if(t.debug||t.retry_debug){let t=S.Interval.of(n,o);console.log("reportAmbiguity "+s+":"+a+", input="+this._parser.inputStream.getText(t))}if(null!=this._parser){let t=this._parser.getErrorListenerDispatch();t.reportAmbiguity&&t.reportAmbiguity(this._parser,e,n,o,i,s,a)}}getReturnState(t){if(t.isEmpty)return A.PredictionContext.EMPTY_FULL_STATE_KEY;return this.atn.states[t.invokingState].transition(0).followState.stateNumber}skipTailCalls(t){if(!this.optimize_tail_calls)return t;for(;!t.isEmpty;){let e=this.atn.states[t.invokingState];if(M(1===e.numberOfTransitions&&3===e.transition(0).serializationType),!e.transition(0).tailCall)break;t=t.parent}return t}get parser(){return this._parser}};z.debug=!1,z.dfa_debug=!1,z.retry_debug=!1,z.STATE_ALT_SORT_COMPARATOR=(t,e)=>{let r=t.state.nonStopStateNumber-e.state.nonStopStateNumber;return 0!==r?r:(r=t.alt-e.alt,0!==r?r:0)},n([x.NotNull],z.prototype,"predictionMode",void 0),n([x.NotNull],z.prototype,"getPredictionMode",null),n([o(0,x.NotNull)],z.prototype,"setPredictionMode",null),n([x.Override],z.prototype,"reset",null),n([o(0,x.NotNull)],z.prototype,"adaptivePredict",null),n([o(0,x.NotNull),o(1,x.NotNull),o(2,x.NotNull)],z.prototype,"getStartState",null),n([o(0,x.NotNull),o(1,x.NotNull),o(3,x.NotNull)],z.prototype,"execDFA",null),n([o(0,x.NotNull),o(1,x.NotNull),o(3,x.NotNull)],z.prototype,"execATN",null),n([o(0,x.NotNull),o(2,x.NotNull)],z.prototype,"handleNoViableAlt",null),n([o(0,x.NotNull)],z.prototype,"getExistingTargetState",null),n([x.NotNull,o(0,x.NotNull),o(1,x.NotNull)],z.prototype,"computeTargetState",null),n([x.NotNull,o(0,x.NotNull)],z.prototype,"removeAllConfigsNotInRuleStopState",null),n([x.NotNull],z.prototype,"computeStartState",null),n([x.NotNull,o(0,x.NotNull)],z.prototype,"applyPrecedenceFilter",null),n([o(0,x.NotNull),o(1,x.NotNull)],z.prototype,"getReachableTarget",null),n([o(0,x.NotNull),o(1,x.NotNull)],z.prototype,"getPredsForAmbigAlts",null),n([o(0,x.NotNull)],z.prototype,"evalSemanticContext",null),n([o(0,x.NotNull)],z.prototype,"evalSemanticContextImpl",null),n([o(1,x.NotNull),o(4,x.Nullable)],z.prototype,"closure",null),n([o(0,x.NotNull),o(1,x.NotNull),o(2,x.Nullable),o(3,x.NotNull),o(6,x.NotNull)],z.prototype,"closureImpl",null),n([x.NotNull],z.prototype,"getRuleName",null),n([o(0,x.NotNull),o(1,x.NotNull)],z.prototype,"getEpsilonTarget",null),n([x.NotNull,o(0,x.NotNull),o(1,x.NotNull)],z.prototype,"actionTransition",null),n([x.Nullable,o(0,x.NotNull),o(1,x.NotNull)],z.prototype,"precedenceTransition",null),n([x.Nullable,o(0,x.NotNull),o(1,x.NotNull)],z.prototype,"predTransition",null),n([x.NotNull,o(0,x.NotNull),o(1,x.NotNull),o(2,x.Nullable)],z.prototype,"ruleTransition",null),n([o(0,x.NotNull)],z.prototype,"isConflicted",null),n([x.NotNull],z.prototype,"getTokenName",null),n([o(0,x.NotNull)],z.prototype,"dumpDeadEndConfigs",null),n([x.NotNull,o(0,x.NotNull),o(1,x.NotNull),o(2,x.NotNull)],z.prototype,"noViableAlt",null),n([o(0,x.NotNull)],z.prototype,"getUniqueAlt",null),n([o(0,x.NotNull)],z.prototype,"configWithAltAtStopState",null),n([x.NotNull,o(0,x.NotNull),o(1,x.NotNull),o(4,x.NotNull)],z.prototype,"addDFAEdge",null),n([o(0,x.Nullable),o(2,x.Nullable)],z.prototype,"setDFAEdge",null),n([x.NotNull,o(0,x.NotNull),o(1,x.NotNull)],z.prototype,"addDFAContextState",null),n([x.NotNull,o(0,x.NotNull),o(1,x.NotNull)],z.prototype,"addDFAState",null),n([x.NotNull,o(0,x.NotNull),o(1,x.NotNull)],z.prototype,"createDFAState",null),n([o(0,x.NotNull),o(2,x.NotNull)],z.prototype,"reportAttemptingFullContext",null),n([o(0,x.NotNull),o(2,x.NotNull)],z.prototype,"reportContextSensitivity",null),n([o(0,x.NotNull),o(5,x.NotNull),o(6,x.NotNull)],z.prototype,"reportAmbiguity",null),z=n([o(0,x.NotNull)],z),e.ParserATNSimulator=z},4604:function(t,e,r){"use strict";var n=this&&this.__decorate||function(t,e,r,n){var o,i=arguments.length,s=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,r,n);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(i<3?o(s):i>3?o(e,r,s):o(e,r))||s);return i>3&&s&&Object.defineProperty(e,r,s),s};Object.defineProperty(e,"__esModule",{value:!0}),e.PlusBlockStartState=void 0;const o=r(4700),i=r(4136),s=r(8042);class a extends i.BlockStartState{get stateType(){return o.ATNStateType.PLUS_BLOCK_START}}n([s.Override],a.prototype,"stateType",null),e.PlusBlockStartState=a},8080:function(t,e,r){"use strict";var n=this&&this.__decorate||function(t,e,r,n){var o,i=arguments.length,s=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,r,n);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(i<3?o(s):i>3?o(e,r,s):o(e,r))||s);return i>3&&s&&Object.defineProperty(e,r,s),s};Object.defineProperty(e,"__esModule",{value:!0}),e.PlusLoopbackState=void 0;const o=r(4700),i=r(213),s=r(8042);class a extends i.DecisionState{get stateType(){return o.ATNStateType.PLUS_LOOP_BACK}}n([s.Override],a.prototype,"stateType",null),e.PlusLoopbackState=a},4649:function(t,e,r){"use strict";var n=this&&this.__decorate||function(t,e,r,n){var o,i=arguments.length,s=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,r,n);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(i<3?o(s):i>3?o(e,r,s):o(e,r))||s);return i>3&&s&&Object.defineProperty(e,r,s),s},o=this&&this.__param||function(t,e){return function(r,n){e(r,n,t)}};Object.defineProperty(e,"__esModule",{value:!0}),e.PrecedencePredicateTransition=void 0;const i=r(7520),s=r(8042),a=r(1888);let l=class extends i.AbstractPredicateTransition{constructor(t,e){super(t),this.precedence=e}get serializationType(){return 10}get isEpsilon(){return!0}matches(t,e,r){return!1}get predicate(){return new a.SemanticContext.PrecedencePredicate(this.precedence)}toString(){return this.precedence+" >= _p"}};n([s.Override],l.prototype,"serializationType",null),n([s.Override],l.prototype,"isEpsilon",null),n([s.Override],l.prototype,"matches",null),n([s.Override],l.prototype,"toString",null),l=n([o(0,s.NotNull)],l),e.PrecedencePredicateTransition=l},5814:function(t,e,r){"use strict";var n=this&&this.__decorate||function(t,e,r,n){var o,i=arguments.length,s=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,r,n);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(i<3?o(s):i>3?o(e,r,s):o(e,r))||s);return i>3&&s&&Object.defineProperty(e,r,s),s},o=this&&this.__param||function(t,e){return function(r,n){e(r,n,t)}};Object.defineProperty(e,"__esModule",{value:!0}),e.PredicateEvalInfo=void 0;const i=r(112),s=r(8042);let a=class extends i.DecisionEventInfo{constructor(t,e,r,n,o,i,s,a){super(e,t,r,n,o,t.useContext),this.semctx=i,this.evalResult=s,this.predictedAlt=a}};a=n([o(0,s.NotNull),o(2,s.NotNull),o(5,s.NotNull)],a),e.PredicateEvalInfo=a},3233:function(t,e,r){"use strict";var n=this&&this.__decorate||function(t,e,r,n){var o,i=arguments.length,s=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,r,n);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(i<3?o(s):i>3?o(e,r,s):o(e,r))||s);return i>3&&s&&Object.defineProperty(e,r,s),s},o=this&&this.__param||function(t,e){return function(r,n){e(r,n,t)}};Object.defineProperty(e,"__esModule",{value:!0}),e.PredicateTransition=void 0;const i=r(7520),s=r(8042),a=r(1888);let l=class extends i.AbstractPredicateTransition{constructor(t,e,r,n){super(t),this.ruleIndex=e,this.predIndex=r,this.isCtxDependent=n}get serializationType(){return 4}get isEpsilon(){return!0}matches(t,e,r){return!1}get predicate(){return new a.SemanticContext.Predicate(this.ruleIndex,this.predIndex,this.isCtxDependent)}toString(){return"pred_"+this.ruleIndex+":"+this.predIndex}};n([s.Override],l.prototype,"serializationType",null),n([s.Override],l.prototype,"isEpsilon",null),n([s.Override],l.prototype,"matches",null),n([s.Override,s.NotNull],l.prototype,"toString",null),l=n([o(0,s.NotNull)],l),e.PredicateTransition=l},9767:function(t,e,r){"use strict";var n=this&&this.__decorate||function(t,e,r,n){var o,i=arguments.length,s=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,r,n);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(i<3?o(s):i>3?o(e,r,s):o(e,r))||s);return i>3&&s&&Object.defineProperty(e,r,s),s},o=this&&this.__param||function(t,e){return function(r,n){e(r,n,t)}};Object.defineProperty(e,"__esModule",{value:!0}),e.SingletonPredictionContext=e.PredictionContext=void 0;const i=r(3874),s=r(9026),a=r(6005),l=r(3943),u=r(8042),c=r(604),h=r(9282);class p{constructor(t){this.cachedHashCode=t}static calculateEmptyHashCode(){let t=l.MurmurHash.initialize(1);return t=l.MurmurHash.finish(t,0),t}static calculateSingleHashCode(t,e){let r=l.MurmurHash.initialize(1);return r=l.MurmurHash.update(r,t),r=l.MurmurHash.update(r,e),r=l.MurmurHash.finish(r,2),r}static calculateHashCode(t,e){let r=l.MurmurHash.initialize(1);for(let e of t)r=l.MurmurHash.update(r,e);for(let t of e)r=l.MurmurHash.update(r,t);return r=l.MurmurHash.finish(r,2*t.length),r}static fromRuleContext(t,e,r=!0){if(e.isEmpty)return r?p.EMPTY_FULL:p.EMPTY_LOCAL;let n;n=e._parent?p.fromRuleContext(t,e._parent,r):r?p.EMPTY_FULL:p.EMPTY_LOCAL;let o=t.states[e.invokingState].transition(0);return n.getChild(o.followState.stateNumber)}static addEmptyContext(t){return t.addEmptyContext()}static removeEmptyContext(t){return t.removeEmptyContext()}static join(t,e,r=c.PredictionContextCache.UNCACHED){if(t===e)return t;if(t.isEmpty)return p.isEmptyLocal(t)?t:p.addEmptyContext(e);if(e.isEmpty)return p.isEmptyLocal(e)?e:p.addEmptyContext(t);let n=t.size,o=e.size;if(1===n&&1===o&&t.getReturnState(0)===e.getReturnState(0)){let n=r.join(t.getParent(0),e.getParent(0));return n===t.getParent(0)?t:n===e.getParent(0)?e:n.getChild(t.getReturnState(0))}let i=0,s=new Array(n+o),a=new Array(s.length),l=0,u=0,f=!0,g=!0;for(;l<n&&u<o;)t.getReturnState(l)===e.getReturnState(u)?(s[i]=r.join(t.getParent(l),e.getParent(u)),a[i]=t.getReturnState(l),f=f&&s[i]===t.getParent(l),g=g&&s[i]===e.getParent(u),l++,u++):t.getReturnState(l)<e.getReturnState(u)?(s[i]=t.getParent(l),a[i]=t.getReturnState(l),g=!1,l++):(h(e.getReturnState(u)<t.getReturnState(l)),s[i]=e.getParent(u),a[i]=e.getReturnState(u),f=!1,u++),i++;for(;l<n;)s[i]=t.getParent(l),a[i]=t.getReturnState(l),l++,g=!1,i++;for(;u<o;)s[i]=e.getParent(u),a[i]=e.getReturnState(u),u++,f=!1,i++;return f?t:g?e:(i<s.length&&(s=s.slice(0,i),a=a.slice(0,i)),0===s.length?p.EMPTY_FULL:1===s.length?new y(s[0],a[0]):new d(s,a))}static isEmptyLocal(t){return t===p.EMPTY_LOCAL}static getCachedContext(t,e,r){if(t.isEmpty)return t;let n=r.get(t);if(n)return n;if(n=e.get(t),n)return r.put(t,n),n;let o,i=!1,s=new Array(t.size);for(let n=0;n<s.length;n++){let o=p.getCachedContext(t.getParent(n),e,r);if(i||o!==t.getParent(n)){if(!i){s=new Array(t.size);for(let e=0;e<t.size;e++)s[e]=t.getParent(e);i=!0}s[n]=o}}if(!i)return n=e.putIfAbsent(t,t),r.put(t,null!=n?n:t),t;if(1===s.length)o=new y(s[0],t.getReturnState(0));else{let e=new Array(t.size);for(let r=0;r<t.size;r++)e[r]=t.getReturnState(r);o=new d(s,e,t.hashCode())}return n=e.putIfAbsent(o,o),r.put(o,n||o),r.put(t,n||o),o}appendSingleContext(t,e){return this.appendContext(p.EMPTY_FULL.getChild(t),e)}getChild(t){return new y(this,t)}hashCode(){return this.cachedHashCode}toStrings(t,e,r=p.EMPTY_FULL){let n=[];t:for(let o=0;;o++){let i=0,s=!0,a=this,l=e,u="";for(u+="[";!a.isEmpty&&a!==r;){let e=0;if(a.size>0){let t=1;for(;1<<t>>>0<a.size;)t++;if(e=o>>i&(1<<t>>>0)-1,s=s&&e>=a.size-1,e>=a.size)continue t;i+=t}if(t){u.length>1&&(u+=" ");let e=t.atn.states[l];u+=t.ruleNames[e.ruleIndex]}else a.getReturnState(e)!==p.EMPTY_FULL_STATE_KEY&&(a.isEmpty||(u.length>1&&(u+=" "),u+=a.getReturnState(e)));l=a.getReturnState(e),a=a.getParent(e)}if(u+="]",n.push(u),s)break}return n}}n([u.Override],p.prototype,"hashCode",null),n([o(0,u.NotNull),o(1,u.NotNull),o(2,u.NotNull)],p,"join",null),n([o(0,u.NotNull),o(1,u.NotNull),o(2,u.NotNull)],p,"getCachedContext",null),e.PredictionContext=p;class f extends p{constructor(t){super(p.calculateEmptyHashCode()),this.fullContext=t}get isFullContext(){return this.fullContext}addEmptyContext(){return this}removeEmptyContext(){throw new Error("Cannot remove the empty context from itself.")}getParent(t){throw new Error("index out of bounds")}getReturnState(t){throw new Error("index out of bounds")}findReturnState(t){return-1}get size(){return 0}appendSingleContext(t,e){return e.getChild(this,t)}appendContext(t,e){return t}get isEmpty(){return!0}get hasEmpty(){return!0}equals(t){return this===t}toStrings(t,e,r){return["[]"]}}n([u.Override],f.prototype,"addEmptyContext",null),n([u.Override],f.prototype,"removeEmptyContext",null),n([u.Override],f.prototype,"getParent",null),n([u.Override],f.prototype,"getReturnState",null),n([u.Override],f.prototype,"findReturnState",null),n([u.Override],f.prototype,"size",null),n([u.Override],f.prototype,"appendSingleContext",null),n([u.Override],f.prototype,"appendContext",null),n([u.Override],f.prototype,"isEmpty",null),n([u.Override],f.prototype,"hasEmpty",null),n([u.Override],f.prototype,"equals",null),n([u.Override],f.prototype,"toStrings",null);let d=class t extends p{constructor(t,e,r){super(r||p.calculateHashCode(t,e)),h(t.length===e.length),h(e.length>1||e[0]!==p.EMPTY_FULL_STATE_KEY,"Should be using PredictionContext.EMPTY instead."),this.parents=t,this.returnStates=e}getParent(t){return this.parents[t]}getReturnState(t){return this.returnStates[t]}findReturnState(t){return a.Arrays.binarySearch(this.returnStates,t)}get size(){return this.returnStates.length}get isEmpty(){return!1}get hasEmpty(){return this.returnStates[this.returnStates.length-1]===p.EMPTY_FULL_STATE_KEY}addEmptyContext(){if(this.hasEmpty)return this;let e=this.parents.slice(0),r=this.returnStates.slice(0);return e.push(p.EMPTY_FULL),r.push(p.EMPTY_FULL_STATE_KEY),new t(e,r)}removeEmptyContext(){if(!this.hasEmpty)return this;if(2===this.returnStates.length)return new y(this.parents[0],this.returnStates[0]);{let e=this.parents.slice(0,this.parents.length-1),r=this.returnStates.slice(0,this.returnStates.length-1);return new t(e,r)}}appendContext(e,r){return t.appendContextImpl(this,e,new p.IdentityHashMap)}static appendContextImpl(e,r,n){if(r.isEmpty){if(p.isEmptyLocal(r)){if(e.hasEmpty)return p.EMPTY_LOCAL;throw new Error("what to do here?")}return e}if(1!==r.size)throw new Error("Appending a tree suffix is not yet supported.");let o=n.get(e);if(!o){if(e.isEmpty)o=r;else{let i=e.size;e.hasEmpty&&i--;let s=new Array(i),a=new Array(i);for(let t=0;t<i;t++)a[t]=e.getReturnState(t);for(let o=0;o<i;o++)s[o]=t.appendContextImpl(e.getParent(o),r,n);1===s.length?o=new y(s[0],a[0]):(h(s.length>1),o=new t(s,a)),e.hasEmpty&&(o=p.join(o,r))}n.put(e,o)}return o}equals(e){if(this===e)return!0;if(!(e instanceof t))return!1;if(this.hashCode()!==e.hashCode())return!1;let r=e;return this.equalsImpl(r,new s.Array2DHashSet)}equalsImpl(t,e){let r=[],n=[];for(r.push(this),n.push(t);;){let t=r.pop(),o=n.pop();if(!t||!o)break;let i=new c.PredictionContextCache.IdentityCommutativePredictionContextOperands(t,o);if(!e.add(i))continue;let s=i.x.size;if(0!==s){if(s!==i.y.size)return!1;for(let t=0;t<s;t++){if(i.x.getReturnState(t)!==i.y.getReturnState(t))return!1;let e=i.x.getParent(t),o=i.y.getParent(t);if(e.hashCode()!==o.hashCode())return!1;e!==o&&(r.push(e),n.push(o))}}else if(!i.x.equals(i.y))return!1}return!0}};n([u.NotNull],d.prototype,"parents",void 0),n([u.NotNull],d.prototype,"returnStates",void 0),n([u.Override],d.prototype,"getParent",null),n([u.Override],d.prototype,"getReturnState",null),n([u.Override],d.prototype,"findReturnState",null),n([u.Override],d.prototype,"size",null),n([u.Override],d.prototype,"isEmpty",null),n([u.Override],d.prototype,"hasEmpty",null),n([u.Override],d.prototype,"addEmptyContext",null),n([u.Override],d.prototype,"removeEmptyContext",null),n([u.Override],d.prototype,"appendContext",null),n([u.Override],d.prototype,"equals",null),d=n([o(0,u.NotNull)],d);let y=class t extends p{constructor(t,e){super(p.calculateSingleHashCode(t,e)),this.parent=t,this.returnState=e}getParent(t){return this.parent}getReturnState(t){return this.returnState}findReturnState(t){return this.returnState===t?0:-1}get size(){return 1}get isEmpty(){return!1}get hasEmpty(){return!1}appendContext(t,e){return e.getChild(this.parent.appendContext(t,e),this.returnState)}addEmptyContext(){let t=[this.parent,p.EMPTY_FULL],e=[this.returnState,p.EMPTY_FULL_STATE_KEY];return new d(t,e)}removeEmptyContext(){return this}equals(e){if(e===this)return!0;if(!(e instanceof t))return!1;let r=e;return this.hashCode()===r.hashCode()&&(this.returnState===r.returnState&&this.parent.equals(r.parent))}};n([u.NotNull],y.prototype,"parent",void 0),n([u.Override],y.prototype,"getParent",null),n([u.Override],y.prototype,"getReturnState",null),n([u.Override],y.prototype,"findReturnState",null),n([u.Override],y.prototype,"size",null),n([u.Override],y.prototype,"isEmpty",null),n([u.Override],y.prototype,"hasEmpty",null),n([u.Override],y.prototype,"appendContext",null),n([u.Override],y.prototype,"addEmptyContext",null),n([u.Override],y.prototype,"removeEmptyContext",null),n([u.Override],y.prototype,"equals",null),y=n([o(0,u.NotNull)],y),e.SingletonPredictionContext=y,function(t){t.EMPTY_LOCAL=new f(!1),t.EMPTY_FULL=new f(!0),t.EMPTY_LOCAL_STATE_KEY=-2147483648,t.EMPTY_FULL_STATE_KEY=2147483647;class e extends i.Array2DHashMap{constructor(){super(r.INSTANCE)}}t.IdentityHashMap=e;class r{IdentityEqualityComparator(){}hashCode(t){return t.hashCode()}equals(t,e){return t===e}}r.INSTANCE=new r,n([u.Override],r.prototype,"hashCode",null),n([u.Override],r.prototype,"equals",null),t.IdentityEqualityComparator=r}(p=e.PredictionContext||(e.PredictionContext={}))},604:function(t,e,r){"use strict";var n=this&&this.__decorate||function(t,e,r,n){var o,i=arguments.length,s=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,r,n);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(i<3?o(s):i>3?o(e,r,s):o(e,r))||s);return i>3&&s&&Object.defineProperty(e,r,s),s};Object.defineProperty(e,"__esModule",{value:!0}),e.PredictionContextCache=void 0;const o=r(3874),i=r(8042),s=r(8842),a=r(9767),l=r(9282);class u{constructor(t=!0){this.contexts=new o.Array2DHashMap(s.ObjectEqualityComparator.INSTANCE),this.childContexts=new o.Array2DHashMap(s.ObjectEqualityComparator.INSTANCE),this.joinContexts=new o.Array2DHashMap(s.ObjectEqualityComparator.INSTANCE),this.enableCache=t}getAsCached(t){if(!this.enableCache)return t;let e=this.contexts.get(t);return e||(e=t,this.contexts.put(t,t)),e}getChild(t,e){if(!this.enableCache)return t.getChild(e);let r=new u.PredictionContextAndInt(t,e),n=this.childContexts.get(r);return n||(n=t.getChild(e),n=this.getAsCached(n),this.childContexts.put(r,n)),n}join(t,e){if(!this.enableCache)return a.PredictionContext.join(t,e,this);let r=new u.IdentityCommutativePredictionContextOperands(t,e),n=this.joinContexts.get(r);return n||(n=a.PredictionContext.join(t,e,this),n=this.getAsCached(n),this.joinContexts.put(r,n),n)}}e.PredictionContextCache=u,u.UNCACHED=new u(!1),function(t){class e{constructor(t,e){this.obj=t,this.value=e}equals(t){if(!(t instanceof e))return!1;if(t===this)return!0;let r=t;return this.value===r.value&&(this.obj===r.obj||null!=this.obj&&this.obj.equals(r.obj))}hashCode(){let t=5;return t=7*t+(null!=this.obj?this.obj.hashCode():0),t=7*t+this.value,t}}n([i.Override],e.prototype,"equals",null),n([i.Override],e.prototype,"hashCode",null),t.PredictionContextAndInt=e;class r{constructor(t,e){l(null!=t),l(null!=e),this._x=t,this._y=e}get x(){return this._x}get y(){return this._y}equals(t){if(!(t instanceof r))return!1;if(this===t)return!0;let e=t;return this._x===e._x&&this._y===e._y||this._x===e._y&&this._y===e._x}hashCode(){return this._x.hashCode()^this._y.hashCode()}}n([i.Override],r.prototype,"equals",null),n([i.Override],r.prototype,"hashCode",null),t.IdentityCommutativePredictionContextOperands=r}(u=e.PredictionContextCache||(e.PredictionContextCache={}))},6247:function(t,e,r){"use strict";var n=this&&this.__decorate||function(t,e,r,n){var o,i=arguments.length,s=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,r,n);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(i<3?o(s):i>3?o(e,r,s):o(e,r))||s);return i>3&&s&&Object.defineProperty(e,r,s),s};Object.defineProperty(e,"__esModule",{value:!0}),e.PredictionMode=void 0;const o=r(3874),i=r(3943),s=r(8042),a=r(5337);!function(t){t[t.SLL=0]="SLL",t[t.LL=1]="LL",t[t.LL_EXACT_AMBIG_DETECTION=2]="LL_EXACT_AMBIG_DETECTION"}(e.PredictionMode||(e.PredictionMode={})),function(t){o.Array2DHashMap;class e{AltAndContextConfigEqualityComparator(){}hashCode(t){let e=i.MurmurHash.initialize(7);return e=i.MurmurHash.update(e,t.state.stateNumber),e=i.MurmurHash.update(e,t.context),e=i.MurmurHash.finish(e,2),e}equals(t,e){return t===e||null!=t&&null!=e&&(t.state.stateNumber===e.state.stateNumber&&t.context.equals(e.context))}}e.INSTANCE=new e,n([s.Override],e.prototype,"hashCode",null),n([s.Override],e.prototype,"equals",null),t.hasConfigInRuleStopState=function(t){for(let e of t)if(e.state instanceof a.RuleStopState)return!0;return!1},t.allConfigsInRuleStopStates=function(t){for(let e of t)if(!(e.state instanceof a.RuleStopState))return!1;return!0}}(e.PredictionMode||(e.PredictionMode={}))},2527:function(t,e,r){"use strict";var n=this&&this.__decorate||function(t,e,r,n){var o,i=arguments.length,s=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,r,n);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(i<3?o(s):i>3?o(e,r,s):o(e,r))||s);return i>3&&s&&Object.defineProperty(e,r,s),s},o=this&&this.__param||function(t,e){return function(r,n){e(r,n,t)}};Object.defineProperty(e,"__esModule",{value:!0}),e.ProfilingATNSimulator=void 0;const i=r(1305),s=r(7949),a=r(7643),l=r(8862),u=r(8966),c=r(291),h=r(8042),p=r(5164),f=r(4584),d=r(5814),y=r(1888),g=r(1071);class _ extends f.ParserATNSimulator{constructor(t){super(t.interpreter.atn,t),this._startIndex=0,this._sllStopIndex=0,this._llStopIndex=0,this.currentDecision=0,this.conflictingAltResolvedBySLL=0,this.optimize_ll1=!1,this.reportAmbiguities=!0,this.numDecisions=this.atn.decisionToState.length,this.decisions=[];for(let t=0;t<this.numDecisions;t++)this.decisions.push(new u.DecisionInfo(t))}adaptivePredict(t,e,r,n){if(void 0!==n)return super.adaptivePredict(t,e,r,n);try{this._input=t,this._startIndex=t.index,this._sllStopIndex=this._startIndex-1,this._llStopIndex=-1,this.currentDecision=e,this.currentState=void 0,this.conflictingAltResolvedBySLL=s.ATN.INVALID_ALT_NUMBER;let n=process.hrtime(),o=super.adaptivePredict(t,e,r),i=process.hrtime(),a=1e9*(i[0]-n[0]);0===a?a=i[1]-n[1]:a+=1e9-n[1]+i[1],this.decisions[e].timeInPrediction+=a,this.decisions[e].invocations++;let l=this._sllStopIndex-this._startIndex+1;if(this.decisions[e].SLL_TotalLook+=l,this.decisions[e].SLL_MinLook=0===this.decisions[e].SLL_MinLook?l:Math.min(this.decisions[e].SLL_MinLook,l),l>this.decisions[e].SLL_MaxLook&&(this.decisions[e].SLL_MaxLook=l,this.decisions[e].SLL_MaxLookEvent=new p.LookaheadEventInfo(e,void 0,o,t,this._startIndex,this._sllStopIndex,!1)),this._llStopIndex>=0){let r=this._llStopIndex-this._startIndex+1;this.decisions[e].LL_TotalLook+=r,this.decisions[e].LL_MinLook=0===this.decisions[e].LL_MinLook?r:Math.min(this.decisions[e].LL_MinLook,r),r>this.decisions[e].LL_MaxLook&&(this.decisions[e].LL_MaxLook=r,this.decisions[e].LL_MaxLookEvent=new p.LookaheadEventInfo(e,void 0,o,t,this._startIndex,this._llStopIndex,!0))}return o}finally{this._input=void 0,this.currentDecision=-1}}getStartState(t,e,r,n){let o=super.getStartState(t,e,r,n);return this.currentState=o,o}computeStartState(t,e,r){let n=super.computeStartState(t,e,r);return this.currentState=n,n}computeReachSet(t,e,r,n){if(void 0===this._input)throw new Error("Invalid state");let o=super.computeReachSet(t,e,r,n);return null==o&&this.decisions[this.currentDecision].errors.push(new c.ErrorInfo(this.currentDecision,e,this._input,this._startIndex,this._input.index)),this.currentState=o,o}getExistingTargetState(t,e){if(void 0===this.currentState||void 0===this._input)throw new Error("Invalid state");this.currentState.useContext?this._llStopIndex=this._input.index:this._sllStopIndex=this._input.index;let r=super.getExistingTargetState(t,e);if(null!=r&&(this.currentState=new g.SimulatorState(this.currentState.outerContext,r,this.currentState.useContext,this.currentState.remainingOuterContext),this.currentState.useContext?this.decisions[this.currentDecision].LL_DFATransitions++:this.decisions[this.currentDecision].SLL_DFATransitions++,r===a.ATNSimulator.ERROR)){let e=new g.SimulatorState(this.currentState.outerContext,t,this.currentState.useContext,this.currentState.remainingOuterContext);this.decisions[this.currentDecision].errors.push(new c.ErrorInfo(this.currentDecision,e,this._input,this._startIndex,this._input.index))}return r}computeTargetState(t,e,r,n,o,i){let s=super.computeTargetState(t,e,r,n,o,i);return o?this.decisions[this.currentDecision].LL_ATNTransitions++:this.decisions[this.currentDecision].SLL_ATNTransitions++,s}evalSemanticContextImpl(t,e,r){if(void 0===this.currentState||void 0===this._input)throw new Error("Invalid state");let n=super.evalSemanticContextImpl(t,e,r);if(!(t instanceof y.SemanticContext.PrecedencePredicate)){let e=this._llStopIndex>=0?this._llStopIndex:this._sllStopIndex;this.decisions[this.currentDecision].predicateEvals.push(new d.PredicateEvalInfo(this.currentState,this.currentDecision,this._input,this._startIndex,e,t,n,r))}return n}reportContextSensitivity(t,e,r,n,o){if(void 0===this._input)throw new Error("Invalid state");e!==this.conflictingAltResolvedBySLL&&this.decisions[this.currentDecision].contextSensitivities.push(new l.ContextSensitivityInfo(this.currentDecision,r,this._input,n,o)),super.reportContextSensitivity(t,e,r,n,o)}reportAttemptingFullContext(t,e,r,n,o){this.conflictingAltResolvedBySLL=null!=e?e.nextSetBit(0):r.s0.configs.getRepresentedAlternatives().nextSetBit(0),this.decisions[this.currentDecision].LL_Fallback++,super.reportAttemptingFullContext(t,e,r,n,o)}reportAmbiguity(t,e,r,n,o,a,u){if(void 0===this.currentState||void 0===this._input)throw new Error("Invalid state");let c;c=null!=a?a.nextSetBit(0):u.getRepresentedAlternatives().nextSetBit(0),this.conflictingAltResolvedBySLL!==s.ATN.INVALID_ALT_NUMBER&&c!==this.conflictingAltResolvedBySLL&&this.decisions[this.currentDecision].contextSensitivities.push(new l.ContextSensitivityInfo(this.currentDecision,this.currentState,this._input,r,n)),this.decisions[this.currentDecision].ambiguities.push(new i.AmbiguityInfo(this.currentDecision,this.currentState,a,this._input,r,n)),super.reportAmbiguity(t,e,r,n,o,a,u)}getDecisionInfo(){return this.decisions}getCurrentState(){return this.currentState}}n([h.Override,o(0,h.NotNull)],_.prototype,"adaptivePredict",null),n([h.Override],_.prototype,"getStartState",null),n([h.Override],_.prototype,"computeStartState",null),n([h.Override],_.prototype,"computeReachSet",null),n([h.Override],_.prototype,"getExistingTargetState",null),n([h.Override],_.prototype,"computeTargetState",null),n([h.Override],_.prototype,"evalSemanticContextImpl",null),n([h.Override],_.prototype,"reportContextSensitivity",null),n([h.Override],_.prototype,"reportAttemptingFullContext",null),n([h.Override,o(0,h.NotNull),o(5,h.NotNull),o(6,h.NotNull)],_.prototype,"reportAmbiguity",null),e.ProfilingATNSimulator=_},2751:function(t,e,r){"use strict";var n=this&&this.__decorate||function(t,e,r,n){var o,i=arguments.length,s=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,r,n);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(i<3?o(s):i>3?o(e,r,s):o(e,r))||s);return i>3&&s&&Object.defineProperty(e,r,s),s},o=this&&this.__param||function(t,e){return function(r,n){e(r,n,t)}};Object.defineProperty(e,"__esModule",{value:!0}),e.RangeTransition=void 0;const i=r(4405),s=r(8042),a=r(312);let l=class extends a.Transition{constructor(t,e,r){super(t),this.from=e,this.to=r}get serializationType(){return 2}get label(){return i.IntervalSet.of(this.from,this.to)}matches(t,e,r){return t>=this.from&&t<=this.to}toString(){return"'"+String.fromCodePoint(this.from)+"'..'"+String.fromCodePoint(this.to)+"'"}};n([s.Override],l.prototype,"serializationType",null),n([s.Override,s.NotNull],l.prototype,"label",null),n([s.Override],l.prototype,"matches",null),n([s.Override,s.NotNull],l.prototype,"toString",null),l=n([o(0,s.NotNull)],l),e.RangeTransition=l},6557:function(t,e,r){"use strict";var n=this&&this.__decorate||function(t,e,r,n){var o,i=arguments.length,s=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,r,n);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(i<3?o(s):i>3?o(e,r,s):o(e,r))||s);return i>3&&s&&Object.defineProperty(e,r,s),s};Object.defineProperty(e,"__esModule",{value:!0}),e.RuleStartState=void 0;const o=r(3269),i=r(4700),s=r(8042);class a extends o.ATNState{constructor(){super(...arguments),this.isPrecedenceRule=!1,this.leftFactored=!1}get stateType(){return i.ATNStateType.RULE_START}}n([s.Override],a.prototype,"stateType",null),e.RuleStartState=a},5337:function(t,e,r){"use strict";var n=this&&this.__decorate||function(t,e,r,n){var o,i=arguments.length,s=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,r,n);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(i<3?o(s):i>3?o(e,r,s):o(e,r))||s);return i>3&&s&&Object.defineProperty(e,r,s),s};Object.defineProperty(e,"__esModule",{value:!0}),e.RuleStopState=void 0;const o=r(3269),i=r(4700),s=r(8042);class a extends o.ATNState{get nonStopStateNumber(){return-1}get stateType(){return i.ATNStateType.RULE_STOP}}n([s.Override],a.prototype,"nonStopStateNumber",null),n([s.Override],a.prototype,"stateType",null),e.RuleStopState=a},4704:function(t,e,r){"use strict";var n=this&&this.__decorate||function(t,e,r,n){var o,i=arguments.length,s=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,r,n);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(i<3?o(s):i>3?o(e,r,s):o(e,r))||s);return i>3&&s&&Object.defineProperty(e,r,s),s},o=this&&this.__param||function(t,e){return function(r,n){e(r,n,t)}};Object.defineProperty(e,"__esModule",{value:!0}),e.RuleTransition=void 0;const i=r(8042),s=r(312);let a=class extends s.Transition{constructor(t,e,r,n){super(t),this.tailCall=!1,this.optimizedTailCall=!1,this.ruleIndex=e,this.precedence=r,this.followState=n}get serializationType(){return 3}get isEpsilon(){return!0}matches(t,e,r){return!1}};n([i.NotNull],a.prototype,"followState",void 0),n([i.Override],a.prototype,"serializationType",null),n([i.Override],a.prototype,"isEpsilon",null),n([i.Override],a.prototype,"matches",null),a=n([o(0,i.NotNull),o(3,i.NotNull)],a),e.RuleTransition=a},1888:function(t,e,r){"use strict";var n=this&&this.__decorate||function(t,e,r,n){var o,i=arguments.length,s=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,r,n);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(i<3?o(s):i>3?o(e,r,s):o(e,r))||s);return i>3&&s&&Object.defineProperty(e,r,s),s},o=this&&this.__param||function(t,e){return function(r,n){e(r,n,t)}};Object.defineProperty(e,"__esModule",{value:!0}),e.SemanticContext=void 0;const i=r(9026),s=r(5192),a=r(3943),l=r(8042),u=r(8842),c=r(5103);class h{static get NONE(){return void 0===h._NONE&&(h._NONE=new h.Predicate),h._NONE}evalPrecedence(t,e){return this}static and(t,e){if(!t||t===h.NONE)return e;if(e===h.NONE)return t;let r=new h.AND(t,e);return 1===r.opnds.length?r.opnds[0]:r}static or(t,e){if(!t)return e;if(t===h.NONE||e===h.NONE)return h.NONE;let r=new h.OR(t,e);return 1===r.opnds.length?r.opnds[0]:r}}e.SemanticContext=h,function(t){function e(e){let r=[];for(let n=0;n<e.length;n++){let o=e[n];o instanceof t.PrecedencePredicate&&(r.push(o),e.splice(n,1),n--)}return r}class r extends t{constructor(t=-1,e=-1,r=!1){super(),this.ruleIndex=t,this.predIndex=e,this.isCtxDependent=r}eval(t,e){let r=this.isCtxDependent?e:void 0;return t.sempred(r,this.ruleIndex,this.predIndex)}hashCode(){let t=a.MurmurHash.initialize();return t=a.MurmurHash.update(t,this.ruleIndex),t=a.MurmurHash.update(t,this.predIndex),t=a.MurmurHash.update(t,this.isCtxDependent?1:0),t=a.MurmurHash.finish(t,3),t}equals(t){return t instanceof r&&(this===t||this.ruleIndex===t.ruleIndex&&this.predIndex===t.predIndex&&this.isCtxDependent===t.isCtxDependent)}toString(){return"{"+this.ruleIndex+":"+this.predIndex+"}?"}}n([l.Override],r.prototype,"eval",null),n([l.Override],r.prototype,"hashCode",null),n([l.Override],r.prototype,"equals",null),n([l.Override],r.prototype,"toString",null),t.Predicate=r;class h extends t{constructor(t){super(),this.precedence=t}eval(t,e){return t.precpred(e,this.precedence)}evalPrecedence(e,r){return e.precpred(r,this.precedence)?t.NONE:void 0}compareTo(t){return this.precedence-t.precedence}hashCode(){let t=1;return t=31*t+this.precedence,t}equals(t){return t instanceof h&&(this===t||this.precedence===t.precedence)}toString(){return"{"+this.precedence+">=prec}?"}}n([l.Override],h.prototype,"eval",null),n([l.Override],h.prototype,"evalPrecedence",null),n([l.Override],h.prototype,"compareTo",null),n([l.Override],h.prototype,"hashCode",null),n([l.Override],h.prototype,"equals",null),n([l.Override],h.prototype,"toString",null),t.PrecedencePredicate=h;class p extends t{}t.Operator=p;let f=class r extends p{constructor(t,n){super();let o=new i.Array2DHashSet(u.ObjectEqualityComparator.INSTANCE);t instanceof r?o.addAll(t.opnds):o.add(t),n instanceof r?o.addAll(n.opnds):o.add(n),this.opnds=o.toArray();let s=function(t){let e;for(let r of t)void 0!==e?e.compareTo(r)>0&&(e=r):e=r;return e}(e(this.opnds));s&&this.opnds.push(s)}get operands(){return this.opnds}equals(t){return this===t||t instanceof r&&s.ArrayEqualityComparator.INSTANCE.equals(this.opnds,t.opnds)}hashCode(){return a.MurmurHash.hashCode(this.opnds,40363613)}eval(t,e){for(let r of this.opnds)if(!r.eval(t,e))return!1;return!0}evalPrecedence(e,r){let n=!1,o=[];for(let i of this.opnds){let s=i.evalPrecedence(e,r);if(n=n||s!==i,null==s)return;s!==t.NONE&&o.push(s)}if(!n)return this;if(0===o.length)return t.NONE;let i=o[0];for(let e=1;e<o.length;e++)i=t.and(i,o[e]);return i}toString(){return c.join(this.opnds,"&&")}};n([l.Override],f.prototype,"operands",null),n([l.Override],f.prototype,"equals",null),n([l.Override],f.prototype,"hashCode",null),n([l.Override],f.prototype,"eval",null),n([l.Override],f.prototype,"evalPrecedence",null),n([l.Override],f.prototype,"toString",null),f=n([o(0,l.NotNull),o(1,l.NotNull)],f),t.AND=f;let d=class r extends p{constructor(t,n){super();let o=new i.Array2DHashSet(u.ObjectEqualityComparator.INSTANCE);t instanceof r?o.addAll(t.opnds):o.add(t),n instanceof r?o.addAll(n.opnds):o.add(n),this.opnds=o.toArray();let s=function(t){let e;for(let r of t)void 0!==e?e.compareTo(r)<0&&(e=r):e=r;return e}(e(this.opnds));s&&this.opnds.push(s)}get operands(){return this.opnds}equals(t){return this===t||t instanceof r&&s.ArrayEqualityComparator.INSTANCE.equals(this.opnds,t.opnds)}hashCode(){return a.MurmurHash.hashCode(this.opnds,486279973)}eval(t,e){for(let r of this.opnds)if(r.eval(t,e))return!0;return!1}evalPrecedence(e,r){let n=!1,o=[];for(let i of this.opnds){let s=i.evalPrecedence(e,r);if(n=n||s!==i,s===t.NONE)return t.NONE;s&&o.push(s)}if(!n)return this;if(0===o.length)return;let i=o[0];for(let e=1;e<o.length;e++)i=t.or(i,o[e]);return i}toString(){return c.join(this.opnds,"||")}};n([l.Override],d.prototype,"operands",null),n([l.Override],d.prototype,"equals",null),n([l.Override],d.prototype,"hashCode",null),n([l.Override],d.prototype,"eval",null),n([l.Override],d.prototype,"evalPrecedence",null),n([l.Override],d.prototype,"toString",null),d=n([o(0,l.NotNull),o(1,l.NotNull)],d),t.OR=d}(h=e.SemanticContext||(e.SemanticContext={}))},6765:function(t,e,r){"use strict";var n=this&&this.__decorate||function(t,e,r,n){var o,i=arguments.length,s=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,r,n);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(i<3?o(s):i>3?o(e,r,s):o(e,r))||s);return i>3&&s&&Object.defineProperty(e,r,s),s},o=this&&this.__param||function(t,e){return function(r,n){e(r,n,t)}};Object.defineProperty(e,"__esModule",{value:!0}),e.SetTransition=void 0;const i=r(4405),s=r(8042),a=r(4966),l=r(312);let u=class extends l.Transition{constructor(t,e){super(t),null==e&&(e=i.IntervalSet.of(a.Token.INVALID_TYPE)),this.set=e}get serializationType(){return 7}get label(){return this.set}matches(t,e,r){return this.set.contains(t)}toString(){return this.set.toString()}};n([s.NotNull],u.prototype,"set",void 0),n([s.Override],u.prototype,"serializationType",null),n([s.Override,s.NotNull],u.prototype,"label",null),n([s.Override],u.prototype,"matches",null),n([s.Override,s.NotNull],u.prototype,"toString",null),u=n([o(0,s.NotNull),o(1,s.Nullable)],u),e.SetTransition=u},1071:function(t,e,r){"use strict";var n=this&&this.__decorate||function(t,e,r,n){var o,i=arguments.length,s=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,r,n);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(i<3?o(s):i>3?o(e,r,s):o(e,r))||s);return i>3&&s&&Object.defineProperty(e,r,s),s},o=this&&this.__param||function(t,e){return function(r,n){e(r,n,t)}};Object.defineProperty(e,"__esModule",{value:!0}),e.SimulatorState=void 0;const i=r(8042),s=r(3208);let a=class{constructor(t,e,r,n){this.outerContext=null!=t?t:s.ParserRuleContext.emptyContext(),this.s0=e,this.useContext=r,this.remainingOuterContext=n}};a=n([o(1,i.NotNull)],a),e.SimulatorState=a},1069:function(t,e,r){"use strict";var n=this&&this.__decorate||function(t,e,r,n){var o,i=arguments.length,s=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,r,n);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(i<3?o(s):i>3?o(e,r,s):o(e,r))||s);return i>3&&s&&Object.defineProperty(e,r,s),s};Object.defineProperty(e,"__esModule",{value:!0}),e.StarBlockStartState=void 0;const o=r(4700),i=r(4136),s=r(8042);class a extends i.BlockStartState{get stateType(){return o.ATNStateType.STAR_BLOCK_START}}n([s.Override],a.prototype,"stateType",null),e.StarBlockStartState=a},7165:function(t,e,r){"use strict";var n=this&&this.__decorate||function(t,e,r,n){var o,i=arguments.length,s=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,r,n);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(i<3?o(s):i>3?o(e,r,s):o(e,r))||s);return i>3&&s&&Object.defineProperty(e,r,s),s};Object.defineProperty(e,"__esModule",{value:!0}),e.StarLoopEntryState=void 0;const o=r(4700),i=r(5280),s=r(213),a=r(8042);class l extends s.DecisionState{constructor(){super(...arguments),this.precedenceRuleDecision=!1,this.precedenceLoopbackStates=new i.BitSet}get stateType(){return o.ATNStateType.STAR_LOOP_ENTRY}}n([a.Override],l.prototype,"stateType",null),e.StarLoopEntryState=l},8303:function(t,e,r){"use strict";var n=this&&this.__decorate||function(t,e,r,n){var o,i=arguments.length,s=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,r,n);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(i<3?o(s):i>3?o(e,r,s):o(e,r))||s);return i>3&&s&&Object.defineProperty(e,r,s),s};Object.defineProperty(e,"__esModule",{value:!0}),e.StarLoopbackState=void 0;const o=r(3269),i=r(4700),s=r(8042);class a extends o.ATNState{get loopEntryState(){return this.transition(0).target}get stateType(){return i.ATNStateType.STAR_LOOP_BACK}}n([s.Override],a.prototype,"stateType",null),e.StarLoopbackState=a},5223:function(t,e,r){"use strict";var n=this&&this.__decorate||function(t,e,r,n){var o,i=arguments.length,s=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,r,n);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(i<3?o(s):i>3?o(e,r,s):o(e,r))||s);return i>3&&s&&Object.defineProperty(e,r,s),s};Object.defineProperty(e,"__esModule",{value:!0}),e.TokensStartState=void 0;const o=r(4700),i=r(213),s=r(8042);class a extends i.DecisionState{get stateType(){return o.ATNStateType.TOKEN_START}}n([s.Override],a.prototype,"stateType",null),e.TokensStartState=a},312:function(t,e,r){"use strict";var n=this&&this.__decorate||function(t,e,r,n){var o,i=arguments.length,s=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,r,n);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(i<3?o(s):i>3?o(e,r,s):o(e,r))||s);return i>3&&s&&Object.defineProperty(e,r,s),s},o=this&&this.__param||function(t,e){return function(r,n){e(r,n,t)}};Object.defineProperty(e,"__esModule",{value:!0}),e.Transition=void 0;const i=r(8042);let s=class{constructor(t){if(null==t)throw new Error("target cannot be null.");this.target=t}get isEpsilon(){return!1}get label(){}};s.serializationNames=["INVALID","EPSILON","RANGE","RULE","PREDICATE","ATOM","ACTION","SET","NOT_SET","WILDCARD","PRECEDENCE"],n([i.NotNull],s.prototype,"target",void 0),s=n([o(0,i.NotNull)],s),e.Transition=s},8844:function(t,e,r){"use strict";var n=this&&this.__decorate||function(t,e,r,n){var o,i=arguments.length,s=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,r,n);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(i<3?o(s):i>3?o(e,r,s):o(e,r))||s);return i>3&&s&&Object.defineProperty(e,r,s),s},o=this&&this.__param||function(t,e){return function(r,n){e(r,n,t)}};Object.defineProperty(e,"__esModule",{value:!0}),e.WildcardTransition=void 0;const i=r(8042),s=r(312);let a=class extends s.Transition{constructor(t){super(t)}get serializationType(){return 9}matches(t,e,r){return t>=e&&t<=r}toString(){return"."}};n([i.Override],a.prototype,"serializationType",null),n([i.Override],a.prototype,"matches",null),n([i.Override,i.NotNull],a.prototype,"toString",null),a=n([o(0,i.NotNull)],a),e.WildcardTransition=a},1060:(t,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.AcceptStateInfo=void 0;e.AcceptStateInfo=class{constructor(t,e){this._prediction=t,this._lexerActionExecutor=e}get prediction(){return this._prediction}get lexerActionExecutor(){return this._lexerActionExecutor}}},7055:function(t,e,r){"use strict";var n=this&&this.__decorate||function(t,e,r,n){var o,i=arguments.length,s=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,r,n);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(i<3?o(s):i>3?o(e,r,s):o(e,r))||s);return i>3&&s&&Object.defineProperty(e,r,s),s},o=this&&this.__param||function(t,e){return function(r,n){e(r,n,t)}};Object.defineProperty(e,"__esModule",{value:!0}),e.DFA=void 0;const i=r(9026),s=r(7176),a=r(3917),l=r(1186),u=r(3694),c=r(8042),h=r(8842),p=r(7165),f=r(6763);let d=class{constructor(t,e=0){if(this.states=new i.Array2DHashSet(h.ObjectEqualityComparator.INSTANCE),this.nextStateNumber=0,!t.atn)throw new Error("The ATNState must be associated with an ATN");this.atnStartState=t,this.atn=t.atn,this.decision=e;let r=!1;t instanceof p.StarLoopEntryState&&t.precedenceRuleDecision&&(r=!0,this.s0=new l.DFAState(new s.ATNConfigSet),this.s0full=new l.DFAState(new s.ATNConfigSet)),this.precedenceDfa=r}get isPrecedenceDfa(){return this.precedenceDfa}getPrecedenceStartState(t,e){if(!this.isPrecedenceDfa)throw new Error("Only precedence DFAs may contain a precedence start state.");return e?this.s0full.getTarget(t):this.s0.getTarget(t)}setPrecedenceStartState(t,e,r){if(!this.isPrecedenceDfa)throw new Error("Only precedence DFAs may contain a precedence start state.");t<0||(e?this.s0full.setTarget(t,r):this.s0.setTarget(t,r))}get isEmpty(){return this.isPrecedenceDfa?0===this.s0.getEdgeMap().size&&0===this.s0full.getEdgeMap().size:null==this.s0&&null==this.s0full}get isContextSensitive(){return this.isPrecedenceDfa?this.s0full.getEdgeMap().size>0:null!=this.s0full}addState(t){return t.stateNumber=this.nextStateNumber++,this.states.getOrAdd(t)}toString(t,e){if(t||(t=f.VocabularyImpl.EMPTY_VOCABULARY),!this.s0)return"";let r;return r=e?new a.DFASerializer(this,t,e,this.atnStartState.atn):new a.DFASerializer(this,t),r.toString()}toLexerString(){if(!this.s0)return"";return new u.LexerDFASerializer(this).toString()}};n([c.NotNull],d.prototype,"states",void 0),n([c.NotNull],d.prototype,"atnStartState",void 0),n([c.NotNull],d.prototype,"atn",void 0),d=n([o(0,c.NotNull)],d),e.DFA=d},3917:function(t,e,r){"use strict";var n=this&&this.__decorate||function(t,e,r,n){var o,i=arguments.length,s=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,r,n);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(i<3?o(s):i>3?o(e,r,s):o(e,r))||s);return i>3&&s&&Object.defineProperty(e,r,s),s};Object.defineProperty(e,"__esModule",{value:!0}),e.DFASerializer=void 0;const o=r(7643),i=r(8042),s=r(9767),a=r(8610),l=r(6763);class u{constructor(t,e,r,n){e instanceof a.Recognizer?(r=e.ruleNames,n=e.atn,e=e.vocabulary):e||(e=l.VocabularyImpl.EMPTY_VOCABULARY),this.dfa=t,this.vocabulary=e,this.ruleNames=r,this.atn=n}toString(){if(!this.dfa.s0)return"";let t="";if(this.dfa.states){let e=new Array(...this.dfa.states.toArray());e.sort(((t,e)=>t.stateNumber-e.stateNumber));for(let r of e){let e=r.getEdgeMap(),n=[...e.keys()].sort(((t,e)=>t-e)),i=r.getContextEdgeMap(),s=[...i.keys()].sort(((t,e)=>t-e));for(let i of n){let n=e.get(i);if((null==n||n===o.ATNSimulator.ERROR)&&!r.isContextSymbol(i))continue;let s=!1;t+=this.getStateString(r)+"-"+this.getEdgeLabel(i)+"->",r.isContextSymbol(i)&&(t+="!",s=!0);let a=n;a&&a.stateNumber!==o.ATNSimulator.ERROR.stateNumber?t+=this.getStateString(a)+"\n":s&&(t+="ctx\n")}if(r.isContextSensitive)for(let e of s)t+=this.getStateString(r)+"-"+this.getContextLabel(e)+"->"+this.getStateString(i.get(e))+"\n"}}let e=t;return 0===e.length?"":e}getContextLabel(t){if(t===s.PredictionContext.EMPTY_FULL_STATE_KEY)return"ctx:EMPTY_FULL";if(t===s.PredictionContext.EMPTY_LOCAL_STATE_KEY)return"ctx:EMPTY_LOCAL";if(this.atn&&t>0&&t<=this.atn.states.length){let e=this.atn.states[t].ruleIndex;if(this.ruleNames&&e>=0&&e<this.ruleNames.length)return"ctx:"+String(t)+"("+this.ruleNames[e]+")"}return"ctx:"+String(t)}getEdgeLabel(t){return this.vocabulary.getDisplayName(t)}getStateString(t){if(t===o.ATNSimulator.ERROR)return"ERROR";let e=t.stateNumber,r="s"+e;if(t.isAcceptState&&(r=t.predicates?":s"+e+"=>"+t.predicates:":s"+e+"=>"+t.prediction),t.isContextSensitive){r+="*";for(let e of t.configs)if(e.reachesIntoOuterContext){r+="*";break}}return r}}n([i.NotNull],u.prototype,"dfa",void 0),n([i.NotNull],u.prototype,"vocabulary",void 0),n([i.Override],u.prototype,"toString",null),e.DFASerializer=u},1186:function(t,e,r){"use strict";var n=this&&this.__decorate||function(t,e,r,n){var o,i=arguments.length,s=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,r,n);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(i<3?o(s):i>3?o(e,r,s):o(e,r))||s);return i>3&&s&&Object.defineProperty(e,r,s),s},o=this&&this.__param||function(t,e){return function(r,n){e(r,n,t)}};Object.defineProperty(e,"__esModule",{value:!0}),e.DFAState=void 0;const i=r(7949),s=r(5280),a=r(3943),l=r(8042),u=r(9767),c=r(9282);class h{constructor(t){this.stateNumber=-1,this.configs=t,this.edges=new Map,this.contextEdges=new Map}get isContextSensitive(){return!!this.contextSymbols}isContextSymbol(t){return!!this.isContextSensitive&&this.contextSymbols.get(t)}setContextSymbol(t){c(this.isContextSensitive),this.contextSymbols.set(t)}setContextSensitive(t){c(!this.configs.isOutermostConfigSet),this.isContextSensitive||this.contextSymbols||(this.contextSymbols=new s.BitSet)}get acceptStateInfo(){return this._acceptStateInfo}set acceptStateInfo(t){this._acceptStateInfo=t}get isAcceptState(){return!!this._acceptStateInfo}get prediction(){return this._acceptStateInfo?this._acceptStateInfo.prediction:i.ATN.INVALID_ALT_NUMBER}get lexerActionExecutor(){if(this._acceptStateInfo)return this._acceptStateInfo.lexerActionExecutor}getTarget(t){return this.edges.get(t)}setTarget(t,e){this.edges.set(t,e)}getEdgeMap(){return this.edges}getContextTarget(t){return t===u.PredictionContext.EMPTY_FULL_STATE_KEY&&(t=-1),this.contextEdges.get(t)}setContextTarget(t,e){if(!this.isContextSensitive)throw new Error("The state is not context sensitive.");t===u.PredictionContext.EMPTY_FULL_STATE_KEY&&(t=-1),this.contextEdges.set(t,e)}getContextEdgeMap(){let t=new Map(this.contextEdges),e=t.get(-1);if(void 0!==e){if(1===t.size){let t=new Map;return t.set(u.PredictionContext.EMPTY_FULL_STATE_KEY,e),t}t.delete(-1),t.set(u.PredictionContext.EMPTY_FULL_STATE_KEY,e)}return t}hashCode(){let t=a.MurmurHash.initialize(7);return t=a.MurmurHash.update(t,this.configs.hashCode()),t=a.MurmurHash.finish(t,1),t}equals(t){if(this===t)return!0;if(!(t instanceof h))return!1;let e=t;return this.configs.equals(e.configs)}toString(){let t="";return t+=this.stateNumber+":"+this.configs,this.isAcceptState&&(t+="=>",this.predicates?t+=this.predicates:t+=this.prediction),t.toString()}}n([l.NotNull],h.prototype,"configs",void 0),n([l.NotNull],h.prototype,"edges",void 0),n([l.NotNull],h.prototype,"contextEdges",void 0),n([l.Override],h.prototype,"hashCode",null),n([l.Override],h.prototype,"equals",null),n([l.Override],h.prototype,"toString",null),e.DFAState=h,function(t){let e=class{constructor(t,e){this.alt=e,this.pred=t}toString(){return"("+this.pred+", "+this.alt+")"}};n([l.NotNull],e.prototype,"pred",void 0),n([l.Override],e.prototype,"toString",null),e=n([o(0,l.NotNull)],e),t.PredPrediction=e}(h=e.DFAState||(e.DFAState={}))},3694:function(t,e,r){"use strict";var n=this&&this.__decorate||function(t,e,r,n){var o,i=arguments.length,s=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,r,n);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(i<3?o(s):i>3?o(e,r,s):o(e,r))||s);return i>3&&s&&Object.defineProperty(e,r,s),s},o=this&&this.__param||function(t,e){return function(r,n){e(r,n,t)}};Object.defineProperty(e,"__esModule",{value:!0}),e.LexerDFASerializer=void 0;const i=r(3917),s=r(8042),a=r(6763);let l=class extends i.DFASerializer{constructor(t){super(t,a.VocabularyImpl.EMPTY_VOCABULARY)}getEdgeLabel(t){return"'"+String.fromCodePoint(t)+"'"}};n([s.Override,s.NotNull],l.prototype,"getEdgeLabel",null),l=n([o(0,s.NotNull)],l),e.LexerDFASerializer=l},352:function(t,e,r){"use strict";var n=this&&this.__createBinding||(Object.create?function(t,e,r,n){void 0===n&&(n=r),Object.defineProperty(t,n,{enumerable:!0,get:function(){return e[r]}})}:function(t,e,r,n){void 0===n&&(n=r),t[n]=e[r]}),o=this&&this.__exportStar||function(t,e){for(var r in t)"default"===r||Object.prototype.hasOwnProperty.call(e,r)||n(e,t,r)};Object.defineProperty(e,"__esModule",{value:!0}),o(r(1466),e),o(r(1597),e),o(r(8623),e),o(r(9701),e),o(r(8218),e),o(r(5699),e),o(r(3675),e),o(r(5444),e),o(r(1540),e),o(r(824),e),o(r(8735),e),o(r(4321),e),o(r(4525),e),o(r(3992),e),o(r(4361),e),o(r(7574),e),o(r(2915),e),o(r(4837),e),o(r(4126),e),o(r(3227),e),o(r(9557),e),o(r(7301),e),o(r(5324),e),o(r(7683),e),o(r(4609),e),o(r(2824),e),o(r(1603),e),o(r(627),e),o(r(3208),e),o(r(9583),e),o(r(6454),e),o(r(3998),e),o(r(8610),e),o(r(7423),e),o(r(6599),e),o(r(3252),e),o(r(345),e),o(r(4966),e),o(r(2362),e),o(r(9089),e),o(r(9293),e),o(r(929),e),o(r(2499),e),o(r(6763),e),o(r(4955),e)},3874:(t,e,r)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.Array2DHashMap=void 0;const n=r(9026);class o{constructor(t){this.keyComparator=t}hashCode(t){return this.keyComparator.hashCode(t.key)}equals(t,e){return this.keyComparator.equals(t.key,e.key)}}class i{constructor(t){this.backingStore=t instanceof i?new n.Array2DHashSet(t.backingStore):new n.Array2DHashSet(new o(t))}clear(){this.backingStore.clear()}containsKey(t){return this.backingStore.contains({key:t})}get(t){let e=this.backingStore.get({key:t});if(e)return e.value}get isEmpty(){return this.backingStore.isEmpty}put(t,e){let r,n=this.backingStore.get({key:t,value:e});return n?(r=n.value,n.value=e):this.backingStore.add({key:t,value:e}),r}putIfAbsent(t,e){let r,n=this.backingStore.get({key:t,value:e});return n?r=n.value:this.backingStore.add({key:t,value:e}),r}get size(){return this.backingStore.size}hashCode(){return this.backingStore.hashCode()}equals(t){return t instanceof i&&this.backingStore.equals(t.backingStore)}}e.Array2DHashMap=i},9026:function(t,e,r){"use strict";var n=this&&this.__decorate||function(t,e,r,n){var o,i=arguments.length,s=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,r,n);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(i<3?o(s):i>3?o(e,r,s):o(e,r))||s);return i>3&&s&&Object.defineProperty(e,r,s),s},o=this&&this.__param||function(t,e){return function(r,n){e(r,n,t)}};Object.defineProperty(e,"__esModule",{value:!0}),e.Array2DHashSet=void 0;const i=r(9282),s=r(3197),a=r(8042),l=r(3943),u=.75;class c{constructor(t,e=16){if(this.n=0,this.threshold=Math.floor(12),t instanceof c){this.comparator=t.comparator,this.buckets=t.buckets.slice(0);for(let t=0;t<this.buckets.length;t++){let e=this.buckets[t];e&&(this.buckets[t]=e.slice(0))}this.n=t.n,this.threshold=t.threshold}else this.comparator=t||s.DefaultEqualityComparator.INSTANCE,this.buckets=this.createBuckets(e)}getOrAdd(t){return this.n>this.threshold&&this.expand(),this.getOrAddImpl(t)}getOrAddImpl(t){let e=this.getBucket(t),r=this.buckets[e];if(!r)return r=[t],this.buckets[e]=r,this.n++,t;for(let e of r)if(this.comparator.equals(e,t))return e;return r.push(t),this.n++,t}get(t){if(null==t)return t;let e=this.getBucket(t),r=this.buckets[e];if(r)for(let e of r)if(this.comparator.equals(e,t))return e}getBucket(t){return this.comparator.hashCode(t)&this.buckets.length-1}hashCode(){let t=l.MurmurHash.initialize();for(let e of this.buckets)if(null!=e)for(let r of e){if(null==r)break;t=l.MurmurHash.update(t,this.comparator.hashCode(r))}return t=l.MurmurHash.finish(t,this.size),t}equals(t){if(t===this)return!0;if(!(t instanceof c))return!1;if(t.size!==this.size)return!1;return this.containsAll(t)}expand(){let t=this.buckets,e=2*this.buckets.length,r=this.createBuckets(e);this.buckets=r,this.threshold=Math.floor(e*u);let n=this.size;for(let e of t)if(e)for(let t of e){let e=this.getBucket(t),r=this.buckets[e];r||(r=[],this.buckets[e]=r),r.push(t)}i(this.n===n)}add(t){return this.getOrAdd(t)===t}get size(){return this.n}get isEmpty(){return 0===this.n}contains(t){return this.containsFast(this.asElementType(t))}containsFast(t){return null!=t&&null!=this.get(t)}*[Symbol.iterator](){yield*this.toArray()}toArray(){const t=new Array(this.size);let e=0;for(let r of this.buckets)if(null!=r)for(let n of r){if(null==n)break;t[e++]=n}return t}containsAll(t){if(t instanceof c){let e=t;for(let t of e.buckets)if(null!=t)for(let e of t){if(null==e)break;if(!this.containsFast(this.asElementType(e)))return!1}}else for(let e of t)if(!this.containsFast(this.asElementType(e)))return!1;return!0}addAll(t){let e=!1;for(let r of t){this.getOrAdd(r)!==r&&(e=!0)}return e}clear(){this.buckets=this.createBuckets(16),this.n=0,this.threshold=Math.floor(12)}toString(){if(0===this.size)return"{}";let t="{",e=!0;for(let r of this.buckets)if(null!=r)for(let n of r){if(null==n)break;e?e=!1:t+=", ",t+=n.toString()}return t+="}",t}toTableString(){let t="";for(let e of this.buckets){if(null==e){t+="null\n";continue}t+="[";let r=!0;for(let n of e)r?r=!1:t+=" ",t+=null==n?"_":n.toString();t+="]\n"}return t}asElementType(t){return t}createBuckets(t){return new Array(t)}}n([a.NotNull],c.prototype,"comparator",void 0),n([a.Override],c.prototype,"hashCode",null),n([a.Override],c.prototype,"equals",null),n([a.Override],c.prototype,"add",null),n([a.Override],c.prototype,"size",null),n([a.Override],c.prototype,"isEmpty",null),n([a.Override],c.prototype,"contains",null),n([o(0,a.Nullable)],c.prototype,"containsFast",null),n([a.Override],c.prototype,Symbol.iterator,null),n([a.Override],c.prototype,"toArray",null),n([a.Override],c.prototype,"containsAll",null),n([a.Override],c.prototype,"addAll",null),n([a.Override],c.prototype,"clear",null),n([a.Override],c.prototype,"toString",null),n([a.SuppressWarnings("unchecked")],c.prototype,"asElementType",null),n([a.SuppressWarnings("unchecked")],c.prototype,"createBuckets",null),e.Array2DHashSet=c},5192:function(t,e,r){"use strict";var n=this&&this.__decorate||function(t,e,r,n){var o,i=arguments.length,s=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,r,n);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(i<3?o(s):i>3?o(e,r,s):o(e,r))||s);return i>3&&s&&Object.defineProperty(e,r,s),s};Object.defineProperty(e,"__esModule",{value:!0}),e.ArrayEqualityComparator=void 0;const o=r(8042),i=r(3943),s=r(8842);class a{hashCode(t){return null==t?0:i.MurmurHash.hashCode(t,0)}equals(t,e){if(null==t)return null==e;if(null==e)return!1;if(t.length!==e.length)return!1;for(let r=0;r<t.length;r++)if(!s.ObjectEqualityComparator.INSTANCE.equals(t[r],e[r]))return!1;return!0}}a.INSTANCE=new a,n([o.Override],a.prototype,"hashCode",null),n([o.Override],a.prototype,"equals",null),e.ArrayEqualityComparator=a},6005:(t,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.Arrays=void 0,function(t){t.binarySearch=function(t,e,r,n){return function(t,e,r,n){let o=e,i=r-1;for(;o<=i;){let e=o+i>>>1,r=t[e];if(r<n)o=e+1;else{if(!(r>n))return e;i=e-1}}return-(o+1)}(t,void 0!==r?r:0,void 0!==n?n:t.length,e)},t.toString=function(t){let e="[",r=!0;for(let n of t)r?r=!1:e+=", ",e+=null===n?"null":void 0===n?"undefined":n;return e+="]",e}}(e.Arrays||(e.Arrays={}))},5280:(t,e,r)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.BitSet=void 0;const n=r(9539),o=r(3943),i=new Uint16Array(0);function s(t){return t>>>4}function a(t){return 16*t}function l(t){let e=1;for(let r=0;r<16;r++){if(0!=(t&e))return r;e=e<<1>>>0}throw new RangeError("No specified bit found")}function u(t){let e=32768;for(let r=15;r>=0;r--){if(0!=(t&e))return r;e>>>=1}throw new RangeError("No specified bit found")}function c(t,e){return(t&=15)===(e&=15)?1<<t>>>0:65535>>>15-e^65535>>>16-t}const h=new Uint8Array(65536);for(let t=0;t<16;t++){const e=1<<t>>>0;let r=0;for(;r<h.length;){r+=e;for(let t=0;t<e;t++)h[r]++,r++}}class p{constructor(t){if(t)if("number"==typeof t){if(t<0)throw new RangeError("nbits cannot be negative");this.data=new Uint16Array(s(t-1)+1)}else if(t instanceof p)this.data=t.data.slice(0);else{let e=-1;for(let r of t)e<r&&(e=r);this.data=new Uint16Array(s(e-1)+1);for(let e of t)this.set(e)}else this.data=i}and(t){const e=this.data,r=t.data,n=Math.min(e.length,r.length);let o=-1;for(let t=0;t<n;t++){0!==(e[t]&=r[t])&&(o=t)}-1===o&&(this.data=i),o<e.length-1&&(this.data=e.slice(0,o+1))}andNot(t){const e=this.data,r=t.data,n=Math.min(e.length,r.length);let o=-1;for(let t=0;t<n;t++){0!==(e[t]&=65535^r[t])&&(o=t)}-1===o&&(this.data=i),o<e.length-1&&(this.data=e.slice(0,o+1))}cardinality(){if(this.isEmpty)return 0;const t=this.data,e=t.length;let r=0;for(let n=0;n<e;n++)r+=h[t[n]];return r}clear(t,e){null==t?this.data.fill(0):null==e?this.set(t,!1):this.set(t,e,!1)}flip(t,e){if(null==e&&(e=t),t<0||e<t)throw new RangeError;let r=s(t);const n=s(e);if(r===n)this.data[r]^=c(t,e);else{for(this.data[r++]^=c(t,15);r<n;)this.data[r++]^=65535;this.data[r++]^=c(0,e)}}get(t,e){if(void 0===e)return!!(this.data[s(t)]&c(t,t));{let r=new p(e+1);for(let n=t;n<=e;n++)r.set(n,this.get(n));return r}}intersects(t){let e=Math.min(this.length(),t.length());if(0===e)return!1;let r=s(e-1);for(let e=0;e<=r;e++)if(0!=(this.data[e]&t.data[e]))return!0;return!1}get isEmpty(){return 0===this.length()}length(){return this.data.length?this.previousSetBit(a(this.data.length)-1)+1:0}nextClearBit(t){if(t<0)throw new RangeError("fromIndex cannot be negative");const e=this.data,r=e.length;let n=s(t);if(n>r)return-1;let o=65535^c(t,15);if(65535==(e[n]|o)){for(n++,o=0;n<r&&65535===e[n];n++);if(n===r)return-1}return a(n)+l(65535^(e[n]|o))}nextSetBit(t){if(t<0)throw new RangeError("fromIndex cannot be negative");const e=this.data,r=e.length;let n=s(t);if(n>r)return-1;let o=c(t,15);if(0==(e[n]&o)){for(n++,o=65535;n<r&&0===e[n];n++);if(n>=r)return-1}return a(n)+l(e[n]&o)}or(t){const e=this.data,r=t.data,n=Math.min(e.length,r.length),o=Math.max(e.length,r.length),s=e.length===o?e:new Uint16Array(o);let a=-1;for(let t=0;t<n;t++){0!==(s[t]=e[t]|r[t])&&(a=t)}const l=e.length>r.length?e:r;for(let t=n;t<o;t++){0!==(s[t]=l[t])&&(a=t)}-1===a?this.data=i:s.length===a+1?this.data=s:this.data=s.slice(0,a)}previousClearBit(t){if(t<0)throw new RangeError("fromIndex cannot be negative");const e=this.data,r=e.length;let n=s(t);n>=r&&(n=r-1);let o=65535^c(0,t);if(65535==(e[n]|o)){for(o=0,n--;n>=0&&65535===e[n];n--);if(n<0)return-1}return a(n)+u(65535^(e[n]|o))}previousSetBit(t){if(t<0)throw new RangeError("fromIndex cannot be negative");const e=this.data,r=e.length;let n=s(t);n>=r&&(n=r-1);let o=c(0,t);if(0==(e[n]&o)){for(n--,o=65535;n>=0&&0===e[n];n--);if(n<0)return-1}return a(n)+u(e[n]&o)}set(t,e,r){if(void 0===e?(e=t,r=!0):"boolean"==typeof e&&(r=e,e=t),void 0===r&&(r=!0),t<0||t>e)throw new RangeError;let n=s(t),o=s(e);if(r&&o>=this.data.length){let t=new Uint16Array(o+1);this.data.forEach(((e,r)=>t[r]=e)),this.data=t}else if(!r){if(n>=this.data.length)return;o>=this.data.length&&(o=this.data.length-1,e=16*this.data.length-1)}if(n===o)this._setBits(n,r,c(t,e));else{for(this._setBits(n++,r,c(t,15));n<o;)this.data[n++]=r?65535:0;this._setBits(n,r,c(0,e))}}_setBits(t,e,r){e?this.data[t]|=r:this.data[t]&=65535^r}get size(){return 8*this.data.byteLength}hashCode(){return o.MurmurHash.hashCode(this.data,22)}equals(t){if(t===this)return!0;if(!(t instanceof p))return!1;const e=this.length();if(e!==t.length())return!1;if(0===e)return!0;let r=s(e-1);for(let e=0;e<=r;e++)if(this.data[e]!==t.data[e])return!1;return!0}toString(){let t="{",e=!0;for(let r=this.nextSetBit(0);r>=0;r=this.nextSetBit(r+1))e?e=!1:t+=", ",t+=r;return t+="}",t}xor(t){const e=this.data,r=t.data,n=Math.min(e.length,r.length),o=Math.max(e.length,r.length),s=e.length===o?e:new Uint16Array(o);let a=-1;for(let t=0;t<n;t++){0!==(s[t]=e[t]^r[t])&&(a=t)}const l=e.length>r.length?e:r;for(let t=n;t<o;t++){0!==(s[t]=l[t])&&(a=t)}-1===a?this.data=i:s.length===a+1?this.data=s:this.data=s.slice(0,a+1)}clone(){return new p(this)}[Symbol.iterator](){return new f(this.data)}[n.inspect.custom](){return"BitSet "+this.toString()}}e.BitSet=p;class f{constructor(t){this.data=t,this.index=0,this.mask=65535}next(){for(;this.index<this.data.length;){const t=this.data[this.index]&this.mask;if(0!==t){const e=a(this.index)+l(t);return this.mask=c(e+1,15),{done:!1,value:e}}this.index++,this.mask=65535}return{done:!0,value:-1}}[Symbol.iterator](){return this}}},9363:(t,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.isSupplementaryCodePoint=e.isLowSurrogate=e.isHighSurrogate=void 0,e.isHighSurrogate=function(t){return t>=55296&&t<=56319},e.isLowSurrogate=function(t){return t>=56320&&t<=57343},e.isSupplementaryCodePoint=function(t){return t>=65536}},3197:function(t,e,r){"use strict";var n=this&&this.__decorate||function(t,e,r,n){var o,i=arguments.length,s=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,r,n);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(i<3?o(s):i>3?o(e,r,s):o(e,r))||s);return i>3&&s&&Object.defineProperty(e,r,s),s};Object.defineProperty(e,"__esModule",{value:!0}),e.DefaultEqualityComparator=void 0;const o=r(8042),i=r(3943),s=r(8842);class a{hashCode(t){return null==t?0:"string"==typeof t||"number"==typeof t?i.MurmurHash.hashCode([t]):s.ObjectEqualityComparator.INSTANCE.hashCode(t)}equals(t,e){return null==t?null==e:"string"==typeof t||"number"==typeof t?t===e:s.ObjectEqualityComparator.INSTANCE.equals(t,e)}}a.INSTANCE=new a,n([o.Override],a.prototype,"hashCode",null),n([o.Override],a.prototype,"equals",null),e.DefaultEqualityComparator=a},6766:function(t,e,r){"use strict";var n=this&&this.__decorate||function(t,e,r,n){var o,i=arguments.length,s=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,r,n);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(i<3?o(s):i>3?o(e,r,s):o(e,r))||s);return i>3&&s&&Object.defineProperty(e,r,s),s};Object.defineProperty(e,"__esModule",{value:!0}),e.IntegerList=void 0;const o=r(6005),i=r(8042),s=new Int32Array(0),a=2147483639;class l{constructor(t){if(t)if(t instanceof l)this._data=t._data.slice(0),this._size=t._size;else if("number"==typeof t)0===t?(this._data=s,this._size=0):(this._data=new Int32Array(t),this._size=0);else{this._data=s,this._size=0;for(let e of t)this.add(e)}else this._data=s,this._size=0}add(t){this._data.length===this._size&&this.ensureCapacity(this._size+1),this._data[this._size]=t,this._size++}addAll(t){if(Array.isArray(t))this.ensureCapacity(this._size+t.length),this._data.subarray(this._size,this._size+t.length).set(t),this._size+=t.length;else if(t instanceof l)this.ensureCapacity(this._size+t._size),this._data.subarray(this._size,this._size+t.size).set(t._data),this._size+=t._size;else{this.ensureCapacity(this._size+t.size);let e=0;for(let r of t)this._data[this._size+e]=r,e++;this._size+=t.size}}get(t){if(t<0||t>=this._size)throw RangeError();return this._data[t]}contains(t){for(let e=0;e<this._size;e++)if(this._data[e]===t)return!0;return!1}set(t,e){if(t<0||t>=this._size)throw RangeError();let r=this._data[t];return this._data[t]=e,r}removeAt(t){let e=this.get(t);return this._data.copyWithin(t,t+1,this._size),this._data[this._size-1]=0,this._size--,e}removeRange(t,e){if(t<0||e<0||t>this._size||e>this._size)throw RangeError();if(t>e)throw RangeError();this._data.copyWithin(e,t,this._size),this._data.fill(0,this._size-(e-t),this._size),this._size-=e-t}get isEmpty(){return 0===this._size}get size(){return this._size}trimToSize(){this._data.length!==this._size&&(this._data=this._data.slice(0,this._size))}clear(){this._data.fill(0,0,this._size),this._size=0}toArray(){return 0===this._size?[]:Array.from(this._data.subarray(0,this._size))}sort(){this._data.subarray(0,this._size).sort()}equals(t){if(t===this)return!0;if(!(t instanceof l))return!1;if(this._size!==t._size)return!1;for(let e=0;e<this._size;e++)if(this._data[e]!==t._data[e])return!1;return!0}hashCode(){let t=1;for(let e=0;e<this._size;e++)t=31*t+this._data[e];return t}toString(){return this._data.toString()}binarySearch(t,e,r){if(void 0===e&&(e=0),void 0===r&&(r=this._size),e<0||r<0||e>this._size||r>this._size)throw new RangeError;if(e>r)throw new RangeError;return o.Arrays.binarySearch(this._data,t,e,r)}ensureCapacity(t){if(t<0||t>a)throw new RangeError;let e;for(e=0===this._data.length?4:this._data.length;e<t;)e*=2,(e<0||e>a)&&(e=a);let r=new Int32Array(e);r.set(this._data),this._data=r}toCharArray(){let t=new Uint16Array(this._size),e=0,r=!1;for(let n=0;n<this._size;n++){let o=this._data[n];if(o>=0&&o<65536){t[e]=o,e++;continue}if(!r){let e=new Uint16Array(this.charArraySize());e.set(t,0),t=e,r=!0}let i=String.fromCodePoint(o);t[e]=i.charCodeAt(0),t[e+1]=i.charCodeAt(1),e+=2}return t}charArraySize(){let t=0;for(let e=0;e<this._size;e++)t+=this._data[e]>=65536?2:1;return t}}n([i.NotNull],l.prototype,"_data",void 0),n([i.Override],l.prototype,"equals",null),n([i.Override],l.prototype,"hashCode",null),n([i.Override],l.prototype,"toString",null),e.IntegerList=l},1350:(t,e,r)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.IntegerStack=void 0;const n=r(6766);class o extends n.IntegerList{constructor(t){super(t)}push(t){this.add(t)}pop(){return this.removeAt(this.size-1)}peek(){return this.get(this.size-1)}}e.IntegerStack=o},8813:function(t,e,r){"use strict";var n=this&&this.__decorate||function(t,e,r,n){var o,i=arguments.length,s=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,r,n);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(i<3?o(s):i>3?o(e,r,s):o(e,r))||s);return i>3&&s&&Object.defineProperty(e,r,s),s};Object.defineProperty(e,"__esModule",{value:!0}),e.Interval=void 0;const o=r(8042);class i{constructor(t,e){this.a=t,this.b=e}static get INVALID(){return i._INVALID}static of(t,e){return t!==e||t<0||t>1e3?new i(t,e):(null==i.cache[t]&&(i.cache[t]=new i(t,t)),i.cache[t])}get length(){return this.b<this.a?0:this.b-this.a+1}equals(t){return t===this||t instanceof i&&(this.a===t.a&&this.b===t.b)}hashCode(){let t=23;return t=31*t+this.a,t=31*t+this.b,t}startsBeforeDisjoint(t){return this.a<t.a&&this.b<t.a}startsBeforeNonDisjoint(t){return this.a<=t.a&&this.b>=t.a}startsAfter(t){return this.a>t.a}startsAfterDisjoint(t){return this.a>t.b}startsAfterNonDisjoint(t){return this.a>t.a&&this.a<=t.b}disjoint(t){return this.startsBeforeDisjoint(t)||this.startsAfterDisjoint(t)}adjacent(t){return this.a===t.b+1||this.b===t.a-1}properlyContains(t){return t.a>=this.a&&t.b<=this.b}union(t){return i.of(Math.min(this.a,t.a),Math.max(this.b,t.b))}intersection(t){return i.of(Math.max(this.a,t.a),Math.min(this.b,t.b))}differenceNotProperlyContained(t){let e;return t.startsBeforeNonDisjoint(this)?e=i.of(Math.max(this.a,t.b+1),this.b):t.startsAfterNonDisjoint(this)&&(e=i.of(this.a,t.a-1)),e}toString(){return this.a+".."+this.b}}i._INVALID=new i(-1,-2),i.cache=new Array(1001),n([o.Override],i.prototype,"equals",null),n([o.Override],i.prototype,"hashCode",null),n([o.Override],i.prototype,"toString",null),e.Interval=i},4405:function(t,e,r){"use strict";var n=this&&this.__decorate||function(t,e,r,n){var o,i=arguments.length,s=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,r,n);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(i<3?o(s):i>3?o(e,r,s):o(e,r))||s);return i>3&&s&&Object.defineProperty(e,r,s),s},o=this&&this.__param||function(t,e){return function(r,n){e(r,n,t)}};Object.defineProperty(e,"__esModule",{value:!0}),e.IntervalSet=void 0;const i=r(5192),s=r(6766),a=r(8813),l=r(9557),u=r(3943),c=r(8042),h=r(4966);class p{constructor(t){this.readonly=!1,this._intervals=null!=t?t.slice(0):[]}static get COMPLETE_CHAR_SET(){return void 0===p._COMPLETE_CHAR_SET&&(p._COMPLETE_CHAR_SET=p.of(l.Lexer.MIN_CHAR_VALUE,l.Lexer.MAX_CHAR_VALUE),p._COMPLETE_CHAR_SET.setReadonly(!0)),p._COMPLETE_CHAR_SET}static get EMPTY_SET(){return null==p._EMPTY_SET&&(p._EMPTY_SET=new p,p._EMPTY_SET.setReadonly(!0)),p._EMPTY_SET}static of(t,e=t){let r=new p;return r.add(t,e),r}clear(){if(this.readonly)throw new Error("can't alter readonly IntervalSet");this._intervals.length=0}add(t,e=t){this.addRange(a.Interval.of(t,e))}addRange(t){if(this.readonly)throw new Error("can't alter readonly IntervalSet");if(!(t.b<t.a)){for(let e=0;e<this._intervals.length;e++){let r=this._intervals[e];if(t.equals(r))return;if(t.adjacent(r)||!t.disjoint(r)){let n=t.union(r);for(this._intervals[e]=n;e<this._intervals.length-1;){e++;let t=this._intervals[e];if(!n.adjacent(t)&&n.disjoint(t))break;this._intervals.splice(e,1),e--,this._intervals[e]=n.union(t)}return}if(t.startsBeforeDisjoint(r))return void this._intervals.splice(e,0,t)}this._intervals.push(t)}}static or(t){let e=new p;for(let r of t)e.addAll(r);return e}addAll(t){if(null==t)return this;if(t instanceof p){let e=t,r=e._intervals.length;for(let t=0;t<r;t++){let r=e._intervals[t];this.add(r.a,r.b)}}else for(let e of t.toArray())this.add(e);return this}complementRange(t,e){return this.complement(p.of(t,e))}complement(t){if(t.isNil)return p.EMPTY_SET;let e;return t instanceof p?e=t:(e=new p,e.addAll(t)),e.subtract(this)}subtract(t){if(null==t||t.isNil)return new p(this._intervals);if(t instanceof p)return p.subtract(this,t);let e=new p;return e.addAll(t),p.subtract(this,e)}static subtract(t,e){if(t.isNil)return new p;let r=new p(t._intervals);if(e.isNil)return r;let n=0,o=0;for(;n<r._intervals.length&&o<e._intervals.length;){let t,i,s=r._intervals[n],l=e._intervals[o];if(l.b<s.a)o++;else if(l.a>s.b)n++;else if(l.a>s.a&&(t=new a.Interval(s.a,l.a-1)),l.b<s.b&&(i=new a.Interval(l.b+1,s.b)),t){if(i){r._intervals[n]=t,r._intervals.splice(n+1,0,i),n++,o++;continue}r._intervals[n]=t,n++}else i?(r._intervals[n]=i,o++):r._intervals.splice(n,1)}return r}or(t){let e=new p;return e.addAll(this),e.addAll(t),e}and(t){if(t.isNil)return new p;let e,r=this._intervals,n=t._intervals,o=r.length,i=n.length,s=0,a=0;for(;s<o&&a<i;){let t=r[s],o=n[a];t.startsBeforeDisjoint(o)?s++:o.startsBeforeDisjoint(t)?a++:t.properlyContains(o)?(e||(e=new p),e.addRange(t.intersection(o)),a++):o.properlyContains(t)?(e||(e=new p),e.addRange(t.intersection(o)),s++):t.disjoint(o)||(e||(e=new p),e.addRange(t.intersection(o)),t.startsAfterNonDisjoint(o)?a++:o.startsAfterNonDisjoint(t)&&s++)}return e||new p}contains(t){let e=0,r=this._intervals.length-1;for(;e<=r;){let n=e+r>>1,o=this._intervals[n],i=o.a;if(o.b<t)e=n+1;else{if(!(i>t))return!0;r=n-1}}return!1}get isNil(){return null==this._intervals||0===this._intervals.length}get maxElement(){if(this.isNil)throw new RangeError("set is empty");return this._intervals[this._intervals.length-1].b}get minElement(){if(this.isNil)throw new RangeError("set is empty");return this._intervals[0].a}get intervals(){return this._intervals}hashCode(){let t=u.MurmurHash.initialize();for(let e of this._intervals)t=u.MurmurHash.update(t,e.a),t=u.MurmurHash.update(t,e.b);return t=u.MurmurHash.finish(t,2*this._intervals.length),t}equals(t){return null!=t&&t instanceof p&&i.ArrayEqualityComparator.INSTANCE.equals(this._intervals,t._intervals)}toString(t=!1){let e="";if(null==this._intervals||0===this._intervals.length)return"{}";this.size>1&&(e+="{");let r=!0;for(let n of this._intervals){r?r=!1:e+=", ";let o=n.a,i=n.b;o===i?o===h.Token.EOF?e+="<EOF>":e+=t?"'"+String.fromCodePoint(o)+"'":o:e+=t?"'"+String.fromCodePoint(o)+"'..'"+String.fromCodePoint(i)+"'":o+".."+i}return this.size>1&&(e+="}"),e}toStringVocabulary(t){if(null==this._intervals||0===this._intervals.length)return"{}";let e="";this.size>1&&(e+="{");let r=!0;for(let n of this._intervals){r?r=!1:e+=", ";let o=n.a,i=n.b;if(o===i)e+=this.elementName(t,o);else for(let r=o;r<=i;r++)r>o&&(e+=", "),e+=this.elementName(t,r)}return this.size>1&&(e+="}"),e}elementName(t,e){return e===h.Token.EOF?"<EOF>":e===h.Token.EPSILON?"<EPSILON>":t.getDisplayName(e)}get size(){let t=0,e=this._intervals.length;if(1===e){let t=this._intervals[0];return t.b-t.a+1}for(let r=0;r<e;r++){let e=this._intervals[r];t+=e.b-e.a+1}return t}toIntegerList(){let t=new s.IntegerList(this.size),e=this._intervals.length;for(let r=0;r<e;r++){let e=this._intervals[r],n=e.a,o=e.b;for(let e=n;e<=o;e++)t.add(e)}return t}toSet(){let t=new Set;for(let e of this._intervals){let r=e.a,n=e.b;for(let e=r;e<=n;e++)t.add(e)}return t}toArray(){let t=new Array,e=this._intervals.length;for(let r=0;r<e;r++){let e=this._intervals[r],n=e.a,o=e.b;for(let e=n;e<=o;e++)t.push(e)}return t}remove(t){if(this.readonly)throw new Error("can't alter readonly IntervalSet");let e=this._intervals.length;for(let r=0;r<e;r++){let e=this._intervals[r],n=e.a,o=e.b;if(t<n)break;if(t===n&&t===o){this._intervals.splice(r,1);break}if(t===n){this._intervals[r]=a.Interval.of(e.a+1,e.b);break}if(t===o){this._intervals[r]=a.Interval.of(e.a,e.b-1);break}if(t>n&&t<o){let n=e.b;this._intervals[r]=a.Interval.of(e.a,t-1),this.add(t+1,n)}}}get isReadonly(){return this.readonly}setReadonly(t){if(this.readonly&&!t)throw new Error("can't alter readonly IntervalSet");this.readonly=t}}n([c.Override],p.prototype,"addAll",null),n([c.Override],p.prototype,"complement",null),n([c.Override],p.prototype,"subtract",null),n([c.Override],p.prototype,"or",null),n([c.Override],p.prototype,"and",null),n([c.Override],p.prototype,"contains",null),n([c.Override],p.prototype,"isNil",null),n([c.Override],p.prototype,"hashCode",null),n([c.Override],p.prototype,"equals",null),n([o(0,c.NotNull)],p.prototype,"toStringVocabulary",null),n([c.NotNull,o(0,c.NotNull)],p.prototype,"elementName",null),n([c.Override],p.prototype,"size",null),n([c.Override],p.prototype,"remove",null),n([c.NotNull],p,"of",null),n([c.NotNull],p,"subtract",null),e.IntervalSet=p},355:(t,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.MultiMap=void 0;class r extends Map{constructor(){super()}map(t,e){let r=super.get(t);r||(r=[],super.set(t,r)),r.push(e)}getPairs(){let t=[];return this.forEach(((e,r)=>{e.forEach((e=>{t.push([r,e])}))})),t}}e.MultiMap=r},3943:(t,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.MurmurHash=void 0,function(t){function e(t=0){return t}function r(t,e){null==e?e=0:"string"==typeof e?e=function(t){let e=t.length;if(0===e)return 0;let r=0;for(let n=0;n<e;n++){r=(r<<5>>>0)-r+t.charCodeAt(n),r|=0}return r}(e):"object"==typeof e&&(e=e.hashCode());let r=e;return r=Math.imul(r,3432918353),r=r<<15|r>>>17,r=Math.imul(r,461845907),t=(t^=r)<<13|t>>>19,4294967295&(t=Math.imul(t,5)+3864292196)}function n(t,e){return t^=4*e,t^=t>>>16,t=Math.imul(t,2246822507),t^=t>>>13,t=Math.imul(t,3266489909),t^=t>>>16}t.initialize=e,t.update=r,t.finish=n,t.hashCode=function(t,o=0){let i=e(o),s=0;for(let e of t)i=r(i,e),s++;return i=n(i,s),i}}(e.MurmurHash||(e.MurmurHash={}))},8842:function(t,e,r){"use strict";var n=this&&this.__decorate||function(t,e,r,n){var o,i=arguments.length,s=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,r,n);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(i<3?o(s):i>3?o(e,r,s):o(e,r))||s);return i>3&&s&&Object.defineProperty(e,r,s),s};Object.defineProperty(e,"__esModule",{value:!0}),e.ObjectEqualityComparator=void 0;const o=r(8042);class i{hashCode(t){return null==t?0:t.hashCode()}equals(t,e){return null==t?null==e:t.equals(e)}}i.INSTANCE=new i,n([o.Override],i.prototype,"hashCode",null),n([o.Override],i.prototype,"equals",null),e.ObjectEqualityComparator=i},156:(t,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.ParseCancellationException=void 0;class r extends Error{constructor(t){super(t.message),this.cause=t,this.stack=t.stack}getCause(){return this.cause}}e.ParseCancellationException=r},4117:(t,e,r)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.UUID=void 0;const n=r(3943);class o{constructor(t,e,r,n){this.data=new Uint32Array(4),this.data[0]=t,this.data[1]=e,this.data[2]=r,this.data[3]=n}static fromString(t){if(!/^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}$/.test(t))throw new Error("Incorrectly formatted UUID");let e=t.split("-"),r=parseInt(e[0],16),n=(parseInt(e[1],16)<<16>>>0)+parseInt(e[2],16),i=(parseInt(e[3],16)<<16>>>0)+parseInt(e[4].substr(0,4),16),s=parseInt(e[4].substr(-8),16);return new o(r,n,i,s)}hashCode(){return n.MurmurHash.hashCode([this.data[0],this.data[1],this.data[2],this.data[3]])}equals(t){return t===this||t instanceof o&&(this.data[0]===t.data[0]&&this.data[1]===t.data[1]&&this.data[2]===t.data[2]&&this.data[3]===t.data[3])}toString(){return("00000000"+this.data[0].toString(16)).substr(-8)+"-"+("0000"+(this.data[1]>>>16).toString(16)).substr(-4)+"-"+("0000"+this.data[1].toString(16)).substr(-4)+"-"+("0000"+(this.data[2]>>>16).toString(16)).substr(-4)+"-"+("0000"+this.data[2].toString(16)).substr(-4)+("00000000"+this.data[3].toString(16)).substr(-8)}}e.UUID=o},5103:(t,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.toCharArray=e.toMap=e.equals=e.join=e.escapeWhitespace=void 0,e.escapeWhitespace=function(t,e){return e?t.replace(/ /,"·"):t.replace(/\t/,"\\t").replace(/\n/,"\\n").replace(/\r/,"\\r")},e.join=function(t,e){let r="",n=!0;for(let o of t)n?n=!1:r+=e,r+=o;return r},e.equals=function(t,e){return t===e||void 0!==t&&void 0!==e&&t.equals(e)},e.toMap=function(t){let e=new Map;for(let r=0;r<t.length;r++)e.set(t[r],r);return e},e.toCharArray=function(t){if("string"==typeof t){let e=new Uint16Array(t.length);for(let r=0;r<t.length;r++)e[r]=t.charCodeAt(r);return e}return t.toCharArray()}},3530:function(t,e,r){"use strict";var n=this&&this.__decorate||function(t,e,r,n){var o,i=arguments.length,s=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,r,n);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(i<3?o(s):i>3?o(e,r,s):o(e,r))||s);return i>3&&s&&Object.defineProperty(e,r,s),s},o=this&&this.__param||function(t,e){return function(r,n){e(r,n,t)}};Object.defineProperty(e,"__esModule",{value:!0}),e.AbstractParseTreeVisitor=void 0;const i=r(8042);class s{visit(t){return t.accept(this)}visitChildren(t){let e=this.defaultResult(),r=t.childCount;for(let n=0;n<r&&this.shouldVisitNextChild(t,e);n++){let r=t.getChild(n).accept(this);e=this.aggregateResult(e,r)}return e}visitTerminal(t){return this.defaultResult()}visitErrorNode(t){return this.defaultResult()}aggregateResult(t,e){return e}shouldVisitNextChild(t,e){return!0}}n([i.Override,o(0,i.NotNull)],s.prototype,"visit",null),n([i.Override,o(0,i.NotNull)],s.prototype,"visitChildren",null),n([i.Override,o(0,i.NotNull)],s.prototype,"visitTerminal",null),n([i.Override,o(0,i.NotNull)],s.prototype,"visitErrorNode",null),n([o(0,i.NotNull)],s.prototype,"shouldVisitNextChild",null),e.AbstractParseTreeVisitor=s},6912:function(t,e,r){"use strict";var n=this&&this.__decorate||function(t,e,r,n){var o,i=arguments.length,s=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,r,n);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(i<3?o(s):i>3?o(e,r,s):o(e,r))||s);return i>3&&s&&Object.defineProperty(e,r,s),s};Object.defineProperty(e,"__esModule",{value:!0}),e.ErrorNode=void 0;const o=r(8042),i=r(8011);class s extends i.TerminalNode{constructor(t){super(t)}accept(t){return t.visitErrorNode(this)}}n([o.Override],s.prototype,"accept",null),e.ErrorNode=s},5971:(t,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0})},6816:(t,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0})},7761:(t,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.ParseTreeProperty=void 0;e.ParseTreeProperty=class{constructor(t="ParseTreeProperty"){this._symbol=Symbol(t)}get(t){return t[this._symbol]}set(t,e){t[this._symbol]=e}removeFrom(t){let e=t[this._symbol];return delete t[this._symbol],e}}},114:(t,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0})},2248:(t,e,r)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.ParseTreeWalker=void 0;const n=r(6912),o=r(8011),i=r(3627);class s{walk(t,e){let r=[],s=[],a=e,l=0;for(;a;)if(a instanceof n.ErrorNode?t.visitErrorNode&&t.visitErrorNode(a):a instanceof o.TerminalNode?t.visitTerminal&&t.visitTerminal(a):this.enterRule(t,a),a.childCount>0)r.push(a),s.push(l),l=0,a=a.getChild(0);else do{if(a instanceof i.RuleNode&&this.exitRule(t,a),0===r.length){a=void 0,l=0;break}let e=r[r.length-1];if(l++,a=l<e.childCount?e.getChild(l):void 0,a)break;a=r.pop(),l=s.pop()}while(a)}enterRule(t,e){let r=e.ruleContext;t.enterEveryRule&&t.enterEveryRule(r),r.enterRule(t)}exitRule(t,e){let r=e.ruleContext;r.exitRule(t),t.exitEveryRule&&t.exitEveryRule(r)}}e.ParseTreeWalker=s,function(t){t.DEFAULT=new t}(s=e.ParseTreeWalker||(e.ParseTreeWalker={}))},3627:(t,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.RuleNode=void 0;e.RuleNode=class{}},6805:(t,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0})},8011:function(t,e,r){"use strict";var n=this&&this.__decorate||function(t,e,r,n){var o,i=arguments.length,s=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,r,n);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(i<3?o(s):i>3?o(e,r,s):o(e,r))||s);return i>3&&s&&Object.defineProperty(e,r,s),s};Object.defineProperty(e,"__esModule",{value:!0}),e.TerminalNode=void 0;const o=r(8813),i=r(8042),s=r(4966);class a{constructor(t){this._symbol=t}getChild(t){throw new RangeError("Terminal Node has no children.")}get symbol(){return this._symbol}get parent(){return this._parent}setParent(t){this._parent=t}get payload(){return this._symbol}get sourceInterval(){let t=this._symbol.tokenIndex;return new o.Interval(t,t)}get childCount(){return 0}accept(t){return t.visitTerminal(this)}get text(){return this._symbol.text||""}toStringTree(t){return this.toString()}toString(){return this._symbol.type===s.Token.EOF?"<EOF>":this._symbol.text||""}}n([i.Override],a.prototype,"getChild",null),n([i.Override],a.prototype,"parent",null),n([i.Override],a.prototype,"setParent",null),n([i.Override],a.prototype,"payload",null),n([i.Override],a.prototype,"sourceInterval",null),n([i.Override],a.prototype,"childCount",null),n([i.Override],a.prototype,"accept",null),n([i.Override],a.prototype,"text",null),n([i.Override],a.prototype,"toStringTree",null),n([i.Override],a.prototype,"toString",null),e.TerminalNode=a},8017:(t,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0})},5194:function(t,e,r){"use strict";var n=this&&this.__decorate||function(t,e,r,n){var o,i=arguments.length,s=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,r,n);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(i<3?o(s):i>3?o(e,r,s):o(e,r))||s);return i>3&&s&&Object.defineProperty(e,r,s),s},o=this&&this.__param||function(t,e){return function(r,n){e(r,n,t)}};Object.defineProperty(e,"__esModule",{value:!0}),e.Trees=void 0;const i=r(7949),s=r(824),a=r(6912),l=r(8042),u=r(2824),c=r(3208),h=r(3627),p=r(8011),f=r(4966),d=r(5103);class y{static toStringTree(t,e){let r;r=e instanceof u.Parser?e.ruleNames:e;let n=d.escapeWhitespace(this.getNodeText(t,r),!1);if(0===t.childCount)return n;let o="";o+="(",n=d.escapeWhitespace(this.getNodeText(t,r),!1),o+=n,o+=" ";for(let e=0;e<t.childCount;e++)e>0&&(o+=" "),o+=this.toStringTree(t.getChild(e),r);return o+=")",o}static getNodeText(t,e){let r;if(e instanceof u.Parser)r=e.ruleNames;else{if(!e){let e=t.payload;return"string"==typeof e.text?e.text:t.payload.toString()}r=e}if(t instanceof h.RuleNode){let e=t.ruleContext,n=r[e.ruleIndex],o=e.altNumber;return o!==i.ATN.INVALID_ALT_NUMBER?n+":"+o:n}if(t instanceof a.ErrorNode)return t.toString();if(t instanceof p.TerminalNode){return t.symbol.text||""}throw new TypeError("Unexpected node type")}static getChildren(t){let e=[];for(let r=0;r<t.childCount;r++)e.push(t.getChild(r));return e}static getAncestors(t){let e=[],r=t.parent;for(;r;)e.unshift(r),r=r.parent;return e}static isAncestorOf(t,e){if(!t||!e||!t.parent)return!1;let r=e.parent;for(;r;){if(t===r)return!0;r=r.parent}return!1}static findAllTokenNodes(t,e){return y.findAllNodes(t,e,!0)}static findAllRuleNodes(t,e){return y.findAllNodes(t,e,!1)}static findAllNodes(t,e,r){let n=[];return y._findAllNodes(t,e,r,n),n}static _findAllNodes(t,e,r,n){r&&t instanceof p.TerminalNode?t.symbol.type===e&&n.push(t):!r&&t instanceof c.ParserRuleContext&&t.ruleIndex===e&&n.push(t);for(let o=0;o<t.childCount;o++)y._findAllNodes(t.getChild(o),e,r,n)}static getDescendants(t){let e=[];return function t(r){e.push(r);const n=r.childCount;for(let e=0;e<n;e++)t(r.getChild(e))}(t),e}static getRootOfSubtreeEnclosingRegion(t,e,r){let n=t.childCount;for(let o=0;o<n;o++){let n=t.getChild(o),i=y.getRootOfSubtreeEnclosingRegion(n,e,r);if(i)return i}if(t instanceof c.ParserRuleContext){let n=t.stop;if(e>=t.start.tokenIndex&&(null==n||r<=n.tokenIndex))return t}}static stripChildrenOutOfRange(t,e,r,n){if(!t)return;let o=t.childCount;for(let i=0;i<o;i++){let o=t.getChild(i),a=o.sourceInterval;if(o instanceof c.ParserRuleContext&&(a.b<r||a.a>n)&&y.isAncestorOf(o,e)){let e=new s.CommonToken(f.Token.INVALID_TYPE,"...");t.children[i]=new p.TerminalNode(e)}}}static findNodeSuchThat(t,e){if(e(t))return t;let r=t.childCount;for(let n=0;n<r;n++){let r=y.findNodeSuchThat(t.getChild(n),e);if(void 0!==r)return r}}}n([o(0,l.NotNull)],y,"toStringTree",null),n([l.NotNull,o(0,l.NotNull)],y,"getAncestors",null),n([o(0,l.NotNull)],y,"getRootOfSubtreeEnclosingRegion",null),e.Trees=y},5822:function(t,e,r){"use strict";var n=this&&this.__createBinding||(Object.create?function(t,e,r,n){void 0===n&&(n=r),Object.defineProperty(t,n,{enumerable:!0,get:function(){return e[r]}})}:function(t,e,r,n){void 0===n&&(n=r),t[n]=e[r]}),o=this&&this.__exportStar||function(t,e){for(var r in t)"default"===r||Object.prototype.hasOwnProperty.call(e,r)||n(e,t,r)};Object.defineProperty(e,"__esModule",{value:!0}),o(r(3530),e),o(r(6912),e),o(r(5971),e),o(r(6816),e),o(r(7761),e),o(r(114),e),o(r(2248),e),o(r(3627),e),o(r(6805),e),o(r(8011),e),o(r(8017),e),o(r(5194),e)},6581:(t,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.Chunk=void 0;e.Chunk=class{}},2719:function(t,e,r){"use strict";var n=this&&this.__decorate||function(t,e,r,n){var o,i=arguments.length,s=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,r,n);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(i<3?o(s):i>3?o(e,r,s):o(e,r))||s);return i>3&&s&&Object.defineProperty(e,r,s),s},o=this&&this.__param||function(t,e){return function(r,n){e(r,n,t)}};Object.defineProperty(e,"__esModule",{value:!0}),e.ParseTreeMatch=void 0;const i=r(8042);let s=class{constructor(t,e,r,n){if(!t)throw new Error("tree cannot be null");if(!e)throw new Error("pattern cannot be null");if(!r)throw new Error("labels cannot be null");this._tree=t,this._pattern=e,this._labels=r,this._mismatchedNode=n}get(t){let e=this._labels.get(t);if(e&&0!==e.length)return e[e.length-1]}getAll(t){const e=this._labels.get(t);return e||[]}get labels(){return this._labels}get mismatchedNode(){return this._mismatchedNode}get succeeded(){return!this._mismatchedNode}get pattern(){return this._pattern}get tree(){return this._tree}toString(){return`Match ${this.succeeded?"succeeded":"failed"}; found ${this.labels.size} labels`}};n([i.NotNull,o(0,i.NotNull)],s.prototype,"getAll",null),n([i.NotNull],s.prototype,"labels",null),n([i.NotNull],s.prototype,"pattern",null),n([i.NotNull],s.prototype,"tree",null),n([i.Override],s.prototype,"toString",null),s=n([o(0,i.NotNull),o(1,i.NotNull),o(2,i.NotNull)],s),e.ParseTreeMatch=s},2237:function(t,e,r){"use strict";var n=this&&this.__decorate||function(t,e,r,n){var o,i=arguments.length,s=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,r,n);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(i<3?o(s):i>3?o(e,r,s):o(e,r))||s);return i>3&&s&&Object.defineProperty(e,r,s),s},o=this&&this.__param||function(t,e){return function(r,n){e(r,n,t)}};Object.defineProperty(e,"__esModule",{value:!0}),e.ParseTreePattern=void 0;const i=r(8042),s=r(1310);let a=class{constructor(t,e,r,n){this._matcher=t,this._patternRuleIndex=r,this._pattern=e,this._patternTree=n}match(t){return this._matcher.match(t,this)}matches(t){return this._matcher.match(t,this).succeeded}findAll(t,e){let r=s.XPath.findAll(t,e,this._matcher.parser),n=[];for(let t of r){let e=this.match(t);e.succeeded&&n.push(e)}return n}get matcher(){return this._matcher}get pattern(){return this._pattern}get patternRuleIndex(){return this._patternRuleIndex}get patternTree(){return this._patternTree}};n([i.NotNull],a.prototype,"_pattern",void 0),n([i.NotNull],a.prototype,"_patternTree",void 0),n([i.NotNull],a.prototype,"_matcher",void 0),n([i.NotNull,o(0,i.NotNull)],a.prototype,"match",null),n([o(0,i.NotNull)],a.prototype,"matches",null),n([i.NotNull,o(0,i.NotNull),o(1,i.NotNull)],a.prototype,"findAll",null),n([i.NotNull],a.prototype,"matcher",null),n([i.NotNull],a.prototype,"pattern",null),n([i.NotNull],a.prototype,"patternTree",null),a=n([o(0,i.NotNull),o(1,i.NotNull),o(3,i.NotNull)],a),e.ParseTreePattern=a},1293:function(t,e,r){"use strict";var n=this&&this.__decorate||function(t,e,r,n){var o,i=arguments.length,s=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,r,n);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(i<3?o(s):i>3?o(e,r,s):o(e,r))||s);return i>3&&s&&Object.defineProperty(e,r,s),s},o=this&&this.__param||function(t,e){return function(r,n){e(r,n,t)}};Object.defineProperty(e,"__esModule",{value:!0}),e.ParseTreePatternMatcher=void 0;const i=r(9701),s=r(3675),a=r(4321),l=r(7683),u=r(355),c=r(8042),h=r(156),p=r(627),f=r(3208),d=r(2719),y=r(2237),g=r(3998),_=r(3627),m=r(5144),v=r(5760),S=r(8011),N=r(8965),x=r(4966),T=r(6290);class b{constructor(t,e){this.start="<",this.stop=">",this.escape="\\",this.escapeRE=/\\/g,this._lexer=t,this._parser=e}setDelimiters(t,e,r){if(!t)throw new Error("start cannot be null or empty");if(!e)throw new Error("stop cannot be null or empty");this.start=t,this.stop=e,this.escape=r,this.escapeRE=new RegExp(r.replace(/[.*+?^${}()|[\]\\]/g,"\\$&"),"g")}matches(t,e,r=0){if("string"==typeof e){let n=this.compile(e,r);return this.matches(t,n)}{let r=new u.MultiMap;return!this.matchImpl(t,e.patternTree,r)}}match(t,e,r=0){if("string"==typeof e){let n=this.compile(e,r);return this.match(t,n)}{let r=new u.MultiMap,n=this.matchImpl(t,e.patternTree,r);return new d.ParseTreeMatch(t,e,r,n)}}compile(t,e){let r=this.tokenize(t),n=new l.ListTokenSource(r),o=new a.CommonTokenStream(n);const s=this._parser;let u,c=new p.ParserInterpreter(s.grammarFileName,s.vocabulary,s.ruleNames,s.getATNWithBypassAlts(),o);try{c.errorHandler=new i.BailErrorStrategy,u=c.parse(e)}catch(t){throw t instanceof h.ParseCancellationException?t.getCause():t instanceof g.RecognitionException?t:t instanceof Error?new b.CannotInvokeStartRule(t):t}if(o.LA(1)!==x.Token.EOF)throw new b.StartRuleDoesNotConsumeFullPattern;return new y.ParseTreePattern(this,t,e,u)}get lexer(){return this._lexer}get parser(){return this._parser}matchImpl(t,e,r){if(!t)throw new TypeError("tree cannot be null");if(!e)throw new TypeError("patternTree cannot be null");if(t instanceof S.TerminalNode&&e instanceof S.TerminalNode){let n;if(t.symbol.type===e.symbol.type)if(e.symbol instanceof T.TokenTagToken){let n=e.symbol;r.map(n.tokenName,t);const o=n.label;o&&r.map(o,t)}else t.text===e.text||n||(n=t);else n||(n=t);return n}if(t instanceof f.ParserRuleContext&&e instanceof f.ParserRuleContext){let n,o=this.getRuleTagToken(e);if(o){if(t.ruleContext.ruleIndex===e.ruleContext.ruleIndex){r.map(o.ruleName,t);const e=o.label;e&&r.map(e,t)}else n||(n=t);return n}if(t.childCount!==e.childCount)return n||(n=t),n;let i=t.childCount;for(let n=0;n<i;n++){let o=this.matchImpl(t.getChild(n),e.getChild(n),r);if(o)return o}return n}return t}getRuleTagToken(t){if(t instanceof _.RuleNode&&1===t.childCount&&t.getChild(0)instanceof S.TerminalNode){let e=t.getChild(0);if(e.symbol instanceof m.RuleTagToken)return e.symbol}}tokenize(t){let e=this.split(t),r=[];for(let n of e)if(n instanceof v.TagChunk){let e=n;const o=e.tag.substr(0,1);if(o===o.toUpperCase()){let n=this._parser.getTokenType(e.tag);if(n===x.Token.INVALID_TYPE)throw new Error("Unknown token "+e.tag+" in pattern: "+t);let o=new T.TokenTagToken(e.tag,n,e.label);r.push(o)}else{if(o!==o.toLowerCase())throw new Error("invalid tag: "+e.tag+" in pattern: "+t);{let n=this._parser.getRuleIndex(e.tag);if(-1===n)throw new Error("Unknown rule "+e.tag+" in pattern: "+t);let o=this._parser.getATNWithBypassAlts().ruleToTokenType[n];r.push(new m.RuleTagToken(e.tag,o,e.label))}}}else{let t=n;this._lexer.inputStream=s.CharStreams.fromString(t.text);let e=this._lexer.nextToken();for(;e.type!==x.Token.EOF;)r.push(e),e=this._lexer.nextToken()}return r}split(t){let e=0,r=t.length,n=[],o=[],i=[];for(;e<r;)e===t.indexOf(this.escape+this.start,e)?e+=this.escape.length+this.start.length:e===t.indexOf(this.escape+this.stop,e)?e+=this.escape.length+this.stop.length:e===t.indexOf(this.start,e)?(o.push(e),e+=this.start.length):e===t.indexOf(this.stop,e)?(i.push(e),e+=this.stop.length):e++;if(o.length>i.length)throw new Error("unterminated tag in pattern: "+t);if(o.length<i.length)throw new Error("missing start tag in pattern: "+t);let s=o.length;for(let e=0;e<s;e++)if(o[e]>=i[e])throw new Error("tag delimiters out of order in pattern: "+t);if(0===s){let e=t.substring(0,r);n.push(new N.TextChunk(e))}if(s>0&&o[0]>0){let e=t.substring(0,o[0]);n.push(new N.TextChunk(e))}for(let e=0;e<s;e++){let r,a=t.substring(o[e]+this.start.length,i[e]),l=a,u=a.indexOf(":");if(u>=0&&(r=a.substring(0,u),l=a.substring(u+1,a.length)),n.push(new v.TagChunk(l,r)),e+1<s){let r=t.substring(i[e]+this.stop.length,o[e+1]);n.push(new N.TextChunk(r))}}if(s>0){let e=i[s-1]+this.stop.length;if(e<r){let o=t.substring(e,r);n.push(new N.TextChunk(o))}}for(let t=0;t<n.length;t++){let e=n[t];if(e instanceof N.TextChunk){let r=e.text.replace(this.escapeRE,"");r.length<e.text.length&&(n[t]=new N.TextChunk(r))}}return n}}n([c.NotNull,o(1,c.NotNull)],b.prototype,"match",null),n([c.NotNull],b.prototype,"lexer",null),n([c.NotNull],b.prototype,"parser",null),n([o(0,c.NotNull),o(1,c.NotNull),o(2,c.NotNull)],b.prototype,"matchImpl",null),e.ParseTreePatternMatcher=b,function(t){class e extends Error{constructor(t){super(`CannotInvokeStartRule: ${t}`),this.error=t}}t.CannotInvokeStartRule=e;class r extends Error{constructor(){super("StartRuleDoesNotConsumeFullPattern")}}t.StartRuleDoesNotConsumeFullPattern=r}(b=e.ParseTreePatternMatcher||(e.ParseTreePatternMatcher={}))},5144:function(t,e,r){"use strict";var n=this&&this.__decorate||function(t,e,r,n){var o,i=arguments.length,s=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,r,n);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(i<3?o(s):i>3?o(e,r,s):o(e,r))||s);return i>3&&s&&Object.defineProperty(e,r,s),s},o=this&&this.__param||function(t,e){return function(r,n){e(r,n,t)}};Object.defineProperty(e,"__esModule",{value:!0}),e.RuleTagToken=void 0;const i=r(8042),s=r(4966);let a=class{constructor(t,e,r){if(null==t||0===t.length)throw new Error("ruleName cannot be null or empty.");this._ruleName=t,this.bypassTokenType=e,this._label=r}get ruleName(){return this._ruleName}get label(){return this._label}get channel(){return s.Token.DEFAULT_CHANNEL}get text(){return null!=this._label?"<"+this._label+":"+this._ruleName+">":"<"+this._ruleName+">"}get type(){return this.bypassTokenType}get line(){return 0}get charPositionInLine(){return-1}get tokenIndex(){return-1}get startIndex(){return-1}get stopIndex(){return-1}get tokenSource(){}get inputStream(){}toString(){return this._ruleName+":"+this.bypassTokenType}};n([i.NotNull],a.prototype,"ruleName",null),n([i.Override],a.prototype,"channel",null),n([i.Override],a.prototype,"text",null),n([i.Override],a.prototype,"type",null),n([i.Override],a.prototype,"line",null),n([i.Override],a.prototype,"charPositionInLine",null),n([i.Override],a.prototype,"tokenIndex",null),n([i.Override],a.prototype,"startIndex",null),n([i.Override],a.prototype,"stopIndex",null),n([i.Override],a.prototype,"tokenSource",null),n([i.Override],a.prototype,"inputStream",null),n([i.Override],a.prototype,"toString",null),a=n([o(0,i.NotNull)],a),e.RuleTagToken=a},5760:function(t,e,r){"use strict";var n=this&&this.__decorate||function(t,e,r,n){var o,i=arguments.length,s=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,r,n);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(i<3?o(s):i>3?o(e,r,s):o(e,r))||s);return i>3&&s&&Object.defineProperty(e,r,s),s};Object.defineProperty(e,"__esModule",{value:!0}),e.TagChunk=void 0;const o=r(6581),i=r(8042);class s extends o.Chunk{constructor(t,e){if(super(),null==t||0===t.length)throw new Error("tag cannot be null or empty");this._tag=t,this._label=e}get tag(){return this._tag}get label(){return this._label}toString(){return null!=this._label?this._label+":"+this._tag:this._tag}}n([i.NotNull],s.prototype,"tag",null),n([i.Override],s.prototype,"toString",null),e.TagChunk=s},8965:function(t,e,r){"use strict";var n=this&&this.__decorate||function(t,e,r,n){var o,i=arguments.length,s=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,r,n);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(i<3?o(s):i>3?o(e,r,s):o(e,r))||s);return i>3&&s&&Object.defineProperty(e,r,s),s},o=this&&this.__param||function(t,e){return function(r,n){e(r,n,t)}};Object.defineProperty(e,"__esModule",{value:!0}),e.TextChunk=void 0;const i=r(6581),s=r(8042);let a=class extends i.Chunk{constructor(t){if(super(),null==t)throw new Error("text cannot be null");this._text=t}get text(){return this._text}toString(){return"'"+this._text+"'"}};n([s.NotNull],a.prototype,"_text",void 0),n([s.NotNull],a.prototype,"text",null),n([s.Override],a.prototype,"toString",null),a=n([o(0,s.NotNull)],a),e.TextChunk=a},6290:function(t,e,r){"use strict";var n=this&&this.__decorate||function(t,e,r,n){var o,i=arguments.length,s=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,r,n);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(i<3?o(s):i>3?o(e,r,s):o(e,r))||s);return i>3&&s&&Object.defineProperty(e,r,s),s},o=this&&this.__param||function(t,e){return function(r,n){e(r,n,t)}};Object.defineProperty(e,"__esModule",{value:!0}),e.TokenTagToken=void 0;const i=r(824),s=r(8042);let a=class extends i.CommonToken{constructor(t,e,r){super(e),this._tokenName=t,this._label=r}get tokenName(){return this._tokenName}get label(){return this._label}get text(){return null!=this._label?"<"+this._label+":"+this._tokenName+">":"<"+this._tokenName+">"}toString(){return this._tokenName+":"+this.type}};n([s.NotNull],a.prototype,"_tokenName",void 0),n([s.NotNull],a.prototype,"tokenName",null),n([s.Override],a.prototype,"text",null),n([s.Override],a.prototype,"toString",null),a=n([o(0,s.NotNull)],a),e.TokenTagToken=a},1310:(t,e,r)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.XPath=void 0;const n=r(3675),o=r(4321),i=r(5324),s=r(3208),a=r(4966),l=r(1257),u=r(393),c=r(8386),h=r(4703),p=r(8576),f=r(4559),d=r(7636),y=r(881);class g{constructor(t,e){this.parser=t,this.path=e,this.elements=this.split(e)}split(t){let e=new l.XPathLexer(n.CharStreams.fromString(t));e.recover=t=>{throw t},e.removeErrorListeners(),e.addErrorListener(new u.XPathLexerErrorListener);let r=new o.CommonTokenStream(e);try{r.fill()}catch(r){if(r instanceof i.LexerNoViableAltException){let n="Invalid tokens or characters at index "+e.charPositionInLine+" in path '"+t+"' -- "+r.message;throw new RangeError(n)}throw r}let s=r.getTokens(),c=[],h=s.length,p=0;t:for(;p<h;){let t,e=s[p];switch(e.type){case l.XPathLexer.ROOT:case l.XPathLexer.ANYWHERE:let r=e.type===l.XPathLexer.ANYWHERE;p++,t=s[p];let n=t.type===l.XPathLexer.BANG;n&&(p++,t=s[p]);let o=this.getXPathElement(t,r);o.invert=n,c.push(o),p++;break;case l.XPathLexer.TOKEN_REF:case l.XPathLexer.RULE_REF:case l.XPathLexer.WILDCARD:c.push(this.getXPathElement(e,!1)),p++;break;case a.Token.EOF:break t;default:throw new Error("Unknowth path element "+e)}}return c}getXPathElement(t,e){if(t.type===a.Token.EOF)throw new Error("Missing path element at end of path");let r=t.text;if(null==r)throw new Error("Expected wordToken to have text content.");let n=this.parser.getTokenType(r),o=this.parser.getRuleIndex(r);switch(t.type){case l.XPathLexer.WILDCARD:return e?new d.XPathWildcardAnywhereElement:new y.XPathWildcardElement;case l.XPathLexer.TOKEN_REF:case l.XPathLexer.STRING:if(n===a.Token.INVALID_TYPE)throw new Error(r+" at index "+t.startIndex+" isn't a valid token name");return e?new p.XPathTokenAnywhereElement(r,n):new f.XPathTokenElement(r,n);default:if(-1===o)throw new Error(r+" at index "+t.startIndex+" isn't a valid rule name");return e?new c.XPathRuleAnywhereElement(r,o):new h.XPathRuleElement(r,o)}}static findAll(t,e,r){return new g(r,e).evaluate(t)}evaluate(t){let e=new s.ParserRuleContext;e.addChild(t);let r=new Set([e]),n=0;for(;n<this.elements.length;){let t=new Set;for(let e of r)if(e.childCount>0){this.elements[n].evaluate(e).forEach(t.add,t)}n++,r=t}return r}}e.XPath=g,g.WILDCARD="*",g.NOT="!"},1298:function(t,e,r){"use strict";var n=this&&this.__decorate||function(t,e,r,n){var o,i=arguments.length,s=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,r,n);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(i<3?o(s):i>3?o(e,r,s):o(e,r))||s);return i>3&&s&&Object.defineProperty(e,r,s),s};Object.defineProperty(e,"__esModule",{value:!0}),e.XPathElement=void 0;const o=r(8042);class i{constructor(t){this.nodeName=t,this.invert=!1}toString(){let t=this.invert?"!":"";return Object.constructor.name+"["+t+this.nodeName+"]"}}n([o.Override],i.prototype,"toString",null),e.XPathElement=i},1257:(t,e,r)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.XPathLexer=void 0;const n=r(9963),o=r(9557),i=r(2178),s=r(6763),a=r(5103);class l extends o.Lexer{constructor(t){super(t),this._interp=new i.LexerATNSimulator(l._ATN,this)}get vocabulary(){return l.VOCABULARY}get grammarFileName(){return"XPathLexer.g4"}get ruleNames(){return l.ruleNames}get serializedATN(){return l._serializedATN}get channelNames(){return l.channelNames}get modeNames(){return l.modeNames}action(t,e,r){switch(e){case 4:this.ID_action(t,r)}}ID_action(t,e){switch(e){case 0:let t=this.text;t.charAt(0)===t.charAt(0).toUpperCase()?this.type=l.TOKEN_REF:this.type=l.RULE_REF}}static get _ATN(){return l.__ATN||(l.__ATN=(new n.ATNDeserializer).deserialize(a.toCharArray(l._serializedATN))),l.__ATN}}e.XPathLexer=l,l.TOKEN_REF=1,l.RULE_REF=2,l.ANYWHERE=3,l.ROOT=4,l.WILDCARD=5,l.BANG=6,l.ID=7,l.STRING=8,l.channelNames=["DEFAULT_TOKEN_CHANNEL","HIDDEN"],l.modeNames=["DEFAULT_MODE"],l.ruleNames=["ANYWHERE","ROOT","WILDCARD","BANG","ID","NameChar","NameStartChar","STRING"],l._LITERAL_NAMES=[void 0,void 0,void 0,"'//'","'/'","'*'","'!'"],l._SYMBOLIC_NAMES=[void 0,"TOKEN_REF","RULE_REF","ANYWHERE","ROOT","WILDCARD","BANG","ID","STRING"],l.VOCABULARY=new s.VocabularyImpl(l._LITERAL_NAMES,l._SYMBOLIC_NAMES,[]),l._serializedATNSegments=2,l._serializedATNSegment0='줝쪺֍꾺体؇쉁\n2\b\t\t\t\t\t\t\b\t\b\t\t\t\n\f"\v\b\b\t\t\t,\n\t\f\t\t/\v\t\t\t-\n\t\b\v\t\r\nʶ\n2;C\\aac|¡¬¬¯¯··¼¼ÂØÚøú˃ˈ˓ˢ˦ˮˮ˰˰̂Ͷ͸͹ͼͿ΁΁ΈΈΊΌΎΎΐΣΥϷϹ҃҅҉ҌԱԳ՘՛՛գ։֓ֿׁׁ׃ׄ׆ׇ׉׉ג׬ײ״؂؇ؒ؜؞؞آ٫ٰەۗ۟ۡ۪۬۾܁܁ܑ݌ݏ޳߂߷߼߼ࠂ࠯ࡂ࡝ࢢࢶࢸࢿࣖ॥२ॱॳঅই঎঑঒কপবল঴঴স঻া৆৉৊্৐৙৙৞য়ৡ৥২৳ਃਅਇ਌਑਒ਕਪਬਲ਴ਵ਷ਸ਺਻ਾਾੀ੄੉੊੍੏੓੓ਜ਼ਫ਼੠੠੨੷ઃઅઇએઑઓકપબલ઴વષ઻ાેૉો્૏૒૒ૢ૥૨૱ૻૻଃଅଇ଎଑଒କପବଲ଴ଵଷ଻ା୆୉୊୍୏୘୙୞ୟୡ୥୨ୱ୳୳஄அஇ஌ஐஒஔ஗஛ஜஞஞ஠஡஥஦ப஬ர஻ீ௄ைொௌ௏௒௒௙௙௨௱ంఅఇఎఐఒఔపబ఻ిెైొౌ౏౗ౘౚ౜ౢ౥౨౱ಂಅಇಎಐಒಔಪಬವಷ಻ಾೆೈೊೌ೏೗೘ೠೠೢ೥೨ೱೳ೴ഃഅഇഎഐഒഔ഼ിെൈൊൌ൐ൖ൙ൡ൥൨൱ർඁ඄අඇ඘ගඳඵල඿඿ෂ෈෌෌ෑූෘෘේ෡෨෱෴෵ฃ฼โ๐๒๛຃ຄຆຆຉຊຌຌຏຏຖນປມຣລວວຩຩຬອຯົຽ຿ໂໆ່່໊໏໒໛ໞ໡༂༂༚༛༢༫༷༷༹༹༻༻ཀཉཋ཮ཱི྆ྈྙྛ྾࿈࿈ဂ။ၒ႟ႢჇ჉჉჏჏გჼჾቊቌ቏ቒቘቚቚቜ቟ቢኊኌ኏ኒኲኴ኷ኺዀዂዂዄ዇ዊዘዚጒጔ጗ጚ፜፟፡ᎂ᎑Ꭲ᏷ᏺ᏿ᐃ᙮ᙱᚁᚃ᚜ᚢ᛬ᛰ᛺ᜂᜎᜐ᜖ᜢ᜶ᝂ᝕ᝢᝮᝰᝲ᝴᝵គ៕៙៙៞៟២៫᠍᠐᠒᠛ᠢ᡹ᢂ᢬ᢲ᣷ᤂᤠᤢ᤭ᤲ᤽᥈᥯ᥲ᥶ᦂ᦭ᦲ᧋᧒᧛ᨂ᨝ᨢ᩠ᩢ᩾᪁᪋᪒᪛᪩᪩᪲ᪿᬂ᭍᭒᭛᭭᭵ᮂ᯵ᰂ᰹᱂᱋ᱏ᱿ᲂᲊ᳒᳔᳖᳸ᳺ᳻ᴂ᷷᷽἗Ἒ἟ἢ὇Ὂ὏ὒὙὛὛὝὝὟὟὡ὿ᾂᾶᾸι῀῀ῄῆῈ῎ῒ῕Ῐ῝ῢ΅ῴῶῸ῾‍‑‬‰⁁⁂⁖⁖⁢⁦⁨ⁱ⁳⁳₁₁ₒ₞⃒⃞⃣⃣⃧⃲℄℄℉℉ℌℕ℗℗ℛ℟ΩΩℨℨKKℬℯℱ℻ℾ⅁ⅇ⅋⅐⅐Ⅲ↊ⰂⰰⰲⱠⱢ⳦Ⳮ⳵ⴂⴧ⴩⴩⴯⴯ⴲ⵩⵱⵱ⶁ⶘ⶢⶨⶪⶰⶲⶸⶺⷀⷂⷈⷊⷐⷒⷘⷚⷠⷢ⸁⸱⸱〇〉〣〱〳〷〺〾ぃ゘゛゜ゟァィーヾ㄁ㄇㄯㄳ㆐ㆢㆼㇲ㈁㐂䶷丂鿗ꀂ꒎ꓒ꓿ꔂ꘎ꘒ꘭Ꙃ꙱ꙶꙿꚁ꛳ꜙ꜡Ꜥ꞊ꞍꞰꞲꞹꟹ꠩ꡂ꡵ꢂ꣇꣒꣛꣢꣹ꣽꣽꣿꣿ꤂꤯ꤲ꥕ꥢ꥾ꦂ꧂꧑꧛ꧢꨀꨂ꨸ꩂ꩏꩒꩛ꩢ꩸ꩼ꫄ꫝ꫟ꫢ꫱ꫴ꫸ꬃ꬈ꬋ꬐ꬓ꬘ꬢꬨꬪꬰꬲꭜꭞꭧꭲ꯬꯮꯯꯲꯻갂힥ힲ퟈ퟍ퟽車﩯全﫛ﬂ﬈ﬕ﬙ײַשׁשּׁטּךּמּנּנּ﭂ףּ﭅צּרּ﮳ﯕ﴿ﵒ﶑ﶔ﷉ﷲ﷽︂︑︢︱︵︶﹏﹑ﹲﹶﹸ﻾！！２；Ｃ＼ａａｃ｜ｨ￀ￄ￉ￌ￑ￔ￙ￜ￞￻�\r(*<>?AOR_üłŶǿǿʂʞʢ˒ˢˢ̂̡̲͌͒ͼ΂Ο΢υϊϑϓϗЂҟҢҫҲӕӚӽԂԩԲե؂ܸ݂ݗݢݩࠂࠇࠊࠊࠌ࠷࠹࠺࠾࠾ࡁࡗࡢࡸࢂࢠ࣢ࣴࣶࣷंगढऻংহীুਂਅਇਈ਎ਕਗਙਛਵ਺਼ੁੁ੢੾ંઞૂૉો૨ଂଷୂୗୢ୴ஂஓంొಂ಴ೂ೴ဂ၈ၨၱႁႼႿႿგცჲ჻ᄂᄶᄸᅁᅒᅵᅸᅸᆂᇆᇌᇎᇒᇜᇞᇞሂሓሕሹቀቀኂኈኊኊኌ኏ኑኟኡኪኲዬዲዻጂጅጇጎ጑ጒጕጪጬጲጴጵጷጻጾፆፉፊፍፏፒፒፙፙ፟፥፨፮፲፶ᐂᑌᑒᑛᒂᓇᓉᓉᓒᓛᖂᖷᖺᗂᗚᗟᘂᙂᙆᙆᙒᙛᚂᚹᛂᛋᜂ᜛ᜟᜭᜲ᜻ᢢᣫᤁᤁ᫂᫺ᰂᰊᰌ᰸᰺᱂᱒ᱛᱴᲑᲔᲩᲫᲸ ⎛␂⑰⒂╅。㐰䐂䙈栂樺橂橠橢橫櫒櫯櫲櫶欂欸歂歅歒歛步歹歿殑漂潆潒澀澑澡濢濢瀂蟮蠂諴뀂뀃밂뱬뱲뱾벂벊벒벛벟베벢벥텧텫텯톄톇톍톬톯퉄퉆퐂푖푘풞풠풡풤풤풧풨풫풮풰풻풽풽풿퓅퓇픇픉플픏픖픘픞픠픻픽핀핂핆핈핈핌핒핔횧횪훂후훜훞훼훾휖휘휶휸흐흒흰흲힊힌힪힬ퟄퟆퟍퟐ\ud801\uda02\uda38\uda3d\uda6e\uda77\uda77\uda86\uda86\uda9d\udaa1\udaa3\udab1ꛘ꜂뜶띂렟렢캣﨟"ĂǱɀC\\c|¬¬··¼¼ÂØÚøú˃ˈ˓ˢ˦ˮˮ˰˰ͲͶ͸͹ͼͿ΁΁ΈΈΊΌΎΎΐΣΥϷϹ҃ҌԱԳ՘՛՛գ։ג׬ײ״آٌٰٱٳەۗۗۧۨ۰۱ۼ۾܁܁ܒܒܔܱݏާ޳޳ߌ߬߶߷߼߼ࠂࠗࠜࠜࠦࠦࠪࠪࡂ࡚ࢢࢶࢸࢿआऻिि॒॒ग़ॣॳংই঎঑঒কপবল঴঴স঻িি৐৐৞য়ৡৣ৲৳ਇ਌਑਒ਕਪਬਲ਴ਵ਷ਸ਺਻ਜ਼ਫ਼੠੠ੴ੶ઇએઑઓકપબલ઴વષ઻િિ૒૒ૢૣૻૻଇ଎଑଒କପବଲ଴ଵଷ଻ିି୞ୟୡୣ୳୳அஅஇ஌ஐஒஔ஗஛ஜஞஞ஠஡஥஦ப஬ர஻௒௒ఇఎఐఒఔపబ఻ిిౚ౜ౢౣಂಂಇಎಐಒಔಪಬವಷ಻ಿಿೠೠೢೣೳ೴ഇഎഐഒഔ഼ിി൐൐ൖ൘ൡൣർඁඇ඘ගඳඵල඿඿ෂ෈ฃาิีโ่຃ຄຆຆຉຊຌຌຏຏຖນປມຣລວວຩຩຬອຯາິີ຿຿ໂໆ່່ໞ໡༂༂གཉཋ཮ྊྎဂာ၁၁ၒၗၜၟၣၣၧၨၰၲၷႃ႐႐ႢჇ჉჉჏჏გჼჾቊቌ቏ቒቘቚቚቜ቟ቢኊኌ኏ኒኲኴ኷ኺዀዂዂዄ዇ዊዘዚጒጔ጗ጚ፜ᎂ᎑Ꭲ᏷ᏺ᏿ᐃ᙮ᙱᚁᚃ᚜ᚢ᛬ᛰ᛺ᜂᜎᜐᜓᜢᜳᝂᝓᝢᝮᝰᝲគ឵៙៙៞៞ᠢ᡹ᢂᢆᢉᢪ᢬᢬ᢲ᣷ᤂᤠᥒ᥯ᥲ᥶ᦂ᦭ᦲ᧋ᨂᨘᨢᩖ᪩᪩ᬇᬵᭇ᭍ᮅᮢ᮰᮱ᮼᯧᰂᰥᱏ᱑ᱜ᱿ᲂᲊᳫᳮᳰᳳ᳷᳸ᴂ᷁Ḃ἗Ἒ἟ἢ὇Ὂ὏ὒὙὛὛὝὝὟὟὡ὿ᾂᾶᾸι῀῀ῄῆῈ῎ῒ῕Ῐ῝ῢ΅ῴῶῸ῾⁳⁳₁₁ₒ₞℄℄℉℉ℌℕ℗℗ℛ℟ΩΩℨℨKKℬℯℱ℻ℾ⅁ⅇ⅋⅐⅐Ⅲ↊ⰂⰰⰲⱠⱢ⳦Ⳮ⳰⳴⳵ⴂⴧ⴩⴩⴯⴯ⴲ⵩⵱⵱ⶂ⶘ⶢⶨⶪⶰⶲⶸⶺⷀⷂⷈⷊⷐⷒⷘⷚⷠ⸱⸱〇〉〣〫〳〷〺〾ぃ゘ゟァィーヾ㄁ㄇㄯㄳ㆐ㆢㆼㇲ㈁㐂䶷丂鿗ꀂ꒎ꓒ꓿ꔂ꘎ꘒ꘡꘬꘭Ꙃ꙰ꚁꚟꚢ꛱ꜙ꜡Ꜥ꞊ꞍꞰꞲꞹꟹꠃꠅꠇꠉꠌꠎꠤꡂ꡵ꢄꢵꣴ꣹ꣽꣽꣿꣿꤌꤧꤲꥈꥢ꥾ꦆꦴ꧑꧑ꧢꧦꧨ꧱ꧼꨀꨂꨪꩂꩄꩆꩍꩢ꩸ꩼꩼꪀꪱꪳꪳꪷꪸꪻ꪿ꫂꫂ꫄꫄ꫝ꫟ꫢꫬꫴ꫶ꬃ꬈ꬋ꬐ꬓ꬘ꬢꬨꬪꬰꬲꭜꭞꭧꭲꯤ갂힥ힲ퟈ퟍ퟽車﩯全﫛ﬂ﬈ﬕ﬙ײַײַﬡשׁשּׁטּךּמּנּנּ﭂ףּ﭅צּרּ﮳ﯕ﴿ﵒ﶑ﶔ﷉ﷲ﷽ﹲﹶﹸ﻾Ｃ＼ｃ｜ｨ￀ￄ￉ￌ￑ￔ￙ￜ￞\r(*<>?AOR_üłŶʂʞʢ˒̂̡̲͌͒ͷ΂Ο΢υϊϑϓϗЂҟҲӕӚӽԂԩԲե؂ܸ݂ݗݢݩࠂࠇࠊࠊࠌ࠷࠹࠺࠾࠾ࡁࡗࡢࡸࢂࢠ࣢ࣴࣶࣷंगढऻংহীুਂਂ਒ਕਗਙਛਵ੢੾ંઞૂૉો૦ଂଷୂୗୢ୴ஂஓంొಂ಴ೂ೴စ္ႅႱგცᄅᄨᅒᅴᅸᅸᆅᆴᇃᇆᇜᇜᇞᇞሂሓሕርኂኈኊኊኌ኏ኑኟኡኪኲዠጇጎ጑ጒጕጪጬጲጴጵጷጻጿጿፒፒ፟፣ᐂᐶᑉᑌᒂᒱᓆᓇᓉᓉᖂᖰᗚᗝᘂᘱᙆᙆᚂᚬᜂ᜛ᢢᣡᤁᤁ᫂᫺ᰂᰊᰌᰰ᱂᱂ᱴᲑ ⎛␂⑰⒂╅。㐰䐂䙈栂樺橂橠櫒櫯欂欱歂歅步歹歿殑漂潆潒潒澕澡濢濢瀂蟮蠂諴뀂뀃밂뱬뱲뱾벂벊벒벛퐂푖푘풞풠풡풤풤풧풨풫풮풰풻풽풽풿퓅퓇픇픉플픏픖픘픞픠픻픽핀핂핆핈핈',l._serializedATNSegment1="핌핒핔횧횪훂후훜훞훼훾휖휘휶휸흐흒흰흲힊힌힪힬ퟄퟆퟍꛘ꜂뜶띂렟렢캣﨟1\t\v\t\v\r%')111,\b#\n \b\r\"  !!#\" #$\b$\f%&\t&'(\t()-)*,\v+*,/-.-+.0/-01)1 -",l._serializedATN=a.join([l._serializedATNSegment0,l._serializedATNSegment1],"")},393:function(t,e,r){"use strict";var n=this&&this.__decorate||function(t,e,r,n){var o,i=arguments.length,s=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,r,n);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(i<3?o(s):i>3?o(e,r,s):o(e,r))||s);return i>3&&s&&Object.defineProperty(e,r,s),s};Object.defineProperty(e,"__esModule",{value:!0}),e.XPathLexerErrorListener=void 0;const o=r(8042);class i{syntaxError(t,e,r,n,o,i){}}n([o.Override],i.prototype,"syntaxError",null),e.XPathLexerErrorListener=i},8386:function(t,e,r){"use strict";var n=this&&this.__decorate||function(t,e,r,n){var o,i=arguments.length,s=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,r,n);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(i<3?o(s):i>3?o(e,r,s):o(e,r))||s);return i>3&&s&&Object.defineProperty(e,r,s),s};Object.defineProperty(e,"__esModule",{value:!0}),e.XPathRuleAnywhereElement=void 0;const o=r(8042),i=r(5194),s=r(1298);class a extends s.XPathElement{constructor(t,e){super(t),this.ruleIndex=e}evaluate(t){return i.Trees.findAllRuleNodes(t,this.ruleIndex)}}n([o.Override],a.prototype,"evaluate",null),e.XPathRuleAnywhereElement=a},4703:function(t,e,r){"use strict";var n=this&&this.__decorate||function(t,e,r,n){var o,i=arguments.length,s=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,r,n);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(i<3?o(s):i>3?o(e,r,s):o(e,r))||s);return i>3&&s&&Object.defineProperty(e,r,s),s};Object.defineProperty(e,"__esModule",{value:!0}),e.XPathRuleElement=void 0;const o=r(3208),i=r(8042),s=r(5194),a=r(1298);class l extends a.XPathElement{constructor(t,e){super(t),this.ruleIndex=e}evaluate(t){let e=[];for(let r of s.Trees.getChildren(t))r instanceof o.ParserRuleContext&&(r.ruleIndex===this.ruleIndex&&!this.invert||r.ruleIndex!==this.ruleIndex&&this.invert)&&e.push(r);return e}}n([i.Override],l.prototype,"evaluate",null),e.XPathRuleElement=l},8576:function(t,e,r){"use strict";var n=this&&this.__decorate||function(t,e,r,n){var o,i=arguments.length,s=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,r,n);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(i<3?o(s):i>3?o(e,r,s):o(e,r))||s);return i>3&&s&&Object.defineProperty(e,r,s),s};Object.defineProperty(e,"__esModule",{value:!0}),e.XPathTokenAnywhereElement=void 0;const o=r(8042),i=r(5194),s=r(1298);class a extends s.XPathElement{constructor(t,e){super(t),this.tokenType=e}evaluate(t){return i.Trees.findAllTokenNodes(t,this.tokenType)}}n([o.Override],a.prototype,"evaluate",null),e.XPathTokenAnywhereElement=a},4559:function(t,e,r){"use strict";var n=this&&this.__decorate||function(t,e,r,n){var o,i=arguments.length,s=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,r,n);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(i<3?o(s):i>3?o(e,r,s):o(e,r))||s);return i>3&&s&&Object.defineProperty(e,r,s),s};Object.defineProperty(e,"__esModule",{value:!0}),e.XPathTokenElement=void 0;const o=r(8042),i=r(8011),s=r(5194),a=r(1298);class l extends a.XPathElement{constructor(t,e){super(t),this.tokenType=e}evaluate(t){let e=[];for(let r of s.Trees.getChildren(t))r instanceof i.TerminalNode&&(r.symbol.type===this.tokenType&&!this.invert||r.symbol.type!==this.tokenType&&this.invert)&&e.push(r);return e}}n([o.Override],l.prototype,"evaluate",null),e.XPathTokenElement=l},7636:function(t,e,r){"use strict";var n=this&&this.__decorate||function(t,e,r,n){var o,i=arguments.length,s=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,r,n);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(i<3?o(s):i>3?o(e,r,s):o(e,r))||s);return i>3&&s&&Object.defineProperty(e,r,s),s};Object.defineProperty(e,"__esModule",{value:!0}),e.XPathWildcardAnywhereElement=void 0;const o=r(8042),i=r(5194),s=r(1310),a=r(1298);class l extends a.XPathElement{constructor(){super(s.XPath.WILDCARD)}evaluate(t){return this.invert?[]:i.Trees.getDescendants(t)}}n([o.Override],l.prototype,"evaluate",null),e.XPathWildcardAnywhereElement=l},881:function(t,e,r){"use strict";var n=this&&this.__decorate||function(t,e,r,n){var o,i=arguments.length,s=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,r,n);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(i<3?o(s):i>3?o(e,r,s):o(e,r))||s);return i>3&&s&&Object.defineProperty(e,r,s),s};Object.defineProperty(e,"__esModule",{value:!0}),e.XPathWildcardElement=void 0;const o=r(8042),i=r(5194),s=r(1310),a=r(1298);class l extends a.XPathElement{constructor(){super(s.XPath.WILDCARD)}evaluate(t){let e=[];if(this.invert)return e;for(let r of i.Trees.getChildren(t))e.push(r);return e}}n([o.Override],l.prototype,"evaluate",null),e.XPathWildcardElement=l},9282:(t,e,r)=>{"use strict";function n(t){return(n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var o,i,s=r(2136).codes,a=s.ERR_AMBIGUOUS_ARGUMENT,l=s.ERR_INVALID_ARG_TYPE,u=s.ERR_INVALID_ARG_VALUE,c=s.ERR_INVALID_RETURN_VALUE,h=s.ERR_MISSING_ARGS,p=r(5961),f=r(9539).inspect,d=r(9539).types,y=d.isPromise,g=d.isRegExp,_=Object.assign?Object.assign:r(8091).assign,m=Object.is?Object.is:r(609);new Map;function v(){var t=r(9158);o=t.isDeepEqual,i=t.isDeepStrictEqual}var S=!1,N=t.exports=O,x={};function T(t){if(t.message instanceof Error)throw t.message;throw new p(t)}function b(t,e,r,n){if(!r){var o=!1;if(0===e)o=!0,n="No value argument passed to `assert.ok()`";else if(n instanceof Error)throw n;var i=new p({actual:r,expected:!0,message:n,operator:"==",stackStartFn:t});throw i.generatedMessage=o,i}}function O(){for(var t=arguments.length,e=new Array(t),r=0;r<t;r++)e[r]=arguments[r];b.apply(void 0,[O,e.length].concat(e))}N.fail=function t(e,r,n,o,i){var s,a=arguments.length;if(0===a)s="Failed";else if(1===a)n=e,e=void 0;else{if(!1===S){S=!0;var l=process.emitWarning?process.emitWarning:console.warn.bind(console);l("assert.fail() with more than one argument is deprecated. Please use assert.strictEqual() instead or only pass a message.","DeprecationWarning","DEP0094")}2===a&&(o="!=")}if(n instanceof Error)throw n;var u={actual:e,expected:r,operator:void 0===o?"fail":o,stackStartFn:i||t};void 0!==n&&(u.message=n);var c=new p(u);throw s&&(c.message=s,c.generatedMessage=!0),c},N.AssertionError=p,N.ok=O,N.equal=function t(e,r,n){if(arguments.length<2)throw new h("actual","expected");e!=r&&T({actual:e,expected:r,message:n,operator:"==",stackStartFn:t})},N.notEqual=function t(e,r,n){if(arguments.length<2)throw new h("actual","expected");e==r&&T({actual:e,expected:r,message:n,operator:"!=",stackStartFn:t})},N.deepEqual=function t(e,r,n){if(arguments.length<2)throw new h("actual","expected");void 0===o&&v(),o(e,r)||T({actual:e,expected:r,message:n,operator:"deepEqual",stackStartFn:t})},N.notDeepEqual=function t(e,r,n){if(arguments.length<2)throw new h("actual","expected");void 0===o&&v(),o(e,r)&&T({actual:e,expected:r,message:n,operator:"notDeepEqual",stackStartFn:t})},N.deepStrictEqual=function t(e,r,n){if(arguments.length<2)throw new h("actual","expected");void 0===o&&v(),i(e,r)||T({actual:e,expected:r,message:n,operator:"deepStrictEqual",stackStartFn:t})},N.notDeepStrictEqual=function t(e,r,n){if(arguments.length<2)throw new h("actual","expected");void 0===o&&v();i(e,r)&&T({actual:e,expected:r,message:n,operator:"notDeepStrictEqual",stackStartFn:t})},N.strictEqual=function t(e,r,n){if(arguments.length<2)throw new h("actual","expected");m(e,r)||T({actual:e,expected:r,message:n,operator:"strictEqual",stackStartFn:t})},N.notStrictEqual=function t(e,r,n){if(arguments.length<2)throw new h("actual","expected");m(e,r)&&T({actual:e,expected:r,message:n,operator:"notStrictEqual",stackStartFn:t})};var E=function t(e,r,n){var o=this;!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),r.forEach((function(t){t in e&&(void 0!==n&&"string"==typeof n[t]&&g(e[t])&&e[t].test(n[t])?o[t]=n[t]:o[t]=e[t])}))};function A(t,e,r,n,o,s){if(!(r in t)||!i(t[r],e[r])){if(!n){var a=new E(t,o),l=new E(e,o,t),u=new p({actual:a,expected:l,operator:"deepStrictEqual",stackStartFn:s});throw u.actual=t,u.expected=e,u.operator=s.name,u}T({actual:t,expected:e,message:n,operator:s.name,stackStartFn:s})}}function P(t,e,r,i){if("function"!=typeof e){if(g(e))return e.test(t);if(2===arguments.length)throw new l("expected",["Function","RegExp"],e);if("object"!==n(t)||null===t){var s=new p({actual:t,expected:e,message:r,operator:"deepStrictEqual",stackStartFn:i});throw s.operator=i.name,s}var a=Object.keys(e);if(e instanceof Error)a.push("name","message");else if(0===a.length)throw new u("error",e,"may not be an empty object");return void 0===o&&v(),a.forEach((function(n){"string"==typeof t[n]&&g(e[n])&&e[n].test(t[n])||A(t,e,n,r,a,i)})),!0}return void 0!==e.prototype&&t instanceof e||!Error.isPrototypeOf(e)&&!0===e.call({},t)}function C(t){if("function"!=typeof t)throw new l("fn","Function",t);try{t()}catch(t){return t}return x}function R(t){return y(t)||null!==t&&"object"===n(t)&&"function"==typeof t.then&&"function"==typeof t.catch}function I(t){return Promise.resolve().then((function(){var e;if("function"==typeof t){if(!R(e=t()))throw new c("instance of Promise","promiseFn",e)}else{if(!R(t))throw new l("promiseFn",["Function","Promise"],t);e=t}return Promise.resolve().then((function(){return e})).then((function(){return x})).catch((function(t){return t}))}))}function w(t,e,r,o){if("string"==typeof r){if(4===arguments.length)throw new l("error",["Object","Error","Function","RegExp"],r);if("object"===n(e)&&null!==e){if(e.message===r)throw new a("error/message",'The error message "'.concat(e.message,'" is identical to the message.'))}else if(e===r)throw new a("error/message",'The error "'.concat(e,'" is identical to the message.'));o=r,r=void 0}else if(null!=r&&"object"!==n(r)&&"function"!=typeof r)throw new l("error",["Object","Error","Function","RegExp"],r);if(e===x){var i="";r&&r.name&&(i+=" (".concat(r.name,")")),i+=o?": ".concat(o):".";var s="rejects"===t.name?"rejection":"exception";T({actual:void 0,expected:r,operator:t.name,message:"Missing expected ".concat(s).concat(i),stackStartFn:t})}if(r&&!P(e,r,o,t))throw e}function k(t,e,r,n){if(e!==x){if("string"==typeof r&&(n=r,r=void 0),!r||P(e,r)){var o=n?": ".concat(n):".",i="doesNotReject"===t.name?"rejection":"exception";T({actual:e,expected:r,operator:t.name,message:"Got unwanted ".concat(i).concat(o,"\n")+'Actual message: "'.concat(e&&e.message,'"'),stackStartFn:t})}throw e}}function L(){for(var t=arguments.length,e=new Array(t),r=0;r<t;r++)e[r]=arguments[r];b.apply(void 0,[L,e.length].concat(e))}N.throws=function t(e){for(var r=arguments.length,n=new Array(r>1?r-1:0),o=1;o<r;o++)n[o-1]=arguments[o];w.apply(void 0,[t,C(e)].concat(n))},N.rejects=function t(e){for(var r=arguments.length,n=new Array(r>1?r-1:0),o=1;o<r;o++)n[o-1]=arguments[o];return I(e).then((function(e){return w.apply(void 0,[t,e].concat(n))}))},N.doesNotThrow=function t(e){for(var r=arguments.length,n=new Array(r>1?r-1:0),o=1;o<r;o++)n[o-1]=arguments[o];k.apply(void 0,[t,C(e)].concat(n))},N.doesNotReject=function t(e){for(var r=arguments.length,n=new Array(r>1?r-1:0),o=1;o<r;o++)n[o-1]=arguments[o];return I(e).then((function(e){return k.apply(void 0,[t,e].concat(n))}))},N.ifError=function t(e){if(null!=e){var r="ifError got unwanted exception: ";"object"===n(e)&&"string"==typeof e.message?0===e.message.length&&e.constructor?r+=e.constructor.name:r+=e.message:r+=f(e);var o=new p({actual:e,expected:null,operator:"ifError",message:r,stackStartFn:t}),i=e.stack;if("string"==typeof i){var s=i.split("\n");s.shift();for(var a=o.stack.split("\n"),l=0;l<s.length;l++){var u=a.indexOf(s[l]);if(-1!==u){a=a.slice(0,u);break}}o.stack="".concat(a.join("\n"),"\n").concat(s.join("\n"))}throw o}},N.strict=_(L,N,{equal:N.strictEqual,deepEqual:N.deepStrictEqual,notEqual:N.notStrictEqual,notDeepEqual:N.notDeepStrictEqual}),N.strict.strict=N.strict},5961:(t,e,r)=>{"use strict";function n(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function o(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}function i(t,e){return!e||"object"!==p(e)&&"function"!=typeof e?s(t):e}function s(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function a(t){var e="function"==typeof Map?new Map:void 0;return(a=function(t){if(null===t||(r=t,-1===Function.toString.call(r).indexOf("[native code]")))return t;var r;if("function"!=typeof t)throw new TypeError("Super expression must either be null or a function");if(void 0!==e){if(e.has(t))return e.get(t);e.set(t,n)}function n(){return u(t,arguments,h(this).constructor)}return n.prototype=Object.create(t.prototype,{constructor:{value:n,enumerable:!1,writable:!0,configurable:!0}}),c(n,t)})(t)}function l(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(t){return!1}}function u(t,e,r){return(u=l()?Reflect.construct:function(t,e,r){var n=[null];n.push.apply(n,e);var o=new(Function.bind.apply(t,n));return r&&c(o,r.prototype),o}).apply(null,arguments)}function c(t,e){return(c=Object.setPrototypeOf||function(t,e){return t.__proto__=e,t})(t,e)}function h(t){return(h=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function p(t){return(p="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var f=r(9539).inspect,d=r(2136).codes.ERR_INVALID_ARG_TYPE;function y(t,e,r){return(void 0===r||r>t.length)&&(r=t.length),t.substring(r-e.length,r)===e}var g="",_="",m="",v="",S={deepStrictEqual:"Expected values to be strictly deep-equal:",strictEqual:"Expected values to be strictly equal:",strictEqualObject:'Expected "actual" to be reference-equal to "expected":',deepEqual:"Expected values to be loosely deep-equal:",equal:"Expected values to be loosely equal:",notDeepStrictEqual:'Expected "actual" not to be strictly deep-equal to:',notStrictEqual:'Expected "actual" to be strictly unequal to:',notStrictEqualObject:'Expected "actual" not to be reference-equal to "expected":',notDeepEqual:'Expected "actual" not to be loosely deep-equal to:',notEqual:'Expected "actual" to be loosely unequal to:',notIdentical:"Values identical but not reference-equal:"};function N(t){var e=Object.keys(t),r=Object.create(Object.getPrototypeOf(t));return e.forEach((function(e){r[e]=t[e]})),Object.defineProperty(r,"message",{value:t.message}),r}function x(t){return f(t,{compact:!1,customInspect:!1,depth:1e3,maxArrayLength:1/0,showHidden:!1,breakLength:1/0,showProxy:!1,sorted:!0,getters:!0})}function T(t,e,r){var n="",o="",i=0,s="",a=!1,l=x(t),u=l.split("\n"),c=x(e).split("\n"),h=0,f="";if("strictEqual"===r&&"object"===p(t)&&"object"===p(e)&&null!==t&&null!==e&&(r="strictEqualObject"),1===u.length&&1===c.length&&u[0]!==c[0]){var d=u[0].length+c[0].length;if(d<=10){if(!("object"===p(t)&&null!==t||"object"===p(e)&&null!==e||0===t&&0===e))return"".concat(S[r],"\n\n")+"".concat(u[0]," !== ").concat(c[0],"\n")}else if("strictEqualObject"!==r){if(d<(process.stderr&&process.stderr.isTTY?process.stderr.columns:80)){for(;u[0][h]===c[0][h];)h++;h>2&&(f="\n  ".concat(function(t,e){if(e=Math.floor(e),0==t.length||0==e)return"";var r=t.length*e;for(e=Math.floor(Math.log(e)/Math.log(2));e;)t+=t,e--;return t+t.substring(0,r-t.length)}(" ",h),"^"),h=0)}}}for(var N=u[u.length-1],T=c[c.length-1];N===T&&(h++<2?s="\n  ".concat(N).concat(s):n=N,u.pop(),c.pop(),0!==u.length&&0!==c.length);)N=u[u.length-1],T=c[c.length-1];var b=Math.max(u.length,c.length);if(0===b){var O=l.split("\n");if(O.length>30)for(O[26]="".concat(g,"...").concat(v);O.length>27;)O.pop();return"".concat(S.notIdentical,"\n\n").concat(O.join("\n"),"\n")}h>3&&(s="\n".concat(g,"...").concat(v).concat(s),a=!0),""!==n&&(s="\n  ".concat(n).concat(s),n="");var E=0,A=S[r]+"\n".concat(_,"+ actual").concat(v," ").concat(m,"- expected").concat(v),P=" ".concat(g,"...").concat(v," Lines skipped");for(h=0;h<b;h++){var C=h-i;if(u.length<h+1)C>1&&h>2&&(C>4?(o+="\n".concat(g,"...").concat(v),a=!0):C>3&&(o+="\n  ".concat(c[h-2]),E++),o+="\n  ".concat(c[h-1]),E++),i=h,n+="\n".concat(m,"-").concat(v," ").concat(c[h]),E++;else if(c.length<h+1)C>1&&h>2&&(C>4?(o+="\n".concat(g,"...").concat(v),a=!0):C>3&&(o+="\n  ".concat(u[h-2]),E++),o+="\n  ".concat(u[h-1]),E++),i=h,o+="\n".concat(_,"+").concat(v," ").concat(u[h]),E++;else{var R=c[h],I=u[h],w=I!==R&&(!y(I,",")||I.slice(0,-1)!==R);w&&y(R,",")&&R.slice(0,-1)===I&&(w=!1,I+=","),w?(C>1&&h>2&&(C>4?(o+="\n".concat(g,"...").concat(v),a=!0):C>3&&(o+="\n  ".concat(u[h-2]),E++),o+="\n  ".concat(u[h-1]),E++),i=h,o+="\n".concat(_,"+").concat(v," ").concat(I),n+="\n".concat(m,"-").concat(v," ").concat(R),E+=2):(o+=n,n="",1!==C&&0!==h||(o+="\n  ".concat(I),E++))}if(E>20&&h<b-2)return"".concat(A).concat(P,"\n").concat(o,"\n").concat(g,"...").concat(v).concat(n,"\n")+"".concat(g,"...").concat(v)}return"".concat(A).concat(a?P:"","\n").concat(o).concat(n).concat(s).concat(f)}var b=function(t){function e(t){var r;if(function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),"object"!==p(t)||null===t)throw new d("options","Object",t);var n=t.message,o=t.operator,a=t.stackStartFn,l=t.actual,u=t.expected,c=Error.stackTraceLimit;if(Error.stackTraceLimit=0,null!=n)r=i(this,h(e).call(this,String(n)));else if(process.stderr&&process.stderr.isTTY&&(process.stderr&&process.stderr.getColorDepth&&1!==process.stderr.getColorDepth()?(g="[34m",_="[32m",v="[39m",m="[31m"):(g="",_="",v="",m="")),"object"===p(l)&&null!==l&&"object"===p(u)&&null!==u&&"stack"in l&&l instanceof Error&&"stack"in u&&u instanceof Error&&(l=N(l),u=N(u)),"deepStrictEqual"===o||"strictEqual"===o)r=i(this,h(e).call(this,T(l,u,o)));else if("notDeepStrictEqual"===o||"notStrictEqual"===o){var f=S[o],y=x(l).split("\n");if("notStrictEqual"===o&&"object"===p(l)&&null!==l&&(f=S.notStrictEqualObject),y.length>30)for(y[26]="".concat(g,"...").concat(v);y.length>27;)y.pop();r=1===y.length?i(this,h(e).call(this,"".concat(f," ").concat(y[0]))):i(this,h(e).call(this,"".concat(f,"\n\n").concat(y.join("\n"),"\n")))}else{var b=x(l),O="",E=S[o];"notDeepEqual"===o||"notEqual"===o?(b="".concat(S[o],"\n\n").concat(b)).length>1024&&(b="".concat(b.slice(0,1021),"...")):(O="".concat(x(u)),b.length>512&&(b="".concat(b.slice(0,509),"...")),O.length>512&&(O="".concat(O.slice(0,509),"...")),"deepEqual"===o||"equal"===o?b="".concat(E,"\n\n").concat(b,"\n\nshould equal\n\n"):O=" ".concat(o," ").concat(O)),r=i(this,h(e).call(this,"".concat(b).concat(O)))}return Error.stackTraceLimit=c,r.generatedMessage=!n,Object.defineProperty(s(r),"name",{value:"AssertionError [ERR_ASSERTION]",enumerable:!1,writable:!0,configurable:!0}),r.code="ERR_ASSERTION",r.actual=l,r.expected=u,r.operator=o,Error.captureStackTrace&&Error.captureStackTrace(s(r),a),r.stack,r.name="AssertionError",i(r)}var r,a,l;return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),e&&c(t,e)}(e,t),r=e,(a=[{key:"toString",value:function(){return"".concat(this.name," [").concat(this.code,"]: ").concat(this.message)}},{key:f.custom,value:function(t,e){return f(this,function(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{},o=Object.keys(r);"function"==typeof Object.getOwnPropertySymbols&&(o=o.concat(Object.getOwnPropertySymbols(r).filter((function(t){return Object.getOwnPropertyDescriptor(r,t).enumerable})))),o.forEach((function(e){n(t,e,r[e])}))}return t}({},e,{customInspect:!1,depth:0}))}}])&&o(r.prototype,a),l&&o(r,l),e}(a(Error));t.exports=b},2136:(t,e,r)=>{"use strict";function n(t){return(n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function o(t,e){return!e||"object"!==n(e)&&"function"!=typeof e?function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t):e}function i(t){return(i=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function s(t,e){return(s=Object.setPrototypeOf||function(t,e){return t.__proto__=e,t})(t,e)}var a,l,u={};function c(t,e,r){r||(r=Error);var n=function(r){function n(r,s,a){var l;return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,n),(l=o(this,i(n).call(this,function(t,r,n){return"string"==typeof e?e:e(t,r,n)}(r,s,a)))).code=t,l}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),e&&s(t,e)}(n,r),n}(r);u[t]=n}function h(t,e){if(Array.isArray(t)){var r=t.length;return t=t.map((function(t){return String(t)})),r>2?"one of ".concat(e," ").concat(t.slice(0,r-1).join(", "),", or ")+t[r-1]:2===r?"one of ".concat(e," ").concat(t[0]," or ").concat(t[1]):"of ".concat(e," ").concat(t[0])}return"of ".concat(e," ").concat(String(t))}c("ERR_AMBIGUOUS_ARGUMENT",'The "%s" argument is ambiguous. %s',TypeError),c("ERR_INVALID_ARG_TYPE",(function(t,e,o){var i,s,l,u;if(void 0===a&&(a=r(9282)),a("string"==typeof t,"'name' must be a string"),"string"==typeof e&&(s="not ",e.substr(!l||l<0?0:+l,s.length)===s)?(i="must not be",e=e.replace(/^not /,"")):i="must be",function(t,e,r){return(void 0===r||r>t.length)&&(r=t.length),t.substring(r-e.length,r)===e}(t," argument"))u="The ".concat(t," ").concat(i," ").concat(h(e,"type"));else{var c=function(t,e,r){return"number"!=typeof r&&(r=0),!(r+e.length>t.length)&&-1!==t.indexOf(e,r)}(t,".")?"property":"argument";u='The "'.concat(t,'" ').concat(c," ").concat(i," ").concat(h(e,"type"))}return u+=". Received type ".concat(n(o))}),TypeError),c("ERR_INVALID_ARG_VALUE",(function(t,e){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"is invalid";void 0===l&&(l=r(9539));var o=l.inspect(e);return o.length>128&&(o="".concat(o.slice(0,128),"...")),"The argument '".concat(t,"' ").concat(n,". Received ").concat(o)}),TypeError,RangeError),c("ERR_INVALID_RETURN_VALUE",(function(t,e,r){var o;return o=r&&r.constructor&&r.constructor.name?"instance of ".concat(r.constructor.name):"type ".concat(n(r)),"Expected ".concat(t,' to be returned from the "').concat(e,'"')+" function but got ".concat(o,".")}),TypeError),c("ERR_MISSING_ARGS",(function(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];void 0===a&&(a=r(9282)),a(e.length>0,"At least one arg needs to be specified");var o="The ",i=e.length;switch(e=e.map((function(t){return'"'.concat(t,'"')})),i){case 1:o+="".concat(e[0]," argument");break;case 2:o+="".concat(e[0]," and ").concat(e[1]," arguments");break;default:o+=e.slice(0,i-1).join(", "),o+=", and ".concat(e[i-1]," arguments")}return"".concat(o," must be specified")}),TypeError),t.exports.codes=u},9158:(t,e,r)=>{"use strict";function n(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=[],n=!0,o=!1,i=void 0;try{for(var s,a=t[Symbol.iterator]();!(n=(s=a.next()).done)&&(r.push(s.value),!e||r.length!==e);n=!0);}catch(t){o=!0,i=t}finally{try{n||null==a.return||a.return()}finally{if(o)throw i}}return r}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance")}()}function o(t){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var i=void 0!==/a/g.flags,s=function(t){var e=[];return t.forEach((function(t){return e.push(t)})),e},a=function(t){var e=[];return t.forEach((function(t,r){return e.push([r,t])})),e},l=Object.is?Object.is:r(609),u=Object.getOwnPropertySymbols?Object.getOwnPropertySymbols:function(){return[]},c=Number.isNaN?Number.isNaN:r(360);function h(t){return t.call.bind(t)}var p=h(Object.prototype.hasOwnProperty),f=h(Object.prototype.propertyIsEnumerable),d=h(Object.prototype.toString),y=r(9539).types,g=y.isAnyArrayBuffer,_=y.isArrayBufferView,m=y.isDate,v=y.isMap,S=y.isRegExp,N=y.isSet,x=y.isNativeError,T=y.isBoxedPrimitive,b=y.isNumberObject,O=y.isStringObject,E=y.isBooleanObject,A=y.isBigIntObject,P=y.isSymbolObject,C=y.isFloat32Array,R=y.isFloat64Array;function I(t){if(0===t.length||t.length>10)return!0;for(var e=0;e<t.length;e++){var r=t.charCodeAt(e);if(r<48||r>57)return!0}return 10===t.length&&t>=Math.pow(2,32)}function w(t){return Object.keys(t).filter(I).concat(u(t).filter(Object.prototype.propertyIsEnumerable.bind(t)))}function k(t,e){if(t===e)return 0;for(var r=t.length,n=e.length,o=0,i=Math.min(r,n);o<i;++o)if(t[o]!==e[o]){r=t[o],n=e[o];break}return r<n?-1:n<r?1:0}function L(t,e,r,n){if(t===e)return 0!==t||(!r||l(t,e));if(r){if("object"!==o(t))return"number"==typeof t&&c(t)&&c(e);if("object"!==o(e)||null===t||null===e)return!1;if(Object.getPrototypeOf(t)!==Object.getPrototypeOf(e))return!1}else{if(null===t||"object"!==o(t))return(null===e||"object"!==o(e))&&t==e;if(null===e||"object"!==o(e))return!1}var s,a,u,h,p=d(t);if(p!==d(e))return!1;if(Array.isArray(t)){if(t.length!==e.length)return!1;var f=w(t),y=w(e);return f.length===y.length&&D(t,e,r,n,1,f)}if("[object Object]"===p&&(!v(t)&&v(e)||!N(t)&&N(e)))return!1;if(m(t)){if(!m(e)||Date.prototype.getTime.call(t)!==Date.prototype.getTime.call(e))return!1}else if(S(t)){if(!S(e)||(u=t,h=e,!(i?u.source===h.source&&u.flags===h.flags:RegExp.prototype.toString.call(u)===RegExp.prototype.toString.call(h))))return!1}else if(x(t)||t instanceof Error){if(t.message!==e.message||t.name!==e.name)return!1}else{if(_(t)){if(r||!C(t)&&!R(t)){if(!function(t,e){return t.byteLength===e.byteLength&&0===k(new Uint8Array(t.buffer,t.byteOffset,t.byteLength),new Uint8Array(e.buffer,e.byteOffset,e.byteLength))}(t,e))return!1}else if(!function(t,e){if(t.byteLength!==e.byteLength)return!1;for(var r=0;r<t.byteLength;r++)if(t[r]!==e[r])return!1;return!0}(t,e))return!1;var I=w(t),L=w(e);return I.length===L.length&&D(t,e,r,n,0,I)}if(N(t))return!(!N(e)||t.size!==e.size)&&D(t,e,r,n,2);if(v(t))return!(!v(e)||t.size!==e.size)&&D(t,e,r,n,3);if(g(t)){if(a=e,(s=t).byteLength!==a.byteLength||0!==k(new Uint8Array(s),new Uint8Array(a)))return!1}else if(T(t)&&!function(t,e){return b(t)?b(e)&&l(Number.prototype.valueOf.call(t),Number.prototype.valueOf.call(e)):O(t)?O(e)&&String.prototype.valueOf.call(t)===String.prototype.valueOf.call(e):E(t)?E(e)&&Boolean.prototype.valueOf.call(t)===Boolean.prototype.valueOf.call(e):A(t)?A(e)&&BigInt.prototype.valueOf.call(t)===BigInt.prototype.valueOf.call(e):P(e)&&Symbol.prototype.valueOf.call(t)===Symbol.prototype.valueOf.call(e)}(t,e))return!1}return D(t,e,r,n,0)}function j(t,e){return e.filter((function(e){return f(t,e)}))}function D(t,e,r,n,o,i){if(5===arguments.length){i=Object.keys(t);var s=Object.keys(e);if(i.length!==s.length)return!1}for(var a=0;a<i.length;a++)if(!p(e,i[a]))return!1;if(r&&5===arguments.length){var l=u(t);if(0!==l.length){var c=0;for(a=0;a<l.length;a++){var h=l[a];if(f(t,h)){if(!f(e,h))return!1;i.push(h),c++}else if(f(e,h))return!1}var d=u(e);if(l.length!==d.length&&j(e,d).length!==c)return!1}else{var y=u(e);if(0!==y.length&&0!==j(e,y).length)return!1}}if(0===i.length&&(0===o||1===o&&0===t.length||0===t.size))return!0;if(void 0===n)n={val1:new Map,val2:new Map,position:0};else{var g=n.val1.get(t);if(void 0!==g){var _=n.val2.get(e);if(void 0!==_)return g===_}n.position++}n.val1.set(t,n.position),n.val2.set(e,n.position);var m=H(t,e,r,i,n,o);return n.val1.delete(t),n.val2.delete(e),m}function M(t,e,r,n){for(var o=s(t),i=0;i<o.length;i++){var a=o[i];if(L(e,a,r,n))return t.delete(a),!0}return!1}function F(t){switch(o(t)){case"undefined":return null;case"object":return;case"symbol":return!1;case"string":t=+t;case"number":if(c(t))return!1}return!0}function z(t,e,r){var n=F(r);return null!=n?n:e.has(n)&&!t.has(n)}function U(t,e,r,n,o){var i=F(r);if(null!=i)return i;var s=e.get(i);return!(void 0===s&&!e.has(i)||!L(n,s,!1,o))&&(!t.has(i)&&L(n,s,!1,o))}function B(t,e,r,n,o,i){for(var a=s(t),l=0;l<a.length;l++){var u=a[l];if(L(r,u,o,i)&&L(n,e.get(u),o,i))return t.delete(u),!0}return!1}function H(t,e,r,i,l,u){var c=0;if(2===u){if(!function(t,e,r,n){for(var i=null,a=s(t),l=0;l<a.length;l++){var u=a[l];if("object"===o(u)&&null!==u)null===i&&(i=new Set),i.add(u);else if(!e.has(u)){if(r)return!1;if(!z(t,e,u))return!1;null===i&&(i=new Set),i.add(u)}}if(null!==i){for(var c=s(e),h=0;h<c.length;h++){var p=c[h];if("object"===o(p)&&null!==p){if(!M(i,p,r,n))return!1}else if(!r&&!t.has(p)&&!M(i,p,r,n))return!1}return 0===i.size}return!0}(t,e,r,l))return!1}else if(3===u){if(!function(t,e,r,i){for(var s=null,l=a(t),u=0;u<l.length;u++){var c=n(l[u],2),h=c[0],p=c[1];if("object"===o(h)&&null!==h)null===s&&(s=new Set),s.add(h);else{var f=e.get(h);if(void 0===f&&!e.has(h)||!L(p,f,r,i)){if(r)return!1;if(!U(t,e,h,p,i))return!1;null===s&&(s=new Set),s.add(h)}}}if(null!==s){for(var d=a(e),y=0;y<d.length;y++){var g=n(d[y],2),_=(h=g[0],g[1]);if("object"===o(h)&&null!==h){if(!B(s,t,h,_,r,i))return!1}else if(!(r||t.has(h)&&L(t.get(h),_,!1,i)||B(s,t,h,_,!1,i)))return!1}return 0===s.size}return!0}(t,e,r,l))return!1}else if(1===u)for(;c<t.length;c++){if(!p(t,c)){if(p(e,c))return!1;for(var h=Object.keys(t);c<h.length;c++){var f=h[c];if(!p(e,f)||!L(t[f],e[f],r,l))return!1}return h.length===Object.keys(e).length}if(!p(e,c)||!L(t[c],e[c],r,l))return!1}for(c=0;c<i.length;c++){var d=i[c];if(!L(t[d],e[d],r,l))return!1}return!0}t.exports={isDeepEqual:function(t,e){return L(t,e,false)},isDeepStrictEqual:function(t,e){return L(t,e,true)}}},6314:(t,e,r)=>{"use strict";var n=["BigInt64Array","BigUint64Array","Float32Array","Float64Array","Int16Array","Int32Array","Int8Array","Uint16Array","Uint32Array","Uint8Array","Uint8ClampedArray"];t.exports=function(){for(var t=[],e=0;e<n.length;e++)"function"==typeof r.g[n[e]]&&(t[t.length]=n[e]);return t}},1924:(t,e,r)=>{"use strict";var n=r(210),o=r(5559),i=o(n("String.prototype.indexOf"));t.exports=function(t,e){var r=n(t,!!e);return"function"==typeof r&&i(t,".prototype.")>-1?o(r):r}},5559:(t,e,r)=>{"use strict";var n=r(8612),o=r(210),i=o("%Function.prototype.apply%"),s=o("%Function.prototype.call%"),a=o("%Reflect.apply%",!0)||n.call(s,i),l=o("%Object.getOwnPropertyDescriptor%",!0),u=o("%Object.defineProperty%",!0),c=o("%Math.max%");if(u)try{u({},"a",{value:1})}catch(t){u=null}t.exports=function(t){var e=a(n,s,arguments);if(l&&u){var r=l(e,"length");r.configurable&&u(e,"length",{value:1+c(0,t.length-(arguments.length-1))})}return e};var h=function(){return a(n,i,arguments)};u?u(t.exports,"apply",{value:h}):t.exports.apply=h},4289:(t,e,r)=>{"use strict";var n=r(2215),o="function"==typeof Symbol&&"symbol"==typeof Symbol("foo"),i=Object.prototype.toString,s=Array.prototype.concat,a=Object.defineProperty,l=a&&function(){var t={};try{for(var e in a(t,"x",{enumerable:!1,value:t}),t)return!1;return t.x===t}catch(t){return!1}}(),u=function(t,e,r,n){var o;(!(e in t)||"function"==typeof(o=n)&&"[object Function]"===i.call(o)&&n())&&(l?a(t,e,{configurable:!0,enumerable:!1,value:r,writable:!0}):t[e]=r)},c=function(t,e){var r=arguments.length>2?arguments[2]:{},i=n(e);o&&(i=s.call(i,Object.getOwnPropertySymbols(e)));for(var a=0;a<i.length;a+=1)u(t,i[a],e[i[a]],r[i[a]])};c.supportsDescriptors=!!l,t.exports=c},4079:(t,e,r)=>{"use strict";var n=r(210)("%Object.getOwnPropertyDescriptor%");if(n)try{n([],"length")}catch(t){n=null}t.exports=n},8091:t=>{"use strict";function e(t,e){if(null==t)throw new TypeError("Cannot convert first argument to object");for(var r=Object(t),n=1;n<arguments.length;n++){var o=arguments[n];if(null!=o)for(var i=Object.keys(Object(o)),s=0,a=i.length;s<a;s++){var l=i[s],u=Object.getOwnPropertyDescriptor(o,l);void 0!==u&&u.enumerable&&(r[l]=o[l])}}return r}t.exports={assign:e,polyfill:function(){Object.assign||Object.defineProperty(Object,"assign",{enumerable:!1,configurable:!0,writable:!0,value:e})}}},9804:t=>{var e=Object.prototype.hasOwnProperty,r=Object.prototype.toString;t.exports=function(t,n,o){if("[object Function]"!==r.call(n))throw new TypeError("iterator must be a function");var i=t.length;if(i===+i)for(var s=0;s<i;s++)n.call(o,t[s],s,t);else for(var a in t)e.call(t,a)&&n.call(o,t[a],a,t)}},7648:t=>{"use strict";var e="Function.prototype.bind called on incompatible ",r=Array.prototype.slice,n=Object.prototype.toString,o="[object Function]";t.exports=function(t){var i=this;if("function"!=typeof i||n.call(i)!==o)throw new TypeError(e+i);for(var s,a=r.call(arguments,1),l=function(){if(this instanceof s){var e=i.apply(this,a.concat(r.call(arguments)));return Object(e)===e?e:this}return i.apply(t,a.concat(r.call(arguments)))},u=Math.max(0,i.length-a.length),c=[],h=0;h<u;h++)c.push("$"+h);if(s=Function("binder","return function ("+c.join(",")+"){ return binder.apply(this,arguments); }")(l),i.prototype){var p=function(){};p.prototype=i.prototype,s.prototype=new p,p.prototype=null}return s}},8612:(t,e,r)=>{"use strict";var n=r(7648);t.exports=Function.prototype.bind||n},210:(t,e,r)=>{"use strict";var n,o=SyntaxError,i=Function,s=TypeError,a=function(t){try{return i('"use strict"; return ('+t+").constructor;")()}catch(t){}},l=Object.getOwnPropertyDescriptor;if(l)try{l({},"")}catch(t){l=null}var u=function(){throw new s},c=l?function(){try{return u}catch(t){try{return l(arguments,"callee").get}catch(t){return u}}}():u,h=r(1405)(),p=Object.getPrototypeOf||function(t){return t.__proto__},f={},d="undefined"==typeof Uint8Array?n:p(Uint8Array),y={"%AggregateError%":"undefined"==typeof AggregateError?n:AggregateError,"%Array%":Array,"%ArrayBuffer%":"undefined"==typeof ArrayBuffer?n:ArrayBuffer,"%ArrayIteratorPrototype%":h?p([][Symbol.iterator]()):n,"%AsyncFromSyncIteratorPrototype%":n,"%AsyncFunction%":f,"%AsyncGenerator%":f,"%AsyncGeneratorFunction%":f,"%AsyncIteratorPrototype%":f,"%Atomics%":"undefined"==typeof Atomics?n:Atomics,"%BigInt%":"undefined"==typeof BigInt?n:BigInt,"%Boolean%":Boolean,"%DataView%":"undefined"==typeof DataView?n:DataView,"%Date%":Date,"%decodeURI%":decodeURI,"%decodeURIComponent%":decodeURIComponent,"%encodeURI%":encodeURI,"%encodeURIComponent%":encodeURIComponent,"%Error%":Error,"%eval%":eval,"%EvalError%":EvalError,"%Float32Array%":"undefined"==typeof Float32Array?n:Float32Array,"%Float64Array%":"undefined"==typeof Float64Array?n:Float64Array,"%FinalizationRegistry%":"undefined"==typeof FinalizationRegistry?n:FinalizationRegistry,"%Function%":i,"%GeneratorFunction%":f,"%Int8Array%":"undefined"==typeof Int8Array?n:Int8Array,"%Int16Array%":"undefined"==typeof Int16Array?n:Int16Array,"%Int32Array%":"undefined"==typeof Int32Array?n:Int32Array,"%isFinite%":isFinite,"%isNaN%":isNaN,"%IteratorPrototype%":h?p(p([][Symbol.iterator]())):n,"%JSON%":"object"==typeof JSON?JSON:n,"%Map%":"undefined"==typeof Map?n:Map,"%MapIteratorPrototype%":"undefined"!=typeof Map&&h?p((new Map)[Symbol.iterator]()):n,"%Math%":Math,"%Number%":Number,"%Object%":Object,"%parseFloat%":parseFloat,"%parseInt%":parseInt,"%Promise%":"undefined"==typeof Promise?n:Promise,"%Proxy%":"undefined"==typeof Proxy?n:Proxy,"%RangeError%":RangeError,"%ReferenceError%":ReferenceError,"%Reflect%":"undefined"==typeof Reflect?n:Reflect,"%RegExp%":RegExp,"%Set%":"undefined"==typeof Set?n:Set,"%SetIteratorPrototype%":"undefined"!=typeof Set&&h?p((new Set)[Symbol.iterator]()):n,"%SharedArrayBuffer%":"undefined"==typeof SharedArrayBuffer?n:SharedArrayBuffer,"%String%":String,"%StringIteratorPrototype%":h?p(""[Symbol.iterator]()):n,"%Symbol%":h?Symbol:n,"%SyntaxError%":o,"%ThrowTypeError%":c,"%TypedArray%":d,"%TypeError%":s,"%Uint8Array%":"undefined"==typeof Uint8Array?n:Uint8Array,"%Uint8ClampedArray%":"undefined"==typeof Uint8ClampedArray?n:Uint8ClampedArray,"%Uint16Array%":"undefined"==typeof Uint16Array?n:Uint16Array,"%Uint32Array%":"undefined"==typeof Uint32Array?n:Uint32Array,"%URIError%":URIError,"%WeakMap%":"undefined"==typeof WeakMap?n:WeakMap,"%WeakRef%":"undefined"==typeof WeakRef?n:WeakRef,"%WeakSet%":"undefined"==typeof WeakSet?n:WeakSet},g=function t(e){var r;if("%AsyncFunction%"===e)r=a("async function () {}");else if("%GeneratorFunction%"===e)r=a("function* () {}");else if("%AsyncGeneratorFunction%"===e)r=a("async function* () {}");else if("%AsyncGenerator%"===e){var n=t("%AsyncGeneratorFunction%");n&&(r=n.prototype)}else if("%AsyncIteratorPrototype%"===e){var o=t("%AsyncGenerator%");o&&(r=p(o.prototype))}return y[e]=r,r},_={"%ArrayBufferPrototype%":["ArrayBuffer","prototype"],"%ArrayPrototype%":["Array","prototype"],"%ArrayProto_entries%":["Array","prototype","entries"],"%ArrayProto_forEach%":["Array","prototype","forEach"],"%ArrayProto_keys%":["Array","prototype","keys"],"%ArrayProto_values%":["Array","prototype","values"],"%AsyncFunctionPrototype%":["AsyncFunction","prototype"],"%AsyncGenerator%":["AsyncGeneratorFunction","prototype"],"%AsyncGeneratorPrototype%":["AsyncGeneratorFunction","prototype","prototype"],"%BooleanPrototype%":["Boolean","prototype"],"%DataViewPrototype%":["DataView","prototype"],"%DatePrototype%":["Date","prototype"],"%ErrorPrototype%":["Error","prototype"],"%EvalErrorPrototype%":["EvalError","prototype"],"%Float32ArrayPrototype%":["Float32Array","prototype"],"%Float64ArrayPrototype%":["Float64Array","prototype"],"%FunctionPrototype%":["Function","prototype"],"%Generator%":["GeneratorFunction","prototype"],"%GeneratorPrototype%":["GeneratorFunction","prototype","prototype"],"%Int8ArrayPrototype%":["Int8Array","prototype"],"%Int16ArrayPrototype%":["Int16Array","prototype"],"%Int32ArrayPrototype%":["Int32Array","prototype"],"%JSONParse%":["JSON","parse"],"%JSONStringify%":["JSON","stringify"],"%MapPrototype%":["Map","prototype"],"%NumberPrototype%":["Number","prototype"],"%ObjectPrototype%":["Object","prototype"],"%ObjProto_toString%":["Object","prototype","toString"],"%ObjProto_valueOf%":["Object","prototype","valueOf"],"%PromisePrototype%":["Promise","prototype"],"%PromiseProto_then%":["Promise","prototype","then"],"%Promise_all%":["Promise","all"],"%Promise_reject%":["Promise","reject"],"%Promise_resolve%":["Promise","resolve"],"%RangeErrorPrototype%":["RangeError","prototype"],"%ReferenceErrorPrototype%":["ReferenceError","prototype"],"%RegExpPrototype%":["RegExp","prototype"],"%SetPrototype%":["Set","prototype"],"%SharedArrayBufferPrototype%":["SharedArrayBuffer","prototype"],"%StringPrototype%":["String","prototype"],"%SymbolPrototype%":["Symbol","prototype"],"%SyntaxErrorPrototype%":["SyntaxError","prototype"],"%TypedArrayPrototype%":["TypedArray","prototype"],"%TypeErrorPrototype%":["TypeError","prototype"],"%Uint8ArrayPrototype%":["Uint8Array","prototype"],"%Uint8ClampedArrayPrototype%":["Uint8ClampedArray","prototype"],"%Uint16ArrayPrototype%":["Uint16Array","prototype"],"%Uint32ArrayPrototype%":["Uint32Array","prototype"],"%URIErrorPrototype%":["URIError","prototype"],"%WeakMapPrototype%":["WeakMap","prototype"],"%WeakSetPrototype%":["WeakSet","prototype"]},m=r(8612),v=r(7642),S=m.call(Function.call,Array.prototype.concat),N=m.call(Function.apply,Array.prototype.splice),x=m.call(Function.call,String.prototype.replace),T=m.call(Function.call,String.prototype.slice),b=/[^%.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|%$))/g,O=/\\(\\)?/g,E=function(t){var e=T(t,0,1),r=T(t,-1);if("%"===e&&"%"!==r)throw new o("invalid intrinsic syntax, expected closing `%`");if("%"===r&&"%"!==e)throw new o("invalid intrinsic syntax, expected opening `%`");var n=[];return x(t,b,(function(t,e,r,o){n[n.length]=r?x(o,O,"$1"):e||t})),n},A=function(t,e){var r,n=t;if(v(_,n)&&(n="%"+(r=_[n])[0]+"%"),v(y,n)){var i=y[n];if(i===f&&(i=g(n)),void 0===i&&!e)throw new s("intrinsic "+t+" exists, but is not available. Please file an issue!");return{alias:r,name:n,value:i}}throw new o("intrinsic "+t+" does not exist!")};t.exports=function(t,e){if("string"!=typeof t||0===t.length)throw new s("intrinsic name must be a non-empty string");if(arguments.length>1&&"boolean"!=typeof e)throw new s('"allowMissing" argument must be a boolean');var r=E(t),n=r.length>0?r[0]:"",i=A("%"+n+"%",e),a=i.name,u=i.value,c=!1,h=i.alias;h&&(n=h[0],N(r,S([0,1],h)));for(var p=1,f=!0;p<r.length;p+=1){var d=r[p],g=T(d,0,1),_=T(d,-1);if(('"'===g||"'"===g||"`"===g||'"'===_||"'"===_||"`"===_)&&g!==_)throw new o("property names with quotes must have matching quotes");if("constructor"!==d&&f||(c=!0),v(y,a="%"+(n+="."+d)+"%"))u=y[a];else if(null!=u){if(!(d in u)){if(!e)throw new s("base intrinsic for "+t+" exists, but the property is not available.");return}if(l&&p+1>=r.length){var m=l(u,d);u=(f=!!m)&&"get"in m&&!("originalValue"in m.get)?m.get:u[d]}else f=v(u,d),u=u[d];f&&!c&&(y[a]=u)}}return u}},1405:(t,e,r)=>{"use strict";var n="undefined"!=typeof Symbol&&Symbol,o=r(5419);t.exports=function(){return"function"==typeof n&&("function"==typeof Symbol&&("symbol"==typeof n("foo")&&("symbol"==typeof Symbol("bar")&&o())))}},5419:t=>{"use strict";t.exports=function(){if("function"!=typeof Symbol||"function"!=typeof Object.getOwnPropertySymbols)return!1;if("symbol"==typeof Symbol.iterator)return!0;var t={},e=Symbol("test"),r=Object(e);if("string"==typeof e)return!1;if("[object Symbol]"!==Object.prototype.toString.call(e))return!1;if("[object Symbol]"!==Object.prototype.toString.call(r))return!1;for(e in t[e]=42,t)return!1;if("function"==typeof Object.keys&&0!==Object.keys(t).length)return!1;if("function"==typeof Object.getOwnPropertyNames&&0!==Object.getOwnPropertyNames(t).length)return!1;var n=Object.getOwnPropertySymbols(t);if(1!==n.length||n[0]!==e)return!1;if(!Object.prototype.propertyIsEnumerable.call(t,e))return!1;if("function"==typeof Object.getOwnPropertyDescriptor){var o=Object.getOwnPropertyDescriptor(t,e);if(42!==o.value||!0!==o.enumerable)return!1}return!0}},7642:(t,e,r)=>{"use strict";var n=r(8612);t.exports=n.call(Function.call,Object.prototype.hasOwnProperty)},5717:t=>{"function"==typeof Object.create?t.exports=function(t,e){e&&(t.super_=e,t.prototype=Object.create(e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}))}:t.exports=function(t,e){if(e){t.super_=e;var r=function(){};r.prototype=e.prototype,t.prototype=new r,t.prototype.constructor=t}}},2584:(t,e,r)=>{"use strict";var n="function"==typeof Symbol&&"symbol"==typeof Symbol.toStringTag,o=r(1924)("Object.prototype.toString"),i=function(t){return!(n&&t&&"object"==typeof t&&Symbol.toStringTag in t)&&"[object Arguments]"===o(t)},s=function(t){return!!i(t)||null!==t&&"object"==typeof t&&"number"==typeof t.length&&t.length>=0&&"[object Array]"!==o(t)&&"[object Function]"===o(t.callee)},a=function(){return i(arguments)}();i.isLegacyArguments=s,t.exports=a?i:s},8662:t=>{"use strict";var e,r=Object.prototype.toString,n=Function.prototype.toString,o=/^\s*(?:function)?\*/,i="function"==typeof Symbol&&"symbol"==typeof Symbol.toStringTag,s=Object.getPrototypeOf;t.exports=function(t){if("function"!=typeof t)return!1;if(o.test(n.call(t)))return!0;if(!i)return"[object GeneratorFunction]"===r.call(t);if(!s)return!1;if(void 0===e){var a=function(){if(!i)return!1;try{return Function("return function*() {}")()}catch(t){}}();e=!!a&&s(a)}return s(t)===e}},8611:t=>{"use strict";t.exports=function(t){return t!=t}},360:(t,e,r)=>{"use strict";var n=r(5559),o=r(4289),i=r(8611),s=r(9415),a=r(3194),l=n(s(),Number);o(l,{getPolyfill:s,implementation:i,shim:a}),t.exports=l},9415:(t,e,r)=>{"use strict";var n=r(8611);t.exports=function(){return Number.isNaN&&Number.isNaN(NaN)&&!Number.isNaN("a")?Number.isNaN:n}},3194:(t,e,r)=>{"use strict";var n=r(4289),o=r(9415);t.exports=function(){var t=o();return n(Number,{isNaN:t},{isNaN:function(){return Number.isNaN!==t}}),t}},5692:(t,e,r)=>{"use strict";var n=r(9804),o=r(6314),i=r(1924),s=i("Object.prototype.toString"),a=r(1405)()&&"symbol"==typeof Symbol.toStringTag,l=o(),u=i("Array.prototype.indexOf",!0)||function(t,e){for(var r=0;r<t.length;r+=1)if(t[r]===e)return r;return-1},c=i("String.prototype.slice"),h={},p=r(4079),f=Object.getPrototypeOf;a&&p&&f&&n(l,(function(t){var e=new r.g[t];if(!(Symbol.toStringTag in e))throw new EvalError("this engine has support for Symbol.toStringTag, but "+t+" does not have the property! Please report this.");var n=f(e),o=p(n,Symbol.toStringTag);if(!o){var i=f(n);o=p(i,Symbol.toStringTag)}h[t]=o.get}));t.exports=function(t){if(!t||"object"!=typeof t)return!1;if(!a){var e=c(s(t),8,-1);return u(l,e)>-1}return!!p&&function(t){var e=!1;return n(h,(function(r,n){if(!e)try{e=r.call(t)===n}catch(t){}})),e}(t)}},4244:t=>{"use strict";var e=function(t){return t!=t};t.exports=function(t,r){return 0===t&&0===r?1/t==1/r:t===r||!(!e(t)||!e(r))}},609:(t,e,r)=>{"use strict";var n=r(4289),o=r(5559),i=r(4244),s=r(5624),a=r(2281),l=o(s(),Object);n(l,{getPolyfill:s,implementation:i,shim:a}),t.exports=l},5624:(t,e,r)=>{"use strict";var n=r(4244);t.exports=function(){return"function"==typeof Object.is?Object.is:n}},2281:(t,e,r)=>{"use strict";var n=r(5624),o=r(4289);t.exports=function(){var t=n();return o(Object,{is:t},{is:function(){return Object.is!==t}}),t}},8987:(t,e,r)=>{"use strict";var n;if(!Object.keys){var o=Object.prototype.hasOwnProperty,i=Object.prototype.toString,s=r(1414),a=Object.prototype.propertyIsEnumerable,l=!a.call({toString:null},"toString"),u=a.call((function(){}),"prototype"),c=["toString","toLocaleString","valueOf","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","constructor"],h=function(t){var e=t.constructor;return e&&e.prototype===t},p={$applicationCache:!0,$console:!0,$external:!0,$frame:!0,$frameElement:!0,$frames:!0,$innerHeight:!0,$innerWidth:!0,$onmozfullscreenchange:!0,$onmozfullscreenerror:!0,$outerHeight:!0,$outerWidth:!0,$pageXOffset:!0,$pageYOffset:!0,$parent:!0,$scrollLeft:!0,$scrollTop:!0,$scrollX:!0,$scrollY:!0,$self:!0,$webkitIndexedDB:!0,$webkitStorageInfo:!0,$window:!0},f=function(){if("undefined"==typeof window)return!1;for(var t in window)try{if(!p["$"+t]&&o.call(window,t)&&null!==window[t]&&"object"==typeof window[t])try{h(window[t])}catch(t){return!0}}catch(t){return!0}return!1}();n=function(t){var e=null!==t&&"object"==typeof t,r="[object Function]"===i.call(t),n=s(t),a=e&&"[object String]"===i.call(t),p=[];if(!e&&!r&&!n)throw new TypeError("Object.keys called on a non-object");var d=u&&r;if(a&&t.length>0&&!o.call(t,0))for(var y=0;y<t.length;++y)p.push(String(y));if(n&&t.length>0)for(var g=0;g<t.length;++g)p.push(String(g));else for(var _ in t)d&&"prototype"===_||!o.call(t,_)||p.push(String(_));if(l)for(var m=function(t){if("undefined"==typeof window||!f)return h(t);try{return h(t)}catch(t){return!1}}(t),v=0;v<c.length;++v)m&&"constructor"===c[v]||!o.call(t,c[v])||p.push(c[v]);return p}}t.exports=n},2215:(t,e,r)=>{"use strict";var n=Array.prototype.slice,o=r(1414),i=Object.keys,s=i?function(t){return i(t)}:r(8987),a=Object.keys;s.shim=function(){Object.keys?function(){var t=Object.keys(arguments);return t&&t.length===arguments.length}(1,2)||(Object.keys=function(t){return o(t)?a(n.call(t)):a(t)}):Object.keys=s;return Object.keys||s},t.exports=s},1414:t=>{"use strict";var e=Object.prototype.toString;t.exports=function(t){var r=e.call(t),n="[object Arguments]"===r;return n||(n="[object Array]"!==r&&null!==t&&"object"==typeof t&&"number"==typeof t.length&&t.length>=0&&"[object Function]"===e.call(t.callee)),n}},384:t=>{t.exports=function(t){return t&&"object"==typeof t&&"function"==typeof t.copy&&"function"==typeof t.fill&&"function"==typeof t.readUInt8}},5955:(t,e,r)=>{"use strict";var n=r(2584),o=r(8662),i=r(6430),s=r(5692);function a(t){return t.call.bind(t)}var l="undefined"!=typeof BigInt,u="undefined"!=typeof Symbol,c=a(Object.prototype.toString),h=a(Number.prototype.valueOf),p=a(String.prototype.valueOf),f=a(Boolean.prototype.valueOf);if(l)var d=a(BigInt.prototype.valueOf);if(u)var y=a(Symbol.prototype.valueOf);function g(t,e){if("object"!=typeof t)return!1;try{return e(t),!0}catch(t){return!1}}function _(t){return"[object Map]"===c(t)}function m(t){return"[object Set]"===c(t)}function v(t){return"[object WeakMap]"===c(t)}function S(t){return"[object WeakSet]"===c(t)}function N(t){return"[object ArrayBuffer]"===c(t)}function x(t){return"undefined"!=typeof ArrayBuffer&&(N.working?N(t):t instanceof ArrayBuffer)}function T(t){return"[object DataView]"===c(t)}function b(t){return"undefined"!=typeof DataView&&(T.working?T(t):t instanceof DataView)}e.isArgumentsObject=n,e.isGeneratorFunction=o,e.isTypedArray=s,e.isPromise=function(t){return"undefined"!=typeof Promise&&t instanceof Promise||null!==t&&"object"==typeof t&&"function"==typeof t.then&&"function"==typeof t.catch},e.isArrayBufferView=function(t){return"undefined"!=typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(t):s(t)||b(t)},e.isUint8Array=function(t){return"Uint8Array"===i(t)},e.isUint8ClampedArray=function(t){return"Uint8ClampedArray"===i(t)},e.isUint16Array=function(t){return"Uint16Array"===i(t)},e.isUint32Array=function(t){return"Uint32Array"===i(t)},e.isInt8Array=function(t){return"Int8Array"===i(t)},e.isInt16Array=function(t){return"Int16Array"===i(t)},e.isInt32Array=function(t){return"Int32Array"===i(t)},e.isFloat32Array=function(t){return"Float32Array"===i(t)},e.isFloat64Array=function(t){return"Float64Array"===i(t)},e.isBigInt64Array=function(t){return"BigInt64Array"===i(t)},e.isBigUint64Array=function(t){return"BigUint64Array"===i(t)},_.working="undefined"!=typeof Map&&_(new Map),e.isMap=function(t){return"undefined"!=typeof Map&&(_.working?_(t):t instanceof Map)},m.working="undefined"!=typeof Set&&m(new Set),e.isSet=function(t){return"undefined"!=typeof Set&&(m.working?m(t):t instanceof Set)},v.working="undefined"!=typeof WeakMap&&v(new WeakMap),e.isWeakMap=function(t){return"undefined"!=typeof WeakMap&&(v.working?v(t):t instanceof WeakMap)},S.working="undefined"!=typeof WeakSet&&S(new WeakSet),e.isWeakSet=function(t){return S(t)},N.working="undefined"!=typeof ArrayBuffer&&N(new ArrayBuffer),e.isArrayBuffer=x,T.working="undefined"!=typeof ArrayBuffer&&"undefined"!=typeof DataView&&T(new DataView(new ArrayBuffer(1),0,1)),e.isDataView=b;var O="undefined"!=typeof SharedArrayBuffer?SharedArrayBuffer:void 0;function E(t){return"[object SharedArrayBuffer]"===c(t)}function A(t){return void 0!==O&&(void 0===E.working&&(E.working=E(new O)),E.working?E(t):t instanceof O)}function P(t){return g(t,h)}function C(t){return g(t,p)}function R(t){return g(t,f)}function I(t){return l&&g(t,d)}function w(t){return u&&g(t,y)}e.isSharedArrayBuffer=A,e.isAsyncFunction=function(t){return"[object AsyncFunction]"===c(t)},e.isMapIterator=function(t){return"[object Map Iterator]"===c(t)},e.isSetIterator=function(t){return"[object Set Iterator]"===c(t)},e.isGeneratorObject=function(t){return"[object Generator]"===c(t)},e.isWebAssemblyCompiledModule=function(t){return"[object WebAssembly.Module]"===c(t)},e.isNumberObject=P,e.isStringObject=C,e.isBooleanObject=R,e.isBigIntObject=I,e.isSymbolObject=w,e.isBoxedPrimitive=function(t){return P(t)||C(t)||R(t)||I(t)||w(t)},e.isAnyArrayBuffer=function(t){return"undefined"!=typeof Uint8Array&&(x(t)||A(t))},["isProxy","isExternal","isModuleNamespaceObject"].forEach((function(t){Object.defineProperty(e,t,{enumerable:!1,value:function(){throw new Error(t+" is not supported in userland")}})}))},9539:(t,e,r)=>{var n=Object.getOwnPropertyDescriptors||function(t){for(var e=Object.keys(t),r={},n=0;n<e.length;n++)r[e[n]]=Object.getOwnPropertyDescriptor(t,e[n]);return r},o=/%[sdj%]/g;e.format=function(t){if(!_(t)){for(var e=[],r=0;r<arguments.length;r++)e.push(a(arguments[r]));return e.join(" ")}r=1;for(var n=arguments,i=n.length,s=String(t).replace(o,(function(t){if("%%"===t)return"%";if(r>=i)return t;switch(t){case"%s":return String(n[r++]);case"%d":return Number(n[r++]);case"%j":try{return JSON.stringify(n[r++])}catch(t){return"[Circular]"}default:return t}})),l=n[r];r<i;l=n[++r])y(l)||!S(l)?s+=" "+l:s+=" "+a(l);return s},e.deprecate=function(t,r){if("undefined"!=typeof process&&!0===process.noDeprecation)return t;if("undefined"==typeof process)return function(){return e.deprecate(t,r).apply(this,arguments)};var n=!1;return function(){if(!n){if(process.throwDeprecation)throw new Error(r);process.traceDeprecation?console.trace(r):console.error(r),n=!0}return t.apply(this,arguments)}};var i={},s=/^$/;function a(t,r){var n={seen:[],stylize:u};return arguments.length>=3&&(n.depth=arguments[2]),arguments.length>=4&&(n.colors=arguments[3]),d(r)?n.showHidden=r:r&&e._extend(n,r),m(n.showHidden)&&(n.showHidden=!1),m(n.depth)&&(n.depth=2),m(n.colors)&&(n.colors=!1),m(n.customInspect)&&(n.customInspect=!0),n.colors&&(n.stylize=l),c(n,t,n.depth)}function l(t,e){var r=a.styles[e];return r?"["+a.colors[r][0]+"m"+t+"["+a.colors[r][1]+"m":t}function u(t,e){return t}function c(t,r,n){if(t.customInspect&&r&&T(r.inspect)&&r.inspect!==e.inspect&&(!r.constructor||r.constructor.prototype!==r)){var o=r.inspect(n,t);return _(o)||(o=c(t,o,n)),o}var i=function(t,e){if(m(e))return t.stylize("undefined","undefined");if(_(e)){var r="'"+JSON.stringify(e).replace(/^"|"$/g,"").replace(/'/g,"\\'").replace(/\\"/g,'"')+"'";return t.stylize(r,"string")}if(g(e))return t.stylize(""+e,"number");if(d(e))return t.stylize(""+e,"boolean");if(y(e))return t.stylize("null","null")}(t,r);if(i)return i;var s=Object.keys(r),a=function(t){var e={};return t.forEach((function(t,r){e[t]=!0})),e}(s);if(t.showHidden&&(s=Object.getOwnPropertyNames(r)),x(r)&&(s.indexOf("message")>=0||s.indexOf("description")>=0))return h(r);if(0===s.length){if(T(r)){var l=r.name?": "+r.name:"";return t.stylize("[Function"+l+"]","special")}if(v(r))return t.stylize(RegExp.prototype.toString.call(r),"regexp");if(N(r))return t.stylize(Date.prototype.toString.call(r),"date");if(x(r))return h(r)}var u,S="",b=!1,O=["{","}"];(f(r)&&(b=!0,O=["[","]"]),T(r))&&(S=" [Function"+(r.name?": "+r.name:"")+"]");return v(r)&&(S=" "+RegExp.prototype.toString.call(r)),N(r)&&(S=" "+Date.prototype.toUTCString.call(r)),x(r)&&(S=" "+h(r)),0!==s.length||b&&0!=r.length?n<0?v(r)?t.stylize(RegExp.prototype.toString.call(r),"regexp"):t.stylize("[Object]","special"):(t.seen.push(r),u=b?function(t,e,r,n,o){for(var i=[],s=0,a=e.length;s<a;++s)P(e,String(s))?i.push(p(t,e,r,n,String(s),!0)):i.push("");return o.forEach((function(o){o.match(/^\d+$/)||i.push(p(t,e,r,n,o,!0))})),i}(t,r,n,a,s):s.map((function(e){return p(t,r,n,a,e,b)})),t.seen.pop(),function(t,e,r){if(t.reduce((function(t,e){return e.indexOf("\n")>=0&&0,t+e.replace(/\u001b\[\d\d?m/g,"").length+1}),0)>60)return r[0]+(""===e?"":e+"\n ")+" "+t.join(",\n  ")+" "+r[1];return r[0]+e+" "+t.join(", ")+" "+r[1]}(u,S,O)):O[0]+S+O[1]}function h(t){return"["+Error.prototype.toString.call(t)+"]"}function p(t,e,r,n,o,i){var s,a,l;if((l=Object.getOwnPropertyDescriptor(e,o)||{value:e[o]}).get?a=l.set?t.stylize("[Getter/Setter]","special"):t.stylize("[Getter]","special"):l.set&&(a=t.stylize("[Setter]","special")),P(n,o)||(s="["+o+"]"),a||(t.seen.indexOf(l.value)<0?(a=y(r)?c(t,l.value,null):c(t,l.value,r-1)).indexOf("\n")>-1&&(a=i?a.split("\n").map((function(t){return"  "+t})).join("\n").substr(2):"\n"+a.split("\n").map((function(t){return"   "+t})).join("\n")):a=t.stylize("[Circular]","special")),m(s)){if(i&&o.match(/^\d+$/))return a;(s=JSON.stringify(""+o)).match(/^"([a-zA-Z_][a-zA-Z_0-9]*)"$/)?(s=s.substr(1,s.length-2),s=t.stylize(s,"name")):(s=s.replace(/'/g,"\\'").replace(/\\"/g,'"').replace(/(^"|"$)/g,"'"),s=t.stylize(s,"string"))}return s+": "+a}function f(t){return Array.isArray(t)}function d(t){return"boolean"==typeof t}function y(t){return null===t}function g(t){return"number"==typeof t}function _(t){return"string"==typeof t}function m(t){return void 0===t}function v(t){return S(t)&&"[object RegExp]"===b(t)}function S(t){return"object"==typeof t&&null!==t}function N(t){return S(t)&&"[object Date]"===b(t)}function x(t){return S(t)&&("[object Error]"===b(t)||t instanceof Error)}function T(t){return"function"==typeof t}function b(t){return Object.prototype.toString.call(t)}function O(t){return t<10?"0"+t.toString(10):t.toString(10)}e.debuglog=function(t){if(t=t.toUpperCase(),!i[t])if(s.test(t)){var r=process.pid;i[t]=function(){var n=e.format.apply(e,arguments);console.error("%s %d: %s",t,r,n)}}else i[t]=function(){};return i[t]},e.inspect=a,a.colors={bold:[1,22],italic:[3,23],underline:[4,24],inverse:[7,27],white:[37,39],grey:[90,39],black:[30,39],blue:[34,39],cyan:[36,39],green:[32,39],magenta:[35,39],red:[31,39],yellow:[33,39]},a.styles={special:"cyan",number:"yellow",boolean:"yellow",undefined:"grey",null:"bold",string:"green",date:"magenta",regexp:"red"},e.types=r(5955),e.isArray=f,e.isBoolean=d,e.isNull=y,e.isNullOrUndefined=function(t){return null==t},e.isNumber=g,e.isString=_,e.isSymbol=function(t){return"symbol"==typeof t},e.isUndefined=m,e.isRegExp=v,e.types.isRegExp=v,e.isObject=S,e.isDate=N,e.types.isDate=N,e.isError=x,e.types.isNativeError=x,e.isFunction=T,e.isPrimitive=function(t){return null===t||"boolean"==typeof t||"number"==typeof t||"string"==typeof t||"symbol"==typeof t||void 0===t},e.isBuffer=r(384);var E=["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"];function A(){var t=new Date,e=[O(t.getHours()),O(t.getMinutes()),O(t.getSeconds())].join(":");return[t.getDate(),E[t.getMonth()],e].join(" ")}function P(t,e){return Object.prototype.hasOwnProperty.call(t,e)}e.log=function(){console.log("%s - %s",A(),e.format.apply(e,arguments))},e.inherits=r(5717),e._extend=function(t,e){if(!e||!S(e))return t;for(var r=Object.keys(e),n=r.length;n--;)t[r[n]]=e[r[n]];return t};var C="undefined"!=typeof Symbol?Symbol("util.promisify.custom"):void 0;function R(t,e){if(!t){var r=new Error("Promise was rejected with a falsy value");r.reason=t,t=r}return e(t)}e.promisify=function(t){if("function"!=typeof t)throw new TypeError('The "original" argument must be of type Function');if(C&&t[C]){var e;if("function"!=typeof(e=t[C]))throw new TypeError('The "util.promisify.custom" argument must be of type Function');return Object.defineProperty(e,C,{value:e,enumerable:!1,writable:!1,configurable:!0}),e}function e(){for(var e,r,n=new Promise((function(t,n){e=t,r=n})),o=[],i=0;i<arguments.length;i++)o.push(arguments[i]);o.push((function(t,n){t?r(t):e(n)}));try{t.apply(this,o)}catch(t){r(t)}return n}return Object.setPrototypeOf(e,Object.getPrototypeOf(t)),C&&Object.defineProperty(e,C,{value:e,enumerable:!1,writable:!1,configurable:!0}),Object.defineProperties(e,n(t))},e.promisify.custom=C,e.callbackify=function(t){if("function"!=typeof t)throw new TypeError('The "original" argument must be of type Function');function e(){for(var e=[],r=0;r<arguments.length;r++)e.push(arguments[r]);var n=e.pop();if("function"!=typeof n)throw new TypeError("The last argument must be of type Function");var o=this,i=function(){return n.apply(o,arguments)};t.apply(this,e).then((function(t){process.nextTick(i.bind(null,null,t))}),(function(t){process.nextTick(R.bind(null,t,i))}))}return Object.setPrototypeOf(e,Object.getPrototypeOf(t)),Object.defineProperties(e,n(t)),e}},6430:(t,e,r)=>{"use strict";var n=r(9804),o=r(6314),i=r(1924),s=i("Object.prototype.toString"),a=r(1405)()&&"symbol"==typeof Symbol.toStringTag,l=o(),u=i("String.prototype.slice"),c={},h=r(4079),p=Object.getPrototypeOf;a&&h&&p&&n(l,(function(t){if("function"==typeof r.g[t]){var e=new r.g[t];if(!(Symbol.toStringTag in e))throw new EvalError("this engine has support for Symbol.toStringTag, but "+t+" does not have the property! Please report this.");var n=p(e),o=h(n,Symbol.toStringTag);if(!o){var i=p(n);o=h(i,Symbol.toStringTag)}c[t]=o.get}}));var f=r(5692);t.exports=function(t){return!!f(t)&&(a?function(t){var e=!1;return n(c,(function(r,n){if(!e)try{var o=r.call(t);o===n&&(e=o)}catch(t){}})),e}(t):u(s(t),8,-1))}}}]);
//# sourceMappingURL=546.index.js.map