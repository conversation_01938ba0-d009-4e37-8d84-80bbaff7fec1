!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define("editor_parser",[],t):"object"==typeof exports?exports.editor_parser=t():e.editor_parser=t()}(self,(function(){return(()=>{"use strict";var e,t,r={2325:(e,t,r)=>{r.d(t,{b:()=>u});var n=r(8130),o=r(2382),a=r(8905),s=r(7717);class i{constructor(e){this.kind=s.R.KEYWORD,this.label=e}}class l{constructor(){this.expectType=s.R.ALL}}var c=function(e,t,r,n){return new(r||(r=Promise))((function(o,a){function s(e){try{l(n.next(e))}catch(e){a(e)}}function i(e){try{l(n.throw(e))}catch(e){a(e)}}function l(e){var t;e.done?o(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(s,i)}l((n=n.apply(e,t||[])).next())}))};class u{static statementParser(e){return c(this,void 0,void 0,(function*(){e.statement=e.statement.trim(),e=this.statementFront(e);let t=yield u.dataSourceStrategy(e);return console.log("最后的结果",t),t}))}static dataSourceStrategy(e){return c(this,void 0,void 0,(function*(){const t={statement:e.statement,cursorOffset:e.cursorOffset,inputCharacters:e.inputCharacters},r=e.dataSourceType;return(yield(0,o.Z7)(r)).call(t)}))}static statementFront(e){return e.statement.trim().endsWith(".")&&(console.log("当前语句是已.结尾，需要请求下一个信息"),e.statement=e.statement+"a",e.cursorOffset=e.cursorOffset+1),e}static generationErrorResult(e,t){const r=new n.n;r.errorMark=e,r.parsingStatus=a.W.FAILURE;for(let e of t){let t=new i(e);t.insertText=e,t.detail="KEYWORD",r.completionFormat.push(t)}return r}static findTokenByRangeOffSetInAST(e,t){let r=e.getTokens();for(let e of r)if(t==e.startIndex&&t==e.stopIndex+1)return e}static findSymbolElementByToken(e,t){let r=t.getPeekElement();if(null!=r)return r.stopIndex==e.stopIndex?r:void 0}static generationCompletionRequest(e,t,r){let n=new l;return n.inputCharacters=t,null==e?(n.expectType=s.R.ALL,n):(n.expectType=null==e.symbolType?s.R.ALL:e.symbolType,n=this.checkResult(n.expectType,r,e,n),r.cleanElement(),n)}static checkResult(e,t,r,n){return n.tableName=u.checkTalbe(e,t,r),n.databaseName=u.checkDatabase(e,r),n}static checkDatabase(e,t){if(e===s.R.TABLE||e===s.R.LIBRARIES_TABLES)return this.removeQuotes(t.FATHER_SON_RELATIONSHIP)}static checkTalbe(e,t,r){let n=[];if(e==s.R.COLUMN||e==s.R.TABLE_COLUMN){let e=t.getAllElement();for(let t of e){let e=t.alias==r.FATHER_SON_RELATIONSHIP||t.text==r.FATHER_SON_RELATIONSHIP||null==r.FATHER_SON_RELATIONSHIP,o=t.symbolType==s.R.LIBRARIES_TABLES||t.symbolType==s.R.TABLE;e&&o&&n.push(this.removeQuotes(t.text))}}return console.log(n),n}}u.removeQuotes=e=>null==e?void 0:e.replace(/^[`'"]/,"").replace(/[`'"]$/,"")},2382:(e,t,r)=>{r.d(t,{yZ:()=>a,Z7:()=>s});var n,o=r(2325);function a(e,t,r,n){let a=t.parserErrorResult;if(void 0!==a&&a.endOffset!==n.cursorOffset)return console.log("语句存在错误，解析失败"),e=o.b.generationErrorResult(a,r),t.symbolTable.cleanElement(),e;let s=o.b.findTokenByRangeOffSetInAST(t.commonTokenStream,n.cursorOffset);if(void 0===s)return console.log("无法获取当前光标的token"),t.symbolTable.cleanElement(),e;let i=o.b.findSymbolElementByToken(s,t.symbolTable);return void 0===i?(console.log("无法从符号表中获取symbolElement..."),(null==a?void 0:a.promptInfo.indexOf("<EOF>"))?e=o.b.generationErrorResult(a,r):e.completionResponse=o.b.generationCompletionRequest(i,n.inputCharacters,t.symbolTable),t.symbolTable.cleanElement(),e):(e.completionResponse=o.b.generationCompletionRequest(i,n.inputCharacters,t.symbolTable),e)}function s(e){return{MySQL:()=>Promise.all([r.e(546),r.e(88)]).then(r.bind(r,1088)),MariaDB:()=>Promise.all([r.e(546),r.e(88)]).then(r.bind(r,1088)),Oracle:()=>Promise.all([r.e(546),r.e(208)]).then(r.bind(r,8208)),MongoDB:()=>Promise.all([r.e(546),r.e(34)]).then(r.bind(r,9034)),SQLServer:()=>Promise.all([r.e(546),r.e(688)]).then(r.bind(r,2688)),PostgreSQL:()=>Promise.all([r.e(546),r.e(353)]).then(r.bind(r,1353)),DamengDB:()=>Promise.all([r.e(546),r.e(88)]).then(r.bind(r,1088)),Db2:()=>Promise.all([r.e(546),r.e(88)]).then(r.bind(r,1088))}[e]().then((e=>e.default))}!function(e){e.MySQL="MySQL",e.MariaDB="MariaDB",e.Oracle="Oracle",e.MongoDB="MongoDB",e.SQLServer="SQLServer",e.PostgreSQL="PostgreSQL",e.DamengDB="DamengDB",e.Db2="Db2"}(n||(n={}))},7717:(e,t,r)=>{var n;r.d(t,{R:()=>n}),function(e){e.COLUMN="COLUMN",e.TABLE="TABLE",e.DATABASE="DATABASE",e.SCHEMA="SCHEMA",e.ALL="ALL",e.LIBRARIES_TABLES="LIBRARIES_TABLES",e.TABLE_COLUMN="TABLE_COLUMN",e.KEYWORD="Keyword"}(n||(n={}))},8905:(e,t,r)=>{var n;r.d(t,{W:()=>n}),function(e){e[e.SUCCESS=0]="SUCCESS",e[e.FAILURE=1]="FAILURE"}(n||(n={}))},8130:(e,t,r)=>{r.d(t,{n:()=>o});var n=r(8905);class o{constructor(){this.parsingStatus=n.W.SUCCESS,this.completionFormat=[]}}}},n={};function o(e){var t=n[e];if(void 0!==t)return t.exports;var a=n[e]={exports:{}};return r[e].call(a.exports,a,a.exports,o),a.exports}o.m=r,o.d=(e,t)=>{for(var r in t)o.o(t,r)&&!o.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},o.f={},o.e=e=>Promise.all(Object.keys(o.f).reduce(((t,r)=>(o.f[r](e,t),t)),[])),o.u=e=>e+".index.js",o.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),o.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),e={},t="editor_parser:",o.l=(r,n,a,s)=>{if(e[r])e[r].push(n);else{var i,l;if(void 0!==a)for(var c=document.getElementsByTagName("script"),u=0;u<c.length;u++){var d=c[u];if(d.getAttribute("src")==r||d.getAttribute("data-webpack")==t+a){i=d;break}}i||(l=!0,(i=document.createElement("script")).charset="utf-8",i.timeout=120,o.nc&&i.setAttribute("nonce",o.nc),i.setAttribute("data-webpack",t+a),i.src=r),e[r]=[n];var f=(t,n)=>{i.onerror=i.onload=null,clearTimeout(p);var o=e[r];if(delete e[r],i.parentNode&&i.parentNode.removeChild(i),o&&o.forEach((e=>e(n))),t)return t(n)},p=setTimeout(f.bind(null,void 0,{type:"timeout",target:i}),12e4);i.onerror=f.bind(null,i.onerror),i.onload=f.bind(null,i.onload),l&&document.head.appendChild(i)}},o.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},o.p="/editor_parser/",(()=>{var e={179:0};o.f.j=(t,r)=>{var n=o.o(e,t)?e[t]:void 0;if(0!==n)if(n)r.push(n[2]);else{var a=new Promise(((r,o)=>n=e[t]=[r,o]));r.push(n[2]=a);var s=o.p+o.u(t),i=new Error;o.l(s,(r=>{if(o.o(e,t)&&(0!==(n=e[t])&&(e[t]=void 0),n)){var a=r&&("load"===r.type?"missing":r.type),s=r&&r.target&&r.target.src;i.message="Loading chunk "+t+" failed.\n("+a+": "+s+")",i.name="ChunkLoadError",i.type=a,i.request=s,n[1](i)}}),"chunk-"+t,t)}};var t=(t,r)=>{var n,a,[s,i,l]=r,c=0;for(n in i)o.o(i,n)&&(o.m[n]=i[n]);if(l)l(o);for(t&&t(r);c<s.length;c++)a=s[c],o.o(e,a)&&e[a]&&e[a][0](),e[s[c]]=0},r=self.webpackChunkeditor_parser=self.webpackChunkeditor_parser||[];r.forEach(t.bind(null,0)),r.push=t.bind(null,r.push.bind(r))})();var a={};return(()=>{o.r(a),o.d(a,{add:()=>r,default:()=>t});var e=o(2325);const t={EditParserFactory:e.b,ParserRequest:class{constructor(e,t,r,n){this.dataSourceType=e,this.statement=t,this.cursorOffset=r,this.inputCharacters=n}}};console.log("loading editor_parser_local3333");const r=(e,t)=>e+t})(),a})()}));
//# sourceMappingURL=index.js.map