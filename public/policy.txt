一、运算符
运算符中分为逻辑运算符和比较运算符
比较运算符中可用的有<、>、<=、>=、==、!=
非时间类型上只建议使用 == 和 !=，时间类型上可以使用所有运算符，具体使用会在下边变量说明处介绍。

逻辑运算符中可用的有&&、||、and、or
其中&& 和 and 同义，|| 和 or 同义，用于连接两个表达式，&& 需要两个表达式都为true才为true,or 则只需要有一个为true结果就为true。

二、变量介绍
用户属性，建议只使用== 和 !=
${USER.Email}  系统中用户配置的邮箱${USER.Email} == "<EMAIL>"，表示邮箱为*********的用户符合规则
${USER.UserId} 系统中用户的登录帐号{USER.UserId} != "user1"，表示只有登录帐号为user1的用户不符合规则，其余人都符合规则
${USER.Dept}   系统中的部门，${USER.Dept} == "test"，表示test部门下的所有人都符合规则
${USER.Gender} 用户的性别属性，${USER.Gender} == "男"，表示所有性别为男的用户符合规则
${USER.Group}  用户管理处组织架构中的组，${USER.Group} == "group1"，表示组group1下的所有用户符合规则
其他属性和新添加的自定义属性不再赘述

时间属性，可用所有运算符
${TIME.DayOfWeek}  星期几的意思，具体周一到周日由1-7表示，${TIME.DayOfWeek} <= 5,表示周五之前（含周五）符合规则
${TIME.TimeOfDay}   一天中几点的意思，为4位数字，24小时制，${TIME.TimeOfDay} > 1430，表示每天下午两天半后符合规则
${TIME.DayOfMonth} 每个月的几号，${TIME.DayOfMonth} <= 20，表示每个月20号之前符合规则
${TIME.FullTime}   日期 + 时间，${TIME.FullTime} > 202307271430,表示2023年7月27日下午两点半之后符合规则

三、示例
如果你想表示每个月中旬，即十号到二十号之间，使用 ${TIME.DayOfMonth} <= 20 && ${TIME.DayOfMonth} >= 10
如果你想让AA项目组下的人在每天上午10点到12点，下午2点到4点有权限，则可以使用 ${USER.Group} == "AA" && ((${TIME.TimeOfDay} >= 1000 && ${TIME.TimeOfDay} <= 1200) || (${TIME.TimeOfDay} >= 1400 && ${TIME.TimeOfDay} <= 1600))