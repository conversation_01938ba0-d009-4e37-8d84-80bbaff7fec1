I. Operators
Operators are divided into logical and comparison operators.
Comparison operators include: <, >, <=, >=, ==, !=.
For non-time types, only == and != are recommended. Time types can use all operators, with details provided below in the variable descriptions.

Logical operators include: &&, ||, and, or.
&& is synonymous with and, and || is synonymous with or. They connect two expressions.
&& returns true only if both expressions are true.
or returns true if at least one expression is true.

II. Variable Introduction
User Attributes
These are recommended for use with == and !=.
${USER.Email} - The user's configured email.${USER.Email} == "<EMAIL>" - Users with <NAME_EMAIL> match the rule.
${USER.UserId} - The user's login account.${USER.UserId} != "user1" - Users with the login account "user1" do not match the rule; others do.
${USER.Dept} - The user's department.${USER.Dept} == "test" - All users in the "test" department match the rule.
${USER.Gender} - The user's gender.${USER.Gender} == "Male" - All male users match the rule.
${USER.Group} - The group in the organizational structure of user management.${USER.Group} == "group1" - All users in the "group1" group match the rule.
Other attributes and custom attributes are not described further.
Time Attributes
These can use all operators.
${TIME.DayOfWeek} - The day of the week, represented by numbers 1-7 for Monday to Sunday.${TIME.DayOfWeek} <= 5 - Users from Monday to Friday (inclusive) match the rule.
${TIME.TimeOfDay} - The time of day, a 4-digit number in 24-hour format.${TIME.TimeOfDay} > 1430 - Users from 2:30 PM onwards match the rule.
${TIME.DayOfMonth} - The day of the month.${TIME.DayOfMonth} <= 20 - Users on the 20th or earlier of each month match the rule.
${TIME.FullTime} - Date and time combined.${TIME.FullTime} > 202307271430 - Users after 2:30 PM on July 27, 2023, match the rule.

III. Examples
If you want to represent the middle of the month (10th to 20th), use:
${TIME.DayOfMonth} <= 20 && ${TIME.DayOfMonth} >= 10
If you want users in the "AA" project group to have permissions from 10 AM to 12 PM and from 2 PM to 4 PM every day, use:
${USER.Group} == "AA" && ((${TIME.TimeOfDay} >= 1000 && ${TIME.TimeOfDay} <= 1200) || (${TIME.TimeOfDay} >= 1400 && ${TIME.TimeOfDay} <= 1600))