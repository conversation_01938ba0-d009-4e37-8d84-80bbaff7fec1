import { fetchDelete, fetchGet, fetchPost, fetchPostBlobFileAduits } from './customFetch'
import { SqlType } from '../types'

export interface ExecutionReport {
  clientIp: string
  createTime: string
  dbEdition: string
  dbType: string
  errorMsg?: string
  executeBegin: string
  executeCost: number
  executeEnd: string
  executeSql: string
  executor: string
  executorName: string
  id: number | string
  serverIp: string
  updateTime: string
}

interface ExecutionReportResponse {
  data: ExecutionReport[]
  limit: number
  offset: number
  total: number
}

// 越权行为：EXTRAT_FLAG   解析失效：SQL_PARSE_FLAG
export type ExtratTypes = 'EXTRAT_FLAG' | 'SQL_PARSE_FLAG'

export interface ExecutionReportRequest {
  /** 执行人 */
  executors: string[]
  /** 执行开始时间(ms) */
  executeBeginMs: number
  /** 执行结束时间(ms) */
  executeEndMs: number
  /** 查询返回行数 */
  limit: number
  /** 日志类型 */
  logType: string
  /** 开始下标 */
  offset: number
  /** 执行结果 */
  resultFlag: number
  /** 数据库类型 */
  dbTypes: string[]
  sqlTypes: SqlType[]
  /** 额外查询条件 */
  extratTypes?: ExtratTypes[]
  /** 执行器类型 */
  actuatorType: 0 | 1 | 2
  /** 导出类型 excel csv */
  type?: string
  /** 执行连接 */
  connectionName?: string[]
  /** 执行连接id */
  connectionId?: string[]
}

export const getExecutionReports = (
  params: Partial<ExecutionReportRequest>,
): Promise<ExecutionReportResponse> => {
  return fetchPost(`/audit/display/audit_report/find_audit_log`, params)
}
export const exportExecutionReports = (
  params: Partial<ExecutionReportRequest>,
) => {
  return fetchPost(`/audit/display/audit_report/export_audit_log_sql`, {
    ...params,
    limit: 0,
    offset: 0,
  })
}


// 2.4.6执行记录导出
export const exportAuditLogSql = (
  params: Partial<ExecutionReportRequest>,
) => {
  return fetchPost(`/analyze/audit_report/export_audit_log_sql`, {
    ...params,
    limit: 0,
    offset: 0,
  })
}

// 用户审计-SQL明细导出
interface IParams {
  limit: number
  offset: number
  executors: string[] | undefined
  type: string
}
export const exportAuditSqlDetail = (
  params: Partial<IParams>,
) => {
  return fetchPost(`/analyze/audit_report/sqlDetail/export`, {
    ...params,
    limit: 0,
    offset: 0,
  })
}

export const exportAuditLogSql4Personal = (
  params: Partial<ExecutionReportRequest>,
) => {
  return fetchPost(`/analyze/audit_report/export_audit_log_sql/personal`, {
    ...params,
    limit: 0,
    offset: 0,
  })
}

export const exportExecutionReportsByUserLog = (
  params: Partial<ExecutionReportRequest>,
) => {
  return fetchPost(`/user/history/export/records`, {
    ...params,
    limit: 0,
    offset: 0,
  })
}

// 批量导出审计概览图表
export const batchExportOverviewCharts = (params: any) => {
  return fetchPostBlobFileAduits(`/analyze/audit_metrics/export`, params)
}

type OperationType = 'USER' | 'ROLE' | 'PERMISSION'
type OperationResult = 0 | 1 | number

export interface OperationReportRequest {
  beginTimeMs: number
  endTimeMs: number
  // 操作记录导出的两个参数
  executeBeginMs?: number
  executeEndMs?: number
  limit: number
  offset: number
  businessType?: OperationType
  resultFlag?: OperationResult
  userIds?: string[]
}

export interface OperationReport {
  id: string
  businessType: OperationType
  operateType: string
  userId: string
  userName: string
  operateEntryPoint: string
  detail: string
  beginTime: string
  endTime: string
  clientIp: string
  resultFlag: OperationResult
}

interface OperationReportResponse {
  data: OperationReport[]
  limit: number
  offset: number
  total: number
}

export const getOperationReports = (
  params: OperationReportRequest,
): Promise<OperationReportResponse> => {
  return fetchPost(`/audit/display/operate_log/detail`, params)
}
export const exportOperationReports = (
  params: Partial<OperationReportRequest>,
) => {
  return fetchPost(`/audit/display/operate_log/export`, {
    ...params,
    limit: 0,
    offset: 0,
  })
}

// 2.4.6操作记录导出
export const exportOperateLog = (
  params: Partial<OperationReportRequest>,
) => {
  return fetchPost(`/analyze/operate_log/export`, {
    ...params,
    limit: 0,
    offset: 0,
  })
}

export interface SqlCountByDbType {
  dbType: string
  amount: number
}
export const getDbTypeAmount = (): Promise<SqlCountByDbType[]> => {
  return fetchGet(`/audit/display/audit_report/db_type_amount`)
}

export type AuditUnit = 'MONTH' | 'DAY' | 'HOUR'
export interface SqlCountRequest {
  beginTimeMs: number
  endTimeMs: number
  unit: AuditUnit
  dbTypes?: string[]
  serverIps?: string[]
}

interface CountPerUnit {
  unit: string
  amount: number
}
interface SqlCountPerUnit extends CountPerUnit {
  dbType: string
}
export const getSqlCountPerUnit = (
  params: SqlCountRequest,
): Promise<SqlCountPerUnit[]> => {
  return fetchPost(`/audit/display/audit_report/load_audit_log`, params)
}

export const getUserCountPerUnit = (
  params: SqlCountRequest,
): Promise<CountPerUnit[]> => {
  return fetchPost(`/audit/display/audit_report/user_amount`, params)
}

export interface SqlCostPerUnit extends CountPerUnit {
  serverIp: string
  executeCost: number
}
export const getExecutionTimePerUnit = (
  params: SqlCountRequest,
): Promise<SqlCostPerUnit[]> => {
  return fetchPost(`/audit/display/audit_report/db_execute_cost_avg`, params)
}

interface auditOverview {
  activeUserCount: number
  allCount: number
  errorCount: number
}

export const getAuditOverview = (days: number = 0): Promise<auditOverview> => {
  return fetchPost(`/audit/display/audit_report/sql_execute_amount`, { days })
}

interface IDashBoardRequest {
  userName?: string
  accountName?: string
  permType?: string
  connectId?: string
  databaseName?: string
  operator?: string
  roleName?: string
  authDate?: string
  pageNo?: string
  pageSize?: string
}

interface IDashboardResponse {
  pageNo: number
  pageSize: number
  totalPage: number
  totalItem: number
  data: [
    {
      accountName: string
      authDate: string
      operator: string
      permType: string
      resourcePath: string
      resourceType: string
      roleId: string
      roleName: string
      userId: string
      userName: string
    },
  ]
}

// dashboard 权限查询
export const postDashBoardPerm = (params: IDashBoardRequest): Promise<IDashboardResponse> => {
  return fetchPost('/customized/perm/dashboard/search/person/life', params)
}


export const getUnauthorizedAccessPerUnit = (
  params: SqlCountRequest,
): Promise<CountPerUnit[]> => {
  return fetchPost(`/audit/display/audit_report/permission_failed_amount`, params)
}

// 获取所有的操作类型
export const getOperateTypes = () => {
  return fetchGet(`/analyze/audit_report/operate`)
}

// 获取所有数据源类型
export const getDataSourceList = () => {
  return fetchGet('/dms/meta/exist/datasource_list')
}

// 获取数据源对应连接
export const getDSConnectionList = (datasource: string) => {
  return fetchGet(`/analyze/audit_report/connections/?datasource=${datasource}`)
}

// 开始审计 
interface auditReportParams {
  "executeBeginMs": number,
  "executeEndMs": number,
  "sqlTypes": string[],
  "objectWithTypes": string[]
}
export const auditReport = (params: auditReportParams) => {
  return fetchPost(`/analyze/audit_report/object`, params)
}

interface userListParams {
  "userName"?: string,
  "sort"?: string,
  [p: string]: any
}
// 获取所有用户状态 
export const getUserlist = (params: userListParams) => {
  return fetchPost(`/analyze/operate_log/all_user`, params)
}

interface userPermissParams {
  "executors"?: string[]
}
// 用户权限 
export const getUserPermissions = (params: userPermissParams) => {
  return fetchPost(`/analyze/audit_report/auth`, params)
}

interface sqlDetailParams {
  "limit": number,
  "offset": number,
  "executors"?: string[]
}
// sql明细 
export const getSqlDetailList = (params: sqlDetailParams) => {
  return fetchPost(`/analyze/audit_report/find_audit_log`, params)
}

interface operateListParams {
  "limit": number,
  "offset": number,
  "userIds"?: string[]
}
// 操作明细
export const getOperateList = (params: operateListParams) => {
  return fetchPost(`/analyze/operate_log/detail`, params)
}

// 用户审计-操作记录导出
interface IParams {
  limit: number
  offset: number
  userIds: string[] | undefined
  type: string
}
export const getOperateExport = (params: IParams) => {
  return fetchPost(`/analyze/audit_report/operate/export`, {
    ...params,
    limit: 0,
    offset: 0,
  })
}

// 操作类型枚举

export const getOperateTypeEnum = () => {
  return fetchGet(`/analyze/operate_log/operate/type`)
}

export interface AuthDashboardParams {
  userId: string[]
  nodePath?: string[]
  dataSourceName?: string[]
  permissionType?: string[] // 权限类型
  nodeType?: string[] // 资源类型
  authTimeBegin?: string
  authTimeEnd?: string
  mixedFuzzyQuery?: string, //模糊搜索
  offset: number
  limit: number
  resourceCondition?: string
  effectiveTimeBegin?: string // 生效时间开始
  effectiveTimeEnd?: string // 生效时间结束
  otherEffectiveTimeType?: string // 生效时间 永久（permanent）|当前（current）
  authUserId?: string | number[] // 授权人
  permissionName?: string // 权限等级
  resourceType?: string[] // 资源类型
}
// 权限看板查询接口
export const getAuthDashboard = (params: AuthDashboardParams) => {
  return fetchPost(`/analyze/audit_report/dashboard`, params)
}

export const getAuthDashboardPersonal = (params: AuthDashboardParams) => {
  return fetchPost(`/analyze/audit_report/dashboard/personal`, params)
}

// 应用操作语句详情的数据源类型options
export const getAppDbTypes = (): Promise<string[]> => {
  return fetchGet(`/analyze/audit_report/app_db_type`)
}

// 应用操作语句详情的语句类型options
export const getAppSqlTypes = (): Promise<string[]> => {
  return fetchGet(`/analyze/audit_report/app_sql_operate`)
}

// 权限看板-资源类型options
export const getNodeTypeOptions = (): Promise<string[]> => {
  return fetchGet(`/analyze/audit_report/dashboard/nodeType`)
}

export interface AuditAppLogParams {
  limit: number;
  offset: number;
  executeBeginMs?: number;
  executeEndMs?: number;
  executeSql?: string
  serverIp?: string
  operateType?: string[]
  operateObject?: string
  dbType?: string[]
  resultFlag?: number
  appIp?: string
  [p: string]: any
}

export interface AuditAppItem {
  applicationIp?: string;
  serverIp?: string;
  dbUser?: string;
  dbType?: string;
  operateObject: string;
  beginTime?: string;
  endTime?: string;
  resultFlag?: number | boolean;
  errorMessage?: string;
  cost?: number;
  executeSql?: string;
  affectedRows?: number;
  userNoperateTypeame?: string;
}

export interface AuditAppResponse {
  total: number;
  offset: number;
  limit: number;
  lastTime: string;
  data: AuditAppItem[];
}

// 应用操作语句详情页api
export const findAuditAppLogs = (
  params: AuditAppLogParams,
): Promise<AuditAppResponse> => {
  return fetchPost(`/analyze/audit_report/find_app_audit_log`, params)
}

/**
 * 获取指标树数据
 * @returns 返回一个Promise对象，解析后得到指标树数据
 */
export const getMetricsTreeData = (): Promise<any> => {
  return fetchGet(`/analyze/audit_metrics/tables`)
}

/**
 * 查询审计指标-XY数据
 *
 * @param params 请求参数
 * @returns 返回Promise对象，解析后得到审计指标chart数据
 */
export const queryAuditMetricsXY = (
  params: any,
): Promise<any> => {
  return fetchPost(`/analyze/audit_metrics/query/xy`, params)
}

/**
 * 查询审计指标-行Y列Y类型
 *
 * @param params 请求参数
 * @returns 返回Promise对象，解析后得到审计指标chart数据
 */
export const queryAuditMetricsColumnY = (
  params: any,
): Promise<any> => {
  return fetchPost(`/analyze/audit_metrics/query/columnY`, params)
}

/**
 * 查询审计指标-辅助Y类型
 *
 * @param params 请求参数
 * @returns 返回Promise对象，解析后得到审计指标chart数据
 */
export const queryAuditMetricsAssistantY = (
  params: any,
): Promise<any> => {
  return fetchPost(`/analyze/audit_metrics/query/assistantY`, params)
}

/**
 * 查询审计指标-矩阵类型
 *
 * @param params 请求参数
 * @returns 返回Promise对象，解析后得到审计指标chart数据
 */
export const queryAuditMetricsMatrix = (
  params: any,
): Promise<any> => {
  return fetchPost(`/analyze/audit_metrics/query/matrix`, params)
}

// 获取审计明细记录中的数据源类型
export const getAuditDbtype = (): Promise<any[]> => {
  return fetchGet(`/analyze/audit_metrics/dbtype`)
}

// 根据数据源类型获取连接名
export const getAuditConnections = (params: any): Promise<any[]> => {
  return fetchPost(`/analyze/audit_metrics/connections`, params)
}

// 获取审计明细记录中的部门
export const getAuditDepts = (): Promise<any[]> => {
  return fetchGet(`/analyze/audit_metrics/dept`)
}

// 根据部门获取账号
export const getAuditExecutors = (params: any): Promise<any[]> => {
  return fetchPost(`/analyze/audit_metrics/executors`, params)
}

/**
 * 保存审计指标
 *
 * @param params 请求参数
 * @returns 返回Promise对象，判断是否成功
 */
export const saveAuditMetrics = (
  params: any,
): Promise<any> => {
  return fetchPost(`/analyze/audit_metrics`, params)
}

/**
 * 查询所有审计指标
 *
 * @param params 请求参数
 * @returns 返回Promise对象，包含所有审计指标
 */
export const getAllAuditMetrics = (params?: any): Promise<any> => {
  return fetchPost(`/analyze/audit_metrics/list`, params || {})
}


/**
 * 保存审计概览页面展示的审计指标
 *
 * @param params 请求参数，id数组
 * @returns 返回一个Promise对象
 */
export const postSaveAuditMetrics = (params: any): Promise<any> => {
  return fetchPost(`analyze/audit_metrics/show`, params)
}

/**
 * 根据id删除审计指标
 *
 * @param id 审计指标
 * @returns 返回Promise对象，判断删除是否成功
 */
export const deleteAuditMetrics = (id: number): Promise<void> => {
  return fetchDelete(`/analyze/audit_metrics/${id}`)
}

/**
 * 导出审计指标
 *
 * @param params 请求参数
 * @returns 返回Promise对象，导出结果
 */
export const downloadAuditMetrics = (params: any, fileName: string): Promise<any> => {
  return fetchPostBlobFileAduits(`/analyze/audit_metrics/download`, params, fileName)
}

/**
 * 查询分页审计指标
 *
 * @param params 请求参数
 * @returns 返回Promise对象，包含当前页审计指标
 */
export const getPagelAuditMetrics = (
  params: any,
): Promise<any> => {
  return fetchPost(`/analyze/audit_metrics/page`, params)
}

/**
 * 用户是否首次进入审计漫游导航
 * @returns 返回Promise对象，布尔值
 */
export const getIsBeginnerGuidance = (): Promise<any> => {
  return fetchGet(`/analyze/audit_metrics/roaming`)
}

interface IGetMetricsResultBySql {
  sql: string
}
/**
 * 自定义审计分析（sql模式） -- sql生成审计结果
 *
 * @param params 请求参数
 * @returns 返回Promise对象，包含当前页审计指标
 */
export const getMetricsResultBySql = (
  params: IGetMetricsResultBySql,
): Promise<any> => {
  return fetchPost(`/analyze/audit_metrics/custom/sql/preview`, params)
}

/**
 * 获取所有字段别名，用户sql编辑器的变量提示
 * @returns 返回Promise对象，data
 */
export const getAllPromptField = (): Promise<any> => {
  return fetchGet(`/analyze/audit_metrics/get/all/filed`)
}

/**
 * 获取所有的数据源类型
 * @returns 返回Promise对象，data
 */
export const getPromptTreeDatasource = (): Promise<any> => {
  return fetchGet(`/dms/meta/datasource_list`)
}

/**
 * 获取数据源类型下的具体连接
 * @param params 请求参数 datasource--数据源名
 * @returns 返回Promise对象，data
 */
export const getPromptTreeConnections = (params: any): Promise<any> => {
  return fetchGet(`/analyze/audit_report/connections`, params)
}


/**
 * 获取所有的系统部门
 * @returns 返回Promise对象，data
 */
export const getPromptTreeDept = (): Promise<any> => {
  return fetchGet(`/user/org/std/true`)
}

/**
 * 获取所有的角色
 * @returns 返回Promise对象，data
 */
export const getPromptTreeRoles = (): Promise<any> => {
  return fetchPost(
    `/user/common/roles`,
    { type: "SYSTEM_ROLE", name: "" } //固定参数
  )
}

/**
 * 获取所有的用户帐号
 * @returns 返回Promise对象，data
 */
export const getPromptTreeUsers = (): Promise<any> => {
  return fetchGet(`/user/users/auditUsers`)
}

/**
 * 获取所有的执行客户端类型
 * @returns 返回Promise对象，data
 */
export const getPromptTreeClientType = (): Promise<any> => {
  return fetchGet(`/analyze/audit_metrics/custom/client/type`)
}

/**
 * 获取所有的执行结果类型
 * @returns 返回Promise对象，data
 */
export const getPromptTreeResultTypes = (): Promise<any> => {
  return fetchGet(`/analyze/audit_metrics/custom/execute/result/type`)
}

/**
 * 获取是否手动事物展示
 * @returns 返回Promise对象，data
 */
export const getPromptTreeTrans = (): Promise<any> => {
  return fetchGet(`/analyze/audit_metrics/custom/trans`)
}

interface ISaveSqlMetrics {
  id?: number
  chartKey: string
  name: string
  remark?: string
  sql: string
  other?: any
}
/**
 * 保存/编辑自定义审计(sql模式)接口
 * @returns 返回Promise对象
 */
export const postSaveSqlMetrics = (params: ISaveSqlMetrics): Promise<any> => {
  return fetchPost(
    `/analyze/audit_metrics/custom/sql/save`,
    params
  )
}

interface IFindSqlMetricsData {
  id: number
}
/**
 * 查询已保存的图表的数据, 根据保存的图表id去执行对应的sql返回数据
 * @returns 返回Promise对象
 */
export const findSqlMetricsData = (params: IFindSqlMetricsData): Promise<any> => {
  return fetchPost(
    `/analyze/audit_metrics/custom/sql/find`,
    params
  )
}

// 导出单个审计指标
export const exportAuditMetrics = (params: any) => {
  return fetchPostBlobFileAduits(`/analyze/audit_metrics/custom/sql/export`, params)
}
