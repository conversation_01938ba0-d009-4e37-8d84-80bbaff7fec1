import { fetchDelete, fetchGet, fetchPost, fetchPut } from "./customFetch"
import { DataSourceType } from '../types'; 
/**
 * 添加用户
 */
interface addUsertI {
  userIds: string[]
  templateId: number
  nodePathWithType?: string
  groupId?: string
}

/** 非对象级模板授权，自定义连接组、连接、schema、database等 */
export const addPermissionUser = (params: addUsertI) => {
  return fetchPost(`/user/permission/sdt/user/add`, params)
} 

/**
 * 是否有权限添加用户 
 */
interface supprtAddUserI {
  nodePathWithType?: string
  groupId?: string
}
export const hasPermissionAddUser = (params: supprtAddUserI) => {
  return fetchPost(`/user/permission/sdt/user/support/add`, params)
}

/**
 * 组织架构和人员查询
 */
export interface IUserOrgSdtVoData {
  name: string;
  child: IUserOrgSdtVoData[] | null;
  orgId: number;
  type: string;
  userId: null | string;
  path: string;
  avatarUrl?: string;
  count?: number;
}

export const queryOrgSdtWithUser = () => {
  return fetchGet(`/user/org/findUserOrgSdtVo`)
}

export const queryUserAndDeptSdt = (): Promise<IUserOrgSdtVoData[]> => {
  return fetchGet(`/user/org/findUserAndDeptSdt`)
}

export interface IApplyApproverItem  {
  userId: string;
  userName: string;
  phone: number;
}
/**
 * 获取审批人列表
 */
export const getApplyApproverlList = (params: {connectionId: number}): Promise<IApplyApproverItem[]> => {
  return fetchGet(`/user/sys/query-approve-users`,params)
}

/**
 * 获取权限等级列表
 */
export const getPermissionList = (dataSourceType: string) => {
  return fetchGet(`/user/datasource/permission/template/all/${dataSourceType}`)
}

export interface IUpdateEffectDate {
  userIds: string[],
  templateId?: number | string, //权限等级
  nodePathWithType: string,
  beginTime?: number,
  endTime?: number,
  effectTimeType: string
  dataSourceType?: string //工具权限
}

export interface IToolTemplateData {
  userIds: string[],
  nodePathWithType: string,
  groupId: number
}

/**
 * 获取工具权限生效时间表格数据
 *
 * @param params 工具权限生效时间表格数据参数
 * @returns 返回一个Promise对象
 */
export const getToolTemplateData = (params: IToolTemplateData): Promise<any> => {
  return fetchPost(`/user/permission/sdt/user/show/tool`, params)
}


/**
 * 修改客体授权权限等级生效时间
 */
export const updateAuthEffectDate = (params: IUpdateEffectDate): Promise<any> => {
  return fetchPost(`/user/permission/sdt/user/template`, params)
}

/**
 * 修改客体授权工具权限生效时间
 */
export const updateToolEffectDate = (params: IUpdateEffectDate): Promise<any> => {
  return fetchPost(`/user/permission/sdt/user/tool/update`, params)
}

export interface IUpdateEffectDateBulk {
  userIds: string,
  permissionIds: number[], //权限id
  beginTime?: number,
  endTime?: number,
}

/**
 * 可批量修改客体授权工具权限生效时间
 */
export const updateToolEffectDateBulk = (params: IUpdateEffectDateBulk): Promise<any> => {
  return fetchPost(`/user/permission/sdt/user/tool/update/time`, params)
}

/**
 * 查询不同数据源下对应的所有权限信息
 */
export const getPermissionTemplate = (dataSourceType: string) => {
  return fetchGet(
    `/user/datasource/permission/template/operations/${dataSourceType}`,
  )
}

/**
 * 新增附加权限及工具权限
 */
export const addPermission = (params: any) => {
  return fetchPost(`/user/permission/sdt/user/other/addPermission`,params)
}

export interface IHighRiskPermissionParams {
  dataSourceType: string;
  groupId: string | null;
  nodePathWithTypeList: string[] | null
}

export interface IHighRiskPermissionRes {
  level: number;
  childObjects: string[];
  operationsVoList: {
    objectType: string;
    objectTypeName: string;
    operations: {
      operation: string;
      operationName: string;
      description: string;
      hasSelected: boolean;
    }[]

  }[]
}
//获取操作模板以及勾选的值
export const getHighRiskPermission = (params: IHighRiskPermissionParams): Promise<IHighRiskPermissionRes> => {
  return fetchPost(
    `/user/permission/highRisk/getPermission`,
    params
  )
}

//获取安全设置-数据源和组 基础设置的值
export const getDataSourceOrGroupBasicSetting = (params: IBasicSettingParams): Promise<any> => {
  return fetchGet(
    `/user/security/time/group`,
    params
  )
}

//获取安全设置-基础设置的值
export const getBasicSetting = (params: IBasicSettingParams): Promise<any> => {
  if (!params?.type) {
    params.type = "EXPORT"
  }
  return fetchPost(
    `/user/security/getSecuritySetting`,
    params
  )
}
export interface ISaveHighRiskPermissionParams1 {
  dataSourceType: string;
  groupId: string | null;
  nodePathWithTypeList: string[] | null
  level?: number;
  operationVoList?: any
}
export interface ISaveHighRiskPermissionParams {
  dataSourceType: string;
  groupId: string | null;
  nodePathWithTypeList: string[] | null
  operationVoList?: any
  clearCache?: boolean  // 编辑结束时，清除缓存
}
// 保存 安全设置-高危操作设置的值
export const saveHighRiskPermission = (params: ISaveHighRiskPermissionParams): Promise<IHighRiskPermissionRes> => {
  return fetchPost(
    `/user/permission/highRisk/savePermission`,
    params
  )
}

// 保存 安全设置-基础设置 的值
export const saveBasicSetting = (params: IBasicSettingParams): Promise<any> => {
  if (!params?.type) {
    params.type = "EXPORT"
  }
  return fetchPost(
    `/user/security/saveSecuritySetting`,
    params
  )
}

interface ITimePeriod {
  startTime: string
  endTime: string
  dayOfWeek: "MONDAY" | "TUESDAY" | "WEDNESDAY" | "THURSDAY" | "FRIDAY" | "SATURDAY" | "SUNDAY" | "EVERY_DAY"
}
interface IGetWindowPeriodParams {
  dataSourceType: string // 数据源类型
  nodeType: string // 节点类型
  requestType: string // 节点的类型大类，分为三种。DATASOURCE：数据源层级，GROUP：连接组层级，CONNECTION：连接及以下层级
  connectionId: string // 连接id，在连接及以下的层级上进行设置时，需要传，数据源层级、连接组层级不用传
  nodePath: string // 节点路径，在连接及以下的层级上进行设置时，需要传，数据源层级、连接组层级不用传
  groupId: string // 组id，在连接组上进行设置时，需要传该字段，其他层级不用传
}
interface ISetWindowPeriodParams extends IGetWindowPeriodParams {
  periods: ITimePeriod[] // 窗口期时间设置的值
}
// 数据保护-安全设置-基础设置-窗口期设置
export const saveBasicSettingWindowPeriod = (params: ISetWindowPeriodParams): Promise<any> => {
  return fetchPost("/user/highrisk/window/time", params)
}
// 数据保护-安全设置-基础设置-窗口期设置-获取窗口期设置的值
export const getBasicSettingWindowPeriod = (params: IGetWindowPeriodParams): Promise<any> => {
  return fetchPost("/user/highrisk/window/time/find", params)
}
// 数据保护-安全设置-高危操作设置-窗口期提示
export const checkWindowPeriod = (params: IGetWindowPeriodParams): Promise<any> => {
  return fetchPost("/user/highrisk/window/time/check", params)
}


export interface IBasicSettingParams {
  type?: "EXPORT" | "IMPORT", // 区分导出设置和文本导入设置
  dataSourceType?: string
  connectionId?: string;
  groupId?: number | null | string
  nodePath?: string;
  nodePathWithTypeList?: string[] | any
  operationVoList?: any
  settingType?: string
  count?: string | number
  nodeType?: string
}

/**
 * 新增或编辑权限
 */
interface savePermisssionI {
  id?: number,
  name: string,
  dataSourceType: string
  templateOperationsSet: any[]
}
export const savePermisssion = (params: savePermisssionI) => {
  return fetchPost(`/user/datasource/permission/template/save`, params)
}

/**
 * 禁用/启用权限
 */
interface paramsI {
  id: number,
  status: string
}
export const togglePermisssion = (params: paramsI) => {
  return fetchPost(`/user/datasource/permission/template/status`, params)
}

/**
 * 删除权限等级
 */
export const deletePermissionItem = (ids: number[]) => {
  return fetchDelete(`/user/datasource/permission/template`, ids)
}

/**
 * 查看修订历史
 */
export const queryPermissionHistory = (id: number) => {
  return fetchGet(`/user/datasource/permission/template/history/${id}`)
}

/**
 * 查询关联用户
 */
export const queryPermissionUser = (id: number) => {
  return fetchGet(`/user/datasource/permission/template/user/${id}`)
}

/**
 * 删除关联用户
 */
interface deleteParamsI {
  id: number,
  userIds: string[]
}
export const deletePermissionUser = (params: deleteParamsI) => {
  return fetchDelete(`/user/datasource/permission/template/user`, params)
}

/**
 * 是否支持创建
 */
export const isSupportCreate = (dataSourceType: string) => {
  return fetchGet(
    `/user/datasource/permission/template/support/create/${dataSourceType}`,
  )
}

/**
 * 连接及以下层级:隐藏模式和测试环境状态回显
 */
interface scurityStausParams {
  nodePath: string
  connectionId: number
  [p: string]: any
}
export const querySecurityStatus = (params: scurityStausParams) => {
  return fetchGet(`/user/security`, params);
}

/**
 * 组:隐藏模式和测试环境状态回显
 */
export const queryGroupSecurityStatus = (groupId: string) => {
  return fetchGet(`/user/security/group/${groupId}`)
}

/**
 * 数据源:隐藏模式和测试环境状态回显
 */
export const queryDataSourceSecurityStatus = (datasource: string) => {
  return fetchGet(`/user/security/datasource/${datasource}`)
}

/**
 * 连接及以下层级:隐藏模式修改  
 */
interface hiddenModeParamsI {
  nodePath: string
  conceal: boolean
  connectionId: string
}
export const changeHiddenMode = (params: hiddenModeParamsI) => {
  return fetchPut(`user/security/conceal`, params)
}

/**
 * 组：隐藏模式修改
 */
export const changeGroupHiddenMode = (params: any) => {
  return fetchPut(`/user/security/group/conceal/${params?.groupId}`, {
    conceal: params?.conceal,
  })
}

/**
 * 数据源：隐藏模式修改
 */
export const changeDataSourceHiddenMode = (params: any) => {
  return fetchPut(`/user/security/datasource/conceal/${params?.datasource}`, {
    conceal: params?.conceal,
  })
}

/**
 * 连接及以下层级:测试环境修改 
 */
export const changeTestEnvironment = (params: any) => {
  return fetchPut(`/user/security/experiment`, params)
}

/**
 * 组:测试环境修改
 */
export const changeGroupTestEnvironment = (params: any) => {
  return fetchPut(`/user/security/group/experiment/${params?.groupId}`, {
    experiment: params?.experiment,
  })
}

/**
 * 数据源:测试环境修改
 */
export const changeDataSourceTestEnvironment = (params: any) => {
  return fetchPut(
    `/user/security/datasource/experiment/${params?.datasource}`,
    { experiment: params?.experiment }
  )
}

/**
 *  获取权限等级模板 
 */
export const getPermissionTemplateInfo = (dataSourceType: string) => {
  return fetchGet(
    `/user/datasource/permission/templateInfo/all/${dataSourceType}`,
  )
}

/**
 * 修改权限等级
 */
interface permissionI {
  nodePathWithType?: string
  groupId?: string
  userId: string
  templateId: string
}
export const updatePermissionTemplateInfo = (params: permissionI) => {
  return fetchPost(`/user/permission/sdt/user/template`, params)
}

/**
 *  重置权限等级 
 */
export interface IResetPermissionLevelParams {
  nodePathWithType?: string;
  groupId?: number | string;
  templateId: number;
  userId: string;
  dataSourceType: DataSourceType;
  beginTime: string;
  endTime: string;
}

export const resetPermissionLevel = (params:IResetPermissionLevelParams[] ) => {
  return fetchPost(`/user/permission/sdt/user/reset`,params)
}
/**
 *  获取工具权限模板 
 */
export const getToolTemplateInfoPost = (dataSourceType: string, nodeType: string, nodePathWithType?: string) => {
  return fetchPost(`/user/datasource/permission/template/operations/tool/${dataSourceType}/${nodeType}`,
    { nodePathWithType: nodePathWithType ?? undefined }
  )
}

/**
 *  获取细粒度工具权限模板, 如 tableGroup
 */
export const getObjectToolTemplatePost = (dataSourceType: string, nodeType: string, nodePathWithTypeList?: string) => {
  return fetchPost(`/user/datasource/permission/object/operations/tool/${dataSourceType}/${nodeType}`,
    { nodePathWithTypeList: nodePathWithTypeList ?? undefined }
  )
}

/**
 *  获取工具权限模板 
 */
export const getToolTemplateInfo = (dataSourceType: string, nodeType: string) => {
  return fetchGet(
    `/user/datasource/permission/template/operations/tool/${dataSourceType}/${nodeType}`,
  )
}


/**
 * 获取连接角色
 */
export const getUserRole = () => {
  return fetchGet(`/user/permission/sdt/user/get/role`)
}

/**
 * 修改连接角色
 */
interface modifyUserRoleParams {
  userId: string
  roleType: string
  nodePathWithType?: string
  groupId?: string
}
export const updateUserRole = (params: modifyUserRoleParams) => {
  return fetchPost(`/user/permission/sdt/user/update/role`, params)
}

/**
 * 修改工具权限
 */
interface modifyUserToolParams {
  userIds: string[]
  templateOperationsSet: any[]
  nodePathWithType?: string
  groupId?: string
}
export const updateUserTool = (params: modifyUserToolParams) => {
  return fetchPost(`/user/permission/sdt/user/tool/update`, params)
}

/**
 * 查询工具权限选中值
 */
interface queryUserToolParams {
  userId: string
  nodePathWithType?: string
  groupId?: string
}
export const queryUserTool = (params: queryUserToolParams) => {
  return fetchPost(`/user/permission/sdt/user/tool/get`, params)
}

/**
 * 移除连接用户
 */
interface deleteParamsI {
  nodePathWithType?: string
  groupId?: string
  userId: string
}
export const removeConnectionUser = (params: deleteParamsI) => {
  return fetchDelete(`/user/permission/sdt/user/delete`, params)
}

/**
 * 禁用、启用用户
 */
interface togglePermissionUserParamsI {
  nodePathWithType?: string
  nodePathWithTypeList?: string[]
  groupId?: string
  userId: string
  status: boolean
}
export const togglePermissionUser = (params: togglePermissionUserParamsI) => {
  return fetchPost(`/user/permission/sdt/user/status`, params)
}

/**
 * 获取权限用户列表 
 */
interface permissionUserParams {
  nodePathWithType?: string
  groupId?: string
}
export const getPermissionUser = (params: permissionUserParams) => {
  return fetchPost(`/user/permission/sdt/user/all`, params)
}

/**
 * 逐级获取树结构
 */
interface nodeParams {
  connectionId: number | string
  connectionType: string
  nodeType: string
  nodeName: string
  nodePath: string
  nodePathWithType: string
}
export const queryTreeNode = (params: nodeParams) => {
  return fetchPost(`/dms/meta/group/node`, params)
}

// 结果集提权无用户权限限制的节点展开接口
export const queryTreeNodeAll = (params: nodeParams) => {
  return fetchPost(`/dms/meta/group/allnodes`, params)
}

/**
 * 组上同步复核设置
 */
interface groupSynchronousReviewI {
  doubleCheck: string;
  groupId: string;
}
export const setGroupSynchronousReview = (params: groupSynchronousReviewI) => {
  return fetchPost(`/user/security/group/doubleCheck`, params);
};

// 数据源类型同步复核设置
interface IDatasourceSynchronousReview {
  datasourceType: string;
  doubleCheck: string;
}
export const setDatasourceSynchronousReview = (params: IDatasourceSynchronousReview) => {
  return fetchPost(`/user/security/datasource/doubleCheck`, params);
};

/**
 * 连接或schema同步复核设置 
 */
interface synchronousReviewI {
  doubleCheck: string;
  connectionId: string;
}
export const setSynchronousReview = (params: synchronousReviewI) => {
  return fetchPost(`/user/security/group/doubleCheck/connection`, params);
};

/**
 * 安全设置查看(单条数据)【连接/Schema/database】
 */
export const queryConnectionSafeSetting = (connectionId: string) => {
  return fetchGet(`/user/security/time/connection`, { connectionId });
};

/**
 * 安全设置【连接/Schema/database】
 */
export const setConnectionSafeSetting = (params: any) => {
  return fetchPut(`/user/security/time/connection`, params);
};

/**
 * 安全设置查看(单条数据)【表、视图、函数等细粒度】
 */
interface SafeSettingParamsI {
  connectionId: string;
  nodePath: string;
  [p: string]: any;
}
export const querySafeSetting = (params: SafeSettingParamsI) => {
  return fetchGet(`/user/security/time`, params);
};

/**
 * 安全设置【数据源 组层级】
 */
export const setSafeDatasourceTypeOrGroupSetting = (params: any[]) => {
  return fetchPost(`/user/security/time/group`, params);
};
/**
 * 安全设置【表组 等同层级】
 */
export const setSafeNodeTypeEndsWithGroupSetting = (params: any) => {
  return fetchPost(`/user/security/time/node/group`, params);
};


/**
 * 安全设置【表、视图、函数等细粒度】
 */
export const setSafeSetting = (params: any[]) => {
  return fetchPut(`/user/security/time`, params);
};

/**
 * 操作安全设置权限查询
 */
export const getSafeSettingAuthorize = (params: {
  nodePathWithType?: string;
  dataSourceType?: string;
  groupId?: number;
}) => {
  return fetchPost(`/user/security/time/canEditNodePath`, params);
};

/**
 * 查看表、视图、函数等组上的列表信息
 */
export const queryDmsNodeData = (params: any) => {
  return fetchPost(`/dms/meta/auth/node`, params);
};

/**
 * 分页渲染接口
 */
export const queryTableGroupData = (params: any) => {
  return fetchPost(`/user/permission/sdt/user/object`, params);
};

/**
 * 查询操作对象关联权限用户列表 
 */
interface perrelationuserParams {
  dataSourceType: string
  nodePathWithTypeList: string
}
export const queryPermissionRelationUsers = (
  params: perrelationuserParams
) => {
  return fetchPost(
    `/user/permission/sdt/object/user/find`,
    params
  );
};

/**
 * 查询用户权限枚举 
 */
export const getUserAuthorizeEnumPost = (params: any) => {
  return fetchPost(
    `/user/permission/sdt/object/operation/${params?.dataSourceType}/${params?.nodeType}`,{
      //兼容客体授权-对象层级批量授权
      ...(params?.nodePathWithTypeList ? { nodePathWithTypeList: params?.nodePathWithTypeList ?? undefined,} : {}),
      ...(params?.nodePathWithType? { nodePathWithType: params?.nodePathWithType ?? undefined }: {})
    }
  );
};

/**
 * 查询用户权限枚举 
 */
export const getUserAuthorizeEnum = (params: any) => {
  return fetchGet(
    `/user/permission/sdt/object/operation/${params?.dataSourceType}/${params?.nodeType}`
  );
};

/**
 * 修改操作对象关联权限用户
 */
export const updatePermissionRelationUsers = (
  params: any[]
) => {
  return fetchPost(
    `/user/permission/sdt/object/user/update`,
    params
  );
};

/**
 * 添加用户时候权限校验 是否冲突
 */
export const checkPermissionStatus = (
  params: {
    userIds: string[],
    groupId?: number;
    nodePathWithType?: string;
    templateId: string
  }
) => {
  return fetchPost(
    `/user/permission/sdt/user/checkAdd`,
    params
  );
};

/**
 *  访问申请 申请清单 获取权限等级模板 
 */
export const getPermissionTemplateAllNoCheckInfo = (dataSourceType: string) => {
  return fetchGet(
    `/user/datasource/permission/templateInfo/all/nocheck/${dataSourceType}`,
  )
}

/**
 * 访问申请 申请清单 获取连接角色
 */
export const getUserRoleNocheck = () => {
  return fetchGet(`/user/permission/sdt/user/get/role/nocheck`)
}

/**
 * 获取数据源高危申请详情
 */
export const getFlowPermissionList = (dataSourceType: string) => {
  return fetchGet(`/user/permission/highRisk/getFlowPermission/${dataSourceType}`)
}

/**
 * 获取数据源高危申请详情
 */
export const delPermission = (params: { permissionIds: any[] }) => {
  return fetchDelete(`/user/permission/highRisk/deleteFlowPermission`, params)
}

/**
 * 查询当前层级下的所有权限
 */
export const getPermissionInfos = (
  params: {
    userId: string,
    nodePathWithType: string | undefined,
    groupId: number | string | undefined
  }) => {
  return fetchPost(`/user/permission/sdt/user/permissionInfo`, params)
}

/**
 * 删除权限
 */
export const delPermissionInfo = (
  params: {
    userId: string | undefined,
    permissionIds: number[],
    nodePathWithType?: string | undefined,
    operationMap: { [key: number]: string[] };
  }) => {
  return fetchDelete(`/user/permission/sdt/user/permissionInfo/delete`, params)
}

/**
 *  工具权限展示
 */

export interface IToolPermissionParams {
  permissionTypes: string[];
  nodePathWithType: string | undefined;
  userId: string;
}
export interface ToolPermissionListItem{
  permissionTypes: string[];
  nodePathWithType: string;
  permissionId: number;
  resource: number;
  operation: string;
  permissionName: string;
  permissionType: string;
  effectiveTime: string;
  sourceType: string;
  authorizerId: string;
  authorizer: string;
  authorizationTime: string;
  supportEdit: boolean;
  dataSourceType: DataSourceType
}

export interface IToolPermissionSelectVoItem {
  objectType: string;
  operations: string[];
}
export interface ToolPermissionListRes {
  totalRows: number;
  list: ToolPermissionListItem[];
  selectVoList: {
    objectType: string;
    operations: string[];
  }[]
}
export const findToolPermissionList = (params: IToolPermissionParams): Promise<ToolPermissionListRes> => {
  return fetchPost(`/user/permission/sdt/user/other/findPermissionPage`,params)
}
//批量编辑生效时间
export interface IupdatePermissionTimeParams {
  permissionIds: number[];
  nodePathWithType: string;
  userId: string;
  beginTime?: number;
  endTime?: number;
  effectTimeType?: 'CUSTOM' | 'FOREVET'
}
export const updatePermissionTime = (params: IupdatePermissionTimeParams) => {
  return fetchPost(` /user/permission/sdt/user/other/updatePermissionTime`,params)
}

// 编辑生效时间（新增）
export const updatePermissionTimeNew = (params: IupdatePermissionTimeParams) => {
  return fetchPost(`/user/permission/sdt/user/permissionInfo/updatePermissionTime`,params)
}

//批量删除权限
export interface IRevokePermissionParams {
  permissionIds?: (number | string)[];
  operations?: (number | string)[];//operations和permissionIds二选一
  userId: string;
  nodePathWithType?: string;
  groupId?: number;
}
export const authRevokePermission = (params: IRevokePermissionParams) => {
  return fetchPost(`/user/permission/sdt/user/other/revokePermission`,params)
}

interface IDBTypeAndNodeTypeAuth {
  dataSourceType: DataSourceType;
  objectType: string;
}

// 查看当前数据库指定节点类型下的操作权限
export const getDBTypeAndNodeTypeAuth = (params: IDBTypeAndNodeTypeAuth) => {
  const { dataSourceType, objectType } = params;
  return fetchGet(`/user/permission/sdt/object/operationOnly/${dataSourceType}/${objectType}`);
};