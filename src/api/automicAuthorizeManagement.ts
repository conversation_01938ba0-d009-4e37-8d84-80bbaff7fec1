import { fetchGet, fetchPost } from "./customFetch"

//获取 符合条件的所有用户
export const matchedUserList = (params: {policy: string}) => {
  return fetchPost(`/user/autoPerm/matchedUserList`, params)
}

/**
 *  获取工具权限模板
 */
export const getToolTemplateInfo = (dataSourceType: string) => {
  return fetchGet(
    `/user/datasource/permission/template/operations/tool/${dataSourceType}`,
  )
}

/**
 * 禁用/启用权限
 */
interface paramsI { 
  roleId: number,
  status: any
}
export const togglePermisssion = (params: paramsI) => {
  return fetchPost(`/user/permissionCollection/updatePermissionCollection/status`, params)
}

 /**
 * 获取权限集下一节点
 * @param params 
 * @returns 
 */
interface databaseParams {
  roleId: string
  nodePathWithType?: string
  nodeType?: string
  nodePath?: string
  dataSource?: string
}
export const queryTreeNode = (params: databaseParams) => {
  return fetchPost(`/dms/meta/group/permissionNode`, params);

}

/**
 *  删除权限集
 */
interface paramsType {
  roleId: string,
}
export const deletePermission = (params: paramsType) => {
  return fetchPost(`/user/permissionCollection/deletePermissionCollection`, params)
}

// 更新权限集策略
export const updatePermissionPolicy = (params: any) => {
  return fetchPost(`user/permissionCollection/updatePermissionCollection/policy`, params)
}