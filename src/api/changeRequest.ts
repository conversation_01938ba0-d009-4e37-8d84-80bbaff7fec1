import { EXECUTE_TYPES } from 'src/constants'
import { fetchGet, fetchPost, fetchPostBlobFile, fetchPostFormData, customErrorMessageFetchPostFormData } from './customFetch'
import { getIsBase64EncodeVal, strArrToBase64, strToBase64 } from 'src/util';

export interface IApplyProps {
  type?: number;
  status: string;
  pageSize: number;
  currentPage: number;
  approveUserId?: string;
  keyword?: string;
}

export type ApplyItem = {
  id?: number;
  flowId?: number;
  applyId?: string
  type?: number;
  dataChangeType: number;
  datasourceType?: string;
  flowTaskId?: string;
  flowInstanceId?: string;
  nodePath: any;
  title: string;
  sqlType: number;
  sqlStatement: string;
  executeTimeType: number;
  executeTime?: string;
  status?: EXECUTE_TYPES;
  executeStatus?: EXECUTE_TYPES;
  approveUserId?: string;
  skip2Step?: boolean;
  flowUUID?: string
  rollbackSqlStatement?: string
  [p: string]: any
}

export interface IApplyResData {
  total: number;
  content: ApplyItem[];
}

export interface IDataChangeList {
  keyword?: string,
  sortType?: string,
  dataSourceType?: string,
  executeStatus?: string,
  pageSize: number,
  currentPage: number
}

// 数据变更-我的申请-待审核
export const getDCMyApplyPending = (params: IDataChangeList): Promise<any> => {
  return fetchPost(`/api/flow/dataChange/myApply/pending`, { ...params })
}

// 数据变更-我的申请-已审核
export const getDCMyApplyApproved = (params: IDataChangeList): Promise<any> => {
  return fetchPost(`/api/flow/dataChange/myApply/approved`, { ...params })
}

// 数据变更-我的申请-已完成
export const getDCMyApplyFinish = (params: IDataChangeList): Promise<any> => {
  return fetchPost(`/api/flow/dataChange/myApply/finish`, { ...params })
}

// 数据变更-我的申请-已撤回
export const getDCMyApplyWithdraw = (params: IDataChangeList): Promise<any> => {
  return fetchPost(`/api/flow/dataChange/myApply/withdraw`, { ...params })
}

// 数据变更-我的申请-全部
export const getDCMyApplyAll = (params: IDataChangeList): Promise<any> => {
  return fetchPost(`/api/flow/dataChange/myApply/all`, { ...params })
}

// 数据变更-我的审批-待审核
export const getDCMyApprovePending = (params: IDataChangeList): Promise<any> => {
  return fetchPost(`/api/flow/dataChange/myApprove/pending`, { ...params })
}

// 数据变更-我的审批-已审核
export const getDCMyApproveApproved = (params: IDataChangeList): Promise<any> => {
  return fetchPost(`/api/flow/dataChange/myApprove/approved`, { ...params })
}

// 数据变更-我的审批-已完成
export const getDCMyApproveFinish = (params: IDataChangeList): Promise<any> => {
  return fetchPost(`/api/flow/dataChange/myApprove/finish`, { ...params })
}

// 数据变更-我的审批-全部
export const getDCMyApproveAll = (params: IDataChangeList): Promise<any> => {
  return fetchPost(`/api/flow/dataChange/myApprove/all`, { ...params })
}

// 数据变更-工单管理-待审核
export const getDCManagerPending = (params: IDataChangeList): Promise<any> => {
  return fetchPost(`/api/flow/dataChange/manager/pending`, { ...params })
}

// 数据变更-工单管理-已审核
export const getDCManagerApproved = (params: IDataChangeList): Promise<any> => {
  return fetchPost(`/api/flow/dataChange/manager/approved`, { ...params })
}

// 数据变更-工单管理-已完成
export const getDCManagerFinish = (params: IDataChangeList): Promise<any> => {
  return fetchPost(`/api/flow/dataChange/manager/finish`, { ...params })
}

// 数据变更-工单管理-全部
export const getDCManagerAll = (params: IDataChangeList): Promise<any> => {
  return fetchPost(`/api/flow/dataChange/manager/all`, { ...params })
}


//我申请的
export const getMyApplyList = (params: IApplyProps): Promise<IApplyResData> => {
  return fetchPost(`/api/flow/dataChange/myApplyList`, { ...params })
}

//我审批的
export const getMyApproveList = (params: IApplyProps): Promise<IApplyResData> => {
  return fetchPost(`/api/flow/dataChange/myApproveList`, { ...params })
}

//所有任务
export const getAllApproveList = (params: IApplyProps): Promise<IApplyResData> => {
  return fetchPost(`/api/flow/dataChange/allApproveList`, { ...params })
}

//发起申请
export const applyDataChange = (params: ApplyItem): Promise<IApplyResData> => {
  if (getIsBase64EncodeVal() && params?.sqlType === 1) {
    params.sqlStatement = strToBase64(params?.sqlStatement)
    params.rollbackSqlStatement = strToBase64(params?.rollbackSqlStatement)
  }
  return fetchPost(`/api/flow/dataChange/applyDataChange`, { ...params })
}

//删除草稿
export const deleteDraft = (id: number): Promise<IApplyResData> => {
  return fetchPost(`/api/flow/dataChange/${id}/delete`)
}


//执行
export const executeApplication = (id: number): Promise<IApplyResData> => {
  return fetchPost(`/api/flow/dataChange/${id}/execute`)
}

//中止执行
export const terminateApplication = (id: number): Promise<IApplyResData> => {
  return fetchPost(`/api/flow/dataChange/${id}/terminate`)
}

export interface IApplicationAmendParams {
  id: number;
  approveUserComment: string;
  executeTimeType: number;
  executeTime: string;
}
//中止执行
export const applicationAmend = (params: IApplicationAmendParams): Promise<IApplyResData> => {
  return fetchPost(`/api/flow/dataChange/amend`, params)
}


//终止执行
export const closeApplication = (id: number): Promise<IApplyResData> => {
  return fetchPost(`/api/flow/dataChange/${id}/close`)
}

//暂存草稿
export const saveAsDraft = (params: ApplyItem): Promise<IApplyResData> => {
  if (getIsBase64EncodeVal() && params?.sqlType === 1) {
    params.sqlStatement = strToBase64(params?.sqlStatement)
    params.rollbackSqlStatement = strToBase64(params?.rollbackSqlStatement)
  }
  return fetchPost(`/api/flow/dataChange/saveAsDraft`, { ...params })
}

//数据变更详情
export const getDataChangeDetail = (id: string | number): Promise<IApplyResData> => {
  return fetchGet(`/api/flow/dataChange/${id}`)
}

export interface ISqlSplitProps {
  type: number;
  statement: string;
  pageSize: number,
  currentPage: number
  dataSourceType?: string;
  filesEncodeName: string, // 附件编码list
}

export interface ISqpSplitRes {
  total: number;
  statements: string[];
}

//sql检查
export const getSqlSplitStatement = (params: ISqlSplitProps): Promise<ISqpSplitRes> => {
  if (getIsBase64EncodeVal() && params?.type === 1) {
    params.statement = strToBase64(params?.statement)
  }
  return fetchPost(`/api/flow/dataChange/splitStatement`, { ...params })
}

interface ILiveLogProps {
  fileName: number
  flowInstanceId: string
  startPoint: number
}
//数据变更-执行变更日志
export const getFetchLiveLog = (params: ILiveLogProps): Promise<any> => {
  return fetchPost(`/api/flow/dataChange/fetchLiveLog`, { ...params })
}


// sql审核
interface SqlCheckIParams {
  connectionId: string | number,
  dataSourceType: string,
  statements: any[],
  scenario?: "GUI",
  nodePathWithType: string
}
export const getSqlCheck = (params: SqlCheckIParams) => {
  if (getIsBase64EncodeVal()) {
    params.statements = strArrToBase64(params?.statements)
  }
  return fetchPost(`/dms/segment/statement/sqlCheck`, params)
}

export interface IExecutePlanParams {
  sqlStatement?: string;
  nodePath?: string;
}

export interface IExecutePlanRes {
  columnNames: string[];
  content?: string[];
}

export type IDataParamType = {
  type?: string,
  overallTransaction: boolean;
  dataBackup: boolean;
  backupLocation?: string;
}

//获取执行计划
export const getExecutePlan = (params: IExecutePlanParams): Promise<IExecutePlanRes> => {
  if (getIsBase64EncodeVal()) {
    params.sqlStatement = strToBase64(params?.sqlStatement)
  }
  return fetchPost(`/dms/statement/explainSingle`, params)
}

export const uploadDataChangeFileChunks = (params: any) => {
  return fetchPostFormData(`/export/uploadDataChangeSql`, params)
}

//下载日志
export const downloadLog = (id: number): Promise<any> => {
  return fetchGet(`/api/flow/dataChange/${id}/dataChangeLog`)
}

// 模拟执行
export interface SimulationExecuteI {
  nodePath: string;
  sqlType: number;
  sqlContent: string;
  dataSourceType: string;
  sqlOwnerId: string;
  dataChangeId?: number;
  simulateType: number;
  filesEncodeName:string;
}
export const simulationExecute = (params: SimulationExecuteI) => {
  return fetchPost(`/api/flow/dataChange/simulate`, params);
};

// 轮询执行结果
export const pollingExecuteResult = (simualteKey: string) => {
  return fetchGet(`/dms/segment/statement/simulateLog/${simualteKey}`);
};

//执行回退
export const executeBack = (id: number): Promise<{ id: number }> => {
  return fetchPost(`/api/flow/dataChange/${id}/executeBack`)
}

// 上传数据变更sql
export const uploadDataChangeSql = (params: any): Promise<any> => {
  return customErrorMessageFetchPostFormData(`/api/flow/dataChange/uploadDataChangeSql`, params)
}

// 上传数据变更sql删除 
export const dataChangeSqlDelete = (fileName: string): Promise<any> => {
  return fetchGet(`/api/flow/dataChange/deleteDataChangeSql/${fileName}`)
}

// 设置数据变更参数
export const dataChangeParam = (params: IDataParamType): Promise<any> => {
  return fetchPost(`/api/flow/dataChange/param`, params)
}

//获取数据变更参数
export const getDataChangeParam = (type: string): Promise<IDataParamType> => {
  return fetchGet(`/api/flow/dataChange/param/${type}`)
}

// 数据变更上传sql文件预览
export const getDataChangeSqlPreviewFile = (params: any): Promise<any> => {
  return fetchGet(`/api/flow/dataChange/previewFile`,params)
}


// 下载审核结果
type IParams = {
  type: string | number,
  database: string,
  statementMap: {
    execCheck: any,
    rollBackCheck: any,
  },
  dataSourceType: string,
  connectionId: string | number,
  currentPage: string | number,
  pageSize: string | number,
}

export const splitCheckDownload = (params: IParams) => {
  return fetchPostBlobFile(`/dms/segment/statement/splitCheckDownload`, params)
}

// 批量执行step2 sql审核结果
interface SqlCheckBatchExecuteIParams {
  connectionId: string | number,
  dataSourceType: string,
  connectionName: string;
  fileName: string;
  fileOrder: number;
  nodePathWithType: string;
  name: string;
}
export const sqlCheckBatchExecute = (
  params: {
  scriptInfos: SqlCheckBatchExecuteIParams
} ) => {
  return fetchPost(`/export/batchSql/sqlCheckBatchExecute`, params)
}
