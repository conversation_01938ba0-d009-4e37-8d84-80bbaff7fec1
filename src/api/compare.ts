import { fetchPost } from "./customFetch"

/**
 *  sdt存储过程,函数等版本对比 
 */
interface ICompareVersion{
  path: string,
  nodePath: string,
  connectionId: string,
  objectType: string,
  dataSourceType: string,
}
export const sqlCompareVersion = (params: ICompareVersion) => {
  return fetchPost(`/dms/programObj/programObjVersionHistory`, params)
}

/** 
 * 详情单条语句版本对比
 */
interface ICompreVersionDetail {
  nodePath: string,
  connectionId: string,
  dataSourceType: string,
  sqlStatement: string
}
export const sqlCompareVersionDetail = (params: ICompreVersionDetail) => {
  return fetchPost(`/dms/programObj/programCheckDiff`, params)
}