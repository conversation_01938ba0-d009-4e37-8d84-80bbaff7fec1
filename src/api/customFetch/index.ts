import fetchJsonp from 'fetch-jsonp'

import {
  fetchWithStatusHandler,
  formFetchWithStatusHandler,
  readResponseAsJSON,
  requestInitJSON,
  requestInitFormData,
  downloadResponse as handleDownload,
  generateDeviceId,
  downloadopenFileResponse,
  throttleWarnMessage,
  RPC_ERROR_CODE,
  RPC_ERROR_MESSAGE,
} from './_util'

export const fetchWithErrorHandler = fetchWithStatusHandler
export const downloadResponse = handleDownload

export const fetchGet = async (
  url: string,
  params?: string[][] | Record<string, any> | string | URLSearchParams,
) => {
  const requestUrl = params ? url + '?' + new URLSearchParams(params) : url
  const response = await fetchWithStatusHandler(requestUrl)

  const contextType = response.headers.get('Content-Type');

  if (contextType === 'text/html;charset=UTF-8') {
    const html = await response.text();

    return { html }
  }
  const data = await readResponseAsJSON(response)
  return data
}

export const fetchPost = async (
  input: RequestInfo,
  params?: object | string,
) => {
  const method = 'POST'
  const { headers, body } = requestInitJSON(params)

  const myHeaders = new Headers(headers);
  if (input === '/user/login') {
    myHeaders.append('deviceId', generateDeviceId());
  }
  const response = await fetchWithStatusHandler(input, {
    method,
    headers: myHeaders,
    body,
  })
  const data = await readResponseAsJSON(response)
  return data
}

export const fetchPostFormData = async (
  input: RequestInfo,
  params?: object,
) => {
  const method = 'POST'
  const { body } = requestInitFormData(params)

  const response = await fetchWithStatusHandler(input, {
    method,
    body,
  })
  const data = await readResponseAsJSON(response)
  return data
}

export const customErrorMessageFetchPostFormData = async (
  input: RequestInfo,
  params?: object,
) => {
  const method = 'POST'
  const { body } = requestInitFormData(params)

  const response = await formFetchWithStatusHandler(input, {
    method,
    body,
  })
  const data = await readResponseAsJSON(response)
  return data
}

export const fetchPut = async (
  input: RequestInfo,
  params?: object | string,
) => {
  const method = 'PUT'
  const { headers, body } = requestInitJSON(params)

  const response = await fetchWithStatusHandler(input, {
    method,
    headers,
    body,
  })
  const data = await readResponseAsJSON(response)
  return data
}

export const fetchDelete = async (
  input: RequestInfo,
  params?: object | string,
) => {
  const method = 'DELETE'
  const { headers, body } = requestInitJSON(params)

  const response = await fetchWithStatusHandler(input, {
    method,
    headers,
    body,
  })
  const data = await readResponseAsJSON(response)
  return data
}

export const getFetchJsonp = (
  url: string,
  params?: string[][] | Record<string, string> | string | URLSearchParams
) => {
  const requestUrl = params ? url + '?' + new URLSearchParams(params) : url
  return fetchJsonp(requestUrl, {
    jsonpCallback: 'callback',
    jsonpCallbackFunction: 'jsonpcallback'
  })
}

export const fetchPostBlobFile = async (
  input: RequestInfo,
  params?: object | string,
  fileName?: string
) => {
  const method = 'POST'
  const { headers, body } = requestInitJSON(params)

  const response: any = await fetchWithStatusHandler(input, {
    method,
    headers,
    body,
  })

  return downloadResponse(response, fileName)
}

// 打开文件夹下载
// https协议或localhost环境下保存会打开文件夹选择保存位置，http下则会直接走下载方法
export const fetchPostBlobFileOF = async (
  input: RequestInfo,
  params?: object | string,
  fileName?: string
) => {
  const method = 'POST'
  const { headers, body } = requestInitJSON(params)

  const response: any = await fetchWithStatusHandler(input, {
    method,
    headers,
    body,
  })

  return downloadopenFileResponse(response, fileName)
}

export const fetchPostBlobFileAduits = async (
  input: RequestInfo,
  params?: object | string,
  fileName?: string
) => {
  const method = 'POST'
  const { headers, body } = requestInitJSON(params)

  const response: any = await fetchWithStatusHandler(input, {
    method,
    headers,
    body,
  })
  let fName: string = ''
  if (fileName) {
    fName = fileName
  }
  else {
    const contextType = response.headers.get('content-disposition');
    const temp = contextType?.split(";")[1]?.split("filename=")[1];
    fName = decodeURIComponent(temp)
  }
  return downloadResponse(response, fName)
}

// 个人中心-偏好设置-结果集显示格式-跳过api的message error
export const fetchGetForDateFormatOutput = async (
  url: string,
  params?: string[][] | Record<string, any> | string | URLSearchParams,
) => {
  const requestUrl = params ? url + '?' + new URLSearchParams(params) : url
  const response = await fetchWithStatusHandler(requestUrl)

  const contextType = response.headers.get('Content-Type');

  if (contextType === 'text/html;charset=UTF-8') {
    const html = await response.text();
    return { html }
  }
  try {
    const { data, ...rest } = await response.json()
    if (rest.resCode !== 10000) {
      const msg = rest.resCode === RPC_ERROR_CODE ? RPC_ERROR_MESSAGE : rest.resMsg
      if (msg === 'Please perform two-factor authentication') {
        throttleWarnMessage(msg)
      }
      throw new Error(msg)
    }
    return data
  } catch (e) {
    if (e instanceof Error) {
      return Promise.reject(e.message)
    }
    return Promise.reject(e)
  }
}