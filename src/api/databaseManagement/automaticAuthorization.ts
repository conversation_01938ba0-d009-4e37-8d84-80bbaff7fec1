import { fetchPost, fetchGet, fetchDelete } from "../customFetch";

export interface IObjectPermissionParams {
  roleId: number;
  nodePath: string;
  dataSourceType: string;
}
export interface IObjectPermissionRes {
  canOperation: boolean;
  operationsVoList: any;
  isFlow?: boolean;
  flowMainUUID?: string;
  canOperationMessage?: string;
}
// 自动授权 获取权限面板数据
export const getAutomaticObjectPermission= (params: IObjectPermissionParams): Promise<IObjectPermissionRes> => { 
	return fetchPost(`/user/permissionCollection/getPermissionsPanelUpObject`,params);
}