import { DataSourceType } from "src/api";
import { fetchPost, fetchGet } from "../customFetch";

export type BatchAuthNodeType = 'cnnection' | 'database' | 'schema';

export interface IBatchAuthListParams {
  nodeTypes?: BatchAuthNodeType[];
  dataSourceTypes?: DataSourceType[];
}
export interface IBatchAuthItem {
  nodeName: string;
  nodeType: BatchAuthNodeType;
  nodePathWithType: string;
  dataSourceType: DataSourceType;
  connectionName: string;
}
export const getBathAuthList = (params: IBatchAuthListParams): Promise<IBatchAuthItem[]> => {
  return fetchPost(`/user/permission/batch/getNode`, params);
}

export const onBathClearAuth = (params: { nodePaths: string[] }): Promise<any> => {
  return fetchPost(`/user/permission/batch/clean`, params);
}


export interface IBatchSaveAuthParams {
  nodePaths: string[];
  userIds: string[];
  templateName: string;
  beginTime: string;
  endTime: string;
}
//批量授权接口
export const onBathSaveAuth = (params: IBatchSaveAuthParams): Promise<any> => {
  return fetchPost(`/user/permission/batch/save`, params);
}

//批量授权获取权限等级名称
export const getBathAuthTemplateName = (): Promise<string[]> => {
  return fetchGet(`/user/permission/batch/templateName`);
}

export interface IBatchAuthEffectUser {
  permissionId: number;
  permissionName: string;
  path: string;
  expr: string;
  userId: string;
  authorizationTime: string;
  roleId: number;
  uuid: number;
}
//批量授权获取影响用户
export const getBathAuthAffectUsers = (params: { nodePath: string }): Promise<IBatchAuthEffectUser[]> => {
  return fetchPost(`/user/permission/batch/getAffectUsers`, params);
}

export const getBathAuthDeleteUser = (params: {
  deleteMap: { [k: number]: string };
  userIds: string[];
  nodePath: string;
}): Promise<any> => {
  return fetchPost(`/user/permission/batch/delete`, params);
}