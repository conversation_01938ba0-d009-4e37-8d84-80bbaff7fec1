
import { DataSourceType } from "..";
import { fetchPost, fetchGet, fetchDelete } from "../customFetch";
import { IUserOrgSdtVoData } from 'src/api';
import { SourceType, PermissionType } from 'src/constants';


//sdt
export const findUserAndDeptSdt = (): Promise<IUserOrgSdtVoData[]> => {
  return fetchGet(`/user/org/findUserAndDeptSdt`);
}

export interface IMainAuthListParams {
  pageSize: number;
  pageNumber: number;
  totalPages?: number;
  orgId?: number;
  userId?: string;
  objectTypes?: string[];
  permissionTypes?: string[];
  permissionSources?: string[];
  nodePathWithTypes?: string[];
  search?: string;
}


export interface IIMainAuthListItem {
  resource: number;
  objectType: string;
  sdtRole: string;
  permissionId: number;
  permissionName?: string;
  permissionType: PermissionType,
  effectiveTime: string;
  dataSourceType: DataSourceType;
  authorizerId: number;
  sourceType: SourceType;
  authorizationIp: string;
  authorizationTime: string;
  supportEdit: boolean;
  orgId?: number;
  userId?: string;
  templateId?: number
  hasConflict?: boolean;
  supportDelete: boolean;
  [p: string]: any
}
export interface IFormattedTreeNode extends IUserOrgSdtVoData {
  title?: string;
  key?: string;
  isLeaf?: boolean;
  children?: IFormattedTreeNode
  id?: string | number | null;
  externalDepartment?: boolean
}

export interface IMainAuthListRes {
  totalRows: number;
  list: IIMainAuthListItem[];
}

//用户详情列表 不带删选条件
export const getMainAuthList = (params: IMainAuthListParams): Promise<IMainAuthListRes> => {
  return fetchPost(`/user/permission/mainAuthorization/findPermission`, params);
}
//带筛选条件
export const getMainAuthListWithSearch = (params: IMainAuthListParams): Promise<IMainAuthListRes> => {
  return fetchPost(`/user/permission/mainAuthorization/page`, params);
}


//自定义列表
export const customColumnList = (): Promise<string[]> => {
  return fetchGet(`/user/permission/mainAuthorization/customList`);
}

//保存自定义列表
export const saveCustomList = (params: string[]): Promise<string[]> => {
  return fetchPost(`/user/permission/mainAuthorization/customList/save`, params);
}


export interface ICopyPermissionParams {
  permissionIds: number[];
  userIds: string[];
}
//复制
export const copyPermission = (params: ICopyPermissionParams): Promise<IIMainAuthListItem[]> => {
  return fetchPost(`/user/permission/mainAuthorization/copyPermission`, params);
}

interface IRevokePermissionParams {
  permissionIds: number[];
  userIds?: string[] | null;
  orgId?: number;
}
//移除
export const revokePermission = (params: IRevokePermissionParams): Promise<IIMainAuthListItem[]> => {
  return fetchPost(`/user/permission/mainAuthorization/revokePermission`, params);
}

interface templateOperationsSet {
  objectType: string;
  operations: string[];
}
export interface ISaveAuthParams {
  dataSourceType: string;
  groupIds?: number[];
  nodePathWithTypes?: string[];
  templateId?: number;
  templateOperationsSet?: templateOperationsSet[];
  operations?: string[];
  userIds?: string[];
  beginTime?: number;
  endTime?: number;
}
//授权
export const saveAuthPermission = (params: ISaveAuthParams): Promise<any> => {
  return fetchPost(`/user/permission/mainAuthorization/saveAuthorizationPermission`, params);
}

//批量授权
export const batchSaveAuthorizationPermission = (params: ISaveAuthParams[]): Promise<any> => {
  return fetchPost(`/user/permission/mainAuthorization/batchSaveAuthorizationPermission`, params);
}

export interface IUpdateDataParams {
  permissionId: number | string,
  userId: string,
  beginTime?: number,
  endTime?: number,
  effectTimeType: string
}
export interface IUpdateDataBulkParams {
  permissionIds: number[],
  userId: string,
  beginTime?: number,
  endTime?: number,
  effectTimeType: string
}

// 主体授权编辑时间
export const updatePermissionData = (params: IUpdateDataParams): Promise<any> => {
  return fetchPost(`/user/permission/mainAuthorization/updatePermission`, params);
}

// 主体授权编辑时间--批量
export const updatePermissionDataBulk = (params: IUpdateDataBulkParams): Promise<any> => {
  return fetchPost(`/user/permission/mainAuthorization/updatePermissionTime`, params);
}

interface IPermissionDataItem {
  isObject: boolean;//false工具权限
  template?: string[];
  operation?: string;
  tool: string[]
}
//获取可选权限
export const getAuthPermission = (params: { userId: string; permissionId: number; }): Promise<IPermissionDataItem[]> => {
  return fetchGet(`/user/permission/mainAuthorization/getSelectPermission/${params.userId}/${params.permissionId}`);
}

export interface IUpdateAuthPermissionParams {
  userId: string;
  permissionId: number;
  templateId?: number;
  operation?: string;
}
//修改权限
export const updateAuthPermission = (params: IUpdateAuthPermissionParams): Promise<any> => {
  return fetchPost(`/user/permission/mainAuthorization/updatePermission`, params);
}

//影响用户
export const getPermissionEffectUser = ({ orgId, permissionId }: { orgId: number; permissionId: number; }): Promise<IPermissionDataItem[]> => {
  return fetchGet(`/user/permission/mainAuthorization/getPermissionEffectUser/${orgId}/${permissionId}`);
}

export interface ICheckMainAuthPermissionStatusParams {
  orgId?: number | string; // 用户组织id(orgId与userIds必填一个)
  userIds: string[];
  dataSourceType: string;
  groupIds?: number[];  // (groupIds与nodePathWithTypes必填一个)
  nodePathWithTypes?: string[];
  templateId: number | string;  // 权限模板id
}
// 主体授权添加模板权限时，会检测当前对象下是否存在和当前模板冲突的权限
export const checkMainAuthPermissionStatus = (params: ICheckMainAuthPermissionStatusParams): Promise<any> => {
  return fetchPost(`/user/permission/mainAuthorization/checkAdd`, params);
}


