import { fetchDelete, fetchGet, fetchPost } from './customFetch'
import { DataSourceType, QueryParams, SdtNodeType } from '../types'
import { ExecuteStatus } from './query'
import { DownloadMsg } from 'src/features/websocket/useDownloadNotification'
import { getIsBase64EncodeVal, strArrToBase64, strToBase64 } from 'src/util'
import { HandleExportStatusKey } from '../constants';

export const ExportFormats = ['EXCEL', 'CSV', 'TXT', 'PDF', 'SQL'] as const
export type ExportFormat = typeof ExportFormats[number]

export const DumpTypes = ['DUMPDATAANDSTRUCTURE', 'DUMPSTRUCTURE']
export type DumpType = typeof DumpTypes[number]
export const DumpTypeMap = new Map([
  ['DUMPSTRUCTURE', 'sdo_structure_only'],
  ['DUMPDATAANDSTRUCTURE', 'sdo_structure_data'],
])

export const taskStatus = [
  'CREATE',
  'PROCESSING',
  'SUCCESS',
  'FAILURE',
  'DOWNLOADED',
  'QUEUING'
] as const
export type TaskStatus = typeof taskStatus[number]

export interface ExportRequest {
  connectionId: number | string
  connectionType: DataSourceType
  exportDataNum: number
  fileName: string
  taskTime?: string
}

export interface ResultExportRequest extends ExportRequest {
  operatingObject?: string
  databaseName?: string
  statement: string
  exportFormat: ExportFormat
  resultNum: number
  containTempTable: boolean
  tabKey: string
  [p: string]: any
}

export interface SdtNodeExportRequest extends ExportRequest {
  dumpType: DumpType
  nodeName: string
  nodeType: SdtNodeType
  nodePath: string
  nodePathWithType: string
  nodeRenderRequest?: {
    connectionId: number | string
    connectionType: DataSourceType
    nodeType: SdtNodeType
    nodeName: string
    nodePath: string
    nodePathWithType: string
    groupId: number | string
    globalHavePermissionFlag: boolean
  }
  nodeActionRequests?: {
    connectionId?: number | string
    connectionType?: string
    nodeName?: string
    nodePath?: string
    nodePathWithType?: string
    nodeType?: string
  }
}

export interface DataExportItem {
  statement: string;
  exportFormat: string;
  exportDataNum: number;
  finishAt: string;
  taskStatus: HandleExportStatusKey;
}
 
export interface ExportEntity {
  taskId: number
  fileName: string
  statement: string
  exportFormat: ExportFormat
  exportDataNum: number
  taskStatus: TaskStatus
  taskCreateTime: number
  origin: string
  errorLog: string,
  dataExportItem?: DataExportItem[]
}

export interface TaskResult {
  executeStatus: ExecuteStatus,
  messageData: DownloadMsg
}

export interface ExportDetail extends ExportEntity {}

export const getTaskResult = (params: QueryParams): Promise<TaskResult> => {
  return fetchPost(`/export/export/message`, params)
}

// 导出
export const getExportList = (params?: {
  current: number,
  pageSize: number,
  sortField: string,  // createAt
  sortOrder?: string, 
  taskStatus?: string,
  resourceName?: string,
  startAt?: string,
  endAt?: string,
}): Promise<{ content: ExportEntity[]; total: number; current: number }> => {
  return fetchPost(`/export/export/page`, params)
}

// 任务中心-导出-删除 
interface IParams {
  ids: (number | string)[],
}
export const delExportTask = (params: IParams): Promise<void> => {
  return fetchPost(`export/export/batchDelTask`, params)
}

// 获取文本导入信息
export const getImportList = (params?: {
  current: number,
  pageSize: number,
  sortField?: string,  // createAt
  sortOrder?: string, // desc
  taskStatus?: string,
  resourceName?: string,
  startAt?: string,
  endAt?: string,
}): Promise<any> => {
  return fetchPost(`export/importer/userTaskList/page`, params)
}

// 任务中心-文本导入-删除 
interface IParams {
  ids: (number | string)[],
}
export const delImportTask = (params: IParams): Promise<void> => {
  return fetchPost(`export/importer/batchDelTask`, params)
}

// 获取同步数据字典信息
export const getSyncDictList = (params?: {
  current: number,
  pageSize: number,
  sortField?: string,  // createAt
  sortOrder?: string, 
  status?: string,
  path?: string,
  startAt?: string,
  endAt?: string,
}): Promise<any> => {
  return fetchPost(`dms/fullTextSearch/syncDictTask/page`, params)
}

// 任务中心-同步数据字典-删除 
interface IParams {
  ids: (number | string)[],
}
export const delFullTextSearchTask = (params: IParams): Promise<void> => {
  return fetchPost(`dms/fullTextSearch/batchDelTask`, params)
}

interface BParams {
  taskIds: (number | string)[],
}
/**
 * 批量终止任务
 *
 * @param params 参数对象
 * @returns 返回一个Promise，无返回值
 */
export const batchStopTask = (params: BParams): Promise<void> => {
  return fetchPost(`dms/fullTextSearch/batchStopTask`, params)
}

// 同步数据字典-终止任务
export const stopTask = (taskId: number | string) => {
  return fetchGet(`/dms/fullTextSearch/stopTask/${taskId}`)
}

// 同步数据字典-查询执行日志详情
export const getSyncTaskExecuteLog = (taskId: number | string) => {
  return fetchGet(`/dms/fullTextSearch/getTaskExecuteLog/${taskId}`)
}

export const makeResultExport = (
  params: ResultExportRequest,
): Promise<QueryParams> => {
  if (getIsBase64EncodeVal()) {
    params.statement = strToBase64(params?.statement)
  }
  return fetchPost(`/export/export`, params)
}

export const makeResultAllExport = (
  params: ResultExportRequest,
): Promise<QueryParams> => {
  if (getIsBase64EncodeVal()) {
    params.statement = strToBase64(params?.statement)
  }
  return fetchPost(`/export/export/fullExport`, params)
}
//校验选中导出 权限
export const getSelectedStatementResult = (
  params: any,
): Promise<any> => {
  if(getIsBase64EncodeVal()) {
    const sql = params?.statements;
    if (typeof sql === 'string') {
      params.statements = strToBase64(sql)
    } else if (Array.isArray(sql)) {
      params.statements = strArrToBase64(sql)
    }
  }
  return fetchPost(`dms/segment/statement/exportFileType`, params)
}

export const makeSdtNodeExport = (
  params: SdtNodeExportRequest,
): Promise<QueryParams> => {
  return fetchPost(`/export/export/menu`, params)
}

export const getExportDetail = (exportId: number): Promise<ExportDetail> => {
  return fetchGet(`/export/export/info/${exportId}`)
}

export const deleteExport = (exportId: number): Promise<void> => {
  return fetchDelete(`/export/export/${exportId}`)
}

// 检查批量文件是否存在
export const checkBatchFile = (taskIds: string) => {
  return fetchGet(`/export/export/exists/more/${taskIds}`)
}

// 导出-中止任务
export const batchStopExportTask = (ids: (number | string)[]) => {
  return fetchPost(`/export/export/batchStopTask`, ids)
}