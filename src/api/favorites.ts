import { fetchDelete, fetchGet, fetchPost, fetchPut } from './customFetch'
import { sqlFavoriteType } from 'src/pageTabs/queryPage/queryTabs/monacoPane/monacoToolbar/constants'
interface FavoriteContent {
  statementName?: string
  statement?: string
  digestStatement?: string
  share?: boolean
}

interface FavoriteStatus {
  updateTime?: string
  selectedNum?: number
}
export interface FavoriteEntity extends FavoriteContent, FavoriteStatus {
  id?: number
  userId?: string
  canDelete?: boolean
  canShare?: boolean
}
type SearchScope = 'ALL' | 'STATEMENTNAME' | 'STATEMENT'
interface SearchFavoritesRequest {
  limit?: number
  keyword?: string
  scope?: sqlFavoriteType
}

export const saveFavorite = (
  favorite: FavoriteEntity,
): Promise<FavoriteEntity> => {
  return fetchPut(`/user/collect/saveCollect`, favorite)
}

export const searchFavorites = (searchParams: SearchFavoritesRequest): Promise<FavoriteEntity[]> => {
  const { limit = 0, keyword = '', scope = 'ALL' } = searchParams

  return fetchPost(`/user/collect/searchCollect`, {
    limit,
    keyword,
    scope,
  } as SearchFavoritesRequest)
}

export const deleteFavorite = (id: number) => {
  return fetchDelete(`/user/collect/deleteCollect/${id}`)
}

export const updateFavoriteStatus = (id: number) => {
  return fetchPost(`/user/collect/selectCollect/${id}`)
}

export const checkFavoriteName = (statementName: string) => {
  return fetchGet(`/user/collect/checkStatementName/${statementName}`)
}
