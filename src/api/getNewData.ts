import { fetchGet, fetchPost } from './customFetch'


interface SqlType{
  executeBeginMs?: number
  executeEndMs?: number
  sqlTypes?: string[]
}
// 获取 慢sql top10 接口
export const getSql = (params:SqlType) => {
  return fetchPost(`/analyze/audit_report/slow/sql`,params)
}

// 用户授权情况
export const getUerAuthorization = () => {
  return fetchGet(`analyze/operate_log/flow/amount`)
}

// 操作卡片 /analyze/audit_report/card/operate
interface IStatisCard {
  unit: string,
  executeBeginMs?: number,
  executeEndMs?: number,
}
export const getOperateCard = (params: IStatisCard) => {
  return fetchPost(`/analyze/audit_report/card/operate`, params)
}

// 活跃用户Card /analyze/audit_report/card/operate
interface Params {
  "unit": string,
}
export const getActiveUserCard = (params: Params) => {
  return fetchPost(` /analyze/audit_report/card/user`, params)
}

// 审计概览-顶部数据接口
export const getCardCenter = () => {
  return fetchPost(` /analyze/audit_report/card/center`)
}

// 应用操作统计数据接口
export const getAppOperateCard = (params: IStatisCard) => {
  return fetchPost(`/analyze/audit_report/find_app_amount`, params)
}

// 判断是否有接入应用，以此来决定是否展示应用接入量卡片
export const getHasApp = () => {
  return fetchGet(`/analyze/audit_report/has_app`)
}