import { fetchGet, fetchPost } from './customFetch'
import { SourceType } from 'src/constants';

interface ISQLExecuteCountParams {
  beginTimeMs?: string;
  endTimeMs?: string;
  databaseTypes?: string[]
}

export interface IDatasourceExecuteCountItem {
  datasourceExecuteCount: number;
  executeCount: number;
  executionDate: string;
  logType: string
}
interface ISQLExecuteCountItemBase {
  executionDate: string;
  executeCount: number;
  datasourceExecuteCount: IDatasourceExecuteCountItem
}

type ISQLExecuteCountItem = ISQLExecuteCountItemBase & {
  [databaseType in SourceType]?: string;
};

/** sql执行次数 */
export const getHomeSqlExecuteCount= (params: ISQLExecuteCountParams): Promise<ISQLExecuteCountItem[]> => {
  return fetchPost(`/user/cq-index/sql-execute-count`, params)
}

export interface ISQLCostItem {
  originSql: string;
  sqlType: string;
  executeCostMs: string;
  id: number
}
/** SQL执行耗时TOP5 */
export const getHomeSqlCostTop5= (): Promise<ISQLCostItem[]> => {
  return fetchGet(`/user/cq-index/sql-cost-top5`)
}

export interface ISqlOperationTop5Item {
  datasourceType: string;
  dbName: string;
  schema: string;
  completeNameToView: string;
  totalOperations: number;
  dqlCount: number;
  dmlCount: number;
  ddlCount: number;
  dclCount: number;
  tclCount: number;
  otherCount: number;
}
/** 最频繁执行SQL的数据库TOP5 */
export const getHomeSqlOperationTop5= (): Promise<ISqlOperationTop5Item[]> => {
  return fetchGet(`/user/cq-index/db-operation-top5`)
}

interface IHomeConInfoRes {
  connectionCount: number;
  connPermissionTypeCount: number
}
// 概览统计 数据源
export const getHomeConnTab = (): Promise<IHomeConInfoRes> => {
  return fetchGet(`/user/cq-index/connection-info-tab`)
}
// 概览统计 sql执行总数
export const getHomeExecuteTab= (): Promise<{executeSqlTotalCount: number;todayTotalSequentialGrowth: string}> => {
  return fetchGet(`/user/cq-index/execute-tab`)
}

// 概览统计 sql执行失败数
export const getHomeExecuteFailTab= (): Promise<{executeFailSqlTotalCount: number; todayFailSequentialGrowth: string}> => {
  return fetchGet(`/user/cq-index/execute-fail-tab`)
}

export interface IHomeOrderParams {
  flowTypes: string[];
  tab: string;
}
// 工单详情 我的申请
export const getHomeApplyOrderDetailInfo= (params: IHomeOrderParams): Promise<any> => {
  return fetchPost(`/api/flow/ticket/applyList/all/count`,params)
}

// 工单详情 我的审批
export const getHomeApproveOrderDetailInfo= (params: IHomeOrderParams): Promise<any> => {
  return fetchPost(`/api/flow/ticket/approveList/all/count`, params)
}

