/*
 * @Author: f<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-01-17 09:42:37
 * @LastEditTime: 2023-01-17 11:19:49
 * @LastEditors: fuzhen<PERSON>ao
 * @Description: 消息相关接口
 * @FilePath: \cq-enterprise-frontend\src\api\userMessage.ts
 */

import { fetchGet, fetchPost } from './customFetch'

/* ----------------------------------- api ---------------------------------- */

const batchDelMessagesApi = `user/msg/batchDelMessages`

/* -------------------------------- interface ------------------------------- */

export interface IParams_batchDelMessagesPost {
  ids: Array<string | number>
}

/* ---------------------------------- 接口请求 ---------------------------------- */
interface IParamsMes {
  searchValue?: any,
  pageNum: number,
  pageSize: number,
  msgType?: string,
}
// 通知 全部 消息
export const getAllMes = (params: IParamsMes) => {
  const { searchValue = '', pageNum = 1, pageSize = 10, msgType = '' } = params || {}
  return fetchGet(`/user/msg/getMessageByUserId/value=${searchValue}/msgType=${msgType}/pageNum=${pageNum}/pageSize=${pageSize}`)
}

// 通知  已读 消息
export const getAllReadMes = (params: IParamsMes) => {
  const { searchValue = '', pageNum = 1, pageSize = 10, msgType = '' } = params || {}
  return fetchGet(`/user/msg/getReadMessageByUserId/value=${searchValue}/msgType=${msgType}/pageNum=${pageNum}/pageSize=${pageSize}`)
}

// 通知 未读 消息
export const getAllUnReadMes = (params: IParamsMes) => {
  const { searchValue = '', pageNum = 1, pageSize = 10, msgType = '' } = params || {}
  return fetchGet(`/user/msg/getUnreadMessageByUserId/value=${searchValue}/msgType=${msgType}/pageNum=${pageNum}/pageSize=${pageSize}`)
}

// 新增的合并后的user轮询接口
export const getUserPollingInfo = (params?: IParamsMes) => {
  const { searchValue = '', pageNum = 1, pageSize = 10, msgType = '' } = params || { searchValue: '', pageNum: 1, pageSize: 10, msgType: ''}
  return fetchGet(`/user/msg/getPollingMessage/value=${searchValue}/msgType=${msgType}/pageNum=${pageNum}/pageSize=${pageSize}`)
}

// 获取消息类型的过滤项
export const getQueryMsgTypes = () => {
  return fetchGet(`/user/msg/query-message-types`)
}

// 通知 删除 消息
export const batchDelMessages = (params: IParams_batchDelMessagesPost) => {
  return fetchPost(`user/msg/batchDelMessages`, params)
}

export const getUnReadMes = (userId: string) => {
  return fetchGet(`/user/msg/getMessageUnRead/${userId}`)
}

export const getUnReadMesNum = (userId: string) => {
  return fetchGet(`/user/msg/getMessageUnReadNum/${userId}`)
}

export const putReadMes = (id: number) => {
  return fetchPost(`/user/msg/readMessage/${id}`)
}

export const putReadMess = (ids: Number[]) => {
  return fetchPost(`/user/msg/markedMessageReaded`, ids)
}

export const batchDelMessagesPost = (params: IParams_batchDelMessagesPost): Promise<number> => {
  return fetchPost(batchDelMessagesApi, params)
}

interface IClearMessage {
  msgTabEnum: 'ALL' | 'UN_READ' | 'READ'
}

// 清空通知
export const clearMessages = (params: IClearMessage) => {
  return fetchPost(`/user/msg/clear-message`, params)
}