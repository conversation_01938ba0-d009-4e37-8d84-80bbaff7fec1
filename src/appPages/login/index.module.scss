.loginHeader {
  margin-bottom: 16px;
  text-align: center;
  .title {
    margin: 14px auto 28px;
    font-size: 30px;
    color: #16273B;
    .txt {
      font-weight: 600;
    }
  }

  .wwLoginTitle{
    margin: 12px auto 0;
    font-size: 30px;
    color: #16273B;
    .txt {
      font-weight: 600;
    }
  }
}

.tabs{
  :global{
    .ant-tabs-nav .ant-tabs-tab-active{
      &,& .ant-tabs-tab-btn{
        &, &:active , &:focus {
          color:#324564;
        }
      }
    }
    .ant-tabs-nav .ant-tabs-tab{
      color:#9BA6B7;
    }
    .ant-tabs-tab-btn{
      &,&:active,&:focus{
        color:#9BA6B7;
      }
    }
    .ant-form-item-label > label{
      color:#324564;
    }
  }
  .formItemPassword {
    .resetPasswordLink {
      font-size: 13px;
    }

    :global {
      .ant-form-item-label > label {
        display: flex;
        justify-content: space-between;
        width: 100%;
      }
    }
  }
  .loginBtn {
    margin-top: 14px;
    width: 100%;
    height: 40px;
    background: var(--primary-color);
    border-radius: 4px;
    &.lh36 {
      line-height: 36px;
    }
  }
}

.oldLoginHeader {
  margin-bottom: 16px;
  .title {
    margin-bottom: 8px;
    padding: 0;
    font-size: 24px;
    font-weight: 400;
    color: #29323c;
  }
}
.oldTabs {
  :global{
    .ant-tabs-nav .ant-tabs-tab-active{
      &,& .ant-tabs-tab-btn{
        &, &:active , &:focus {
          color:#324564;
        }
      }
    }
    .ant-tabs-nav .ant-tabs-tab{
      color:#9BA6B7;
    }
    .ant-tabs-tab-btn{
      &,&:active,&:focus{
        color:#9BA6B7;
      }
    }
    .ant-form-item-label > label{
      color:#324564;
    }
  }
  .formItemPassword {
    .resetPasswordLink {
      font-size: 13px;
    }
    :global {
      .ant-form-item-label > label {
        display: flex;
        justify-content: space-between;
        width: 100%;
      }
    }
  }
  .loginBtn {
    width: 100%;
    height: 40px;
    background: var(--primary-color);
    border-radius: 4px;
    &.lh36 {
      line-height: 36px;
    }
  }
}

.wWLoginQrCodeBox {
  position: relative;
  .wWLoginIcon {
    position: absolute;
    left: 12px;
    top: 45px;
  }
}

.wWLoginQrCode {
  width: 100%;
  text-align: center;
}