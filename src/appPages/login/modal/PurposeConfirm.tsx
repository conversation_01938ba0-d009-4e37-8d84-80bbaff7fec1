import React from 'react'
import { Input, message, Modal } from 'antd'
import { postLoginPurposeContent } from 'src/api'
import { useSelector } from 'src/hook'
import { Button } from 'antd/lib/radio'
import { useDispatch } from 'src/hook'
import { setPurposeConfirm } from '../loginSlice'
import { useTranslation } from 'react-i18next'
interface Visible {
  visible: boolean
}
export const PurposeConfirm: React.FC<Visible> = ({ visible }) => {
  const { t } = useTranslation()
  let content: string
  const clientId = useSelector((state) => state.login.userInfo.sessionId)
  const dispatch = useDispatch()

  function handleSubmit(closeFn: React.MouseEvent<HTMLElement>) {
    if (!content || !content?.trim()) {
      message.warn(t("loginPurpose"))
      return
    }
    postLoginPurposeContent({ purpose: content }).finally(() => {
      dispatch(setPurposeConfirm(false))
    })
  }
  return (
    <Modal
      title={
        <div>
          <span>
            <svg
              viewBox="64 64 896 896"
              width="1.5em"
              height="1.5em"
              fill="currentColor"
              style={{
                color: '#faad14',
                marginRight: '1em',
                position: 'relative',
                top: '0.35em',
              }}
            >
              <path d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"></path>
              <path d="M464 688a48 48 0 1096 0 48 48 0 10-96 0zm24-112h48c4.4 0 8-3.6 8-8V296c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8v272c0 4.4 3.6 8 8 8z"></path>
            </svg>
          </span>
          <span>{t("loginPurposeDescription")}</span>
        </div>
      }
      visible={visible}
      okText={t("confirm")}
      centered={true}
      width={500}
      closable={false}
      onOk={handleSubmit}
      footer={[
        <Button key="submit" type="primary" onClick={handleSubmit}>
          {t("confirm")}
        </Button>,
      ]}
    >
      <Input.TextArea rows={4} onChange={(e) => (content = e.target.value)} />
    </Modal>
  )
}
