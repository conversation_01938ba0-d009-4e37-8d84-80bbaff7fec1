import React, { useMemo, useCallback, useState, useEffect, useRef } from 'react'
import { SettingOutlined } from '@ant-design/icons';
import classnames from 'classnames'
import { useTranslation } from 'react-i18next';
import { useHistory, useLocation } from 'react-router-dom'
import { ResizableBox, ResizableProps } from "react-resizable";
import { Dropdown, Menu, Modal, Input } from 'antd'
import { 
  getLogo, 
  getUserAvatarAndUserName, 
  userLogout, 
} from 'src/api'
import i18n from 'src/i18n';
import Service from 'src/service'
import { persistor } from 'src/store'
import logoText from 'src/assets/logo-text.png'
import { Iconfont } from 'src/components'
import { IModuleMapKeys } from 'src/service/moduleService'
import GlobalSearchModal from 'src/pageTabs/GlobalSearchModal';
import { useDispatch, useSelector,  useRequest } from 'src/hook'
import { setUserAvatar } from 'src/store/extraSlice/userAvatarSlice'
import { clearGuideSteps } from 'src/pageTabs/SceneGuideModal/guideSlice'
import { hideModal, showModal } from 'src/store/extraSlice/modalVisibleSlice'
import { PurposeConfirm } from '../login/modal/PurposeConfirm'
import { setIsLoggedIn, setLocales } from '../login/loginSlice'
import  MessagePopover  from './MessagePopover'
import styles from './header.module.scss'
interface RouteEntity {
  key: IModuleMapKeys
  title?: string
  children?: RouteEntity[]
  logo?: any
  activeLogo?: any
}

const ResizableBoxProps: ResizableProps = {
  axis: "x",
  width: 300,
  height: 0,
  resizeHandles: ['w'],
  minConstraints: [150, 0],
  maxConstraints: [200, 0],
};


const UserMesMenuRight: RouteEntity = {
  key: '/mes-management',
  title: i18n.t('msg.subTitle'),
}

const DefaultUserDropMenu: RouteEntity[] = [
  { key: '/personal-center' },
  // { key: '/me' },
  { key: '/my-folder' },
  // { key: '/personalize' },
  { key: '/download' },
  { key: '/sql-log' },
  { key: '/logout', title: i18n.t("personal:logout")},
]

const filterRouteByPerm = (
  routeList: RouteEntity[] | undefined,
): RouteEntity[] => {
  if (!routeList || !routeList[0]) return []
  const modulePermList = routeList
    .filter(({ key }) => Service.permService.checkModulePerm(key))
    .map(({ key, children, ...rest }) => {
      const permModule: RouteEntity = {
        ...Service.moduleService.getModule(key),
        ...rest,
      }
      if (children && children[0]) {
        permModule.children = filterRouteByPerm(children)
      }
      return permModule
    })
  return modulePermList
}

export const HeaderNav: React.FC = () => {
  const { t } = useTranslation();
  const history = useHistory()
  const dispatch = useDispatch()
  const location = useLocation()
  const {
    userInfo: { userName, permissionMenus },
    loginType,
    saveEditorContent,
  } = useSelector((state) => state.login);

  const { userAvatar } = useSelector((state) => state.sysUserAvatar)
  const clientId = useSelector((state) => state.login.userInfo.sessionId)
  const purposeConfirm = useSelector((state) => state.login.purposeConfirm)

  const globalSearchModalVisible = useSelector((state) => state.modal?.['ModalGlobalSearch']?.visible || false)
  const activeKey = location.pathname

  const userPermList = filterRouteByPerm(DefaultUserDropMenu)
  userPermList.splice(1, 0,
    { key: 'scene-guide', title: t("personal:newbieSceneGuide") },
  )

  userPermList.splice(4, 0,
    { key: 'checkVersion', title: t("personal:versionInfo") },
    { key: 'driverDownload', title: t("personal:auditAgentDownload") }
  )
  //全局搜索效果
  const globalSearchRef = useRef<any>();
  const globalInputSearchRef =  useRef<any>();
  const [globalSearchValue, setGlobalSearchValue] = useState();
  const [globalSearchInputVisible, setGlobalSearchInputVisible] = useState(false);
  //null不展示弹框 modal 弹框格式 overlay：搜索下拉框
  const [globalSearchModalType, setGlobalSearchModalType] = useState<null | 'modal' | 'overlay'>(null);
  const [globalSearchInputWidth, setGlobalSearchInputWidth] = useState(300);

  function exitSaveConfirmation(logout: any) {
    Modal.confirm({
      content: t("personal:confirmExit"),
      okText: t("personal:confirm"),
      cancelText:  t("personal:cancel"),
      onOk() {
        logout()
      },
      onCancel() {
      },
    })
  }

  const handleClickMenu = useCallback(
    // ! todo: 使用 RouteEntity 里的 action 为 menu item 绑定点击事件
    (event) => {
      const path = event.key as IModuleMapKeys | 'logout' | 'checkVersion |driverDownload'
      const logout = function () {
        if (loginType === 'casLogin') {
          persistor.purge()
          window.location.href = 'user/login/casLogout'
        } else if (loginType === 'OauthLogin') {
          persistor.purge()
          window.location.href = 'user/login/oauth2Logout'
        } else {
          userLogout().then(async () => {
            // https://github.com/rt2zz/redux-persist/issues/1015#issuecomment-494492949
            // persistor.purge()
            //登出 缓存保留信息 清除登录用户信息CQ-3027
            // dispatch(setUserInfo({}))
            //清除引导信息
            await dispatch(clearGuideSteps())
            dispatch(setIsLoggedIn(false))
          })
        }
      }
      switch (path) {
        case '/logout':
          if (saveEditorContent) {
            return exitSaveConfirmation(logout)
          }
          logout()
          break
        case 'checkVersion':
          dispatch(showModal('UpdateVersion'))
          break
        case 'scene-guide':
          dispatch(showModal('ModalSceneGuide'))
          break
        case 'driverDownload':
          window.open(`/drivers/dpeasy-sql-audit.jar`)
          break
        default:
          history.push(String(path))
          break
      }
    },
    [dispatch, saveEditorContent, history, loginType],
  )

  let { data: logoUrl } = useRequest(getLogo)
  logoUrl = logoUrl && logoUrl !== '' ? '/user' + logoUrl : logoText


  const { data: UserAvatar } = useRequest(getUserAvatarAndUserName, {
    onSuccess: (res) => {
      if (res?.avatar) {
        dispatch(setUserAvatar('/user' + res?.avatar))
      } else {
        dispatch(setUserAvatar(''))
      }
    }
  });

  const handleClickOutside = (event: any) => {
    const modalContent = document.getElementById('searchModalPositionNav');
    if (modalContent && !modalContent.contains(event.target) && !globalSearchInputVisible) {
      dispatch(hideModal('ModalGlobalSearch'));
    }
  };

  useEffect(() => {
    document.addEventListener('mousedown', handleClickOutside, {passive: true});
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    }

  }, [])

  const [width, setWidth] = useState(0); // 状态来存储宽度

  const handleResize = () => {
    if (globalSearchRef?.current) {
      setWidth(globalSearchRef.current.offsetWidth - 30);
    }
  };

  // 语言切换
  const handleLanguageChange = (language: string) => {
    dispatch(setLocales(language))
    i18n.changeLanguage(language); // 切换语言
  };

  useEffect(() => {
    setTimeout(() => {
      handleResize();
    }, 500);
   
    window.addEventListener('resize', handleResize);
    // 清理事件监听器
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  // 左侧菜单
  const leftMenu = permissionMenus?.filter(
    (i: any) => !["SYSTEM_MANAGEMENT", "DATA_PROTECT", "DATABASE_MANAGEMENT", "SYSTEM_MONITOR"].includes(i?.menuType)
  ) || [];

  // 右侧菜单
  const rightMenu =
    permissionMenus?.filter((i: any) =>
      ["DATA_PROTECT", "DATABASE_MANAGEMENT", "SYSTEM_MONITOR"].includes(i?.menuType)
    ) || [];

  // 右侧特殊处理菜单  
  const renderCustomMenuTitle = (item: any) => {
    if (item?.menuType === 'DATA_PROTECT') {
      return <><Iconfont type='icon-shujukuguanli' style={{ fontSize: '16px', marginRight: "6px" }} />{item?.menuName}</>
    }
    if (item?.menuType === 'DATABASE_MANAGEMENT') {
      return <><Iconfont type='icon-shujukuguanli1' style={{ fontSize: '16px', marginRight: "6px" }} />{item?.menuName}</>
    }
    if (item?.menuType === 'SYSTEM_MONITOR') {
      return <><Iconfont type='icon-jiankong' style={{ fontSize: '16px', marginRight: "6px" }} />{item?.menuName}</>
    }
    return item?.menuName
  }

  // 系统管理菜单
  const systemManage =
    permissionMenus
      ?.filter((i: any) => i?.menuType === "SYSTEM_MANAGEMENT")
      ?.map((i: any) => {
        return i?.items?.map((res: any) => {
          const permissionType = `/${res?.permissionType?.toLowerCase()}`;
          const key =
            permissionType === "/user_management"
              ? "/person_management"
              : permissionType; // key避免和后端接口前缀相同，会被当成接口拦截页面404

              if (key === '/system_monitor') {
                return {
                  title: res?.name,
                  key,
                  children: [
                    {
                      title: t('systemMonitor.containerMonitor.title'),//权限和CLIENT_MANAGER保持一致
                      key: '/container_monitor'
                    },
                    {
                      title: t('systemMonitor.machineMonitor.title'),
                      key: `/machine_monitor`
                    },
                  ]
                }
              }
          return {
            key,
            title: res?.name,
          };
        });
      })
      ?.flat(1) || [];

  const renderMenu = (menuData: any[]) => {
    return menuData?.map((item: any, index) => {
      let curRoute = "/" + item?.menuType?.toLowerCase();
      //三级菜单单独处理 后端无法控制
      let routeMenuList = item?.items?.map((res: any) => {
        if (res?.permissionType === 'AUTH_MANAGEMENT') {
          return {
            title: res?.name,
            key: `/${res?.permissionType?.toLowerCase()}`,
            children: [
              {
                title: t('db.auth.authorizedObject'),
                key: `/${res?.permissionType?.toLowerCase()}`
              },
              {
                title: t('db.subjectAuth.title'),//权限和auth_managemen保持一致
                key: '/subject-authorization'
              },
              {
                title: t('db.batchAuth.title'),//权限和auth_managemen保持一致
                key: '/batch-authorization'
              },
            ]
          }
        }else if (res?.permissionType === 'CLIENT_MANAGER') {
          return {
            title: res?.name,
            key: `/${res?.permissionType?.toLowerCase()}`,
            children: [
              {
                title: t('tool.toolConfig.title'),//权限和CLIENT_MANAGER保持一致
                key: '/client_config'
              },
              {
                title: t('tool.controlRecord.title'),
                key: `/client_manager_records`
              },
            ]
          }
        }
        return {
          title: res?.name,
          key: `/${res?.permissionType?.toLowerCase()}`,
        };
      });

      if (!routeMenuList?.length) {
        return (
          <div
            key={curRoute}
            className={classnames(styles.singleMenu, {
              [styles.active]: activeKey === curRoute ||
                (curRoute === '/system_data_operate' && activeKey === '/batch_execute'), //  特殊处理批量操作
            })}
            onClick={() => {
              history.push(curRoute);
            }}
          >
            {renderCustomMenuTitle(item)}
          </div>
        );
      } else {
        curRoute = "/" + item?.items?.[0]?.permissionType?.toLowerCase()
      }

      return (
        <DropDownRouteMenu
          key={curRoute}
          title={renderCustomMenuTitle(item)}
          routeMenuList={routeMenuList}
          handleClickMenu={handleClickMenu}
          activeKey={activeKey}
        ></DropDownRouteMenu>
      );
    });
  }

  const handleLeftWrapResize = () => {
    // @ts-ignore
    const lWidth = document.getElementsByClassName("react-resizable-global-search")[0]?.offsetWidth;

    setGlobalSearchInputWidth(lWidth);
  };
 
  const ResizeHandle = (
    <div className={styles.resizeHandle}>
      <Iconfont type="icon-handle-8"></Iconfont>
    </div>
  );

  return (
    <div className={styles.headerNavWrapper}>
      <div
        className={classnames(styles.headerNavCol, styles.headerNavLogo)}
        onClick={() => history.push("/system_home")}
      >
        <img alt="CloudQuery Logo" src={logoUrl} height={36}></img>
      </div>
      <div
        key={'/system_home'}
        className={classnames(styles.singleMenu, {
          [styles.active]: activeKey === '/system_home'
        })}
        onClick={() => {
          history.push('/system_home');
        }}
      >
        {t('home.title')}
      </div>
      <div className={classnames(styles.menuContent, styles.headerNavCol)}>
        {renderMenu(leftMenu)}
      </div>
      {/* 全局检索 */}
      <div
        ref={globalSearchRef}
        className={classnames('flexAlignCenter', styles.globalSearchContent)}
        onMouseEnter={() => { 
          !globalSearchModalVisible && setGlobalSearchInputVisible(true)
         }}
        onMouseLeave={() => {

          if (!globalSearchValue) {
            setGlobalSearchInputVisible(false);
            setGlobalSearchValue(undefined);
            setGlobalSearchModalType(null);
            dispatch(hideModal('ModalGlobalSearch'));
            setGlobalSearchInputWidth(300);
          }
          if (globalSearchModalVisible && globalSearchModalType === null) {
            setGlobalSearchModalType(null);
            dispatch(hideModal('ModalGlobalSearch'));
          }
        }}>
        {
          (globalSearchInputVisible || globalSearchValue) ?
            <div>
              <ResizableBox
               className={classnames('react-resizable-global-search',styles.globalSearchResizableBox) }
                handle={ResizeHandle}
                {...ResizableBoxProps}
                onResize={handleLeftWrapResize}
                maxConstraints={[width, 0]}
                width={width > 300 ? 300 : 200}
              >
                <Input.Search
                  allowClear
                  autoFocus={false}
                  ref={globalInputSearchRef}
                  enterButton
                  onSearch={(value) => {
                    if (globalSearchValue && value) {
                      setGlobalSearchModalType('modal');
                      setTimeout(() => {
                        dispatch(showModal('ModalGlobalSearch'));
                      })
                      globalInputSearchRef.current?.blur()
                    }
                  }}
                  placeholder={t('golbalSearch.placeholder')}
                  onFocus={() => {

                    // debugger
                    dispatch(showModal('ModalGlobalSearch'))
                    setGlobalSearchModalType('overlay');
                  }}
                  onChange={(e: any) => {
                    setGlobalSearchValue(e.target.value);
                    setGlobalSearchModalType('overlay');
                    if (!e.target.value) {
                      dispatch(hideModal('ModalGlobalSearch'))
                    } else {
                      dispatch(showModal('ModalGlobalSearch'))
                    }
                  }}
                  size='small'
                  style={{ width: '100%' }}
                />
              </ResizableBox>
              
              <GlobalSearchModal  
               visible={globalSearchModalVisible}
                width={globalSearchInputWidth}
                searchValue={globalSearchValue} 
                globalSearchModalType={globalSearchModalType} 
                resetGlobalSearchModalType={() => {
                  setGlobalSearchModalType(null)
                } } 
              />
            </div>
            :
            <Iconfont
              style={{ fontSize: 14 }}
              className={styles.subMenuItemWrapper}
              onMouseEnter={() => { setGlobalSearchInputVisible(true); }}
              type='icon-search'
            />
        }

      </div>
      <div className={classnames('flexAlignCenterJustifyEnd', styles.rigthMenuWrapper)}>
        {/* 消息 */}
        <MessagePopover
          activeKey={activeKey}
          routeMenu={UserMesMenuRight}
        ></MessagePopover>
        {renderMenu(rightMenu)}
        {/* 系统管理 */}
        <DropDownRouteMenu
          title={<><SettingOutlined style={{ fontSize: '16px', marginRight: "6px" }} />{t('systemManagement.title')}</>}
          routeMenuList={systemManage}
          handleClickMenu={handleClickMenu}
          activeKey={activeKey}
        />
        {/* 中英文语言切换 */}
        {/* <Switch
          checkedChildren="英文"
          unCheckedChildren="中文"
          checked={locales === 'en'}
          onChange={(checked: boolean) => {
            if (checked) {
              handleLanguageChange('en')
            }
            else {
              handleLanguageChange('zh')
            }
          }}
        /> */}
      </div>
      {/* 个人设置 */}
      <DropDownRouteMenu
        title={
          userAvatar ? (
            <img
              alt=''
              src={userAvatar}
              className={styles.fs28}
            />
          ) : (
            <span className={classnames(styles.fs28, styles.userImgWrap)}>{userName?.substring(0, 1)}</span>
          )
        }
        routeMenuList={userPermList}
        handleClickMenu={handleClickMenu}
        hasDownOutlined={false}
        activeKey={activeKey}
        extraInfo={
          <div className={classnames(styles.flexItem)} style={{ padding: '8px 24px 8px 14px' }}>
            {userAvatar ? (
              <img
                alt="avatar"
                src={userAvatar}
                className={styles.fs28}
              />
            ) : (
              <span className={classnames(styles.fs28, styles.userImgWrap)}>{userName?.substring(0, 1)}</span>
            )}
            <div className={styles.ml4}>
              <span className={styles.helloText}>{t("personal:hello")}，</span>
              <div className={styles.themeColor}>{userName}</div>
            </div>
          </div>
        }
      ></DropDownRouteMenu>

      <PurposeConfirm
        visible={clientId !== undefined && purposeConfirm}
      ></PurposeConfirm>
    </div>
  );
}

export const DropDownRouteMenu = ({
  title,
  routeMenuList,
  handleClickMenu,
  activeKey,
  hasDownOutlined = true,
  extraInfo,
}: {
  title: any
  routeMenuList: RouteEntity[]
  handleClickMenu: any
  activeKey: string
  hasDownOutlined?: boolean
  extraInfo?: any
}) => {
  const activePaths = useMemo(() => {
    const tempPaths: any[] = []
    routeMenuList.forEach(({ key, children }) => {
      tempPaths.push(key)
      if (children) {
        children.map(({ key }) => tempPaths.push(key))
      }
    })
    return tempPaths
  }, [routeMenuList])

  const matchRoute = useMemo(
    () =>
      activePaths.find((path) => {
        const pathReg = new RegExp(`^${path}($|/)`)
        return activeKey.match(pathReg)
      }),
    [activeKey, activePaths],
  )

  if (!routeMenuList || !routeMenuList[0]) return <div></div>

  const dropMenu = (
    <Menu onClick={handleClickMenu}>
      {extraInfo}
      {routeMenuList.map(({ key, title, children }) =>
        children ? (
          <Menu.SubMenu key={key} title={title} popupClassName="drop-submenu">
            {children.map(({ key, title }) => (
              <Menu.Item key={key}>{title}</Menu.Item>
            ))}
          </Menu.SubMenu>
        ) : (
          <Menu.Item key={key}>{title}</Menu.Item>
        ),
      )}
    </Menu>
  )

  return (
    <Dropdown
      overlay={dropMenu}
      trigger={['hover']}
      placement="bottomRight"
      overlayClassName="menu-dropdown"
    >
      <div
        className={classnames(
          styles.subMenuItemWrapper,
          matchRoute && styles.subMenuItemWrapperActive,
        )}
      >
        <span className={styles.subMenuItemText}>
          {title}
          {hasDownOutlined && (
            <Iconfont type="icon-xialaanniu" style={{ fontSize: 16, marginLeft: 4 }} />
          )}
        </span>
      </div>
    </Dropdown>
  )
}
