import React, { useCallback, useEffect, useRef, useState } from 'react'
import { useDispatch, useRequest, useSelector } from 'src/hook'
import { Button, Form, Input, Steps, message } from 'antd'
import { UIModal } from 'src/components'
import { FormLayout } from 'src/constants'
import { hideModal } from 'src/store/extraSlice/modalVisibleSlice'
import { getUserOTPKey, refreshUserOTPKey, bindUserOTPKey, userLogout } from 'src/api'
import * as OriginQRCode from 'src/assets/js/qrcode.js'
import styles from './index.module.scss'
import classNames from 'classnames'
import { persistor } from 'src/store'
import { useTranslation } from 'react-i18next';

interface ILoginBindOtpModalProps {
  visible: boolean;
  closeOtpModal: () => void;
}

const QRCode = OriginQRCode.default as any

export const LoginBindOtpModal = ({visible, closeOtpModal}: ILoginBindOtpModalProps) => {
  const { t } = useTranslation()
  const dispatch = useDispatch()
  const { userId } = useSelector((state) => state.login.userInfo)

  const [current, setCurrent] = useState(0)
  const [form] = Form.useForm()
  const qrRef = useRef() as any
  const qrDom = document.getElementById('qrcode')

  const [checkCodeKey, setCheckCodeKey] = useState('')

  //初次 二维码
  const { run: getInitialCheckCodeKey } = useRequest(getUserOTPKey, {
    manual: true,
    formatResult: ({ optKey }) => optKey,
    onSuccess: (optKey: string) => {
      setCheckCodeKey(optKey)
    },
  })
  // 刷新二维码
  const { run: getRefreshCheckCodeKey } = useRequest(refreshUserOTPKey, {
    manual: true,
    formatResult: ({ optKey }) => optKey,
    onSuccess: (optKey: string) => {
      setCheckCodeKey(optKey)
    },
  })

  // 绑定otpKey
  const {run: bindOtpKey} = useRequest(bindUserOTPKey, {
    manual: true,
    onSuccess(data) {
      message.success(t("main:otpBindSuccessful"));
      closeOtpModal();
    }
  })

  useEffect(() => {
    if (visible && userId && current == 1) {
      getInitialCheckCodeKey(userId)
    }
    if (userId) {
      let arr=userId.split('');
      arr.splice(2,3,"*","*","*");
      let str=arr.join('');
    }
  }, [userId, current, visible])

  const createQR = useCallback(() => {
    if (qrRef.current) {
      qrRef.current?.clear()
    }
    if (qrDom) {
      qrDom.innerHTML = ''
    }

    if (!qrDom) return
    let uri = 'otpauth://totp/'
    uri += encodeURIComponent(userId ? `${userId}-otp` : '')
    const code = checkCodeKey?.slice(0, 32)
    uri += '?secret=' + code
    uri += '&algorithm=SHA1'
    uri += '&digits=6'
    uri += '&period=30'

    const qrImg = new QRCode(qrDom, {
      text: uri,
      width: 200,
      height: 200,
      correctLevel: QRCode.CorrectLevel.H,
    })
    qrRef.current = qrImg
  }, [checkCodeKey, qrDom, qrRef, userId])

  useEffect(() => {
    if (checkCodeKey) {
      createQR()
    }
  }, [createQR, checkCodeKey, qrDom])

  const renderFooter = (
    <div>
      <Button
        style={{}}
        type="primary"
        onClick={() =>
          userLogout().then(async () => {
            persistor.purge();
            closeOtpModal();
          })
        }
      >
        {t("main:exit")}
      </Button>
      {
        current === 1 &&
        <Button
          className={styles.mr10}
          onClick={() => {
            setCurrent(0)
          }}
        >
          {t("main:prevStep")}
        </Button>
      }
      {
        current === 0? 
        <Button type="primary" onClick={() => {
          setCurrent(1)
        }}>
          {t("main:nextStep")}
        </Button>
        :
        <Button
          type="primary"
          onClick={() => {
            form.validateFields().then((values) => {
              userId && bindOtpKey({userId, otpPassword: values?.otpPassword})
            })
          }}
        >
          {t("main:bindNow")}
        </Button>
      }
    </div>
  );

  const steps = [
    {
      title: t("main:installApplication"),
    },
    {
      title: t("main:bindOtp"),
    },
  ];

  return (
    <UIModal
      title={t("main:bindOtp")}
      visible={visible}
      closable={false}
      footer={renderFooter}
      onCancel={() => {
        dispatch(hideModal('OtpBindingtModal'))
      }}
      className={styles.loginBindOtp}
    >
      <>
        <Steps current={current} labelPlacement="vertical">
          {steps.map(({ title }, index) => (
            <Steps.Step title={title} key={index} />
          ))}
        </Steps>
        {
          current === 0 && (
            <div className={styles.boxMargin}>
              <div style={{ marginLeft: '20%' }}>
                <p className={styles.otpRed}>{t("main:bindSecurityDevice", { userId })}</p>
                <p>{t("main:downloadAuthyGoogleAuthenticator")}</p>
                <p>{t("main:searchAuthyGoogleAuthenticatorOnAppStore")}</p>
                <p>{t("main:searchAuthyGoogleAuthenticatorOnGooglePlayStore")}</p>
                <h3 className={styles.otpHint}>{t("main:proceedToBindingPage")}</h3>
              </div>
            </div>
          )
        }
        {
          current === 1 && (
            <div className={styles.boxMargin}>
              <div >
                <p className={classNames(
                  styles.otpRed,
                  styles.textAlignCenter
                )}>{t("main:bindSecurityDevice", { userId })}</p>
                <p className={styles.textAlignCenter}>{t("main:scanQrCodeForVerificationCode")}</p>

                <div
                  id="qrcode"
                  style={{ display: 'flex', justifyContent: 'center', cursor: 'pointer', width: 100, height: 100, marginLeft: '44%' }}
                  onClick={() => userId && getRefreshCheckCodeKey(userId)}
                ></div>
                <Form form={form} {...FormLayout} style={{marginLeft: '36%', marginTop: '10px'}}>
                  <Form.Item
                    name='otpPassword'
                    rules={[{ required: true, message: t("main:enterOtpVerification") }]}
                  >
                    <Input placeholder={t("main:otpVerificationCode")} />
                  </Form.Item>
                </Form>
              </div>
            </div>
          )
        }
      </>
    </UIModal>
  )
}
