import React, { useEffect } from 'react';
import { Modal, Button, Form, Input, message } from 'antd';
import { useCounter, useInterval } from 'react-use';
import { persistor } from 'src/store';
import { userPhoneValidator } from 'src/util';
import { useDispatch, useRequest, useSelector } from 'src/hook';
import { setUserInfo } from 'src/appPages/login/loginSlice'
import { sendPhoneCode, userLogout, updateUserPhone } from 'src/api';
import styles from './index.module.scss';
import { useTranslation } from 'react-i18next';
import i18next from 'i18next';

interface ILoginBindPhoneModalProps {
  visible: boolean;
  closePhoneModal: () => void;
}
// eslint-disable-next-line import/no-anonymous-default-export
export default ({ visible, closePhoneModal }: ILoginBindPhoneModalProps) => {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  
  const {userInfo} = useSelector(state => state.login)
  const [count, { dec, reset, set }] = useCounter(60, 60, 0);
  //绑定手机号
  const { run: updateUserPhoneRun } = useRequest(updateUserPhone, {
    manual: true,
    onSuccess() {
      message.success(t("main:mobileBindSuccessful"));
      dispatch(setUserInfo({...userInfo, forceBindPhone: false, telephone: form.getFieldValue('phoneNumber')}))
      closePhoneModal();
    },
  });

  useEffect(() => {
    set(0);
  }, [set]);

  useInterval(() => dec(1), 1000);

  const [form] = Form.useForm();

  const renderFooter = (
    <div>
      <Button
        style={{}}
        type="primary"
        onClick={() =>
          userLogout().then(async () => {
            persistor.purge();
            closePhoneModal();
          })
        }
      >
        {t("main:exit")}
      </Button>
      <Button
        type="primary"
        onClick={() => {
          form.validateFields().then((values) => {
            updateUserPhoneRun(values?.phoneNumber, values?.authCode);
          });
        }}
      >
        {t("main:bindNow")}
      </Button>
    </div>
  );

  return (
    <Modal
      width={i18next.language === 'en' ? 800 : 500}
      title={t("main:bindPhoneNumber")}
      visible={visible}
      closable={false}
      footer={renderFooter}
    >
      <Form form={form} labelCol={{ span: 5 }} labelAlign="left">
        <Form.Item label={t("main:phoneNumber")}>
          {
            <Form.Item
              name="phoneNumber"
              rules={[{ validator: userPhoneValidator }]}
            >
              <Input placeholder={t("main:enterPhoneNumber")} />
            </Form.Item>
          }
        </Form.Item>
        <Form.Item label={t("main:verificationCode")}>
          <div className={styles.displayFlex}>
            <Form.Item
              name="authCode"
              style={{width: '60%'}}
              rules={[{ required: true, message: t("main:enterVerificationCode") }]}
            >
              <Input autoFocus />
            </Form.Item>
            {
              <Button
                disabled={!!count}
                className={styles.ml10}
                type="primary"
                onClick={() => {
                  form.validateFields(['phoneNumber']).then(({ phoneNumber }) => {
                    // todo 根据手机号码 获取验证码
                    sendPhoneCode?.(phoneNumber)?.then(() => {
                      reset();
                    });
                  });
                }}
              >
                {count ? `${count}s` : t("main:getVerificationCode")}
              </Button>
            }
          </div>
        </Form.Item>
      </Form>
    </Modal>
  );
};
