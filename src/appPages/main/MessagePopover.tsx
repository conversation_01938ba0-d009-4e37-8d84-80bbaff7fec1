import React, { useState, useEffect, useCallback } from 'react'
import { CopyOutlined } from '@ant-design/icons';
import * as _ from 'lodash';
import classnames from 'classnames'
import copy from "copy-to-clipboard";
import { useTranslation } from 'react-i18next';
import { useHistory } from 'react-router-dom'
import { Badge, List, Popover, Modal, notification, message as Message } from 'antd'
import {
  putReadMes,
  checkFileExistance
} from 'src/api'
import i18n from 'src/i18n';
import { handleDownload } from "src/util";
import { fetchGet } from 'src/api/customFetch'
import {
  setOverviewPageState,
  setOverviewPageDetailParams
} from 'src/pageTabs/audit/overview/overviewSlice'
import { IModuleMapKeys } from 'src/service/moduleService'
import {
  setDataChangeMineApplyPageState,
  setDataChangeMineApplyDetailParams,
  setDataChangeMineApprovePageState,
  setDataChangeMineApproveDetailParams,
} from 'src/pageTabs/data-change/dataChangeSlice'
import { Iconfont, LinkButton } from 'src/components'
import {
  setMineApplyPageState,
  setMineApplyDetailParams,
  setMineApprovePageState,
  setMineApproveDetailParams
} from 'src/pageTabs/access-request/accessRequestSlice'
import { useDispatch, useSelector } from 'src/hook'
import { UserMesState, getMessages } from 'src/store/extraSlice/useMesSlice'
import { showModal } from 'src/store/extraSlice/modalVisibleSlice'
import { setUnauthorizedOperationState } from 'src/pageTabs/audit/auditSlice';
import { ProblemConnectionModal } from 'src/pageTabs/userMesPage/components/ProblemConnectionModal'
import { setTabsKey, setExportParamsFromMessage } from 'src/pageTabs/taskCenter/taskCenterSlice'
import styles from './header.module.scss'
interface MesType {
  [key: string]: {
    name: string
    opName: string
    opClick: (id: number, record?: any, msgId?: number) => void
  }
}

interface RouteEntity {
  key: IModuleMapKeys
  title?: string
  children?: RouteEntity[]
  logo?: any
  activeLogo?: any
}

const MAX_NOTIFICATIONS = 3; // 最多显示的通知数量
// 个人消息
const MessagePopover = ({
  activeKey,
  routeMenu,
}: {
  activeKey: string
  routeMenu: RouteEntity
}) => {

  const { t } = useTranslation();
  const history = useHistory()
  const dispatch = useDispatch()
  const [visible, setVisible] = useState(false)

  const [visibleNotifications, setVisibleNotifications] = useState<Set<number>>(new Set());

  const message: UserMesState = useSelector((state) => state.message)
  const userId: string = useSelector((state) => state.login.userInfo.userId) || '';
  const exportAutoDownloadStatus = useSelector((state) => state.login.exportAutoDownload) || false;

  const mesTypeMap: MesType = {
    EXPORT: {
      name: t('mes.type.exportTask'),
      opName: t('common.btn.view'),
      opClick: async (id: number) => {
        await putReadMes(id)
        dispatch(getMessages(userId))
        history.push('/download')
      },
    },
    PROCESS: {
      name: t('mes.type.process'),
      opName: t('common.btn.view'),
      opClick: async (id: number) => {
        await putReadMes(id)
      },
    },
    AUDIT_WARNING: {
      name: t('mes.type.audit_warning'),
      opName: t('common.btn.view'),
      opClick: async (id: number, msgId) => {
        await putReadMes(id)
        dispatch(getMessages(userId))
        // TODO: 审计详情表格
        // refreshMes()
      },
    },
    ALARM: {
      name: t('mes.type.alarm'),
      opName: '',
      opClick: async () => { },
    },
  }
  //轮询的近10条消息 根据字段判断是否导出弹框
  /* 
   whetherPop 是否弹出弹框（任务创建成功|执行完成）
   isCanDownload: 是否为已执行完成 ， whetherPop = true,此字段false弹框内容创建成功 true表示任务执行完成
   businessId 下载id
   */

  const copyMet = (mes: string) => {
    copy(mes);
    Message.success(i18n.t("features:copySuccess"));
  };
  //结果集导出下载 先校验文件是否存在-> 加密文件弹框 -> 下载
  const onDownloadResultGrid = async (taskId: string) => {
    checkFileExistance(taskId.toString(), 'export')
      .then(() => {
        fetchGet(`/export/export/check/encrypt/${taskId}`).then((res: any) => {

          const { whetherToEncrypt, fileSecretKey } = res || {}
          if (whetherToEncrypt) {
            Modal.info({
              width: 520,
              content: (
                <div>
                  <div style={{ fontSize: '18px', marginBottom: '30px' }}>{i18n.t("features:fileKey")}</div>
                  {i18n.t("features:exportFileEncrypted")}:
                  <span style={{ fontWeight: "bold" }}>
                    {fileSecretKey}
                  </span>
                  <CopyOutlined
                    style={{ color: "#0c2dc7" }}
                    onClick={() => {
                      copyMet(fileSecretKey);
                    }}
                  />
                </div>
              ),
              icon: null,
              onOk: () => {
                handleDownload({
                  href: `/export/export/download/${taskId}`,
                });
              }
            });
          } else {
            handleDownload({
              href: `/export/export/download/${taskId}`,
            });
          }
        }).catch((err: any) => { console.error(err) })
      })
      .catch();
  }

  //审计  注意部分导出不需要文件校验
  const onDownloadAuditExport = async (taskId: string) => {
    const origin = window?.location?.origin;
    checkFileExistance(taskId.toString(), 'export')
      .then(() => {
        handleDownload({
          href: `${origin}/export/export/download/${taskId}`
        });
      })
      .catch();
  }

  const onHandleExport = (businessId: string, msgSourceType: any, id: number) => {
    // 标记已读
    mesTypeMap['PROCESS']?.opClick(id);
    notification.close(`${id}`);
    removeNotification(id)

    switch (msgSourceType) {
      case 'RESULTSET_EXPORT':
        onDownloadResultGrid(businessId)
        break;
      case 'AUDIT_EXPORT': //审计导出
        onDownloadAuditExport(businessId)
        break;
      default:  //'USER_EXPORT' 默认直接用户导出
        window.open(`/export/export/download/${businessId}`)
        break;
    }
  }

  const goDownloadMenu = (businessId: number, id?: number,) => {
    // 标记已读 其他地方已处理已读 部分未处理
    if (id) {
      mesTypeMap['PROCESS']?.opClick(id);
      notification.close(`${id}`);
      removeNotification(id)

    }
    history.push('/download');
    dispatch(setTabsKey('handleExport'))
    dispatch(setExportParamsFromMessage({ taskId: businessId, dataPicker: [] }));

  }

  const openErrOrInfoNotification = (item: any, type: 'info' | 'error') => {

    notification[type]({
      type: 'error',
      placement: 'bottomLeft',
      message: <div className={styles.messageNotTitle}>{item?.content}</div>,
      duration: 10,
      key: `${item?.id}`,
      description: (
        <div style={{ color: '#667084', fontSize: 12, fontWeight: 500 }}>
          {t('msg.notifaction.taskCreated.prefix')}<LinkButton onClick={() => goDownloadMenu(item?.businessId, item?.id)}>{t('personal:taskCenter')}</LinkButton>
          {t('common.btn.view')}
        </div>
      ),
      onClose: () => {
        mesTypeMap['PROCESS']?.opClick(item?.id);
        removeNotification(item?.id)
      }
    });
  }

  const openSuccessNotification = (autoDownload: boolean, item: any) => {

    notification.success({
      placement: 'bottomLeft',
      message: <div className={styles.messageNotTitle}>{item?.content}</div>,
      duration: 10,
      key: `${item?.id}`,
      onClose: () => {
        mesTypeMap['PROCESS']?.opClick(item?.id, item?.msgId, item);
        removeNotification(item?.id)
      },
      description: (
        <div style={{ color: '#667084', fontSize: 12, fontWeight: 500 }}>
          {autoDownload ?
            <>
              {t('msg.notifaction.taskExecuted.tip1')}
              <LinkButton onClick={() => goDownloadMenu(item?.businessId, item?.id)}>{t('personal:taskCenter')}</LinkButton>
            </>
            :
            <>
              {t('msg.notifaction.taskExecuted.tip2')}<LinkButton onClick={() => goDownloadMenu(item?.businessId, item?.id)}>{t('personal:taskCenter')}</LinkButton>{t('msg.notifaction.taskExecuted.tip3')}
              <LinkButton
                onClick={() => {
                  onHandleExport(item?.businessId, item?.msgSourceType, item?.id);

                }}>{t('common.btn.download')
                }
              </LinkButton>
            </>
          }
        </div>
      )
    });
  }
  const addNotificationIfNotExists = useCallback((id: number) => {
    setVisibleNotifications(prev => {
      if (prev.has(id)) return prev;
      const updated = new Set(prev);
      updated.add(id);
      return updated;
    });
  }, []);

  // 移除通知
  const removeNotification = useCallback((id: number) => {
    setVisibleNotifications(prev => {
      const updated = new Set(prev);
      updated.delete(id);
      return updated;
    });
  }, []);

  const renderNovicationTip = useCallback((messages: any, autoDownload: boolean) => {

    const filteredMessages = messages?.filter((item: any) => {
      //符合弹窗条件的
      return item?.whetherPop && (item?.exportOccurError || item?.isCanDownload);
    })

    setVisibleNotifications(prevVisible => {
      const activeKeys = Array.from(prevVisible);
      //过滤前三条满足条件的消息
      const remainingSlots = MAX_NOTIFICATIONS - activeKeys.length;
      const newMessages = filteredMessages?.slice(0, remainingSlots)?.filter((item: any) => !prevVisible.has(item?.id));

      newMessages.forEach((item: any) => {
          //添加新消息
        if (!prevVisible.has(item.id)) {
          if (item?.exportOccurError) {//失败
            openErrOrInfoNotification(item, 'error');
            addNotificationIfNotExists(item?.id);
          } else if (item?.isCanDownload) {
            //是否自动下载
            if (autoDownload) {
              onHandleExport(item?.businessId, item?.msgSourceType, item?.id)
            }
            //导出文件完成
            openSuccessNotification(autoDownload, item);
            addNotificationIfNotExists(item?.id);
          }
        }
      });
      return prevVisible; 
    });

  }, [])

  useEffect(() => {
    renderNovicationTip(message?.messages, exportAutoDownloadStatus)
  }, [JSON.stringify(message?.messages), exportAutoDownloadStatus])

  const dropdownOverlay = (
    <div className={styles.MessagePopover}>
      <List
        size="small"
        header={<div>{t('msg.sdtMenu.notReadMessage')}</div>}
        bordered={false}
        dataSource={
          message?.messages?.length > 5
            ? message?.messages?.slice(0, 5)
            : message?.messages
        }
        loadMore={
          <div style={{ textAlign: "center", padding: "12px " }}>
            <LinkButton
              onClick={() => {
                history.push(String(routeMenu.key));
                setVisible(false);
              }}
            >
              {t('common.btn.viewMore')}
            </LinkButton>
          </div>
        }

        renderItem={(item: any) => {

          const onHandleTitleClick = () => {

            if (item.type === "dataCorrection" || item.type === "publishChanges") {
              const params = {
                ...item,
                id: item?.dataChangeId,
                mainUUID: item?.flowMainUUID,
                originType: 'message'
              }
              if (item.applyType === "APPROVAL") {
                history.push('/data_change_mine_approve')
                dispatch(setDataChangeMineApprovePageState('detail'))
                dispatch(setDataChangeMineApproveDetailParams(params))
              } else {
                // 数据变更-我的申请-详情
                history.push('/data_change_mine_apply')
                dispatch(setDataChangeMineApplyPageState('detail'))
                dispatch(setDataChangeMineApplyDetailParams(params))
              }

            } else {
              const params = {
                ...item,
                id: item?.flowUUID,
                mainUUID: item?.flowMainUUID,
                originType: 'message'
              }

              if (item.applyType === "APPROVAL") {
                // 流程-我的审批-详情
                history.push('/mine_approve')
                dispatch(setMineApprovePageState('detail'))
                dispatch(setMineApproveDetailParams(params))
              } else {
                // 流程-我的申请-详情
                history.push('/mine_apply')
                dispatch(setMineApplyPageState('detail'))
                dispatch(setMineApplyDetailParams(params))
              }
            }
          }

          // 渲染语句明细
          const gotoStatementDetail = (params: any) => {
            dispatch(setOverviewPageState('statement_detail'))
            dispatch(setOverviewPageDetailParams(params))
          }

          // 渲染越权操作
          const gotoOperateUnauthorized = (params: any) => {
            dispatch(setOverviewPageState('operate_unauthorized'))
            dispatch(setOverviewPageDetailParams(params))
          }
          const onHandleJumpToAuditAnalysis = async () => {

            let params: any = {
              timeRange: undefined,
              auditIds: item?.param?.['${SYSTEM.audit_ids}'] ? item.param['${SYSTEM.audit_ids}'].split(',') : []
            };

            switch (item?.alarmType) {
              case 'OVER_PERMISSION':
                dispatch(setUnauthorizedOperationState(params));
                // 越权操作
                gotoOperateUnauthorized(params);
                break;
              default:
                // 语句明细
                gotoStatementDetail(params);
                break;
            }

            history.push({
              pathname: '/audit_view',
              state: params
            })
          }

          const onHandleProblemConnectionClick = async () => {
            dispatch(showModal('ProblemConnectionModal', item));
          }

          return (
            <List.Item>
              <div className={styles.messageInfo}>
                {
                  ['PROCESS', 'RESULTSET_EXPORT', 'AUDIT_EXPORT', 'USER_EXPORT'].includes(item?.msgSourceType) || ['PROBLEM_CONNECTION', 'SQL_CHECK', 'OVER_PERMISSION', 'SLOW_SQL', 'HIGH_RISK', 'EXPORT_FILE_SIZE'].includes(item?.alarmType) ?
                    <span
                      title={item?.content}
                      className={styles.titleHover}
                      onClick={() => {
                        mesTypeMap['PROCESS']?.opClick(item?.id, item?.msgId, item)
                        if (item?.msgSourceType === 'PROCESS') {
                          onHandleTitleClick()
                        } else if (['RESULTSET_EXPORT', 'AUDIT_EXPORT', 'USER_EXPORT'].includes(item?.msgSourceType) || ['EXPORT_FILE_SIZE'].includes(item?.alarmType)) {
                          goDownloadMenu(item?.businessId)

                        } else if (item?.alarmType === 'PROBLEM_CONNECTION') {
                          onHandleProblemConnectionClick()
                        } else {
                          onHandleJumpToAuditAnalysis()
                        }
                      }}
                    >
                      {item?.content}
                    </span>
                    :
                    <span title={item?.content}>{item?.content}</span>
                }
                <div className={styles.messageRight}>
                  <div>{item.createdAt} </div>
                  {item?.applyType === 'RE_APPLY' && <div className='messageRedLabel ml10'>{t('msg.tip.important')}</div>}
                </div>
              </div>
            </List.Item>
          )
        }}
      />
    </div>
  );

  const msgRender = (
    <span
      onClick={() => {
        history.push(String(routeMenu.key));
      }}
      onMouseEnter={() => {
        setVisible(true);
      }}
      className={styles.subMenuItemText}
    >
      <Badge
        count={message?.num}
        size="small"
      >
        <Iconfont type="icon-tongzhi1" style={{ fontSize: "16px", marginRight: "6px" }} />
      </Badge>
      {t('msg.title')}
    </span>
  );

  return (
    <div
      className={classnames(
        styles.subMenuItemWrapper,
        activeKey.includes(String(routeMenu.key)) &&
        styles.subMenuItemWrapperActive,
      )}
      onMouseLeave={() => setVisible(false)}
    >
      {message?.num ? (
        <Popover
          visible={visible}
          content={dropdownOverlay}
          placement="bottom"
          overlayStyle={{
            minWidth: 600,
            minHeight: 120,
          }}
        >
          {msgRender}
        </Popover>
      ) : (
        msgRender
      )}
      <ProblemConnectionModal />
    </div>
  )
}

export default MessagePopover;