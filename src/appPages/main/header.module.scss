@import 'src/styles/variables.scss';

.themeColor{
  color: var(--primary-color);
}
.helloText {
  color: #0F244C;
  
  [data-theme='dark'] & {
    color: #fff;
  }
}
.ml4 {
  margin-left: 4px;
}
.mr20 {
  margin-right: 20px;
}
.fs16 {
  font-size: 16px;
}
.pad8 {
  padding: 8px;
}
.flexItem {
  display: flex;
  align-items: center;
  justify-content: center;
}
.singleMenu {
  margin: 0 8px;
  padding: 12px 4px;
  font-weight: 500;
  cursor: pointer;
  &:hover {
    color: var(--primary-color);
  }
  &.active {
    border-bottom: 3px solid var(--primary-color);
  }
}
.headerNavWrapper {
  height: 100%;
  display: flex;
  overflow-x: auto;
 
  .headerNavLogo {
    margin-right: 28px;
  }

  .headerNavCol {
    height: 100%;
    display: flex;
    align-items: center;  
  }
  .menuContent {
    overflow-y: hidden;
    overflow-x: auto;
  }
  .rigthMenuWrapper {
   width: auto;
  }
  .menuItemWrapper {
    position: relative;
    height: 100%;
    font-size: 14px;
    font-weight: 500;
    .menuItem {
      padding: 0 6px;
      margin: 0 14px;
      height: 100%;
      display: flex;
      align-items: center;
      transition: all .3s ease-in-out;
      &:hover {
        .menuItemText {
          opacity: 1;
        }
      }
    }

    .menuItemLogo {
      margin-right: 8px;
      height: 20px;
    }

    .menuItemText {
      color: #0F244C;;
      white-space: nowrap;
      transition: all .3s ease-in-out;
    }

    .menuItemIcon {
      margin-right: 8px;
      height: 30px;
      width: 30px;
    }

    &.menuItemWrapperActive {
      border-bottom: 1px solid #2708ee;;
      .menuItemIcon {
        filter: drop-shadow(0 0 0.75rem var(--primary-color));
      }

      &::before {
        position: absolute;
        content: '';
        bottom: 0;
        left:0;
        width: 100%;
        height: 2px;
        background-color: var(--primary-color);
      }

      .menuItemText {
        opacity: 1;
      }
    }
  }

  .subMenuItemWrapper {
    position: relative;
    margin: 0 8px;
    padding: 0 4px;
    display: flex;
    align-items: center;
    height: 100%;
    font-size: 14px;
    font-weight: 500;
    transition: all .3s ease-in-out;
    :hover {
      cursor: pointer;
      color: var(--primary-color) important;
    }

    .subMenuItem {
      cursor: context-menu;
    }

    .subMenuItemText {
      font-size: 14px !important;
      font-weight: 500;
      color: #0F244C;
      display: flex;
      align-items: center;
      :hover {
        cursor: pointer;
        color: var(--primary-color);
      }
    }

    &.subMenuItemWrapperActive {
      color: var(--primary-color);
     
      &::before {
        position: absolute;
        content: '';
        bottom: 0;
        left: 0;
        width: 100%;
        height: 2px;
        background-color: var(--primary-color);
      }
    }
  }

  .globalSearchResizableBox {
    width: auto;
    margin-right: 10px;
    height: 100% !important;
  }
  .resizeHandle {
    position: absolute;
    left: -13px;
    top: calc(50% - 12px);
    font-size: 16px;
    cursor: col-resize;
    color: rgba(0, 0, 0, 0.85);
  }
}

.MessagePopover {

  .messageInfo {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    span:nth-child(1) {
      // width: 110px;
      width: 230px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    span:nth-child(1)::before {
      content: ""; /* 添加 content 属性 */
      display: inline-block; /* 设置为内联块级元素 */
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background-color: #87d068;
      margin-right: 6px;
    }

    .messageRight{
      width: 205px;
      display: flex;
      align-items: center;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .titleHover:hover {
      color: #5b85fc;
      cursor: pointer;
    }
  }

  :global {
    .ant-list.ant-list-sm.ant-list-split {
      max-height: 400px;
      overflow-y: auto;
    }
  }
}
.messageNotTitle {
  font-size: 14px;
  font-weight: 600;
}

.userImgWrap {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: #fff;
  background-color: var(--primary-color);
  &:hover {
    color: #fff !important;
  }
}
.fs20 {
  width: 20px;
  height: 20px;
  border-radius: 50%;
}
.fs28 {
  width: 28px;
  height: 28px;
  border-radius: 50%;
}

.globalSearchContent {
  padding: 10px 10px;
  flex: 1;
  justify-content: end;
}
