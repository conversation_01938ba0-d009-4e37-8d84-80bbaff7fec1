@import 'src/styles/variables';

.background {
  background-color: #F5F5F5;
}
.app {
  height: 100%;
  overflow: hidden;
  background: #fff;
  display: flex;
  flex-direction: column;
  .appHeader {
    background-color: #fff;
    height: 48px;
    padding: 0 20px;
    position: relative;
    border-bottom: 1px solid rgba(195, 199, 206, 0.5);
    // box-shadow: 0px 4px 5px 0px rgba(195, 199, 206, 0.5);
  }
  .appContent {
    flex: 1;
    overflow: hidden;
  }
}
.displayFlex {
  display: flex;
  justify-content: space-between;
}

.loginBindOtp {
  .boxMargin {
    margin: 20px 10px;
  }
  .otpHint {
    color: #3D3D3D;
    font-size: 18px;
  }
  .otpRed {
    color: #FF3232;
    font-size: 14px;
  }
  .textAlignCenter {
    text-align: center;
  }
}