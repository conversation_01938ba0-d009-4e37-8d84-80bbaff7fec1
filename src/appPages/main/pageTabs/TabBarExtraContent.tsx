// TabBarExtraContent 组件
import { Popconfirm, Tooltip } from 'antd';
import React from 'react';
import { Iconfont } from 'src/components';
import styles from './index.module.scss';
import i18n from "i18next";

const TabBarExtraContent = ({ onClose }: { onClose: () => void }) => {
  return (
    <div>
      <Tooltip title={i18n.t('closeAllTabs')} placement="left">
        <Popconfirm
          title={`${i18n.t('sureCloseAllTabs')}?`}
          onConfirm={onClose}
          placement="bottomRight"
        >
          <Iconfont type="icon-closeAll" className={styles.closeBtn} />
        </Popconfirm>
      </Tooltip>
    </div>
  );
};

export default TabBarExtraContent;