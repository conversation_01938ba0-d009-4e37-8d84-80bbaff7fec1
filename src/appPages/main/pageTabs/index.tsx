// index.tsx
import { Dropdown, Menu, Tabs } from 'antd';
import React, { useEffect, useMemo, useState }  from 'react';
import { RouterMenus } from "src/projectConfig/RouterMenus";
import { useSelector, useDispatch } from 'src/hook';
import { setActiveKey, addPageTabPanes, removePageTabPanes, clearPageTabPanes, keepCurrentPageTab } from 'src/appPages/main/pageTabs/pageTabsSlice';
import { RenderTabBar } from 'rc-tabs/lib/interface';
import { useHistory,useLocation } from 'react-router-dom';
import TabBar from './TabBar';
import TabBarExtraContent from './TabBarExtraContent';
import { getNextActiveKey } from './util';
import i18n from "i18next";
import styles from './index.module.scss';
import { isEqual } from 'lodash';

interface TabPaneProps<T> {
  title: string;
  key: string;
  tabKey: string;
  Component: React.ComponentType<T>;
}
const PageTabs = () => {
  const history = useHistory();
  const location = useLocation();
  const dispatch = useDispatch();
  const { pageTabPanes, activeKey } = useSelector(state => state.pageTabs);
  const { userInfo } = useSelector(state => state.login)
  const firstMenu = '/system_home'; //首页

  const [ refreshTabKey, setRefreshTabKey ] = useState('')
  const [ refreshUniqueKey, setRefreshUniqueKey ] = useState('')

  // 使用 useMemo 缓存菜单查找结果
  const tabPanes: TabPaneProps<any>[] = useMemo(() => {
    let result: TabPaneProps<any>[] = []
    pageTabPanes.forEach((paneKey: string) => {
      const menu = RouterMenus.find(item => item?.key === paneKey);
      if(menu){
        result.push({
          tabKey: paneKey,
          key: paneKey,
          title: menu.title,
          Component: menu.component
        })
      }
    })
    return result
  }, [pageTabPanes]);

  // 初始化时，判断上次权限是否改变,改变清空所有标签页（因为标签有持久化处理，不手动清空可能会出现权限和菜单不一致的场景）
  useEffect(()=>{
    const stroragePermissionMenus = localStorage.getItem('permissionMenus')
    if(!stroragePermissionMenus){
      return
    }
    if(!isEqual(JSON.parse(stroragePermissionMenus), userInfo.permissionMenus)){
      handleCloseAllTabs()
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  },[])


  useEffect(()=>{
    localStorage.setItem('permissionMenus', JSON.stringify(userInfo.permissionMenus))
  }, [userInfo.permissionMenus])

  // 同步路由和activeKey, pageTabPanes保持一致(这种方式可以监听所有形式的路由改变，不需要手动监听history的各种操作等)
  useEffect(() => {
    const currentPathKey = location.pathname;
    if(currentPathKey === '/system_index') return
    if (!pageTabPanes.includes(currentPathKey)) {
      dispatch(addPageTabPanes(currentPathKey));  // 添加新的Tab和activeKey
    } else if (activeKey !== currentPathKey) {
      dispatch(setActiveKey(currentPathKey));     // 仅更新 activeKey
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [location.pathname]);

  // 关闭所有标签
  const handleCloseAllTabs = () => {
    dispatch(clearPageTabPanes(firstMenu));
    history.push(firstMenu); // 更新路由到默认标签页
  };

  // 关闭单个标签
  const handleTabsEdit = (key: string | React.MouseEvent | React.KeyboardEvent, action: 'add' | 'remove') => {
    const targetKey = typeof key === 'string' ? key : '';
    const newActiveKey = getNextActiveKey(pageTabPanes, activeKey);
    if (action === 'remove' && targetKey ) {
      dispatch(removePageTabPanes({targetKey, firstMenu })); 
      // 如果关闭的是当前激活的标签，则切换到下一个标签，并更新路由
      if (activeKey === targetKey && newActiveKey) {
        history.push(newActiveKey);
      }
      // 如果是关闭最后一个标签，则更新路由为默认路由
      if (pageTabPanes.length === 1 || !newActiveKey) {
        history.push(firstMenu);
        dispatch(addPageTabPanes(firstMenu)); //首页最后再次关闭时需要设置标签页
      }
    }
  };

  // 自定义TabBar
  const renderTabBar: RenderTabBar = (props, DefaultTabBar) => {
    return <TabBar renderTabBar={() => <DefaultTabBar {...props} />} />
  }

  const handleMenuClick = (e: any, key: string) => {
    switch (e.key) {
      case 'close':
        e.domEvent.stopPropagation();      // 阻止事件冒泡,只执行当前关闭操作
        handleTabsEdit(key, 'remove');     // 关闭单个标签
        break;
      case 'refresh':
        const refreshUniqueKey = `${key}_${Date.now()}`
        setRefreshTabKey(key)                           // 刷新当前标签页
        setRefreshUniqueKey(refreshUniqueKey)           // 刷新标签对应的唯一key 
        break;
      case 'closeOther':
        dispatch(keepCurrentPageTab(key))  // 保留当前标签页
        break;
      default:
        break;
    }
  };

  const getMenu = (key: string) => (
    <Menu onClick={(e) => handleMenuClick(e, key)}>
      <Menu.Item key='close'>{i18n.t('close')}</Menu.Item>
      {/* 数据操作因为editor实例原因不能单个页面刷新 */}
      {
        key !== '/system_data_operate' && (
          <Menu.Item key='refresh'>{i18n.t('reload')}</Menu.Item>  
        )
      }
      <Menu.Item key='closeOther'>{i18n.t('closeOtherTabs')}</Menu.Item>
    </Menu>
  );

  // 自定义tab标题
  const renderTabPaneTitle = (key: string, title: string) => (
    <Dropdown 
      overlay={getMenu(key)} 
      trigger={['contextMenu']}
      placement="bottomLeft"
      getPopupContainer={() => document.body}
    >
      <div className='flexAlignCenterBetween'>
        <span className='tab-title'>{title}</span>
        <span
          className="ant-tabs-tab-remove"
          onClick={(e) => {
            e.stopPropagation();  // 阻止事件冒泡,只执行当前关闭操作
            handleTabsEdit(key, 'remove');
          }}
        >
          ×
        </span>
      </div>
    </Dropdown>
  )

  const handleTabsChange = (key: string) => {
    dispatch(setActiveKey(key));
    history.push(key);
  };

  return (
    <Tabs
      className={styles.pageTabsWrapper}
      activeKey={activeKey}
      tabBarGutter={0}
      tabBarStyle={{ marginBottom: 0 }}
      tabBarExtraContent={<TabBarExtraContent onClose={handleCloseAllTabs} />}
      renderTabBar={renderTabBar}
      onChange={handleTabsChange}
    >
      {
        tabPanes.map(({ title, key, Component }) => {
          return (
            <Tabs.TabPane 
              key={key}
              tab={renderTabPaneTitle(key, title)} 
            >
              {
                refreshTabKey === key 
                ? <Component key={refreshUniqueKey} />
                : <Component />
              }
            </Tabs.TabPane>
          )
        })
      }
    </Tabs>
  );
};

export default PageTabs;