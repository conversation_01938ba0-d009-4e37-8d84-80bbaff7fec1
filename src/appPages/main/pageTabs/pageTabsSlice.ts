import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { getNextActiveKey } from "./util";

interface IPageTabsState {
  activeKey: string
  pageTabPanes: string[]; 
}

const initialState: IPageTabsState = {
  activeKey: '',
  pageTabPanes: [''],
};  

export const pageTabsSlice = createSlice({
  name: "pageTabs",
  initialState,
  reducers: {
    addPageTabPanes(state, action: PayloadAction<string>) {
      state.activeKey = action.payload;
      if (state.pageTabPanes.includes(action.payload)) return
      state.pageTabPanes.push(action.payload);
    },
    removePageTabPanes(state, action) {
      const {targetKey, firstMenu } = action.payload;
      // 判断是否只剩下一个tab
      if (state.pageTabPanes.length === 1) {
        state.pageTabPanes = [firstMenu];
        state.activeKey = firstMenu;
        return
      }

      // 计算新的 activeKey
      if (targetKey === state.activeKey) {
        const newActiveKey = getNextActiveKey(state.pageTabPanes, state.activeKey);
        state.activeKey = newActiveKey;
      }
      // 过滤掉目标标签页
      const newPanes = state.pageTabPanes.filter(item => item !== targetKey);
      state.pageTabPanes = newPanes;
    },
    clearPageTabPanes(state, action) {
      const firstMenu = action.payload;
      // 清空时保留默认的首页
      state.pageTabPanes = [firstMenu];
      state.activeKey = firstMenu;
    },
    setActiveKey(state, action: PayloadAction<string>) {
      state.activeKey = action.payload;
    },
    // 保留当前激活的标签页
    keepCurrentPageTab(state, action: PayloadAction<string>) {
      const targetKey = action.payload;
      const newPanes = state.pageTabPanes.filter(item => item === targetKey);
      state.pageTabPanes = newPanes;
    },
  },
});

export const pageTabsReducer = pageTabsSlice.reducer;

export const { 
  addPageTabPanes, 
  removePageTabPanes, 
  clearPageTabPanes,
  setActiveKey,
  keepCurrentPageTab,
} = pageTabsSlice.actions;