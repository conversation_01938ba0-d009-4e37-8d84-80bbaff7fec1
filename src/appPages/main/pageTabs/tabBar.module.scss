// COPY FROM OLD VERSION
$limit: 20;

@function indexList($n) {
  $list: $n;
  @for $index from $n - 1 through 1 {
    $list: append($list, $index);
  }
  @return $list;
}

$list: indexList($limit);

.pageTabBarWrapper {
  padding-right: 16px;
  background: #1c335a; // tabBar背景色
  :global {
    .ant-tabs-nav-list > .ant-tabs-tab {
      margin-left: -5px !important;
      padding-left: 19px !important;
      padding-right: 9px !important;
      background: transparent;
      font-size: 13px;
      height: 30px !important;

      &:first-of-type {
        margin-left: 0px !important;
        padding-left: 14px !important;
      }

      .ant-tabs-tab-remove {
        margin-left: 0;
        font-size: 20px;
        padding: 0 5px;
      }
      .ant-tabs-tab-remove:hover {
        color: #ffffff;
      }

      &::before {
        position: absolute;
        z-index: -1;
        content: '';
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: #223856;  // 未高亮tab背景色
        box-shadow: 0px 1px 5px 1px rgba(0, 0, 0, 0.45);
        transform: skewX(10deg);

        [data-theme='dark'] & {
          background: #3e4b5d;
        }
      }

      @for $n from 1 through $limit {
        &:nth-of-type(#{$n}) {
          z-index: nth($list, $n);
        }
      }

      &.ant-tabs-tab-active {
        z-index: 30;
        transform: none;
        padding-left: 19px !important;

        .ant-tabs-tab-remove:hover {
          color: rgba(0, 0, 0, 0.85);
        }
        &::before {
          background: #f1f4f8; // 高亮tab背景色
          transform: perspective(0.5em) scale(1.015, 1.1) rotateX(1.6deg);
          box-shadow: 0px 1px 5px 1.6px rgba(0, 0, 0, 0.45);
        }
      }

      &:first-of-type {
        margin-left: 0;
        padding-left: 16px;

        &::before {
          transform-origin: left bottom;
        }

        &.ant-tabs-tab-active {
          &::before {
            transform: perspective(0.5em) scale(1, 1.1) rotateX(0.7deg);
          }
        }
      }

      &:nth-of-type(2) {
        margin-left: -8px !important;
      }
    }
    .ant-tabs {
      font-size: 12px !important;
    }
   
    .ant-tabs-nav::before {
      border: 0 !important;
    }

    .ant-tabs-nav-operations {
      [data-theme='dark'] & {
        color: #fff;
      }
    }
    .ant-tabs-nav-list > .ant-tabs-tab.ant-tabs-tab-with-remove {
      border: none !important;
      transition: none !important;
      height: 32px;
      border-radius: 0 !important;
    }

    .ant-tabs-tab-remove {
      color: #9c9c9f;
      [data-theme='dark'] & {
        &:hover {
          color: #fff;
        }
      }
    }

    // 避免点击标签时选中文字
    .ant-tabs-tab {
      user-select: none;
    }
    // 激活 Tab 文字
    .ant-tabs-nav-list > .ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn,
    .ant-tabs-nav-list > .ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn:active {
      color: #324564;
      .tab-title {
        color: #324564;
      }
    }
    // 非激活 Tab 文字
    .ant-tabs-nav-list > .ant-tabs-tab .ant-tabs-tab-btn,
    .ant-tabs-nav-list > .ant-tabs-tab .ant-tabs-tab-btn:active {
      color: #9c9c9f;
      .tab-title {
        color: #9c9c9f;
      }
    }

    // fix height on Safari
    .ant-tabs-content-holder {
      flex: 1;
    }
    .monaco-editor {
      height: 100%;
    }
  }
}