// 获取下一个activeKey
export const getNextActiveKey = (pageTabPanes: any[], activeKey: string) => {
  const nextKeyIndex = pageTabPanes.findIndex(paneKey => paneKey === activeKey);
  const newTabPanes = pageTabPanes.filter(paneKey => paneKey !== activeKey);
  const newTabPanesLastIndex = newTabPanes.length - 1;
  let newActiveKey = '';
  if(nextKeyIndex === -1 || nextKeyIndex > newTabPanesLastIndex){
    newActiveKey = newTabPanes[newTabPanesLastIndex]
  } else {
    newActiveKey = newTabPanes[nextKeyIndex]
  }
  return newActiveKey;
};