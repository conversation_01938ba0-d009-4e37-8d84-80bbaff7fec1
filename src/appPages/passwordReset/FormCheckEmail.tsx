import React, { useEffect, useRef } from 'react'
import { useHistory } from 'react-router'
import { useCounter, useInterval } from 'react-use'
import { Alert, Button, Form, Input } from 'antd'
import type { FormInstance } from 'antd/lib/form'
import styles from './index.module.scss'
import { useTranslation } from 'react-i18next'

interface FormCheckEmailProps {
  form?: FormInstance
  onConfirm?: (values: { code: string }) => void
  confirmBtn: React.ReactElement
  result?: { code: string; message: string }
  resendMail?: () => Promise<void>
}

export const FormCheckEmail = ({
  form,
  onConfirm,
  confirmBtn,
  result,
  resendMail,
}: FormCheckEmailProps) => {
  const { t } = useTranslation()
  const history = useHistory()

  const [count, { dec, reset, set }] = useCounter(45, 45, 0)
  useEffect(() => {
    set(0)
  }, [set])

  const inputRef = useRef<Input>(null)

  useEffect(() => {
    inputRef.current?.focus()
  }, [])

  useInterval(() => dec(1), 1000)

  return (
    <Form
      name="password_reset_check_email"
      form={form}
      layout="vertical"
      onFinish={(values) => {
        onConfirm?.(values)
      }}
    >
      <Form.Item noStyle>
        <Alert
          className={styles.formItemAlert}
          message={
            <pre style={{ whiteSpace: 'pre-line' }}>{`${
              result?.message || ''
            }`}</pre>
          }
          type={result?.code === 'SUCCESS' ? 'success' : 'error'}
        />
      </Form.Item>
      {result?.code === 'SUCCESS' && (
        <>
          <Form.Item label={t("verificationCode")}>
            <div className={styles.formItemMailCode}>
              <Form.Item
                name="code"
                noStyle
                rules={[{ required: true, message: t("verificationCodeInput") }]}
              >
                <Input className={styles.mailCodeInput} ref={inputRef} />
              </Form.Item>
              <Button
                className={styles.sendMailBtn}
                onClick={() => {
                  resendMail?.()?.then(() => reset())
                }}
                disabled={!!count}
              >
                {count ? `${count}s` : t("resend")}
              </Button>
            </div>
          </Form.Item>
          <Form.Item>{confirmBtn}</Form.Item>
        </>
      )}
      {result?.code !== 'SUCCESS' && (
        <Button
          className={styles.confirmBtn}
          onClick={() => history.push('/login')}
          type="primary"
        >
          {t("returnLogin")}
        </Button>
      )}
    </Form>
  )
}
