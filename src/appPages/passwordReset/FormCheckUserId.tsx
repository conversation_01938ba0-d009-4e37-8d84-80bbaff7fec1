import React, { useEffect, useRef } from 'react'
import { useHistory } from 'react-router-dom'
import { Form, Input } from 'antd'
import { FormInstance } from 'antd/lib/form'
import { UserOutlined } from '@ant-design/icons'
import { Iconfont, LinkButton } from 'src/components'
import styles from './index.module.scss'
import { useTranslation } from 'react-i18next'

interface FormCheckUserProps {
  form?: FormInstance
  onConfirm?: (values: { userId: string; captcha: string }) => void
  confirmBtn: React.ReactElement
  imgSrc?: string
  refreshCaptcha?: () => void
}

export const FormCheckUserId = ({
  form,
  onConfirm,
  confirmBtn,
  imgSrc,
  refreshCaptcha,
}: FormCheckUserProps) => {
  const { t } = useTranslation()
  const inputRef = useRef<Input>(null)
  useEffect(() => {
    inputRef.current?.focus()
  }, [])

  // 回填账号
  const { location } = useHistory<{ userId: string }>()
  useEffect(() => {
    const { userId } = location.state
    form?.setFields([{ name: 'userId', value: userId }])
  }, [form, location.state])

  return (
    <Form
      name="password_reset_check_userId"
      form={form}
      layout="vertical"
      onFinish={(values) => {
        onConfirm?.(values)
      }}
    >
      <Form.Item
        name="userId"
        label={t("account")}
        required={false}
        rules={[{ required: true, message: t("pleaseEnterAccount") }]}
      >
        <Input
          prefix={<UserOutlined className="site-form-item-icon" />}
          ref={inputRef}
        />
      </Form.Item>
      <Form.Item label={t("verificationCode")}>
        <div className={styles.formItemCaptcha}>
          <Form.Item
            name="captcha"
            noStyle
            rules={[
              {
                required: true,
                message: t("verificationCodeInput"),
              },
            ]}
          >
            <Input
              className={styles.captchaInput}
              prefix={<Iconfont type="icon-captcha" />}
            />
          </Form.Item>
          <div className={styles.captchaImg} onClick={() => refreshCaptcha?.()}>
            {imgSrc ? (
              <img
                src={imgSrc}
                onClick={(e) => {
                  e.preventDefault()
                }}
                alt={t("verificationCode")}
                title={t("clickRefresh")}
              />
            ) : (
              <LinkButton className={styles.captchaImgBtn}>{t("clickRefresh")}</LinkButton>
            )}
          </div>
        </div>
      </Form.Item>
      <Form.Item>{confirmBtn}</Form.Item>
    </Form>
  )
}
