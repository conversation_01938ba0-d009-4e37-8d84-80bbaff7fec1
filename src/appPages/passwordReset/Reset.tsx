import React from 'react'
import { usePara<PERSON>, useHistory } from 'react-router'
import { Link } from 'react-router-dom'
import { useRequest } from 'src/hook'
import { Alert, Button, Form, Input, message, Space } from 'antd'
import { passwordSpecialCharacterValidator, passwordValidator } from 'src/util'
import { changePasswordByLink, getUserIdByMailCode } from 'src/api'
import styles from './index.module.scss'
import { Redirect } from 'react-router-dom'
import { useTranslation } from 'react-i18next'

export const Reset = () => {
  const { t } = useTranslation()
  const [form] = Form.useForm()
  const { code } = useParams<{ code: string }>()
  const history = useHistory()

  const { data: userId, error } = useRequest(() => getUserIdByMailCode(code), {
    refreshDeps: [code],
  })

  const { run, loading } = useRequest(changePasswordByLink, {
    manual: true,
    onSuccess() {
      message.success(t("changeSuccess"))
      history.push('/login')
    },
  })

  if (error) {
    return <Redirect to="/login" />
  }

  return (
    <div className={styles.reset}>
      <div className={styles.header}>
        <h1 className={styles.title}>{t("changePassword")}</h1>
      </div>
      {!userId && (
        <Form.Item>
          <Alert
            className={styles.formItemAlert}
            message={
              <Space direction="vertical">
                <div>{t("currentPasswordChangeLinkInvalid")}</div>
                <Link to="/password_reset" replace>
                  {t("reApply")}
                </Link>
              </Space>
            }
            type="warning"
          />
        </Form.Item>
      )}

      {userId && (
        <>
          <Form.Item label={t("account")}>
            <div>{userId}</div>
          </Form.Item>
          <Form
            name="password_reset"
            form={form}
            layout="vertical"
            onFinish={({ newP }) => {
              run({ newPassword: newP, key: code })
            }}
          >
            <Form.Item
              label={t("newPassword")}
              name="newP"
              required={false}
              rules={[{ required: true, validator: passwordValidator }, { validator: passwordSpecialCharacterValidator }]}
            >
              <Input.Password ref={(input: Input | null) => input?.focus()} />
            </Form.Item>
            <Form.Item
              label={t("confirmNewPassword")}
              name="confirm"
              dependencies={['newP']}
              required={false}
              rules={[
                { required: true, message: t("pleaseConfirmNewPassword") },
                ({ getFieldValue }) => ({
                  validator(_rule, value) {
                    if (!value || getFieldValue('newP') === value) {
                      return Promise.resolve()
                    }
                    return Promise.reject(t("passwordsDoNotMatch"))
                  },
                }),
              ]}
            >
              <Input.Password />
            </Form.Item>
            <Form.Item>
              <Button
                className={styles.confirmBtn}
                type="primary"
                htmlType="submit"
                loading={loading}
              >
                {t("changePassword")}
              </Button>
            </Form.Item>
          </Form>
        </>
      )}
    </div>
  )
}
