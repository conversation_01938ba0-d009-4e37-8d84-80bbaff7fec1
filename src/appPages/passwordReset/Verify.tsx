import React, { useEffect, useState } from 'react'
import { useHistory } from 'react-router'
import { useRequest } from 'src/hook'
import { <PERSON>ert, Button, Form, message } from 'antd'
import { FormCheckUserId } from './FormCheckUserId'
import { FormCheckEmail } from './FormCheckEmail'
import {
  verifyUserId,
  verifyMail,
  getCaptchaImg,
  sendResetMail,
  resendMailCode,
} from 'src/api'
import styles from './index.module.scss'
import { useTranslation } from 'react-i18next'

export const Verify = () => {
  const { t } = useTranslation()
  const [current, setCurrent] = useState(0)
  const history = useHistory()

  const verifyUserIdRequest = useRequest(verifyUserId, {
    manual: true,
    throwOnError: true,
  })
  const verifyEmailRequest = useRequest(verifyMail, { manual: true })
  const sendResetMailRequest = useRequest(sendResetMail, { manual: true })
  const { run: resendMail } = useRequest(resendMailCode, { manual: true })

  const [verifyUserIdForm] = Form.useForm()
  const [verifyEmailForm] = Form.useForm()

  const { data: imgSrc, run: fetchCaptcha } = useRequest(getCaptchaImg, {
    manual: true,
  })

  useEffect(() => {
    fetchCaptcha()
  }, [fetchCaptcha])

  useEffect(() => {
    if (imgSrc) {
      return () => URL.revokeObjectURL(imgSrc)
    }
  }, [imgSrc])

  return (
    <div className={styles.verify}>
      <div className={styles.header}>
        <h1 className={styles.title}>{t("forgotPassword")}</h1>
      </div>
      {current === 0 && (
        <FormCheckUserId
          imgSrc={imgSrc}
          form={verifyUserIdForm}
          onConfirm={({ userId, captcha }) => {
            verifyUserIdRequest
              .run({ userId, verificationCode: captcha })
              .then(() => setCurrent(1))
              .catch(() => {
                fetchCaptcha()
              })
          }}
          confirmBtn={
            <Button
              className={styles.confirmBtn}
              type="primary"
              htmlType="submit"
              loading={verifyUserIdRequest.loading}
            >
              {t("confirmAccount")}
            </Button>
          }
          refreshCaptcha={fetchCaptcha}
        />
      )}
      {current === 1 && (
        <FormCheckEmail
          result={verifyUserIdRequest.data}
          form={verifyEmailForm}
          resendMail={() => {
            const userId = verifyUserIdForm.getFieldValue('userId')
            return resendMail(userId)
          }}
          onConfirm={async ({ code }) => {
            const userId = verifyUserIdForm.getFieldValue('userId')
            // 校验邮箱验证码
            const pass = await verifyEmailRequest.run({ code, userId })
            if (!pass) {
              return message.error(t("codeExpiredOrNotMatch"))
            }
            // 发送重置邮件
            const { origin, pathname } = window.location
            await sendResetMailRequest.run({
              userId,
              address: origin + pathname,
            })
            setCurrent(2)
          }}
          confirmBtn={
            <Button
              className={styles.confirmBtn}
              type="primary"
              htmlType="submit"
              loading={
                verifyEmailRequest.loading || sendResetMailRequest.loading
              }
            >
              {t("confirmEmail")}
            </Button>
          }
        />
      )}
      {current === 2 && (
        <>
          <Alert
            className={styles.formItemAlert}
            message={
              <pre style={{ whiteSpace: 'pre-line' }}>
                {`${sendResetMailRequest.data?.message || ''}`}
              </pre>
            }
            type={
              sendResetMailRequest.data?.code === 'SUCCESS'
                ? 'success'
                : 'error'
            }
          />

          <Button
            className={styles.confirmBtn}
            onClick={() => history.push('/login')}
            type="primary"
          >
           {t("returnLogin")}
          </Button>
        </>
      )}
    </div>
  )
}
