import React from 'react';
import { Iconfont, UIModal } from 'src/components';
import { useDispatch, useSelector } from 'src/hook';
import { hideModal } from 'src/store/extraSlice/modalVisibleSlice';
import styles from './index.module.scss'
import { Button } from 'antd';
import i18n from 'i18next';
export const AlarmMessagesModal = () => {
  const dispatch = useDispatch();

  const { alarmMessages } = useSelector((state) => state.login.userInfo ) as any;
  const visible = useSelector((state) => state.modal['AlarmMessagesModal']?.visible);

  const handleSubmit = () => {
    dispatch(hideModal('AlarmMessagesModal'));
  }

  return (
    <UIModal
      title=""
      width={550}
      visible={visible}
      closable={false}
      footer={<Button onClick={handleSubmit} className={styles.okBtn} >{i18n.t("ok")}</Button>}
      className={styles.alarmMessagesModal}
    >
      <div className={styles.alarmMessagesWrap}>
        <Iconfont type='icon-alarmMessagesIcon' />
        <span className={styles.alarmMessagesText}>{alarmMessages?.join(', ')}</span>
      </div>
    </UIModal>
  )
}