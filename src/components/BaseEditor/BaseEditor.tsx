import React, { useEffect, useContext, useMemo, useState, useRef } from 'react'
import {
  editor,
  IPosition,
  IScrollEvent,
} from 'monaco-editor'
import * as monaco from "monaco-editor"
import { message } from 'antd'
import { EditorContext, getModelLanguageId } from 'src/components'
import { useDispatch, useSelector } from 'src/hook'
import {
  activePaneInfoSelector,
  addPane
} from 'src/pageTabs/queryPage/queryTabs/queryTabsSlice'
import { keyStringSplitWithMultiKey } from 'src/util/hotKeys'
import { format } from 'sql-formatter';
import { formatOptionalLangs } from 'src/constants'
import {
  language as LSP_LANGUAGE,
} from 'monaco-editor/esm/vs/basic-languages/sql/sql.js'
import { allLettersUpperCase } from 'src/util'
import { editCommand } from './editCommand';
import i18n from 'i18next';
import { splitSql } from 'src/api/query'
import { debounce } from 'lodash'

export type ChangeModelContentEvent = (
  editor: editor.IStandaloneCodeEditor,
  event: editor.IModelContentChangedEvent,
) => void

interface BaseEditorProps {
  /** 如果不传入 model, 则使用 editor 实例化时自动创建的 model */
  /** 传入 null, 则会设置 editor 实例的 model 为空(即没有内容区域) */
  model?: editor.ITextModel | null
  /** editor.create 方法的第二个参数, editor 实例的 options */
  options?: editor.IEditorOptions
  /** 执行选中语句事件 */
  onExecuteSelectedText?: () => void
  /** model 内容变化事件 */
  onChangeModelContent?: ChangeModelContentEvent
  /** model 光标位置变化事件 */
  onChangeCursorPosition?: (position: IPosition) => void
  /* 滚动条位置变化事件*/
  handleChangeScrollTop?: (e: IScrollEvent) => void
  /** 执行计划的callback */
  onCommandExplain?: () => void
  getViewTableStructure?: () => void;
  className?: string
}

const defaultOptions = {
  automaticLayout: true,
  autoClosingQuotes: 'never' as editor.EditorAutoClosingStrategy,
}

export const BaseEditor: React.FC<BaseEditorProps> = (props) => {
  const {
    model,
    // @ts-ignore
    options,
    className,
    onExecuteSelectedText,
    onChangeModelContent,
    onChangeCursorPosition,
    handleChangeScrollTop,
    onCommandExplain,
    getViewTableStructure
  } = props

  const dispatch = useDispatch();
  const [transfer, setTransfer] = useState<boolean>(true)
  const { editorInstance, containerRef } = useContext(EditorContext)
  const { Execute,Hint, Explain,OpenAndEditData, splitSqlStatement } = useSelector(
    (state) => state.setting.hotKeys,
  )
  const keys = useSelector((state) => state.setting.hotKeys)
  const activePaneInfo = useSelector(activePaneInfoSelector)

  const execPlanMonaco = useMemo(() => {
    return keyStringSplitWithMultiKey(Explain as string) || undefined
  }, [Explain])
  const executeMonaco = useMemo(() => {
    return keyStringSplitWithMultiKey(Execute as string) || undefined
  }, [Execute])
  
  const OpenAndEditDataMonaco = useMemo(() => {
    return keyStringSplitWithMultiKey(OpenAndEditData as string) || undefined
  }, [OpenAndEditData])

  const promptMonaco = keyStringSplitWithMultiKey(Hint as string) || undefined;
  const splitMonaco = keyStringSplitWithMultiKey(splitSqlStatement as string)

  const dataSourceList = useSelector((state) => state.dataSource.dataSourceList)
  const dataSourceImplementedExplain = useMemo(
    () =>
      dataSourceList
        .filter(({ editorToolbarButtons }) =>
          editorToolbarButtons.includes('EXPLANATION'),
        )
        .map(({ dataSourceName }) => dataSourceName),
    [dataSourceList],
  )
  const { plSql, connectionType } = useSelector(activePaneInfoSelector) || {}
  const supportExplanation = connectionType && dataSourceImplementedExplain.includes(connectionType)

  // 实时监听滚动条位置
  useEffect(() => {
    if (!editorInstance) return
    const eventDisposer = editorInstance?.onDidScrollChange((e) => handleChangeScrollTop?.(e))
    return () => {
      eventDisposer.dispose()
    }
  }, [editorInstance])

  useEffect(() => {
    // editor 当前 model 内容变化事件
    if (!editorInstance) return
    const eventDisposer = editorInstance.onDidChangeModelContent(debounce((event) => {
      onChangeModelContent?.(editorInstance, event)
      handleLanguageChange()
    },1000))
    return () => {
      eventDisposer.dispose()
    }
  }, [editorInstance, onChangeModelContent])

  useEffect(() => {
    if (!editorInstance) return
    const eventDisposer = editorInstance.onDidChangeCursorPosition(async (event) => {
      onChangeCursorPosition && onChangeCursorPosition(event.position)  
    })
    return () => {
      eventDisposer.dispose()
    }
  }, [editorInstance, onChangeCursorPosition])

  const onAddPanel = () => {
    dispatch(addPane({}));
  }

  //快捷方式改变
  useEffect(() => {
    if (!editorInstance) return
    editCommand({editorInstance, keys, onAddPanel, OpenAndEditDataMonaco})

  }, [editorInstance, JSON.stringify(keys), OpenAndEditDataMonaco])

  useEffect(() => {
    // action 转大写
    if (!editorInstance) return
    const actionDisposer = editorInstance.addAction({
      id: 'custom.edit.uppercase',
      label: i18n.t("caseConversion"),
      contextMenuGroupId: 'edit',
      contextMenuOrder: 1,
      run: (editor) => {
        const selection = editor?.getSelection()
        const isSelected = !selection?.isEmpty()
        // 判断是否选中语句，若未选中语句则所有内容执行转换
        if (!isSelected) {
          let transferStr: string = ''
          if (transfer) {
            transferStr = editor.getModel()?.getValue()?.toUpperCase() ?? ''
          } else {
            transferStr = editor.getModel()?.getValue().toLowerCase() ?? ''
          }
          const model = editor?.getModel()
          const fullRange = model?.getFullModelRange(); // 获取整个文本范围
          // 此处不可以使用setValue，因为setValue会清空之前的撤销堆栈，导致无法回退
          if (fullRange) {
            // 添加撤销点，不然可能和之前操作合并为一个撤掉单元
            editor.pushUndoStop();
            editor.executeEdits("toggle-case", [
              {
                range: fullRange,
                text: transferStr,
                forceMoveMarkers: true
              }
            ]);
            editor.pushUndoStop();
          }
          setTransfer(!transfer)
        }
        // 选中语句则对选中内容进行转换
        else {
          const { startColumn = 1, endColumn = 1, startLineNumber = 1, endLineNumber = 1 } = selection || {}
          const range = new monaco.Range(
            startLineNumber,
            startColumn,
            endLineNumber,
            endColumn
          );
          const str = editor.getModel()?.getValueInRange(range) ?? ''
          // 判断所选中的内容是否全是大写字母，如果不是则默认执行转换大写操作
          const transfer = allLettersUpperCase(str)
          let exchangeStr: string = transfer ? str?.toLowerCase() : str?.toUpperCase()
          // 添加撤销点
          editor.pushUndoStop();
          editor.executeEdits("toggle-case-selection", [
            {
              // 这里不可以强制变更文本标记，会把选中清除
              forceMoveMarkers: false,
              range: range,
              text: exchangeStr
            }
          ]);
          editor.pushUndoStop();
        }
      },
    })
    return () => actionDisposer.dispose()
  }, [editorInstance, transfer])

  // 格式化：如果有选中文本就格式化选中文本，不然就是格式化全文
  useEffect(() => {
    if (!editorInstance) return
    const actionDisposer = editorInstance.addAction({
      id: 'editor.action.formatDocument',
      label: i18n.t("format"),
      contextMenuGroupId: 'formatDocument',
      contextMenuOrder: 1,
      run: (editorParam: any) => {
        const model = editorParam?.getModel()
        // 获取整个文本范围
        const fullRange = model?.getFullModelRange(); 
        // 获取选中文本范围
        const selection = editorInstance?.getSelection()

        let value = model?.getValue() ?? ''
        let updateRange: any = fullRange
        // 如果有选中文本，就格式化选中文本
        if(selection && !selection?.isEmpty()){
          updateRange = selection
          value = model?.getValueInRange(selection) ?? ''
        }

        // 计算一行能显示的字符数
        const layoutInfo = editorInstance.getLayoutInfo();
        const editorWidth = layoutInfo.width; // 编辑器的宽度
        const fontSize = editorInstance.getOption(editor.EditorOption.fontSize);  // 字体大小
        // 使用一个字符的宽度来估算每行可输入的字符数（一个字符的宽度大约是字体大小的0.6倍）
        const characterWidth = fontSize * 0.6;
        const charactersPerLine = Math.floor(editorWidth / characterWidth);

        // 选择数据源类型格式化
        let language: typeof formatOptionalLangs[number]
        switch (connectionType) {
          case 'Oracle':
          case 'OracleCDB':
          case 'DamengDB':
          case 'OceanBase':
          case 'KingBaseOracle':
          case 'GBase':
            language = 'plsql'
            break
          case 'SQLServer':
            language = 'tsql'
            break
          case 'ClickHouse':
          case 'OceanBaseMySQL':
          case 'TiDB':
          case 'RDSMySQL':
          case 'StarRocks':
          case 'TDSQL':
          case 'GBase8a':
          case 'GoldenDB':
            language = 'mysql'
            break
          case 'MogDB':
          case 'GaussDB':
          case 'OpenGauss':
          case 'GaussDB_DWS':
          case 'HighGo':
          case 'Greenplum':
          case 'TDSQLPG':
          case 'PolarDB':
          case 'KingBasePG':
          case 'GBase8c':
            language = 'postgresql'
            break
          case 'Inceptor':
          case 'Impala':
            language = 'hive'
            break
          case 'Presto':
            language = 'presto'
            break
          case 'Trino':
            language = 'trino'
            break
          default:
            language =
              formatOptionalLangs.find(
                (lang: string) => lang === connectionType?.toLowerCase(),
              ) || 'sql'
            break
        }
        const formated = format(value, { language, propertyAccessWidth: charactersPerLine })

        if (updateRange) {
          // 添加撤销点，不然可能和之前操作合并为一个撤掉单元
          editorParam.pushUndoStop();
          // 执行编辑操作，此处不可以使用setValue，因为setValue会清空之前的撤销堆栈
          editorParam.executeEdits("custom-format", [
            {
              range: updateRange,
              text: formated,
              forceMoveMarkers: true
            }
          ]);
          editorParam.pushUndoStop();
        }
      },
    })
    return () => actionDisposer.dispose()
  }, [connectionType, editorInstance])

  useEffect(() => {
    // action 复制
    if (!editorInstance) return
    // const copyMessage = function (e: ClipboardEvent) {
    //   message.success('复制成功')
    // }
    // 此处只对复制的事件监听后提示
    // document.addEventListener('copy', copyMessage);
    // 使用keybindings 后 会导致默认的快捷键 复制的内容不对 所以将提示拆分 监听copy 方法 来提示 
    // 下面的逻辑保持不变 取消掉对复制快捷键的覆盖
    const actionDisposer = editorInstance.addAction({
      id: 'custom.edit.copy',
      label: i18n.t("copy"),
      //keybindings: [KeyMod.CtrlCmd | KeyCode.KEY_C],
      contextMenuGroupId: 'edit',
      contextMenuOrder: 1,
      run: (editor) => {
        editor.getAction('editor.action.clipboardCopyWithSyntaxHighlightingAction').run()
        message.success(i18n.t("copySuccess"));
      },
    })
    return () => {
      actionDisposer.dispose();
      // document.removeEventListener('copy', copyMessage)
    }
  }, [editorInstance])

  useEffect(() => {
    // action 执行(选中)语句
    if (!editorInstance) return
    const actionDisposer = editorInstance.addAction({
      id: 'custom.execute.executeSelectedText',
      label: i18n.t("executeSelectedStatement"),
      keybindings: executeMonaco,
      contextMenuGroupId: 'execute',
      contextMenuOrder: 2,
      run: () => {
        // 执行语句
        onExecuteSelectedText?.()
      },
    })
    return () => actionDisposer.dispose()
  }, [editorInstance, onExecuteSelectedText, executeMonaco])

  useEffect(() => {
    const actionDisposer = editorInstance?.addAction({
      id: 'custom.lsp.autoCompletion',
      label: i18n.t("executeSelectedStatement"),
      keybindings: promptMonaco,
      keybindingContext:
        'editorTextFocus && !editorHasSelection && ' +
        '!editorHasMultipleSelections && !editorTabMovesFocus && ' +
        '!hasQuickSuggest',
      run: (editor) => {
        // 执行语句
        editor.trigger('', 'editor.action.triggerSuggest', '')
      },
    })
    return () => actionDisposer?.dispose()
  }, [editorInstance, promptMonaco])

  useEffect(() => {
    // action 执行计划
    if (!supportExplanation || plSql) return
    const disposer = editorInstance?.addAction({
      id: 'custom.execute.explainSelectedText',
      label: i18n.t("executePlan"),
      keybindings: execPlanMonaco,
      contextMenuGroupId: 'execute',
      contextMenuOrder: 3,
      run: () => {
        // 解释语句
        onCommandExplain?.()
      },
    })
    return () => disposer?.dispose()
  }, [
    editorInstance,
    onCommandExplain,
    plSql,
    supportExplanation,
    execPlanMonaco,
  ])

  useEffect(() => {
    if (options && editorInstance) {
      // 更新 instance options
      editorInstance.updateOptions({ ...defaultOptions, ...options })
    }
  }, [editorInstance, options])

  useEffect(() => {
    // 组件 props 没有 model 时, 使用实例化 editor 时的 model
    if (model == null || !editorInstance) return
    // 组件 props 传入了 model 时, 设置 model(ITextModel or null)
    editorInstance.setModel(model)
  }, [editorInstance, model])

  const handleSqlSplit = async(connectionType: string, value: string, cursorPosition: IPosition) => {
    if(!editorInstance) return
    
    try {
      const params = {
        dataSourceType: connectionType,
        line: cursorPosition?.lineNumber ?? 1,  // 编辑器右下角那个行号
        column: cursorPosition?.column ?? 1,    // 编辑器右下角那个列号
        text: value
      }
      const res = await splitSql(params)
      const position = res?.position || {}
      const range = new monaco.Range(
        position?.startLine,
        position?.startCol,
        position?.stopLine,
        position?.stopCol
      );
      editorInstance.setSelection(range);
    } catch (error) {
      console.log(error)
    }
  }

  const eventDisposableRef = useRef<monaco.IDisposable | null>(null);

  // 分句选择
  useEffect(() => {
    if (!editorInstance || !connectionType) return
    // 添加按住Ctrl加鼠标单击触发分句事件
    const onMouseDown = (e: monaco.editor.IEditorMouseEvent)  => {
      if (!e.event.ctrlKey) {
        return;
      }
      if ([monaco.editor.MouseTargetType.CONTENT_EMPTY, monaco.editor.MouseTargetType.CONTENT_TEXT].includes(e.target.type)) {
        const position: IPosition = e.target.position as IPosition;
        const cursorPosition = {
          lineNumber: position?.lineNumber,
          column: position?.column
        }
        handleSqlSplit(connectionType, editorInstance.getValue(), cursorPosition)
      }
    };

    const disposer = editorInstance.addAction({
      id: 'custom.execute.splitSelectedText',
      label: i18n.t("splitStatement"),
      keybindings: splitMonaco,
      contextMenuGroupId: 'navigation',
      contextMenuOrder: 4,
      run: (ed: any) => {
        const cursorPosition = activePaneInfo?.cursorPosition
        if(!cursorPosition) return
        handleSqlSplit(connectionType, ed.getValue(), cursorPosition)
      },
    })

    // 如果已经有绑定的事件监听器，则先清理
    if (eventDisposableRef.current) {
      eventDisposableRef.current.dispose();
    }

    // 绑定新的事件监听器
    eventDisposableRef.current = editorInstance.onMouseDown(onMouseDown);

    return () => {
      if (eventDisposableRef.current) {
        eventDisposableRef.current.dispose();
      }
      disposer?.dispose()
    } 
  }, [connectionType, editorInstance, splitMonaco])

  //查看表结构
  useEffect(() => {
    if (!editorInstance) return
    const actionDisposer = editorInstance.addAction({
      id: 'custom.execute.viewTableStructure',
      label: i18n.t("viewDefinition"),
      contextMenuGroupId: 'viewTableStructure',
      contextMenuOrder: 5,
      run: () => {
        // 查看表结构
       getViewTableStructure?.();
      },
    })
    return () => {
      actionDisposer?.dispose();
    }
  }, [editorInstance])

  // 编辑器sql类型改变,修改注释语法
  useEffect(() => {
    handleLanguageChange()
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [connectionType])
  
  const handleLanguageChange = () => {
    if(!connectionType) return
    const dealConnectionType = ["MySQL","GoldenDB","MariaDB","OceanbaseMySQL","TDSQL","TiDB","TeleDB"]
    // 需要特殊处理的数据库类型
    const needDeal = dealConnectionType.includes(connectionType)
    const modelLanguageId = getModelLanguageId(connectionType)
    const CUSTOM_LSP_LANGUAGE_CONFIG = needDeal ? 
      {
        ...LSP_LANGUAGE,
        tokenizer:{
          ...LSP_LANGUAGE.tokenizer,
          root: [
            // 匹配单行注释,这个配置要放在前面,优先级是前面的大于后面 
            [
              /#.*/,  
              {
                token: 'comment',
                bracket: '@open',
              },
            ],
            ...LSP_LANGUAGE.tokenizer.root,
          ],
          comments: [
            [/-- +.*/, 'comment'],
            [/\/\*/, { token: 'comment.quote', next: '@comment' }]
          ],
        }
      } 
      : LSP_LANGUAGE
    monaco.languages.setMonarchTokensProvider(modelLanguageId, CUSTOM_LSP_LANGUAGE_CONFIG)
  }

  return <div className={className} ref={containerRef}></div>
}
