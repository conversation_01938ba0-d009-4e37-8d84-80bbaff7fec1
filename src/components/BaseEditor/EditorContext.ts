import React, { RefCallback } from 'react'
import { editor } from 'monaco-editor'

export type IEditorContext = {
  editorInstance: editor.IStandaloneCodeEditor | null
  containerRef: RefCallback<HTMLDivElement> | null
  fileInputRef: React.RefObject<HTMLInputElement>
}

export const EditorContext = React.createContext<IEditorContext>({
  editorInstance: null,
  containerRef: null,
  fileInputRef: { current: null }, //保存文件 本地时需要用到
})
