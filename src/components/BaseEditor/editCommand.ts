import * as monaco from "monaco-editor"
import { keyStringSplitWithMultiKey } from 'src/util/hotKeys'
import { defaultMonacoHotKeys } from 'src/store/extraSlice/settingSlice';

const ACTION_COMMAND_MAPPINGS: { [k in string]: string } = {
  //注释
  Comment: 'editor.action.commentLine',
  Find: 'actions.find',
  Replace: 'editor.action.startFindReplaceAction',
}


export const editCommand = ({
  editorInstance,
  keys,
  onAddPanel,
  OpenAndEditDataMonaco
}: {
  editorInstance: monaco.editor.IStandaloneCodeEditor | null;
  keys: any
  onAddPanel: () => void
  OpenAndEditDataMonaco: number[]
}) => {

  if (!editorInstance) return

  Object.keys(ACTION_COMMAND_MAPPINGS).map(action => {
    const key = keys[action];
    if (!key) return

    if (action === 'Comment' && key?.toLowerCase() !== 'ctrl+/') {
      editorInstance.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.US_SLASH, function () {
        console.log('Ctrl + / 被禁用')
      })
    }
    if (action === 'Find' && key?.toLowerCase() !== 'ctrl+f') {
      editorInstance.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.KEY_F, function () {
        console.log('Ctrl + F 被禁用')
      })
    }
    if (action === 'Replace' && key?.toLowerCase() !== 'ctrl+h') {
      editorInstance.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.KEY_H, function () {
        console.log('Ctrl + H 被禁用')
      })
    }
    //常用 屏蔽和浏览器冲突的快捷键 比如 Ctrl+Z 撤销操作
    if(Object.keys(defaultMonacoHotKeys)?.includes(action)) {
      if ( key?.toLowerCase() === 'ctrl+z') {
        editorInstance.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.KEY_Z, function () {
          console.log('Ctrl + Z 被禁用')
        })
      }
      if (key?.toLowerCase() === 'ctrl+s') {
        editorInstance.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.KEY_S, function () {
          console.log('Ctrl + S 被禁用')
        })
      }
    }

    const code = keyStringSplitWithMultiKey(key)?.[0];
    editorInstance.addCommand(code, () => {
      editorInstance.trigger('keyboard', ACTION_COMMAND_MAPPINGS[action], null);
    });
  })

  editorInstance.addCommand(OpenAndEditDataMonaco?.[0], () => {
    onAddPanel();
  });

}