.showIcon {
  float: left;
  position: absolute;
  top: 28px;
  left: 17px;
  span > svg {
    width: 1.6em;
    height: 1.6em;
  }
}

.bdNavigationWrap {
  width: 50px;
  height: 100%;
  background-color: white;
  padding: 2px 3px 3px 0px;
  .bdNavigation {
    width: 100%;
    height: 100%;
    transition: box-shadow 0.3s ease; /* 添加过渡效果 */
    padding-top: 4px;
    .dataOperation, .task {
      display: grid;
      text-align: center;
      color: #667084;
      .icon {
        margin: auto;
        margin-top: 20px;
        width: 60%;
        padding: 8px 0px 8px;
        border-radius: 3px;
      }
      .iconLight:hover{
        color: #3262FF;
        background-color: #E5EBFF;
        cursor: pointer;
      }
      .iconDark:hover{
        color: #3262FF;
        background-color: #4c4c58;
        cursor: pointer;
      }
      .taskIcon {
        margin-top: 30px;
      }
      span > svg {
        width: 1.6em;
        height: 1.6em;
      }
    }
  }
  .bdNavigation:hover {
    box-shadow: 0 0 10px rgba(149, 149, 149, 0.5); /* 鼠标悬停时添加阴影效果 */
  }
}

.bgColorLight{
  background-color: white;
}

.bgColorDark{
  background-color: #3a3a42;
}