import React, { useCallback, useEffect, useRef, useState } from 'react'
import { Form, Input, message } from 'antd'
import { useForm } from 'antd/lib/form/Form'
import { UIModal } from 'src/components'
import { useDispatch, useRequest, useSelector } from 'src/hook'
import { hideModal } from 'src/store/extraSlice/modalVisibleSlice'
import { bindUserOTPKey, getUserOTPKey, refreshUserOTPKey, twoStepSMSLoginAuth } from 'src/api'
import { setIsLoggedIn } from 'src/appPages/login/loginSlice'
import * as OriginQRCode from 'src/assets/js/qrcode.js'
import { useHistory } from 'react-router-dom'
import i18n from 'i18next';
const QRCode = OriginQRCode.default as any

interface Iprops {
  userId: string
}

export const BindOtpModal = (props: Iprops) => {
  const { userId } = props || {};

  const [form] = useForm()
  const history = useHistory()
  const dispatch = useDispatch()

  const qrRef = useRef() as any
  const qrDom = document.getElementById('qrcode-otp')
  const visible = useSelector((state) => state.modal['BindOtpModal']?.visible)

  const [checkCodeKey, setCheckCodeKey] = useState('')
  //初次 二维码
  const { run: getInitialCheckCodeKey } = useRequest(getUserOTPKey, {
    manual: true,
    formatResult: ({ optKey }) => optKey,
    onSuccess: (optKey: string) => {
      setCheckCodeKey(optKey)
    },
  })
  
  // 刷新二维码
  const { run: getRefreshCheckCodeKey } = useRequest(refreshUserOTPKey, {
    manual: true,
    formatResult: ({ optKey }) => optKey,
    onSuccess: (optKey: string) => {
      setCheckCodeKey(optKey)
    },
  })

  // 绑定otpKey
  const { run: bindOtpKey, loading } = useRequest(bindUserOTPKey, {
    manual: true,
  })

  useEffect(() => {
    if (visible) {
      getInitialCheckCodeKey(userId);
    }
  }, [visible])

  useEffect(() => {
    if (visible) {
      createQR();
    }
  }, [checkCodeKey])

  const createQR = useCallback(() => {
    if (qrRef.current) {
      qrRef.current?.clear()
    }
    if (qrDom) {
      qrDom.innerHTML = ''
    }

    if (!qrDom) return
    let uri = 'otpauth://totp/'
    uri += encodeURIComponent(userId ? `${userId}-otp` : '')
    const code = checkCodeKey?.slice(0, 32)
    uri += '?secret=' + code
    uri += '&algorithm=SHA1'
    uri += '&digits=6'
    uri += '&period=30'

    let qrImg = new QRCode(qrDom, {
      text: uri,
      width: 200,
      height: 200,
      correctLevel: QRCode.CorrectLevel.H,
    })
    qrRef.current = qrImg
  }, [checkCodeKey, qrDom, qrRef, userId])

  const handleSubmit = () => {
    form.validateFields().then(values => {
      const { otpPassword } = values || {};
      bindOtpKey({ otpPassword, userId }).then(res => {
        message.success(i18n.t("bindingSuccess"));
        dispatch(hideModal('BindOtpModal'));
      })
    })
  }

  const handleCancel = () => {
    dispatch(hideModal('BindOtpModal'))
    dispatch(setIsLoggedIn(false))
    history.push('/login')
  }

  return (
    <UIModal
      title={i18n.t("otpBinding")}
      visible={visible}
      onOk={handleSubmit}
      okButtonProps={{ loading }}
      onCancel={handleCancel}
      cancelText={i18n.t("exit")}
      closable={false}
      width={600}
    >
      <p>{i18n.t("useAuthenticator")}</p>
      <div
        id="qrcode-otp"
        style={{ display: 'flex', cursor: 'pointer', width: 100, height: 100, marginLeft: '40%' }}
        onClick={() => userId && getRefreshCheckCodeKey(userId)}
      ></div>
      <br />
      <Form form={form} wrapperCol={{ offset: 7, span: 10 }}>
        <Form.Item
          name="otpPassword"
          rules={[{ required: true, message: i18n.t("enterOtpVerificationCode") }]}
        >
          <Input placeholder={i18n.t("oneTimePasswordOtp")} />
        </Form.Item>
      </Form>
      <p>{i18n.t("iphoneAuthenticator")}</p>
      <p>{i18n.t("androidAuthenticator")}</p>
    </UIModal>
  )
}
