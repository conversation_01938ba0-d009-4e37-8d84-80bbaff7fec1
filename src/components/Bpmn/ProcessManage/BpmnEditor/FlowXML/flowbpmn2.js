var { t } = require('i18next');

const flowbpmn2= `<?xml version="1.0" encoding="UTF-8"?>
<bpmn2:definitions 
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xmlns:bpmn2="http://www.omg.org/spec/BPMN/20100524/MODEL"
    xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI"
    xmlns:dc="http://www.omg.org/spec/DD/20100524/DC"
    xmlns:di="http://www.omg.org/spec/DD/20100524/DI"
    xsi:schemaLocation="http://www.omg.org/spec/BPMN/20100524/MODEL BPMN20.xsd"
    id="sample-diagram"
    xmlns:activiti="http://activiti.org/bpmn"
    targetNamespace="http://activiti.org/bpmn">
  <bpmn2:process id="Process_1" name="" isExecutable="true">
    <bpmn2:startEvent id="Event_169bnhb">
      <bpmn2:outgoing>Flow_07wy3fh</bpmn2:outgoing>
    </bpmn2:startEvent>
    <bpmn2:userTask id="Activity_0gakuvf" name=${t("bpmn:flowbpmn2.initiate_approval")}>
      <bpmn2:incoming>Flow_07wy3fh</bpmn2:incoming>
      <bpmn2:outgoing>Flow_1ib642y</bpmn2:outgoing>
    </bpmn2:userTask>
    <bpmn2:sequenceFlow id="Flow_07wy3fh" sourceRef="Event_169bnhb" targetRef="Activity_0gakuvf" />
    <bpmn2:userTask id="Activity_1xc84ev" name=${t("bpmn:flowbpmn2.level_1_approval")}>
      <bpmn2:incoming>Flow_1ib642y</bpmn2:incoming>
      <bpmn2:outgoing>Flow_1weypsm</bpmn2:outgoing>
      <bpmn2:multiInstanceLoopCharacteristics />
    </bpmn2:userTask>
    <bpmn2:sequenceFlow id="Flow_1ib642y" sourceRef="Activity_0gakuvf" targetRef="Activity_1xc84ev" />
    <bpmn2:userTask id="Activity_1ek72qd" name=${t("bpmn:flowbpmn2.level_2_approval")}>
      <bpmn2:incoming>Flow_09onhhs</bpmn2:incoming>
      <bpmn2:outgoing>Flow_0x9yti2</bpmn2:outgoing>
    </bpmn2:userTask>
    <bpmn2:intermediateThrowEvent id="Event_19uyzsd">
      <bpmn2:incoming>Flow_0x9yti2</bpmn2:incoming>
    </bpmn2:intermediateThrowEvent>
    <bpmn2:sequenceFlow id="Flow_0x9yti2" sourceRef="Activity_1ek72qd" targetRef="Event_19uyzsd" />
    <bpmn2:exclusiveGateway id="Gateway_0edym30">
      <bpmn2:incoming>Flow_1weypsm</bpmn2:incoming>
      <bpmn2:outgoing>Flow_09onhhs</bpmn2:outgoing>
    </bpmn2:exclusiveGateway>
    <bpmn2:sequenceFlow id="Flow_1weypsm" sourceRef="Activity_1xc84ev" targetRef="Gateway_0edym30" />
    <bpmn2:sequenceFlow id="Flow_09onhhs" sourceRef="Gateway_0edym30" targetRef="Activity_1ek72qd" />
  </bpmn2:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="Process_1">
      <bpmndi:BPMNEdge id="Flow_07wy3fh_di" bpmnElement="Flow_07wy3fh">
        <di:waypoint x="248" y="250" />
        <di:waypoint x="300" y="250" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1ib642y_di" bpmnElement="Flow_1ib642y">
        <di:waypoint x="400" y="250" />
        <di:waypoint x="460" y="250" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0x9yti2_di" bpmnElement="Flow_0x9yti2">
        <di:waypoint x="890" y="250" />
        <di:waypoint x="1070" y="250" />
        <di:waypoint x="1070" y="282" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1weypsm_di" bpmnElement="Flow_1weypsm">
        <di:waypoint x="560" y="250" />
        <di:waypoint x="645" y="250" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_09onhhs_di" bpmnElement="Flow_09onhhs">
        <di:waypoint x="695" y="250" />
        <di:waypoint x="790" y="250" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="Event_169bnhb_di" bpmnElement="Event_169bnhb">
        <dc:Bounds x="212" y="232" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0gakuvf_di" bpmnElement="Activity_0gakuvf">
        <dc:Bounds x="300" y="210" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1xc84ev_di" bpmnElement="Activity_1xc84ev">
        <dc:Bounds x="460" y="210" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_19uyzsd_di" bpmnElement="Event_19uyzsd">
        <dc:Bounds x="1052" y="282" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0edym30_di" bpmnElement="Gateway_0edym30" isMarkerVisible="true">
        <dc:Bounds x="645" y="225" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1ek72qd_di" bpmnElement="Activity_1ek72qd">
        <dc:Bounds x="790" y="210" width="100" height="80" />
      </bpmndi:BPMNShape>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn2:definitions>
`
export default flowbpmn2