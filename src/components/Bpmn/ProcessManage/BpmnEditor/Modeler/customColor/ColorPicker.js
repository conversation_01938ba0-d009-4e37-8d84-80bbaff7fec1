import {is} from 'bpmn-js/lib/util/ModelUtil';
import { t } from 'i18next'
/**
 * A handler updating an elements color.
 */
function UpdateColorHandler() {
    this.execute = context => {
        context.oldColor = context.element.color;
        context.element.color = context.color;

        return context.element;
    };

    this.revert = context => {
        context.element.color = context.oldColor;

        return context.element;
    };
}

/**
 * A basic color picker implementation.
 *
 * @param {EventBus} eventBus
 * @param {ContextPad} contextPad
 * @param {CommandStack} commandStack
 */
export default function ColorPicker(eventBus, contextPad, commandStack) {
    contextPad.registerProvider(this);

    commandStack.registerHandler('shape.updateColor', UpdateColorHandler);

    function changeColor(event, element) {
        const color = window.prompt(t("bpmn:color_pop.enter_color_value"));
        commandStack.execute('shape.updateColor', {element, color});
    }

    this.getContextPadEntries = element => {
        if (is(element, 'bpmn:Event')) {
            return {
                changeColor: {
                    group: 'edit',
                    className: 'icon-red',
                    title: 'Change element color',
                    action: {
                        click: changeColor,
                    },
                },
            };
        }
    };
}

ColorPicker.$inject = ['eventBus', 'contextPad', 'commandStack'];
