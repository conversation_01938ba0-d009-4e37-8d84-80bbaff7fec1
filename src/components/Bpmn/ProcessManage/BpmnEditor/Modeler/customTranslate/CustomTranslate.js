import translations_zh from './TranslationsGerman_zh';  // 中文翻译文件
import translations_en from './TranslationsGerman_en';  // 英文翻译文件
import { CURRENT_LANGUAGE } from 'src/util/getCurrentLanguage'

// 设置默认语言
let currentTranslations = CURRENT_LANGUAGE === 'en' ? translations_en : translations_zh;

export default function customTranslate(template, replacements) {
    replacements = replacements || {};

    // Translate 
    template = currentTranslations[template] || template;

    // Replace 
    return template.replace(/{([^}]+)}/g, (_, key) => {
        return replacements[key] || `{${key}}`;
    });
}

// 根据当前语言变更翻译文件
export function setLanguage(lng) {
    
    if (lng === 'en') {
        currentTranslations = translations_en;
    } else if (lng === 'zh') {
        currentTranslations = translations_zh;
    } else {
        console.warn(`Unsupported language: ${lng}. Defaulting to Chinese.`);
        currentTranslations = translations_zh;
    }
}
