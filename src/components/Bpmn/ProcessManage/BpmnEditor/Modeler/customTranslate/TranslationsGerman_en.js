// eslint-disable-next-line import/no-anonymous-default-export
export default {
    // Labels
    'Activate the global connect tool': 'Activate the global connect tool',
    'Append': 'Append',
    'Append {type}': 'Append {type}',
    'Append EndEvent': 'Append End Event',
    'Append Task': 'Append Task',
    'Append Gateway': 'Append Gateway',
    'Append Intermediate/Boundary Event': 'Append Intermediate/Boundary Event',
    'Add Lane above': 'Add Lane above',
    'Divide into two Lanes': 'Divide into two Lanes',
    'Divide into three Lanes': 'Divide into three Lanes',
    'Add Lane below': 'Add Lane below',
    'Append compensation activity': 'Append compensation activity',
    'Change type': 'Change type',
    'Connect using Association': 'Connect using Association',
    'Connect using Sequence/MessageFlow or Association': 'Connect using Sequence/MessageFlow or Association',
    'Connect using DataInputAssociation': 'Connect using DataInputAssociation',
    'Remove': 'Remove',
    'Activate the hand tool': 'Activate the hand tool',
    'Activate the lasso tool': 'Activate the lasso tool',
    'Activate the create/remove space tool': 'Activate the create/remove space tool',
    'Create expanded SubProcess': 'Create expanded SubProcess',
    'Create IntermediateThrowEvent/BoundaryEvent': 'Create IntermediateThrowEvent/BoundaryEvent',
    'Create Pool/Participant': 'Create Pool/Participant',
    'Parallel Multi Instance': 'Parallel Multi Instance',
    'Sequential Multi Instance': 'Sequential Multi Instance',
    'DataObjectReference': 'DataObjectReference',
    'DataStoreReference': 'DataStoreReference',
    'Loop': 'Loop',
    'Ad-hoc': 'Ad-hoc',
    'Create {type}': 'Create {type}',
    'Create Group': 'Create Group',
    'Task': 'Task',
    'Process': 'Process',
    'UserTask': 'UserTask',
    'Send Task': 'Send Task',
    'Receive Task': 'Receive Task',
    'User Task': 'User Task',
    'Manual Task': 'Manual Task',
    'Business Rule Task': 'Business Rule Task',
    'Service Task': 'Service Task',
    'Script Task': 'Script Task',
    'Call Activity': 'Call Activity',
    'CallActivity': 'CallActivity',
    'Sub Process (collapsed)': 'Sub Process (collapsed)',
    'Sub Process (expanded)': 'Sub Process (expanded)',
    'Start Event': 'Start Event',
    'StartEvent': 'Start Event',
    'Intermediate Throw Event': 'Intermediate Throw Event',
    'End Event': 'End Event',
    'EndEvent': 'EndEvent',
    'Create Gateway': 'Create Gateway',
    'GateWay': 'Gateway',
    'Create Intermediate/Boundary Event': 'Create Intermediate/Boundary Event',
    'Message Start Event': 'Message Start Event',
    'Timer Start Event': 'Timer Start Event',
    'Conditional Start Event': 'Conditional Start Event',
    'Signal Start Event': 'Signal Start Event',
    'Error Start Event': 'Error Start Event',
    'Escalation Start Event': 'Escalation Start Event',
    'Compensation Start Event': 'Compensation Start Event',
    'Message Start Event (non-interrupting)': 'Message Start Event (non-interrupting)',
    'Timer Start Event (non-interrupting)': 'Timer Start Event (non-interrupting)',
    'Conditional Start Event (non-interrupting)': 'Conditional Start Event (non-interrupting)',
    'Signal Start Event (non-interrupting)': 'Signal Start Event (non-interrupting)',
    'Escalation Start Event (non-interrupting)': 'Escalation Start Event (non-interrupting)',
    'Message Intermediate Catch Event': 'Message Intermediate Catch Event',
    'Message Intermediate Throw Event': 'Message Intermediate Throw Event',
    'Timer Intermediate Catch Event': 'Timer Intermediate Catch Event',
    'Escalation Intermediate Throw Event': 'Escalation Intermediate Throw Event',
    'Conditional Intermediate Catch Event': 'Conditional Intermediate Catch Event',
    'Link Intermediate Catch Event': 'Link Intermediate Catch Event',
    'Link Intermediate Throw Event': 'Link Intermediate Throw Event',
    'Compensation Intermediate Throw Event': 'Compensation Intermediate Throw Event',
    'Signal Intermediate Catch Event': 'Signal Intermediate Catch Event',
    'Signal Intermediate Throw Event': 'Signal Intermediate Throw Event',
    'Message End Event': 'Message End Event',
    'Escalation End Event': 'Escalation End Event',
    'Error End Event': 'Error End Event',
    'Cancel End Event': 'Cancel End Event',
    'Compensation End Event': 'Compensation End Event',
    'Signal End Event': 'Signal End Event',
    'Terminate End Event': 'Terminate End Event',
    'Message Boundary Event': 'Message Boundary Event',
    'Message Boundary Event (non-interrupting)': 'Message Boundary Event (non-interrupting)',
    'Timer Boundary Event': 'Timer Boundary Event',
    'Timer Boundary Event (non-interrupting)': 'Timer Boundary Event (non-interrupting)',
    'Escalation Boundary Event': 'Escalation Boundary Event',
    'Escalation Boundary Event (non-interrupting)': 'Escalation Boundary Event (non-interrupting)',
    'Conditional Boundary Event': 'Conditional Boundary Event',
    'Conditional Boundary Event (non-interrupting)': 'Conditional Boundary Event (non-interrupting)',
    'Error Boundary Event': 'Error Boundary Event',
    'Cancel Boundary Event': 'Cancel Boundary Event',
    'Signal Boundary Event': 'Signal Boundary Event',
    'Signal Boundary Event (non-interrupting)': 'Signal Boundary Event (non-interrupting)',
    'Compensation Boundary Event': 'Compensation Boundary Event',
    'Exclusive Gateway': 'Exclusive Gateway',
    'ExclusiveGateway': 'Exclusive Gateway',
    'Parallel Gateway': 'Parallel Gateway',
    'ParallelGateway': 'Parallel Gateway',
    'Inclusive Gateway': 'Inclusive Gateway',
    'InclusiveGateway': 'Inclusive Gateway',
    'Complex Gateway': 'Complex Gateway',
    'Event based Gateway': 'Event-based Gateway',
    'Transaction': 'Transaction',
    'Sub Process': 'Sub Process',
    'Event Sub Process': 'Event Sub Process',
    'Collapsed Pool': 'Collapsed Pool',
    'Expanded Pool': 'Expanded Pool',
    // Errors
    'no parent for {element} in {parent}': 'No parent for {element} in {parent}',
    'no shape type specified': 'No shape type specified',
    'flow elements must be children of pools/participants': 'Flow elements must be children of pools/participants',
    'out of bounds release': 'Out of bounds release',
    'more than {count} child lanes': 'More than {count} child lanes',
    'element required': 'Element required',
    'diagram not part of bpmn:Definitions': 'Diagram not part of bpmn:Definitions',
    'no diagram to display': 'No diagram to display',
    'no process or collaboration to display': 'No process or collaboration to display',
    'element {element} referenced by {referenced}#{property} not yet drawn': 'Element {element} referenced by {referenced}#{property} not yet drawn',
    'already rendered {element}': '{element} already rendered',
    'failed to import {element}': 'Failed to import {element}',
    // 属性面板的参数
    'Id': 'Id',
    'Name': 'Name',
    'General': 'General',
    'Details': 'Details',
    'Message Name': 'Message Name',
    'Message': 'Message',
    'Initiator': 'Initiator',
    'Asynchronous Continuations': 'Asynchronous Continuations',
    'Asynchronous Before': 'Asynchronous Before',
    'Asynchronous After': 'Asynchronous After',
    'Job Configuration': 'Job Configuration',
    'Exclusive': 'Exclusive',
    'Job Priority': 'Job Priority',
    'Retry Time Cycle': 'Retry Time Cycle',
    'Documentation': 'Documentation',
    'Element Documentation': 'Element Documentation',
    'History Configuration': 'History Configuration',
    'History Time To Live': 'History Time To Live',
    'Start Time': 'Start Time',
    'continuedTime': 'Continued Time',
    'create class': 'Task Create Listener Class',
    'Listener Id': 'Listener Id',
    'create_delegateExpression': 'Task Create Listener Delegate Expression',
    'assignmentClass': 'Assignment Listener Class',
    'assignmentDelegateExpression': 'Assignment Listener Delegate Expression',
    'completeClassEntry': 'Complete Listener Class',
    'completeDelegateExpression': 'Complete Listener Delegate Expression',
    'deleteClass': 'Delete Listener Class',
    'deleteDelegateExpression': 'Delete Listener Delegate Expression',
    'Execution Class': 'Execution Class',
    'expression': 'Expression',
    'delegate Expression': 'Delegate Expression',
    'approvalBtn': 'Approval Opinion Type',
    'assignee List': 'Assignee List',
    'node Type': 'Node Type',
    'transConditions': 'Auto to Manual Transition Conditions',
    'loopTime': 'Loop Time',
    'start Configuration': 'Start Configuration',
    'end Configuration': 'End Configuration',
    'service Task': 'Service Task Configuration',
    'Forms': 'Forms',
    'Form Key': 'Form Key',
    'Form Fields': 'Form Fields',
    'Business Key': 'Business Key',
    'Form Field': 'Form Field',
    'ID': 'Id',
    'Type': 'Type',
    'Label': 'Label',
    'Default Value': 'Default Value',
    'Validation': 'Validation',
    'Add Constraint': 'Add Constraint',
    'Config': 'Config',
    'Properties': 'Properties',
    'Add Property': 'Add Property',
    'Value': 'Value',
    'Add': 'Add',
    'Values': 'Values',
    'Add Value': 'Add Value',
    'Listeners': 'Listeners',
    'Execution Listener': 'Execution Listener',
    'Event Type': 'Event Type',
    'Listener Type': 'Listener Type',
    'Java Class': 'Java Class',
    'Expression': 'Expression',
    'Must provide a value': 'Must provide a value',
    'Delegate Expression': 'Delegate Expression',
    'Script': 'Script',
    'Script Format': 'Script Format',
    'Script Type': 'Script Type',
    'Inline Script': 'Inline Script',
    'External Script': 'External Script',
    'External Resource': 'External Resource',
    'Resource': 'Resource',
    'Field Injection': 'Field Injection',
    'Extensions': 'Extensions',
    'Input/Output': 'Input/Output',
    'event Configure': 'Event Configuration',
    'Input Parameters': 'Input Parameters',
    'Output Parameters': 'Output Parameters',
    'Parameters': 'Parameters',
    'Output Parameter': 'Output Parameter',
    'Timer Definition Type': 'Timer Definition Type',
    'Timer Definition': 'Timer Definition',
    'Date': 'Date',
    'Duration': 'Duration',
    'Cycle': 'Cycle',
    'Signal': 'Signal',
    'Signal Name': 'Signal Name',
    'Escalation': 'Escalation',
    'Error': 'Error',
    'Link Name': 'Link Name',
    'Condition': 'Condition Name',
    'Variable Name': 'Variable Name',
    'Variable Event': 'Variable Event',
    'Specify more than one variable change event as a comma separated list.': 'Specify multiple variable change events separated by commas.',
    'Wait for Completion': 'Wait for Completion',
    'Activity Ref': 'Activity Reference',
    'Version Tag': 'Version Tag',
    'end configure': 'End Configuration',
    'Executable': 'Executable',
    'External Task Configuration': 'External Task Configuration',
    'Task Priority': 'Task Priority',
    'External': 'External',
    'Connector': 'Connector',
    'Must configure Connector': 'Connector must be configured',
    'Connector Id': 'Connector Id',
    'Implementation': 'Implementation',
    'Field Injections': 'Field Injections',
    'Fields': 'Fields',
    'Result Variable': 'Result Variable',
    'Topic': 'Topic',
    'Configure Connector': 'Configure Connector',
    'Input Parameter': 'Input Parameter',
    'Assignee': 'Assignee',
    'Candidate Users': 'Candidate Users',
    'Candidate Groups': 'Candidate Groups (positions)',
    'Due Date': 'Due Date',
    'Follow Up Date': 'Follow Up Date',
    'Priority': 'Priority',
    'The follow up date as an EL expression (e.g. ${someDate} or an ISO date (e.g. 2015-06-26T09:54:00)': 'The follow-up date must conform to an EL expression, e.g., ${someDate} or an ISO date (e.g., 2015-06-26T09:54:00)',
    'The due date as an EL expression (e.g. ${someDate} or an ISO date (e.g. 2015-06-26T09:54:00)': 'The due date must conform to an EL expression, e.g., ${someDate} or an ISO date (e.g., 2015-06-26T09:54:00)',
    'Variables': 'Variables',
    'Candidate Starter Configuration': 'Candidate Start Configuration',
    'Task Listener': 'Task Listener',
    'Candidate Starter Groups': 'Candidate Starter Groups',
    'Candidate Starter Users': 'Candidate Starter Users',
    'Tasklist Configuration': 'Tasklist Configuration',
    'Startable': 'Startable',
    'Specify more than one group as a comma separated list.': 'Specify multiple groups separated by commas.',
    'Specify more than one user as a comma separated list.': 'Specify multiple users separated by commas.',
    'Specify more than one user as a comma separated list..': 'Specify multiple assignees separated by commas (manual type)',
    'This maps to the process definition key.': 'This maps to the process definition key.',
    'CallActivity Type': 'Call Activity Type',
    'Create CallActivity': 'Create Call Activity',
    'Called Element': 'Called Element',
    'Create DataObjectReference': 'Create Data Object Reference',
    'Create DataStoreReference': 'Create Data Store Reference',
    'Multi Instance': 'Multi Instance',
    // 'Loop Cardinality':'实例数量',
    'Collection': 'Task Participant List',
    'Element Variable': 'Element Variable',
    'Completion Condition': 'Completion Condition',
    'Append CallActivity': 'Append Call Activity',
    'Binding': 'Binding',
    'Tenant Id': 'Tenant ID',
    'This maps to the task definition key.': 'This maps to the task definition key.',
    'Form field id must not be empty': 'Form field ID must not be empty',
    'Form field id already used in form data.': 'Form field ID already used in form data.',
    'Must provide either loop cardinality or collection': 'Must provide either loop cardinality or collection',
    'Select UserTask': 'Select a configured user task',
    'Element must have a unique id.': 'Element must have a unique ID',
    'create': 'Create',
    'assignment': 'Assignment',
    'complete': 'Complete',
    'delete': 'Delete',
    'update': 'Update',
    'timeout': 'Timeout',
    'start': 'Start',
    'end': 'End',
    'acceptor': 'Acceptor',

    'save': 'Save',
    'Tools': 'Tools',
    'FlowGateway': 'Flow Gateway',
    'ProcessControl': 'Process Control',
    'Create StartEvent': 'Create Start Event',
    'Create EndEvent': 'Create End Event',
    'Create ExclusiveGateway': 'Create Exclusive Gateway',
    'Create ParallelGateway': 'Create Parallel Gateway',
    'Create InclusiveGateway': 'Create Inclusive Gateway',
    'Create Task': 'Create Task',
    'Create UserTask': 'Create User Task',
    'Create ServiceTask': 'Create Service Task',
    'Condition Type': 'Condition Type',


    // 流程校验
    'Sequence flow is missing condition': 'Sequence flow missing condition',
    ' is missing end event': 'Missing end event',
    'Start event is missing event definition': 'Start event missing event definition',
    'Incoming flows do not join': 'Incoming flows do not merge',
    'Element is missing label/name': 'Element missing label/name',
    'Element has disallowed type': 'Element has disallowed type',
    'Element is not connected': 'Element is not connected',
    'Element is not left connected': 'Element is not left connected',
    'Element is not right connected': 'Element is not right connected',
    'SequenceFlow is a duplicate': 'SequenceFlow is a duplicate',
    'Duplicate outgoing sequence flows': 'Duplicate outgoing sequence flows',
    'Duplicate incoming sequence flows': 'Duplicate incoming sequence flows',
    'Gateway forks and joins': 'Gateway forks and joins',
    'Flow splits implicitly': 'Flow splits implicitly',
    ' has multiple blank start events': 'Has multiple blank start events',
    'Event has multiple event definitions': 'Event has multiple event definitions',
    ' is missing start event': 'Missing start event',
    'Start event must be blank': 'Start event must be blank',
    'Gateway is superfluous. It only has one source and target.': 'Gateway is superfluous. It only has one source and target.',
    '': '',

    //others
    'Multi-party rule': 'Multi-party Rule',
    'Approval type': 'Approval Type',
    'Dept List': 'Department List'
};
