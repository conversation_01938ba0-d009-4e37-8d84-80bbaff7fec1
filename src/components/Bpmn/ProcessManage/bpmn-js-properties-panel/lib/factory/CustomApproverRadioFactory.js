'use strict';
var { t } = require('i18next');

var getBusinessObject = require('bpmn-js/lib/util/ModelUtil').getBusinessObject,
    cmdHelper = require('../helper/CmdHelper'),
    escapeHTML = require('../Utils').escapeHTML;

var entryFieldDescription = require('./EntryFieldDescription');


var CustomApproverRadio = function(options, defaultParameters) {
  var resource = defaultParameters,
      id = resource.id,
      label = options.label || id,
      canBeDisabled = !!options.disabled && typeof options.disabled === 'function',
      canBeHidden = !!options.hidden && typeof options.hidden === 'function',
      description = options.description;

  resource.html =resource.html =
  '<input id="flowable-' + escapeHTML(id) + '-or-sign" ' +
       'type="radio" ' +
       'name="' + escapeHTML(options.modelProperty) + '" ' +
       'value="or-sign" ' + // 设置不同的值
       ' />' +
  '<span for="flowable-' + escapeHTML(id) + '-or-sign" ' +
       '>' + escapeHTML(t("bpmn:cta_rdo.or_sign")) + '</span>' +
  '<input id="flowable-' + escapeHTML(id) + '-specified" ' +
       'type="radio" ' +
       'name="' + escapeHTML(options.modelProperty) + '" ' +
       'value="specified" ' + // 设置不同的值
       'class="custorm-approver-specified-radio"' +
       ' />' +
  '<span for="flowable-' + escapeHTML(id) + '-specified" ' +
       '>' + escapeHTML(t("bpmn:cta_rdo.specific")) +
       '<input id="flowable-' + escapeHTML(id) + '-number" ' +
       'type="number" ' +
       'name="' + escapeHTML(options.modelProperty) + '-number" ' +
       'class="custorm-approver-number"' +
       ' />' + escapeHTML(t("bpmn:cta_rdo.approver_group"))  + '</span>' ;

  // add description below checkbox entry field
  if (description) {
    resource.html += entryFieldDescription(description);
  }

  resource.get = function(element) {
    var bo = getBusinessObject(element),
    res = {};
    res[options.modelProperty] = bo.get(options.modelProperty);

    return res;
  };

  resource.set = function(element, values) {
    var res = values;

    return cmdHelper.updateProperties(element, res);
  };

  if (typeof options.set === 'function') {
    resource.set = options.set;
  }

  if (typeof options.get === 'function') {
    resource.get = options.get;
  }

  if (canBeDisabled) {
    resource.isDisabled = function() {
      return options.disabled.apply(resource, arguments);
    };
  }

  if (canBeHidden) {
    resource.isHidden = function() {
      return !options.hidden.apply(resource, arguments);
    };
  }

  resource.cssClasses = ['bpp-approver-radio'];

  return resource;
};

module.exports = CustomApproverRadio;
