/*
 * @Author: fuz<PERSON><PERSON><PERSON>
 * @Date: 2022-11-28 20:58:33
 * @LastEditTime: 2022-12-02 14:41:11
 * @LastEditors: fuzhenghao
 * @Description:
 * @FilePath: \cq-enterprise-frontend\src\components\Bpmn\ProcessManage\bpmn-js-properties-panel\lib\provider\flowable\parts\AssigneeListProps.js
 */
'use strict'

var is = require('bpmn-js/lib/util/ModelUtil').is
var cmdHelper = require('../../../helper/CmdHelper')
var entryFactory = require('../../../factory/EntryFactory')
var { getAllUserList } = require('./commonRequest/index')
var getBusinessObject = require('bpmn-js/lib/util/ModelUtil').getBusinessObject

// module.exports = function (group, element, bpmnFactory, translate) {
//   if (!is(element, 'bpmn:UserTask')) {
//     return
//   }


//   function getAttribute(element, prop) {
//     let attr = {}
//     const bo = getBusinessObject(element)
//     var value = bo.get(prop)
//     attr[prop] = value
//     return attr
//   }

//   group.entries.push(
//     entryFactory.selectBox({
//       id: 'assignee',
//       label: translate('acceptor'),
//       selectOptions: function (element) {
//         return getAllUserList()
//       },
//       modelProperty: 'activiti:assignee',
//       multiple: 'multiple', // 加上这个方法变成多选下拉框
//       get: function (element) {
//         var attr = getAttribute(element, 'activiti:assignee')
//         return attr
//       },
//       set: function (element, values) {
//         const bo = getBusinessObject(element)
//         return cmdHelper.updateBusinessObject(element, bo, values)
//       },
//       validate: function (element, values, node) {
//         var validation = {}
//         let activitiAssignee_isHidden =
//           ensureMultiInstanceSupported(element) &&
//           getAttribute(element, 'activiti:formKey')

//         if (
//           (values['activiti:assignee'] === '${assignee}' ||
//             values['activiti:assignee'] === '') &&
//           !activitiAssignee_isHidden
//         ) {
//           validation['activiti:assignee'] = translate('Must provide a value')
//         }
//         return validation
//       },
//     }),
//   )
// }
