'use strict';
var entryFactory = require('bpmn-js-properties-panel/lib/factory/EntryFactory');
var is = require('bpmn-js/lib/util/ModelUtil').is,
  getBusinessObject = require('bpmn-js/lib/util/ModelUtil').getBusinessObject;
var { t } = require('i18next');

module.exports = function(group, element, bpmnFactory, translate) {
  var businessObject = getBusinessObject(element);
  if (is(element, 'bpmn:UserTask')) {
    const isMajor = entryFactory.selectBox({
      id: 'isMajor',
      label: translate(t("bpmn:entry_lbl.mandatory_node")),
      modelProperty: 'isMajor',
      selectOptions: [
        { name: '', value: '' },
        { name: t("bpmn:entry_opts_lbl.yes"), value: '0' },
        { name: t("bpmn:entry_opts_lbl.no"), value: '1' },
      ],
    });

    // 设置默认值
    if (!businessObject.get('isMajor')) {
      businessObject.$attrs['isMajor'] = '0';
    }

    group.entries = group.entries.concat(isMajor);
  }
};
