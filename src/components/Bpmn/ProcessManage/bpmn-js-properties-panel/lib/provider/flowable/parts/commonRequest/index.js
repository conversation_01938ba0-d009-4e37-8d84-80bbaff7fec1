// /*
//  * @Author: fuz<PERSON><PERSON><PERSON>
//  * @Date: 2022-12-02 11:39:21
//  * @LastEditTime: 2022-12-02 11:53:12
//  * @LastEditors: fuzhenghao
//  * @Description:
//  * @FilePath: \cq-enterprise-frontend\src\components\Bpmn\ProcessManage\bpmn-js-properties-panel\lib\provider\flowable\parts\commonRequest\index.js
//  */
var { getAllSimpleUsers } = require('../../../../../../../../../api/')

export function getAllUserList() {
  return getAllSimpleUsers().then((data) => {
    return data.map(({ userName, userId }) => {
      return {
        name: userName,
        value: userId,
      }
    })
  })
}