.invalid() {
  border-color: @bpp-color-error;
  background: @bpp-color-error-bg;
}

.focus() {
  // outline-color: @bpp-color-primary;
  outline: none;
  border-color: @bpp-color-primary;
  box-shadow: 0 0 1px 2px fade(@bpp-color-primary, 20%);
}


.icon-font() {
  font-family: "bpmn-js-pp";
  font-style: normal;
  font-weight: normal;
  speak: none;

  display: inline-block;
  text-decoration: inherit;
  text-align: center;
  font-variant: normal;
  text-transform: none;
  line-height: 1em;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.overlay() {
  background-color: @bpp-color-gray-lighter;
}

.action-button() {
  position: absolute;
  top: 0;
  height: 23px;
  width: 24px;
  overflow: hidden;
  cursor: pointer;
  background-color: @bpp-color-gray-lighter;
  border: 1px solid @bpp-color-gray-light;

  > span {
    display: none;
  }

  &:before {
    .icon-font();
    position: relative;
  }

  &.add:before {
    content: '\E803';
  }

  &.clear:before {
    content: '\E802';
  }

  &:hover {
    color: @bpp-color-primary;
  }
}

.label() {
  font-weight: bolder;
  display: inline-block;
  vertical-align: middle;
  color: @bpp-color-gray;
  &[for] {
    cursor: pointer;
  }
  &.bpp-hidden { display: none; }
}
