import React, { CSSProperties, FC, ReactPropTypes } from 'react'
import { with<PERSON><PERSON><PERSON>, RouteComponentProps } from 'react-router'
import { Breadcrumb, Divider } from 'antd'
import styles from './index.module.scss'
import { Link } from 'react-router-dom'

interface BreadcrumbsProps extends RouteComponentProps {
  title: string
  urlMap: { [key: string]: string }
  style: CSSProperties
}

const Breadcrumbs: FC<BreadcrumbsProps> = ({ title, urlMap, match, style }) => {
  const matchList = match.path.split('/').slice(1)
  return (
    <div className={styles.wrap} style={style}>
      <span className={styles.title}>{title}</span>
      <Divider type="vertical" />
      <Breadcrumb className={styles.breadcrumb}>
        {matchList
          .reduce((acc, cur, idx) => {
            let link = idx - 1 < 0 ? '/' + cur : `${acc[idx - 1]}/${cur}`
            acc.push(link)
            return acc
          }, [] as string[])
          .map((link, idx) => (
            <Breadcrumb.Item key={link}>
              {idx !== matchList.length - 1 ? (
                <Link to={link}>{urlMap[link]}</Link>
              ) : (
                urlMap[link]
              )}
            </Breadcrumb.Item>
          ))}
      </Breadcrumb>
    </div>
  )
}

export default withRouter(Breadcrumbs)
