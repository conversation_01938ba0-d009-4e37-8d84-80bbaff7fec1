import React from 'react'
import { Card } from 'antd'
import { CardProps } from 'antd/lib/card'
import styles from './index.module.scss'
import DisplayTypeSwitch from 'src/pageTabs/audit/overview/components/DisplayTypeSwitch'

interface ChartCardProps extends CardProps {
  displayType?: string
  onDisplayTypeChange?: (type: string) => void
  showDisCom?: boolean
}

export const ChartCard: React.FC<ChartCardProps> = (props) => {
  const { title, children, extra, displayType, onDisplayTypeChange, showDisCom = true } = props
  return (
    <Card
      title={title}
      className={styles.card}
      bodyStyle={{ padding: '16px 24px' }}
      extra={
        <div>
          {extra}
          {
            showDisCom &&
            onDisplayTypeChange &&
            displayType &&
            <DisplayTypeSwitch
              displayType={displayType}
              onDisplayTypeChange={onDisplayTypeChange}
            />
          }
        </div>
      }
    >
      {children}
    </Card>
  )
}
