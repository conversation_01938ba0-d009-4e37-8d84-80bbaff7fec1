.chooseCompareDiffListsWrap{
  background-color: #fff;
  height: 300px;
  [data-theme='dark'] & {
    background-color: #43434a;
  }
  .contentWrap {
    height: 120px;
    overflow-y: auto;
  }
  .rightContextMenu{
    position: 'fixed';
    z-index: 10000;
    .menuBtn {
      cursor: pointer;
      border: 1px solid #e5e5e5;
    }
  }

  .tableRow {
    display: flex;
    justify-content: space-between;
    cursor: pointer;
  }
  .tableHeaderContent, .tableRowContent {
    display: flex;
    justify-content: space-between;
    border-bottom: 1px solid #ccc;
    margin-bottom: 0;
    width: 100%;
  }
  .tableHeaderContent li, .tableRowContent li {
    width: 25%; 
    text-align: left; 
    list-style: none;
  }
}