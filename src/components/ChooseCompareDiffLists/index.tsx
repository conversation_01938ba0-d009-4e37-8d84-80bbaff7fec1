import React, { useEffect, useState } from 'react';
import { Ta<PERSON>, But<PERSON>, message } from 'antd';
import { sqlCompareVersion } from 'src/api'
import { useRequest } from 'src/hook'
import styles from './index.module.scss'
import i18n from 'i18next';
interface IProps {
  nodeInfo: any
  theme: string
  handleCompare: (selectItems: any[]) => void
}
const ChooseCompareDiffLists = (props: IProps) => {
  const { theme, nodeInfo } = props
  const {
    nodePath, 
    nodeType,
    connectionId,
    connectionType,
    nodePathWithType
  } = nodeInfo || {}
  const [showMenu, setShowMenu] = useState(false);
  const [menuX, setMenuX] = useState(0);
  const [menuY, setMenuY] = useState(0);
  const [selectItems, setSelectItems] = useState<any[]>([])

  const { data: compareData=[], run: getCompareVersion } = useRequest(sqlCompareVersion, {
    manual: true,
  })

  useEffect(()=>{
    if(!nodePath || !connectionId || !nodePathWithType || !connectionType){
      return
    }
    const params = {
      nodePath,
      connectionId,
      path: nodePathWithType, 
      dataSourceType: connectionType,
      objectType: nodeType?.toUpperCase() ?? '',
    }
    getCompareVersion(params)
  },[nodePath, nodeType, nodePathWithType, connectionType, connectionId, getCompareVersion])

  const handleContextMenu = (event: any) => {
    event.preventDefault();
    if(selectItems?.length !== 2){
      return
    }
    setShowMenu(true);
    setMenuX(event.clientX);
    setMenuY(event.clientY);
  };

  const handleCloseMenu = () => {
    setShowMenu(false);
  };

  const handleCompare = () => {
    setShowMenu(false);
    props?.handleCompare(selectItems)
  }

  const handleToggleSelect = (item: any) => {
    if(showMenu){
      return
    }
    if(selectItems?.map(i=>i?.id).includes(item?.id)){
      setSelectItems(selectItems?.filter(i=>i?.id !== item?.id))
    }else{
      if(selectItems?.length === 2){
        return message.warning(i18n.t("maxTwoVersions"))
      }
      setSelectItems([...selectItems, item])
    }
  }

  return (
    <div onContextMenu={handleContextMenu} onClick={handleCloseMenu} className={styles.chooseCompareDiffListsWrap}>
      <div
        className={styles.rightContextMenu}
        style={{
          position: 'fixed',
          zIndex: 10000,
          top: menuY,
          left: menuX,
          display: showMenu ? 'block' : 'none',
        }}
      >
        <Button size='small' className={styles.menuBtn} onClick={handleCompare}>{i18n.t("versionComparison")}</Button>
      </div>
      <Tabs type='card' className="resetTabStyle">
        <Tabs.TabPane tab={<span className="resetTabPaneStyle">{i18n.t("historyVersionList")}</span>} key="1" />
      </Tabs>
      {/* list 头部 */}
      {
        !!compareData?.length &&
        <div>
          <ul 
            className={styles.tableHeaderContent}
            style={theme === 'dark' ? 
              {
                color: '#d4d4d4',
                backgroundColor:'#43434a',
                fontWeight: 'bold'
              }
              : {
                color: '#666',
                backgroundColor: '#fff',
                fontWeight: 'bold'
              }}
          >
            <li>{i18n.t("historyVersionName")}</li>
            <li>{i18n.t("modifier")}</li>
            <li>{i18n.t("creationTime")}</li>
          </ul>
        </div>
      }
      {/* list 内容 */}
      <div className={styles.contentWrap}>
        {
          (compareData ?? [])?.map((item: any, index: number) => {
            const selectedItem = selectItems?.map(i=>i?.id).includes(item?.id)
            const style = theme === 'dark' ? 
            {
              color: selectedItem ? '#fff' : '#d4d4d4',
              backgroundColor: selectedItem ? '#555' : '#43434a',
            }
            : {
              color: selectedItem ? '#333' : '#666',
              backgroundColor: selectedItem ? '#e5e5e5' : '#fff',
            }
            return (
              <div
                key={index}
                className={styles.tableRow}
                style={style}
                onClick={()=>handleToggleSelect(item)}
              >
                <ul className={styles.tableRowContent}>
                  <li>{item?.version}</li>
                  <li>{item?.userId}</li>
                  <li>{item?.createAt}</li>
                </ul>
              </div>
            )
          })
        }
      </div>
    </div>
  );
};

export default ChooseCompareDiffLists;