import { Popover } from "antd";
import React from "react";
import { SketchPicker } from 'react-color';
import styles from './index.module.scss'
import { isEmpty } from "lodash";
import { hexToRgbObj, hexToRgbStr } from "./util";

// 颜色选择器
const ColorPicker = (props: any) => {
  const { value, onChange, alphaArr = [0, 1], defaultValue = {
    hex:'#FFFFFF',
    opacity: 1
  }} = props;

  const handleChangeComplete = (color: any) => {
    // 限制透明度的范围在 alphaArr 内
    const alpha = Math.min(Math.max(color.rgb.a, alphaArr[0]), alphaArr[1]);
    onChange({
      hex: color.hex?.toUpperCase(),
      opacity: alpha
    });
  }

  return <Popover
    content={<SketchPicker
      color={
        !isEmpty(value) ?
          hexToRgbObj(value?.hex, value?.opacity ?? 1) :
          hexToRgbObj(defaultValue?.hex, defaultValue?.opacity ?? 1)
      }
      onChangeComplete={handleChangeComplete}
      onChange={handleChangeComplete}
      styles={{
        picker: {
          border: "none",
          width: '244px',
          background: "rb(255, 255, 255)",
          borderRadius: '4px',
          boxShadow: "none",
          padding: "12px 12px 2px",
        } as any
      }}
    />}
    trigger="click"
    overlayClassName={styles.colorPickerPopover}
  >
    <div className={styles.colorPicker}>
      <div
        className={styles.colorShow}
        style={{
          backgroundColor: hexToRgbStr(
            value?.hex ?? defaultValue?.hex,
            value?.opacity ?? defaultValue?.opacity
          )
        }}
      />
      <div className={styles.colorValue}>{value?.hex ?? defaultValue?.hex}, {(value?.opacity ?? defaultValue?.opacity) * 100 + '%'}</div>
    </div>
  </Popover>
}
export default ColorPicker;