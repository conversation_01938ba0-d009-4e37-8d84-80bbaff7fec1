// HEX 颜色值转换为 RGB 颜色值
export const hexToRgbObj = (hex: string, transparent: string): any => {
  if (!hex) return { r: 102, g: 102, b: 102, a: 0.6 }
  hex = hex?.replace(/^#/, '');
  if (hex?.length === 3) {
    hex = hex.split('').map(x => x + x).join('');
  }
  const bigint = parseInt(hex, 16);
  const r = (bigint >> 16) & 255;
  const g = (bigint >> 8) & 255;
  const b = bigint & 255;
  return {
    r,
    g,
    b,
    a: transparent
  };
}

export const hexToRgbStr = (hex: string, transparent: string, type:'rgb'|'rgba' = 'rgba') => {
  const rgbObj = hexToRgbObj(hex, transparent);
  const rgbStr = type === 'rgba' ?
    `rgba(${rgbObj.r},${rgbObj.g},${rgbObj.b},${rgbObj.a})` :
    `rgb(${rgbObj.r},${rgbObj.g},${rgbObj.b})`
  return rgbStr;
 }
