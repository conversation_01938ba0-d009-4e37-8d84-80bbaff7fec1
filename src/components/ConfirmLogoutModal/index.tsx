import React, { useEffect, useState } from 'react'
import { useSelector, useDispatch } from 'src/hook'
import { Iconfont, UIModal } from 'src/components'
import { hideModal } from 'src/store/extraSlice/modalVisibleSlice'
import { Button } from 'antd'
import { setIsLoggedIn } from 'src/appPages/login/loginSlice'
import { useHistory } from 'react-router-dom'
import moment from 'moment'
import i18n from 'i18next';

export const ConfirmLogoutModal = () => {
  const dispatch = useDispatch()
  const history = useHistory()
  const visible = useSelector((state) => state.modal.ConfirmLogoutModal?.visible || false)

  const [currentTime, setCurrentTime] = useState(moment().format('HH:mm').toString());

  useEffect(() => {
    if (visible) {
      setCurrentTime(moment().format('HH:mm').toString());
    }
  }, [visible])

  useEffect(() => {
    return () => {
      goBackToLogin()
    }
  }, [])

  const goBackToLogin = () => {
    dispatch(hideModal('ConfirmLogoutModal'))
    dispatch(setIsLoggedIn(false))
    history.push('/login')
  }

  return (
    <UIModal
      title={i18n.t("offlineNotification")}
      visible={visible}
      footer={<Button type="primary" onClick={goBackToLogin}>{i18n.t("ok")}</Button>}
      width={500}
      closable={false}
    >
      <div style={{ display: 'flex', flexDirection: 'column', justifyContent: 'center', alignItems: 'center' }}>
        <Iconfont type='icon-ERROR' style={{ fontSize: '40px' }} />
        <div style={{ fontSize: '18px' }}>{i18n.t("accountLoggedInElsewhere")}{currentTime}{i18n.t("loggedInElsewhere")}</div>
        <div>{i18n.t("potentialPasswordLeak")}</div>
      </div>
    </UIModal>
  )
}
