
import { Tooltip } from 'antd';
import React from 'react'
import connectionFailWarnSrc from "src/assets/img/connectionFailWarn.png";
import i18n from 'i18next';

export const ConnectionFailWarnImg = () => {

  return (
    <Tooltip title={i18n.t("connectionTemporarilyUnavailable")}>
      <img
        src={connectionFailWarnSrc}
        alt={i18n.t("connectionFailureWarning")}
        width={15}
        style={{ marginLeft: 5, cursor: "pointer", marginTop: 3, height: 'max-content' }}
      />
    </Tooltip>
  )
}