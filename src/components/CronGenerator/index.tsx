import React, { useEffect, useState } from "react";
import Cron from "qnn-react-cron"
import { Button } from 'antd'
import { useSelector } from 'src/hook'
import "./index.css"
import i18n from 'i18next';
import { useTranslation } from "react-i18next";

interface IProps {
  onCancel: ()=>void
  onOk: (value: string)=>void
  defaultValue?: any
}
const CronGenerator = (props: IProps) => {
  const { onCancel, onOk, defaultValue } = props

  const { t } = useTranslation()
  const isLangEn = useSelector(state => state.login.locales) === 'en'
  let CronRef: any

  const [value, setValue] = useState("* * * * * ? *");  // 日和周条件互斥，因为不存在既满足日又满足周的所有情况，只有少数情况符合

  useEffect(()=>{
    defaultValue && setValue(defaultValue)
  },[defaultValue])

  const getValue = () => {
    const cronStr = CronRef.getValue()
    setValue(cronStr)
    onOk(cronStr)
  }


  const language = {

    // 面板标题,
    // panel title,
    paneTitle: {
      second:"Second",
      minute: "Minute",
      hour: "Hour",
      day: "Day",
      month: "Month",
      week: "Week",
      year: "Year",
    },
    assign:"Assign",
    donTAssign: "Do Not Assign",
    everyTime: {
      second:"Every Second",
      minute:"Every Minute",
      hour: "Every Hour",
      day: "Every Day",
      month: "Every Month",
      week: "Every Week",
      year: "Every Year",
    },

    // weel option  周选项
    week: {
      "sun": "Sunday",
      "mon": "Monday",
      "tue": "Tuesday",
      "wed": "Wednesday",
      "thu": "Thursday",
      "fri": "Friday",
      "sat": "Saturday",
    },

    aTob: {
      second: (AInput: any, BInput: any) => (
        <span>
        From {AInput}-{BInput} second, executed once every second
        </span>
      ),
      minute: (AInput: any, BInput: any) => (
        <span>
           From {AInput}-{BInput} minute, executed once every minute
        </span>
      ),
      hour: (AInput: any, BInput: any) => (
        <span>
        From {AInput}-{BInput} hour, executed once every hour
        </span>
      ),
      day: (AInput: any, BInput: any) => (
        <span>
         From {AInput}-{BInput} day, executed once every day
        </span>
      ),
      month: (AInput: any, BInput: any) => (
        <span>
        From {AInput}-{BInput} month, executed once every month
        </span>
      ),
      week: (AInput: any, BInput: any) => (
        <span>
            From {AInput}-{BInput}, executed once every week
        </span>
      ),
      year: (AInput: any, BInput: any) => (
        <span>
         From {AInput}-{BInput} year, executed once every year
        </span>
      ),
    },
    aStartTob: {
      second: (AInput: any, BInput: any) => (
        <span>
         From {AInput} starting from second, every{BInput}second executes once
        </span>
      ),
      minute: (AInput: any, BInput: any) => (
        <span>
       From {AInput} starting from minute, every {BInput} minute executes once
        </span>
      ),
      hour: (AInput: any, BInput: any) => (
        <span>
            From {AInput} starting from hour, every {BInput} hour executes once
        </span>
      ),
      day: (AInput: any, BInput: any) => (
        <span>
           From {AInput} starting from day, every {BInput} day executes once
        </span>
      ),
      month: (AInput: any, BInput: any) => (
        <span>
        From {AInput} starting from month, every {BInput} month executes once
        </span>
      ),
      // [n] in the NTH week of this month    本月第 n 周的 星期[n] 执行一次
      week: (AInput: any, BInput: any) => (
        <span>
           In the {AInput} of the week of this month {BInput} executes once
        </span>
      ),

      // 本月的最后一个 星期[n] 执行一次
      week2: (AInput: any) => <span>The last {AInput} of the month executes once</span>,

      year: (AInput: any, BInput: any) => (
        <span>
        From {AInput} Starting from year, every {BInput} year executes once
        </span>
      ),
    }

  };
	return (
    <Cron.Provider value={{
      language:isLangEn ? language : {}
    }}>
    <Cron
      value={value}
     style={{width: 730}}
      getCronFns={ (fns: any) => CronRef = fns }
      footer={[
        <>
          <Button style={{ marginRight: 10 }} onClick={onCancel}>
           {i18n.t("cancel")}
          </Button>
          <Button type="primary" onClick={getValue}>
            {i18n.t("generate")}
          </Button>
        </>,
      ]}
    />
    </Cron.Provider>
  ) 
}

export default CronGenerator


