import React, { useState } from 'react'
import { Input } from 'antd'
import { InputProps } from 'antd/lib/input'
import { handleContentBySpecifiedType } from 'src/util/handleContentByType'

interface CustomInputNumberProps extends Omit<InputProps, 'onChange'> {
  value?: string
  onChange?: (value: string) => void
  inputRef?: React.RefObject<Input>
  type?: string
  [p: string]: any
}

export const CustomInputNumber: React.FC<CustomInputNumberProps> = (props) => {
  const { value, onChange, inputRef, type, ...rest } = props
  const [inputValue, setInputValue] = useState<string>('')

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value.replace(/\s/g, '');

    // 判断输入的类型
    const handleRes = handleContentBySpecifiedType(type, newValue, rest);

    if (handleRes === null) {
      return
    }

    setInputValue(handleRes);
    if (onChange) {
      onChange(newValue)
    }
  }

  return (
    <Input
      {...rest}
      value={value !== undefined ? value : inputValue}
      onChange={handleChange}
      ref={inputRef}
    />
  )
}
