import React from 'react'
import { But<PERSON> } from 'antd'
import { DeleteOutlined } from '@ant-design/icons'
import { ButtonProps } from 'antd/lib/button'
import classNames from 'classnames'
// import styles from './index.module.scss'

export const DustbinButton: React.FC<ButtonProps> = (props) => {
  const { children, className, ...rest } = props
  return (
    <Button
      className={classNames(className)}
      danger
      icon={<DeleteOutlined />}
      {...rest}
    >
      {children}
    </Button>
  )
}
