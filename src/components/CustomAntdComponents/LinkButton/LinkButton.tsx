import React from 'react'
import { But<PERSON> } from 'antd'
import { ButtonProps } from 'antd/lib/button'
import classNames from 'classnames'
import styles from './index.module.scss'

export const LinkButton: React.FC<ButtonProps> = (props) => {
  const { children, className, ...rest } = props
  return (
    <Button
      className={classNames(styles.linkButton, className)}
      type="link"
      size="small"
      {...rest}
    >
      {children}
    </Button>
  )
}
