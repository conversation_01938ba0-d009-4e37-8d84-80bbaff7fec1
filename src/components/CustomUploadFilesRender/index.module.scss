.customUploadFilesRender {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 3px;

  .fileName {
    cursor: pointer;
  }

  .actionIcon {
    margin-left: 10px;
    cursor: pointer;
    color: var(--primary-color);
  }

  .actions {
    width: fit-content;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    font-size: 14px;



    .editEncode {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      width: fit-content;

      .encodeSelect {
        width: 120px;
      }
    }
  }
}

.previewTooltipOverlay {
  .previewDiv {
    color: #FFFFFF;
    white-space: pre;
    max-width: 650px;
    max-height: 400px;
    overflow: auto;
  }

  .previewDiv::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.5);
  }

  .previewDiv::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.8);
  }

  :global {
    .ant-tooltip-inner {
      min-width: 10px;
      min-height: 10px;
    }
  }
}