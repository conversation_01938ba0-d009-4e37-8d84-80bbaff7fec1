import { ArrowDownOutlined, ArrowUpOutlined, DeleteOutlined, EyeOutlined, PaperClipOutlined, VerticalAlignBottomOutlined, VerticalAlignTopOutlined } from "@ant-design/icons"
import React, { useMemo, useState } from "react";
import styles from './index.module.scss'
import { message, Select, Tooltip } from "antd";
import { codeTypes } from "src/features/perm/forms/textImportForms/FilePathForm";
import { useRequest } from "src/hook";
import { useTranslation } from "react-i18next";

interface ICustomUploadFilesRender {
  file: any // 文件
  fileRenderList: any[] // 文件列表
  form: any // 表单
  width?: string // 展示宽度
  field: string // 文件字段
  uploadFormName: string // 上传表单名称
  handleDelParams?: () => any // 删除参数特殊化处理回调
  deleteApi: (params: any) => Promise<any> // 删除接口
  previewApi: (params: any) => Promise<any> // 预览接口
  uploadFileEncodeName: string // 上传文件编码名称
  uploadFilePreviewMap: Map<string, any> // 上传文件预览map
  modifyUploadFilePreviewMap: (map: Map<string, any>) => void // 修改上传文件预览map
}

const CustomUploadFilesRender = (props: ICustomUploadFilesRender) => {
  const { file, fileRenderList, form, width = '100%', field, uploadFormName, handleDelParams, deleteApi, previewApi, uploadFileEncodeName, uploadFilePreviewMap, modifyUploadFilePreviewMap } = props;
  const { t } = useTranslation()
  const index = useMemo(() => fileRenderList.indexOf(file), [file, fileRenderList])
  const encodeValue = useMemo(() => {
    const encodeValues = form.getFieldValue(uploadFileEncodeName) ?? []
    return encodeValues[index] ?? 'UTF-8'
  }, [form?.getFieldValue(uploadFileEncodeName), uploadFileEncodeName, index])
  const [visible, setVisible] = useState(false) // 文件预览是否显示

  // SQL文件预览
  const { run: getPreViewSqlFile } = useRequest(previewApi, {
    manual: true,
    onSuccess: (res: any) => {
      const fileField = file?.[field]
      const newUploadFilePreviewMap = new Map(uploadFilePreviewMap)
      newUploadFilePreviewMap?.set(`${uploadFormName}-${fileField}`, res)
      modifyUploadFilePreviewMap(newUploadFilePreviewMap)
    }
  })

  // 删除单个sql文件
  const handleDeleteRow = () => {
    const uploadFileValue = form.getFieldValue(`${uploadFormName}`)
    const uploadFileValueArr = (!Array.isArray(uploadFileValue)) ?
      uploadFileValue?.split(",")?.map((item: any) => ({
        name: item
      })) : uploadFileValue;
    const uploadFileEncodeValue = form.getFieldValue(`${uploadFileEncodeName}`)
    const deleteFileName = uploadFileValueArr?.[index]?.[field]
    const updatedFileList = uploadFileValueArr?.filter((_: any, i: number) => index !== i)
    const updatedFileEncodeList = uploadFileEncodeValue?.filter((_: any, i: number) => index !== i)
    const params = handleDelParams?.() || deleteFileName
    deleteApi(params).then(() => {
      message.success(t("data_chg:msg.delete_success"))
      form.setFieldsValue({
        [uploadFormName]: updatedFileList,
        [uploadFileEncodeName]: updatedFileEncodeList
      })
      if (uploadFilePreviewMap?.has(`${uploadFormName}-${deleteFileName}`)) {
        const newUploadFilePreviewMap = new Map(uploadFilePreviewMap)
        newUploadFilePreviewMap?.delete(`${uploadFormName}-${deleteFileName}`)
        modifyUploadFilePreviewMap(newUploadFilePreviewMap)
      }
    }).catch(() => { message.error(t("data_chg:msg.delete_failure")) })
  }

  // 更改文件编码
  const handleEncodeSelectChange = (value: string) => {
    const preEncodeValues = form.getFieldValue(uploadFileEncodeName) || []
    if (index !== -1) {
      preEncodeValues[index] = value
    }
    form.setFieldsValue({
      [uploadFileEncodeName]: [...preEncodeValues]
    })
    getPreViewSqlFile({
      fileName: file?.[field],
      encodeName: value
    })
  }

  // 处理文件移动
  const handleMove = (type: 'UP' | 'DOWN' | 'TOP' | 'BOTTOM') => {
    if (fileRenderList?.length <= 1) return;
    const uploadFileValue = form.getFieldValue(`${uploadFormName}`)
    const uploadFileEncodeValue = form.getFieldValue(`${uploadFileEncodeName}`)
    const updatedFileList = [...uploadFileValue];
    const updatedFileEncodeList = [...uploadFileEncodeValue];
    switch (type) {
      case 'UP':
        if (index > 0) {
          const opeFile = updatedFileList.splice(index, 1)?.[0];
          updatedFileList.splice(index - 1, 0, opeFile);
          const opeFileEncode = updatedFileEncodeList.splice(index, 1)?.[0];
          updatedFileEncodeList.splice(index - 1, 0, opeFileEncode);
        }
        break;
      case "DOWN":
        if (index < uploadFileValue.length - 1) {
          const opeFile = updatedFileList.splice(index, 1)?.[0];
          updatedFileList.splice(index + 1, 0, opeFile);
          const opeFileEncode = updatedFileEncodeList.splice(index, 1)?.[0];
          updatedFileEncodeList.splice(index + 1, 0, opeFileEncode);
        }
        break;
      case "TOP":
        if (index !== 0) {
          const opeFile = updatedFileList.splice(index, 1)?.[0];
          updatedFileList.unshift(opeFile);
          const opeFileEncode = updatedFileEncodeList.splice(index, 1)?.[0];
          updatedFileEncodeList.unshift(opeFileEncode);
        }
        break;
      case "BOTTOM":
        if (index !== uploadFileValue.length - 1) {
          const opeFile = updatedFileList.splice(index, 1)?.[0];
          updatedFileList.push(opeFile);
          const opeFileEncode = updatedFileEncodeList.splice(index, 1)?.[0];
          updatedFileEncodeList.push(opeFileEncode);
        }
        break;
    }
    form.setFieldsValue({
      [uploadFormName]: updatedFileList,
      [uploadFileEncodeName]: updatedFileEncodeList
    })
  }

  // 文件预览是否显示
  const handleVisibleChange = (visible: boolean) => {
    if (visible) {
      if (!uploadFilePreviewMap?.get(`${uploadFormName}-${file?.[field]}`)) {
        getPreViewSqlFile({
          fileName: file?.[field],
          encodeName: encodeValue
        }).finally(() => {
          setVisible(true)
        })
      }
      else {
        setVisible(true)
      }
    }
    else {
      setVisible(false)
    }
  }

  return <div
    className={styles.customUploadFilesRender}
    style={{ width: width }}
    key={file}
  >
    <div className={styles.fileName}>
      <Tooltip
        overlayClassName={styles.previewTooltipOverlay}
        placement="top"
        onVisibleChange={handleVisibleChange}
        visible={visible}
        title={() => {
          const content = uploadFilePreviewMap?.get(`${uploadFormName}-${file?.[field]}`)
          return content ? <div className={styles.previewDiv}>
            {content}
          </div> : ''
        }}
      >
        <PaperClipOutlined className='mr4' />
        {file?.name}
        <EyeOutlined className={styles.actionIcon} title={t("data_chg:icon_btn.preview")} />
      </Tooltip>
    </div>
    <div className={styles.actions}>
      <div className={styles.editEncode}>
        <div>{t("data_chg:div_lbl.encoding")}：</div>
        <Select
          className={styles.encodeSelect}
          size="small"
          allowClear={false}
          value={encodeValue}
          options={codeTypes}
          onChange={handleEncodeSelectChange}
        />
      </div>
      <Tooltip placement="top" title={t("data_chg:tip_title.delete")}>
        <DeleteOutlined className={styles.actionIcon} onClick={handleDeleteRow} />
      </Tooltip>
      <Tooltip placement="top" title={t("data_chg:tip_title.move_up")}>
        <ArrowUpOutlined className={styles.actionIcon} onClick={() => handleMove('UP')} />
      </Tooltip>
      <Tooltip placement="top" title={t("data_chg:tip_title.move_down")}>
        <ArrowDownOutlined className={styles.actionIcon} onClick={() => handleMove('DOWN')} />
      </Tooltip>
      <Tooltip placement="top" title={t("data_chg:tip_title.move_top")}>
        <VerticalAlignTopOutlined className={styles.actionIcon} onClick={() => handleMove('TOP')} />
      </Tooltip>
      <Tooltip placement="top" title={t("data_chg:tip_title.move_bottom")}>
        <VerticalAlignBottomOutlined className={styles.actionIcon} onClick={() => handleMove('BOTTOM')} />
      </Tooltip>
    </div>
  </div>
}

export default CustomUploadFilesRender;