import React from 'react';
import { render, fireEvent } from '@testing-library/react';
import CycleTimes from './index';

// Mock antd components and i18n
jest.mock('antd', () => ({
  Select: ({ children, ...props }: any) => (
    <div className="ant-select" {...props}>
      {children}
    </div>
  ),
  TimePicker: {
    RangePicker: (props: any) => (
      <div className="ant-picker-range" {...props} />
    )
  },
  Button: ({ children, ...props }: any) => (
    <button className="ant-btn" {...props}>
      {children}
    </button>
  ),
  message: {
    error: jest.fn()
  },
  t: (key: string) => {
    const translations: { [key: string]: string } = {
      monday: '星期一'
    };
    return translations[key] || key;
  }
}));

// Mock icons
jest.mock('@ant-design/icons', () => ({
  DeleteOutlined: () => <span data-testid="delete-icon">delete</span>,
  PlusOutlined: () => <span data-testid="plus-icon">plus</span>
}));

// Mock hooks
jest.mock('src/hook', () => ({
  useSelector: () => ({
    locales: 'zh'
  })
}));

describe('CycleTimes', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('应该正确渲染初始状态', () => {
    const onChange = jest.fn();
    const { container } = render(<CycleTimes onChange={onChange} />);
    
    expect(container.querySelector('.ant-select')).toBeInTheDocument();
    expect(container.querySelector('.ant-picker-range')).toBeInTheDocument();
  });

  it('应该正确渲染添加按钮', () => {
    const onChange = jest.fn();
    const { getByTestId } = render(<CycleTimes onChange={onChange} />);
    
    expect(getByTestId('plus-icon')).toBeInTheDocument();
  });

  it('禁用状态下不应该允许编辑', () => {
    const onChange = jest.fn();
    const { container } = render(<CycleTimes disabled={true} onChange={onChange} />);
    
    // 检查 TimePicker 的 input 是否禁用
    const timePickerInput = container.querySelector('.ant-picker-range input');
    expect(timePickerInput).toHaveAttribute('disabled');
  });

  it('应该使用传入的value正确初始化', () => {
    const initialValue = [{
      dayOfWeek: 'MONDAY',
      startTime: '09:00',
      endTime: '18:00'
    }];
    
    const onChange = jest.fn();
    const { container } = render(
      <CycleTimes value={initialValue} onChange={onChange} />
    );
    
    // 验证组件是否正确渲染
    const cycleTimeWrap = container.querySelector('.cycleTimeWrap');
    expect(cycleTimeWrap).toBeInTheDocument();

    // 验证是否有正确数量的时间段
    const timeSlots = container.querySelectorAll('.mb10');
    expect(timeSlots.length).toBe(1);

    // 验证 Select 和 TimePicker 是否存在
    const select = container.querySelector('.ant-select');
    const timePicker = container.querySelector('.ant-picker-range');
    expect(select).toBeInTheDocument();
    expect(timePicker).toBeInTheDocument();
  });

  it('点击删除按钮应该触发onChange', () => {
    const initialValue = [{
      dayOfWeek: 'MONDAY',
      startTime: '09:00',
      endTime: '18:00'
    }];
    
    const onChange = jest.fn();
    const { getByTestId } = render(
      <CycleTimes value={initialValue} onChange={onChange} />
    );

    const deleteButton = getByTestId('delete-icon').closest('button');
    fireEvent.click(deleteButton!);

    expect(onChange).toHaveBeenCalled();
  });
});