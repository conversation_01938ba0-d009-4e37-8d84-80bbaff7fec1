import React from 'react'
import { Popconfirm } from 'antd'
import { DustbinButton } from '../index'
import i18n from 'i18next';

export const BatchDelete = ({
  selectedRowKeys,
  runDelete,
  loading,
  deleteObjName,
}: {
  selectedRowKeys: (string | number)[]
  runDelete: any
  loading?: any
  deleteObjName?: string
}) => {
  const deleteObjText = deleteObjName ? deleteObjName : i18n.t("item")
  return (
    <Popconfirm
      title={`${i18n.t("confirmDeletion")} ${selectedRowKeys.length} ${i18n.t("selectedItems")}${deleteObjText}？`}
      onConfirm={() => runDelete(selectedRowKeys)}
      okButtonProps={{
        danger: true,
        loading,
      }}
    >
      <DustbinButton danger disabled={!selectedRowKeys.length}>
        {i18n.t("batchDeletion")}
      </DustbinButton>
    </Popconfirm>
  )
}
