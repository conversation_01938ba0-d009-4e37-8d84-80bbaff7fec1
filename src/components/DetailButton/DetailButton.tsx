import { Button, Modal } from 'antd'
import { ButtonProps } from 'antd/es/button'
import React from 'react'
import styles from './index.module.scss'
import i18n from 'i18next';

interface IDetailButtonProps extends ButtonProps {
  text: string
  title?: string
}

export const DetailButton = (props: IDetailButtonProps) => {
  const { text, title, ...others } = props
  return (
    <Button
      {...others}
      className={styles.button}
      type="link"
      onClick={() =>
        Modal.info({
          width: 700,
          title: title ?? i18n.t("details"),
          content: <div className={styles.detailbox}>{text}</div>,
        })
      }
    >
      {text}
    </Button>
  )
}
