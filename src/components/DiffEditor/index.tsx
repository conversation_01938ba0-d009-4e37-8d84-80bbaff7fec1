import React, { useEffect, useRef } from 'react';
import * as monaco from 'monaco-editor';

interface DiffEditorProps {
  originalTxt: string;
  modifiedTxt: string;
  theme?: string;
  language?: string;
  options?: monaco.editor.IStandaloneEditorConstructionOptions;
  diffEditorOptions?: monaco.editor.IDiffEditorConstructionOptions;
  wrapStyle?: React.CSSProperties;
}

const DiffEditor = React.memo((props: DiffEditorProps) => {
  const { 
    language, 
    originalTxt, 
    modifiedTxt, 
    theme, 
    options, 
    diffEditorOptions,
    wrapStyle,
  } = props;

  const containerRef = useRef<HTMLDivElement>(null);
  const diffEditorRef = useRef<monaco.editor.IStandaloneDiffEditor | null>(null);
  const stheme = theme || 'vs';
  const slanguage = language || 'sql';

  useEffect(() => {
    if (containerRef.current) {
      const diffEditor = monaco.editor.createDiffEditor(containerRef.current, {
        theme: stheme,
        language: slanguage,
        ...options,
        ...diffEditorOptions,
        smoothScrolling: false,
        readOnly: true,
        automaticLayout: true,
      });
      diffEditor.setModel({
        original: monaco.editor.createModel(originalTxt, slanguage),
        modified: monaco.editor.createModel(modifiedTxt, slanguage)
      });
      diffEditorRef.current = diffEditor;
    }
    return () => {
      if (diffEditorRef.current) {
        diffEditorRef.current.dispose();
      }
    };
  }, [
    originalTxt, 
    modifiedTxt, 
    stheme, 
    slanguage, 
    options, 
    diffEditorOptions, 
  ]);

  window.onresize = () => {
    if (diffEditorRef.current) {
      diffEditorRef.current.layout();
    }
  };

  return <div ref={containerRef} style={{ width: '100%', height: '100vh', ...wrapStyle }} />;
});

export default DiffEditor;