import React, { PropsWithChildren } from 'react'
import { <PERSON>er, But<PERSON> } from 'antd'
import i18n from 'i18next';

interface IProps {
  visible: boolean
  title: string
  width: number
  onCancel: () => void
  onOk: (fields: string[]) => void
  footer?: React.ReactNode
}

export const DrawerWrapper = ({
  visible = false,
  title,
  width,
  onCancel,
  onOk,
  footer,
  children,
}: PropsWithChildren<IProps>) => {
  return (
    <Drawer
      title={title}
      width={width || 400}
      onClose={onCancel}
      visible={visible}
      maskClosable={false}
      footer={
        footer ? (
          footer
        ) : (
          <>
            <Button
              type="primary"
              style={{ marginRight: 10 }}
              onClick={() => onOk(['d'])}
            >
              {i18n.t("ok")}
            </Button>
            <Button onClick={onCancel}>{i18n.t("cancel")}</Button>
          </>
        )
      }
    >
      {children}
    </Drawer>
  )
}
