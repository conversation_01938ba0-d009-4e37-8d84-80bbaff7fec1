import React, { useRef, useState, useEffect } from 'react';
import { Tooltip } from 'antd';

export const EllipsisWithTooltip = ({ 
  text, 
  width = '100%',
  className
 }: {
  text: string;
  width?: string | number;
  className?: string
 }) => {
  const textRef = useRef<HTMLDivElement | null>(null);
  const [isOverflowing, setIsOverflowing] = useState(false);

  useEffect(() => {
    if (textRef?.current) {
      const element = textRef.current;
      // 检测文本是否超出容器宽度
      setIsOverflowing(element?.scrollWidth > element?.clientWidth);
    }
  }, [text, width]);

  return (
    <Tooltip 
      title={isOverflowing ? text : null} 
      overlayStyle={{maxHeight: 400, overflow: 'auto'}}
    >
      <div
        ref={textRef}
        style={{
          whiteSpace: 'nowrap',
          overflow: 'hidden',
          textOverflow: 'ellipsis',
          width,
          display: 'inline-block',
          verticalAlign: 'bottom'
        }}
        className={className}
      >
        {text}
      </div>
    </Tooltip>
  );
};

export default EllipsisWithTooltip;
