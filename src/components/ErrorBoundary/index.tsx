import React from 'react'
import { Popconfirm, Button } from 'antd'
import errorImage from 'src/assets/img/errorBoundary.svg'
import './index.css'
import i18n from 'i18next';
import { storeRegister } from 'src/store/storeRegister'

export class ErrorBoundary extends React.Component<any, any> {
  constructor(props: any) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(error: any) {
    // Update state so the next render will show the fallback UI.
    console.error('ErrorBoundary-error-------', error)
    return { hasError: true }
  }

  componentDidCatch(error: any, errorInfo: any) {
    // You can also log the error to an error reporting service
    console.log(error, errorInfo)
  }

  render() {
    if (this.state.hasError) {
      // You can render any custom fallback UI

      return (
        <div className="error-container">
          <div className="error-content">
            <img src={errorImage} alt="error image" height={128} />
            <div className="error-text">
              {i18n.t("pageRenderingError")}
            </div>
            <div>
              <Button
                className="linkbutton"
                type="primary"
                onClick={() => window?.location?.reload()}
              >
                {i18n.t("refresh")}
              </Button>
              <Popconfirm
                className="linkbutton"
                title={i18n.t("confirmReset")}
                onConfirm={async () => {
                  // 清除 Redux persist 的 IndexedDB 缓存
                  const persistor = storeRegister.getPersistor()
                  if (persistor) {
                    await persistor.purge()
                  }

                  // 重新加载页面
                  window?.location?.reload()
                }}
              >
                <Button>{i18n.t("clearCacheData")}</Button>
              </Popconfirm>
            </div>
          </div>
        </div>
      )
    }

    return <>{this.props.children}</>;
  }
}
