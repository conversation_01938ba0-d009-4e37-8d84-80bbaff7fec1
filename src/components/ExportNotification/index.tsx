import React from 'react';
import { Button, notification, message } from 'antd';
import i18n from 'i18next';
import { useHistory } from 'react-router-dom'
import { LinkButton } from 'src/components'
import styles from './index.module.scss';

export const openNotification = (title: string, des: string, download: () => void, duration?: number) => {
  return notification.success({
    placement: 'bottomLeft',
    duration: duration === 0 ? duration : duration || 15,
    message: title,
    description: (
      <>
        {des}
        <Button
          type='link'
          onClick={() => {
            download()
          }}
        >
          {i18n.t("download")}
        </Button>
      </>
    ),
  });
};

export const exportTaskCreatedNot = () => {

  return message.info(i18n.t('common.message.exportCreated.tip'))
  //  return notification.info({
  //   placement: 'bottomLeft',
  //   message: <div className={styles.messageNotTitle}>导出任务已创建</div>,
  //   duration: 3
  // });
}