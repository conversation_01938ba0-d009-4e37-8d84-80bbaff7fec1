import React from 'react'
import { Form, Input } from 'antd'
import { SearchOutlined } from '@ant-design/icons'
import i18n from 'i18next';
export const BaseFilter = ({
  fetchData,
  filterText,
}: {
  fetchData: any
  filterText?: string
}) => {
  const [filterForm] = Form.useForm<{ name: string }>()
  return (
    <Form
      name="permFilter"
      layout="inline"
      form={filterForm}
      onFinish={({ name }) => {
        fetchData(name)
      }}
    >
      <Form.Item name="name" noStyle>
        <Input
          prefix={<SearchOutlined />}
          placeholder={`${i18n.t("pleaseEnter")}${filterText}`}
          allowClear
        />
      </Form.Item>
    </Form>
  )
}
