import React from 'react';
import ReactJoyride from 'react-joyride';
import { useDispatch, useSelector } from 'src/hook';
import { resetGuideSteps } from 'src/pageTabs//SceneGuideModal/guideSlice';
import styles from './index.module.scss';
import i18n from 'i18next';

export const STEP_OPTION = {
  disableBeacon: true,
  hideCloseButton: true,
  placement: "right",
  styles: {
    options: {
      arrowSize: 14,
    },
  }
}

export const RenderTourStepContent = ({
  title,
  position,
  detail
}: {
  title: string;
  position?: string;
  detail: string;
}) => {
  return (
    <div className={styles.guideStep1Content}>
      <div className={styles.guideTitle}>
        <span> {title} </span>
        {position && <span> {position} </span>}
      </div>
      <div className={styles.guideContent}>{detail}</div>
    </div>
  )
}

export const Guide = () => {

  const dispatch = useDispatch();
  const { steps = [], guideVisible } = useSelector(state => state.guide);

  const handleJoyrideCallback = (data: any) => {
    const { action } = data;

    if (action == 'reset' || action == 'last') {
      dispatch(resetGuideSteps());
    }
  };

  return (
    <ReactJoyride
      steps={steps}
      disableScrolling={true}
      continuous={true}
      locale={{ back: i18n.t("return"), close: i18n.t("close"), last: i18n.t("completed"), next: i18n.t("nextStep"), skip: i18n.t("skip") }}
      styles={{
        options: {
          primaryColor: '#fff',
          textColor: '#fff',
          width: 241,
          backgroundColor: '#165DFF',
          arrowColor: '#165DFF', //箭头颜色
        },
        buttonNext: {
          color: '#165DFF',
          fontSize: 12
        },
        tooltipContainer: {
          padding: '0px 0px',
        },
        tooltipContent: {
          padding: '0px 0px',
        }
      }}
      showSkipButton={true}
      disableOverlayClose={true}
      run={guideVisible}
      callback={handleJoyrideCallback}
    />
  );
};
