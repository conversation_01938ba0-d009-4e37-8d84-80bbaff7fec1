import React, { useEffect } from "react";
import * as _ from 'lodash';
import { Row, Col, Input, Form, Checkbox, Tooltip } from 'antd';
import { useDispatch, useSelector } from 'src/hook';
import { updateCopySourceContexts } from 'src/store/extraSlice/hdrSlice';
import { renderNodeWithIcon } from '../utils';
import styles from './index.module.scss';
import i18n from 'i18next';

const ConfirmRepInfoContent = ({
  form,
  targetNodeInfos
}: {
  form: any;
  targetNodeInfos: any[];
}) => {

  const dispatch = useDispatch();

  const { copySourceTexts } = useSelector(state => state.hdrGuide);

  useEffect(() => {
    form.setFieldsValue({ noData: copySourceTexts?.noData })
  }, [form, copySourceTexts?.noData])

  const updateCopyInfo = (tableName: string, key: string) => {
    let cloneCopyInfos = _.cloneDeep(copySourceTexts?.sourceContexts || []);
    cloneCopyInfos = cloneCopyInfos.map(info => {
      if (info?.nodePathWithType === key) {
        info.renameTable = tableName;
      }
      return info;
    })

    dispatch(updateCopySourceContexts({
      ...copySourceTexts,
      sourceContexts: cloneCopyInfos
    }))
  }

  return (
    <div className={styles.confirmRepInfoContent}>
      <div className={styles.stepTitle}>
        <div className={styles.stepIcon}>2</div>
        <h3>{i18n.t("confirmCopyInformation")}</h3>
      </div>
      <div className={styles.confirmNodeInfo}>
        <Form form={form}>
          <div className={styles.mb20}>
            {
              copySourceTexts?.sourceContexts?.map((item, index) => (
                <Row key={item?.nodePathWithType}>
                  <Col span={12}>
                    <Form.Item label={`${i18n.t("source")}${index + 1}:`}>
                      <Tooltip title={renderNodeWithIcon(item?.connectionName, item?.connectionType, item?.nodePathWithType)}>
                        <div className={styles.commonEleWith}>{renderNodeWithIcon(item?.connectionName, item?.connectionType, item?.nodePathWithType)}</div>
                      </Tooltip>
                    </Form.Item>

                  </Col>
                  <Col span={12}>
                      <div className="flexAlignCenter">
                        <div style={{width: 100}}>{i18n.t("newTableName")}：</div>
                        <Input placeholder={i18n.t("pleaseEnterNewTableName")} value={item?.renameTable} onChange={e => updateCopyInfo(e.target.value, item?.nodePathWithType)} />
                    </div>
                  </Col>
                </Row>
              ))
            }
          </div>
          <div className={styles.mb20}>

            {
              targetNodeInfos?.map((item, index) => (
                <Row key={item?.nodePathWithType}>
                  <Col span={12} >
                    <Form.Item label={`${i18n.t("target")}${index + 1}:`}>
                      <Tooltip title={renderNodeWithIcon(item?.connectionName, item?.connectionType, item?.nodePathWithType)}>
                        <div className={styles.commonEleWith}>{renderNodeWithIcon(item?.connectionName, item?.connectionType, item?.nodePathWithType)}</div>
                      </Tooltip>
                    </Form.Item>
                  </Col>
                </Row>
              ))
            }
          </div>
          <Form.Item className={styles.formItemStyle} label={i18n.t("containsData")}>
            <Checkbox
              checked={!copySourceTexts?.noData}
              onChange={e => dispatch(updateCopySourceContexts({
                ...copySourceTexts,
                noData: !e.target.checked
              }))} />
          </Form.Item>
        </Form>
      </div>
    </div>
  )
}

export default ConfirmRepInfoContent