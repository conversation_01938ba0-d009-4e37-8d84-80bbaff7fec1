import React, { useEffect, useState, useCallback, memo, useMemo } from 'react';
import { PlusOutlined } from '@ant-design/icons';
import * as _ from 'lodash';
import { v4 as uuidv4 } from 'uuid'
import { Table, Select, Form, Button } from 'antd';
import { useRequest } from 'src/hook';
import { Iconfont } from 'src/components';
import { DataSourceType, getTableListBySchema, queryTreeNodeAll } from "src/api";
import { THREE_TIER_CONNECTION_TYPES, DATA_REPLICATION_TYPE } from '../../contstant';
import styles from '../index.module.scss';
import classNames from 'classnames';
import { useEffectOnce } from 'react-use';
import i18n from 'i18next';

export const DynamicTable = memo(({
  form,
  sourceType, //统一数据源类型
  dataSource,
  setDataSource,
  dataReplicationType,
  isTarget = false, //是否为目标
  selectedConnectionInfo
}: {
  form: any;
  sourceType?: DataSourceType;
  dataSource: any,
  setDataSource: (params: any) => void;
  dataReplicationType?: DATA_REPLICATION_TYPE;
  isTarget?: boolean;
  selectedConnectionInfo: any;
}) => {

  const isHideTableColumn = dataReplicationType === 'schema' && isTarget;
  const onlyOneTargetNode = ['schema', 'singleTable'].includes(dataReplicationType) && isTarget;
  const onlyOneSourceNode = dataReplicationType === 'multiTable' && !isTarget;

  const [schemaOptions, setSchemaOptions] = useState<any>([])

  const { run: querySchemaList, loading: schemaLoading } = useRequest(queryTreeNodeAll, {
    manual: true,
    formatResult(res) {
      return res?.map((i: any) => {
        return {
          label: <span key={i?.nodeName}><Iconfont type={`icon-${i?.connection?.connectionType}`} style={{ marginRight: 4 }} />{i?.nodeName}</span>,
          value: i?.nodeName,
          props: i,
        };
      });
    },
  })

  //请求table
  const { run: getTableBySchema, loading: tableListLoading } = useRequest(getTableListBySchema, {
    manual: true,
    formatResult(res) {
      return res.map((item: any) => {
        return {
          value: item.nodeName,
          lebel: <span key={item?.nodeName}><Iconfont type={`icon-${item?.connection?.connectionType}`} style={{ marginRight: 4 }} />{item?.nodeName}</span>,
          props: item,
        };
      });
    },
  });

  //设置初始db or schema的值
  useEffectOnce(() => {

  })
  useEffect(() => {
    if (selectedConnectionInfo) {
      const sourceParams = {
        connectionId: selectedConnectionInfo.connectionId,
        connectionType: selectedConnectionInfo.connectionType,
        nodeType: "connection",
        nodeName: selectedConnectionInfo.connectionName,
        nodePath: `/root/${selectedConnectionInfo.connectionId}`,
        nodePathWithType: `/CONNECTION:${selectedConnectionInfo.connectionId}`,
      };
      querySchemaList(sourceParams).then(res => {
        setSchemaOptions(res)
      })
    } else {
      setSchemaOptions([]);
    }
  }, [JSON.stringify(selectedConnectionInfo)])


  const handleSelectChange = useCallback(_.debounce((async (value: any, dataIndex: string, key: string, selectOption: any) => {

    let cloneDataSource = _.cloneDeep(dataSource);
    let options: any = [];

    if (['database', 'schema'].includes(dataIndex)) { // 如果是列 1 的变化，调用接口获取列 2 的选项
      const { sdt, nodeType, nodeName, nodePath, nodePathWithType } = selectOption?.props || {};
      const params = {
        connectionId: sdt?.connectionId,
        connectionType: sdt?.conncetionType,
        nodeType,
        nodeName,
        nodePath,
        nodePathWithType,
      };
      if (dataIndex === 'database') {
        options = await querySchemaList(params);
      } else if (dataIndex === 'schema') {
        options = await getTableBySchema(params);
      }
    }

    const updataDataSource = cloneDataSource?.map((item: any) => {

      if (item?.key === key) {
        item[dataIndex] = value;
        item['connectionName'] = item?.connectionName || selectedConnectionInfo?.connectionName;
        if (dataIndex === 'database') {
          item[`schemaOptions`] = options;
          form.setFieldsValue({ [`${isTarget}schema${key}_schema`]: undefined });
          form.setFieldsValue({ [`${isTarget}table${key}_table`]: undefined });
        }
        if (dataIndex === 'schema') {
          item[`tableOptions`] = options;
          item['table'] = null;
          form.setFieldsValue({ [`${isTarget}table${key}_table`]: undefined });
        }
      }

      return item
    })

    setDataSource(updataDataSource)

  })), [JSON.stringify(dataSource), JSON.stringify(selectedConnectionInfo?.connectionName)]);

  const onRemoveSourceItem = (key: number) => {

    let cloneDataSource = _.cloneDeep(dataSource);
    cloneDataSource = cloneDataSource.filter((item: any) => item?.key !== key);
    setDataSource(cloneDataSource);
  }

  const columnWidth = useMemo(() => {
    if (!sourceType) return '40%';
    //目标
    if (isHideTableColumn) {
      //db schema 或schema -10
      if (THREE_TIER_CONNECTION_TYPES.includes(sourceType)) {
        return '35%'
      } else {
        return '85%'
      }
    }
    //源
    if (THREE_TIER_CONNECTION_TYPES.includes(sourceType)) {
      return '25%'
    } else {
      return '40%'
    }

  }, [isHideTableColumn, sourceType])

  const columns = [
    ...(sourceType && THREE_TIER_CONNECTION_TYPES.includes(sourceType) ?
      [{
        title: i18n.t("sourceDatabase"),
        dataIndex: 'database',
        width: columnWidth,
        render: (text: any, record: any, index: any) => {
          return (
            <Form.Item
              name={`${isTarget}schema${record?.key}_database`}
              rules={[{ required: true, message: i18n.t("pleaseSelectDatabaseValue") }]}
              style={{ margin: 0 }}
            >
              <Select
                bordered={false}
                loading={schemaLoading}
                options={schemaOptions}
                className={styles.fixedEleWidth}
                placeholder={isTarget ? i18n.t("pleaseSelectTargetDatabase") : i18n.t("pleaseSelectSourceDatabase")}
                onChange={(value, option) => handleSelectChange(value, 'database', record?.key, option)}
              />
            </Form.Item>
          )
        },
      }] : []),
    {
      title: i18n.t("sourceSchema"),
      dataIndex: 'schema',
      width: columnWidth,
      render: (text: any, record: any, index: any) => {
        return (
          <Form.Item
            name={`${isTarget}schema${record?.key}_schema`}
            rules={[{ required: true, message: i18n.t("pleaseSelectSchemaValue") }]}
            style={{ margin: 0 }}
          >
            <Select
              bordered={false}
              loading={schemaLoading}
              options={sourceType && THREE_TIER_CONNECTION_TYPES.includes(sourceType) ? record?.schemaOptions : schemaOptions}
              className={styles.fixedEleWidth}
              placeholder={isTarget ? i18n.t("pleaseSelectTargetSchema") : i18n.t("pleaseSelectSourceSchema")}
              onChange={(value, option) => handleSelectChange(value, 'schema', record?.key, option)}
            />
          </Form.Item>
        )
      },
    },
    ...(isHideTableColumn ? [] :
      [{
        title: i18n.t("sourceObject"),
        dataIndex: 'table',
        width: columnWidth,
        render: (text: any, record: any, index: any) => {

          return (
            <Form.Item
              name={`${isTarget}table${record?.key}_table`}
              rules={[{ required: true, message: i18n.t("pleaseSelectTableValue") }]}
              style={{ margin: 0 }}
            >
              <Select
                bordered={false}
                mode={((isTarget && dataReplicationType === 'singleTable') || (!isTarget && dataReplicationType === 'multiTable')) ? undefined : 'multiple'}
                showArrow
                maxTagTextLength={6}
                maxTagCount={1}
                loading={tableListLoading}
                options={record?.tableOptions || []}
                className={styles.fixedEleWidth}
                placeholder={isTarget ? i18n.t('pleaseSelectTargetObject') : i18n.t("pleaseSelectSourceObject")}
                onChange={(value, option) => handleSelectChange(value, 'table', record?.key, option)}
              />
            </Form.Item>
          )
        }
      }]),
    {
      title: i18n.t("operation"),
      dataIndex: 'action',
      with: '20%',
      render: (text: any, record: any, index: string | number) => (
        <Button type='link' onClick={() => onRemoveSourceItem(record?.key)}>{i18n.t("remove")}</Button>
      ),
    },
  ];

  const onAddSource = () => {
    let defaultDataSource = _.cloneDeep(dataSource);
    defaultDataSource = defaultDataSource.concat([{ key: uuidv4(), connectionType: selectedConnectionInfo?.connectionType, connectionName: selectedConnectionInfo?.nodeName }]);
    setDataSource(defaultDataSource)
  }

  return (
    <div className={styles.sourceContainer}>
      <div className={classNames('linkStyle', 'mb10', [styles.addSourceBtn], {
        'nonmodifiableColor': (onlyOneTargetNode || onlyOneSourceNode) && dataSource?.length === 1
      })} >
        <Button
          type='link'
          icon={ <PlusOutlined color='#3262ff' />}
          onClick={() => {
            if ((onlyOneTargetNode || onlyOneSourceNode) && dataSource?.length === 1) {
              return
            }
            onAddSource()
          }}>
         {i18n.t("add")}
        </Button>
      </div>
      <Form form={form}>
        <Table
          bordered
          dataSource={dataSource}
          columns={columns}
          pagination={false}
          scroll={{ y: 130 }}
          size='small'
          className={styles.asyncTable}
        />
      </Form>
    </div>
  );
});
