import React, { memo, useState } from "react";
import { Select, Form, Badge, Button } from 'antd';
import * as _ from 'lodash';
import { Iconfont } from "src/components";
import { useDispatch, useSelector } from 'src/hook';
import { 
  setTargetNodeInfos, 
  setSourceNodeInfos,
  updateTestConnectionState,
  setTableFieldMap,
  updateCopySourceContexts
} from 'src/store/extraSlice/hdrSlice';
import { DataSourceType } from 'src/api';
import { DynamicTable } from './AsyncEditTable';
import { SUPPROT_CONNECTION_TYPES, DATA_REPLICATION_TYPE } from '../../contstant';
import styles from '../index.module.scss';
import i18n from 'i18next';

const SourceObjectContent = memo(({
  form,
  connectionLoading,
  connectionList,
  dataReplicationType,
  updataConnectionList,
  runTestConnection,
  runQueryConnection,
}: {
  form: any;
  connectionLoading: boolean;
  connectionList: any[];
  dataReplicationType: DATA_REPLICATION_TYPE,
  updataConnectionList: (params: any) => void;
  runTestConnection: (params: { connectionId: number, dataSourceType: DataSourceType }, type: 'source'| 'target') => void;
  runQueryConnection: (type: DataSourceType) => void;
}) => {


  const dispatch = useDispatch();
  const { sourceNodeInfos, testConnectionState, copySourceTexts } = useSelector(state => state.hdrGuide);

  //源连接测试状态
  const [sourceConnectionInfo, setSourceConnectionInfo] = useState<any>(null);
  //选择数据源类型
  const [sourceType, setSourceType] = useState<DataSourceType>();
 
  const connectionTypeSelectOption = SUPPROT_CONNECTION_TYPES.map(type => ({
    label: <span>
      <Iconfont type={`icon-connection-${type}`} style={{ marginRight: 4 }} />{type}
    </span>,
    value: type
  }))

  const beginTestConnection = async () => {
    const { sourceType, sourceId } = form?.getFieldsValue() ?? {};
    if (sourceType && sourceId) {
      runTestConnection({ connectionId: sourceId, dataSourceType: sourceType }, 'source')
    }
  }

  return (
    <div>
      <h3 className={styles.subTitle}>{i18n.t("source")}</h3>
      <div className={styles.confirmNodeInfo}>
        <Form form={form} layout="inline" >
          <Form.Item label={i18n.t("dataSourceType")} required name='sourceType' rules={[{ required: true, message: i18n.t("pleaseSelectDataSourceType") }]}>
            <Select
              placeholder={i18n.t("pleaseSelectDataSourceType")}
              style={{ width: 150 }}
              options={connectionTypeSelectOption}
              onChange={async(value: DataSourceType) => {
                updataConnectionList([]);
                setSourceConnectionInfo(null)
                form.setFieldsValue({ sourceId: undefined,targetType: value,targetId: undefined })
                runQueryConnection(value);
                setSourceType(value);
                //清空源和目标所有已选信息
                await dispatch(setSourceNodeInfos([]));
                await dispatch(setTargetNodeInfos([]));
                await dispatch(updateCopySourceContexts({...copySourceTexts, sourceContexts: []}))

              }}
            />
          </Form.Item>
          <Form.Item label={i18n.t("databaseConnection")} required name='sourceId' rules={[{ required: true, message: i18n.t("pleaseSelectDatabaseConnection") }]}>
            <Select
              loading={connectionLoading}
              style={{ width: 150 }}
              placeholder={i18n.t("pleaseSelectDatabaseConnection")}
              options={connectionList}
              dropdownMatchSelectWidth={false}
              onChange={async(_, options: any) => {
                setSourceConnectionInfo(options.props);
                //清空已选源信息
                await dispatch(setSourceNodeInfos([]));
                await dispatch(updateCopySourceContexts({...copySourceTexts, sourceContexts: []}))
                await dispatch(updateTestConnectionState({
                  ...testConnectionState,
                  source: false
                }))
              }}
            />
          </Form.Item>
          <Form.Item>
            <Button
              type="link"
              size="small"
              onClick={() => beginTestConnection()}
            >
              <Badge status={testConnectionState?.source ? 'success' : 'default'} />{i18n.t("testConnection")}
            </Button>
          </Form.Item>
        </Form>
        <DynamicTable
          form={form}
          sourceType={sourceType}
          dataSource={_.cloneDeep(sourceNodeInfos)}
          dataReplicationType={dataReplicationType}
          setDataSource={ (data: any) => {
            dispatch(setSourceNodeInfos(data))
            dispatch(setTableFieldMap([]))
            dispatch(updateCopySourceContexts({...copySourceTexts, sourceContexts: []}))
          } }
          selectedConnectionInfo={sourceConnectionInfo} />
      </div>
    </div>
  )
})

export default SourceObjectContent