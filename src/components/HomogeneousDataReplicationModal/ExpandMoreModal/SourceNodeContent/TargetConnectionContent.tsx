import React, { memo, useState } from "react";
import { Select, Form, Badge, Button } from 'antd';
import * as _ from 'lodash';
import { Iconfont } from "src/components";
import {  
  setTargetNodeInfos,
  updateTestConnectionState,
  setTableFieldMap,
  updateTargetContexts
} from 'src/store/extraSlice/hdrSlice';
import { DataSourceType } from 'src/api';
import { useDispatch, useSelector } from 'src/hook';
import { DynamicTable } from './AsyncEditTable';
import { SUPPROT_CONNECTION_TYPES, DATA_REPLICATION_TYPE } from '../../contstant';
import styles from '../index.module.scss';
import i18n from 'i18next';

const TargetConnectionContent = memo(({
  form,
  connectionLoading,
  connectionList,
  dataReplicationType,
  runTestConnection,
}: {
  form: any;
  connectionLoading: boolean;
  connectionList: any[];
  dataReplicationType: DATA_REPLICATION_TYPE,
  runTestConnection: (params: { connectionId: number, dataSourceType: DataSourceType }, type: 'source'| 'target') => void;
}) => {

  const dispatch = useDispatch();
  const { targetNodeInfos, testConnectionState } = useSelector(state => state.hdrGuide);

 //连接信息
  const [sourceConnectionInfo, setSourceConnectionInfo] = useState<any>(null)

  const connectionTypeSelectOption = SUPPROT_CONNECTION_TYPES.map(type => ({
    label: <span>
      <Iconfont type={`icon-connection-${type}`} style={{ marginRight: 4 }} />{type}
    </span>,
    value: type
  }))


  const beginTestConnection = async () => {
    const { targetId, targetType } = form?.getFieldsValue() ?? {};
    if (targetId && targetType) {
      runTestConnection({ connectionId: targetId, dataSourceType: targetType }, 'target');
    }
  }

  return (
    <div>
      <h3 className={styles.subTitle}>{i18n.t("target")}</h3>
      <div className={styles.confirmNodeInfo}>
        <Form form={form} layout="inline" >
          <Form.Item label={i18n.t("dataSourceType")} required name='targetType' rules={[{ required: true, message: i18n.t("pleaseSelectDataSourceType") }]}>
            <Select
              style={{ width: 150 }}
              placeholder={i18n.t("pleaseSelectDataSourceType")}
              disabled
              options={connectionTypeSelectOption}
            />
          </Form.Item>
          <Form.Item label={i18n.t("databaseConnection")} required name='targetId' rules={[{ required: true, message: i18n.t("pleaseSelectDatabaseConnection") }]}>
            <Select
              loading={connectionLoading}
              style={{ width: 150 }}
              placeholder={i18n.t("pleaseSelectDatabaseConnection")}
              options={connectionList}
              dropdownMatchSelectWidth={false}
              onChange={async(_, options: any) => {
                setSourceConnectionInfo(options.props);
                //清空已选源信息
                await dispatch(setTargetNodeInfos([]));
                await dispatch(setTableFieldMap([]));
                await dispatch(updateTargetContexts([]));
                await dispatch(updateTestConnectionState({
                  ...testConnectionState,
                  target: false
                }))
              }}
            />

          </Form.Item>
          <Form.Item>
            <Button
              type="link"
              size="small"
              onClick={() => beginTestConnection()}
            >
              <Badge status={testConnectionState?.target ? 'success' : 'default'} />{i18n.t("testConnection")}
            </Button>
          </Form.Item>
        </Form>
        <DynamicTable
          form={form}
          dataReplicationType={dataReplicationType }
          isTarget={true} //目标
          sourceType={form.getFieldValue('sourceType')}
          selectedConnectionInfo={sourceConnectionInfo}
          dataSource={_.cloneDeep(targetNodeInfos)}
          setDataSource={async(data: any) => {
            await dispatch(setTargetNodeInfos(data));
            await dispatch(setTableFieldMap([]))
            await dispatch(updateTargetContexts([]));
          } }
        />
      </div>
    </div>
  )
})

export default TargetConnectionContent