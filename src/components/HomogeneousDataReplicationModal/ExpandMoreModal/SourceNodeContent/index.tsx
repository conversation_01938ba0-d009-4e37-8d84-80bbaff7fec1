import React, { useState } from "react";
import { message, Radio } from 'antd';
import { useDispatch, useSelector, useRequest } from 'src/hook';
import { 
  updateTestConnectionState,
  setSourceNodeInfos,
  setTargetNodeInfos
} from 'src/store/extraSlice/hdrSlice';
import {
  queryDataDuplicationConnections
} from 'src/api';
import { testConnect } from "src/api/dataTransfer";
import {  formatSelectOptionsWithIcon } from '../../utils'
import SourceObjectContent from './SourceConnectionContent';
import TargetConnectionContent from './TargetConnectionContent';
import { DATA_REPLICATION_TYPE, DATA_REPLICATION_TYPES, getDataReplicationTypes } from '../../contstant';
import styles from '../index.module.scss';
import i18n from 'i18next';
const SourceNodeContent = ({
  form,
  dataReplicationType,
  setDataReplicationType
}: {
  form: any;
  dataReplicationType: 'multiTable' | 'singleTable'| 'schema',
  setDataReplicationType: (type: DATA_REPLICATION_TYPE)  => void;
}) => {

  const dispatch = useDispatch();
  const { testConnectionState } = useSelector(state => state.hdrGuide);
  //连接列表
  const [connectionList, setConnectionList] = useState<any[]>([]);

  //获取连接列表
  const { run: runQueryConnection, loading: connectionLoading } = useRequest(queryDataDuplicationConnections, {
    manual: true,
    onSuccess: (res) => {
      setConnectionList(res)
    },
    formatResult(res) {
      const options = formatSelectOptionsWithIcon(res || []);
      return options;
    },
  });
  // 测试连接 testConnect
  const { run: runTestConnection } = useRequest(testConnect, {
    manual: true,
    onSuccess: () => {
      message.success(i18n.t("testSuccess"));
    }
  });

  const saveTestConnectionState = async(params: any, type: 'source' | 'target') => {
    const res = await runTestConnection(params);
    dispatch(updateTestConnectionState({...testConnectionState, [type]: !!res}))
  }

  return (
    <div className={styles.confirmRepInfoContent}>
      <Radio.Group  
         buttonStyle="solid" 
         optionType="button"
         className="mb10"
         value={dataReplicationType} 
         onChange={async e => {
          setDataReplicationType(e.target.value);
          await dispatch(setSourceNodeInfos([]));
          await dispatch(setTargetNodeInfos([]));
         }}
         options={Object.keys(DATA_REPLICATION_TYPES).map(type => ({label: getDataReplicationTypes()?.[type], value: type}))}
         />
      <div className={styles.stepTitle}>
        <div className={styles.stepIcon}>1</div>
        <h3>{i18n.t("selectSourceAndTarget")}</h3>
      </div>
      <div className={styles.confirmNodeInfo}>
        <SourceObjectContent
          form={form}
          connectionLoading={connectionLoading}
          connectionList={connectionList}
          dataReplicationType={dataReplicationType}
          updataConnectionList={(params: any) => setConnectionList(params)}
          runQueryConnection={(type: string) => runQueryConnection(type)}
          runTestConnection={(params: any,type: 'source'| 'target') => saveTestConnectionState(params, type)}

        />
        <TargetConnectionContent
          form={form}
          connectionLoading={connectionLoading}
          connectionList={connectionList}
          dataReplicationType={dataReplicationType}
          runTestConnection={(params: any,type: 'source'| 'target') => saveTestConnectionState(params, type)}
        />
      </div>
    </div>
  )
}

export default SourceNodeContent