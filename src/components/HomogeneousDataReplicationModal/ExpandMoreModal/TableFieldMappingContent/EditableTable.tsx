import React, { memo, useContext, useEffect, useState } from 'react'
import { Table, Form, Select, Checkbox, Tooltip } from 'antd'
import { FormInstance } from 'antd/lib/form'
import styles from './index.module.scss'
import { cloneDeep } from 'lodash'
import i18n from 'i18next';

const EditableContext = React.createContext<FormInstance<any> | null>(null)

interface Item {
  key: string | number
  sourceFields: string
  targetFields: string
  primary: string
  checked?: boolean
}

interface EditableRowProps {
  index: number
}

const EditableRow: React.FC<EditableRowProps> = ({ index, ...props }) => {
  const [form] = Form.useForm()
  return (
    <Form form={form} component={false}>
      <EditableContext.Provider value={form}>
        <tr {...props} />
      </EditableContext.Provider>
    </Form>
  )
}

interface EditableCellProps {
  title: React.ReactNode
  editable: boolean
  children: React.ReactNode | null
  dataIndex: keyof Item
  record: Item
  type?: 'input' | 'select' | 'checkbox'
  options?: any[]
  handleSave: (record: Item) => void
}

const EditableCell: React.FC<EditableCellProps> = ({
  title,
  editable,
  children,
  dataIndex,
  record,
  type,
  options,
  handleSave,
  ...restProps
}) => {
  const [editing, setEditing] = useState(false)
  const form = useContext(EditableContext)!

  const toggleEdit = () => {
    setEditing(!editing)
    form.setFieldsValue({ [dataIndex]: record[dataIndex] })
  }

  const save = async () => {
    try {
      const values = await form.validateFields()

      toggleEdit()
      handleSave({ ...record, ...values })
    } catch (errInfo) {
      console.log('Save failed:', errInfo)
    }
  }

  let childNode = children

  if ((editable && dataIndex.includes('sourceFields')) || (editable && dataIndex.includes('targetFields')&& record[dataIndex])) {
    childNode = editing ? (
      <>
        {type === 'select' && (
          <Form.Item
            name={dataIndex}
            className="editable-cell-select"
          >
            <Select onBlur={save} options={options}></Select>
          </Form.Item>
        )}
      </>
    ) : (
      <div
        className="editable-cell-value-wrap"
        onClick={toggleEdit}
      >
        {children}
      </div>
    )
  }

  if (type === 'checkbox') {
    return <td {...restProps}>
      <Checkbox checked={record?.checked} onChange={e => {
        //@ts-ignore
        handleSave({ ...record, checked: e.target.checked })
      }} />
    </td>
  }
  return <td {...restProps}>{childNode}</td>
}

type EditableTableProps = Parameters<typeof Table>[0]

interface DataType {
  key: React.Key | number
  sourceFields: string | null
  targetFields: string | null
  checked?: boolean
}

interface ExtraProps extends EditableTableProps {
  loading: boolean;
  tableFieldMap: any;
  setTableFieldMap: any
  formattedColumns: any[]
}

type ColumnTypes = Exclude<EditableTableProps['columns'], undefined>


export const EditableTable = memo(({
  loading,
  tableFieldMap,
  formattedColumns,
  setTableFieldMap
}: ExtraProps) => {

  const defaultColumnes = [
    {
      title: i18n.t("sourceField"),
      dataIndex: 'sourceFields',
      editable: true,
      type: 'select',
    },
    {
      title: i18n.t("targetField"),
      dataIndex: 'targetFields',
      editable: true,
      type: 'select',
    },
    {
      title: i18n.t("whetherToMap"),
      fixed: 'right',
      width: 180,
      editable: true,
      type: 'checkbox',
      dataIndex: 'operation',
    },
  ]

  const [columns, setColumns] = useState<any>(defaultColumnes)

  const getDefaultTableColumn = (fColumns: any[]) => {
    const actionColumn = columns[columns?.length - 1]

    const newColumns = fColumns?.map((column) => {
      const match = column?.key?.match(/(\d+)$/);
      const number = Number(match?.[0]||0);
      return  (
        {
          title: column?.key?.includes('sourceFields') ? `${i18n.t("source")} ${number +1} ${i18n.t("field")}` : `${i18n.t("target")} ${number +1} ${i18n.t("field")}`,
          dataIndex: column?.key,
          editable: true,
          type: 'select',
          options:column?.options || [],
          children: [{
            title: <Tooltip title={column?.subTitle}>{column?.subTitle}</Tooltip> ,
            dataIndex: column?.key,
          }]
        }
      )
    })
    //@ts-ignore
    return newColumns.concat([actionColumn])
  }

  useEffect(() => {
    const newColumns = getDefaultTableColumn(formattedColumns)

    setColumns(newColumns)


  }, [JSON.stringify(formattedColumns)])

  const updateFieldMap = (newData: any[]) => {
    setTableFieldMap(newData)
  }

  const handleSave = (row: DataType) => {

    const newData = cloneDeep(tableFieldMap);
    const index = newData.findIndex((item: any) => row.key === item.key)
    const item = newData[index]
    newData.splice(index, 1, {
      ...item,
      ...row,
    })
    updateFieldMap(newData)
  }


  const components = {
    body: {
      row: EditableRow,
      cell: EditableCell,
    },
  }
  const tableColumns = columns.map((col: any) => {
    if (!col.editable) {
      return col
    }

    return {
      ...col,
      onCell: (record: DataType) => ({
        record,
        editable: col.editable,
        type: col.type,
        options: col?.options,
        dataIndex: col.dataIndex,
        title: col.title,
        handleSave: handleSave,
      }),
    }
  })

  return (
    <Table
      loading={loading}
      components={components}
      rowClassName={() => 'editable-row'}
      bordered
      scroll={{ x: 'max-content', y: 260 }}
      dataSource={tableFieldMap}
      columns={tableColumns}
      size="small"
      className={styles.editableTable}
      pagination={false}
    />
  )
})
