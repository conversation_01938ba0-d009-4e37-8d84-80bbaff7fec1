import React, { useEffect, useState, memo } from "react";
import { postSourceTableFields } from 'src/api';
import { useDispatch, useSelector } from 'src/hook';
import { setTableFieldMap } from 'src/store/extraSlice/hdrSlice';
import { renderConnectionPath } from 'src/components/HomogeneousDataReplicationModal/utils'
import { EditableTable } from "./EditableTable";
import { THREE_TIER_CONNECTION_TYPES } from '../../contstant';
import styles from './index.module.scss';
import i18n from 'i18next';

interface ITableColumnItem {
  label: string;
  value: string;
  type: string;
}
const TableFieldMappingContent = memo(({
  isShowFieldMapping = false
}: {
  isShowFieldMapping: boolean
}) => {


  const dispatch = useDispatch();

  const { copySourceTexts,tableFieldMap, targetContexts = [] } = useSelector(state => state.hdrGuide);
  //存储列options
  const [columnOptions, setColumnOptions] = React.useState<any>([]);
  const [loading, setLoading] = useState(false);

  const formatTableFields = (data: any, type: string) => {
    return data?.map((d: any) => {
      return {
        label: `${d.columnName}(${d.columnTypeName})`,
        value: `${d.columnName}(${d.columnTypeName})`,
        type
      }
    })
  }

  const getDefaultDataSource = (...arrays: ITableColumnItem[][]) => {
    const result = [];

    // 获取最长数组的长度
    const maxLength = Math.max(...arrays.map(arr => arr.length));

    for (let i = 0; i < maxLength; i++) {
      const newItem = { key: i };

      for (const arr of arrays) {
        if (i < arr.length) {
          const iItem = arr[i]
          //@ts-ignore
          newItem[`${iItem?.type}`] = iItem?.label;
        }
      }

      // 如果 newItem 有值，则推入结果
      if (Object.keys(newItem).length > 0) {
        //@ts-ignore
        const allFieldValues = Object.keys(newItem).filter(key => key.includes('Fields')).map(f => newItem[f])
        result.push({
          ...newItem,
          checked: allFieldValues?.length === arrays.length && allFieldValues.every(value => value === allFieldValues[0])
        });
      }
    }

    return result;
  }

  const fetchDataForItem = (item: any) => {
    return new Promise((resolve) => {
      postSourceTableFields({
        connectionId: item?.connectionId,
        tableName: item?.table,
        dataSourceType: item?.connectionType,
        connectionName: item?.connectionName,
        nodePathWithType: item?.nodePathWithType,
        nodePath: THREE_TIER_CONNECTION_TYPES.includes(item?.connectionType) ? 
        `/root/${item?.connectionId}/${item?.database}/${item?.schema}/${i18n.t("table")}/${item?.table}`:
        `/root/${item?.connectionId}/${item?.schema}/${i18n.t("table")}/${item?.table}`
      }).then(resolve).catch(() => resolve([]))
    });
  };
  const fetchAllData = async () => {
    setLoading(true);
    
    const sourceNodes = copySourceTexts?.sourceContexts || [];
    const sLength = sourceNodes.length;
    const allRequests = [...sourceNodes, ...targetContexts].map(item => fetchDataForItem(item))

    try {
     
      const resultsArray = await Promise.all(allRequests);
      let response: any = [];
      const newColumnsWithOptions = [...sourceNodes, ...targetContexts].map((item, index) => {
        const key = sLength >index ? `sourceFields${index}`:  `targetFields${index - sLength}`;
        const formatColumn = formatTableFields(resultsArray[index],key);
        response.push(formatColumn);
        return  ({
          key,
          subTitle: renderConnectionPath(item?.connectionType, item?.connectionName, item?.database, item?.schema, item?.table),
          options:  formatColumn
        })
      })
      
      if (!tableFieldMap?.length) {
        const columnOptions = response && Object.keys(response)?.map(col => response[col])
        const newTableMap = getDefaultDataSource(...columnOptions);
        dispatch(setTableFieldMap(newTableMap))
      }else {
        dispatch(setTableFieldMap(tableFieldMap))
      }
     
      setColumnOptions(newColumnsWithOptions);
     
    } catch (error) {
      console.error('Error fetching data:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (targetContexts?.length && copySourceTexts?.sourceContexts?.length && isShowFieldMapping) {
      //获取所有表字段
      fetchAllData();
    }
  
  }, [JSON.stringify(copySourceTexts?.sourceContexts),JSON.stringify(targetContexts), isShowFieldMapping])

  return (
    <div className={styles.confirmRepInfoContent}>
      <div className={styles.stepTitle}>
        <div className={styles.stepIcon}>2</div>
        <h3>{i18n.t("confirmFieldMapping")}</h3>
      </div>
      <div className={styles.confirmNodeInfo}>
        <EditableTable
          key={JSON.stringify('')}
          loading={loading}
          rowKey="sourceFields"
          tableFieldMap={tableFieldMap}
          setTableFieldMap={(res: any) => { dispatch(setTableFieldMap(res))}}
          formattedColumns={columnOptions}
        />
      </div>
    </div>
  )
})

export default TableFieldMappingContent