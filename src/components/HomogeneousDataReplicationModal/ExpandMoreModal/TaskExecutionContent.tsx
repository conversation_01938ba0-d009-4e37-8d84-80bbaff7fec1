import React, { useEffect } from "react";
import { Radio, Form, Input } from 'antd';
import { useDispatch, useSelector } from 'src/hook';
import { updateTaskExecuteInfo } from 'src/store/extraSlice/hdrSlice';
import styles from './index.module.scss';
import i18n from 'i18next';

const ConfirmRepInfoContent = ({
  form,
  dataReplicationType
}: {
  form: any;
  dataReplicationType: any
}) => {

  const dispatch = useDispatch();

  const { taskExecuteInfo } = useSelector(state => state.hdrGuide);

  useEffect(() => {
    if (taskExecuteInfo) {
      form.setFieldsValue({ ...taskExecuteInfo })
    }
  }, [form, taskExecuteInfo])
 
  return (
    <div className={styles.confirmRepInfoContent}>
      <div className={styles.stepTitle}>
        <div className={styles.stepIcon}>3</div>
        <h3>{i18n.t("taskExecution")}</h3>
      </div>
      <div className={styles.confirmNodeInfo}>
        <Form form={form} labelCol={{ span: 5 }} wrapperCol={{ span: 7 }}
          onValuesChange={async(filed, allValues) => {
            await dispatch(updateTaskExecuteInfo(allValues));
          }}
        >
          <Form.Item className={styles.formItemStyle} label={i18n.t("executionMethod")} initialValue={true} valuePropName='checked'>
            <Radio disabled checked/>{i18n.t("executeImmediately")}
          </Form.Item>
          <Form.Item
            required
            className={styles.formItemStyle}
            label={i18n.t("taskName")}
            name='taskName'
            rules={[{ required: true, message: i18n.t("pleaseEnterTaskName") }]}
          >
            <Input placeholder={i18n.t("pleaseEnterTaskName")} />
          </Form.Item>
          {
           dataReplicationType === 'multiTable' &&
            <Form.Item className={styles.formItemStyle} label={i18n.t("allowDataCoverage")} name='overwriteTarget' initialValue={true}>
              <Radio.Group options={[{ label: i18n.t("yes"), value: true }, { label: i18n.t("no"), value: false}]} />
            </Form.Item>
          }
        </Form>
      </div>
    </div>
  )
}

export default ConfirmRepInfoContent