
.hdrManualModal {
  :global {
    .ant-modal-header {
      padding: 8px 18px;
    }
    .ant-modal-body {
      padding: 16px;
    }
  }

  .modalTitle {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  
}
.confirmRepInfoContent {
  .confirmNodeInfo {
    padding: 0 20px;

    .sourceIndex {
      color:#ccc;
    }
     
  }
  .subTitle {
    padding: 4px 0px;
    &::before {
			content: "";
			width: 4px;
			height: 19px;
			background-color: #3262ff;
			display: inline-block;
			vertical-align: middle;
			margin-right: 16px;
		}
  }
  .asyncTable {
    :global {
      .ant-table-body .ant-table-tbody {
        .ant-table-placeholder .ant-empty-normal{
          margin: 8px 0px;
        }
      }
      
    }
  }
}

.mb20{
  margin-bottom: 20px;
}

.lh32 {
  line-height: 32px;
}
.commonEleWith {
  min-width: 150px!important;
  max-width: 200px;
}
.fixedEleWidth {
  width: 100%;
}
//下拉内容
.sourceContainer {
  position: relative;
  .addSourceBtn {
    position: absolute;
    right: 0;
    top: -46px;
    text-align: right;
  }
}

.stepTitle {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  .stepIcon {
    background-color: #1677ff;
    width: 26px;
    height: 26px;
    line-height: 26px;
    text-align: center;
    border-radius: 50%;
    color:#fff;
    margin-right: 10px;
    margin-bottom: 10px;
  }
}

.formItemStyle {
  :global {
    .ant-form-item-no-colon {
      color: #ccc;
    }
  }
}
.connectionTestStatus {
  cursor: pointer;
  margin-top: 10px;
}