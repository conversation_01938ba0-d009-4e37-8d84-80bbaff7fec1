import React, { useState, memo, useMemo } from "react";
import * as _ from 'lodash';
import { Modal, Space, Button, Form, message } from 'antd';
import { useDispatch, useSelector } from 'src/hook';
import ConfirmRepInfoContent from './ConfirmRepInfoContent';
import TaskExecutionContent from './TaskExecutionContent';
import SourceNodeContent from './SourceNodeContent';

import {
  updateTaskExecuteInfo,
  updateCopySourceContexts,
  setTargetNodeInfos,
  updateTargetContexts,
} from 'src/store/extraSlice/hdrSlice';
import { DataSourceType } from "src/types";
import TableFieldMappingContent from './TableFieldMappingContent';
import { THREE_TIER_CONNECTION_TYPES, DATA_REPLICATION_TYPE } from '../contstant';
import styles from './index.module.scss'
import i18n from 'i18next';

const ExpandMoreModal = memo((
  {
    isShortcut = false,
    currentStep,
    dataReplicationType,
    setDataReplicationType,
    onPopupModal,
    onSubmit,
    onClose,
    onSwitchShortcutMethod,
  }: {
    isShortcut: boolean;
    currentStep: number;
    dataReplicationType: DATA_REPLICATION_TYPE;
    onPopupModal: (strp: number) => void;
    onSubmit: () => void;
    onClose: () => void;
    onSwitchShortcutMethod: () => void;
    setDataReplicationType: (type: DATA_REPLICATION_TYPE) => void;
  }
) => {

  const [form] = Form.useForm();
  //源与目标
  const [sourceAndTargetForm] = Form.useForm();
  const [taskExecuteInfoForm] = Form.useForm();

  const dispatch = useDispatch();

  const [modalCurStrep, setModalCurStep] = useState(currentStep);

  const { sourceNodeInfos, targetNodeInfos, copySourceTexts, tableFieldMap, taskExecuteInfo, testConnectionState } = useSelector(state => state.hdrGuide);
  //表映射 目标字段是否重复
  const checkTragetTableFieldIsDuplicated = (targetTableFieldArr: any) => {
    const seen = new Map();

    for (const obj of targetTableFieldArr) {

      for (const key of Object.keys(obj)) {
        if (key.includes('targetFields')) {
          const value = obj[key];
          const identifier = `${key}:${value}`;

          if (seen.has(identifier)) {
            const match = key.match(/(\d+)$/g);
            const targetNum = Number(match?.[0] ?? 0);

            return `${i18n.t("targetField")} ${targetNum + 1}`; // 返回重复的字段和对应的值
          }

          seen.set(identifier, true);
        }

      }
    }
    return null; // 没有找到重复
  }

  const onSubmitDataDuplicationFrom = async () => {
    taskExecuteInfoForm.validateFields().then(() => {
      onSubmit()
    })
  }

  const onVerifyForm = () => {
    if (modalCurStrep === 3 && dataReplicationType !== 'schema') {
      //表映射 校验目标不能重复
      const repeatingKey = checkTragetTableFieldIsDuplicated(tableFieldMap);
      if (repeatingKey) {
        return message.warning(`${repeatingKey}${i18n.t("cannotHaveDuplicateFields")}`)
      }
    }
    taskExecuteInfoForm.validateFields().then(async (values) => {
      if (modalCurStrep === 4) {
        await dispatch(updateTaskExecuteInfo({ ...taskExecuteInfo, taskName: values?.taskName }))
      }
      onPopupModal(modalCurStrep);
      taskExecuteInfoForm.resetFields();
    })
  }

  const formatTableFieldMappingOriginData = (nodeItem: any, connectionType: DataSourceType, connectionId: number) => {
    //兼容目标 可只选到schema情况
    const basicSchemaNodePathWithType = THREE_TIER_CONNECTION_TYPES.includes(connectionType)
      ? `/CONNECTION:${connectionId}/DATABASE:${nodeItem.database}/SCHEMA:${nodeItem.schema}`
      : `/CONNECTION:${connectionId}/SCHEMA:${nodeItem.schema}`;

    const baseParams = {
      connectionId,
      connectionType,
      nodePathWithType: basicSchemaNodePathWithType,
      schema: nodeItem.schema,
      database: nodeItem?.database,
      connectionName: nodeItem.connectionName,
    };

    if (!nodeItem?.table) {
      return baseParams;
    } else if (nodeItem?.table && _.isArray(nodeItem.table)) {
      return nodeItem?.table?.map((t: string) => ({
        ...baseParams,
        table: t,
        nodePathWithType: `${basicSchemaNodePathWithType}/TABLE:${t}`,
      }));
    } else {
      return {
        ...baseParams,
        table: nodeItem?.table,
        nodePathWithType: `${basicSchemaNodePathWithType}/TABLE:${nodeItem?.table}`,
      };
    }

  }
  const saveManualStep1Data = async () => {

    //校验
    sourceAndTargetForm.validateFields().then(async (values) => {

      const cloneSourceData = _.cloneDeep(sourceNodeInfos);
      let tableInfos = cloneSourceData.flatMap(i => formatTableFieldMappingOriginData(i, values?.sourceType, values?.sourceId));
      // 存储第二步需要的数据
      await dispatch(updateCopySourceContexts({
        ...copySourceTexts,
        sourceContexts: tableInfos
      }))


      let cloneTargetNodeInfos = _.cloneDeep(targetNodeInfos)
      cloneTargetNodeInfos = cloneTargetNodeInfos.map(i => ({
        ...i,
        connectionName: i?.connectionName,
        connectionType: values?.targetType,
        nodePathWithType: THREE_TIER_CONNECTION_TYPES.includes(values?.sourceType) ?
          `/CONNECTION:${values?.targetId}/DATABASE:${i?.database}/SCHEMA:${i?.schema}` :
          `/CONNECTION:${values?.targetId}/SCHEMA:${i?.schema}`
      }))
      await dispatch(setTargetNodeInfos(cloneTargetNodeInfos));
      //当表-表映射时候 需要存储
      if (dataReplicationType !== 'schema') {
        const formatTargetNodeInfos = cloneTargetNodeInfos.flatMap((target) => formatTableFieldMappingOriginData(target, values?.targetType, values?.targetId))
        await dispatch(updateTargetContexts(formatTargetNodeInfos));
      }

      setModalCurStep(modalCurStrep + 1);
    })
  }

  const onCheckNodeBeforeNextStep = () => {
    if (!targetNodeInfos?.length) {
      return message.warning(i18n.t("pleaseSelectTargetInformation"))
    }
    if (!sourceNodeInfos?.length) {
      return message.warning(i18n.t("pleaseSelectSourceInformation"))
    }
    //测试校验
    if (!testConnectionState?.source) {
      return message.warning(i18n.t("pleaseTestSourceConnection"))
    }
    if (!testConnectionState.target) {
      return message.warning(i18n.t("pleaseTestTargetConnection"))
    }
    saveManualStep1Data()
  }
  const onNextStep = async () => {

    if (modalCurStrep === 1) {
      onCheckNodeBeforeNextStep();
    } else if (modalCurStrep === 2 && dataReplicationType !== 'schema') {
      //字段映射 校验目标不能重复
      const repeatingKey = checkTragetTableFieldIsDuplicated(tableFieldMap);
      if (repeatingKey) {
        return message.warning(`${repeatingKey}${i18n.t("cannotHaveDuplicateFields")}`)
      }
      //手动方式 映射必须有选中项
      const hasMappedField = tableFieldMap.some(table => table?.checked);
      if (!hasMappedField) {
        return message.warning(i18n.t("pleaseSelectFieldsToMap"))
      }
      setModalCurStep(modalCurStrep + 1);
    } else {
      setModalCurStep(modalCurStrep + 1);
    }

  }

  const isShowTableFieldMappingComponent = useMemo(() => {
    return (
      (modalCurStrep === 2 && ['multiTable', 'singleTable'].includes(dataReplicationType))
      || (isShortcut && dataReplicationType !== 'schema' && modalCurStrep == 3)
    )
  }, [modalCurStrep, isShortcut, dataReplicationType])

  return (
    <Modal
      closable={false}
      visible={true}
      width={800}
      maskClosable={!isShortcut}
      onCancel={() => onClose()}
      className={styles.hdrManualModal}
      title={

        <div className={styles.modalTitle}>
          <div>{i18n.t("newDataCopy")}</div>
          {
            isShortcut ? <Button type="link" onClick={() => onVerifyForm()}>{i18n.t("shrink")}</Button>
              : <>
                {modalCurStrep === 1 && <Button type="link" onClick={() => onSwitchShortcutMethod()}>{i18n.t("quickSelection")}</Button>}
              </>
          }
        </div>
      }
      footer={
        //完全区分开
        isShortcut ? <div>
          {modalCurStrep === 3 && <Button type="primary" onClick={() => {
            //校验table映射zhuang是否重复
            if (modalCurStrep === 3 && dataReplicationType !== 'schema') {
              //表映射 校验目标不能重复
              const repeatingKey = checkTragetTableFieldIsDuplicated(tableFieldMap);
              if (repeatingKey) {
                return message.warning(`${repeatingKey}${i18n.t("cannotHaveDuplicateFields")}`)
              }
            }
            setModalCurStep(modalCurStrep + 1)
          }}>{i18n.t("nextStep")}</Button>}
          {modalCurStrep === 4 && <Button type="primary" onClick={() => setModalCurStep(modalCurStrep - 1)}>{i18n.t("previousStep")}</Button>}
          {modalCurStrep === 4 && <Button type="primary" onClick={() => onSubmitDataDuplicationFrom()}>{i18n.t("confirm")}</Button>}
        </div> :
          <Space>
            {modalCurStrep === 1 && <Button onClick={() => onClose()}>{i18n.t("cancel")}</Button>}
            {modalCurStrep !== 1 && <Button onClick={() => { setModalCurStep(modalCurStrep - 1) }}>{i18n.t("previousStep")}</Button>}
            {modalCurStrep < 3 && <Button type="primary" onClick={() => onNextStep()}>{i18n.t("nextStep")}</Button>}
            {modalCurStrep === 3 && <Button type="primary" onClick={() => onSubmitDataDuplicationFrom()}>{i18n.t("confirm")}</Button>}
          </Space>
      }
    >
      {/* 手动设置源与目标 */}
      <div style={{ display: modalCurStrep === 1 ? 'block' : 'none' }}>
        <SourceNodeContent
          form={sourceAndTargetForm}
          dataReplicationType={dataReplicationType}
          setDataReplicationType={(type: DATA_REPLICATION_TYPE) => {
            setDataReplicationType(type)
          }}
        />
      </div >
      {/* 表 =》表 字段映射*/}
      <div
        style={{
          display: isShowTableFieldMappingComponent ? 'block' : 'none'
        }}>
        <TableFieldMappingContent
          isShowFieldMapping={isShowTableFieldMappingComponent}
        />
      </div >

      {
        (
          (modalCurStrep === 2 && !isShortcut && dataReplicationType === 'schema')
          || (isShortcut && modalCurStrep === 3 && dataReplicationType === 'schema'))
        && (<ConfirmRepInfoContent form={form} targetNodeInfos={targetNodeInfos} />)
      }

      {
        ((modalCurStrep === 3 && !isShortcut) || (isShortcut && modalCurStrep === 4)) && (
          <TaskExecutionContent form={taskExecuteInfoForm} dataReplicationType={dataReplicationType} />
        )
      }

    </Modal >
  )
})

export default ExpandMoreModal