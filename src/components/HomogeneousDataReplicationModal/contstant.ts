import i18n from "i18next";
//四大数据源
export const SUPPROT_CONNECTION_TYPES = ['MySQL', 'PostgreSQL', 'Oracle', 'SQLServer', 'OracleCDB','OceanBaseMySQL'];

//数据源三层结构
export const THREE_TIER_CONNECTION_TYPES= ['PostgreSQL', 'SQLServer', 'OracleCDB']

export const getDataReplicationTypes = (): Record<any, string> => {
  return {
    'multiTable': i18n.t("singleToMultiTable"),
    'singleTable': i18n.t("multiToSingleTable"),
    'schema': i18n.t("tableSchema"),
  }
}

export const DATA_REPLICATION_TYPES = getDataReplicationTypes();

export type DATA_REPLICATION_TYPE =  keyof typeof DATA_REPLICATION_TYPES;