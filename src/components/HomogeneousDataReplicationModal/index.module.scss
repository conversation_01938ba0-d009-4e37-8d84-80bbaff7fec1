.hdrModal {
  .hdrMask {
    position: fixed;
    width: 100%;
    height: 100%;
    inset: 0;
    background-color: rgba(0, 0, 0, 0.45);
    pointer-events: none;
    z-index: 800;
  }

  .dfrContent {
    position: relative;
    z-index: 900;
    background-color: #fff;
    
    [data-theme='dark'] & {
      background-color: #3a3a42;
    }
  }
}

.mb14 {
  margin-bottom: 14px;
}

.hdrStepModal {
  position: absolute;
  .modalTitle {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  :global {
    .ant-modal-body {
      padding: 18px;
      padding-bottom: 0px;
    }

    .ant-modal-footer {
      border-top: none;
    }

    .ant-modal-close .ant-modal-close-x {
      width: 40px;
      height: 40px;
    }
  }

  .arrow {
    position: absolute;
    top: 20%;
    /* 垂直居中 */
    left: -20px;
    /* 调整箭头的位置 */
    transform: translateY(-50%);
    /* 垂直居中 */
    border: solid transparent;
    /* 透明边框 */
    border-width: 10px;
    /* 箭头大小 */
    border-right-color: white;
    /* 箭头颜色 */
  }

  .stepContent {
    .requiredTip {
      margin-bottom: 10px;
    }

    .nodeContent {
      width: 100%;

      .nodeItem {
        max-width: 95%;
        display: inline-block;
        margin: 4px 4px;
      }
    }

    .confirmNodeContent {
      .confirmTip {
        color: #667084;
        font-size: 12px;
      }
    }

    .executeNodeContent {
      .input {
        width: 200px;
      }
    }
  }
}