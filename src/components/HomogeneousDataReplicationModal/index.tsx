import React, { useEffect, useMemo, useState, memo, useRef, useCallback } from "react";
import { ExclamationCircleFilled } from '@ant-design/icons';
import * as _ from 'lodash';
import classNames from "classnames";
import { Button, Radio, Row, Col, Modal, Space, message, Tag, Checkbox, Input } from 'antd';
import { setMulSelectedNodes, setSelectedNode } from 'src/pageTabs/queryPage/sdt/sdtSlice';
import {
  setHdrVisible,
  setSourceNodeInfos,
  setTargetNodeInfos,
  updateTaskExecuteInfo,
  updateCopySourceContexts,
  onClearAllInfo,
  updateTargetContexts,
  setTableFieldMap
} from 'src/store/extraSlice/hdrSlice';
import { createDataDuplication } from 'src/api';
import { useDispatch, useSelector, useRequest } from "src/hook";
import { SUPPROT_CONNECTION_TYPES, DATA_REPLICATION_TYPE } from './contstant';
import ExpandMoreModal from './ExpandMoreModal';
import { renderDatabaseElementIcon, getNodeTypeValueByPath } from './utils';
import styles from './index.module.scss';
import i18n from 'i18next';

const HomogeneousDataReplicationModal = memo(({
  children,
}: {
  children: React.ReactNode;
}) => {

  const STEP_CONTENT_MAPPING: { [k in number]: string } = {
    1: i18n.t("selectSource"),
    2: i18n.t("selectTarget"),
    3: i18n.t("confirmCopyInfo"),
    4: i18n.t("taskExecution")
  }


  const dispatch = useDispatch()
  const { mulSelectedNodes, selectedNode, treeData, } = useSelector((state) => state.sdt)
  const { userId } = useSelector((state) => state.login.userInfo)
  const { hdrVisible, taskExecuteInfo, sourceNodeInfos, targetNodeInfos, copySourceTexts, targetContexts, tableFieldMap } = useSelector(state => state.hdrGuide);

  const hdrModalRef = useRef<any>();
  //快捷方式弹框定位
  const [shortcutModalPositionLeft, setShortcutModalPositionLeft] = useState(320);
  const [visibleModal, setVisibleModal] = useState(true);
  //手动弹框状态
  const [visibleExpandModal, setVisibleExpandModal] = useState(false)
  const [curStep, setCurStep] = useState(1);
  //是否为快捷模式
  const [isShortcutMode, setIsShortcutMode] = useState(true);

  //源与目标类型
  const [dataReplicationType, setDataReplicationType] = useState<DATA_REPLICATION_TYPE>('multiTable')

  //提交
  const { run: runReateDataDuplication } = useRequest(createDataDuplication, {
    manual: true,
    onSuccess: () => {
      message.success(i18n.t("createDataCopyTaskSuccess"))
      dispatch(setHdrVisible(false));
      dispatch(onClearAllInfo());
      onClose()
    }
  })

  const handleResize = () => {
    if (hdrModalRef?.current) {
      setShortcutModalPositionLeft(hdrModalRef?.current?.offsetWidth);
    }
  };

  //监听sdt宽度变化
  useEffect(() => {

    const resizeObserver = new ResizeObserver(handleResize);

    if (hdrModalRef.current) {
      resizeObserver.observe(hdrModalRef.current);
    }

    return () => {
      if (hdrModalRef.current) {
        resizeObserver.unobserve(hdrModalRef.current);
      }
    };
  }, []);

  useEffect(() => {

    if (!hdrVisible) return;

    if ((!_.isEmpty(selectedNode) && !isSupportConnectionTypes([selectedNode])) || (mulSelectedNodes?.length && !isSupportConnectionTypes(mulSelectedNodes))) {
      return message.info(i18n.t("currentlyOnlySupportMySQLPostgreEtcCopy"))
    }
    if (curStep === 1) {
      if (mulSelectedNodes?.length && isAllSourceTable(mulSelectedNodes, 'table')) {
        if (isSameConnectionId(mulSelectedNodes)) {
          dispatch(setSourceNodeInfos(mulSelectedNodes));
          dispatch(setTargetNodeInfos([]));
          dispatch(setTableFieldMap([]))
        } else {
          return message.warning(i18n.t("pleaseSelectSameConnectionSource"))
        }

        //单选
      } else if (!mulSelectedNodes?.length && selectedNode && isAllSourceTable([selectedNode], 'table')) {
        dispatch(setSourceNodeInfos([selectedNode]));
        dispatch(setTargetNodeInfos([]));
      } else if (!sourceNodeInfos?.length) {

        dispatch(setSourceNodeInfos([]));
      }
    }
    if (curStep === 2) {
      //根据源数量判断目标类型
      // 1. 同种类型 2. source 为多目标只能一个表或schema 3. source为单 一个schema或者多个表
      const ml = mulSelectedNodes?.length;
      const sourceLength = sourceNodeInfos?.length;
      const sourceConnectionType = sourceNodeInfos?.[0]?.connectionType;

      if ((ml && sourceConnectionType !== mulSelectedNodes?.[0]?.connectionType) || (!_.isEmpty(selectedNode) && sourceConnectionType !== selectedNode?.connectionType)) {
        return message.warning(i18n.t("pleaseKeepSourceAndTargetSameDataSourceType"))
      }

      if (sourceLength === 1) {
        // 目标: （多|单）表 或 单schema

        if (ml > 1) {
          //只能为表
          if (!isAllSourceTable(mulSelectedNodes, 'table') || !isSameConnectionId(mulSelectedNodes)) {
            return message.warning(i18n.t("singleTableSourceTargetRestriction"))
          } else {
            dispatch(setTargetNodeInfos(mulSelectedNodes));

          }
        }
        //目标为schema 或单表
        if (!ml && selectedNode && (isAllSourceTable([selectedNode], 'table') || (isAllSourceTable([selectedNode], 'schema') || isAllSourceTable([selectedNode], 'oracleUser') || (selectedNode?.connectionType === 'MySQL' && isAllSourceTable([selectedNode], 'database'))))) {
          dispatch(setTargetNodeInfos([selectedNode]))

        }
      } else {
        // 目标: 单表|单schema
        if (ml > 1) {
          return message.warning(i18n.t("multipleTablesSourceTargetRestriction"))
        }
        if (selectedNode && (isAllSourceTable([selectedNode], 'table') || (isAllSourceTable([selectedNode], 'schema') || isAllSourceTable([selectedNode], 'oracleUser') || (selectedNode?.connectionType === 'MySQL' && isAllSourceTable([selectedNode], 'database'))))) {
          dispatch(setTargetNodeInfos([selectedNode]));

        }
      }
      dispatch(setTableFieldMap([]))
    }

  }, [
    curStep, hdrVisible,
    JSON.stringify(mulSelectedNodes),
    JSON.stringify(selectedNode)
  ])

  const onClose = async () => {
    //全部清空
    await dispatch(setMulSelectedNodes([]));
    await dispatch(onClearAllInfo());
    setCurStep(1);
    setVisibleModal(true);
    setVisibleExpandModal(false);
    dispatch(setSelectedNode({}));
    setIsShortcutMode(true);
    setDataReplicationType('multiTable')
  }

  //选中数据是否为指定类型
  const isAllSourceTable = (mulSelectedNodes: any[], nodeType: string) => {
    return mulSelectedNodes?.every(item => item.nodeType === nodeType)
  }
  //同一连接下元素
  const isSameConnectionId = (mulSelectedNodes: any[]) => {
    const fId = getNodeTypeValueByPath(mulSelectedNodes?.[0]?.nodePathWithType, 'CONNECTION');
    return mulSelectedNodes?.every(item => {
      const id = getNodeTypeValueByPath(item?.nodePathWithType, 'CONNECTION');
      return id === fId
    })
  }
  //指定四大数据源
  const isSupportConnectionTypes = (selectedNodes: any[]) => {
    return selectedNodes?.every(item => SUPPROT_CONNECTION_TYPES.includes(item.connectionType))
  }

  //查找指定connectionid信息
  const findPointConnectionName = (connectionId: number) => {

    const connection = treeData?.find(node => node?.connectionId === connectionId);
    return connection?.nodeName;
  }

  const nextBtnState = useMemo(() => {
    if (curStep === 1) {

      return !sourceNodeInfos?.length
    }
    if (curStep === 2) {

      return !targetNodeInfos?.length
    }

    return false
  }, [
    curStep,
    JSON.stringify(sourceNodeInfos),
    JSON.stringify(targetNodeInfos),
  ])
  //删除操作
  const onCloseSelectedNode = (path: string) => {
    let curSelectedNodes = [];
    if (curStep === 1) {
      curSelectedNodes = sourceNodeInfos?.filter((item: any) => item?.nodePathWithType !== path);
      dispatch(setSourceNodeInfos(curSelectedNodes));
    } else if (curStep === 2) {
      curSelectedNodes = targetNodeInfos?.filter((item: any) => item?.nodePathWithType !== path);
      dispatch(setTargetNodeInfos(curSelectedNodes));
    }
    if (!curSelectedNodes.length) {
      dispatch(setSelectedNode({}));
    }
    dispatch(setMulSelectedNodes(curSelectedNodes));
  }

  const onNextStep = async () => {

    if (curStep === 1) {
      const params = sourceNodeInfos.map(i => {

        return ({
          schema: getNodeTypeValueByPath(i?.nodePathWithType, 'SCHEMA'),
          table: getNodeTypeValueByPath(i?.nodePathWithType, 'TABLE'),
          database: getNodeTypeValueByPath(i?.nodePathWithType, 'DATABASE'),
          nodePathWithType: i?.nodePathWithType,
          connectionType: i?.connectionType,
          connectionId: i?.connectionId,
          connectionName: findPointConnectionName(i?.connectionId)
        })
      })
      //直接重新存储了一份  后续可以优化 直接都使用一个变量存储就好
      dispatch(updateCopySourceContexts({
        ...copySourceTexts,
        sourceContexts: params
      }))
    }
    if (curStep === 2) {
      const targetNodes = targetNodeInfos.map(i => {
        return ({
          schema: getNodeTypeValueByPath(i?.nodePathWithType, 'SCHEMA'),
          table: getNodeTypeValueByPath(i?.nodePathWithType, 'TABLE'),
          connectionName: findPointConnectionName(i?.connectionId),
          database: getNodeTypeValueByPath(i?.nodePathWithType, 'DATABASE'),
          nodePathWithType: i?.nodePathWithType,
          connectionId: i?.connectionId,
          connectionType: i?.connectionType
        })
      })

      if (['oracleUser', 'schema', 'database'].includes(targetNodeInfos?.[0]?.nodeType)) {
        setDataReplicationType('schema')
      } else if (targetNodeInfos?.[0]?.nodeType === 'table' && sourceNodeInfos?.length > 1 && targetNodeInfos?.length === 1) {
        setDataReplicationType('singleTable')
        
      } else {
        setDataReplicationType('multiTable')
      }
      dispatch(setTargetNodeInfos(targetNodes))
      dispatch(updateTargetContexts(targetNodes))
    }
    Promise.all([dispatch(setMulSelectedNodes([])), dispatch(setSelectedNode({})), setCurStep(curStep + 1)])

  }

  const onSubmit = useCallback(() => {

    const fTargetNode = targetNodeInfos[0];

    if (!taskExecuteInfo?.taskName) {
      return message.info(i18n.t("pleaseEnterTaskName"))
    }

    let params: any = {
      sourceType: fTargetNode?.connectionType,
      sourceId: copySourceTexts?.sourceContexts?.[0]?.connectionId,
      "owner": userId,
      taskType: "DATA_REPLICATION",
      desensitizationGrade: 0,
      targetId: getNodeTypeValueByPath(fTargetNode?.nodePathWithType, 'CONNECTION'),
      targetType: fTargetNode?.connectionType,
      targetDatabase: getNodeTypeValueByPath(fTargetNode?.nodePathWithType, 'DATABASE'),
      targetSchema: getNodeTypeValueByPath(fTargetNode?.nodePathWithType, 'SCHEMA'),
      taskName: taskExecuteInfo?.taskName,
    }

    if (dataReplicationType === 'multiTable') {
      params.overwriteTarget = taskExecuteInfo?.overwriteTarget;
    }
    if (dataReplicationType === 'schema') {
      params.sourceContexts = copySourceTexts?.sourceContexts;
      params.noData = copySourceTexts?.noData;

      params.targetNodePath = fTargetNode?.nodePathWithType;
      params.targetSchema = getNodeTypeValueByPath(fTargetNode?.nodePathWithType, 'SCHEMA');
    } else {
      //默认值 如果字段映射 未展开
      params.sourceContexts = copySourceTexts?.sourceContexts;
      params.targetContexts = targetContexts;

    }
    if (tableFieldMap?.length && dataReplicationType !== 'schema') {
      let sources = _.cloneDeep(copySourceTexts?.sourceContexts || []);
      sources = sources.map((s, index) => {
        const columnField = `sourceFields${index}`;
        const columns = tableFieldMap
          .filter(item => item.checked) // 过滤出 checked 为 true 的项
          .map(item => item[columnField])      // 提取 name0 的值
          .map(String);
        s.columnInfo = columns;
        s.columns = columns.map(c => c.replace(/\(.*\)$/, ''));
        return s;
      })
      let cloneTargetContexts = _.cloneDeep(targetContexts);
      cloneTargetContexts = cloneTargetContexts.map((t, index) => {
        let newItem: any = {
          table: t?.table,
          schema: t?.schema,
          database: t?.database,
          nodePathWithType: t?.nodePathWithType,
          connectionName: t?.connectionName,
          connectionType: t?.connectionType
        }
        const columnField = `targetFields${index}`;
        const columns = tableFieldMap
          .filter(item => item.checked) // 过滤出 checked 为 true 的项
          .map(item => item[columnField])      // 提取 name0 的值
          .map(String);
        newItem.columnInfo = columns;
        newItem.columns = columns.map(c => c.replace(/\(.*\)$/, ''));
        return newItem;
      })
      //表映射字段
      params.sourceContexts = sources;
      params.targetContexts = cloneTargetContexts;
    }

    runReateDataDuplication({
      ...params
    })

  }, [JSON.stringify(taskExecuteInfo)])
  //切换手动
  const onSwitchManualMode = async () => {
    setVisibleExpandModal(true);
    setIsShortcutMode(false);
    //清空快捷信息
    await dispatch(onClearAllInfo());
  }
  //切换快捷方式
  const onSwitchShortcutMethod = async () => {
    setVisibleExpandModal(false);
    setIsShortcutMode(true);
    setVisibleModal(true);

    //清空快捷信息
    await dispatch(onClearAllInfo());
    await dispatch(setHdrVisible(true));
  }

  const renderShortcutStepTitle = (curStep: number) => {
    let defaultTitle = STEP_CONTENT_MAPPING[curStep];
    if (curStep === 3 && dataReplicationType !== 'schema') {
      defaultTitle = i18n.t("confirmFieldMapping");
    }
    return defaultTitle;
  }
  //渲染快捷方式展示内容
  const renderSourceNodes = useMemo(() => {
    let selectedNodes = [];

    if (curStep === 1) {
      selectedNodes = sourceNodeInfos;

    } else if (curStep === 2) {
      selectedNodes = targetNodeInfos;
    }

    return (
      <>
        <div className={styles.requiredTip}>
          {
            curStep === 1 && !sourceNodeInfos?.length && <> <ExclamationCircleFilled style={{ color: '#FF7D00', marginRight: 10, fontSize: 16 }} />{i18n.t("pleaseSelectSourceObject")}</>
          }
          {
            curStep === 2 && <>
              {!targetNodeInfos?.length && <>
                <ExclamationCircleFilled style={{ color: '#FF7D00', marginRight: 10, fontSize: 16 }} />
                {i18n.t("pleaseSelectTarget")}
              </>}
            </>
          }
        </div>
        <div className={styles.nodeContent}>
          {
            selectedNodes?.map((item: any) => (
              <Tag
                closable
                key={item?.key}
                className={styles.nodeItem}
                onClose={() => onCloseSelectedNode(item?.nodePathWithType)}
              >
                {renderDatabaseElementIcon(item?.nodePathWithType, item?.connectionType, findPointConnectionName(item?.connectionId))}
              </Tag>

            ))
          }
        </div>
        {
          curStep === 3 &&
          <div className={styles.confirmNodeContent}>
            {
              dataReplicationType === 'schema' ?
                <>
                  {i18n.t("copyContainsData")}：<Checkbox checked={!copySourceTexts?.noData} onChange={e => dispatch(updateCopySourceContexts({ ...copySourceTexts, noData: !e.target.checked }))} />
                  <Row className={styles.confirmTip}>
                    <Col span={24}>{i18n.t("clickExpandToCustomizeNewTableName")}</Col>
                  </Row>
                </>
                : <>
                  {i18n.t("confirmFieldMapping")}：
                  <Row className={styles.confirmTip}>
                    <Col span={24}>{i18n.t("defaultMapAllFields")}</Col>
                  </Row>
                </>
            }
          </div>
        }
        {
          curStep === 4 &&
          <div className={styles.executeNodeContent}>
            {i18n.t("taskName")}：<Input className={styles.input}
              placeholder={i18n.t("pleaseEnterTaskName")}
              value={taskExecuteInfo?.taskName}
              onChange={e => dispatch(updateTaskExecuteInfo({ ...taskExecuteInfo, taskName: e.target.value }))}
            />
            {
              dataReplicationType === 'multiTable' &&
              <div className="mt10">{i18n.t("allowDataCoverage")}：
                <Radio.Group
                  value={taskExecuteInfo?.overwriteTarget}
                  options={[{ label: i18n.t("yes"), value: true }, { label: i18n.t("no"), value: false }]}
                  onChange={(e) => dispatch(updateTaskExecuteInfo({ ...taskExecuteInfo, overwriteTarget: e.target.value }))}
                />
              </div>
            }
          </div>
        }
      </>
    )
  }, [
    curStep,
    JSON.stringify(sourceNodeInfos),
    JSON.stringify(targetNodeInfos),
    copySourceTexts?.noData,
    JSON.stringify(taskExecuteInfo),
    visibleExpandModal,
    dataReplicationType
  ])

  return (
    <>
      <div className={styles.hdrModal} style={{ height: '100%' }} ref={hdrModalRef}>
        <div className={classNames({ [styles.hdrMask]: hdrVisible })} ></div>
        <div style={{ height: '100%' }} className={classNames({ [styles.dfrContent]: hdrVisible })}>
          {children}
        </div>
      </div>
      <Modal
        title={null}
        zIndex={806}
        width={350}
        closable={false}
        visible={hdrVisible && visibleModal} //visible保证 展开复制信息时候左侧高亮 快捷状态提示
        onCancel={() => { onClose() }}
        okText={i18n.t("nextStep")}
        className={styles.hdrStepModal}
        style={{ left: shortcutModalPositionLeft + 30, top: 300 }}
        footer={
          <Space>
            {curStep === 1 && <Button onClick={() => onClose()}>{i18n.t("cancel")}</Button>}
            {curStep !== 1 && <Button onClick={() => {
              Promise.all([dispatch(setMulSelectedNodes([])), dispatch(setSelectedNode({})), setCurStep(curStep - 1)])


            }}>{i18n.t("previousStep")}</Button>}
            {curStep !== 4 && <Button type="primary" disabled={nextBtnState} onClick={() => onNextStep()}>{i18n.t("nextStep")}</Button>}
            {curStep === 4 && <Button type="primary" disabled={nextBtnState} onClick={() => onSubmit()}>{i18n.t("confirm")}</Button>}
          </Space>
        }
      >
        <div className={styles.arrow} />
        <div className={styles.modalTitle}>
          <h3>{i18n.t("dataCopy")}</h3>
          {curStep === 1 && <Button type="link" className={styles.mb14} onClick={() => onSwitchManualMode()}>{i18n.t("manualSelection")}</Button>}
          {
            [3, 4].includes(curStep) &&
            <Button
              type="link"
              className={classNames(styles.mb14)}
              onClick={() => { setVisibleExpandModal(true); setVisibleModal(false) }}
            >
              {i18n.t("expand")}
            </Button>
          }
        </div>
        <p>{renderShortcutStepTitle}</p>
        <div className={styles.stepContent}>
          {renderSourceNodes}
        </div>
      </Modal>
      {
        visibleExpandModal &&
        <ExpandMoreModal
          isShortcut={isShortcutMode}
          currentStep={curStep}
          dataReplicationType={dataReplicationType}
          setDataReplicationType={setDataReplicationType}
          onPopupModal={(step: number) => {
            setVisibleExpandModal(false);
            setVisibleModal(true);
            setCurStep(step);
          }}
          onSubmit={() => onSubmit()}
          onClose={() => onClose()}
          onSwitchShortcutMethod={() => onSwitchShortcutMethod()}
        />
      }
    </>
  )
})

export default HomogeneousDataReplicationModal;