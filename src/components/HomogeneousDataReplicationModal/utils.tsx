import React from 'react';
import { Tooltip, Typography } from 'antd';
import { Iconfont } from 'src/components';
import { generateObjectName } from 'src/util';
const { Text } = Typography;

export const renderDatabaseElementIcon = (
  nodePathWithType: string,
  dataSourceType?: string,
  connectionName?: string
) => {
  const splitPaths = nodePathWithType?.split('/').filter((i) => i);
  let databaseElementObj: any = {};

  splitPaths?.forEach((item) => {
    const splitArr = item.split(':');
    databaseElementObj[splitArr[0]] = splitArr[1];
  });

  const keys = Object.keys(databaseElementObj);

  // 找到最后一个键
  const lastKey = keys[keys.length - 1];
  const renderTitle = () => {

    return (
      <div style={{display: 'flex'}}>
        <Iconfont
          type={`icon-connection-${dataSourceType}`}
          style={{ fontSize: '16px', marginRight: 4 }}
        />
        <span style={{display: 'inline-block', width: '98%'}}>
        {connectionName ? `${connectionName}.` : ''}{generateObjectName(nodePathWithType, ['DATABASE', 'SCHEMA', 'TABLE'])}</span>
      </div>
    )
  }

  return (
    <Text ellipsis={true} style={{ overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap', width: 'calc(100% - 14px)' }}>
      <Tooltip title={renderTitle()} style={{ display: 'flex', alignItems: 'center' }} key={nodePathWithType}>
        <Iconfont
          type={`icon-${lastKey?.toLowerCase()}`}
          style={{ fontSize: '16px', marginRight: 4 }}
        />
        <span style={{ fontSize: 14 }}>{databaseElementObj[lastKey]}</span>
      </Tooltip>
    </Text>
  );
};

//根据path获取指定节点类型值
export const getNodeTypeValueByPath = (nodePathWithType: string, poitnType: string) => {

  if (!nodePathWithType || !poitnType) return '';

  const allNodesInfo =
    nodePathWithType.split('/')
      .filter((item) => item.length)
      .reduce((prev, next) => {
        const result = next.split(':')
        return result.length === 2 ? prev.set(result[0], result[1]) : prev
      }, new Map())
  return allNodesInfo?.get(poitnType)
}

export const renderConnectionPath = (connectionType: string, connectionName: string, databaseName: string, schemaName: string, table?: string) => {
  let arr = [];

  if (connectionName) {
    arr.push(connectionName)
  }
  if (databaseName) {
    arr.push(databaseName)
  }
  if (schemaName) {
    arr.push(schemaName)
  }
  if (table) {
    arr.push(table)
  }

  return (
    <div style={{
      width: '100%',
      cursor: 'pointer',
      overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap'
    }}>
      <Iconfont
        type={`icon-connection-${connectionType}`}
        style={{ fontSize: '16px', marginRight: 4 }}
      />
      {arr?.join('.')}
    </div>
  )
}

export const renderNodeWithIcon = (connectionName: string, connectionType: string, path: string) => {

  return (
    <div style={{
      width: '100%',
      cursor: 'pointer',
      overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap'
    }}>
      <Iconfont
        type={`icon-connection-${connectionType}`}
        style={{ fontSize: '16px', marginRight: 4 }}
      />
      {connectionName ? `${connectionName}.` : ''}{generateObjectName(path, ['DATABASE', 'SCHEMA', 'TABLE'])}
    </div>
  )
}

export const formatSelectOptionsWithIcon = (res: any[]): any[] => {
  return res.map((i: any) => {
    return {
      label: <span><Iconfont type={`icon-${i.connectionType}`} style={{ marginRight: 4 }} />{i.connectionName}</span>,
      value: i.connectionId,
      props: i,
    };
  });
}