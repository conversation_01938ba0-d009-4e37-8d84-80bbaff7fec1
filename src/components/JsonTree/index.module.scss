.jsonTree {
  .root {
    font-weight: bold;
    color: #0070c1;
  }

  .field {
    color: #0451a5;

    &.colon::after {
      display: inline-block;
      margin-left: 4px;
      margin-right: 8px;
      content: ':';
      color: #9c9c9f;
    }
  }

  .number {
    color: #098658;
  }

  .string {
    color: #a31515;
  }

  .boolean,
  .null {
    color: #0000ff;
  }

  .count {
    margin-left: 8px;
    color: #9c9c9f;
  }

  // override ant-tree
  :global {
    .ant-tree-list-holder-inner {
      padding: 0 12px;

      .ant-tree-treenode {
        width: 100%;
        white-space: normal;
        word-break: break-all;
        overflow: hidden;
      }
    }
    .ant-tree-node-content-wrapper {
      overflow: hidden;
      &:hover {
        background-color: #d6e5ff;
      }
    }
    .ant-tree-list-scrollbar {
      width: 14px !important;
      height: 14px !important;
      display: inline-block !important;
    }
    .ant-tree-list-scrollbar-thumb {
      border-radius: 0 !important;
      background: #e3e2e2 !important;
    }
  }
}
