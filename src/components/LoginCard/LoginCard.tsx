import React, { PropsWithChildren } from 'react'
import BackImg from 'src/assets/img/img.png'
import { useRequest } from 'src/hook'
import { 
  getBackImg
} from 'src/api'
import styles from './loginCard.module.scss'

export const LoginCard = ({ children, notification, isOldVersion }: PropsWithChildren<{ notification?: JSX.Element, isOldVersion?: boolean }>) => {
  const imgPrefix = "/user";
  const { data: backImg } = useRequest(getBackImg)
  const backImgUrl = { backgroundImage: `url(${backImg ? imgPrefix + backImg : BackImg})` }
  
  if(isOldVersion){
    return (
      <div className={styles.oldLogin}>
        {notification}
        <div className={styles.loginBox}>
          <div style={backImgUrl} className={styles.loginImg}></div>
          <div className={styles.loginCard}>{children}</div>
        </div>
      </div>
    )
  }
  return (
    <div className={styles.login}>
      {notification}
      <div className={styles.loginBox}>
        <div className={styles.loginCard}>{children}</div>
      </div>
    </div>
  )
}
