import React, { CSSProperties, FunctionComponent } from 'react'
import { Card } from 'antd'

interface ModuleCardProps {
  title: string
  unitList: FunctionComponent[]
}

const gridStyle: CSSProperties = {
  width: '25%',
  textAlign: 'center',
}

export const ModuleCard = ({ title, unitList }: ModuleCardProps) => {
  return (
    <Card title={title}>
      {unitList.map((Unit) => (
        <Card.Grid style={gridStyle}>
          <Unit />
        </Card.Grid>
      ))}
    </Card>
  )
}
