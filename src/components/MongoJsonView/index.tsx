import React, { useState, useRef, useEffect, useCallback } from 'react'
import { Tree } from 'antd'
import { DataNode } from 'antd/lib/tree'
import isArray from 'lodash/isArray'
import styles from './index.module.scss'
import classNames from 'classnames'

// todo 已解决: 1. 虚拟滚动 2. 节点展开时遍历子json;
// todo 待解决: ant tree 组件在大数据量下的渲染性能问题, vtree 在 100w 行数据下的性能良好, 后期考虑在 vtree 的基础上二次开发

/** 可展开节点的 title */
const BranchNode: React.FC<{
  field: React.ReactNode
  type?: string
  length?: number
}> = ({ field, type, length }) => {
  return (
    <>
      <span className={styles.field}>{field}</span>
      {type === 'object' && (
        <span className={styles.count}>{`{${length || ''}}`}</span>
      )}
      {type === 'array' && (
        <span className={styles.count}>{`[${length || ''}]`}</span>
      )}
    </>
  )
}

/** 叶子节点的 title */
const LeafNode: React.FC<{
  field: React.ReactNode
  value: string | number | boolean | null
  type?: string
}> = ({ field, value, type }) => {
  return (
    <>
      <span className={classNames(styles.field, styles.colon)}>{field}</span>
      <span className={type && styles[type]}>
        {typeof value === 'string' ? `"${value}"` : `${value}`}
      </span>
    </>
  )
}

const getMetaType = (meta: any) => {
  let type: string | undefined = typeof meta
  if (meta === null) {
    type = 'null'
  }
  if (isArray(meta)) {
    type = 'array'
  }
  return type
}

interface JsonTreeNode extends DataNode {
  meta?: string | number | boolean | null | object | any[]
}

export const MongoJsonView: React.FC<{
  dataSource?: any[]
  height?: number
  [p: string]: any
}> = React.memo(({ dataSource, height, ...rest }) => {
  const nodeMapRef = useRef<Map<string, any>>(new Map())
  const [mongoDocs, setMongoDocs] = useState<JsonTreeNode[]>()
  useEffect(() => {
    const nodeMap = nodeMapRef.current
    const nextState = dataSource?.map((json, index) => {
      /** 节点元信息 */
      let meta
      try {
        meta = JSON.parse(json)
      } catch {}
      /** 节点元信息类型 */
      const type = getMetaType(meta)
      /** 是否是叶子节点 */
      const isLeaf = !['object', 'array'].includes(type)
      /** object | array 类型节点的字段数 */
      let length = 0

      if (!isLeaf) {
        length = Object.keys(meta as object).length
      }

      const key = `0-${index}`
      const docTitle = (
        <span className={styles.document}>{`Document ${index + 1}`}</span>
      )
      const docNode: JsonTreeNode = {
        key,
        title: isLeaf ? (
          <LeafNode field={docTitle} value={meta} type={type} />
        ) : (
          <BranchNode field={docTitle} type={type} length={length} />
        ),
        meta,
        isLeaf,
      }
      nodeMap.set(key, docNode)
      return docNode
    })
    setMongoDocs(nextState)
  }, [dataSource])

  const handleLoadData: (treeNode: JsonTreeNode) => Promise<void> = useCallback(
    ({ key, meta }) => {
      const nodeMap = nodeMapRef.current
      const target = nodeMap.get(key as string)
      // 健壮性处理
      if (!target) return Promise.resolve()
      if (typeof meta !== 'object') return Promise.resolve()
      if (meta === null) return Promise.resolve()

      // 构造子节点
      const children = Object.entries(meta as object).map(
        ([field, value], index) => {
          const type = getMetaType(value)
          const isLeaf = !['object', 'array'].includes(type)
          let length = 0
          if (!isLeaf) {
            length = Object.keys(value as object).length
          }
          const itemKey = `${key}-${index}`
          const node = {
            key: itemKey,
            title: isLeaf ? (
              <LeafNode field={field} value={value} type={type} />
            ) : (
              <BranchNode field={field} length={length} type={type} />
            ),
            meta: value,
            isLeaf,
          }
          nodeMap.set(itemKey, node)
          return node
        },
      )
      target.children = children
      setMongoDocs((state) => {
        if (!state) return state
        return [...state]
      })
      return Promise.resolve()
    },
    [],
  )

  return (
    <Tree
      className={styles.mongoJsonView}
      treeData={mongoDocs}
      loadData={handleLoadData}
      height={height}
      selectable={false}
      {...rest}
    />
  )
})
