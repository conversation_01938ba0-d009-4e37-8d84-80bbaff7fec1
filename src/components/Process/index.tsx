import React, { FC } from 'react'
import { Steps } from 'antd';
import classNames from 'classnames';
import { StepProps, StepsProps } from 'antd/lib/steps'
import styles from './index.module.scss'

interface ItemProps extends StepProps{
  iconText?: string | React.ReactNode
  badge?: any
  status?: any;
  type?: string;
  hidden?: boolean;
}

interface ProcessProps extends StepsProps {
  items: ItemProps[];
  className?: string;
  isShowStepIcon?: boolean;
}

export const Process: FC<ProcessProps> = ({
  items,
  className,
  labelPlacement,
  isShowStepIcon = true,
  ...rest
}) => {
  
  return (
    <Steps
      {...rest}
      size='default'
      labelPlacement={labelPlacement || "vertical"}
      className={`${styles.wrap} ${className}`}
    >
      {items.filter(({ hidden= false}) => !hidden)
      .map(({ iconText, status, badge,...rest }, idx) => {
        return (
          <Steps.Step
            key={idx}
            icon={ 
              isShowStepIcon?
                iconText &&
                  <div className={classNames(styles.iconWrapper, {
                    [styles.success]: status === 0,
                    [styles.error]: status === 1
                  }) }>
                    <span className={styles.icon}>{iconText}</span>
                    {badge}
                  </div>
                : <div className='displaypnone'></div>
            }
            {...rest}
          />
        )
      })}
    </Steps>
  )
}

export default Process
