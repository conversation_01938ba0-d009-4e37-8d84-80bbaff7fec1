import React from 'react';
import { Iconfont } from 'src/components';

const nodeTypeAndIconMapping: {[key: string]: string}= {
    DATABASE: 'schema_nomal',
    SCHEMA: 'schema_nomal'
};

export const RenderNodePathIcon = () => {
    const path = '/CONNECTION:2/DATABASE:postgres/SCHAME:5'
    const regex = /\/([A-Z]+):(\w+)/gi;
    const matches = path.matchAll(regex);
    
    const allFieldArr = Array.from(matches, match => ({ type: match[1], value: match[2] }));
    const filterFieldArr = allFieldArr.filter((filed) => Object.keys(nodeTypeAndIconMapping).includes(filed.type))
    return (<div>
       {
        filterFieldArr.map(item => (
            <Iconfont
            type={nodeTypeAndIconMapping[item?.type]}
            style={{ marginRight: "20px", fontSize: "28px" }}
          >{item?.type}</Iconfont>
          ))
       }
     </div>)
}