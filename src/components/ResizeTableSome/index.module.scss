@import '../../styles/variables';

.CQTable {
  :global {
    .react-resizable {
      position: relative;
      background-clip: padding-box;
    }

    .react-resizable-handle {
      position: absolute;
      right: -5px;
      bottom: 0;
      z-index: 1;
      width: 10px;
      height: 100%;
      cursor: col-resize;
      &::after {
        content: '|';
        position: absolute;
        top: 50%;
        transform: translate(0, -50%);
        font-weight: 700;
        color: $text-support;
      }
    }
  }

  .tableHeader:hover {
    background-color: $background-color;
    cursor: pointer;
  }
}
