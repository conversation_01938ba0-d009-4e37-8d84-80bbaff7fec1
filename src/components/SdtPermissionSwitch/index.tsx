import React from "react";
import { useDispatch, useSelector } from "src/hook";
import { Switch, Tooltip, Row, Col } from "antd";
import { setSdtPermissionStatus } from "src/store/extraSlice/sdtPermissionSlice";
import i18n from 'i18next';

export const SdtPermissionSwitch = ({
  onChange,
  disabled = false
}: {
  onChange: () => void;
  disabled?: boolean;
}) => {
  const dispatch = useDispatch();
  const { status } = useSelector((state) => state.sdtPermission);
  return (
    <Tooltip
      placement="bottomRight"
      title={
        <Row>
          <Col>{i18n.t("turnOnOnlyDisplayDatabaseObjectsWithPermission")}&nbsp;&nbsp;</Col>
          <Col>
            {i18n.t("close")}：
            {i18n.t("displayAllDatabaseObjects")}
          </Col>
        </Row>
      }
    >
      <Switch
        defaultChecked={status}
        disabled={disabled}
        onChange={(checked) => {
          dispatch(setSdtPermissionStatus(checked));
          onChange();
        }}
        size="small"
        style={{
          margin: 4,
          opacity: disabled ? 0.5 : 1,
        }}
      />
    </Tooltip>
  );
};
