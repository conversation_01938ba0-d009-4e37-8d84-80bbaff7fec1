    // SelectCheckboxAll.test.ts
    import React from 'react';
    import { render, screen, fireEvent } from '@testing-library/react';
    import SelectCheckboxAll from '../SelectCheckboxAll';
    
    // 模拟 calculateTextWidth 和 cleanupCalculateTextWidth
    jest.mock('../../util/utils.ts', () => ({
      calculateTextWidth: () => 100,
      cleanupCalculateTextWidth: jest.fn()
    }));
    
    describe('SelectCheckboxAll Component', () => {
      const data = [
        { value: '1', label: 'Option 1' },
        { value: '2', label: 'Option 2' },
        { value: '3', label: 'Option 3' }
      ];
    
      const onChange = jest.fn();
    
      it('renders correctly with empty data', () => {
        render(<SelectCheckboxAll data={[]} value={[]} onChange={onChange} />);
        expect(screen.queryByText('No data available')).toBeInTheDocument();
      });
    
      it('renders correctly with non-empty data', () => {
        render(<SelectCheckboxAll data={data} value={[]} onChange={onChange} />);
        expect(screen.queryByText('No data available')).not.toBeInTheDocument();
        expect(screen.getAllByRole('checkbox').length).toBe(data.length + 1); // 包括全选复选框
      });
    
      it('handles check all connections', () => {
        render(<SelectCheckboxAll data={data} value={[]} onChange={onChange} />);
        const checkAllCheckbox = screen.getByRole('checkbox', { name: 'Select All' });
        fireEvent.click(checkAllCheckbox);
        expect(onChange).toHaveBeenCalledWith(['1', '2', '3']);
      });
    
      it('handles individual checkbox selection', () => {
        render(<SelectCheckboxAll data={data} value={[]} onChange={onChange} />);
        const optionCheckbox = screen.getAllByRole('checkbox')[1]; // 第一个选项
        fireEvent.click(optionCheckbox);
        expect(onChange).toHaveBeenCalledWith(['1']);
      });
    
      it('handles search functionality', () => {
        render(<SelectCheckboxAll data={data} value={[]} onChange={onChange} />);
        const searchInput = screen.getByPlaceholderText('Please Select');
        fireEvent.change(searchInput, { target: { value: 'Option 1' } });
        expect(screen.getAllByRole('checkbox').length).toBe(2); // 包括全选复选框
      });
    
      it('renders correctly when no options match search', () => {
        render(<SelectCheckboxAll data={data} value={[]} onChange={onChange} />);
        const searchInput = screen.getByPlaceholderText('Please Select');
        fireEvent.change(searchInput, { target: { value: 'Nonexistent' } });
        expect(screen.queryByText('No data available')).toBeInTheDocument();
      });
    });