// 定义一个用于全选操作的复选框组件
import React, { useEffect, useMemo, useRef, useState } from 'react';
import { Select, Tag } from 'antd';
import Checkbox, { CheckboxChangeEvent } from 'antd/lib/checkbox';
import { VariableSizeList } from 'react-window';
import { useTranslation } from 'react-i18next';
import _ from 'lodash';
import { calculateTextWidth, cleanupCalculateTextWidth } from 'src/util';
import styles from './index.module.scss';
import { Iconfont } from '../CustomAntdComponents';

export const SelectCheckboxAll = ({
  data,
  value,
  onChange,
  showIcon = false,
}: {
  data: any,
  value: string[],
  onChange: (selectedValues: string[]) => void,
  showIcon?: boolean
  [p: string]: any,
}) => {
  const { t } = useTranslation();
  const [checkedConnectionIds, setCheckedConnectionIds] = useState<string[]>(value || []);  // 选中
  const [indeterminate, setIndeterminate] = useState<boolean>(false); // 是否半选
  const [searchValue, setSearchValue] = useState<string | null>('');
  const [precomputedItemSizes, setPrecomputedItemSizes] = useState<{[p: string]: number}>({});
  const [valueToConnectionTypeMap, setValueToConnectionTypeMap] = useState<{[p: string]: any}>({});
  const [lineWidth, setLineWidth] = useState<number>(0);
  const [sourceData, setSourceData] = useState<any[]>(data || []);

  const ItemSizeVal = 19;
  const ListHeight = 200;
  const SelectPaddingLR = 28;// Select padding-left、padding-right
  const CheckboxBoxWidth = 16 + 8;  // 复选框的宽度 + padding-right
  const IconWidth = 15 + 8; // icon的宽度 + padding-right

  // 添加 ref, 用于获取 Select 的宽度
  const selectRef = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    if (selectRef?.current) {
      const rect = selectRef.current.getBoundingClientRect();
      setLineWidth((rect?.width - SelectPaddingLR) || 0); 
    }
    return () => {
      cleanupCalculateTextWidth();
      setPrecomputedItemSizes({});
      setValueToConnectionTypeMap({});
      setSourceData([]);
      setSearchValue(null);
      setCheckedConnectionIds([]);
      setIndeterminate(false);
      setLineWidth(0);
    }
  }, []);

  useEffect(() => {
    setSourceData(data || []);
  }, [JSON.stringify(data)])

  // 初始化计算每个选项的高度
  useEffect(() => {
    setPrecomputedItemSizes((sizeData: any) => {
      if (sourceData?.length === Object.keys(sizeData)?.length) {
        return sizeData;
      } else {

        if (showIcon) {
          // 创建 value 到 connectionType 的映射
          const map = sourceData?.reduce((acc: any, cur: any) => {
            acc[cur.value] = cur?.connectionType;
            return acc;
          }, {});
          setValueToConnectionTypeMap(map);
        }

        return sourceData?.reduce((acc: any, cur: any, index: number) => {
          acc[cur?.value] = getItemSizeVal(index);
          return acc;
        }, {});
      }
    })
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [showIcon, JSON.stringify(sourceData)])

  const onChangeOptionStatus = (e: CheckboxChangeEvent) => {
    let cloneCheckedConnectionIds = _.cloneDeep(checkedConnectionIds);

    if (e?.target?.checked) {
      cloneCheckedConnectionIds = cloneCheckedConnectionIds.concat([e?.target?.value]);
    } else {
      cloneCheckedConnectionIds = cloneCheckedConnectionIds.filter(i => i !== e?.target?.value);
    }
    setCheckedConnectionIds(cloneCheckedConnectionIds);
    setIndeterminate(!!cloneCheckedConnectionIds?.length && cloneCheckedConnectionIds?.length < sourceData?.length);
    setSearchValue('')
    onChange(cloneCheckedConnectionIds);
  }

  const onCheckAllConnections = (e: CheckboxChangeEvent) => {
    if (e.target.checked) {
      const connectionIds = sourceData?.map((i: any) => i?.value);

      setCheckedConnectionIds(connectionIds);
      onChange(connectionIds);
    } else {
      setCheckedConnectionIds([]);
      onChange([]);
    }
    setIndeterminate(false);
    setSearchValue('');
  }

  const getItemSizeVal = (index: number): number => {
    if (!sourceData || index < 0 || index >= sourceData.length) {
      return ItemSizeVal; // 处理无效索引的情况
    }

    const text = sourceData[index]?.label || '';
    let textWidth = calculateTextWidth(text) + CheckboxBoxWidth;
    
    if (showIcon) {
      textWidth += IconWidth;
    }
    
    const lines = Math.ceil(textWidth / lineWidth); // 向上取整
    const height = Math.max(ItemSizeVal, lines * ItemSizeVal) + 6;
    return height;
  }

  const selectOptions = useMemo(() => {
    let cloneData = _.cloneDeep(sourceData)
    return searchValue ? cloneData.filter((i: any) => i?.label.toLowerCase().includes(searchValue.toLowerCase())) : cloneData
  }, [searchValue, JSON.stringify(sourceData)])


  const renderDropdown = useMemo(() => {
    return (
      <>
      {
        !searchValue &&
        <>
          <Checkbox
            key="checkedAllKey"
            checked={sourceData?.length === checkedConnectionIds?.length}
            indeterminate={indeterminate}
            onChange={onCheckAllConnections}
          >{t("common.text.checkAll")}</Checkbox>
          <br></br>
        </>
      }
      <div>
      <VariableSizeList
        key={JSON.stringify(selectOptions)} // 使用 selectOptions 的字符串表示作为 key, 以保证过滤得到selectOptions渲染时可以重新获取itemSize
        className={styles.variableSizeListSty}
        itemCount={selectOptions.length}
        itemSize={(index) => {
          const item = selectOptions[index];
          return precomputedItemSizes[item?.value] || getItemSizeVal(index);
        }}
        height={ListHeight}
        width="100%"
        itemData={selectOptions}
      >
        {({ index, style, data: listData }) => {
          const item = listData[index];
          const connectionType = item?.connectionType;
          return <Checkbox
            key={item?.value}
            checked={checkedConnectionIds?.includes(item?.value)}
            value={item?.value}
            onChange={onChangeOptionStatus}
            style={{ ...style, display: 'block' }}
          >
            {
              showIcon && connectionType && <Iconfont
                type={'icon-connection-' + item?.connectionType}
                className="mr8"
              />
            }
            {item?.label}
          </Checkbox>
        }}
      </VariableSizeList >
      </div>
      </>
    )
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    checkedConnectionIds,
    sourceData?.length,
    indeterminate,
    precomputedItemSizes,
    searchValue,
    JSON.stringify(selectOptions),
    showIcon,
    t,
    lineWidth
  ])

  const tagRender = (props: any) => {
    const { label, closable, onClose } = props;
    const iconType = `icon-connection-${valueToConnectionTypeMap[props?.value]}`
    const onPreventMouseDown = (event: React.MouseEvent<HTMLSpanElement>) => {
      event.preventDefault();
      event.stopPropagation();
    };
    return (
      <Tag
        onMouseDown={onPreventMouseDown}
        closable={closable}
        onClose={onClose}
      >
        {
          showIcon &&
          <Iconfont
            type={iconType}
            style={{ marginRight: 5 }}
          />
        }
        <span className={styles.tagText} title={label}>{label}</span>
      </Tag>
    );
  };

  return (
    <div ref={selectRef} className={styles.selectWrapSty}>
      <Select
        mode="multiple"
        placeholder={t("common.search.select.placeholder")}
        allowClear
        showSearch
        onSearch={(val: string) => setSearchValue(val)}
        maxTagCount={3}
        optionLabelProp="label"
        options={selectOptions}
        value={checkedConnectionIds}
        tagRender={tagRender}
        onBlur={() => setTimeout(() => setSearchValue(''),1000)}
        onClear={() => {
          setCheckedConnectionIds([]);
          setIndeterminate(false);
          onChange([]);
        }}
        onChange={(value:string[]) => {
          setCheckedConnectionIds(value);
          setIndeterminate(!!value?.length && value?.length < sourceData?.length)
          onChange(value);
        }}

        dropdownRender={() => {
          if (!selectOptions?.length) {
            return <span style={{ padding: 8 }}>{t("common.noData")}</span>
          } else {
            return (
              <div style={{ padding: 8, overflow: 'auto' }}>
                {renderDropdown}
              </div>
            )
          }
        }}
      />
    </div>
  )
}
