
import React from 'react'
import { PlusOutlined } from '@ant-design/icons';
import { Button, Checkbox, Divider, Input, Space } from 'antd';
import styles from './index.module.scss';
import i18n from 'i18next';

interface IProps {
  [p: string]: any;
}

export const DropdownMenu = (props: IProps) => {
  const { 
    checked,
    menu,
    inputRef,
    name,

    handleSelectAll,
    onNameChange,
    addItem,
   } = props;
  return (
    <div className={styles.dropdownMenuWrap}>
      <Checkbox onChange={(e) => handleSelectAll(e)} checked={checked}>{i18n.t("selectAll")}</Checkbox>
      {menu}
      <Divider style={{ margin: '8px 0' }} />
      <Space style={{ padding: '0 8px 4px' }}>
        <Input
          placeholder={i18n.t("pleaseEnterSystemLibrary")}
          ref={inputRef}
          value={name}
          onChange={onNameChange}
        />
        <Button type="text" icon={<PlusOutlined />} onClick={addItem}>
          {i18n.t("create")}
        </Button>
      </Space>
    </div>
  );
};