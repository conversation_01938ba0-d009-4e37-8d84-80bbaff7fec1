import React, { useEffect, useRef, useState } from 'react';
import { Select } from 'antd';
import { DropdownMenu } from './DropdownMenu';
import i18n from 'i18next';

interface IProps {
  form: any,
  fieldKey: string | string[],
  options: string[],
  disabled?: boolean,
  maxTagCount: number,
  handleSave?: (a: any) => void,
  [key: string]: any
}
let index = 0;
const Option = Select;

export const SelectWithAdd = (props: IProps) => {

  const { form, fieldKey, width, options, disabled, maxTagCount, handleSave } = props;

  const [items, setItems] = useState(options);
  const [name, setName] = useState('');
  const [checked, setChecked] = useState(false);
  const [value, setValue] = useState<string[]>([]);
  const inputRef = useRef<any>(null);
  const selectRef = useRef<any>(null);

  useEffect(() => {
    setItems(options);
    setChecked(false);
  }, [options])

  useEffect(() => {
    setValue(form.getFieldValue(fieldKey) || []);
  }, [fieldKey, form.getFieldValue(fieldKey)])

  useEffect(() => {
    if (items?.length > 0 && value?.length === items?.length) {
      setChecked(true)
    }
  },[items, value])

  const onNameChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setName(event.target.value);
  };

  const updateField = (value: any) => {
    if (Array.isArray(fieldKey)) {
      form.setFieldsValue({
        [fieldKey[0]]: {
          [fieldKey[1]]: value
        }
      });
    } else {
      form.setFieldsValue({
        [fieldKey]: value,
      });
    }
  };

  const addItem = (e: React.MouseEvent<HTMLAnchorElement>) => {
    e.preventDefault();
    if (!name || name?.trim() === '') return;
    if (items?.includes(name?.trim())) {
      setName('');
      return;
    }
    setItems([...items, name || `New item ${index++}`]);
     
    setName('');
    setChecked(false);
    setTimeout(() => {
      inputRef.current?.focus();
    }, 0);
  };

  const handleSelectAll = (e: any) => {
    const checked = e.target.checked
    setChecked(checked)
    if (checked) {
      const val = items?.map((item: any) => item)
      setValue(val)
      updateField(val)
    } else {
      setValue([])
      updateField([])
    }
  }

  const handleSelectChange = (value: any) => {
    if (value?.length !== items?.length) {
      setChecked(false)
    } else {
      setChecked(true)
    }
    setValue(value || [])
    updateField(value || [])
    if (handleSave) {
      selectRef.current?.focus()
    }
  }

  const renderDropdownMenu = (menu: any) => {
    return (
      <DropdownMenu
        checked={checked}
        menu={menu}
        inputRef={inputRef}
        name={name}
        handleSelectAll={handleSelectAll}
        onNameChange={onNameChange}
        addItem={addItem}
      />
    )
  }
  
  return (
    <Select
      ref={selectRef}
      style={{ width: value?.length > 0 ? (width || "auto") : 250 }}
      placeholder={i18n.t("pleaseSelect")}
      mode="multiple"
      maxTagCount={maxTagCount}
      value={value}
      dropdownRender={menu => renderDropdownMenu(menu)}
      onChange={(e) => {handleSelectChange(e)}}
      onBlur={(e) => {
        const target = e.relatedTarget as HTMLElement;
        if (target?.closest?.('.ant-select-dropdown')) {
          // 如果焦点转移到下拉框内部，阻止 onBlur 事件处理
          return;
        }
        handleSave && handleSave(e)
      }}
      disabled={disabled}
    >
      {items?.map(option => (
        <Option key={option} value={option} >
          {option}
        </Option>
      ))}
    </Select>
  )
}