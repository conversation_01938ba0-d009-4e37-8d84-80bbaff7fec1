.editorWrap {
    border: 1px solid rgb(135 139 146 / 15%);
    border-radius: 3px;
    background: white;
    .editorActions {
        border-bottom: 1px solid #EAEBF0;
    }

    .container {
        :global {
            .monaco-editor {
              background-color: #fff;
              .monaco-editor-background {
                background-color: #fff;
              }
            }
            .margin {
              background-color: #F7F9FC;
            }
            .mtk6 {
               color: #0000ff;
            }
            .mtk18 {
                color: #778899;
            }
            .mtk1 {
                color: #000000;
            }
            .line-numbers {
                color: #333333;
            }
            .monaco-editor .view-overlays .current-line {
                border: 2px solid #eeeeee !important; /* 强制覆盖默认样式 */
            }
            .monaco-editor .view-overlays .selected-text {
              background-color: #e5ebf1 !important;  /* 浅色主题下选中语句背景颜色 */
            }
            .monaco-editor .focused .selected-text {
              background-color:  #add6ff !important;
            }
        }
    }
}