import React, { FC, useCallback, useEffect, useReducer, useRef, useState } from 'react'
import { editor } from 'monaco-editor'
import type { IDisposable } from 'monaco-editor'
import { Button } from 'antd'
import { format } from 'sql-formatter'
import { formatOptionalLangs } from 'src/constants'
import styles from './SimpleEditor.module.scss'
import { isNumber } from 'lodash'
import i18n from 'i18next';

interface SimpleEditorProps {
  id: string
  height?: number | string
  value?: string | string[]
  readOnly?: boolean
  onChange?: (newValue: string) => void
  theme?: string
  connectionType?: string
}

const initRecord = {
  customCaseConversion: true,
}
function simpleReducer(state: any, action: { type: any; payload: any }) {
  switch (action.type) {
    case 'update':
      return {
        ...state,
        ...action.payload,
      }
  }
}

export const SimpleEditor: FC<SimpleEditorProps> =({
  id,
  height = 270,
  value: valueProps,
  readOnly = false,
  onChange,
  theme,
  connectionType,
}) => {
  const editorRef = useRef<editor.IStandaloneCodeEditor>()
  const [record, dispatchRecord] = useReducer(simpleReducer, initRecord)
  const subscriptionRef = useRef<IDisposable>()
  const [isEditorReady, setIsEditorReady] = useState<Boolean>(false)

  // onChange
  useEffect(() => {
    if (isEditorReady && onChange) {
      subscriptionRef.current?.dispose()
      subscriptionRef.current = editorRef.current?.onDidChangeModelContent(
        (event) => {
          onChange(editorRef.current!.getValue())
        },
      )
    }
  }, [isEditorReady, onChange])

  useEffect(() => {
    const myEditor = editor.create(document.getElementById(id) as HTMLElement, {
      value: ``,
      language: 'sql',
      automaticLayout: true,
      readOnly,
      minimap: { enabled: false },
      theme,  // 手动设置主题
    })
    editorRef.current = myEditor
    setIsEditorReady(true)
  }, [readOnly, theme])

  useEffect(() => {
    // 附件上传
    if(typeof valueProps === 'object' && valueProps?.length){
      return 
    }
    if (valueProps === editorRef.current!.getValue()) return
    triggerChange(valueProps)
  }, [valueProps])

  const triggerChange = useCallback((newValue: any, updateRange?: any) => {
    let param = newValue ?? ''
    
    const model = editorRef.current?.getModel()
    let fullRange = updateRange ? updateRange : model?.getFullModelRange() as any;
    const {endColumn, endLineNumber, startColumn, startLineNumber} = fullRange
    if (!fullRange || (startColumn ===endColumn && startLineNumber === endLineNumber) || readOnly) {
      editorRef.current?.setValue(param)
    }
    else {
      // 添加撤销点
      editorRef.current?.pushUndoStop();
      editorRef.current?.executeEdits("trigger-change", [
        {
          range: fullRange,
          text: param,
          forceMoveMarkers: true
        }
      ]);
      editorRef.current?.pushUndoStop();
    }
  },[readOnly])

  const menuList = [
    {
      name: i18n.t("caseConversion"),
      id: 'customCaseConversion',
      hide: false,
      action: () => {
        let result = editorRef.current?.getValue().toUpperCase()
        if (!record.customCaseConversion) {
          result = editorRef.current?.getValue().toLowerCase()
        }
        dispatchRecord({
          type: 'update',
          payload: {
            customCaseConversion: !record.customCaseConversion,
          },
        })
        triggerChange(result)
      },
    },
    {
      name: i18n.t("format"),
      id: 'customFormat',
      hide: false,
      action: () => {
        const model = editorRef.current?.getModel()
        // 获取整个文本范围
        const fullRange = model?.getFullModelRange(); 
        // 获取选中文本范围
        const selection = editorRef.current?.getSelection();

        let value = model?.getValue() ?? ''
        let updateRange: any = fullRange
        // 如果有选中文本，就格式化选中文本
        if(selection && !selection?.isEmpty()){
          updateRange = selection
          value = model?.getValueInRange(selection) ?? ''
        }

        // 选择数据源类型格式化
        let language: typeof formatOptionalLangs[number]
        switch (connectionType) {
          case 'Oracle':
            language = 'plsql'
            break
          case 'SQLServer':
            language = 'tsql'
            break
          default:
            language =
              formatOptionalLangs.find(
                (lang: string) => lang === connectionType?.toLowerCase(),
              ) || 'sql'
            break
        }
        const formated = format(value, { language })
        triggerChange(formated, updateRange)
      },
    },
  ]

  return (
    <div className={styles.editorWrap}>
      <section className={styles.editorActions}>
        {/* actions */}
        {menuList.map((item) => {
          return (
            <Button type="text" key={item.id} onClick={item.action}>
              {item.name}
            </Button>
          )
        })}
      </section>
      <div
        id={id}
        className={styles.container}
        style={{ minHeight: isNumber(height) ? `${height}px` : height }}
      ></div>
    </div>
  )
}

export default SimpleEditor
