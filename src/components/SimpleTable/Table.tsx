import React from 'react'
import { useTranslation } from 'react-i18next'
import type { TableProps } from 'antd/lib/table'
import { ResizeTableSome } from '../ResizeTableSome'

interface ISimpleTableProps extends TableProps<any> {
  total: number;
  searchParams?: { currentPage?: number; pageSize?: number }
  setSearchParams?: (params: any) => void
  showPagination?: boolean;
  autoWidthField?: string[];
}

export const SimpleTable = (props: ISimpleTableProps) => {

  const { t } = useTranslation()
  const { searchParams = {}, setSearchParams, total, showPagination = true, autoWidthField = [], ...rest } = props

  return (
    <ResizeTableSome
      scroll={{ x: 1700, y: 600 }}
      {...rest}
      pagination={showPagination ? {
        current: searchParams?.currentPage,
        pageSize: searchParams?.pageSize || 10,
        total,
        size:"default",
        showSizeChanger: true,
        showQuickJumper: true,
        showTotal: (total: number) => t('systemManagement.personManagement.table.pagination.total',{val:total || 0}),
      } : false}
      onChange={(pagination, filters, sorter, { action }) => {
        const { field, order } = sorter as {
          field: string
          order: 'ascend' | 'descend' | null
        }

        let currentSearchParams: any = {
          ...searchParams,
          currentPage: pagination?.current,
          pageSize: pagination?.pageSize
        }

        if (action !== 'paginate') {
          currentSearchParams = {
            ...currentSearchParams,
            ...(field && order
              ? { [field]: order }
              : {}),
            ...filters,
          }
          if(field && !order) {
            delete currentSearchParams?.[field];
          }
        }
        setSearchParams?.(currentSearchParams)
      }}
      autoWidthField={autoWidthField}
    />
  )
}
