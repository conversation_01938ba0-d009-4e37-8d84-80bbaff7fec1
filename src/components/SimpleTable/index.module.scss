@import 'src/styles/variables';

//暂时不需要分页样式，使用默认
.new-version-simple-table {
   
    :global {
        ::webkit-scrollbar-thumb {
            border-radius: 6px;
            width: 8px;
          }
          ::-webkit-scrollbar {
            width: 8px; /* 控制滚动条宽度 */
            height: 8px;
        }
        .ant-table-container .ant-table-header .ant-table-thead >  tr  > th:nth-child(1) {
               padding-left: 16px;
            
        }
        .ant-table-container .ant-table-body .ant-table-tbody  tr  > td:nth-child(1) {
            padding-left: 16px;
         
     }
        .ant-table-container .ant-table-content {
            .ant-table-thead > tr  > th {
                color: $sub-text-color;

            }
            .ant-table-thead > tr  > th:nth-child(0) {
                padding-left: 16px!important;
            }
            .ant-table-tbody > tr > td {
                color:  $primary-text-color;
            }
            .ant-table-tbody > tr{
                height: 48px;
            }
        }
        .ant-pagination {
            .ant-pagination-item a{
                color: $sub-text-color;
            }
            .ant-pagination-item-active {
                border: none;
                background-color: #F7F9FC;
              
                > a {
                    color: #3262FF;
                }
            }

            .ant-pagination-options {
                .ant-select-selector {
                    border: none;
                    color: $sub-text-color;
                }
            }
        }
    }
}