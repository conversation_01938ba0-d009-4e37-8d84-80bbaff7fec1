.mr4 {
	margin-right: 4px;
}
.mr8 {
	margin-right: 8px;
}
.mr10 {
	margin-right: 10px;
}
.mr20 {
	margin-right: 20px;
}
.color008dff {
	color: #3357ff;
}
.colorgreen {
	color: green;
}
.colorf00 {
	color: #f00;
}

.treeWrap {
  :global {
    .ant-tree-treenode {
      width: 100%;
      padding: 0;
      border-bottom: 4px solid #F7F9FC;
      background-color: #fff;
      height: 32px;
      align-items: center;
      border-radius: 4px;
    }
    .ant-tree-node-content-wrapper {
      flex: 1;
    }

    .ant-tree-node-content-wrapper:hover,
    .ant-tree-treenode:hover {
      background-color: #d6e5ff;
    }
    .ant-tree-node-content-wrapper {
      transition: none;
    }
    .ant-tree-treenode-selected {
      background-color: #d6e5ff;
    }
    .ant-tree-treenode-selected .ant-tree-switcher,
    .ant-tree-treenode
      .ant-tree-node-content-wrapper.ant-tree-node-selected {
      color: initial;
      background-color: #d6e5ff;
    }
  }
}
.treeTitleItem {
  display: flex;
  align-items: center;
  .titleTxt {
    flex: 1;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }
}