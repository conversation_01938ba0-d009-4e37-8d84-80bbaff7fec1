import React, { ReactNode } from 'react'

interface IUITablePage {
  pageTitle: string
  filter: ReactNode
  tableTitle?: string
  actionDom?: ReactNode
  tableAction?: ReactNode
  children?: ReactNode
}

export const UITablePage = (props: IUITablePage) => {
  const { pageTitle, filter, actionDom } = props
  return (
    <div className="cq-card">
      <div className="cq-card__headerbar">
        <h3 className="cq-card__title">{pageTitle}</h3>
        <div className="cq-card__action">
          {actionDom}
        </div>
      </div>
      <div className="cq-card__headerbar">
        {filter}
      </div>
      <section className="cq-table">
        {props.children}
      </section>
    </div>
  )
}
