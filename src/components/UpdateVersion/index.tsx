import React, { useEffect } from "react";
import { Modal, Descriptions } from "antd";
import { useDispatch, useRequest, useSelector } from "src/hook";
import { hideModal } from "src/store/extraSlice/modalVisibleSlice";
import { getVersion, getVersionIsCommunity, getChangelogUrl } from "src/api";
import { Iconfont } from "../CustomAntdComponents";
import { handleDownload } from 'src/util'
import i18n from 'i18next';

export const UpdateVersion = () => {
  const visible = useSelector((state) => state.modal.UpdateVersion?.visible || false);
  const isLangEn = useSelector((state) => state.login.locales) === 'en';
  const dispatch = useDispatch();
  const { data, run } = useRequest(getVersion, { manual: true });
  const { data: isCommunity, run: fetchVersion } = useRequest(
    getVersionIsCommunity,
    {
      manual: true,
    }
  );
  useEffect(() => {
    if (visible) {
      run();
      fetchVersion();
    }
  }, [visible]);

  // 下载更新日志
  const handleDownloadVersionInfo = () => {
    handleDownload({ href: `/user/versionInfo/downloadChangelog` })
  }

  // 语雀地址查看信息
  const handleLookChangeLogOnYuQue = async () => {
    const url = await getChangelogUrl();
    if (url) {
      window.open(url);
    }
  }

  return (
    <Modal
      title={i18n.t("versionInformation")}
      visible={visible}
      onCancel={() => dispatch(hideModal("UpdateVersion"))}
      footer={null}
    >
      <Descriptions layout="horizontal" column={1}>
        <Descriptions.Item label={i18n.t("versionNumber")}>
          {data?.versionNumber}
        </Descriptions.Item>
        <Descriptions.Item label={i18n.t("coreServiceVersion")}>
          {data?.serviceVersion?.join("，")}
        </Descriptions.Item>
        <Descriptions.Item label={i18n.t("releaseTime")}>
          {data?.packageTime}
        </Descriptions.Item>
        {isCommunity?.version === "community" && (
          <Descriptions.Item label={i18n.t("copyrightStatement")}>
            {i18n.t("copyrightNotice")}
          </Descriptions.Item>
        )}
      </Descriptions>
      {
        !isLangEn &&
        <>
          {/* 隐藏 VSCode调试器下载 */}
          {/* <a href={window.location.origin + '/vscode/VSCode-win32-x64-1.91.1.zip'} download target="_blank" rel="noreferrer">{i18n.t("vscodeDebuggerDownload")}</a> */}
          <span className="ml20 options" onClick={handleLookChangeLogOnYuQue}>{i18n.t("versionUpdateInformation")}</span>
          <Iconfont type="icon-xiazai" style={{ fontSize: 14, marginLeft: 10, color: "#3262FF" }} onClick={handleDownloadVersionInfo} />
        </>
      }
    </Modal>
  );
};
