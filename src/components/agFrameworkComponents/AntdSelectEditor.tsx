import React, {
  forwardRef,
  useState,
  useRef,
  useEffect,
  useImperativeHandle,
  ReactNode,
} from 'react'
import { ICellRendererParams } from '@ag-grid-community/core'
import styles from './ColumnSelectEditor.module.scss'
import { Select } from 'antd'

interface IColumnSelectEditorProps extends ICellRendererParams {
  columnsOptions: (params: any) => string[] | string[] ,
  async?: boolean,//options是不是异步获取， 默认false
  onValueChange?: (params: any) => void,
}

export const AntdSelectEditor = forwardRef(
  (props: IColumnSelectEditorProps, ref) => {

    const [isLoading, setIsLoading] = useState(false)
     const [value, setValue] = useState<string>('')
    const [newOptions, setNewOptions] = useState<string[]>([])

    const { data, colDef = {}, async = false,columnsOptions=[], onValueChange } = props

    const setSelectOptions = () => {   
      setIsLoading(true)
      let defaultSelectOptions = columnsOptions;
      if (async && typeof columnsOptions === 'function') {
        //@ts-ignore
        columnsOptions({data,colDef})?.then((res: any) => {
          defaultSelectOptions = res
          setIsLoading(false)
          setNewOptions(defaultSelectOptions)
        })
      }else {
        setIsLoading(false)
        setNewOptions(defaultSelectOptions)
      }
    }


    useEffect(() => {
      const originValue = data[colDef?.field || ''] || ''
      setValue(originValue)
      setSelectOptions()
    }, [colDef, data])

    const [editing, setEditing] = useState(true)
    const refContainer = useRef(null)

    useImperativeHandle(ref, () => {
      return {
        getValue() {
          return value
        },
        isPopup() {
          return false
        },
        afterGuiAttached: () => {
          getSelection()?.empty()
        },
      }
    })

    useEffect(() => {
      if (!editing) {
        props.api.stopEditing()
      }
    }, [editing, props.api])

    const dropdownRender = (originNode: ReactNode) => {
      return (
        <div className={styles.selecdropdown}>
          {originNode}
        </div>
      )
    }

    const options = newOptions?.map((colName) => ({
      label: colName,
      value: `${colName}`,
    }))

    const onChange = (v: string) => {
      setEditing(true)
      setValue(v)
      //@ts-ignore
      onValueChange && onValueChange({columns: {...data, [colDef.field]: v},rowIndex: props.rowIndex, oldValue: props.value, field: colDef.field })
    }

    const onDropdownVisibleChange = (open: boolean) => {
      setEditing(open)
    }

    return (
      <div
        ref={refContainer}
        tabIndex={1} // ag-grd: important - without this the key presses wont be caught
      >
        <Select
          className={'columnselect normalSelect'}
          value={value}
          options={options}
          showArrow
          size='small'
          loading={isLoading}
          dropdownRender={dropdownRender}
          onChange={(v: string) => onChange(v)}
          style={{ width: '220px' }}
          onDropdownVisibleChange={onDropdownVisibleChange}
          optionLabelProp="value"
          dropdownStyle={{ padding: 0 }}
          dropdownClassName={'selectdropdown'}
        ></Select>
      </div>
    )
  },
)
