@import 'src/styles/variables';
:global {
  .ag-theme-balham {
    .columnselect {
      min-width: 120px;
      max-width: 100%;
      overflow-y: auto;
      display: flex;
      max-height: 200px;
      flex-direction: column;
      white-space: nowrap;
      overflow-x: hidden;
      text-overflow: ellipsis;
      outline: none;
      .ant-select-multiple .ant-select-selector,
      .ant-select-multiple .ant-select-selection-item {
        border: 1px solid #eee;
        background: #f8f8f8;
      }
      .ant-select-multiple .ant-select-selector {
        border-radius: 0;
      }
    }
    .normalSelect {
     .ant-select-selector,
     .ant-select-selection-item {
        background: #f8f8f8;
        border:none;
      }
    }
    .inputNumber {
      .ant-input-number-input-wrap {
        background: #f8f8f8;
        border:none;
      }
    }
  }

  .ag-theme-balham-dark {
    .columnselect {
      min-width: 120px;
      max-width: 100%;
      overflow-y: auto;
      display: flex;
      max-height: 200px;
      flex-direction: column;
      white-space: nowrap;
      overflow-x: hidden;
      text-overflow: ellipsis;
      outline: none;
      .ant-select-selector,
      .ant-select-selection-item {
        background: #48484e;
        border: 1px solid #666;
        color: #fff;
      }
      .ant-select-multiple .ant-select-selector {
        border-radius: 0;
      }
      .ant-select-selection-item-remove {
        color: #fff;
      }
    }
    .normalSelect {
      .ant-select-selector,
      .ant-select-selection-item {
        background: #48484e;
        color: #fff;
        border:none;
       }
     }
     .inputNumber {
      .ant-input-number-input-wrap {
        background: #48484e;
        color: #fff;
        border:none;
      }
      .ant-input-number-handler-wrap {
        .ant-input-number-handler {
          color: #48484e;
        }
      }
      &:hover {
       .ant-input-number-handler-wrap {
        background: #48484e;
        .ant-input-number-handler {
          color: #fff;
        }
       }
      }
    }
  }
}

.switchwrap {
  padding: 5px 12px;
  background: #f8f8f8;
  border: 0;
  text-align: end;
  [data-theme='dark'] & {
    background: #48484e;
  }
}

:global {
  .selectdropdown {
    .ant-select-item,
    .ant-select-item-empty {
      background: #f8f8f8 !important;
      [data-theme='dark'] & {
        background: #48484e !important;
        color: #fff !important;
      }
    }
  }
}
