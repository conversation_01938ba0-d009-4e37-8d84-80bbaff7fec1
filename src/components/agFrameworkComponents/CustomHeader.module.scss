@import 'src/styles/variables.scss';

.headerWrapper {
  width: 100%;  // 改为占满整个列头宽度
  display: flex;
  justify-content: flex-start;  // 改为左对齐，让所有元素聚集在一起
  align-items: center;  // 垂直居中对齐
  line-height: 32px;
}

.headerSortersWithTooltip {
  flex: none;  // 改为不占用额外空间，只占用内容所需空间
  display: flex;
}

.headerSorters {
  margin-left: 8px;
  margin-right: 8px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  .headerSortUp,
  .headerSortDown {
    font-size: 11px;
    &.active {
      color: var(--primary-color);
    }
  }
  .headerSortDown {
    margin-top: -0.3em;
  }
}

.headerMenu {
  margin-left: 4px;  // 改为 margin-left，让筛选按钮紧跟在前面的元素后面
  .active {
    color: var(--primary-color);
  }
}

// 黑夜模式下的图标样式
[data-theme='dark'] .headerWrapper {
  // 筛选图标颜色
  .headerMenu {
    .anticon {
      color: #FAFCFF;
      
      &.active {
        color: var(--primary-color);
      }
    }
  }
  
  // 排序图标颜色
  .headerSorters {
    .headerSortUp,
    .headerSortDown {
      color: #C3C7CD;
      
      &.active {
        color: var(--primary-color);
      }
    }
  }
  
  // 信息图标颜色
  .ant-typography {
    color: #FAFCFF;
  }
  
  .anticon-info-circle {
    color: #C3C7CD;
  }
}

