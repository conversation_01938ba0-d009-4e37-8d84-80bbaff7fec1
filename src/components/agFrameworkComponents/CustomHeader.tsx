import React, { useRef, useState, useEffect, useMemo, useReducer, useCallback } from 'react'
import { Dropdown, Menu, Tooltip } from 'antd'
import { InfoCircleOutlined } from '@ant-design/icons'
import {
  FilterOutlined,
  CaretUpFilled,
  CaretDownFilled,
} from '@ant-design/icons'
import { IHeaderParams, Column } from '@ag-grid-community/core'
import classNames from 'classnames'
import styles from './CustomHeader.module.scss'
import { setTooltipTitleSty } from 'src/util'
import { handleCopyAllTitle, handleHeaderCopyCellTitle } from './ResultMenuFunc'
import { useContextMenuLogic, MenuHandlers } from './hooks/useContextMenuLogic'

type SortStatus = 'asc' | 'desc' | ''

interface CustomHeaderProps extends IHeaderParams {
  headerName: string
  comment: string,
  copyable: boolean,
  sortModel?: any
  onHeaderLabelClick?: (column: Column, event: React.MouseEvent) => void
  // 新增的选择上下文相关的 props
  selectionContext?: any
  getSelectionContext?: () => any
  allowCreateSql?: boolean
  createSqlDisabled?: boolean
  // 结果集生成处理函数
  handleResInsert?: (params: any) => void
  handleResUpdate?: (params: any) => void
  handleResDelete?: (params: any) => void
  // 获取选中行数据函数
  getSelectedRowsDataForResultSet?: () => any[]
  // 动态菜单处理函数
  handleCopySelectedColumnTitles?: (params: any) => void
  handleCopySelectedColumnData?: (params: any) => void
  handleCopySelectedFullColumnDataWithTitle?: (params: any) => void
  // 复制全部处理函数
  handleCopyAll?: (params: any) => void
  // 清除列选择的方法
  clearColumnSelections?: () => void
}

export const CustomHeader: React.FC<CustomHeaderProps> = (props) => {
  const {
    headerName,
    comment,
    enableMenu,
    enableSorting,
    column,
    showColumnMenu,
    setSort,
    sortModel,
    onHeaderLabelClick,
    // 新增的选择上下文相关的 props
    selectionContext,
    getSelectionContext,
    allowCreateSql,
    createSqlDisabled,
    copyable,
    // 结果集生成处理函数（从 props 中获取）
    handleResInsert,
    handleResUpdate,
    handleResDelete,
    // 获取选中行数据函数
    getSelectedRowsDataForResultSet,
    // 动态菜单处理函数（从 props 中获取）
    handleCopySelectedColumnTitles,
    handleCopySelectedColumnData,
    handleCopySelectedFullColumnDataWithTitle,
    // 复制全部处理函数
    handleCopyAll,
    // 清除列选择的方法
    clearColumnSelections,
    // 从 Ag-Grid 的 api 中获取 context
    api,
    columnApi
  } = props

  const [, forceUpdate] = useReducer((r) => r + 1, 0)
  const [sortStatus, setSortStatus] = useState<SortStatus>('')
  const filterActive = column.isFilterActive()

  const menuButtonRef = useRef<any>(null)
  const tooltipRef = useRef<any>(null)

  const nextSortStatus = useMemo(() => {
    switch (sortStatus) {
      case 'asc':
        return 'desc'
      case 'desc':
        return ''
      default:
        return 'asc'
    }
  }, [sortStatus])

  // 构建处理函数对象
  const handlers: MenuHandlers = {
    // 特殊处理：表头的复制标题处理
    handleCopyCellTitle: () => handleHeaderCopyCellTitle?.(headerName),
    handleCopyAllTitle: () => handleCopyAllTitle?.(column),
    // 复制全部处理函数
    handleCopyAll,
    // 动态菜单处理函数
    handleCopySelectedColumnTitles,
    handleCopySelectedColumnData,
    handleCopySelectedFullColumnDataWithTitle,
    // 结果集生成
    handleResInsert,
    handleResUpdate,
    handleResDelete,
    getSelectedRowsDataForResultSet,
  }

  // 创建获取选择上下文的函数
  // 对于列头右键，需要智能判断：
  // - 如果是列选择且选中的是当前列，显示列菜单
  // - 如果是其他类型的选择（单元格/行），显示默认菜单
  const getSelectionContextFn = useCallback(() => {
    const context = getSelectionContext ? getSelectionContext() : selectionContext;
    
    // 如果没有选择上下文，显示默认菜单
    if (!context) {
      return null;
    }
    
    // 如果是列选择，检查是否选中了当前列
    if (context.selectionMode === 'column') {
      // 如果选中的列包含当前列，显示列菜单
      if (context.selectedColumns && context.selectedColumns.includes(column.getColId())) {
        return context;
      }
    }
    
    // 其他情况（单元格选择、行选择等）显示默认菜单
    return null;
  }, [getSelectionContext, selectionContext, column])

  // 使用统一的上下文菜单逻辑
  const {
    renderOverlay,
    overlayClassName
  } = useContextMenuLogic({
    column,
    headerName,
    getSelectionContext: getSelectionContextFn,
    allowCreateSql,
    createSqlDisabled,
    copyable,
    handlers,
    clearColumnSelections,
    isHeaderMode: true,
    api,
    columnApi,
  })


 // 设置默认排序状态
useEffect(() => {
  if (sortModel) {
    const columnSort = sortModel?.find((sort: any) => sort?.colId === column.getColId());
    if (columnSort) {
      setSortStatus(columnSort.sort); // 设置为当前列的排序状态
    } else {
      setSortStatus(''); // 如果没有排序，重置状态
    }
  }
}, [JSON.stringify(sortModel), column]);

  useEffect(() => {
    const onSortChanged = () => {
      let sortStatus: SortStatus = ''
      if (column.isSortAscending()) sortStatus = 'asc'
      if (column.isSortDescending()) sortStatus = 'desc'
      setSortStatus(sortStatus)
    }
    const onFilterChanged = () => {
      forceUpdate()
    }

    const debounce = (fn: ()=>void, wait: number) => {
      let timeId: any = null
      return () => {
        if(timeId !== null) {
          clearTimeout(timeId)
        }
        timeId = setTimeout(fn, wait)
      }
    }
    column.addEventListener('sortChanged', onSortChanged)
    column.addEventListener('filterChanged', debounce(onFilterChanged, 1000))
    return () => {
      column.removeEventListener('sortChanged', onSortChanged)
      column.removeEventListener('filterChanged', debounce(onFilterChanged, 1000))
    }
  }, [column])

  const handleVisibleChange = useCallback(() => {
    setTooltipTitleSty(tooltipRef, 'customTooltip');
  }, [tooltipRef, comment])

  const titleContent = (
    <div style={{display: 'flex', flexDirection: 'row', justifyContent: 'center', alignItems: 'center'}}>
        <span>{headerName}</span>
      {!!comment && comment.trim().length > 0 && <InfoCircleOutlined style={{marginLeft: '5px'}} />}
        {enableSorting && (
          <span
            className={styles.headerSorters}
            onClick={(e) => {
              e.stopPropagation()
              if (!enableSorting) return
              setSort(nextSortStatus, e.shiftKey)
            }}
          >
            <CaretUpFilled
              className={classNames(
                styles.headerSortUp,
                sortStatus === 'asc' && styles.active,
              )}
            />
            <CaretDownFilled
              className={classNames(
                styles.headerSortDown,
                sortStatus === 'desc' && styles.active,
              )}
            />
          </span>
        )}
      </div>
  )

  return (
    <Dropdown
      overlay={renderOverlay}
      trigger={['contextMenu']}
      overlayClassName={overlayClassName}
    >
      <div
        className={styles.headerWrapper}
        onClick={(event) => {
          // 阻止内部排序/筛选图标点击事件的冒泡，避免重复处理
          if (event.target !== event.currentTarget && (event.target as HTMLElement).closest(`.${styles.headerSorters}, .${styles.headerMenu}`)) {
            return;
          }
          // 确保是鼠标左键点击
          if (event.button === 0 && onHeaderLabelClick) {
            onHeaderLabelClick(column, event);
          }
        }}
      >
        <div
          className={styles.headerSortersWithTooltip}
        >
          {
            comment ?
              <Tooltip
                ref={tooltipRef}
                title={<div className={"customTooltip"}>{comment}</div>}
                overlayStyle={{ whiteSpace: 'pre-line' }}
                onVisibleChange={handleVisibleChange}
              >
                {
                  titleContent
                }
              </Tooltip>
              : titleContent
          }
        </div>
        {enableMenu && (
          <span
            className={styles.headerMenu}
            ref={(menuButton) => {
              menuButtonRef.current = menuButton
            }}
          >
            <FilterOutlined
              className={classNames(filterActive && styles.active)}
              onClick={(e) => {
                showColumnMenu(menuButtonRef.current)
                e.stopPropagation()
              }}
            />
          </span>
        )}
      </div>
    </Dropdown>
  )
}
