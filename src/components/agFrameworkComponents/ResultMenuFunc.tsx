import type { Column, ICellRendererParams } from '@ag-grid-community/core'
import { cloneDeep } from 'lodash'
import { copyTextToClipboard } from "src/util"

// 将单元格数据转换为字符串
const stringifyCellData = (cellData: unknown) => {
  if (typeof cellData === 'string') {
    return cellData
  }
  return JSON.stringify(cellData)
}

// 复制单元格
export const handleCopyCell = (params: ICellRendererParams) => {
  const cellData = stringifyCellData(params.value)
  copyTextToClipboard(cellData)
}

// 复制单行/多行
export const handleCopyRow = (context: ICellRendererParams) => {
  const selectedRows = context.api.getSelectedRows();
  const columnDefs = context.columnApi.getAllDisplayedColumns();
  const rowData = selectedRows.map(row => {
    // 通过columnDefs获取表格标题按标题顺序复制内容就能保证顺序一致
    // @ts-ignore
    // 去除第一个序号列
    const columns: any[] = columnDefs.slice(1)
    return columns.map(column => row[column.colDef.field]).join('\t');
  });
  const clipboardData = rowData.join('\n');
  copyTextToClipboard(clipboardData);
}

// 复制全部
export const handleCopyAll = (context: ICellRendererParams, allData: any[]) => {
  const currentAllData = cloneDeep(allData);
  const columnDefs = context.columnApi.getAllDisplayedColumns();
  const copyData = currentAllData.map(row => {
    const columns: any[] = columnDefs.slice(1)
    return columns.map(column => row[column.colDef.field]).join('\t');
  });
  const clipboardData = copyData.join('\n');
  copyTextToClipboard(clipboardData);
}

// 复制单元格标题
export const handleCopyCellTitle = (context: ICellRendererParams) => {
  // 获取当前选中单元格所在列的标题
  const curHeaderName = context?.column?.getColId() ?? '';
  copyTextToClipboard(curHeaderName);
}

// header -- 复制单元格标题
export const handleHeaderCopyCellTitle = (headerName: string) => {
  copyTextToClipboard(headerName);
}

// 复制全部标题 ICellRendererParams | Column
export const handleCopyAllTitle = (context: any) => {
  const columnDefs = context?.columnApi?.getAllDisplayedColumns();
  // 去除第一个序号列
  const allHeaderName = columnDefs.slice(1).map((column: Column) => column?.getColId());
  copyTextToClipboard(allHeaderName.join('\t'));
}

// 与标题一起复制
export const handleCopyWithTitle = (context: ICellRendererParams) => {
  const selectedRows = context.api.getSelectedRows();
  const columnDefs = context.columnApi.getAllDisplayedColumns();
  const allHeaderName = columnDefs.slice(1).map((column: Column) => column?.getColId());
  const rowData = selectedRows.map(row => {
    return allHeaderName.map((field: string) => row[field]).join('\t');
  });
  const clipboardData = allHeaderName.join('\t') + '\n' + rowData.join('\n');
  copyTextToClipboard(clipboardData);
}

// 复制选中单元格的标题（在全选状态下复制所有列标题）
export const handleCopySelectedAllCellsTitles = (context: ICellRendererParams) => {
  const columnDefs = context?.columnApi?.getAllDisplayedColumns();
  // 去除第一个序号列
  const allHeaderName = columnDefs.slice(1).map((column: Column) => column?.getColId());
  copyTextToClipboard(allHeaderName.join('\t'));
}

// 全选状态：与标题一起复制全部（性能优化版本）
export const handleCopySelectAllWithTitle = (context: ICellRendererParams, allData: any[]) => {
  const currentAllData = cloneDeep(allData);
  const columnDefs = context.columnApi.getAllDisplayedColumns();
  // 去除第一个序号列
  const columns: any[] = columnDefs.slice(1);
  const allHeaderName = columns.map((column: Column) => column?.getColId());
  
  // 构建数据行
  const rowData = currentAllData.map(row => {
    return allHeaderName.map((field: string) => row[field]).join('\t');
  });
  
  // 标题行 + 数据行
  const clipboardData = allHeaderName.join('\t') + '\n' + rowData.join('\n');
  copyTextToClipboard(clipboardData);
}
