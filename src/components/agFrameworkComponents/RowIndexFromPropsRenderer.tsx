import React from 'react'
import { useContextMenu } from './useContextMenu'
import type { ICellRendererParams } from '@ag-grid-community/core'

export const RowIndexFromPropsRenderer = (params: ICellRendererParams & { newRowIndex: number}) => {
  const {
    node: { rowIndex = 0, data = null },
    newRowIndex = 0
  } = params

  // == 可检查null和undefined
  if (rowIndex == null || data == null ) return null;
 
  const realRowIndex = newRowIndex + rowIndex + 1;

  return <div>{realRowIndex}</div>
}

export const RowIndexFromPropsRendererWithContextMenu = (
  params: ICellRendererParams & { newRowIndex: number},
) => {
  const [wrapper] = useContextMenu(params)

  return wrapper(RowIndexFromPropsRenderer(params))
}
