.markRenderer {
  width: 40px;
  text-align: center;
}
.placeholder {
  opacity: 0.65;
  // pointer-events: none;
}
.textCell {
  overflow: hidden;
  white-space: pre;
  text-overflow: ellipsis;

  .textFileSize {
    padding: 2px 4px;
    background: #bebebe;
    margin-right: 4px;
  }
}

.cellRenderTooltip {
  :global {
    .ant-tooltip-inner {
      background-color: rgba(255, 255, 255, 1);
      color: #8A939F
    }

    .ant-tooltip-arrow-content {
      background-color: rgba(255, 255, 255, 1);
    }
  }
}

.clickableCell {
  cursor: pointer;
  color: #1890ff;
  text-decoration: underline;

  &:hover {
    color: #40a9ff;
    // background-color: #f0f8ff;
  }
}