import React, { useEffect, useMemo, useState, useRef, useCallback } from 'react'
import { useContextMenu } from './useContextMenu'
import { ICellRendererParams } from '@ag-grid-community/core'
import styles from './SimpleMarkRenderer.module.scss'
import { Tooltip } from 'antd'
import { useDispatch, useSelector } from 'src/hook'
import { isEmpty } from 'lodash'
import { isCellTag } from 'src/pageTabs/queryPage/resultTabs/resultContentGrid/CellViewerModal'
import { setDoubleClickCell, createCursorResultTab } from 'src/pageTabs/queryPage/resultTabs/resultTabsSlice'
import i18n from 'i18next';

export const SimpleTextRenderer = (params: ICellRendererParams) => {
  const { doubleClickCell } = useSelector((state) => state.resultTabs)
  const [tooltipShow, setTooltipShow] = useState<boolean>(false)
  const [tooltipTitle, setTooltipTitle] = useState<string>('')
  const dispatch = useDispatch()
  const { data, value } = params
  const [formatValue, setFormatValue] = useState<any>()
  const timeoutRef = useRef<NodeJS.Timeout | null>(null)

  // 检测当前单元格是否包含 cursorValue
  const hasCursorValue = useMemo(() => {
    const fieldName = params?.colDef?.field
    if (!fieldName || !data) return false
    const cellData = data[fieldName]
    return cellData && typeof cellData === 'object' && cellData.cursorValue !== undefined
  }, [data, params?.colDef?.field])

  // 获取 queryKey 从 context
  const queryKey = useMemo(() => {
    return params?.context?.queryKey
  }, [params?.context?.queryKey])

  // 处理单元格点击事件
  const handleCellClick = useCallback((event: React.MouseEvent) => {
    if (hasCursorValue && queryKey) {
      event.preventDefault()
      event.stopPropagation()

      const fieldName = params?.colDef?.field
      if (!fieldName) return

      const cellData = data[fieldName]
      const cursorValue = cellData?.cursorValue

      if (cursorValue) {
        const rowIndex = params?.rowIndex ?? 0
        dispatch(createCursorResultTab({
          queryKey,
          cursorData: cursorValue,
          fieldName,
          rowIndex: rowIndex + 1 // 行号从1开始显示
        }))
      }
    }
  }, [hasCursorValue, queryKey, params, data, dispatch])

  useEffect(()=>{
    // 如果为时间类型，则调用get方法获取格式化后的值
    const key = params?.colDef?.field
    if(data?.[`get${key}`] && typeof data?.[`get${key}`] === "function"){
      const res = data?.[`get${key}`]?.()
      setFormatValue(res || value)
    }
    else {
      // 如果 cellData 是对象且包含 value 字段，使用 value；否则使用原始 value
      if (key && data) {
        const cellData = data[key]
        if (cellData && typeof cellData === 'object' && cellData.value !== undefined) {
          setFormatValue(cellData.value)
        } else {
          setFormatValue(value)
        }
      } else {
        setFormatValue(value)
      }
    }
  },[params, data, value])

  useEffect(()=>{
    if(!isEmpty(doubleClickCell) && !isEmpty(params)) {
      const { colDef = {}, rowIndex, column } = params
      //@ts-ignore
      const { editable = true } = colDef
      const cellUnEditable = !editable || (typeof editable === 'function' && !editable(params))
      if(rowIndex === doubleClickCell.rowIndex && column.getColId() === doubleClickCell.colId && cellUnEditable) {
        setTooltipShow(true)
      }
      else setTooltipShow(false)
    }
  },[doubleClickCell, params])

  useEffect(() => {
    if ( !isEmpty(params)) {
      const { colDef = {} } = params
      //@ts-ignore
      const { editable = true, context = {} } = colDef
      const { editable : tableEditable = true, columnEditable, unEditableReason, columnUnEditableReason, renderType } = context
      // 设置提示内容
      if (!editable || (typeof editable === 'function' && !editable(params))) {
        let tipContent: string = ''
        if (!tableEditable) tipContent = unEditableReason
        else if (!columnEditable) tipContent = columnUnEditableReason
        else if (renderType === 'binary') tipContent = `${i18n.t("unsupportedFieldType")}[binary]`
        else if (Object.values(params?.data || {})?.some((value) =>
          isCellTag(String(value)),
        )) tipContent = i18n.t("fieldTooLarge")
        setTooltipTitle(tipContent)
      }
      else setTooltipTitle('')
    }
  }, [params])

  useEffect(()=>{
    if(tooltipShow) {
      // 清除之前的定时器
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }
      
      timeoutRef.current = setTimeout(() => {
        setTooltipShow(false)
        dispatch(setDoubleClickCell({}))
      }, 3000)
    }
    
    // 清理函数
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }
    }
  },[tooltipShow, dispatch])

  let cell = (
    <div
      className={`${styles.textCell} ${hasCursorValue ? styles.clickableCell : ''}`}
      onClick={hasCursorValue ? handleCellClick : undefined}
    >
      {/* {params.value ?? ''} */}
      {typeof formatValue === 'boolean'
        ? formatValue.toString()
        : formatValue ?? ''}
    </div>
)
  //含有binary类型 都展示文件大小，所以增加了columnCQSize字段
  const binaryTypeValueWithSize = params?.node?.data?.[`${params.column.getColId()}CQSize`];
  if (binaryTypeValueWithSize) {
    cell = (
      <div
        className={`${styles.textCell} ${hasCursorValue ? styles.clickableCell : ''}`}
        onClick={hasCursorValue ? handleCellClick : undefined}
      >
        <span className={styles.textFileSize}>{binaryTypeValueWithSize}</span>{formatValue}
      </div>
    )
  }

  //若formatValue 为对象  为保证不报错 转为字符串显示
  if (typeof formatValue === 'object' && formatValue !== null) {
    cell = (
      <div
        className={hasCursorValue ? styles.clickableCell : ''}
        onClick={hasCursorValue ? handleCellClick : undefined}
      >
        {'<' + JSON.stringify(formatValue) + '>'}
      </div>
    )
  }

  if (formatValue === null) {
    cell = (
      <div
        className={`${styles.placeholder} ${hasCursorValue ? styles.clickableCell : ''}`}
        onClick={hasCursorValue ? handleCellClick : undefined}
      >
        {`<null>`}
      </div>
    )
    // cell = <input className={styles.placeholder} placeholder="<null>" />
  }

  return useMemo(() => {
    // 如果没有 tooltip 内容或不显示 tooltip，直接返回 cell
    if (!tooltipTitle || !tooltipShow) {
      return cell
    }
    
    return (
      <Tooltip
        key={params?.rowIndex + params?.column?.getColId()}
        placement='right'
        title={tooltipTitle}
        visible={tooltipShow}
        overlayClassName={styles.cellRenderTooltip}>
        {cell}
      </Tooltip>
    )
  }, [params, tooltipShow, tooltipTitle, formatValue, hasCursorValue, handleCellClick, cell])
}

export const SimpleTextRendererWithContextMenu = (
  params: ICellRendererParams,
) => {
  const [wrapper] = useContextMenu(params)

  return wrapper(SimpleTextRenderer(params))
}
