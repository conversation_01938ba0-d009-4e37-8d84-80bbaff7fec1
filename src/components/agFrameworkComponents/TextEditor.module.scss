@import 'src/styles/variables';
.wrapper {
  width: 100%;
  height: 26px;
  border: none;
  outline: none;
}
.inputWrapper {
  padding-left: 9px;
  background: #fff;
  [data-theme='dark'] & {
    background: $blue-43;
  }
}
.nullText {
  padding-left: 2px;
}
.input {
  height: 100%;
  width: 100%;
  border: none;
  outline: none;
  padding-right: 26px;
  background: #fff;
  [data-theme='dark'] & {
    background: $blue-43;
  }
}
.nullSetter {
  width: 26px;
  height: 26px;
  position: absolute;
  top: 0;
  right: 0;
  font-size: 26px;
  cursor: pointer;
  opacity: 0.55;
}
