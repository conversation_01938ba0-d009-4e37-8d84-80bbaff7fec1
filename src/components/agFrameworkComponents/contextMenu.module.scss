.contextMenu {
  .copyAllTitle {
    display: flex;
    justify-content: flex-start;
    align-items: center;

    .copyAllTip {
      margin-left: 6px;
      color: #3C3D3E;
      font-size: 14px;
      
      // 黑夜模式下的提示颜色
      [data-theme='dark'] & {
        color: #C3C7CD;
      }
    }
  }
}

.dark {
  :global {
    .ant-dropdown-menu {
      background-color: #43434A !important;
      border: 1px solid #3A3A42 !important;
      box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.48), 0 6px 16px 0 rgba(0, 0, 0, 0.32), 0 9px 28px 8px rgba(0, 0, 0, 0.2) !important;
    }
    
    .ant-dropdown-menu-item,
    .ant-dropdown-menu-submenu-title {
      color: #FAFCFF !important;
      background-color: transparent !important;
      
      .anticon {
        color: #FAFCFF !important;
      }
      
      &:hover {
        background-color: #3A3A42 !important;
        color: #FAFCFF !important;
        
        .anticon {
          color: #FAFCFF !important;
        }
      }
      
      &:active {
        background-color: #2F2F37 !important;
        
        .anticon {
          color: #FAFCFF !important;
        }
      }
      
      &.ant-dropdown-menu-item-disabled {
        color: #7D848E !important;
        background-color: transparent !important;
        
        .anticon {
          color: #7D848E !important;
        }
        
        &:hover {
          background-color: transparent !important;
          color: #7D848E !important;
          
          .anticon {
            color: #7D848E !important;
          }
        }
      }
    }
    
    .ant-dropdown-menu-item-divider {
      background-color: #3A3A42 !important;
    }
    
    .ant-dropdown-menu-submenu-arrow {
      color: #FAFCFF !important;
      
      &::before,
      &::after {
        background-color: #FAFCFF !important;
      }
    }
    
    .ant-dropdown-menu-submenu-title.ant-dropdown-menu-item-disabled {
      .ant-dropdown-menu-submenu-arrow {
        color: #7D848E !important;
        
        &::before,
        &::after {
          background-color: #7D848E !important;
        }
      }
    }
    
    // 覆盖特定的禁用子菜单样式，使用更高权重的选择器
    .ant-dropdown-menu-submenu.ant-dropdown-menu-submenu-disabled .ant-dropdown-menu-submenu-title,
    .ant-dropdown-menu-submenu.ant-dropdown-menu-submenu-disabled .ant-dropdown-menu-submenu-title .ant-dropdown-menu-submenu-arrow-icon {
      color: #7D848E !important;
      background-color: transparent !important;
      cursor: not-allowed !important;
    }
  }
}