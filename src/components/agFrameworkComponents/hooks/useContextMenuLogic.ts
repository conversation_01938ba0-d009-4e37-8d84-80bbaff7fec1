import { useCallback, useMemo, useRef } from 'react'
import { useSelector } from 'src/hook/reactReduxHooks'
import type { Column, ICellRendererParams } from '@ag-grid-community/core'
import { MenuGenerator } from '../utils/MenuGenerator'
import { MenuEventHandler } from '../utils/MenuEventHandler'
import { StyleHelper } from '../utils/StyleHelper'
import contextMenuStyles from '../contextMenu.module.scss'

/**
 * 菜单事件处理函数集合
 */
export interface MenuHandlers {
  // 查看操作
  handleViewCell?: (params: ICellRendererParams) => void
  handleViewRow?: (params: ICellRendererParams) => void
  
  // 复制操作
  handleCopyCell?: (params: ICellRendererParams) => void
  handleCopyRow?: (params: ICellRendererParams) => void
  handleCopyAll?: (params: ICellRendererParams) => void
  handleCopyCellTitle?: (params: ICellRendererParams) => void
  handleCopyAllTitle?: (params: ICellRendererParams) => void
  handleCopyWithTitle?: (params: ICellRendererParams) => void
  
  // 特殊操作
  handleCloneRow?: (params: ICellRendererParams) => void
  handlePasteRow?: (params: ICellRendererParams) => void
  
  // 结果集生成
  handleResInsert?: (params: ICellRendererParams) => void
  handleResUpdate?: (params: ICellRendererParams) => void
  handleResDelete?: (params: ICellRendererParams) => void
  
  // 动态菜单处理函数
  handleCopySelectedColumnTitles?: (params: ICellRendererParams) => void
  handleCopySelectedColumnData?: (params: ICellRendererParams) => void
  handleCopySelectedCellsData?: (params: ICellRendererParams) => void
  handleCopySelectedCellsTitles?: (params: ICellRendererParams) => void
  handleCopySelectedCellsWithTitle?: (params: ICellRendererParams) => void
  handleCopySelectedFullColumnDataWithTitle?: (params: ICellRendererParams) => void
  
  // 全选专用处理函数
  handleCopySelectAllWithTitle?: (params: ICellRendererParams) => void
  handleCopySelectedAllCellsTitles?: (params: ICellRendererParams) => void
  
  // 特殊处理函数
  getSelectedRowsDataForResultSet?: () => any[]
}

/**
 * 上下文菜单逻辑 Hook 的参数接口
 */
export interface UseContextMenuLogicProps {
  // 基础参数
  column?: Column
  headerName?: string
  
  // 单元格参数 - 用于获取真实的单元格数据
  cellParams?: ICellRendererParams
  
  // 上下文获取
  getSelectionContext: () => any
  
  // 权限配置
  allowCreateSql?: boolean
  createSqlDisabled?: boolean
  copyable?: boolean
  canCopyCell?: boolean
  allowClone?: boolean
  
  // 事件处理函数
  handlers: MenuHandlers
  
  // 特殊配置
  clearColumnSelections?: () => void
  isHeaderMode?: boolean // 区分是表头还是单元格
  
  // API 相关
  api?: any
  columnApi?: any
  
  // 禁用状态
  disableContextMenu?: boolean
}

/**
 * 统一的上下文菜单逻辑 Hook
 */
export const useContextMenuLogic = (props: UseContextMenuLogicProps) => {
  const {
    column,
    headerName,
    cellParams,
    getSelectionContext,
    allowCreateSql = false,
    createSqlDisabled = false,
    copyable = true,
    canCopyCell = true,
    allowClone = false,
    handlers,
    clearColumnSelections,
    isHeaderMode = false,
    api,
    columnApi,
    disableContextMenu = false
  } = props

  const theme = useSelector(state => state.appearance.theme)

  // 使用 ref 保持函数引用稳定
  const clearColumnSelectionsRef = useRef(clearColumnSelections)
  
  // 使用 ref 保存稳定的配置项，避免触发重新渲染
  const configRef = useRef({
    theme,
    isHeaderMode,
    headerName,
    column,
    allowCreateSql,
    createSqlDisabled,
    copyable,
    canCopyCell,
    allowClone
  })
  
  // 使用 ref 保存菜单点击处理相关的参数
  const menuClickRef = useRef({
    cellParams,
    column,
    api,
    columnApi,
    handlers
  })
  
  // 更新 ref 的值但不触发重新渲染
  clearColumnSelectionsRef.current = clearColumnSelections
  configRef.current = {
    theme,
    isHeaderMode,
    headerName,
    column,
    allowCreateSql,
    createSqlDisabled,
    copyable,
    canCopyCell,
    allowClone
  }
  menuClickRef.current = {
    cellParams,
    column,
    api,
    columnApi,
    handlers
  }

  // 创建菜单点击处理器，使用稳定的 ref 值避免重复渲染
  const handleMenuClick = useCallback(
    (key: string) => {
      const { cellParams, column, api, columnApi, handlers } = menuClickRef.current
      // 如果有真实的单元格参数，直接使用；否则创建模拟参数（兼容表头模式）
      const params = cellParams || MenuEventHandler.createMockParams(
        column,
        api,
        columnApi
      )
      
      MenuEventHandler.handleMenuClick(key, params, handlers)
    },
    [] // 空依赖数组，因为我们使用 ref 获取最新值
  )

  // 使用 ref 保存 getSelectionContext 函数避免闭包问题
  const getSelectionContextRef = useRef(getSelectionContext)
  getSelectionContextRef.current = getSelectionContext
  
  // 创建一个稳定的渲染函数，避免 Antd Dropdown 重复调用
  const renderOverlay = useCallback(() => {
    // 在菜单显示时动态获取最新的选择上下文和配置
    const selectionContext = getSelectionContextRef.current()
    const config = configRef.current
    
    if (!selectionContext) {
      return MenuGenerator.generateDefaultMenu({
        handleMenuClick,
        ...config
      })
    }

    return MenuGenerator.generateContextMenu({
      selectionContext: selectionContext,
      handleMenuClick,
      ...config,
      clearColumnSelections: clearColumnSelectionsRef.current
    })
  }, []) // 现在可以使用空依赖数组，因为所有值都通过 ref 获取

  // 生成样式类名
  const overlayClassName = useMemo(() => {
    return StyleHelper.getOverlayClassName(theme, contextMenuStyles)
  }, [theme])

  return {
    renderOverlay,
    handleMenuClick,
    overlayClassName,
    disableContextMenu
  }
}