import type { ICellRendererParams } from '@ag-grid-community/core'
import type { SelectionContext } from '../../pageTabs/queryPage/resultTabs/resultContentGrid/types/selection'

export interface IGridContext {
  status: 'NORMAL' | 'UPDATE' | 'INSERT' | 'DELETE' | 'CLONE'
  disableContextMenu?: boolean
  copyable?: boolean
  allowClone?: boolean
  canCopyCell?: boolean
  allowCreateSql?: boolean // 结果集生成的显隐开关
  createSqlDisabled?:boolean // 结果集生成的置灰开关
  canSave?: boolean
  handleCopyCell?: (params: ICellRendererParams) => void
  handleCopyRow?: (params: ICellRendererParams) => void
  handleCopyAll?: (params: ICellRendererParams) => void
  handleCopyCellTitle?: (params: ICellRendererParams) => void
  handleCopySelectAllWithTitle?: (params: ICellRendererParams) => void
  handleCopyAllTitle?: (params: ICellRendererParams) => void
  handleCopyWithTitle?: (params: ICellRendererParams) => void
  handlePasteRow?: (params: ICellRendererParams) => void
  handleViewCell?: (params: ICellRendererParams) => void
  handleViewRow?: (params: ICellRendererParams) => void
  handleCloneRow?: (params: ICellRendererParams) => void
  handleResInsert?: (params: ICellRendererParams) => void
  handleResUpdate?: (params: ICellRendererParams) => void
  handleResDelete?: (params: ICellRendererParams) => void
  // 新增的动态菜单处理函数
  handleCopySelectedColumnTitles?: (params: ICellRendererParams) => void
  handleCopySelectedColumnData?: (params: ICellRendererParams) => void
  handleCopySelectedCellsData?: (params: ICellRendererParams) => void
  handleCopySelectedCellsTitles?: (params: ICellRendererParams) => void
  handleCopySelectedCellsWithTitle?: (params: ICellRendererParams) => void
  handleCopySelectedFullColumnDataWithTitle?: (params: ICellRendererParams) => void
  getSelectedRowsDataForResultSet?: () => any[]
  // 新增：获取选择上下文的方法
  getSelectionContext?: () => SelectionContext | null
  // 新增：直接的选择上下文属性
  selectionContext?: SelectionContext | null
  // 新增：选中单元格的方法
  selectCell?: (params: ICellRendererParams) => void
  copySelectAllWithTitle?: (params: ICellRendererParams) => void
  handleCopySelectedAllCellsTitles?: (params: ICellRendererParams) => void
}
