import React, { useRef, useEffect } from 'react'
import { Dropdown, Menu } from 'antd'
import type { ICellRendererParams } from '@ag-grid-community/core'
import type { IGridContext } from './types.agFrameworkComponents'
import { useContextMenuLogic, MenuHandlers } from './hooks/useContextMenuLogic'

export const useContextMenu = (params: ICellRendererParams) => {
  const { context, column } = params
  const [allowPaste, setAllowPaste] = React.useState<boolean>(false)
  const [showPaste, setShowPaste] = React.useState<boolean>(false)
  const [menuContent, setMenuContent] = React.useState<React.ReactElement | null>(null)
  const timeoutRef = useRef<NodeJS.Timeout | null>(null)

  // 创建获取选择上下文的函数，优先使用直接属性，否则使用方法
  const getSelectionContext = () => {
    return (context as IGridContext)?.getSelectionContext?.() || null
  }
  

  const gridContext = (context as IGridContext) || {}
  const {
    disableContextMenu,
    copyable,
    canCopyCell,
    allowClone,
    allowCreateSql,
    createSqlDisabled,
    handleCopyCell,
    handleCopyRow,
    handleCopyAll,
    handleCopySelectAllWithTitle,
    handleCopyAllTitle,
    handleCopyWithTitle,
    handleViewCell,
    handleViewRow,
    handleCloneRow,
    handleResInsert,
    handleResUpdate,
    handleResDelete,
    handlePasteRow,
    handleCopyCellTitle,
    handleCopySelectedColumnTitles,
    handleCopySelectedColumnData,
    handleCopySelectedCellsData,
    handleCopySelectedCellsTitles,
    handleCopySelectedCellsWithTitle,
    handleCopySelectedFullColumnDataWithTitle,
    getSelectedRowsDataForResultSet,
    handleCopySelectedAllCellsTitles
  } = gridContext

  // 构建处理函数对象
  const handlers: MenuHandlers = {
    handleViewCell,
    handleViewRow,
    handleCopyCell,
    handleCopyRow,
    handleCopyAll,
    handleCopyCellTitle,
    handleCopySelectAllWithTitle,
    handleCopyAllTitle,
    handleCopyWithTitle,
    handleCloneRow,
    handlePasteRow,
    handleResInsert,
    handleResUpdate,
    handleResDelete,
    handleCopySelectedColumnTitles,
    handleCopySelectedColumnData,
    handleCopySelectedCellsData,
    handleCopySelectedCellsTitles,
    handleCopySelectedCellsWithTitle,
    handleCopySelectedFullColumnDataWithTitle,
    getSelectedRowsDataForResultSet,
    handleCopySelectedAllCellsTitles
  }

  // 使用统一的上下文菜单逻辑
  const {
    renderOverlay,
    overlayClassName
  } = useContextMenuLogic({
    column,
    cellParams: params, // 传递真实的单元格参数
    getSelectionContext,
    allowCreateSql,
    createSqlDisabled,
    copyable,
    canCopyCell,
    allowClone,
    handlers,
    isHeaderMode: false,
    api: params.api,
    columnApi: params.columnApi,
    disableContextMenu
  })

  // 组件卸载时清理定时器
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }
    }
  }, [])

  const wrapper = (wrapped: React.ReactElement | null) => {
    if (wrapped == null) return null
    // TODO: 更新 antd 版本，补充 destroyPopupOnHide
    return (
      <Dropdown
        overlay={menuContent || <Menu />}
        disabled={disableContextMenu}
        trigger={['contextMenu']}
        overlayClassName={overlayClassName}
        onVisibleChange={(visible) => {
          if (visible) {
            // 清除之前的定时器
            if (timeoutRef.current) {
              clearTimeout(timeoutRef.current)
            }
            
            timeoutRef.current = setTimeout(() => {
              const content = renderOverlay()
              setMenuContent(content)
              
              // 处理剪贴板权限检查
              let protocolStr = document.location.protocol;
              let hostName = document.location.hostname;
              // navigator.clipboard仅支持安全上下文（https）和localhost
              if (protocolStr === 'https' || hostName === 'localhost') {
                setShowPaste(true)
                navigator?.clipboard?.readText().then(() => {
                  setAllowPaste(true)
                }).catch(() => {
                  setAllowPaste(false)
                });
              }
              else setShowPaste(false)
            }, 15); // 使用 15ms 延迟，确保 handleBeforeContextMenu 有足够时间完成
          } else {
            // 菜单关闭时清理缓存，避免内存泄漏
            setMenuContent(null)
            // 清除定时器
            if (timeoutRef.current) {
              clearTimeout(timeoutRef.current)
            }
          }
        }}
      >
        {wrapped}
      </Dropdown>
    )
  }

  return [wrapper]
}