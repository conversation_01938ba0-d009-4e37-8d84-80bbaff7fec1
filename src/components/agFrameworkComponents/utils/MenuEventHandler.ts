import type { Column, ICellRendererParams } from '@ag-grid-community/core'
import { MenuHandlers } from '../hooks/useContextMenuLogic'

/**
 * 菜单键枚举
 */
export enum MenuKey {
  // 查看操作
  viewCell = 'viewCell',
  viewRow = 'viewRow',
  
  // 复制操作
  copyCell = 'copyCell',
  copyRow = 'copyRow',
  copyAll = 'copyAll',
  copyCellTitle = 'copyCellTitle',
  copyWithTitle = 'copyWithTitle',
  copyAllTitle = 'copyAllTitle',
  copySelectedAllCellsTitles = 'copySelectedAllCellsTitles',

  // 特殊操作
  pasteRow = 'pasteRow',
  cloneRow = 'cloneRow',
  
  // 结果集生成
  resInsert = 'resInsert',
  resUpdate = 'resUpdate',
  resDelete = 'resDelete',
  
  // 动态菜单键
  copySelectedColumnTitles = 'copySelectedColumnTitles',
  copySelectedColumnData = 'copySelectedColumnData',
  copySelectedCellsData = 'copySelectedCellsData',
  copySelectedCellsTitles = 'copySelectedCellsTitles',
  copySelectedCellsWithTitle = 'copySelectedCellsWithTitle',
  resultSetGeneration = 'resultSetGeneration',
  copyCellTable = 'copyCellTable',
  copyAllColumnTitles = 'copyAllColumnTitles',
  copySelectedFullColumnDataWithTitle = 'copySelectedFullColumnDataWithTitle',
  
  // 全选专用菜单键
  copySelectAllWithTitle = 'copySelectAllWithTitle'
}

/**
 * 菜单事件处理器工具类
 */
export class MenuEventHandler {
  /**
   * 创建模拟的 ICellRendererParams 对象
   */
  static createMockParams(
    column?: Column,
    api?: any,
    columnApi?: any
  ): ICellRendererParams {
    return {
      column: column!,
      node: null,
      data: null,
      value: null,
      colDef: column?.getColDef()!,
      rowIndex: -1,
      api: typeof api === 'function' ? (api as any)() : api,
      columnApi: typeof columnApi === 'function' ? (columnApi as any)() : columnApi,
      context: null,
      refreshCell: () => {},
      eGridCell: null,
      eParentOfValue: null,
      formatValue: () => '',
      getValue: () => null,
      setValue: () => {},
      addRenderedRowListener: () => {},
      registerRowDragger: () => {},
      valueFormatted: null,
      $scope: null,
    } as unknown as ICellRendererParams
  }

  /**
   * 处理结果集操作（INSERT/UPDATE/DELETE）
   */
  static handleResAction(
    action: 'insert' | 'update' | 'delete',
    params: ICellRendererParams,
    handlers: MenuHandlers
  ): void {
    const { getSelectedRowsDataForResultSet } = handlers
    
    let handler: ((params: ICellRendererParams) => void) | undefined
    
    switch (action) {
      case 'insert':
        handler = handlers.handleResInsert
        break
      case 'update':
        handler = handlers.handleResUpdate
        break
      case 'delete':
        handler = handlers.handleResDelete
        break
    }
    
    if (!handler) return
    
    if (getSelectedRowsDataForResultSet) {
      const selectedRows = getSelectedRowsDataForResultSet()
      const mockParams = {
        ...params,
        api: {
          ...params.api,
          getSelectedRows: () => selectedRows,
        },
      } as ICellRendererParams
      handler(mockParams)
    } else {
      handler(params)
    }
  }

  /**
   * 统一的菜单点击处理函数
   */
  static handleMenuClick(
    menuKey: string,
    params: ICellRendererParams,
    handlers: MenuHandlers
  ): void {
    switch (menuKey as MenuKey) {
      // 查看操作
      case MenuKey.viewCell:
        handlers.handleViewCell?.(params)
        return
      case MenuKey.viewRow:
        handlers.handleViewRow?.(params)
        return
        
      // 复制操作
      case MenuKey.copyCell:
        handlers.handleCopyCell?.(params)
        return
      case MenuKey.copyRow:
        handlers.handleCopyRow?.(params)
        return
      case MenuKey.copyAll:
        handlers.handleCopyAll?.(params)
        return
      case MenuKey.copyCellTitle:
        handlers.handleCopyCellTitle?.(params)
        return
      case MenuKey.copyAllTitle:
        handlers.handleCopyAllTitle?.(params)
        return
      case MenuKey.copyWithTitle:
        handlers.handleCopyWithTitle?.(params)
        return
      case MenuKey.copySelectedAllCellsTitles:
        handlers.handleCopySelectedAllCellsTitles?.(params)
        return
        
      // 特殊操作
      case MenuKey.pasteRow:
        handlers.handlePasteRow?.(params)
        return
      case MenuKey.cloneRow:
        handlers.handleCloneRow?.(params)
        return
        
      // 结果集生成
      case MenuKey.resInsert:
        MenuEventHandler.handleResAction('insert', params, handlers)
        return
      case MenuKey.resUpdate:
        MenuEventHandler.handleResAction('update', params, handlers)
        return
      case MenuKey.resDelete:
        MenuEventHandler.handleResAction('delete', params, handlers)
        return
        
      // 动态菜单处理
      case MenuKey.copySelectedColumnTitles:
      case MenuKey.copyAllColumnTitles:
        handlers.handleCopySelectedColumnTitles?.(params)
        return
      
      // 特殊的表头复制处理
      case 'copyCellTitle' as MenuKey:
        handlers.handleCopyCellTitle?.(params)
        return
      case 'copyAllTitle' as MenuKey:
        handlers.handleCopyAllTitle?.(params)
        return
      case MenuKey.copySelectedColumnData:
        handlers.handleCopySelectedColumnData?.(params)
        return
      case MenuKey.copySelectedCellsData:
      case MenuKey.copyCellTable:
        handlers.handleCopySelectedCellsData?.(params)
        return
      case MenuKey.copySelectedCellsTitles:
        handlers.handleCopySelectedCellsTitles?.(params)
        return
      case MenuKey.copySelectedCellsWithTitle:
        handlers.handleCopySelectedCellsWithTitle?.(params)
        return
      case MenuKey.copySelectedFullColumnDataWithTitle:
        handlers.handleCopySelectedFullColumnDataWithTitle?.(params)
        return
      case MenuKey.copySelectAllWithTitle:
        handlers.handleCopySelectAllWithTitle?.(params)
        return
    }
  }

  /**
   * 创建菜单点击处理器
   */
  static createMenuClickHandler(
    params: ICellRendererParams,
    handlers: MenuHandlers
  ): (key: string) => void {
    return (key: string) => {
      MenuEventHandler.handleMenuClick(key, params, handlers)
    }
  }
}