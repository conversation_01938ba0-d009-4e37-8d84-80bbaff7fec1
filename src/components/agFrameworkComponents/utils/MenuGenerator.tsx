import React from 'react'
import { <PERSON><PERSON>, Toolt<PERSON> } from 'antd'
import { QuestionCircleFilled } from '@ant-design/icons'
import { Column } from '@ag-grid-community/core'
import { indexColId } from '../const.agFrameworkComponents'
import contextMenuStyles from '../contextMenu.module.scss'
import styles from '../contextMenu.module.scss'
import i18n from 'i18next'

/**
 * 菜单生成器的基础参数接口
 */
export interface MenuGeneratorBaseProps {
  handleMenuClick: (key: string) => void
  theme: string
  isHeaderMode?: boolean
  headerName?: string
  column?: Column
  allowCreateSql?: boolean
  createSqlDisabled?: boolean
  copyable?: boolean
  canCopyCell?: boolean
  allowClone?: boolean
}

/**
 * 上下文菜单生成器的参数接口
 */
export interface ContextMenuGeneratorProps extends MenuGeneratorBaseProps {
  selectionContext: any
  clearColumnSelections?: () => void
}

/**
 * 子菜单生成器的参数接口
 */
export interface SubMenuProps {
  theme: string
  allowCreateSql?: boolean
  createSqlDisabled?: boolean
  handleMenuClick: (key: string) => void
}

/**
 * 菜单生成器工具类
 */
export class MenuGenerator {
  /**
   * 生成列选择菜单
   */
  static generateColumnMenu(props: MenuGeneratorBaseProps): React.ReactElement {
    const { handleMenuClick, theme, allowCreateSql, createSqlDisabled } = props
    
    return (
      <Menu onClick={({ key }) => handleMenuClick(key as string)}>
        <Menu.SubMenu 
          title={i18n.t("copyColumnTitles")} 
          popupClassName={theme === 'dark' ? contextMenuStyles.dark : ''}
        >
          <Menu.Item key="copyAllColumnTitles">
            {i18n.t("copyColumnTitle")}
          </Menu.Item>
          <Menu.Item key="copyAllTitle">
            {i18n.t("copyAllTitles")}
          </Menu.Item>
        </Menu.SubMenu>
        <Menu.Item key="copySelectedColumnData" disabled={props.copyable === false}>
          {i18n.t("copyColumnData")}
        </Menu.Item>
        <Menu.Item key="copyAll" disabled={props.copyable === false} className={styles.copyAllTitle}>
          {i18n.t("copyAll")}
          <Tooltip title={i18n.t("copyCurrentPageData")} className={styles.copyAllTip}>
            <QuestionCircleFilled />
          </Tooltip>
        </Menu.Item>
        <Menu.Item key="copySelectedFullColumnDataWithTitle" disabled={props.copyable === false}>
          {i18n.t("copyWithTitle")}
        </Menu.Item>
        <Menu.Divider />
        {MenuGenerator.generateResultSetSubMenu({
          theme,
          allowCreateSql,
          createSqlDisabled,
          handleMenuClick
        })}
      </Menu>
    )
  }

  /**
   * 生成行选择菜单
   */
  static generateRowMenu(props: MenuGeneratorBaseProps): React.ReactElement {
    const { handleMenuClick, theme, allowCreateSql, createSqlDisabled, copyable, allowClone } = props
    
    return (
      <Menu onClick={({ key }) => handleMenuClick(key as string)}>
        <Menu.Item key="viewRow">{i18n.t("viewRow")}</Menu.Item>
        <Menu.Divider />
        <Menu.Item key="copyRow" disabled={copyable === false}>
          {i18n.t("copy")}
        </Menu.Item>
        <Menu.Item key="copyAll" disabled={copyable === false} className={styles.copyAllTitle}>
          {i18n.t("copyAll")}
          <Tooltip title={i18n.t("copyCurrentPageData")} className={styles.copyAllTip}>
            <QuestionCircleFilled />
          </Tooltip>
        </Menu.Item>
        <Menu.Item key="copyAllTitle">
          {i18n.t("copyTitle")}
        </Menu.Item>
        <Menu.Item key="copyWithTitle" disabled={copyable === false}>
          {i18n.t("copyWithTitle")}
        </Menu.Item>
        <Menu.Divider />
        <Menu.Item key="cloneRow" disabled={!allowClone}>
          {i18n.t("clone")}
        </Menu.Item>
        {<Menu.Divider />}
        {MenuGenerator.generateResultSetSubMenu({
          theme,
          allowCreateSql,
          createSqlDisabled,
          handleMenuClick
        })}
      </Menu>
    )
  }

  /**
   * 生成单元格菜单（单个单元格）
   */
  static generateSingleCellMenu(props: MenuGeneratorBaseProps): React.ReactElement {
    const { handleMenuClick, theme, allowCreateSql, createSqlDisabled, copyable, canCopyCell, column } = props
    const colId = column?.getColId()
    
    return (
      <Menu onClick={({ key }) => handleMenuClick(key as string)}>
        <Menu.Item key="viewRow">{i18n.t("sdo_view_single_line")}</Menu.Item>
        <Menu.Divider />
        <Menu.Item key="viewCell" disabled={colId === indexColId}>
          {i18n.t("viewCell")}
        </Menu.Item>
        <Menu.Divider />
        <Menu.Item key="copyCell" disabled={copyable === true ? false : canCopyCell === false}>
          {i18n.t("copy")}
        </Menu.Item>
        <Menu.Item key="copyAll" disabled={copyable === false} className={styles.copyAllTitle}>
          {i18n.t("copyAll")}
          <Tooltip title={i18n.t("copyCurrentPageData")} className={styles.copyAllTip}>
            <QuestionCircleFilled />
          </Tooltip>
        </Menu.Item>
        <Menu.Item key="copyCellTitle" disabled={colId === indexColId}>
          {i18n.t("copyTitle")}
        </Menu.Item>
        <Menu.Item key="copySelectedCellsWithTitle" disabled={copyable === true ? false : canCopyCell === false}>
          {i18n.t("copyWithTitle")}
        </Menu.Item>
        <Menu.Divider />
        {MenuGenerator.generateResultSetSubMenu({
          theme,
          allowCreateSql,
          createSqlDisabled,
          handleMenuClick
        })}
      </Menu>
    )
  }

  /**
   * 生成多单元格选择菜单
   */
  static generateMultiCellMenu(props: MenuGeneratorBaseProps & { isRegular: boolean }): React.ReactElement {
    const { handleMenuClick, theme, allowCreateSql, createSqlDisabled, copyable } = props
    
    return (
      <Menu onClick={({ key }) => handleMenuClick(key as string)}>
        <Menu.Item key="copySelectedCellsData" disabled={copyable === false}>
          {i18n.t("copy")}
        </Menu.Item>
        <Menu.Item key="copyAll" disabled={copyable === false} className={styles.copyAllTitle}>
          {i18n.t("copyAll")}
          <Tooltip title={i18n.t("copyCurrentPageData")} className={styles.copyAllTip}>
            <QuestionCircleFilled />
          </Tooltip>
        </Menu.Item>
        <Menu.Item key="copySelectedCellsTitles">
          {i18n.t("copyTitle")}
        </Menu.Item>
        <Menu.Item key="copySelectedCellsWithTitle" disabled={copyable === false}>
          {i18n.t("copyWithTitle")}
        </Menu.Item>
        <Menu.Divider />
        {MenuGenerator.generateResultSetSubMenu({
          theme,
          allowCreateSql,
          createSqlDisabled,
          handleMenuClick
        })}
      </Menu>
    )
  }

  /**
   * 生成默认菜单
   */
  static generateDefaultMenu(props: MenuGeneratorBaseProps): React.ReactElement {
    const { handleMenuClick, theme, isHeaderMode, headerName, column } = props
    const colId = column?.getColId()
    
    if (isHeaderMode) {
      // 表头默认菜单
      return (
        <Menu onClick={({ key }) => handleMenuClick(key as string)}>
          <Menu.SubMenu 
            title={i18n.t("copyTitle")} 
            popupClassName={theme === 'dark' ? contextMenuStyles.dark : ''}
          >
            <Menu.Item key="copyCellTitle">
              {i18n.t("copyCellTitle")}
            </Menu.Item>
            <Menu.Item key="copyAllTitle">
              {i18n.t("copyAllTitles")}
            </Menu.Item>
          </Menu.SubMenu>
        </Menu>
      )
    }
    
    // 单元格默认菜单
    return (
      <Menu onClick={({ key }) => handleMenuClick(key as string)}>
        <Menu.Item key="viewCell" disabled={colId === indexColId}>
          {i18n.t("viewCell")}
        </Menu.Item>
        <Menu.Item key="viewRow">{i18n.t("viewRow")}</Menu.Item>
      </Menu>
    )
  }

  /**
   * 生成全选状态菜单（区域选择模式，只保留复制全部功能）
   */
  static generateSelectAllMenu(props: MenuGeneratorBaseProps): React.ReactElement {
    const { handleMenuClick, copyable, allowCreateSql, createSqlDisabled } = props
    
    return (
      <Menu onClick={({ key }) => handleMenuClick(key as string)}>
        <Menu.Item key="copyAll" disabled={copyable === false} className={styles.copyAllTitle}>
          {i18n.t("copy")}
        </Menu.Item>
        <Menu.Item key="copyAll" disabled={copyable === false} className={styles.copyAllTitle}>
          {i18n.t("copyAll")}
          <Tooltip title={i18n.t("copyCurrentPageData")} className={styles.copyAllTip}>
            <QuestionCircleFilled />
          </Tooltip>
        </Menu.Item>
        <Menu.Item key="copySelectedAllCellsTitles">
          {i18n.t("copyTitle")}
        </Menu.Item>
        <Menu.Item key="copySelectAllWithTitle" disabled={copyable === false}>
          {i18n.t("copyWithTitle")}
        </Menu.Item>
        <Menu.Divider />
        {MenuGenerator.generateResultSetSubMenu({
          theme: '',
          allowCreateSql,
          createSqlDisabled,
          handleMenuClick
        })}
      </Menu>
    )
  }

  /**
   * 生成基于上下文的菜单
   */
  static generateContextMenu(props: ContextMenuGeneratorProps): React.ReactElement {
    const { 
      selectionContext, 
      handleMenuClick, 
      theme, 
      isHeaderMode, 
      clearColumnSelections,
      column,
      ...baseProps 
    } = props
    
    // 全选状态菜单 - 优先级最高
    if (selectionContext?.isSelectAllActive) {
      return MenuGenerator.generateSelectAllMenu({ ...baseProps, handleMenuClick, theme, column })
    }
    
    // 列选择菜单
    if (selectionContext?.selectionMode === 'column') {
      if (isHeaderMode) {
        // 表头模式的列选择处理
        const currentColId = column?.getColId()
        const isCurrentColumnSelected = selectionContext.selectedColumns?.includes(currentColId)
        
        if (!isCurrentColumnSelected) {
          // 右键的不是选中的列，但不自动清除状态，只显示默认菜单
          return MenuGenerator.generateDefaultMenu({ ...baseProps, handleMenuClick, theme, isHeaderMode, column })
        }
      }
      
      return MenuGenerator.generateColumnMenu({ ...baseProps, handleMenuClick, theme, column })
    }

    // 行选择菜单
    if (selectionContext?.selectionMode === 'row') {
      return MenuGenerator.generateRowMenu({ ...baseProps, handleMenuClick, theme })
    }

    // 单元格选择菜单
    if (selectionContext?.selectionMode === 'cell') {
      if (selectionContext.isSingleCell) {
        return MenuGenerator.generateSingleCellMenu({ ...baseProps, handleMenuClick, theme, column })
      } else {
        const isRegular = selectionContext.isRegularCellSelection
        return MenuGenerator.generateMultiCellMenu({ 
          ...baseProps, 
          handleMenuClick, 
          theme, 
          column,
          isRegular 
        })
      }
    }

    // 默认菜单
    return MenuGenerator.generateDefaultMenu({ ...baseProps, handleMenuClick, theme, isHeaderMode, column })
  }

  /**
   * 生成结果集子菜单
   */
  static generateResultSetSubMenu(props: SubMenuProps): React.ReactElement {
    const { theme, createSqlDisabled, handleMenuClick } = props
    
    return (
      <Menu.SubMenu 
        disabled={!createSqlDisabled} 
        title={i18n.t("resultSetGenerated")} 
        popupClassName={theme === 'dark' ? contextMenuStyles.dark : ''}
      >
        <Menu.Item key="resInsert" disabled={!createSqlDisabled}>
          INSERT
        </Menu.Item>
        <Menu.Item key="resUpdate" disabled={!createSqlDisabled}>
          UPDATE
        </Menu.Item>
        <Menu.Item key="resDelete" disabled={!createSqlDisabled}>
          DELETE
        </Menu.Item>
      </Menu.SubMenu>
    )
  }

  /**
   * 生成复制子菜单
   */
  static generateCopySubMenu(props: SubMenuProps & { keys: string[], labels: string[] }): React.ReactElement {
    const { theme, handleMenuClick, keys, labels } = props
    
    return (
      <Menu.SubMenu 
        title={i18n.t("copyTitle")} 
        popupClassName={theme === 'dark' ? contextMenuStyles.dark : ''}
      >
        {keys.map((key, index) => (
          <Menu.Item key={key}>
            {labels[index]}
          </Menu.Item>
        ))}
      </Menu.SubMenu>
    )
  }
}
