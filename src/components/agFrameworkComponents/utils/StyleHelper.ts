import styles from '../CustomHeader.module.scss'

/**
 * 样式处理工具类
 */
export class StyleHelper {
  /**
   * 获取 Overlay 样式类名
   * @param theme 主题
   * @param contextMenuStyles 上下文菜单样式对象
   * @returns 组合后的样式类名
   */
  static getOverlayClassName(
    theme: string,
    contextMenuStyles: any
  ): string {
    const baseClass = contextMenuStyles.contextMenu
    const themeClass = theme === 'dark' ? contextMenuStyles.dark : ''
    
    return [baseClass, themeClass].filter(Boolean).join(' ')
  }

  /**
   * 获取弹窗样式类名
   * @param theme 主题
   * @param contextMenuStyles 上下文菜单样式对象
   * @returns 弹窗样式类名
   */
  static getPopupClassName(theme: string, contextMenuStyles: any): string {
    return theme === 'dark' ? contextMenuStyles.dark : ''
  }

  /**
   * 获取基础上下文菜单样式类名
   * @param theme 主题
   * @param contextMenuStyles 上下文菜单样式对象
   * @returns 基础上下文菜单样式类名
   */
  static getContextMenuClassName(theme: string, contextMenuStyles: any): string {
    return [contextMenuStyles.contextMenu, theme === 'dark' ? contextMenuStyles.dark : ''].join(' ')
  }

  /**
   * 获取表头上下文菜单样式类名
   * @param theme 主题
   * @param contextMenuStyles 上下文菜单样式对象
   * @returns 表头上下文菜单样式类名
   */
  static getHeaderContextMenuClassName(theme: string, contextMenuStyles: any): string {
    return [styles.headerContextMenu, theme === 'dark' ? contextMenuStyles.dark : ''].join(' ')
  }

  /**
   * 判断是否为暗色主题
   * @param theme 主题字符串
   * @returns 是否为暗色主题
   */
  static isDarkTheme(theme: string): boolean {
    return theme === 'dark'
  }

  /**
   * 获取主题相关的样式对象
   * @param theme 主题
   * @param lightStyles 浅色主题样式
   * @param darkStyles 深色主题样式
   * @returns 对应主题的样式对象
   */
  static getThemeStyles<T>(theme: string, lightStyles: T, darkStyles: T): T {
    return StyleHelper.isDarkTheme(theme) ? darkStyles : lightStyles
  }

  /**
   * 组合多个样式类名
   * @param classNames 样式类名数组
   * @returns 组合后的样式类名字符串
   */
  static combineClassNames(...classNames: (string | undefined | null | false)[]): string {
    return classNames.filter(Boolean).join(' ')
  }

  /**
   * 条件性添加样式类名
   * @param baseClassName 基础样式类名
   * @param conditionalClassName 条件样式类名
   * @param condition 条件
   * @returns 组合后的样式类名
   */
  static conditionalClassName(
    baseClassName: string,
    conditionalClassName: string,
    condition: boolean
  ): string {
    return condition 
      ? StyleHelper.combineClassNames(baseClassName, conditionalClassName)
      : baseClassName
  }
}