.options {
	cursor: pointer;
	color: #2189f3;
	:global {
		.ant-dropdown-menu-item {
			color: #2189f3;
		}
	}
}
.ml10 {
    margin-left: 10px;
}
.padding0 {
  padding: 0;
}
.treeWrap {
	:global {
		.ant-tree-treenode {
			width: 100%;
			padding: 0 0 4px 0;
			background-color: #fff;
			align-items: center;
		}
		.ant-tree-node-content-wrapper {
			flex: 1;
		}
	}
}
.flexItem {
	display: flex;
	align-items: center;
}
.chooseManageModal {
	.content{
		display: flex;
		margin-bottom: 20px;
		padding-right: 12px;
		height: 370px;
		.left{
			width: 350px;
		}
		.right{
			padding-left: 16px;
			width: 222px;
			border-left: 1px solid #e5e5e5;
			.userWrap {
				padding-right: 20px;
				max-height: 338px;
				overflow-y: auto;
				.selectItem {
					margin-bottom: 10px;
					display: flex;
					align-items: center;
					justify-content: space-between;
				}
			}
		}
		.title{
			margin-bottom: 10px;
			font-weight: 600;
		}
	}
	.radioGroupWrap {
		width: 100%;
		max-height: 120px;
		overflow-y: auto;
		:global {
			.ant-radio-wrapper {
				max-width: 136px;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
			}
		}
	}
	.searchUserListWrap {
		padding-right: 20px;
		max-height: 300px;
		overflow-y: auto;
	}
}