import React from "react";
import { Modal, Input } from "antd";
import styles from './index.module.scss';
import classNames from "classnames";
import i18n from 'i18next';

const BtnWithConfirmModal = ({
  title,
  btnText,
  onClick,
  hideReason = false
}: {
  title: React.ReactNode;
  btnText: string;
  hideReason?: boolean;
  onClick: (reason?: string) => void;
}) => {
  const handleModalConfirm = () => {

    const textOnchange = (val: string) => {
      update({
        onOk: (closeFn) => {
          onClick(val);
          closeFn();
        },
      });
    };

    const { update } = Modal.confirm({
      title: title || `${i18n.t("confirm")}?`,
      centered: true,
      content: !hideReason && (
        <Input.TextArea
          rows={5}
          onChange={(e) => textOnchange(e.target.value)}
        />
      ),
      onOk: () => {
       onClick()
      },
    });
  };

  return <span className={classNames(styles.normalBtn)} onClick={() => handleModalConfirm()}>{btnText || i18n.t("delete")}</span>;
};

export default BtnWithConfirmModal;