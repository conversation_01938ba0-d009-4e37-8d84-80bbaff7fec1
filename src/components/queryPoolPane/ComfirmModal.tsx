import { Modal, Table } from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import styles from './index.module.scss'
import i18n from 'i18next';

interface IProps {
  highRiskResultInfoList: any,
  timeout: number,
  visible: boolean
  getShouldResume: (n: number) => void
  setVisible: (b: boolean) => void
}

const ComfirmModal = (props: IProps) => {

  const { highRiskResultInfoList, timeout, visible, setVisible, getShouldResume } = props
  const timerRef = useRef<any>(null);  // 使用 useRef 创建 ref
  const [seconds, setSeconds] = useState<number>(timeout);
  const [open, setOpen] = useState<boolean>(visible)

  const countdown = (seconds: number) => {
    timerRef.current = setInterval(() => {

      seconds--;

      setSeconds(seconds)

      if (seconds < 0) {
        clearInterval(timerRef.current); // 使用 ref 中的定时器引用
        setVisible(false)
        setOpen(false)
        getShouldResume(0)
      }
    }, 1000);
  }

  const handleOk = () => {
    clearInterval(timerRef.current)
    setVisible(false)
    setOpen(false)
    getShouldResume(1)
  };

  const handleCancel = () => {
    clearInterval(timerRef.current)
    setVisible(false)
    setOpen(false)
    getShouldResume(0)
  };

  useEffect(() => {
    clearInterval(timerRef.current)

    // 重新开始倒计时
    countdown(timeout);
    
    return () => {
      clearInterval(timerRef.current) // 组件卸载时清除定时器
    };

  }, [timeout, visible])

  
  const columns = [
    {
      title: i18n.t("executeSQL"),
      dataIndex: 'sql',
      key: 'sql',
    },
    {
      title: i18n.t("prompt"),
      dataIndex: 'message',
      key: 'message',
    },
  ];
  
  

  return (
    <>
      <Modal
        title={i18n.t("operationConfirmation")}
        width={600}
        visible={open}
        onOk={handleOk}
        onCancel={handleCancel}
        okText={i18n.t("execute")}
        cancelText={`${i18n.t("cancel")}(${seconds})`}
        okButtonProps={{ style: { backgroundColor: '#eff0f4', color: 'black', border: 0 } }} // 确认按钮样式
        cancelButtonProps={{ style: { backgroundColor: '#3262fe', color: 'white' } }} // 取消按钮样式
        className={styles.confirmModalWrap}
        maskStyle={{ backgroundColor: 'rgba(0, 0, 0, 0.1)' }}
      >
        <Table
          dataSource={highRiskResultInfoList}
          columns={columns}
          pagination={false}
          scroll={{x: 'max-content',y: 500}}
        />
      </Modal>
    </>
  );
};

export default ComfirmModal