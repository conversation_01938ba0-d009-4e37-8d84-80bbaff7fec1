import React, { useCallback, useContext, useEffect, useRef, useState } from 'react'
import * as _ from 'lodash';
import {cancelExecute, getSegmentResults, QueryParams, QueryResult, startExecute} from 'src/api'
import { useSelector, useDispatch, useRequest } from 'src/hook'
import {
  setTabExecutionStatus,
  setTabExecutionStatusPercentage
} from 'src/pageTabs/queryPage/queryTabs/queryTabsSlice'
import { setNeedRefreshResultData } from 'src/pageTabs/queryPage/resultTabs/resultTabsSlice'
import {
  setActiveResultTabKey,
  setExecuteKeyList,
  updateResultTabs,
  saveResultCount,
  addSegmentMsg,
  SegmentPositionMsg,
  addSegmentPositionMsg,
  setLastErrorPosition,
  setIsRun
} from 'src/pageTabs/queryPage/resultTabs/resultTabsSlice'
import { setExamineReviewResult } from 'src/pageTabs/queryPage/queryTabs/queryTabsSlice'
import { QueryPoolContext } from './QueryPoolContext'
import { addLog } from 'src/store/extraSlice/logsSlice'
import { QueryLogItem } from 'src/types'

const ComfirmModal = React.lazy(() => import('./ComfirmModal'));

interface PaneProps {
  queryTabKey: string
  shouldResume: number
  setVisible: (b: boolean) => void
  getHighRiskResultInfoList: (a: any) => any
  setShouldResume: (n: number) => void
  [p: string]: any
}

export const QueryPoolPaneRenderer: React.FC<PaneProps> = ({
  queryTabKey,
  executeInfo,
  shouldResume,
  setShouldResume,
  setVisible,
  getHighRiskResultInfoList,
}) => {
  const dispatch = useDispatch()
  const poolMap = useContext(QueryPoolContext)
  const results = useRef<QueryResult[]>([])
  const needRefreshResultData = useSelector((state) => state.resultTabs.needRefreshResultData)
  const lastErrorPosition = useSelector((state) => state.resultTabs.lastErrorPosition)
  const deferExecuteResultMap = useSelector((state) => state.resultTabs.deferExecuteResultMap)
  const derferExecuteResult = deferExecuteResultMap?.[queryTabKey] ?? null;

  const [curErrorPositionInfo, setCurErrorPositionInfo] = useState<{ [key: string]: any }>()
  const [highRiskResultInfoList, setHighRiskResultInfoList] = useState<any>()
  const [params, setParams] = useState<any>() // fetch参数
  const [subShouldResume, setsubShouldResume] = useState<number>(shouldResume)

  // 分批次渲染logs
  const queueLogs = (logs: QueryLogItem[]) => {
    dispatch(addLog(logs))
  }

  useEffect(() => {
    let newErrorPosition = {...lastErrorPosition, ...curErrorPositionInfo}
    dispatch(setLastErrorPosition(newErrorPosition))
  }, [curErrorPositionInfo])

  useEffect(() => {
    setsubShouldResume(shouldResume)
  }, [shouldResume])

  //fetchSegmentResult 执行结果处理
  const moreOperationAfterExecute = useCallback(async (data) => {
    const { messageData = [], messageId } = _.cloneDeep(data)
    // 获取sql审核结果
    const { reviewResult } = messageData;
    if(reviewResult && Object.keys(reviewResult)?.length) {
      dispatch(setExamineReviewResult({queryTabKey ,reviewResult}))
    }
    const resultCount = messageData.executionInfos?.length
    dispatch(saveResultCount(resultCount))

    let logs: QueryLogItem[] = []
    messageData?.executionInfos?.forEach((item: any) => {
      item.executeLogInfo.message.messageId = messageId
      if (item?.response) {
        results.current.push(item.response)
      }
      if (item?.response?.executeError) {
        item.executeLogInfo.message.executeError = item?.response?.executeError
        if (item.response.executeError?.position) {
          const position = item.response.executeError?.position
          const errorPosition = {
            'endColumn': position.stopCol,
            'endLineNumber': position.stopLine,
            'startColumn': position.startCol,
            'startLineNumber': position.startLine
          }
          // 更新，当前执行的sql语句json中的 错误sql语句的位置
          let tmpLastErrorPosition: {[key: string]: any} = {[queryTabKey]:errorPosition}
          setCurErrorPositionInfo(tmpLastErrorPosition)
        }
      }
      if(item?.executeLogInfo?.message?.show !== false) {
        const logMessage = {
          ...(item?.executeLogInfo?.message ?? {}),
          executeSqlFlag: true  // 标记是执行sql产生的日志
        }
        logs.push(logMessage)
      }
    })
    //const logsChunks = chunk(logs, 50)
    queueLogs(logs)
    /** 全部执行完后再处理结果集 */
    // if (data.executeStatus === 'SUCCESS' && results.current.length) {
    // 将错误语句之前的正确的SQL语句执行的结果集展示出来
    const len = results.current.length
    if (len) {
      //derferExecute接口响应比之前execute接口速度快 过快修改isRun状态导致没有触发monaco中标记逻辑
      setTimeout(() => {
        dispatch(setIsRun(false))
    }, 1000)
    
      // 当有错误sql时，剔除掉所有有executeError的结果集，只保留成功的结果在Tab中显示
      if (data.executeStatus === 'FAILED') {
        results.current = results.current.filter(result => !result.executeError?.message)
      }
      const final = results.current.slice(-20)
      const resultTabs = final.map((info, index) => ({
        key: `execute/${queryTabKey}-${index}`,
        info,
      }))
      const list = resultTabs.map(({ key }) => key)
      // 先更新 resultTabs, 再设置 keyList
      dispatch(updateResultTabs(resultTabs))
      dispatch(setExecuteKeyList({ key: queryTabKey, list }))

      const currentQueryResult = results.current.filter(r => r.tabKey === queryTabKey && r?.executeError?.message !== undefined && r?.executeError?.message !== "").length > 0;
      // 通过判断res 的tabkey 和当前key 一致并且执行后message 不为空 则将本页内的结果集active 设在log 
      // 执行出错则优先展示log
      if (currentQueryResult) {
        dispatch(
          setActiveResultTabKey({
            queryTabKey: queryTabKey,
            resultTabKey: `log/${queryTabKey}`,
          }),
        )
      } else {
        dispatch(
          setActiveResultTabKey({
            queryTabKey: queryTabKey,
            resultTabKey: list[0] || `log/${queryTabKey}`,
          }),
        )
      }
    } else {
      // 没有结果集时默认展示log
      dispatch(
        setActiveResultTabKey({
          queryTabKey: queryTabKey,
          resultTabKey: `log/${queryTabKey}`,
        }),
      )
    }
  },[])

  const updateExecutePenddingState = useCallback((data) => {
    if (!(data && data.executeStatus === 'RUNNING')) {
      // 恢复面板为可执行状态
      dispatch(setTabExecutionStatus({ key: queryTabKey, pending: false }))
    }else if(data && data.executeStatus === 'RUNNING'){
      dispatch(setTabExecutionStatus({ key: queryTabKey, pending: true }))
    }
    // 更新执行进度
    dispatch(setTabExecutionStatusPercentage({ key: queryTabKey, executePercentage: data?.executePercentage, executeStatusMessage : data?.executeStatusMessage}))
  }, [])

  async function recursiveQuery(params: QueryParams, delay = 100) {
    try {
      const data = await getSegmentResults(params);
     //默认进来更新执行状态
      updateExecutePenddingState(data);
      if (data && data.messageData) {
        //抽离
        moreOperationAfterExecute(data)
      }
      if (data && data.executeStatus === 'RUNNING') {
        if (data.highRiskResultInfoList != null && data.highRiskResultInfoList.length > 0){
          await Promise.all([
            setVisible(true),
            setParams(params),
            setHighRiskResultInfoList(data.highRiskResultInfoList),
            getHighRiskResultInfoList(data.highRiskResultInfoList)
          ]).then(() => {

          });
        }else {
          setTimeout(() => {
            // 继续下一次查询，增加延迟从100ms开始，每次增加100ms，但不超过3000ms
            const max = 3000;
            let nextDelay = delay;
            if (delay < max){
              nextDelay = delay + 100;
            }
            recursiveQuery(params, nextDelay);
          }, delay);
        }
      }
    } catch (error) {
      console.error('查询状态时出错:', error);
    }
  }

  // 根据 操作确认modal 返回的结果，判断是否结束执行
  const fetchData = async () => {
    try {
      if (params && highRiskResultInfoList) {
        if (subShouldResume === 1){
          await startExecute({messageId: queryTabKey, tabKey: queryTabKey}).then(() => {
            //直接继续 fetch 状态
            recursiveQuery(params);

            setsubShouldResume(2)
            setShouldResume(2)
            setParams('')
          })
        }else if (subShouldResume === 0){
          await cancelExecute({messageId: queryTabKey, tabKey: queryTabKey}).then(() => {
            //直接继续 fetch 状态
            recursiveQuery(params);

            setsubShouldResume(2)
            setShouldResume(2)
            setParams('')
          })
        }
        
      }
    } catch (error) {
      // 处理错误
    }
  };
  useEffect(() => {
    fetchData();
  }, [subShouldResume]);

  // todo: 支持多个messageId查询以及对应的结果处理
  const handleRenderResults = useCallback(
    async (queryContext: QueryParams) => {
      // const key = queryContext.tabKey
      // if (!key) return
      try {
        results.current = []
        // 手动执行一次后开启轮询
        await recursiveQuery(queryContext)
      } catch { }
    },
    [],
  )

  useEffect(() => {
    if (!queryTabKey || _.isEmpty(derferExecuteResult)) return

    results.current = [];
    //不为PENDDING即为执行成功
    if (!_.isEmpty(derferExecuteResult) && derferExecuteResult?.executeStatus !== 'RUNNING' && derferExecuteResult?.messageData) {
      // derferExecute接口直接返回执行结果时 不需要轮询调用fetchStatement接口
      dispatch(setIsRun(true))
      updateExecutePenddingState(derferExecuteResult);
      moreOperationAfterExecute(derferExecuteResult);
      return ;
    }
    handleRenderResults({channelId: "DMS_EXECUTE", groupName: "DMS_EXECUTE", messageId: queryTabKey})
  }, [dispatch, handleRenderResults, queryTabKey, derferExecuteResult])

  // 根据事物未提交且已回滚，判断是否需要刷新执行(日志)数据
  useEffect(() => {
    if (!queryTabKey || !needRefreshResultData) return
    handleRenderResults({channelId: "DMS_EXECUTE", groupName: "DMS_EXECUTE", messageId: queryTabKey})
    dispatch(setNeedRefreshResultData(false))
  // eslint-disable-next-line react-hooks/exhaustive-deps
  },[needRefreshResultData])

  return null
}

export const QueryPoolPane = () => {
  const tabInfoMap = useSelector((state) => state.queryTabs.tabInfoMap)
  const monacoTabs = Object.values(tabInfoMap).filter(({ paneType }) =>
    ['monaco', 'plSql', 'tSql', 'obCreateView', 'createFnAndProcedure'].includes(paneType),
  )
  const [visible, setVisible] = useState<boolean>(false) // 操作确认弹窗
  const [shouldResume, setShouldResume] = useState<number>(2)
  const [highRiskResultInfoList, setHighRiskResultInfoList] = useState<any>()

  const getHighRiskResultInfoList = (list: any) => {
    setHighRiskResultInfoList(list)
  }

  const getShouldResume = (newTimes: number) => {
    setShouldResume(newTimes)
  }

  return (
    <>
      {monacoTabs.map(({ key }) => {
        return (
          <QueryPoolPaneRenderer
            key={key}
            queryTabKey={key}
            setVisible={setVisible}
            shouldResume={shouldResume}
            setShouldResume={setShouldResume}
            getHighRiskResultInfoList={getHighRiskResultInfoList}
          />
        )
      })}
      {visible && <ComfirmModal
        highRiskResultInfoList={highRiskResultInfoList}
        timeout={60}  // 60s 超时结束
        getShouldResume={getShouldResume}
        visible={visible}
        setVisible={setVisible}
      />}
    </>
  )
}

