import { ColumnsType } from 'antd/lib/table'
import { AppSql } from 'src/api'
import { renderItemField } from './render'
import i18n from 'i18next';

export const AppSqlColumns: ColumnsType<AppSql> = [
  {
    title: i18n.t("sqlMode"),
    dataIndex: 'sqlPattern',
    render: renderItemField('longString'),
  },
  {
    title: i18n.t("recentExecution"),
    dataIndex: 'lastExecutedAt',
    ellipsis: { showTitle: false },
    width: '120px',
    render: renderItemField('timeString'),
  },
  {
    title: i18n.t("executionHost"),
    dataIndex: 'hostname',
    ellipsis: true,
    width: '120px',
  },
  {
    title: i18n.t("averageTime"),
    dataIndex: 'costAvg',
    ellipsis: true,
    width: '120px',
  },
  {
    title: i18n.t('totalTime'),
    dataIndex: 'costTotal',
    ellipsis: true,
    width: '120px',
  },
  {
    title: i18n.t("executionTimes"),
    dataIndex: 'executeCount',
    ellipsis: true,
    width: '96px',
  },
]
