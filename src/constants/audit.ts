export const DEFAULT_EXECUTE_LOG_COLUMNS = [
  'executor',
  'deptName',
  'connectionName',
  'objectName'
]

// 执行历史
export const DEFAULT_SQL_LOG_COLUMNS = [
  "executor",
  "deptName",
  "connectionName",
  "objectName",
  "executeBegin",
  "executeEnd",
  "errorMsg",
  "affectRows",
  "executeCost",
  "executeSql",
  "inTrans",
  "tabKey",
  "sqlType",
  "dbType",
  "dbEdition",
  "clientIp",
  "resultFlag",
  "serverIp",
  "port",
  "executorName",
  'source'
]

export const EXECUTION_COLUMN_FIELDS: { [k: string]: string } = {
  executor: "personal:account",
  deptName: "personal:department",
  connectionName: "personal:dataSource",
  objectName: "personal:operationObject",
  executorName: "personal:userName",
  dbType: "personal:databaseType",
  requestIp: "personal:applicationServerIP",
  serverIp: "personal:databaseIP",
  port: "personal:databasePort",
  executeSql:  "personal:operationStatement",
  dbEdition:  "personal:dataVersion",
  executeBegin:  "personal:startTime",
  executeEnd:  "personal:endTime",
  clientIp: "personal:clientIP",
  sqlType:  "personal:operationType",
  resultFlag: "personal:operationResult",
  errorMsg: "personal:errorMessage",
  affectRows: "personal:affectedRows",
  executeCost: "personal:durationMs",
  tabKey: "personal:executionWindowID",
  inTrans: "personal:executionMode",
  source: "personal:source",
}

export const DEFAULT_OPERATE_LOG_COLUMNS = [
  'userName',
  'detail',
  'operateEntryPoint',
  'resultFlag',
]

export const OPERATE_COLUMN_FIELDS: { [k: string]: string } = {
  userName: "auays:tb_title.user",
  detail: "auays:tb_title.operation_detail",
  operateEntryPoint: "auays:tb_title.operation",
  resultFlag: "auays:tb_title.execution_result",
  clientIp: "auays:tb_title.client_IP",
  businessType: "auays:tb_title.function_name",
  beginTime: "auays:tb_title.operation_time",
}

export const SQL_OPERATE_TYPES: { [key: string]: string } = {
  SELECT: 'magenta',
  DELETE: 'red',
  INSERT: 'orange',
  MERGE: 'green',
  UPDATE: 'cyan',
  ALTER: 'blue',
  CREATE: 'geekblue',
  DROP: 'purple',
}

export const USER_AUDIT_SQL_DETAIL_DEFAULT_COLUMN = [
  'connectionName',
  'objectName'
]

export const USER_AUDIT_SQL_DETAIL_COLUMN = {
  connectionName: "auays:tb_title.data_source",
  objectName: "auays:tb_title.operation_object",
  serverIp: "auays:tb_title.database_ip",
  port: "auays:tb_title.database_port",
  executeSql: "auays:tb_title.ope_statement",
  dbEdition: "auays:tb_title.data_version",
  executeBegin: "auays:tb_title.start_time",
  executeEnd: "auays:tb_title.end_time",
  clientIp: "auays:tb_title.client_ip",
  sqlType: "auays:tb_title.operation_type",
  resultFlag: "auays:tb_title.operation_result",
  errorMsg: "auays:tb_title.error_message",
  affectRows: "auays:tb_title.affected_rows",
  executeCost: "auays:tb_title.duration_ms",
  source: "auays:tb_title.source",
}

// 用户审计-用户详情-权限tab 默认必选字段
export const USER_AUDIT_AUTH_DETAIL_DEFAULT_COLUMN = [
  'nodePath',
  'nodeType',
  'permissionTemplate',
  'permissionType',
  'authUser',
  'permissionId',
]
// 用户审计-用户详情-权限tab 全部字段
export const USER_AUDIT_AUTH_DETAIL_COLUMN = {
  permissionType: "auays:tb_title.permission_type",
  permissionTemplate: "auays:tb_title.permission_level",
  nodePath: "auays:tb_title.resource",
  nodeType: "auays:tb_title.resource_type",
  role: "auays:tb_title.role",
  expirationTime: "auays:tb_title.effective_time",
  authUser: "auays:tb_title.authorizer",
  sourceType: "auays:tb_title.authorization_source",
  authIp: "auays:tb_title.authorization_ip",
  authTime: "auays:tb_title.authorization_time",
  permissionId: "auays:tb_title.permission_details",
}


// 用户审计-权限看板 默认必选字段
export const AUTH_BOARD_DETAIL_DEFAULT_COLUMN = [
  'user',
  'permissionType',
  'permissionTemplate',
  'nodePath',
  'nodeType',
  'authUser',
  'permissionId',
]
// 用户审计-权限看板 全部字段
export const AUTH_BOARD_DETAIL_COLUMN = {
  user: "auays:tb_title.username",
  permissionType: "auays:tb_title.permission_type",
  permissionTemplate: "auays:tb_title.permission_level",
  nodePath: "auays:tb_title.resource",
  nodeType: "auays:tb_title.resource_type",
  role: "auays:tb_title.linked_role",
  expirationTime: "auays:tb_title.effective_time",
  authUser: "auays:tb_title.authorizer",
  sourceType: "auays:tb_title.authorization_source",
  authIp: "auays:tb_title.authorization_ip",
  authTime: "auays:tb_title.authorization_time",
  permissionId: "auays:tb_title.permission_details",
}

// 审计分析-审计概览-应用语句明细-列名字典
export const APP_STATEMENT_COLUMN_FIELDS: { [key: string]: string } = {
  applicationIp: "auays:tb_title.application_ip",
  serverIp: "auays:tb_title.database_ip",
  operateObject: "auays:tb_title.operation_object",
  dbUser: "auays:tb_title.database_account",
  beginTime: "auays:tb_title.start_time",
  endTime: "auays:tb_title.end_time",
  executeSql: "auays:tb_title.operation_statement",
  operateType: "auays:tb_title.statement_type",
  resultFlag: "auays:tb_title.operation_result",
  errorMessage: "auays:tb_title.error_message",
  affectedRows: "auays:tb_title.affected_rows",
  cost: "auays:tb_title.time_consuming_ms"
}

// 审计分析-审计概览-应用语句明细-默认列
export const APP_STATEMENT_DETAIL_COLUMNS = [
  'applicationIp',
  'serverIp',
  // 'operateObject', // 操作对象暂时没有数据
  'executeSql',
  'resultFlag',
  'errorMessage',
]


// 审计分析-审计概览-应用语句明细-来源字段过滤
export const SOURCE_FIELD_FILTERS = [
  {
    value: 'DATA_OPERATE',
    text: "auays:tb_filter.source.data_operation",
  }, {
    value: 'DATA_CHANGE',
    text: "auays:tb_filter.source.data_change",
  }, {
    value: 'BATCH_EXECUTE',
    text: "auays:tb_filter.source.batch_execution",
  }, {
    value: 'TERMINAL_EXECUTE',
    text: "auays:tb_filter.source.terminal_exe",
  }
]