import i18n from 'i18next';
interface optionsIprops {
  label: string;
  value: string | number
}
// 高危提示
export const levelMenu  = (): optionsIprops[] => [
  {
    value: 4,
    label: i18n.t('dataProtection.tab.security.highType4'),
  },
  {
    value: 1,
    label: i18n.t('dataProtection.tab.security.highType1'),
  },
  {
    value: 2,
    label: i18n.t('dataProtection.tab.security.highType2'),
  },
  {
    value: 3,
    label: i18n.t('dataProtection.tab.security.highType3'),
  },
]

// 高危等级枚举
export const highRiskLevelMenu: optionsIprops[] = [
  {
    value: 1,
    label: i18n.t('dataProtection.tab.security.highLevel1'),
  },
  {
    value: 2,
    label: i18n.t('dataProtection.tab.security.highLevel2'),
  },
  {
    value: 3,
    label: i18n.t('dataProtection.tab.security.highLevel3'),
  },
]