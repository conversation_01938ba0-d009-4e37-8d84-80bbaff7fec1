export const DATA_REQUEST_APPLY_TYPES: { [key: number]: string } = {
  1: "data_chg:tab.data_correction",
  2: "data_chg:tab.pub_chg",
}

export const SQL_TYPES: { [key: number]: string } = {
  1: "data_chg:rdo_label.text",
  2: "data_chg:rdo_label.upload_attachment",
}

const interrupted: string = "data_chg:status.interrupted"
const draft: string = "data_chg:status.draft"
const pending: string = "data_chg:status.pending_review"
const approved: string = "data_chg:status.reviewed"
const completed: string = "data_chg:status.completed"
const underReview: string = "data_chg:status.approving"
const awaitingExecution: string = "data_chg:status.pending_execution"
const inProgress: string = "data_chg:status.executing"
const executed: string = "data_chg:status.executed"
const executionSuccessful: string = "data_chg:status.exe_success"
const executionFailed: string = "data_chg:status.exe_failed"
const revoked: string = "data_chg:status.withdrawn"
const rejected: string = "data_chg:status.rejected"
const taskAborted: string = "data_chg:status.task_terminated"
const rollbackSuccessful: string = "data_chg:status.rollback_success"
const rollbackFailed: string = "data_chg:status.rollback_failed"

export const APPLY_COLUMN_FILTER = {
  [draft]:"Draft",
  [underReview]:"UnderReview",
  [awaitingExecution]:'AwaitingExecution',
  [inProgress]:'InProgress',
  [executionSuccessful]:'ExecutionSuccessful',
  [executionFailed]:'ExecutionFailed',
  [rollbackSuccessful]:'RollbackSuccessful',
  [rollbackFailed]:'RollbackFailed',
  [revoked]:'Revoked',
  [rejected]:'Rejected',
  [interrupted]:'Interrupted'
}

export const APPROVE_COLUMN_FILTER_MAP: { [key: string]: string }  = {
  'UnderReview': underReview,
  'AwaitingExecution': awaitingExecution,
  'InProgress': inProgress,
  'ExecutionSuccessful': executionSuccessful,
  'ExecutionFailed': executionFailed,
  'RollbackSuccessful': rollbackSuccessful,
  'RollbackFailed': rollbackFailed,
  'Interrupted': interrupted
}

export type EXECUTE_TYPES = 
  "RollbackFailed" // 回退失败
  | "RollbackSuccessful" // 回退成功
  | "Interrupted" // 被中断
  | "Draft" // 草稿
  | "Pending" // 待审核
  | "Approved" // 已审核
  | "Completed" // 已完成
  | "UnderReview" // 审批中
  | "AwaitingExecution" // 待执行
  | "InProgress" // 执行中
  | "Executed" // 已执行
  | "ExecutionSuccessful" // 执行成功
  | "ExecutionFailed" // 执行失败
  | "Revoked" // 已撤回
  | "Rejected" // 已驳回
  | "TaskAborted" // 任务中止
  | "Fallback" // 回退中
  | "Discontinued" // 被中止
  | "EndSuccess" // 成功结束
  | "EndFail" // 失败结束
  | "InterruptedExecution" // 中断执行


export const EXECUTE_TYPE_LABEL_MAP: { [key: string]: string } = {
  'Interrupted': interrupted,
  'Draft': draft,
  'Pending': pending,
  'Approved': approved,
  'Completed': completed,
  'UnderReview': underReview,
  'AwaitingExecution': awaitingExecution,
  'InProgress': inProgress,
  'Executed': executed,
  'ExecutionSuccessful': executionSuccessful,
  'ExecutionFailed': executionFailed,
  'Revoked': revoked,
  'Rejected': rejected,
  'TaskAborted': taskAborted,
  'RollbackSuccessful': rollbackSuccessful,
  'RollbackFailed': rollbackFailed,
}

export type CURRENT_ROUTE_TYPE =
  | 'MINE_APPLY'
  | 'MINE_APPLY_DETAIL'
  | 'MINE_APPROVE'
  | 'FLOW_WORK_ORDER_MANAGEMENT'
  | 'MINE_APPROVE_DEALED'

export const ORDER_MAPPING = {
  ascend: 'asc',
  descend: 'desc',
}