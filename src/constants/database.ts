import { SdtNodeType } from 'src/api'

export const ORACLE_COLUMN_TYPES = [
  'VARCHAR2',
  'NVARCHAR2',
  'NUMBER',
  'FLOAT',
  'LONG',
  'DATE',
  'BINARY_FLOAT',
  'BINARY_DOUBLE',
  'TIMESTAMP',
  'TIMESTAMP WITH TIME ZONE',
  'TIMESTAMP WITH LOCAL TIME ZONE',
  'INTERVAL YEAR TO MONTH',
  'INTERVAL DAY TO SECOND',
  'RAW',
  'LONG RAW',
  'ROWID',
  'UROWID',
  'CHAR',
  'NCHAR',
  'CLOB',
  'NCLOB',
  'BLOB',
  'BFILE'
]

export const MYSQL_COLUMN_TYPES = [
  'bigint',
  'blob',
  'char',
  'date',
  'datetime',
  'decimal',
  'double',
  'float',
  'int',
  'longtext',
  'smallint',
  'text',
  'time',
  'timestamp',
  'tinyblob',
  'tinyint',
  'tinytext',
  'varchar',
  'year',
]

export const SQLSERVER_COLUMN_TYPES = [
  'bigint',
  'bit',
  'decimal',
  'int',
  'money',
  'numeric',
  'smallint',
  'smallmoney',
  'tinyint',
  'float',
  'real',
  'date',
  'datetime2',
  'datetime',
  'datetimeoffset',
  'smalldatetime',
  'time',
  'char',
  'varchar',
  'text',
  'nchar',
  'nvarchar',
  'ntext',
  'binary',
  'varbinary',
  'image',
  'cursor',
  'rowversion',
  'hierarchyid',
  'uniqueidentifier',
  'sql_variant',
  'xml',
  'Spatial Geometry Types',
  'Spatial Geography Types',
  'table',
]

export const IMPALA_COLUMN_TYPES = [
  'boolean',
  'tinyint',
  'smallint',
  'int',
  'bigint',
  'decimal',
  'float',
  'double',
  'timestamp',
  'string',
  'varchar',
  'char',
]

export const VERTICA_COLUMN_TYPES = [
  'binary ',
  'varbinary',
  'long varbinary',
  'bytea',
  'raw',
  'boolean',
  'char',
  'varchar',
  'long varchar',
  'date',
  'time',
  'datetime',
  'timestamp',
  'float',
  'float8',
  'int',
  'bigint',
  'integer',
  'smallint',
  'tinyint',
  'decimal',
  'numeric',
  'number',
  'uuid',
]

export const HIVE_COLUMN_TYPES = [
  'tinyint',
  'smallint',
  'int',
  'bigint',
  'boolean',
  'binary',
  'float',
  'double',
  'decimal',
  'string',
  'varchar',
  'char',
  'timestamp',
  'date',
  'array<int>',
  'array<string>',
  'map<int,int>',
  'map<string,string>',
  'map<int,string>',
  'map<string,int>',
]

export const POSTGRESQL_COLUMN_TYPES = [
  'bigint',
  'bigserial',
  'bit',
  'bit varying',
  'boolean',
  'box',
  'bytea',
  'character',
  'character varying',
  'cidr',
  'circle',
  'date',
  'double precision',
  'inet',
  'integer',
  'interval',
  'json',
  'jsonb',
  'line',
  'lseg',
  'macaddr',
  'macaddr8',
  'money',
  'numeric',
  'path',
  'pg_lsn',
  'point',
  'polygon',
  'real',
  'smallint',
  'smallserial',
  'serial',
  'text',
  'time',
  'timestamp',
  'tsquery',
  'tsvector',
  'txid_snapshot',
  'uuid',
  'xml',
  'timestamp with time zone',
  'time with time zone',
]

// 数据库同层级类型
export const databaseLike: SdtNodeType[] = [
  'database',
  'redisDataBase',
  'oracleUser',
  'schema',
]

// 同步sql-formatter工具所支持的格式化语言
export const formatOptionalLangs: any[] = [
  "bigquery",
  "db2",
  "db2i",
  "hive",
  "mariadb",
  "mysql",
  "n1ql",
  "plsql",
  "postgresql",
  "redshift",
  "singlestoredb",
  "snowflake",
  "spark",
  "sql",
  "sqlite",
  "tidb",
  "transactsql",
  "trino",
  "tsql",
  "presto"
]

//含有schema层级的数据源
export const INCLUDE_SCHEMA_CONNECTIONS = [
  'SQLServer',
  'DamengDB',
  'DB2',
  'MogDB',
  'PostgreSQL',
  'KingBasePG',
  'KingBaseOracle',
  'PolarDB',
  'OracleCDB',
  'StarRocks',
  'Vertica',
  'Trino',
  'Presto',
  'GaussDB_DWS',
  'HighGo',
  'TDSQLPG',
  'Greenplum',
  'GaussDB',
  'OpenGauss',
  'GaussDB_DWS',
  'OceanBaseMySQL',
  'Vertica',
  'GBase8c',
  'GoldenDB',
];

// 数据库中暂不支持导出的字段类型
export const NOT_ALLOWED_EXPORT_FIELDTYPE = [
  'any',
  'anyarray',
  'anyelement',
  'anyenum',
  'anynonarray',
  'anyrange',
  'array',
  'bfile',
  'bigint',
  'binary',
  'binary18',
  'binaryvar',
  'bit',
  'bit varying',
  'blob',
  'box',
  'bytea',
  'cidr',
  'circle',
  'clob',
  'complex',
  'cstring',
  'cursor',
  'date',
  'daterange',
  'datetime',
  'dbclob',
  'distinct',
  'event_trigger',
  'fdw_handler',
  'geography',
  'geometry',
  'hierarchyid',
  'hyperloglog',
  'idssecuritylabel',
  'image',
  'index_am_handler',
  'inet',
  'int4range',
  'int8range',
  'integer',
  'internal',
  'interval',
  'ipaddress',
  'json',
  'jsonb',
  'language_handler',
  'line',
  'list(e)',
  'lld_lob_data',
  'lld_locator',
  'long',
  'long raw',
  'longblob',
  'longvarbinary',
  'lseg',
  'lvarchar',
  'macaddr',
  'macaddr8',
  'map',
  'mediumblob',
  'multiset',
  'nclob',
  'numrange',
  'oid',
  'opaque',
  'p4hyperloglog',
  'path',
  'pg_ddl_command',
  'pg_lsn',
  'point',
  'polygon',
  'qdigest',
  'raw',
  'record',
  'regclass',
  'regconfig',
  'regdictionary',
  'regnamespace',
  'regoper',
  'regoperator',
  'regproc',
  'regprocedure',
  'regrole',
  'regtype',
  'row',
  'rowid',
  'set',
  'setdigest',
  'spatial',
  'sql_variant',
  'st_geometry',
  'st_linestring',
  'st_multilinestring',
  'st_multipoint',
  'st_multipolygon',
  'st_point',
  'st_polygon',
  'struct',
  'table',
  'tdigest',
  'text',
  'time',
  'timeseries',
  'timestamp',
  'tinyblob',
  'trigger',
  'tsm_handler',
  'tsquery',
  'tsrange',
  'tstzrange',
  'tsvector',
  'unknown',
  'urowid',
  'uuid',
  'varbinary',
  'void',
  'xml',
]

// obmysql函数返回类型
export const OBMYSQL_RETURN_TYPE = [
  'int',
  'numeric',
  'decimal',
  'bit',
  'tinyint',
  'smallint',
  'mediumint',
  'bigint',
  'bool',
  'boolean',
  'float',
  'double',
  'varchar',
  'char',
  'tinytext',
  'mediumtext',
  'text',
  'longtext',
  'tinyblob',
  'blob',
  'mediumblob',
  'longblob',
  'binary',
  'varbinary',
  'timestamp',
  'date',
  'time',
  'datetime',
  'year',
  'set',
  'enum',
]

// oboracle函数返回类型
export const OBORACLE_RETURN_TYPE = [
  'INTEGER',
  'NUMBER',
  'CHAR',
  'VARCHAR',
  'VARCHAR2',
  'BLOB',
  'CLOB',
  'DATE',
  'TIMESTAMP',
  'TIMESTAMP WITH TIME ZONE',
  'TIMESTAMP WITH LOCAL TIME ZONE',
  'RAW',
  'TERVAL YEAR TO MONTH',
  'INTERVAL DAY TO SECON',
  'NCHAR',
  'NVARCHAR2',
  'FLOAT',
  'BINARY_FLOAT',
  'BINARY_DOUBLE',
  'ROWID',
  'UROWID',
]