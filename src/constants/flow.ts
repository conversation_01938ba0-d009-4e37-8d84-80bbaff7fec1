import i18n from "i18next";
// 流程
import { t } from "i18next";

//菜单
export const MENU_FLOW = "fp:bc_title.process";
export const DATA_CHANGE_FLOW = "fp:bc_title.data_change";

// 待审核
const pendingReview: string = t("data_chg:rcsg_opt.pending_review")
// 已审核
const reviewed: string = t("data_chg:rcsg_opt.reviewed")

// 记录的status中已经不含空格
export const STATUS_MAPPING = {
  [pendingReview]: {
    'Draft': [
      { label: t("data_chg:ope_menu_label.delete_draft") },
      { label: t("data_chg:ope_menu_label.edit"), type: "edit" }
    ],
    'UnderReview': [
      { label: t("data_chg:ope_menu_label.withdraw_application"), bgColor: "#3262ff", color: "#ffffff" },
      { label: t("data_chg:ope_menu_label.remind") }
    ],
  },
  [reviewed]: {
    'Rejected': [
      { label: t("data_chg:ope_menu_label.reapply"), bgColor: "#3262ff", color: "#ffffff" }
    ],
    'AwaitingExecution': [
      { label: t("data_chg:ope_menu_label.execute"), bgColor: "#3262ff", color: "#ffffff" },
      { label: t("data_chg:ope_menu_label.interrupt_task") }
    ],
    'InProgress': [
      { label: t("data_chg:ope_menu_label.terminate_execution") }
    ],
    'ExecutionSuccessful': [
      { label: t("data_chg:ope_menu_label.end_task"), bgColor: "#3262ff", color: "#ffffff" },
      { label: t("data_chg:ope_menu_label.rollback_execution") }
    ], // 返回已完成
    'ExecutionFailed': [
      { label: t("data_chg:ope_menu_label.end_task"), bgColor: "#3262ff", color: "#ffffff" }
    ], 
    'RollbackSuccessful': [
      { label: t("data_chg:ope_menu_label.end_task"), bgColor: "#3262ff", color: "#ffffff" }
    ], 
    'RollbackFailed': [
      { label: t("data_chg:ope_menu_label.end_task"), bgColor: "#3262ff", color: "#ffffff" }
    ], 
    'Interrupted': [
      { label: t("data_chg:ope_menu_label.end_task") },
      { label: t("data_chg:ope_menu_label.reapply"), bgColor: "#3262ff", color: "#ffffff" }
    ]
  },
};

// 后端 工单详情接口，frontStatus与status的对应关系与 工单list返回的不一致, 后端没有时间修改, 额外定义按钮
export const STATUS_MAPPING_DETAIL = {
  'Draft': [{ label: "data_chg:ope_menu_label.delete_draft"}, { label: "data_chg:ope_menu_label.edit", type: "edit" }],
  'UnderReview': [{ label: "data_chg:ope_menu_label.withdraw_application", bgColor: "#3262ff", color: "#ffffff" }, { label: "data_chg:ope_menu_label.remind" }],
  'Rejected': [{ label: "data_chg:ope_menu_label.reapply", bgColor: "#3262ff", color: "#ffffff" }],
  'AwaitingExecution': [{ label: "data_chg:ope_menu_label.execute", bgColor: "#3262ff", color: "#ffffff" }, { label: "data_chg:ope_menu_label.interrupt_task" }],
  'InProgress': [{ label: "data_chg:ope_menu_label.terminate_execution" }],
  'ExecutionSuccessful': [{ label: "data_chg:ope_menu_label.end_task", bgColor: "#3262ff", color: "#ffffff" }, { label: "data_chg:ope_menu_label.rollback_execution" }], //返回已完成
  'ExecutionFailed': [{ label: "data_chg:ope_menu_label.end_task", bgColor: "#3262ff", color: "#ffffff" }], //返回已完成
  'RollbackSuccessful': [{ label: "data_chg:ope_menu_label.end_task", bgColor: "#3262ff", color: "#ffffff" }], //返回已完成
  'RollbackFailed': [{ label: "data_chg:ope_menu_label.end_task", bgColor: "#3262ff", color: "#ffffff" }], //返回已完成
  'Interrupted': [{ label: "data_chg:ope_menu_label.end_task" }, { label: "data_chg:ope_menu_label.reapply", bgColor: "#3262ff", color: "#ffffff" }],
  'Revoked': [{ label:"data_chg:ope_menu_label.reapply", bgColor: "#3262ff", color: "#ffffff" }]
};

//同步复核审批人
export const SYNC_CHECK_OPTION_MAPPING: Record<any, string> = {
  CONNECTION_MANAGER: i18n.t('db.connection.conn.approver.connManager'),
  APPROVE_GROUP: i18n.t('db.connection.conn.approve')
}

export type SYNC_CHECK_OPTION =   keyof typeof SYNC_CHECK_OPTION_MAPPING;