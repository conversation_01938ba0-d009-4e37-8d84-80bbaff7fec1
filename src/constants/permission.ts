import { DataSourceType } from "src/types"
import i18n from "i18next"

export const PERM_TYPES = 'PERM_TYPES'
export const PERM_TYPES_WITHOUT_PERMSET = 'PERM_TYPES_WITHOUT_PERMSET'
export const BATCH_DELETE = 'BATCH_DELETE'

const permTypes = ['dataSource', 'permissionSet', 'system'] as const

export type PermType = typeof permTypes[number]

export const permTypesLabelMap: { [key in PermType]: string } = {
  dataSource: i18n.t("dataSourcePermission"),
  permissionSet: i18n.t("permissionSet"),
  system: i18n.t("systemPermission"),
}

export const NORMAL_DATABASE_PERMISSIONS: { [k: string]: string[] } = {
  table: ['SELECT_TABLE', 'UPDATE_TABLE', 'INSERT_TABLE', 'DELETE_TABLE'],
  tableGroup: ['CREATE_TABLE', 'ALTER_TABLE', 'DROP_TABLE', 'TRUNCATE', 'SELECT_TABLE', 'UPDATE_TABLE', 'DELETE_TABLE', 'INSERT_TABLE'],
  view: ['SELECT_VIEW', 'UPDATE_VIEW', 'DELETE_VIEW', 'INSERT_VIEW'],
  viewGroup: ['SELECT_VIEW', 'CREATE_VIEW', 'ALTER_VIEW', 'DROP_VIEW', 'UPDATE_VIEW', 'DELETE_VIEW', 'INSERT_VIEW'],
  foreignTable: ['SELECT_FOREIGN_TABLE', 'UPDATE_FOREIGN_TABLE', 'DELETE_FOREIGN_TABLE', 'INSERT_FOREIGN_TABLE'],
  foreignTableGroup: ['SELECT_FOREIGN_TABLE', 'UPDATE_FOREIGN_TABLE', 'DELETE_FOREIGN_TABLE', 'INSERT_FOREIGN_TABLE', 'CREATE_FOREIGN_TABLE', 'ALTER_FOREIGN_TABLE', 'DROP_FOREIGN_TABLE'],
  materializedView: ['SELECT_MATERIALIZED_VIEW'],
  materializedViewGroup: ['CREATE_MATERIALIZED_VIEW', 'ALTER_MATERIALIZED_VIEW', 'DROP_MATERIALIZED_VIEW', 'SELECT_MATERIALIZED_VIEW'],
  function: ['EXECUTE_FUNCTION'],
  functionGroup: ['EXECUTE_FUNCTION', 'CREATE_FUNCTION', 'ALTER_FUNCTION', 'DROP_FUNCTION'],
  procedure: ['EXECUTE_PROCEDURE'],
  storedProcedureGroup: ['EXECUTE_PROCEDURE', 'CREATE_PROCEDURE', 'ALTER_PROCEDURE', 'DROP_PROCEDURE'],
  synonym: ['SYNONYM_OPERATE'],
  synonymsGroup: ['SYNONYM_OPERATE', 'CREATE_SYNONYM', 'ALTER_SYNONYM', 'DROP_SYNONYM'],
  sequence: ['CQ_OPEN_SEQUENCE'],
  sequenceGroup: ['CQ_OPEN_SEQUENCE', 'CREATE_SEQUENCE', 'ALTER_SEQUENCE', 'DROP_SEQUENCE'],
  trigger: ['CQ_OPEN_TRIGGER'],
  triggerGroup: ['ALTER_TRIGGER', 'CREATE_TRIGGER', 'DROP_TRIGGER', 'CQ_OPEN_TRIGGER'],
  dbLink: ['DBLINK_OPERATE'],
  dbLinkGroup: ['CREATE_DATABASE_LINK', 'ALTER_DATABASE_LINK', 'DROP_DATABASE_LINK', 'DBLINK_OPERATE'],
  package: ['EXECUTE_PACKAGE'],
  packageGroup: ['EXECUTE_PACKAGE', 'CREATE_PACKAGE', 'ALTER_PACKAGE', 'DROP_PACKAGE'],
  packageBody: ['EXECUTE_PACKAGE'],
  packageBodyGroup: ['EXECUTE_PACKAGE', 'CREATE_PACKAGE', 'ALTER_PACKAGE', 'DROP_PACKAGE']
}

export const SQL_SERVER_PERMISSION = {
  ...NORMAL_DATABASE_PERMISSIONS,
  synonymsGroup: ['SYNONYM_OPERATE', 'CREATE_SYNONYM', 'DROP_SYNONYM'],
  dbLinkGroup: ['DBLINK_OPERATE'],
}
export const MONGODB_PERMISSIONS = {
  collection: ['INSERT_COLLECTION', 'DELETE_COLLECTION', 'UPDATE_COLLECTION', 'FIND_COLLECTION', 'BULK_WRITE_COLLECTION'],
  collectionGroup: ['CREATE_COLLECTION', 'DROP_COLLECTION'],
  view: ['FIND_VIEW'],
  viewGroup: ['CREATE_VIEW', 'FIND_VIEW', 'DROP_VIEW', 'RENAME_VIEW']
}

export const REDIS_PERMISSIONS = {
  redisKey: ['OPEN_KEY'],
  keyGroup: ['OPEN_KEY'],
  file: ['OPEN_KEY'],
  fileGroup: ['OPEN_KEY']
}
export const HIVE_PERMISSION = {
  ...NORMAL_DATABASE_PERMISSIONS,
  view: ['SELECT_VIEW'],
  viewGroup: ['SELECT_VIEW', 'CREATE_VIEW', 'ALTER_VIEW', 'DROP_VIEW']
}
export const VERTICA_PERMISSION = {
  ...NORMAL_DATABASE_PERMISSIONS,
  procedure: [],
  storedProcedureGroup: ['CREATE_PROCEDURE', 'DROP_PROCEDURE'],
}
export const PHOENIX_PERMISSION = {
  table: ['SELECT_TABLE', 'UPSERT_TABLE', 'DELETE_TABLE'],
  tableGroup: ['CREATE_TABLE', 'ALTER_TABLE', 'DROP_TABLE', 'SELECT_TABLE', 'UPSERT_TABLE', 'DELETE_TABLE'],
  view: ['SELECT_VIEW'],
  viewGroup: ['SELECT_VIEW', 'CREATE_VIEW', 'ALTER_VIEW', 'DROP_VIEW'],
  function: ['EXECUTE_FUNCTION'],
  functionGroup: ['CREATE_FUNCTION', 'DROP_FUNCTION'],
}
export const TRINO_PERMISSION = {
  ...NORMAL_DATABASE_PERMISSIONS,
  table: ['SELECT_TABLE', 'UPDATE_TABLE', 'INSERT_TABLE', 'DELETE_TABLE'],
  tableGroup: ['CREATE_TABLE', 'ALTER_TABLE', 'DROP_TABLE', 'TRUNCATE_TABLE', 'SELECT_TABLE', 'UPDATE_TABLE', 'DELETE_TABLE', 'INSERT_TABLE'],
  view: ['SELECT_VIEW'],
  viewGroup: ['SELECT_VIEW', 'CREATE_VIEW', 'ALTER_VIEW', 'DROP_VIEW'],
}

export const STARROCKS_PERMISSION = {
  ...NORMAL_DATABASE_PERMISSIONS,
  foreignTableGroup: ['SELECT_FOREIGN_TABLE', 'UPDATE_FOREIGN_TABLE', 'DELETE_FOREIGN_TABLE', 'INSERT_FOREIGN_TABLE', 'CREATE_FOREIGN_TABLE', 'DROP_FOREIGN_TABLE'],
  functionGroup: ['EXECUTE_FUNCTION', 'CREATE_FUNCTION', 'DROP_FUNCTION'],
  view: ['SELECT_VIEW'],
  viewGroup: ['SELECT_VIEW', 'CREATE_VIEW', 'ALTER_VIEW', 'DROP_VIEW'],
  syncMaterializedView: ['SELECT_SYNC_MATERIALIZED_VIEW'],
  syncMaterializedViewGroup: ['SELECT_SYNC_MATERIALIZED_VIEW', 'CREATE_MATERIALIZED_VIEW', 'DROP_MATERIALIZED_VIEW'],
  asyncMaterializedView: ['SELECT_ASYNC_MATERIALIZED_VIEW'],
  asyncMaterializedViewGroup: ['SELECT_ASYNC_MATERIALIZED_VIEW', 'ALTER_MATERIALIZED_VIEW', 'CREATE_MATERIALIZED_VIEW', 'DROP_MATERIALIZED_VIEW'],
  routineLoadGroup: ['CREATE_ROUTINE_LOAD', 'ALTER_ROUTINE_LOAD']
}

export const HANA_PERMISSION = {
  ...NORMAL_DATABASE_PERMISSIONS,
  synonymsGroup: ['SYNONYM_OPERATE', 'CREATE_SYNONYM', 'DROP_SYNONYM'],
  triggerGroup: ['CREATE_TRIGGER', 'DROP_TRIGGER', 'CQ_OPEN_TRIGGER'],
}
export const INCEPTOR_PERMISSION = {
  ...NORMAL_DATABASE_PERMISSIONS,
  view: ['SELECT_VIEW'],
  viewGroup: ['SELECT_VIEW', 'CREATE_VIEW', 'ALTER_VIEW', 'DROP_VIEW'],
  packageGroup: ['EXECUTE_PACKAGE', 'CREATE_PACKAGE', 'DROP_PACKAGE'],
  packageBodyGroup: ['EXECUTE_PACKAGE', 'CREATE_PACKAGE', 'DROP_PACKAGE'],
  dbLinkGroup: ['CREATE_DATABASE_LINK', 'DROP_DATABASE_LINK', 'DBLINK_OPERATE'],
}

export const ORACLE_OR_ORACLECDB_PERMISSION = {
  ...NORMAL_DATABASE_PERMISSIONS,
  typeGroup: ['ALTER_TYPE', 'CREATE_TYPE', 'DROP_TYPE', 'CQ_OPEN_TYPE'],
  type: ['CQ_OPEN_TYPE'],
}

export const DAMENGDB_PERMISSION = {
  ...NORMAL_DATABASE_PERMISSIONS,
  synonymsGroup: ['SYNONYM_OPERATE', 'CREATE_SYNONYM', 'DROP_SYNONYM'],
}

export const MYSQL_PERMISSION = {
  ...NORMAL_DATABASE_PERMISSIONS,
  triggerGroup: ['CREATE_TRIGGER', 'DROP_TRIGGER', 'SHOW_CREATE_TRIGGER', 'SHOW_TRIGGERS'],
}

export const getSpecialConnectionTypePermissions: any = (connectionType: DataSourceType) => {

  switch (connectionType) {
    case 'Redis':
      return REDIS_PERMISSIONS;
    case 'MongoDB':
      return MONGODB_PERMISSIONS;
    case 'SQLServer':
      return SQL_SERVER_PERMISSION;
    case 'Hive':
      return HIVE_PERMISSION;
    case 'Vertica':
      return VERTICA_PERMISSION;
    case 'Phoenix':
      return PHOENIX_PERMISSION;
    case 'Trino':
      return TRINO_PERMISSION;
    case 'Presto':
      return TRINO_PERMISSION;
    case 'StarRocks':
      return STARROCKS_PERMISSION;
    case 'HANA':
      return HANA_PERMISSION;
    case 'Inceptor':
      return INCEPTOR_PERMISSION;
    case 'Oracle':
    case 'OracleCDB':
    case 'OceanBase':
      return ORACLE_OR_ORACLECDB_PERMISSION;
    case 'DamengDB':
      return DAMENGDB_PERMISSION;
    case 'MySQL':
      return MYSQL_PERMISSION;
    default:
      return NORMAL_DATABASE_PERMISSIONS;
  }
}
//特殊nodeType 需要另外添加
export const actionIconNodeTypes = Object.keys({
  ...NORMAL_DATABASE_PERMISSIONS,
  ...MONGODB_PERMISSIONS,
  ...REDIS_PERMISSIONS,
  ...STARROCKS_PERMISSION,
  ...ORACLE_OR_ORACLECDB_PERMISSION
}).map(type => type);

// 权限类型
export const PERMISSION_TYPE = [
  {
    key: 'datasource',
    value: "auays:tb_filter.perms_type.data_source_ope"
  },
  {
    key: 'desensitizedResource',
    value: "auays:tb_filter.perms_type.sensitive_resource_access"
  },
  {
    key: 'highRiskOperation',
    value: "auays:tb_filter.perms_type.high_risk_ope"
  },
  {
    key: 'permissionTool',
    value: "auays:tb_filter.perms_type.tool"
  }
]
//权限类型
export const PERMISSION_TYPE_MAPPING: { [k in string]: string } = {
  dataSource: "subjectAuth:tb_render.data_source_operation_permission", //确认是大写
  desensitizedResource: "subjectAuth:tb_render.sensitive_resource_access_permission",
  highRiskOperation: "subjectAuth:tb_render.high_risk_operation_permission",
  permissionTool: "subjectAuth:tb_render.tool_permission",
}

export type PermissionType = keyof typeof PERMISSION_TYPE_MAPPING;

// 资源类型
export const NODE_TYPE = ['connection','database', 'schema', 'table', 'view', 'function', 'procedure', 'column', 'other']

//权限来源
export const SOURCE_OF_AUTHORITY: any = {
  AUTO_USER: "subjectAuth:tb_render.automatic_authorization",
  FLOW: "subjectAuth:tb_render.process_privilege_escalation",
  USER: "subjectAuth:tb_render.manual_authorization"
}

export type SourceType = keyof typeof SOURCE_OF_AUTHORITY;


export const USERAUDIT_INPUT_EUM: Record<any, any> = {
  1: { placeholder: "auays:srh_ph.perms_level_resource", width: 300 },
  2: { placeholder: "auays:srh_ph.ds_ob_sql", width: 360 },
  3: { placeholder: "auays:srh_ph.operation", width: 190 },
}

export const TYPE_OF_MODIFICAION_ALLOWED: PermissionType[] = ['dataSource', 'permissionTool'];
