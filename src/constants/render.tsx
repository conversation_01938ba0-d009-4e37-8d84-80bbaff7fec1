import { Tooltip, Typography } from 'antd'
import React from 'react'
import styles from './index.module.scss'
import i18n from 'i18next'

// todo: 移动到 util
export const getGenderText = (gender: any) => {
  if (gender === 'MALE') return i18n.t("male")
  if (gender === 'FEMALE') return i18n.t("female")
  return gender
}

type RenderFieldKey =
  | 'list'
  | 'listString'
  | 'timeString'
  | 'userGender'
  | 'longString'

export const renderItemField = (key: RenderFieldKey | any) => (value: any) => {
  switch (key) {
    case 'userGender':
      return getGenderText(value)
    case 'list':
      return ((value as []) || []).join(', ')
    case 'listString':
      return ((value as string) || '')
        .split(',')
        .map((item) => item.trim())
        .join(', ')
    case 'timeString':
      value = value || ''
      return (
        <Tooltip title={value} placement="top">
          {value}
        </Tooltip>
      )
    case 'longString':
      if (!value) return ''
      // todo: return render object, including ellipsis
      value = String(value)
      if (value.length <= 1) return value
      return (
        <Typography.Paragraph
          copyable
          ellipsis
          style={{ marginBottom: 0, width: '470px' }}
          className={styles.longStringRender}
        >
         {value}
        </Typography.Paragraph>
      )
    case 'successStatus':
      return <div>{value ? i18n.t("success") : i18n.t("failure")}</div>
    case 'isPrincipal':
      return value ? i18n.t("yes") : i18n.t("no")
    case 'userStatus':
      if (value === 'NORMAL') return i18n.t("normal")
      if (value === 'LOCKED') return i18n.t("locked")
      break
    case 'userCategory':
      if (value === 'USER') return i18n.t("commonUser")
      if (value === 'OWNER') return i18n.t("connectionAdmin")
      if (value === 'GRANTER') return i18n.t("authorizationAdmin")
      break
    default:
      return String(value)
  }
}

// 任务周期-corn表达式提示
export const getCronExpressionTooltipTitle = () => {
  return (
    <>
      {i18n.t("cronExpression")}
      <div>（1）{i18n.t("cronExample1")}</div>
      <div>（2）{i18n.t("cronExample2")}</div>
      <div>（3）{i18n.t("cronExample3")}</div>
      <div>（4）{i18n.t("cronExample4")}</div>
    </>
  )
}
// 搜索图标
export const SEARCH_ICON = <svg transform="1715240034079" className="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" width="14" height="14"><path d="M926.208 865.792l-156.672-156.672c52.736-65.536 83.968-148.992 83.968-239.616 0-211.968-172.032-384-384-384s-384 172.032-384 384 172.032 384 384 384c90.624 0 174.08-31.232 239.616-83.968l156.672 156.672c16.896 16.896 43.52 16.896 60.416 0 16.384-16.896 16.384-43.52 0-60.416z m-241.664-194.56c-1.536 1.024-3.072 2.56-4.096 3.584-1.536 1.536-2.56 2.56-3.584 4.096-53.76 51.712-126.976 83.456-207.36 83.456-164.864 0-298.496-133.632-298.496-298.496 0-164.864 133.632-298.496 298.496-298.496 164.864 0 298.496 133.632 298.496 298.496 0 80.896-31.744 153.6-83.456 207.36z" fill="#d9d9d9"></path></svg>