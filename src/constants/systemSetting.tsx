import type { ColumnsType } from 'antd/es/table';
import React from 'react';
import i18n from 'i18next';
import { Iconfont } from 'src/components';
import exterDeptIcon from 'src/assets/img/out_dept.png'
export interface BusinessWarnConfigColumnsDataType {
  key: string;
  type: string;
  warnCondition: number | string;
  alarmMethod: string;
  targetPerson: string;
  userId: string[];
  status?: boolean;
  operation?: any;
}
export const BusinessWarnConfigColumns: ColumnsType<BusinessWarnConfigColumnsDataType> = [
  {
    title: i18n.t("businessAlarmType"),
    dataIndex: 'type',
    key: 'type',
  },
  {
    title: i18n.t("alarmCondition"),
    dataIndex: 'warnCondition',
    key: 'warnCondition',
  },
  {
    title: i18n.t("alarmMethod"),
    dataIndex: 'alarmMethod',
    key: 'alarmMethod',
  },
  {
    title: i18n.t("messageReceiverType"),
    dataIndex: 'targetPerson',
    key: 'targetPerson',
  }
]

// 告警类型 枚举
export const typeEnum = {
  CPU_USAGE: i18n.t('systemManagement.system.targetPerson.CPU_USAGE'),
  MEMORY_USAGE: i18n.t('systemManagement.system.targetPerson.MEMORY_USAGE'),
  SYSTEM_ERROR: i18n.t('systemManagement.system.targetPerson.SYSTEM_ERROR'),
  UPLOAD_FILE_SIZE: i18n.t('systemManagement.system.targetPerson.UPLOAD_FILE_SIZE'),
  // DOWNLOAD_FILE_SIZE: "文件读取大小",
  IMPORT_TASK_COUNT: i18n.t('systemManagement.system.targetPerson.IMPORT_TASK_COUNT'),
  EXPORT_TASK_COUNT: i18n.t('systemManagement.system.targetPerson.EXPORT_TASK_COUNT'),
  LICENSE_REMAINING: i18n.t('systemManagement.system.targetPerson.LICENSE_REMAINING'),
  OVER_PERMISSION: i18n.t('systemManagement.system.targetPerson.OVER_PERMISSION'),
  HIGH_RISK: i18n.t('systemManagement.system.targetPerson.HIGH_RISK'),
  SLOW_SQL: i18n.t('systemManagement.system.targetPerson.SLOW_SQL'),
  BATCH_EXECUTE: i18n.t('systemManagement.system.targetPerson.BATCH_EXECUTE'),
  SQL_CHECK: i18n.t('systemManagement.system.targetPerson.SQL_CHECK'),
  PROBLEM_CONNECTION: i18n.t('systemManagement.system.targetPerson.PROBLEM_CONNECTION'),
  FILE_EXPORT_SIZE: i18n.t('systemManagement.system.targetPerson.FILE_EXPORT_SIZE')
}

// 告警方式 枚举
interface Data {
  [key: string]: string;
  PHONE: string;
  SYSTEM: string;
  EMAIL: string;
}
// 消息接收人类型 枚举
interface TargetPersonEnumData {
  [key: string]: string;
  PERSONAL: string;
  DEPT: string;
  CONN: string;
}
export const TargetPersonEnum : TargetPersonEnumData = {
  PERSONAL: i18n.t('systemManagement.system.targetPerson.PERSONAL'),
  DEPT:  i18n.t('systemManagement.system.targetPerson.DEPT'),
  CONN:  i18n.t('systemManagement.system.targetPerson.CONN')
}

// 告警类别
export const alarmTypeEnum = {
  0: i18n.t("systemAlarm"),
  1: i18n.t("businessAlarm"),
}

// 业务告警配置-业务告警类型
export const BusinessAlarmTypeOption = [
  {label: i18n.t("highRiskOperation"), value:"HIGH_RISK"},
  {label: i18n.t("unauthorizedOperation"), value: "OVER_PERMISSION"},
  {label: i18n.t("slowSQL"), value:"SLOW_SQL"},
  {label: i18n.t("batchExecution"), value: "BATCH_EXECUTE"},
  {label: i18n.t("sqlAudit"), value: "SQL_CHECK"}
]

// 系统告警配置 - 高危等级
export const highRiskClassOptions = [
  {
    label: i18n.t("high"),
    value: i18n.t("high"),
  },
  {
    label: i18n.t("medium"),
    value: i18n.t("medium"),
  },
  {
    label: i18n.t("low"),
    value: i18n.t("low"),
  }
]

// 系统告警配置 - 审核等级
export const auditClassOptions = [
  {
    label: "error",
    value: "error"
  },
  {
    label: "warn",
    value: "warn"
  },
  {
    label: "notice",
    value: "notice"
  }
]



export const SMSChannelEnum = [
  {
    label: i18n.t("aliCloud"),
    value: 'alibaba'
  },
  {
    label: i18n.t("tencentCloud"),
    value: 'tencent'
  },
  {
    label: i18n.t("huaweiCloud"),
    value: 'huawei'
  },
  {
    label: i18n.t("dreamNetCloud"),
    value: 'monyun'
  },
  {
    label: i18n.t("custom"),
    value: 'CUSTOM'
  }
]

// 服务协议
export const ServerAgreement = ['UDP', 'TCP'];

// 日志级别
export const LogLevel = ['EMERGENCY', 'ALERT', 'CRITICAL', 'ERROR', 'WARN', 'NOTICE', 'INFO', 'DEBUG'];

export const FormLabelCol = { span: 5, offset: 0 }
export const FormWrapperCol = { span: 14 }

// 获取组织架构部门图标，区分是否为外部部门
export const getOrgDeptIcon = (externalDepartment: boolean) => {
  return !externalDepartment ?
    <Iconfont type="icon-org-department"/> :
    <img src={exterDeptIcon} alt="" style={{ marginRight: 8 }}/>
}

// 获取授权部门图标，区分是否为外部部门
export const getDeptIcon = (externalDepartment: boolean) => {
  return !externalDepartment ? <Iconfont type='icon-dept' /> :
    <svg transform="1716517512355" className="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="16613" width="14" height="14"><path d="M384 554.688v128h256v-128h298.688v298.624A42.688 42.688 0 0 1 896 896H128a42.688 42.688 0 0 1-42.688-42.688V554.688H384z m85.312-85.376h85.376v128H469.312v-128z m-170.624-256v-128c0-23.552 19.072-42.624 42.624-42.624h341.376c23.552 0 42.624 19.072 42.624 42.624v128H896c23.552 0 42.688 19.136 42.688 42.688v213.312H640V384H384v85.312H85.312V256c0-23.552 19.136-42.688 42.688-42.688h170.688zM384 128v85.312h256V128H384z" fill="#FAAD14" ></path></svg>
}