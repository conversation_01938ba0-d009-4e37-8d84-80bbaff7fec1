import { Typography, Progress, Tooltip } from 'antd';
import React from 'react';
import dayjs from 'dayjs';
import { ExclamationCircleFilled } from '@ant-design/icons';
import { renderNodeWithIcon } from 'src/components/HomogeneousDataReplicationModal/utils'
import i18n from 'src/i18n';

const { Text } = Typography;

// 排序
const SortFilters = [
  {
    text: i18n.t("ascending"),
    value: 'asc',
  },
  {
    text: i18n.t("descending"),
    value: 'desc',
  },
]

// 导出
const columnsExport = [
  {
    title: i18n.t("personal:taskNumber"),
    dataIndex: 'taskNo',
    key: 'taskNo',
    fixed: true,
    render: (text: string) => {
      return <Text>{text || '-'}</Text>
    }
  },
  {
    title: i18n.t("personal:fileName"),
    dataIndex: 'fileName',
    key: 'fileName',
    ellipsis: true,
    render: (text: string) => {
      return <Text>{text || '-'}</Text>
    }
  },
  {
    title: i18n.t("personal:exportFormat"),
    dataIndex: 'exportFormat',
    key: 'exportFormat',
  },
  {
    title: i18n.t("personal:creator"),
    dataIndex: 'userShow',
    key: 'userShow',
    render: (text: string) => {
      return <Text>{text || '-'}</Text>
    }
  },
  {
    title: i18n.t("personal:creationTime"),
    dataIndex: 'createAt',
    key: 'createAt',
    render: (text: string) => {
      return <Text>{text || '-'}</Text>
    }
  },
  {
    title: i18n.t("personal:status"),
    dataIndex: 'taskStatus',
    key: 'taskStatus',
  }
];

// 文本导入
const columnsImport = [
  {
    title: i18n.t("personal:taskNumber"),
    dataIndex: 'taskNo',
    key: 'taskNo',
    fixed: true,
    render: (text: string) => {
      return <Text>{text || '-'}</Text>
    }
  },
  {
    title: i18n.t("personal:creator"),
    dataIndex: 'user',
    key: 'user',
    render: (text: string) => {
      return <Text>{text || '-'}</Text>
    }
  },
  {
    title: i18n.t("personal:creationTime"),
    dataIndex: 'startTime',
    key: 'startTime',
    render: (text: string) => {
      return <Text>{text || '-'}</Text>
    }
  },
  {
    title: i18n.t("personal:status"),
    dataIndex: 'taskStatus',
    key: 'taskStatus',
  }
];

// 批量执行
const columnsBatchExecute = [
  {
    title: i18n.t("personal:taskNumber"),
    dataIndex: 'taskNo',
    key: 'taskNo',
    fixed: true,
    render: (text: string) => {
      return <Text>{text || '-'}</Text>
    }
  },
  {
    title: i18n.t("personal:description"),
    dataIndex: 'description',
    key: 'description',
    render: (text: string) => {
      return <Text>{text || '-'}</Text>
    }
  },
  {
    title: i18n.t("personal:creator"),
    dataIndex: 'userShow',
    key: 'userShow',
    render: (text: string) => {
      return <Text>{text || '-'}</Text>
    }
  },
  {
    title: i18n.t("personal:creationTime"),
    dataIndex: 'createdAt',
    key: 'createdAt',
    render: (text: string) => {
      return <Text>{text || '-'}</Text>
    }
  },
  {
    title: i18n.t("personal:status"),
    dataIndex: 'status',
    key: 'status',
  }
];
//同构复制
// 进行中、已暂停、等待中、任务失败
function renderStatusBadge(record: any): React.ReactNode {
  const { successNum, totalTaskNum } = record
  if (record.state === 2) {
    return (
      <span style={{ display: 'flex', alignItems: 'center', justifyContent: "space-between", color: "blue" }}>
        <span>{i18n.t("personal:inProgress")}</span>
        <span>{i18n.t("personal:completed")}{successNum + '/' + totalTaskNum}</span>
      </span>

    );
  } else if (record.state === 3) {
    return (
      <span style={{ display: 'flex', alignItems: 'center', justifyContent: "space-between", color: 'gray' }}>
        <span>{i18n.t("personal:paused")}</span>
        <span>{i18n.t("personal:completed")}{successNum + '/' + totalTaskNum}</span>
      </span>
    );
  } else if (record.state === 1) {
    return (
      <span style={{ display: 'flex', alignItems: 'center', justifyContent: "space-between", color: 'gray' }}>
        <span>{i18n.t("personal:pending")}</span>
        <span>{i18n.t("personal:completed")}{successNum + '/' + totalTaskNum}</span>
      </span>
    );
  } else if (record.state === 5) {
    return (
      <span style={{ display: 'flex', alignItems: 'center', justifyContent: "space-between", color: 'green' }}>
        <span>{i18n.t("personal:successful")}</span>
        <span>{i18n.t("personal:completed")}{successNum + '/' + totalTaskNum}</span>
      </span>
    );
  } else if (record.state === 6) {
    return (
      <span style={{ display: 'flex', alignItems: 'center', justifyContent: "space-between", color: 'red' }}>
        <span>{i18n.t("personal:fail")}</span>
        <span><span>{i18n.t("personal:completed")}{successNum + '/' + totalTaskNum}</span></span>
      </span>
    );
  }

  return <></>;
}

const dataReplicationColumns = [
  {
    title: i18n.t("personal:taskNumber"),
    dataIndex: 'taskNo',
    key: 'taskNo',
    render: (text: string, record: any) => {
      const idStr = record?.id?.toString()
      const serialNumberStr = record?.serialNumber?.toString()
      const showTxt = serialNumberStr || idStr;
      return <>
        {
          showTxt
            ?
            <Tooltip title={showTxt}>{showTxt?.length > 16 ? showTxt?.substring(0, 16) + '...' : showTxt}</Tooltip>
            : '_'
        }
      </>
    }
  },
  {
    dataIndex: "taskName",
    title: i18n.t("personal:taskName"),
  },
  {
    key: "sourceContexts",
    title: i18n.t("personal:source"),
    dataIndex: 'sourceContexts',
    render(value: any[], record: any) {
      const firstData: any = value?.[0];

      return (
        <Tooltip title={
          <div>
            {
              value?.map(i => (<div key={i?.nodePathWithType}>
                 {renderNodeWithIcon(record?.sourceName, record?.targetType, i?.nodePathWithType)}
              </div>))
            }
          </div>
        }>
          <div className='flexAlignCenter'>
          {renderNodeWithIcon(record?.sourceName,record?.sourceType, firstData?.nodePathWithType)}
          {value?.length > 1 && <div style={{ padding: '2px 5px', textAlign: 'center',borderRadius: '50%', background: '#ccc'}}>{`+${value.length -1}`}</div>}
          </div>
        </Tooltip>
      )
    },
  },
  {
    key: "target",
    dataIndex:'targetNodePath',
    title: i18n.t("personal:target"),
    render(value: string, record: any) {
      const targetNodes = record?.targetContexts?.length ? record?.targetContexts : [{
        nodePathWithType: value,
        connectinName:record?.targetName,
        connectionType:record?.targetType,
      }]
      const firstData: any = targetNodes?.[0];

      return (
        <Tooltip title={
          <div>
            {
              targetNodes?.map((i: any) => (<div key={i?.nodePathWithType}>
                 {renderNodeWithIcon(record?.targetName, record?.targetType, i?.nodePathWithType)}
              </div>))
            }
          </div>
        }>
          <div className='flexAlignCenter'>
          {renderNodeWithIcon(record?.sourceName,record?.sourceType, firstData?.nodePathWithType)}
          {targetNodes?.length > 1 && <div style={{ padding: '2px 5px', textAlign: 'center',borderRadius: '50%', background: '#ccc'}}>{`+${targetNodes.length -1}`}</div>}
          </div>
        </Tooltip>
      )
    },
  },
  {
    dataIndex: "state",
    title: i18n.t("personal:status"),
    render(value: string, record: any) {
      return (
        <span style={{ fontSize: 12 }}>
          {/* 已暂停和进行中不需要 */}
          {
            ![2, 3,5].includes(record.state) &&
            <div
            style={{ color: 'red', marginBottom: '0px !important', height: 12 }}
            title={i18n.t("data_desens:div_title.failure_count")}
            >
              <ExclamationCircleFilled style={{ marginRight: 4 }} />{record.failureNum}
            </div>
          }
          <Progress
            showInfo={false}
            size="small"
            status={record.failureNum && "exception"}
            style={{ width: '90%' }}
            percent={((record.successNum) / record.totalTaskNum) * 100}
          />
          <div style={{ width: '90%', height: 12 }}>{renderStatusBadge(record)}</div>
        </span>
      );
    },
  },
  {
    dataIndex: "createTime",
    title: i18n.t("personal:startTimeFa"),
    render(value: string) {
      return dayjs(value).format("YYYY-MM-DD HH:mm:ss");
    },
  },
  {
    dataIndex: "updateTime",
    title: i18n.t("personal:endTime"),
    render(value: string) {
      return dayjs(value).format("YYYY-MM-DD HH:mm:ss");
    },
  },
  {
    dataIndex: "owner",
    title: i18n.t("personal:creator"),
    render: (val: string, record: any) => {
      return `${record?.userName}(${ val})`
    }
  },
];
// 同步数据字典
const columnsSyncDict = [
  {
    title: i18n.t("personal:taskNumber"),
    dataIndex: 'taskNo',
    key: 'taskNo',
    fixed: true,
    render: (text: string) => {
      return <Text>{text || '-'}</Text>
    }
  },
  {
    title: i18n.t("personal:source"),
    dataIndex: 'sourceType',
    key: 'sourceType',
    render: (text: string) => {
      return <Text>{text || '-'}</Text>
    }
  },
  {
    title: i18n.t("personal:creator"),
    dataIndex: 'showUser',
    key: 'showUser',
    render: (text: string) => {
      return <Text>{text || '-'}</Text>
    }
  },
  {
    title: i18n.t("personal:creationTime"),
    dataIndex: 'createdAt',
    key: 'createdAt',
  },
  {
    title: i18n.t("personal:status"),
    dataIndex: 'processStatus',
    key: 'processStatus',
  }
];

// 导出-任务信息-字段定义
const ExportFields =  {
  // title
  handleExport: i18n.t("personal:export"),
  createInfo : i18n.t("personal:createInfo"),
  taskInfo : i18n.t("personal:taskInformation"),
  exportFile : i18n.t("personal:exportFile"),
  // fields
  // 创建信息
  userShow : i18n.t("personal:creator"),
  createAt : i18n.t("personal:creationTime"),
  // 任务信息
  taskNo : i18n.t("personal:taskNumber"),
  taskType : i18n.t("personal:taskType"), // 后端不返回，其值与tab名称一致，写死
  resourceName : i18n.t("personal:exportedResources"), // connectionType + resourceName
  statement : i18n.t("personal:exportStatement"),
  exportFormat : i18n.t("personal:exportFormat"),
  exportDataNum : i18n.t("personal:exportRows"),
  menuDumpType : i18n.t("personal:exportType"),
  finishAt : i18n.t("personal:taskEndTime"),
  taskStatus : i18n.t("personal:taskStatus"),
}
// 文本导入-任务信息-字段定义
const ImportFields = {
  // title
  handleImport : i18n.t("personal:textImport"),
  createInfo : i18n.t("personal:createInfo"),
  taskInfo : i18n.t("personal:taskInformation"),
  dataImportConfig : i18n.t("personal:importConfiguration"),
  columnMap : i18n.t("personal:mappingRelationshipDisplay"),
  // fields
  // 创建信息
  user :  i18n.t("personal:creator"),
  startTime : i18n.t("personal:creationTime"),
  // 任务信息
  taskNo : i18n.t("personal:taskNumber"),
  taskType : i18n.t("personal:taskType"), // 后端不返回，其值与tab名称一致，写死
  resourceName : i18n.t("personal:targetTable"),
  fileName : i18n.t("personal:importFile"), // dataSourceType + resourceName
  // 导入配置
  delimiter : i18n.t("personal:fieldDelimiter"),
  lineBreak : i18n.t("personal:recordLineDelimiter"),
  quote : i18n.t("personal:fieldIdentifier"),
  fieldNameLine : i18n.t("personal:fieldNameLine"),
  dataStartLine : i18n.t("personal:firstDataLine"),
  dataEndLine : i18n.t("personal:lastDataLine"),
  decimalPointSymbol : i18n.t("personal:decimalPointSymbol"),
  dateSort : i18n.t("personal:dateTimeSorting"),
  encodeType : i18n.t("personal:binaryDataEncoding"),
  // 映射关系展示
  sourceColumnName : i18n.t("personal:sourceField"),
  targetColumnName : i18n.t("personal:targetField"),
}
// 批量执行-任务信息-字段定义
const BatchExecuteFields = {
  // title
  batchExecute : i18n.t("personal:batchExecute"),
  createInfo : i18n.t("personal:createInfo"),
  taskInfo : i18n.t("personal:taskInformation"),
  scriptInfos : i18n.t("personal:attachmentInformation"),
  indexFile : i18n.t("personal:indexFile"),
  sqlCheckFile : i18n.t("personal:sqlAuditResult"),
  // fields
  // 创建信息
  userShow : i18n.t("personal:creator"),
  createdAt : i18n.t("personal:creationTime"),
  // 任务信息
  taskNo : i18n.t("personal:taskNumber"),
  dataSourceType : i18n.t("personal:dataSource"),
  updatedAt : i18n.t("personal:taskEndTime"),
  executeMode : i18n.t("personal:executionMethod"),
  scheduledCron : i18n.t("personal:taskCycle"),
  errorHandleMode : i18n.t("personal:executionErrorHandler"),
  taskType : i18n.t("personal:taskType"), // 后端不返回，其值与tab名称一致，写死
}
// 同步数据字典-任务信息-字段定义
const SyncDictFields = {
  // title
  syncDict : i18n.t("personal:syncDataDictionary"),
  createInfo : i18n.t("personal:createInfo"),
  taskInfo : i18n.t("personal:taskInformation"),
  // fields
  // 创建信息
  showUser : i18n.t("personal:creator"),
  createdAt : i18n.t("personal:creationTime"),
  // 任务信息
  taskNo : i18n.t("personal:taskNumber"),
  taskType : i18n.t("personal:taskType"), // 后端不返回，其值与tab名称一致，写死
  resourceName : i18n.t("personal:synchronizedResources"),
  sourceType : i18n.t("personal:source"),
  updatedAt : i18n.t("personal:taskEndTime"),
}

// 导出-执行状态
const HandleExportStatus: { [p: string]: string }  = {
  'SUCCESS': i18n.t("personal:successful"),
  'FAILURE': i18n.t("personal:fail"),
  'DOWNLOADED': i18n.t("personal:downloaded"),
  'PROCESSING': i18n.t("personal:processing"),
  'CREATE': i18n.t("personal:creating"),
  'QUEUING': i18n.t("personal:inQueue"),
  'STOP': i18n.t("personal:abort"),
  'STOPPING': i18n.t("personal:aborting"),
} 

export type HandleExportStatusKey = keyof typeof HandleExportStatus;

// 文本导入-执行状态
const HandleImportStatus = {
  'SUCCESS': i18n.t("personal:successful"),
  'FAILURE': i18n.t("personal:fail"),
  'PROCESSING': i18n.t("personal:inProgress"),
}

// 批量执行-执行状态
const BatchExecuteStatus = {
  'EXECUTE_SUCCESS': i18n.t("personal:executionSuccess"),
  'EXECUTE_FAILURE': i18n.t("personal:executionFailure"),
  'EXECUTING': i18n.t("personal:executing"),
  'TOBE_EXECUTE': i18n.t("personal:pendingExecution"),
  'WAIT_EXECUTING': i18n.t("personal:waitingForAutoExecution"),
  'TERMINATE': i18n.t("personal:terminate"),
  'SCHEDULED_EXECUTE': i18n.t("personal:scheduledExecute"),
}

// 同步数据字典-执行状态
const SyncDictStatus = {
  'WAITING': i18n.t("personal:waiting"),
  'RUNNING': i18n.t("personal:currentlyExecuting"),
  'TASK_COMPLETE': i18n.t("personal:taskCompleted"),
  'TASK_FAILED': i18n.t("personal:taskFailed"),
  'TASK_CLOSED': i18n.t("personal:taskClosed"),
  'TASK_STOP_ING': i18n.t("personal:terminating"),
  'TASK_STOP': i18n.t("personal:taskTerminated"),
  // 'PARTIAL_SUCCESS':'部分成功'
}

// 状态对应 icon color
 const StatusIconColor: { [p: string]: string } = {
  'SUCCESS': '#52c41a',
  'FAILURE': '#ff3333',
  'DOWNLOADED': '#15c44f',
  'PROCESSING': '#0256ff',
  'CREATE': '#a7c4ff',
  'QUEUING': '#a7c4ff',
  'EXECUTE_SUCCESS': '#15c44f',
  'EXECUTE_FAILURE': '#cb3838',
  'EXECUTING': '#0256ff',
  'TOBE_EXECUTE': '#a7c4ff',
  'TERMINATE': '#868fa3',
  'WAITING': '#a7c4ff',
  'RUNNING': '#0256ff',
  'TASK_COMPLETE': '#15c44f',
  'TASK_FAILED': '#cb3838',
  'TASK_CLOSED': '#868fa3',
  'FINISH': '#868fa3',
  'WAIT_EXECUTING': '#a7c4ff',
  'TASK_STOP_ING': '#0256ff',
  'TASK_STOP': '#cb3838',
  'SCHEDULED_EXECUTE': '#a7c4ff',
  'PARTIAL_SUCCESS':'#cb3838',
  'STOP': '#ff3333',
  'STOPPING': '#0256ff',
}

const TaskCenterTabsItems = ['handleExport', 'handleImport', 'batchExecute', 'syncDict', 'dataReplication'];

export type TaskCenterTabKey = typeof TaskCenterTabsItems[number];
// 任务类型Map
const TaskTypeMap: { [key: string]: string } = {
  "handleExport": i18n.t("personal:export"),
  "handleImport": i18n.t("personal:textImport"),
  "batchExecute": i18n.t("personal:batchExecute"),
  "syncDict": i18n.t("personal:syncDataDictionary"),
  "dataReplication": i18n.t("personal:dataCopy"),
}

// 执行方式Map
export const ExecuteWayMap: { [key: string]: string } = {
  "AUTO": i18n.t("personal:autoExecute"),
  "MANUAL": i18n.t("personal:immediateExecute"),
  "SCHEDULED": i18n.t("personal:scheduledExecute"),
}

// 错误处理方式Map
export const ErrorHandleWayMap: { [key: string]: string } = {
  "TERMINAL": i18n.t("personal:terminate"),
  "IGNORE": i18n.t("personal:ignore"),
}

// 导出-导出配置字段
const DataImportConfigFields = [
  "delimiter",
  "lineBreak",
  "quote",
  "fieldNameLine",
  "dataStartLine",
  "dataEndLine",
  "decimalPointSymbol",
  "dateSort",
  "encodeType"
]

interface TaskInfoConfigType {
  [key: string]: { section: string; fields: any }[];
}

// 任务信息配置
const TaskInfoConfig: TaskInfoConfigType = {
  handleExport: [ // 导出
    // section: 详情显示块，fields 各个详情块，都要显示哪些字段
    { section: 'createInfo', fields: [ExportFields.userShow, ExportFields.createAt] },
    {
      section: 'taskInfo', fields: [ExportFields.taskNo, ExportFields.taskType, ExportFields.resourceName, ExportFields.statement,
      ExportFields.exportFormat, ExportFields.exportDataNum, ExportFields.menuDumpType, ExportFields.finishAt]
    },
    // section: 'file' 是文件显示块，fields 是指有几个 文件显示块
    { section: 'file', fields: [ExportFields.exportFile] },
  ],
  handleImport: [ // 文本导入 
    { section: 'createInfo', fields: [ImportFields.user, ImportFields.startTime] },
    { section: 'taskInfo', fields: [ImportFields.taskNo, ImportFields.taskType, ImportFields.resourceName, ImportFields.fileName] },
    {
      section: 'dataImportConfig', fields: [ImportFields.lineBreak, ImportFields.delimiter, ImportFields.quote,
      ImportFields.fieldNameLine, ImportFields.dataStartLine, ImportFields.dataEndLine, ImportFields.decimalPointSymbol,
      ImportFields.dateSort, ImportFields.encodeType]
    },
    { section: 'columnMap', fields: [ImportFields.sourceColumnName, ImportFields.targetColumnName] },
  ],
  batchExecute: [ // 批量执行 
    // section: 详情显示块，fields 各个详情块，都要显示哪些字段
    { section: 'createInfo', fields: [BatchExecuteFields.userShow, BatchExecuteFields.createdAt] },
    {
      section: 'taskInfo', fields: [BatchExecuteFields.taskNo, BatchExecuteFields.dataSourceType, BatchExecuteFields.updatedAt, BatchExecuteFields.executeMode,
      BatchExecuteFields?.scheduledCron, BatchExecuteFields.errorHandleMode, BatchExecuteFields.taskType]
    },
    // section: 'file' 是文件显示块，fields 是指有几个 文件显示块
    { section: 'file', fields: [BatchExecuteFields.scriptInfos, BatchExecuteFields.indexFile, BatchExecuteFields.sqlCheckFile] },
  ],
  syncDict: [ // 同步数据字典
    { section: 'createInfo', fields: [SyncDictFields.showUser, SyncDictFields.createdAt] },
    { section: 'taskInfo', fields: [SyncDictFields.taskNo, SyncDictFields.taskType, SyncDictFields.resourceName, SyncDictFields.sourceType, SyncDictFields.updatedAt] },
  ],
};

//导出 - 编辑器选多多条语句导出 字段
export const exportEditorMultiSqlFields = [ExportFields.taskNo, ExportFields.taskType, ExportFields.resourceName,ExportFields.exportFormat, i18n.t("totalExportedRows"),
  ExportFields.menuDumpType,ExportFields.finishAt,ExportFields.taskStatus
];

export const getTaskInfoFieldConfig = (tabKey: TaskCenterTabKey, parentDetail: any) => {

  if (!tabKey) return [];
  if(tabKey === 'handleExport') {
    let defaultTaskInfo: any = TaskInfoConfig[tabKey];
    if (parentDetail?.dataExportItem?.length) {
      defaultTaskInfo = defaultTaskInfo.map((taskPart: any) => {
        if (taskPart.section === 'taskInfo') {
          return {
            ...taskPart,
            fields: exportEditorMultiSqlFields
          }
        }
        return taskPart
      })
    }
    return defaultTaskInfo;
  }else {
    return TaskInfoConfig[tabKey];
  }
}

export {
  columnsExport,
  columnsImport,
  columnsBatchExecute,
  dataReplicationColumns,
  columnsSyncDict,
  ExportFields,
  ImportFields,
  BatchExecuteFields,
  SyncDictFields,
  TaskInfoConfig,
  TaskCenterTabsItems,
  TaskTypeMap,
  DataImportConfigFields,
  SortFilters,
  HandleExportStatus,
  HandleImportStatus,
  BatchExecuteStatus,
  SyncDictStatus,
  StatusIconColor,
}


