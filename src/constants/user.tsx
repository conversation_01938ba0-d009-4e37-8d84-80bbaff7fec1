import React from 'react'
import { Iconfont } from 'src/components'
import { ColumnsType } from 'antd/lib/table'
import dayjs from 'dayjs'
import {
  HistoryRecord,
  RoleEntity,
  UserConnection,
  UserConnectionInfo,
} from 'src/api'
import { renderItemField } from './render'
import i18n from 'src/i18n';

export const userInfoLabelMap: Record<string, string> = {
  dept: i18n.t("dept"),
  email: i18n.t("email"),
  images: i18n.t("userAvatar"),
  jobNumber: i18n.t("jobNumber"),
  password: i18n.t("userPassword"),
  // postId: '岗位 ID',
  postName: i18n.t("postName"),
  sessionId: i18n.t("sessionId"),
  telephone: i18n.t("telephone"),
  userCategory: i18n.t("userCategory"),
  userGender: i18n.t("userGender"),
  userId: i18n.t("account"),
  userName: i18n.t("userName"),
  userStatus: i18n.t("userStatus"),
  isPrincipal: i18n.t("isPrincipal"),
}

// 设置认证生效时间的下拉框
export const effectTimeTypeOptions: any[] = [
  { value: 8, label: i18n.t("daily") },
  { value: 1, label: i18n.t("monday") },
  { value: 2, label: i18n.t("tuesday") },
  { value: 3, label: i18n.t("wednesday") },
  { value: 4, label: i18n.t("thursday") },
  { value: 5, label: i18n.t("friday") },
  { value: 6, label: i18n.t("saturday") },
  { value: 7, label: i18n.t("sunday") },
]

export const userTableColumns = [
  { title: i18n.t("userName"), dataIndex: 'userName', ellipsis: true },
  { title: i18n.t("account"), dataIndex: 'userId', ellipsis: true },
  //{ title: '所属部门', dataIndex: 'dept', ellipsis: true },
]

export const UserRolesColumns: ColumnsType<RoleEntity> = [
  { title: i18n.t("roleName"), dataIndex: 'roleName', ellipsis: true },
  { title: i18n.t("description"), dataIndex: 'description', ellipsis: true },
  {
    dataIndex: 'permissionNames',
    title: i18n.t("permissions"),
    render(_, record) {
      const { permissionNames } = record
      return permissionNames
        .map(({ permissionName }) => permissionName)
        .join(', ')
    },
    ellipsis: true,
  },
  {
    title: i18n.t("validityPeriod"),
    dataIndex: 'validPeriod',
    ellipsis: true,
    width: '96px',
    render(text) {
      return text === i18n.t("permanent") ? text : dayjs(text).format('YYYY-MM-DD')
    },
  },
  {
    title: i18n.t("currentStatus"),
    dataIndex: 'valid',
    ellipsis: true,
    render: (valid) => (valid ? i18n.t("valid") : i18n.t("invalid")),
    width: '96px',
  },
]

// ! deprecated. Use UserConnectionInfoColumns below.
// ! creator -> creatorName
export const UserConnectionsColumns: ColumnsType<UserConnection> = [
  {
    title: i18n.t("connectionName"),
    dataIndex: 'connectionName',
    ellipsis: true,
    render: (connectionName, record) => {
      const { connectionType } = record
      return (
        <>
          <Iconfont
            type={'icon-connection-' + (connectionType || 'Other')}
            className="mr8"
            style={{ fontSize: 14 }}
          ></Iconfont>
          {connectionName}
        </>
      )
    },
  },
  { title: i18n.t("creator"), dataIndex: 'creator', ellipsis: true, width: '104px' },
]

export const UserConnectionInfoColumns: ColumnsType<UserConnectionInfo> = [
  {
    title: i18n.t("personal:connectionName"),
    dataIndex: 'connectionName',
    ellipsis: true,
    render: (connectionName, record) => {
      const { connectionType, deleted, archive } = record
      return (
        <>
          <Iconfont
            type={'icon-connection-' + (connectionType || 'Other')}
            className="mr8"
            style={{ fontSize: 14, opacity: deleted ? 0.55 : 1 }}
          ></Iconfont>
          {deleted ? <span style={{ opacity: 0.55 }}>({i18n.t("deleted")}) </span>
            : archive && <span style={{ opacity: 0.55 }}>({i18n.t("archived")}) </span>
          }
          {connectionName}
        </>
      )
    },
  },
  { title: i18n.t("personal:creator"), dataIndex: 'creator', ellipsis: true, width: '104px' },
]

export const UserHistoryColumns: ColumnsType<HistoryRecord> = [
  {
    title: i18n.t("executionConnection"),
    dataIndex: 'connectionInfo',
    ellipsis: true,
    render: (connectionInfo) => {
      const { connectionName, connectionType, deleted } = connectionInfo || {}
      // todo: {} 为健壮性处理，应由后端保证
      return (
        <>
          <Iconfont
            type={'icon-connection-' + (connectionType || 'Undefined')}
            className="mr8"
            style={{ fontSize: 14, opacity: deleted ? 0.55 : 1 }}
          ></Iconfont>
          {deleted && <span style={{ opacity: 0.55 }}>({i18n.t("deleted")}) </span>}
          {connectionName}
        </>
      )
    },
    width: 168,
  },
  {
    title: i18n.t("executionDatabase"),
    dataIndex: 'databaseName',
    ellipsis: true,
    width: '120px',
  },

  {
    title: i18n.t("sqlStatement"),
    dataIndex: 'sql',
    render: renderItemField('longString'),
  },
  {
    title: i18n.t("executionResult"),
    dataIndex: 'resultStatus',
    render: renderItemField('successStatus'),
    width: '80px',
  },
  {
    title: i18n.t("executionTime"),
    dataIndex: 'startTime',
    render: renderItemField('timeString'),
    width: '152px',
  },
  {
    title: i18n.t("timeConsumed"),
    dataIndex: 'timeConsuming',
    ellipsis: true,
    width: '88px',
  },
]

export const UserPermsColumns = [
  {
    title: i18n.t("permissionName"),
    dataIndex: 'permissionName',
    ellipsis: true,
    width: '240px',
  },
  {
    title: i18n.t("permissionType"),
    dataIndex: 'permissionType',
    ellipsis: true,
    width: '240px',
  },
  {
    title: i18n.t("permissionDescription"),
    dataIndex: 'permissionDesc',
    width: '240px',
    ellipsis: true,
  },
  { title: i18n.t("operationPermissions"), dataIndex: 'operations', ellipsis: true },
]
