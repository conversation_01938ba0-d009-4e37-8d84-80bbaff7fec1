import React from 'react'
import { message, Typography, Button } from 'antd'
import { setIsLoggedIn, setUserInfo } from 'src/appPages/login/loginSlice'
import { useDispatch, useSelector } from 'src/hook'
import { useTranslation } from 'react-i18next'

export const LogoutMessage = () => {
  const { t } = useTranslation()
  const dispatch = useDispatch()
  const { userId, userName } = useSelector((state) => state.login?.userInfo) || {};

  return (
    <Typography.Text>
      {t("features:loginRequired")}
      <Button
        type="link"
        size="small"
        onClick={() => {
          message.destroy()
          dispatch(setIsLoggedIn(false))
          // 登出时清理用户信息状态
          if (!!userId) {
            dispatch(setUserInfo({userId, userName}))
          }
        }}
      >
        {t("features:pleaseLoginImmediately")}
      </Button>
    </Typography.Text>
  )
}
