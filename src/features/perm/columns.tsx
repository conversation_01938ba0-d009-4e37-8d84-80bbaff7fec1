import React from 'react'
import { Tooltip } from 'antd'
import { ColumnsType } from 'antd/lib/table'
import { PermType } from 'src/constants'
import Service from 'src/service'
import { useSelector } from 'src/hook'
import { useTranslation } from 'react-i18next'

export const GetPermissionTransferTableColumns = (
  permTypes?: { logo: string; message: string }[],
) => {
  const { t } = useTranslation()
  const { productGrade } = useSelector((state) => state.login)
  const columns: ColumnsType<{
    name: string
    description?: string
    permissionType: PermType
    permissionId: number | string
  }> = [
    {
      title: t("feature:permissionAlias"),
      dataIndex: 'name',
      ellipsis: true,
      render: (_, record) => (
        <Tooltip title={record.description} placement="topLeft">
          {record.name}
        </Tooltip>
      ),
    },
    {
      title: (t("feature:permissionType")),
      dataIndex: 'permissionType',
      ellipsis: true,
      filters: permTypes?.map(({ logo, message }) => ({
        text: message,
        value: logo,
      })),
      onFilter: (value, record) => record.permissionType === value,
      render: (_, record: any) => {
        const permService = productGrade === "CQ3" ? Service.permServiceCq3 : Service.permService  
        return (
          t(permService.getPermData({ key: record.permissionType })?.name) ||
          record.permissionType
        )
      },
    },
  ]
  return columns
}
