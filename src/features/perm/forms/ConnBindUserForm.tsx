import React, { useEffect } from 'react'
import { useRequest } from 'src/hook'
import { Form } from 'antd'
import { FormInstance } from 'antd/lib/form'
import { TableTransfer } from 'src/components'
import {
  getAllUsers,
  getConnectionUsers,
  RoleEntity,
  UserEntity,
} from 'src/api'
import { useTranslation } from 'react-i18next'

export const ConnBindUserForm: React.FC<{
  form?: FormInstance
  mode: 'add' | 'edit'
  record?: RoleEntity
  connectionId?: string | number
}> = ({ form, mode, record, connectionId }) => {
  const { t } = useTranslation()
  const {
    data: connUsers,
    loading: loadingConnUsers,
    run: fetchConnUsers,
  } = useRequest(getConnectionUsers, {
    manual: true,
  })
  // 对自定义角色，待选用户为连接下用户
  useEffect(() => {
    if (!connectionId) return
    if (record?.initRole === true) return
    fetchConnUsers(connectionId)
  }, [connectionId, record, fetchConnUsers])

  const {
    data: allUsers,
    loading: loadingAllUsers,
    run: fetchAllUsers,
  } = useRequest(getAllUsers, {
    manual: true,
  })
  // 对初始角色，待选用户为所有用户
  useEffect(() => {
    if (!record?.initRole) return
    fetchAllUsers()
  }, [fetchAllUsers, record])

  useEffect(() => {
    if (mode === 'edit' && record) {
      const userIds = record.userInfos.map(({ userId }) => userId)
      form?.setFieldsValue({ boundUsers: userIds })
    }
  }, [form, mode, record])

  const columns = [
    { title: t("feature:userName"), dataIndex: 'userName', ellipsis: true },
    {
      title: t("feature:account"),
      dataIndex: 'userId',
      ellipsis: true,
      // sorter: (a: { userId: string | any[] }, b: { userId: string | any[] }) => a.userId.length - b.userId.length
      sorter: (a: any, b: any) => a.userId.toLowerCase().slice(0,1).charCodeAt(0) - b.userId.toLowerCase().slice(0,1).charCodeAt(0)
    },
  ]

  return (
    <Form name="bindUserForm" form={form} component={false}>
      <Form.Item name="boundUsers" valuePropName="targetKeys" noStyle>
        <TableTransfer
          // todo: 修复穿梭框左上角全选选项
          tableLoading={{ left: loadingAllUsers || loadingConnUsers }}
          dataSource={record?.initRole ? allUsers : connUsers}
          columns={columns}
          showSearch
          filterOption={(inputValue, record) => {
            const { userId, userName } = (record as unknown) as UserEntity
            return (
              (userId || '')?.indexOf(inputValue) !== -1 ||
              (userName || '')?.indexOf(inputValue) !== -1
            )
          }}
          rowKey={(record) => record.userId}
          titles={['', t("feature:bindUser")]}
        />
      </Form.Item>
    </Form>
  )
}
