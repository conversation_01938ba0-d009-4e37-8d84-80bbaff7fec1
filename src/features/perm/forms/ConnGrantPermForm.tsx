import React, { useEffect } from 'react'
import { useRequest } from 'src/hook'
import { Form } from 'antd'
import { FormInstance } from 'antd/lib/form'
import { TableTransfer } from 'src/components'
import { getPermsByConnectionId, RoleEntity } from 'src/api'
import { GetPermissionTransferTableColumns } from '../columns'
import { useTranslation } from 'react-i18next'

// 授予权限(连接)
export const ConnGrantPermForm: React.FC<{
  form?: FormInstance
  permTypes?: { logo: string; message: string }[]
  mode: 'add' | 'edit'
  connectionId?: number | string
  record?: RoleEntity
}> = ({ form, permTypes, mode, record, connectionId }) => {
  const { t } = useTranslation()
  const { data: perms, loading, run: fetchPerms } = useRequest(
    getPermsByConnectionId,
    {
      manual: true,
      formatResult: (data) =>
        data.map((perm) => ({
          ...perm,
          key: perm.permissionId.toString(),
        })),
    },
  )

  useEffect(() => {
    if (connectionId) {
      fetchPerms(connectionId)
    }
  }, [connectionId, fetchPerms])

  useEffect(() => {
    if (mode === 'edit' && record) {
      const permIds = record.currentRolePerms.map(
        ({ permissionId }) => permissionId,
      )
      form?.setFieldsValue({ grantedPerms: permIds })
    }
  }, [form, mode, record])

  const columns = GetPermissionTransferTableColumns(permTypes)

  return (
    <Form form={form} name="grantPermForm" component={false}>
      <Form.Item name="grantedPerms" valuePropName="targetKeys" noStyle>
        <TableTransfer
          titles={['', t("features:grantPermission")]}
          dataSource={perms}
          tableLoading={{ left: loading }}
          columns={columns}
          rowKey={(record) => record.permissionId}
          filterOption={(inputValue, record) => {
            // todo: 明确类型定义
            const { name, permissionType } = record
            return (
              (name || '')?.indexOf(inputValue) !== -1 ||
              (permissionType || '')?.indexOf(inputValue) !== -1
            )
          }}
          showSearch
        />
      </Form.Item>
    </Form>
  )
}
