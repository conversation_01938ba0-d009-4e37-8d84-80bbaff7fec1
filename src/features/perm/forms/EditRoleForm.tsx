import React, { useEffect } from 'react'
import { Checkbox, Form, Input, Switch } from 'antd'
import { DatePicker } from 'src/components'
import { FormInstance } from 'antd/lib/form'
import dayjs, { Dayjs } from 'dayjs'
import { RoleInfoEntity } from 'src/api'
import { LabeledValue } from 'antd/lib/select'
import { looseNameValidator } from 'src/util/nameValidator'
import { FormLayout } from 'src/constants'
import { useTranslation } from 'react-i18next'

const { RangePicker } = DatePicker

export interface RoleInfoFormValues {
  id: number
  name: string
  description?: string
  timeLimit?: boolean
  duration?: Dayjs[] | null
  period?: string[]
}

export const EditRoleForm: React.FC<{
  form?: FormInstance
  mode: 'add' | 'edit'
  record?: RoleInfoEntity
  roleOptions?: LabeledValue[]
}> = ({ form, mode, record, roleOptions }) => {
  const { t } = useTranslation()

  const durationOptions = [
    { label: t("features:sunday"), value: '1' },
    { label: t("features:monday"), value: '2' },
    { label: t("features:tuesday"), value: '3' },
    { label: t("features:wednesday"), value: '4' },
    { label: t("features:thursday"), value: '5' },
    { label: t("features:friday"), value: '6' },
    { label: t("features:saturday"), value: '7' },
  ]

  const isEditing = mode === 'edit'

  useEffect(() => {
    if (record && isEditing) {
      const { name, description, beginDate, endDate, dayMask } = record
      const timeLimit = Boolean(beginDate || endDate || dayMask)
      const period = dayMask?.split('')
      const duration = [
        beginDate && dayjs(beginDate),
        endDate && dayjs(endDate),
      ]
      form?.setFieldsValue({
        name,
        description,
        timeLimit,
        period,
        duration,
      })
    }
  }, [form, isEditing, record])

  return (
    <Form name="roleInfoForm" form={form} {...FormLayout}>
      <Form.Item
        name="name"
        label={t("features:roleName")}
        {...(!isEditing && {
          rules: [
            { required: true, message: t("features:pleaseInputRoleName") },
            { validator: looseNameValidator },
          ],
        })}
      >
        <Input readOnly={isEditing} bordered={!isEditing} />
      </Form.Item>
      <Form.Item name="description" label={t("features:roleDescription")}>
        <Input.TextArea autoSize={{ minRows: 2, maxRows: 2 }} maxLength={200} />
      </Form.Item>
      <Form.Item name="timeLimit" label={t("features:roleValidity")} valuePropName="checked">
        <Switch />
      </Form.Item>
      <Form.Item
        noStyle
        shouldUpdate={(prev, next) => {
          return prev.timeLimit !== next.timeLimit
        }}
      >
        {({ getFieldValue }) => {
          const timeLimit = getFieldValue('timeLimit')
          return timeLimit ? (
            <>
              <Form.Item name="duration" label={t("features:timePeriod")}>
                <RangePicker
                  disabledDate={(currentDate) =>
                    currentDate && currentDate < dayjs().startOf('day')
                  }
                />
              </Form.Item>
              <Form.Item name="period" label={t("features:cycle")}>
                <Checkbox.Group options={durationOptions} />
              </Form.Item>
            </>
          ) : null
        }}
      </Form.Item>
    </Form>
  )
}
