import React, { useEffect } from 'react'
import { useRequest } from 'src/hook'
import { Form } from 'antd'
import { FormInstance } from 'antd/lib/form'
import { TableTransfer } from 'src/components'
import { getPermsByType, RoleEntity } from 'src/api'
import { GetPermissionTransferTableColumns } from '../columns'
import { useTranslation } from 'react-i18next'

// 授予权限(系统)
export const SysGrantPermForm: React.FC<{
  form?: FormInstance
  mode: 'add' | 'edit'
  record?: RoleEntity
}> = ({ form, mode, record }) => {
  const { t } = useTranslation()
  const { data: perms, loading } = useRequest(
    () => getPermsByType({ type: 'system', connectionId: 0 }),
    {
      formatResult: (data) =>
        data.map((perm) => ({
          ...perm,
          key: perm.permissionId.toString(),
        })),
    },
  )

  useEffect(() => {
    if (mode === 'edit' && record) {
      const permIds = record.currentRolePerms.map(
        ({ permissionId }) => permissionId,
      )
      form?.setFieldsValue({ grantedPerms: permIds })
    }
  }, [form, mode, record])

  const columns = GetPermissionTransferTableColumns()

  // ! HACK
  // 因为需要暂时拿到告警模块，但是后端返回的权限数据中仍然有告警这一项
  // 所以前端暂时手动过滤掉这行数据
  const permissionsWithoutAlarmModule = perms?.filter(
    ({ permissionId }) => permissionId !== 'Cloudquery.Warning',
  )

  return (
    <Form form={form} name="grantPermForm" component={false}>
      <Form.Item name="grantedPerms" valuePropName="targetKeys" noStyle>
        <TableTransfer
          titles={['', t("features:grantPermission")]}
          dataSource={permissionsWithoutAlarmModule}
          tableLoading={{ left: loading }}
          columns={columns}
          rowKey={(record) => record.permissionId}
          filterOption={(inputValue, record) => {
            const { name, permissionType } = record
            return (
              (name || '')?.indexOf(inputValue) !== -1 ||
              (permissionType || '')?.indexOf(inputValue) !== -1
            )
          }}
          showSearch
        />
      </Form.Item>
    </Form>
  )
}
