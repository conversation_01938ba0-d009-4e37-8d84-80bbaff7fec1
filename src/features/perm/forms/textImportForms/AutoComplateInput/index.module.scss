.autoComplateContainer {
  position: relative;
  width: 100%;

  .input-container {
    position: relative;
    display: inline-block;
    width: calc(100% - 34px);
  }

  input {
    padding: 5px;
    font-size: 14px;
    border: 1px solid #ccc;
    border-right: none;
    border-radius: 4px 0px 0px 4px;
    width: 100%;

    &:focus {
      outline: none;
      /* 移除默认的聚焦轮廓 */
      border-color: #5487ff;
    }

    &:-internal-autofill-selected {
      background-color: #fff !important;
    }
  }

  button {
    height: 34px;
    width: 34px;
    padding: 4px 10px;
    border-radius: 0px 4px 4px 0px;
  }

  .highlight {
    position: absolute;
    top: 6px;
    left: 5px;
    background-color: lightblue;
    user-select: none;
    display: inline-block;
    overflow: hidden;
    max-width: 87%;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .overlayClassName {
    position: absolute;
    width: 100%;
    left: 0 !important;

    >ul li {
      min-height: 32px;
    }
  }
}

:global {
  .ant-form-item-has-error .ant-form-item-control .ant-form-item-control-input {

    .autoComplateInput,
    .autoComplateButton {
      border-color: #ff3333 !important;
    }
  }
}