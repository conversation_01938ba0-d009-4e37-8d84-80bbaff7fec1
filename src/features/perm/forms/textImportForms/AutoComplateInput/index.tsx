import React, { useState, useRef, useEffect, useCallback } from "react";
import { Button, Dropdown, Menu } from 'antd'
import { DownOutlined } from '@ant-design/icons'
import styles from './index.module.scss'

// 仿照navicat导入表自动补全效果

const AutoCompleteInput = ({
  value: defaultValue,
  options = [],
  onChange
}: {
  value?: string,
  options: any,
  onChange: (value: string) => void
}) => {

  const inputFieldRef = useRef<HTMLInputElement | null>(null);
  const highlightRef = useRef<HTMLSpanElement | null>(null);
  const inputField = inputFieldRef.current;
  const highlight = highlightRef.current;

  // 记录上一次输入框的值
  const [lastValue, setLastValue] = useState("");

  const onChangeText = useCallback((e: Event) => {

    if (!inputField || !highlight) return
    const target = e.target as HTMLInputElement;

    if (!target) return;
    const value = target.value;
    // 当前光标位置
    const cursorPosition = target.selectionStart;
    // 输入字符有补全提示，删除字符不需要补全提示
    if (value.length > lastValue.length) {
      // 以输入字符为首起始位进行匹配
      const match = options.find((item: any) => item?.label?.startsWith(value.toLowerCase()));
      if (match) {
        // 设置补全内容
        const suggestion = match?.label?.slice(value.length);
        highlight.textContent = suggestion;
        const valueWidth = getTextWidth(value);
        // 动态调整补全内容位置
        highlight.style.left = `${valueWidth}px`;
      } else {
        highlight.textContent = "";
      }
    } else {
      highlight.textContent = "";
    }
    // 更新输入框的值
    setLastValue(value);

    // 保持光标位置
    inputField.setSelectionRange(cursorPosition, cursorPosition);
  }, [highlight, inputField, lastValue,JSON.stringify(options)])

  const onHandelHighlightedArea = useCallback((e: MouseEvent) => {

    if (!inputField || !highlight) return
    const value = inputField.value;
    const match = options.find((item: any) => item?.label?.startsWith(value?.toLowerCase()));
    //定位到补全内容
    const highlightText = highlight.textContent;
    if (highlightText && match) {
      //计算点击位置在补全字符的偏移量
      const clickOffsetX = e.offsetX;
      const suggestion = match?.label.slice(value.length);
      const suggestionWidth = getTextWidth(suggestion);
      //鼠标聚焦位置
      const insertPosition = Math.floor((clickOffsetX / suggestionWidth) * suggestion.length);
      //鼠标聚焦之前字符
      const newValue = value + suggestion.slice(0, insertPosition);
      inputField.value = newValue;
      //鼠标聚焦之后字符
      inputField.value += suggestion.slice(insertPosition); // 输入补全内容
      inputFieldRef.current?.focus();
      highlight.textContent = ""; 
      // 光标定位到末尾
      inputField.setSelectionRange(newValue.length, newValue.length); 
    }

  }, [highlight, inputField, lastValue, JSON.stringify(options)])

  useEffect(() => {
    if (!inputField || !highlight) return
    // 初始化输入框的值
    if (defaultValue) {
      inputField.value = defaultValue;
      highlight.textContent = "";
    }
  },[defaultValue])

  //更formitem新值
  useEffect(() => {
    if (!inputFieldRef.current) return
    
    onChange(inputField?.value || '');
  },[inputField?.value])

  useEffect(() => {

    if (!inputField || !highlight) return

    // 监听输入事件
    inputField.addEventListener("input", onChangeText);
    //点击补全区域事件
    highlight.addEventListener("click", onHandelHighlightedArea);
    // 监听回车键 全部填充匹配内容
    inputField.addEventListener("keydown", (e) => {
      if (e.key === "Enter") {
        const value = inputField.value.toLowerCase();
        const match = options.find((item: any) => item?.label?.toLowerCase().startsWith(value?.toLowerCase()));

        if (match) {
          inputField.value = match?.label;
          setLastValue(match);
          highlight.textContent = ""; // 清空补全部分
          inputField.setSelectionRange(match?.label?.length, match?.label?.length); // 光标定位到末尾
        }
      }
    });
  }, [inputField, highlight, lastValue])


  // 计算文本宽度
  const getTextWidth = (text: string) => {
    const span = document.createElement("span");
    span.style.visibility = "hidden";
    span.style.whiteSpace = "nowrap";
    span.style.fontSize = "14px";
    span.textContent = text;
    document.body.appendChild(span);
    const width = span.offsetWidth + 7;//数据框内间距+边框宽度
    document.body.removeChild(span);
    return width;
  }

  const onSelectMoreItem = ({ key }: { key: any }) => {
    if (!inputField || !highlight) return
    if (key === 'item_0') {
      inputField.value = '';
    }else {
      inputField.value = key;
    }
    
    highlight.textContent = "";
  }

  const menu = (
    <Menu onClick={onSelectMoreItem}>
      {
        options?.map((i: any) => (
          <Menu.Item key={i?.value}>{i?.label}</Menu.Item>
        ))
      }
    </Menu>
  );
  return (
    <div className={styles['autoComplateContainer']} id="autoComplate">
      <div className={styles["input-container"]}>
        <input ref={inputFieldRef} type="text" id="inputField" className="autoComplateInput"/>
        <span id="highlight" ref={highlightRef} className={styles["highlight"]} data-testid="highlight"></span>
      </div>
      <Dropdown
        overlay={menu}
        trigger={['click']}
        onVisibleChange={(visible) => {
          if (!highlight || !visible) return;
          highlight.textContent = "";
        }}
        getPopupContainer={trigger => trigger?.parentNode as HTMLElement || document.getElementById('autoComplate') || document.body}
        overlayClassName={styles.overlayClassName}
      >
        <Button icon={<DownOutlined />} className="autoComplateButton"/>
      </Dropdown>
    </div>
  );
};

export default AutoCompleteInput;
