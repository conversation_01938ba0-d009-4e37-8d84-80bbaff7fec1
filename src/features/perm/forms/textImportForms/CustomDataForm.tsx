import React from 'react'
import { Form, Input } from 'antd'
import { FormInstance } from 'antd/lib/form'
import { FormItemMultiConectionApprover } from 'src/pageTabs/flowPages/flowFormItems'
import { useTranslation } from 'react-i18next'
export const CustomDataForm = ({
  form,
  connectionId,
}: {
  form: FormInstance
  connectionId: number | string
}) => {
  const { t } = useTranslation()
  return (
    <Form form={form} labelCol={{ span: 6 }} labelAlign="left">
      <Form.Item label={t("features:title")} name="title" rules={[{ required: true, message: t("features:pleaseInputTitle") }]}>
        <Input
          placeholder={t("features:pleaseInputTitle")}
        />
      </Form.Item>
      <Form.Item label={t("features:applyReason")} name="applyReason" rules={[{ required: true, message: t("features:pleaseInputApplyReason") }]}>
        <Input.TextArea
          rows={3}
          placeholder={t("features:pleaseInputApplyReason")}
        />
      </Form.Item>
      <FormItemMultiConectionApprover
        flowType='importTask'
        isRender={true}
        connectionId={connectionId}
      />
    </Form>
  )
}