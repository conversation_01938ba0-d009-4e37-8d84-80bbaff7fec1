import React, { useEffect, useMemo, useState } from 'react';
import { Input, AutoComplete, Button } from 'antd';
import _ from 'lodash';
import { DownOutlined } from '@ant-design/icons';
import styles from './index.module.scss';
import { useTranslation } from 'react-i18next';

const DropdownInput = ({
  value: defaultValue,
  options = [],
  onChange
}: {
  value?: string,
  options: any,
  onChange: (value: string) => void
}) => {
  const { t } = useTranslation()
  const [showMoreOptions, setShowMoreOptions] = useState(true);
  const [value, setValue] = useState<string | undefined>(defaultValue);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setValue(e.target.value); // 更新输入框的值
    setShowMoreOptions(false)
    onChange?.(e.target.value)
  };

  const handleSelect = (value: string) => {
    setValue(value);
    setShowMoreOptions(false);
    onChange?.(value)
  };

  const handleBlur = () => {

    //失焦时候判断当前表是否和下拉框中有相同的值 不区分大小写 
    const tableItem = options?.find((i: any) => i?.value?.toLowerCase() === value?.toLowerCase())
    if (!_.isEmpty(tableItem)) {
      setValue(tableItem?.value);
      setShowMoreOptions(false)
      onChange?.(tableItem?.value)
    }
  };

  useEffect(() => {
    setValue(defaultValue)
  }, [defaultValue])

  const filteredOptions = useMemo(() => {
    let defaultOptions = _.cloneDeep(options);

    if (!showMoreOptions && !!value) {
      defaultOptions = options.filter((i: any) => i.value.toUpperCase().includes(value.toUpperCase()))
    }
    return defaultOptions;
  }, [JSON.stringify(options), value, showMoreOptions])

  const handleShowAllOptions = (e: React.MouseEvent) => {
    e.stopPropagation(); // 阻止事件冒泡
    setShowMoreOptions(true); // 点击后显示所有选项
  };
  
  return (
    <div className={styles.dropdownInput}>
      <AutoComplete
        value={value}
        options={filteredOptions}
        onSelect={handleSelect}
      >
        <Input
          value={value}
          allowClear
          onChange={handleChange} // 处理输入框变化
          onBlur={handleBlur} // 处理失去焦点事件
          placeholder={t("features:pleaseSelectOrEnterTargetTable")}
          addonAfter={
            <Button className={styles.inputIcon} icon={<DownOutlined />} onClick={handleShowAllOptions} />}
        />
      </AutoComplete>
    </div>
  );
};

export default DropdownInput;