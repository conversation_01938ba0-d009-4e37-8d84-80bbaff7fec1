import React, { useContext, useState } from 'react'
import * as _ from 'lodash'
import { Table, Form, Select, Input, Button, Popconfirm } from 'antd'
import { FormInstance } from 'antd/lib/form'
import styles from './index.module.scss'
import { isEmpty } from 'lodash'
import { Iconfont } from 'src/components'
import i18n from 'i18next'

const EditableContext = React.createContext<FormInstance<any> | null>(null)

interface Item {
  key: string
  sourceFields: string
  targetFields: string
  primary: boolean
}

interface EditableRowProps {
  index: number
}

const EditableRow: React.FC<EditableRowProps> = ({ index, ...props }) => {
  const [form] = Form.useForm()
  return (
    <Form form={form} component={false}>
      <EditableContext.Provider value={form}>
        <tr {...props} />
      </EditableContext.Provider>
    </Form>
  )
}

interface EditableCellProps {
  title: React.ReactNode
  editable: boolean
  children: React.ReactNode | null
  dataIndex: keyof Item
  record: Item
  type?: 'input' | 'select' | 'primaryKey'
  targetTableFieldList?: any[]
  sourceTableFieldList?: any[]
  columnTypeList?: any[]
  handleSave: (record: Item) => void
  editingKey: string //正在编辑字段
  handleEdit: (key: string) => void
}

const EditableCell: React.FC<EditableCellProps> = ({
  title,
  editable,
  children,
  dataIndex,
  record,
  type,
  targetTableFieldList,
  sourceTableFieldList,
  columnTypeList,
  handleSave,
  handleEdit,
  editingKey,
  ...restProps
}) => {
  const CELL_OPTION_MAPPING: { [key: string]: any[] | undefined } = {
    sourceFields: sourceTableFieldList,
    targetFields: targetTableFieldList,
    columnTypeName: columnTypeList,

  }

  const [editing, setEditing] = useState(false)
  const form = useContext(EditableContext)!

  const toggleEdit = (type?: 'blur') => {
    setEditing(!editing)
    //失焦取消编辑
    if (type) {
      handleEdit('')
    }else {
      handleEdit(`${dataIndex}-${record?.key}`)
    }
   
    form.setFieldsValue({ [dataIndex]: record[dataIndex] })
  }

  const togglePrimary = () => {
    handleSave({ ...record, primary: !record.primary || false })
  }
  const save = async (type?: 'blur') => {
    try {
      const values = await form.validateFields()
      toggleEdit(type)
      handleSave({ ...record, ...values })
    } catch (errInfo) {
      console.log('Save failed:', errInfo)
    }
  }

  let childNode = children;

  const options = CELL_OPTION_MAPPING[dataIndex] || []

  if (dataIndex === 'primary') {
    return <td {...restProps} onClick={togglePrimary}>{record[dataIndex] === true ? <Iconfont type='icon-primaryKey' /> : ''}</td>
  }

  if (editable) {
    childNode = editingKey === `${dataIndex}-${record?.key}` ? (
      <>
        {type === 'select' && (
          <Form.Item
            name={dataIndex}
            className="editable-cell-select"
          >
            <Select autoFocus onBlur={() => save('blur')} options={options}></Select>
          </Form.Item>
        )}
        {type === 'input' && (
          <Form.Item
            name={dataIndex}
            className="editable-cell-input">
            <Input autoFocus onPressEnter={() => save('blur')} onBlur={() => save('blur')} />
          </Form.Item>
        )}
      </>
    ) : (
      <div
        className="editable-cell-value-wrap"
        onClick={() =>toggleEdit()}>
        {children}
      </div>
    )
  }

  return <td {...restProps}>{childNode}</td>
}

type EditableTableProps = Parameters<typeof Table>[0]

interface DataType {
  key: React.Key
  sourceFields: string | null
  targetFields: string | null
}

interface EditableTableState {
  dataSource: DataType[]
  count: number
  editingKey: string | null
}

interface ExtraProps extends EditableTableProps {
  targetTableFieldList?: any[]
  sourceTableFieldList?: any[]
  columnTypeList?: any[]
  setTableFieldMap: any
  isCreateTable: boolean
}

type ColumnTypes = Exclude<EditableTableProps['columns'], undefined>

export class EditableTable extends React.Component<
  ExtraProps,
  EditableTableState
> {
  columns: (ColumnTypes[number] & {
    editable?: boolean
    dataIndex: string
    type?: 'select' | 'input'
    targetTableFieldList?: any[]
    sourceTableFieldList?: any[]
    columnTypeList?: any[]
  })[]

  constructor(props: ExtraProps) {
    super(props)

    this.state = {
      dataSource: [
        {
          key: '0',
          sourceFields: null,
          targetFields: null,
        },
      ],
      count: 1,
      editingKey: '',
    }
    //@ts-ignore
    this.columns = this.getTableColumn()
  }

  getTableColumn = () => {
    return [
      {
        title: i18n.t("features:sourceField"),
        dataIndex: 'sourceFields',
        editable: true,
        type: 'select',
        sourceTableFieldList: this.props.sourceTableFieldList,
      },
      {
        title: i18n.t("features:targetField"),
        dataIndex: 'targetFields',
        editable: true,
        type: this.props.isCreateTable ? 'input' : 'select',
        targetTableFieldList: this.props.targetTableFieldList,
      },
      //@ts-ignore
      ...(this.props.isCreateTable ? [
        {
          title: i18n.t("features:type"),
          dataIndex: 'columnTypeName',
          editable: true,
          type: 'select',
          width: 100,
          columnTypeList: this.props.columnTypeList
        },
        {
          title: i18n.t("features:length"),
          dataIndex: 'length',
          editable: true,
          type: 'input',
          width: 100,
        },
        {
          title: i18n.t("features:decimalPoint"),
          dataIndex: 'scale',
          editable: true,
          type: 'input',
          width: 100,
        },
        {
          title:  i18n.t("features:primaryKey"),
          dataIndex: 'primary',
          editable: true,
          type: 'primaryType',
          width: 100,
        },
      ] : []),
      {
        title:  i18n.t("features:operation"),
        dataIndex: 'operation',
        // @ts-ignore
        render: (_, record: { key: React.Key }) =>
          this.state.dataSource.length >= 1 ? (
            <Popconfirm
              title={i18n.t("features:confirmDelete")+"?"}
              onConfirm={() => this.handleDelete(record.key)}
            >
              <Button type="link">{i18n.t("features:delete")}</Button>
            </Popconfirm>
          ) : null,
      },
    ]
  }

  // 返回与最后一个反括号匹配的括号的index，如：999((())) => 3
  findIndex = (str: string) => {
    if (!str) return 0;
    let bracketStack: string[] = [], index = 0;
    for (let i = str.length - 1; i > 0; i--) {
      if (str[i] === ')') {
        bracketStack.push(str[i]);
      } else if (str[i] === '(') {
        bracketStack.pop();
      }
      if (bracketStack.length === 0) {
        index = i;
        break;
      }
    }
    return index;
  }

  matchField = (str: string) => {
    const index = this.findIndex(str);
    const result = str.substring(0, index);
    return result;
  }

  setDefaultDataSource = (sourceFields: any, targetFields: any) => {
    if (!isEmpty(sourceFields)) {
      return sourceFields.map((s: any, index: string) => {
        // 需要渲染文本，否则会导致高度为0，无法触发select 或者 修改input的样式设置高度
        let initItem = {
          key: index,
          sourceFields: s.value,
          targetFields: i18n.t("features:pleaseSelect")
        }

        targetFields && targetFields.forEach((t: any) => {
          if (this.matchField(s.label?.toLowerCase()) === this.matchField(t.label?.toLowerCase())) {
            initItem['targetFields'] = t.value
          }
        })
        // 如果映射失败，按顺序填充对应值
        if (initItem.targetFields === i18n.t("features:pleaseSelect")) {
          initItem['targetFields'] = targetFields[index]?.value
        }
        return initItem
      })
    }
  }

  init = () => {
    const { targetTableFieldList = [], sourceTableFieldList } = this.props;

    const initDataSource: DataType[] = this.setDefaultDataSource(sourceTableFieldList, targetTableFieldList)
    this.setState({ dataSource: initDataSource, count: initDataSource.length + 1 })
    this.updateFieldMap(initDataSource)
  }

  componentDidMount(): void {
    this.init()
  }

  //外部参数发生改变重新渲染
  componentDidUpdate(prevProps: ExtraProps) {
    const { isCreateTable, sourceTableFieldList, targetTableFieldList } = this.props;

    if (
      prevProps.isCreateTable !== isCreateTable ||
      !_.isEqual(prevProps.sourceTableFieldList, sourceTableFieldList) ||
      !_.isEqual(prevProps.targetTableFieldList, targetTableFieldList)
    ) {
      this.init();
      //@ts-ignore
      this.columns = this.getTableColumn();
    }
  }

  updateFieldMap = (newData: any[]) => {
    const dataFormat = newData.map((d) => {
      // const columnTypeIdx = d.targetFields?.lastIndexOf('(')
      const columnTypeIdx = this.findIndex(d.targetFields)
      const sourceColumn = d.sourceFields === null ? null : d.sourceFields.match(/\([\d)]+\)$/)

      return {
        columnName: columnTypeIdx === -1 ? null : d.targetFields?.slice(0, columnTypeIdx),
        //特殊处理
        ...(this.props?.isCreateTable ?
          {
            columnTypeName: d?.columnTypeName,
            length: d?.length,
            scale: d?.scale,
            primary: d?.primary,
          } :
          { columnTypeName: columnTypeIdx === -1 ? null : d.targetFields?.slice(columnTypeIdx + 1, -1), }),

        index: sourceColumn === null ? null : sourceColumn[sourceColumn.length - 1].replace(/[()]/g, ''),
        sourceColumnName: sourceColumn === null ? null : sourceColumn.input,
      }
    })
    this.props.setTableFieldMap(dataFormat)
  }

  handleAdd = () => {
    const { count, dataSource } = this.state
    const newData: DataType = {
      key: count,
      sourceFields: null,
      targetFields: null,
    }
    const newDataSource = [...dataSource, newData]
    this.setState({
      dataSource: newDataSource,
      count: count + 1,
    })
  }

  handleDelete = (key: React.Key) => {
    const dataSource = [...this.state.dataSource]
    const newData = dataSource.filter((item) => item.key !== key)
    this.updateFieldMap(newData)
    this.setState({ dataSource: newData })
  }

  handleSave = (row: DataType, dataIndex: string) => {
    const newData = [...this.state.dataSource]
    const index = newData.findIndex((item) => row.key === item.key)
    const item = newData[index]
    newData.splice(index, 1, {
      ...item,
      ...row,
    })
    this.setState({ dataSource: newData })
    this.updateFieldMap(newData)
  }

  handleEdit = (key: string) => {
    this.setState({ editingKey: key })
  }

  render() {
    const { dataSource } = this.state
    const components = {
      body: {
        row: EditableRow,
        cell: EditableCell,
      },
    }
    const columns = this.columns.map((col) => {
      if (!col.editable) {
        return col
      }
      return {
        ...col,
        onCell: (record: DataType) => ({
          record,
          editable: col.editable,
          type: col.type,
          sourceTableFieldList: this.props.sourceTableFieldList,
          targetTableFieldList: this.props.targetTableFieldList,
          columnTypeList: this.props.columnTypeList,
          dataIndex: col.dataIndex,
          title: col.title,
          editingKey: this.state.editingKey,
          handleSave: this.handleSave,
          handleEdit: this.handleEdit
        }),
      }
    })

    return (
      <div>
        <Button
          onClick={this.handleAdd}
          type="primary"
          style={{ marginBottom: 16 }}
        >
          {i18n.t("features:addMapping")}
        </Button>
        <Table
          components={components}
          rowClassName={() => 'editable-row'}
          bordered
          scroll={{ x: 'max-content', y: 260 }}
          dataSource={dataSource}
          columns={columns as ColumnTypes}
          size="small"
          className={styles.editableTable}
        />
      </div>
    )
  }
}
