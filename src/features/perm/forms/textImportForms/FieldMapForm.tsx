import React, { useEffect, useState, useMemo } from 'react'
import { Form, Checkbox, Spin } from 'antd'
import { FormInstance } from 'antd/lib/form'
import { useSelector, useRequest, useDispatch } from 'src/hook'
import { EditableTable } from './EditableTable'
import { postSourceTableFields, postImportFileMessage, DataSourceType } from 'src/api'
import { setTableFieldMap } from 'src/store/extraSlice/textImportSlice'
import { getColumnTypeOptions } from 'src/util'
import { useTranslation } from 'react-i18next'

export function FieldMapForm({
  form,
  isCreateTable,
  connectionType
}: {
  form?: FormInstance
  isCreateTable: boolean
  targetFields: string
  connectionType: DataSourceType
}) {
  const { t } = useTranslation()
  const { targetTable, sourceTableFieldsRequest, targetTableMessage } =
    useSelector((state) => state.textImport)
  const dispatch = useDispatch()

  //目标字段
  const [targetTableFieldList, setTargetTableFieldList] = useState([])

  // 获取目标字段列表
  const { run: getTargetTableFieldList, loading } =
    useRequest(postSourceTableFields, {
      manual: true,
      formatResult: (data) => {
        return data?.map((d) => {
          return {
            label: `${d.columnName}(${d.columnTypeName})`,
            value: `${d.columnName}(${d.columnTypeName})`,
          }
        })
      },
      onSuccess: (data) => {
        setTargetTableFieldList(data || [])
      },
    })

  useEffect(() => {
    if (!targetTableMessage || isCreateTable || !targetTableMessage?.tableName) return
    //新表 默认使用源字段
    getTargetTableFieldList({ ...targetTableMessage })
  }, [getTargetTableFieldList, targetTableMessage, isCreateTable])

  /** 获取源字段列表 */
  const { data: sourceTableData, run: getSourceTableFieldList } = useRequest(
    postImportFileMessage,
    {
      manual: true,
    },
  )
  useEffect(() => {
    if (!sourceTableFieldsRequest) return
    getSourceTableFieldList({ ...sourceTableFieldsRequest })
  }, [getSourceTableFieldList, sourceTableFieldsRequest])

  const sourceTableList =
    sourceTableData?.fieldNames && sourceTableData?.fieldNames?.length > 0
      ? sourceTableData?.fieldNames
      : sourceTableData?.values[0]

  const sourceTableListMap = new Map(sourceTableList?.map((value, key) => [key, value]))
  let sourceTableListFormat: any[] = []
  sourceTableListMap.forEach((value, key) => {
    sourceTableListFormat.push({ label: `${value}(${key})`, value: `${value}(${key})` })
  })

  const dispatchSetTableFieldMap = (param: any) => {
    dispatch(setTableFieldMap(param))
  }

  const realTargetTableFieldList = useMemo(() => {
    //新建表 table字段为源字段保持一直
    if (isCreateTable) {
      return sourceTableListFormat
    } else {
      return targetTableFieldList
    }
  }, [isCreateTable, JSON.stringify(targetTableFieldList), JSON.stringify(sourceTableListFormat)])

  return (
    <>
      <span>{t("features:targetTable")}：{targetTable}</span>
      <Form form={form} initialValues={{ bulkInsert: true }}>
        <Form.Item label={t("features:importErrorContinue")} valuePropName='checked' name="isContinue" style={{ marginBottom: '0' }}>
          <Checkbox
            onChange={(e) => {
              form?.setFieldsValue({ isContinue: e.target.checked })
            }}
          ></Checkbox>
        </Form.Item>
        {
          targetTableMessage?.dataSourceType === 'Vertica' &&
          <Form.Item label={t("features:quickImport")} valuePropName='checked' name="bulkInsert" style={{ marginBottom: '0' }}>
            <Checkbox
              onChange={(e) => {
                form?.setFieldsValue({ bulkInsert: e.target.checked })
              }}
            ></Checkbox>
          </Form.Item>
        }
      </Form>
      <br />
      {
        loading &&
        <Spin spinning style={{ color: '#03204a' }} tip={t("features:fieldLoading")}>
          <div
            style={{
              width: '100vw',
              height: '100vh',
            }}
          ></div>
        </Spin>
      }
      {!!sourceTableList && !!realTargetTableFieldList && (
        <EditableTable
          key={JSON.stringify(sourceTableListFormat)}
          rowKey="sourceFields"
          isCreateTable={isCreateTable}
          targetTableFieldList={realTargetTableFieldList}
          sourceTableFieldList={sourceTableListFormat}
          columnTypeList={getColumnTypeOptions(connectionType)?.map((i) => ({ label: i, value: i }))}
          setTableFieldMap={dispatchSetTableFieldMap}
        ></EditableTable>
      )}
    </>
  )
}
