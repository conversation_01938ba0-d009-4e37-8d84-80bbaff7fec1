import React, { useEffect, useMemo, useState } from 'react'
import { Form, Select, Row, Col, message, Radio, Upload } from 'antd'
import { FormInstance } from 'antd/lib/form'
import { useDispatch, useRequest, useSelector } from 'src/hook'
import { showModal } from 'src/store/extraSlice/modalVisibleSlice'
import { setFilePath } from 'src/store/extraSlice/textImportSlice'
import { RadioChangeEvent } from 'antd/lib/radio'
import { RcFile } from 'antd/lib/upload'
import { LoadingOutlined, UploadOutlined } from '@ant-design/icons'
import styles from './index.module.scss'
import { CHUNK_SIZE, calculateHash, getFileSuffix, readFileMd5, splitFile, useTimer } from 'src/components/BigFileUpload/utils'
import { deleteFiles, getImportFilePreview, getIncludeDir, uploadFileChunks, validateFileExist } from 'src/api'
import classnames from 'classnames'
import { useTranslation } from 'react-i18next'

export function FilePathForm({ form }: { form?: FormInstance }) {
  const { t } = useTranslation()
  const dispatch = useDispatch()
  const { filePath, fileType } = useSelector((state) => state.textImport)
  const [value, setValue] = useState(1);
  const [fileList, setFileList] = useState<any>([]);
  const [fileSize, setFileSize] = useState(0)
  const timerInstance = useTimer(fileSize)
  const [fileExist, setFileExist] = useState<boolean>(false)
  const [uploadFilePath, setUploadFilePath] = useState<string>('') // 个人空间path: 本地上传的文件，默认存储到 个人文件夹-我的空间 中
  const [uploading, setUploading] = useState<boolean>(false)
  const [filePreview, setFilePreview] = useState<string>() // 文件预览

  // 获取个人空间path接口
  const { data, run: getTreeDir } = useRequest(() => getIncludeDir('/'), {
    manual: true,
    formatResult(data) {
      data?.map((item: any) => {
        if (item?.isHomeUserDir) {
          setUploadFilePath(item?.path)
        }
      })
    }
  })

  // 文件预览
  const { run: getPreViewSqlFile } = useRequest(getImportFilePreview, {
    manual: true,
    onSuccess: (res: any) => {
      setFilePreview(res || '')
    }
  })

  useEffect(() => {
    getTreeDir()
  }, [])

  const onChange = (e: RadioChangeEvent) => {
    setValue(e.target.value)
    form?.setFieldsValue({
      codeType: 'UTF-8'
    })
    dispatch(setFilePath(''))
    setFileExist(false)
  };
  
  // 个人文件夹中选择的文件，构建结构回显Upload中
  useEffect(() => {
    if (filePath) {
      setFileList([{
        uid: new Date().getTime(),
        name: filePath?.split("\\")?.[filePath?.split("\\")?.length-1],
        status: "done",
        type: fileType,
      }])
      form?.setFieldsValue({upload: {fileList: [{
        uid: new Date().getTime(),
        name: filePath?.split("\\")?.[filePath?.split("\\")?.length-1],
        status: "done",
        type: fileType,
      }]}})
    } else {
      form?.setFieldsValue({upload: null})
      setFileList([])
    }
    form?.setFieldsValue({
      importPath: filePath,
    })
  }, [filePath, fileType, form])

  useEffect(() => {
    if (fileList.length > 0) {
      const encodeName = form?.getFieldValue('codeType') || 'UTF-8'
      getPreViewSqlFile({
        fileName: fileList[0]?.name,
        encodeName,
      })
    }
  }, [fileList])

  // 上传分片
  const uploadChunks = async (chunksData: any, hash: string, param: any) => {
    let currentUploadIndex = 0
    timerInstance?.startTimer()
    const uploadChunk = (index: number) => {
      chunksData[index]?.then((d: any) => {
        const {
          listLength,
          md5,
          index: cindex,
          file,
          md5All,
          fileName,
        } = d
        const beforetime = new Date().getTime()
        uploadFileChunks({
          listLength,
          md5,
          index: cindex,
          file,
          md5All,
          fileName,
          path: uploadFilePath,
          chunkSize: String(CHUNK_SIZE),
        }).then((data) => {
          const requesttime = (new Date().getTime() - beforetime) / 1000
          timerInstance?.updateCurrenLostTimeBySpeed(file.size / requesttime)
         
          // 结束分片上传
          if (data && currentUploadIndex === chunksData.length - 1) {
            setUploading(false)
            timerInstance?.stopTimer()
            // 验证文件是否已存在, 获取 path
            Promise.all([validateFileExist(param)]).then((res) => {
              const { uploaded = false, path = '', encode } = res?.[0] || {}
              if (uploaded) {
                dispatch(setFilePath(path))
                form?.setFieldsValue({
                  codeType: encode,
                })
              } else {
                message.error(t("features:fileUploadFailed"))
              }
            }).catch((err) => {
              message.error(t("features:fileUploadFailed"))
              console.error('文件上传失败 err :>> ', err);
            })
            return
          }
          if (data) {
            currentUploadIndex++
            if (currentUploadIndex < chunksData.length)
              uploadChunk(currentUploadIndex)
          }
        }).catch((error) => {
          console.error('文本导入-本地上传，文件上传失败 :>> ', error);
        })
      })
    }
    uploadChunk(currentUploadIndex)
  }

  // 上传文件
  const handleUploadFile = async (file: RcFile, name: string) => {
    setUploading(true)
    const chunkList = splitFile(file)
    const fileName = file.name
    setFileSize(file.size)
    if (!file.name) {
      message.warning(t("features:pleaseSelectFileFirst"))
      return
    }
    // 计算hash
    const hash = await calculateHash(chunkList)
    try {
      // 检查文件是否已存在
      const validateFileExistParam = {
        md5: hash,
        suffix: getFileSuffix(fileName),
        filePath: uploadFilePath,
      }
      const validateFileRes = await validateFileExist(validateFileExistParam)
      const { uploaded, list, path = '', encode } = validateFileRes

      if (uploaded) {
        // 已存在
        setFileExist(true)
        dispatch(setFilePath(path))
        form?.setFieldsValue({
          codeType: encode,
        })
        setUploading(false)
      } else {
        const chunksData = chunkList
          .map(async ({ chunk }, index) => {
            const md5 = await readFileMd5(chunk)
            return {
              listLength: chunkList.length,
              hash: `${hash}-${index}`,
              md5,
              index,
              file: chunk,
              md5All: hash,
              fileName: fileName,
            }
          })
          .filter(async (item2) => {
            return !list ? true : list.indexOf((await item2).md5) === -1
          })
        // 开始上传分片
        uploadChunks(chunksData, hash, validateFileExistParam)
      }
    } catch (error) {
      console.error('文本导入-本地上传，文件验证失败 :>> ', error);
    }
  }


  const beforeUpload = (file: any) => {
    if (!file) {
      message.error(t("features:pleaseSelectFileFirst")); 
      return false;
    }
    const name = file.name?.toString()!
    const suffix = name?.split('.')?.pop()!;

    // 校验是否为选择的文件类型
    if(fileType && suffix !== fileType){
      message.error(t("features:pleaseSelectFileType", {fileType}))
      return false;
    }

    const isTr = ['txt', 'csv', 'xls', 'xlsx', 'json'].includes(suffix)
    if(!isTr){
      message.error(t("features:onlySupportFileType"));
      return false;
    }
    // 文件分片上传
    handleUploadFile(file, name);
    return false; // 阻止自动上传，进行手动上传
  };

  // 删除文件
  const { run: tryDeleteFiles } = useRequest(deleteFiles, {
    manual: true,
    onSuccess: () => {
    },
  })

  const uploadOnRemove = (info: any) => {
    let fileListTmp: any = []
    fileList?.map((item: any) => {
      if (item?.uid !== info?.uid) {
        fileListTmp.push(item)
      } else {
        // 删除文件夹：本地上传的文件，若个人文件夹中原先不存在，则移除文件时从个人文件夹中删除
        if (value === 1 && !fileExist) {
          filePath && tryDeleteFiles([filePath])
        }
        dispatch(setFilePath(''))
      }
    })
    setFileList(fileListTmp)
  }

  const renderUpload = useMemo(() => {
    return (
      <Form.Item
        label={t("features:attachmentUpload")}
        name="upload"
        rules={[{ 
          required: true,
          message: t("features:pleaseUploadAttachment"),
          validator: (_: any, value: any) => {
            if (!value || value?.fileList.length === 0) {
              return Promise.reject(t("features:uploadingAttachment"));
            }
            return Promise.resolve();
          }
        }]}
      >
        { value === 2?
          <Upload
            multiple={false}
            openFileDialogOnClick={false}
            fileList={fileList}
            onRemove={(info: any) => {uploadOnRemove(info)}}
          >
            <div
              className={styles.uploadDiv}
              onClick={() => {dispatch(showModal('ModalOpenServerFile'))}}
            >
              <p><UploadOutlined />{t("features:uploadAttachment")}</p>
            </div>
          </Upload>
          :
          <Upload
            multiple={false}
            fileList={fileList}
            beforeUpload={beforeUpload}
            onRemove={(info: any) => {uploadOnRemove(info)}}
            disabled={uploading}
          >
            <div className={classnames(styles.uploadDiv, uploading && styles.notAllowed)}>
              {
                uploading ?
                  <p><LoadingOutlined />&nbsp;&nbsp;{t("features:uploadingAttachment")}</p>
                  : <p><UploadOutlined />{t("features:uploadAttachment")}</p>
              }
            </div>
          </Upload> 
        }
      </Form.Item>
    )
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [fileList, filePath, value, uploadFilePath, uploading])

  return (
    // 所有字段均为必填项
    <Form form={form} labelAlign="left" labelCol={{ span: 7 }} className={styles.FilePathForm}>
      <Row gutter={4}>
        <Col span={22}>
          <Form.Item
            label={t("features:uploadLocation")}
            rules={[{ required: true }]}
          >
            <Radio.Group
              onChange={(e) => onChange(e)}
              value={value}
              disabled={uploading}
            >
              <Radio value={1}>{t("features:localUpload")}</Radio>
              <Radio value={2}>{t("features:personalFolderUpload")}</Radio>
            </Radio.Group>
          </Form.Item>
          { renderUpload }
        </Col>
      </Row>
      {fileType !== 'xls' && fileType !== 'xlsx' && (
        <>
          <Form.Item
            label={t("features:encoding")}
            name="codeType"
            rules={[{ required: true, message: t("features:pleaseSelectEncoding") }]}
            initialValue='UTF-8'
          >
            {/* GBK, GB2312, ISO-88591, UTF-8, US-ASCII */}
            <Select
              options={codeTypes}
              onChange={(value: string) => {
                if (fileList?.length > 0)
                  getPreViewSqlFile({
                    fileName: fileList[0]?.name,
                    encodeName: value
                  })
              }}
              allowClear={false}
            />
          </Form.Item>
          {
            fileList?.length > 0 && filePreview &&
            <Form.Item label={t("features:item_label.file_preview")} wrapperCol={{ span: 24 }}>
              <div className={styles.previewDiv}>
                {filePreview}
              </div>
            </Form.Item>
          }
        </>
      )}
    </Form>
  )
}

export const codeTypes = [
  'ISO-8859-1',
  "UTF-8",
  "GBK",
  "GB2312",
  "GB18030",
  "UTF-16BE",
  "UTF-16LE",
  "UTF-16",
  "BIG5",
  "UNICODE",
  "US-ASCII"
].map(
  (c) => {
    return {
      label: c,
      value: c,
    }
  },
)
