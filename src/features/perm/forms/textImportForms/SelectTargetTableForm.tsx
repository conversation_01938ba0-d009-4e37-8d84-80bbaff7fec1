import React, { useEffect, useState } from 'react'
import { Form, Checkbox } from 'antd'
import { FormInstance } from 'antd/lib/form'
import { useDispatch, useSelector } from 'src/hook'
import {
  setTargetTable,
  setTargetTableMessage,
} from 'src/store/extraSlice/textImportSlice'
import { getTreeNodeChildren } from 'src/pageTabs/queryPage/sdt/sdtSlice'
import DropdownInput from './DropdownInput'
import { useTranslation } from 'react-i18next'

export function SelectTargetTableForm({ form }: { form?: FormInstance }) {
  const { t } = useTranslation()
  const dispatch = useDispatch()

  //表组及上一层级增加文本导入
  const moreTextInportNodeTypes = ['database', 'schema', 'oracleUser', 'tableGroup']
  const { targetTableList, targetTableMessage } = useSelector(
    (state) => state.textImport,
  )
  const { selectedNode } = useSelector((state) => state.sdt)
  const { connectionType } = selectedNode || {}
  const isMongoDB = connectionType === "MongoDB"

  const node: any = useSelector((state) => state.sdt.rightClickedNode)
  const [options, setOptions] = useState<any[]>([])

  const getLastBeforeStr = (str: string) => {
    const lastIndex = str.lastIndexOf("/");
    const result = str.substring(0, lastIndex);
    return result
  }
  const queryTreeNode = async () => {
    // 构造父级表组的参数
    let params = {
      ...node,
      notMemorize: true, // 标记不缓存数据,为了和分页保持一致,避免数据互相影响
      funcAndProcOriginName: null,
      nodeName: t("features:table"),
      nodePath: getLastBeforeStr(node?.nodePath),
      nodePathWithType: getLastBeforeStr(node?.nodePathWithType),
      nodeType: 'tableGroup',
    }
 
    // 如果是db或schema 参数需要拼接表
    if (moreTextInportNodeTypes.includes(node?.nodeType)){
      if (node?.nodeType === 'tableGroup'){
        params.nodePath =  `${params.nodePath}/${t("features:tables")}`;
      }else  {
        params.nodePath =  `${node.nodePath}/${t("features:tables")}`;
      }
    
      params.nodePathWithType =  `${node.nodePathWithType}`
    }
   
    const res: any = await dispatch(getTreeNodeChildren(params))
    const options = (res?.payload || [])?.map((t: any) => {
      return {
        label: t.nodeName,
        value: t.nodeName,
      }
    })
    setOptions(options)
  }

  useEffect(() => {
    if (targetTableList?.length) {
      const init = targetTableList?.map((t) => {
        return {
          label: t.nodeName,
          value: t.nodeName,
        }
      })
      setOptions(init)
    } else {
      queryTreeNode()
    }
  }, [targetTableList])

  useEffect(() => {
    //  设置默认值
    if (options?.length) {
      const hasMatch = !!options?.filter(i => i?.value === targetTableMessage?.tableName)?.length
      let v = hasMatch ? targetTableMessage?.tableName :options[0].value;
      if (moreTextInportNodeTypes.includes(node?.nodeType)){
        v = null
      }
   
      form?.setFieldsValue({ targetTable: v })
      dispatch(setTargetTable(v))
      
      let nodePath = targetTableMessage?.nodePath
      let nodePathWithType = targetTableMessage?.nodePathWithType
      if (['database', 'schema', 'oracleUser'].includes(node?.nodeType)){
        nodePath =  `${node.nodePath}/${t("features:tables")}`;
        nodePathWithType =  `${node.nodePathWithType}/TABLE:${v}`
      }
    
      const name = !isMongoDB ? t("features:tables") : t("features:collections")
      const index = nodePath?.indexOf(name)
      if (nodePath?.split('/')[-1] !== name) {
        if (!index) return
        nodePath = nodePath?.slice(0, index + name?.length || 1) + '/' + v
      }
      dispatch(
        setTargetTableMessage(
          Object.assign(
            {},
            targetTableMessage,
            { nodePath: nodePath },
            { nodePathWithType: nodePathWithType },
            { tableName: v },
          ),
        ),
      )
    }
  }, [options?.length])

  const targetValidator = (_: any, value: string) => {
    if(!form?.getFieldValue('createTable') && !value){
      return Promise.reject(new Error(t("features:pleaseSelectTargetTable")))
    }
    return Promise.resolve()
  }

  return (
    // 必填项
    <Form form={form} labelCol={{ span: 8 }} labelAlign="left">
      <Form.Item
        label={t("features:pleaseSelectTargetTable")}
        name="targetTable"
        rules={[{ required: !form?.getFieldValue('createTable'), validator: targetValidator }]}
      >
        <DropdownInput
          options={[{ label: '', value: '' }].concat(options)}
          onChange={(v: string) => {
            dispatch(setTargetTable(v))
            let nodePath = targetTableMessage?.nodePath;
            let nodePathWithType = targetTableMessage?.nodePathWithType;

            //更新nodePathWithType
            if (nodePathWithType?.includes('/TABLE:')) {
              nodePathWithType = nodePathWithType?.replace(/TABLE:[^/]+/, `TABLE:${v}`)
            }else {
              nodePathWithType = `${targetTableMessage?.nodePathWithType}/TABLE:${v}`
            }
           
            const index = nodePath?.indexOf(t('common.dictSearch.nodeName.table'))
            if (nodePath?.split('/')[-1] !== t('common.dictSearch.nodeName.table')) {
              if (!index) return
              nodePath = nodePath?.slice(0, index + t('common.dictSearch.nodeName.table')?.length || 1) + '/' + v
            }
            dispatch(
              setTargetTableMessage(
                Object.assign(
                  {},
                  targetTableMessage,
                  { nodePath: nodePath },
                  { nodePathWithType: nodePathWithType },
                  { tableName: v },
                ),
              ),
            )
          }}
        />
      </Form.Item>
      <Form.Item
        noStyle
        dependencies={['targetTable']}
      >
        {({ getFieldValue }) => {
          const targetTable = getFieldValue('targetTable');
          const extstingTable = options.some(option => option?.value?.toLowerCase() === targetTable?.toLowerCase()); // 判断是否在options中
          form?.setFieldsValue({createTable: !extstingTable})
          return (
            <Form.Item name='createTable' label={t("features:newTable")} >
              <Checkbox checked={!extstingTable} disabled={true}/>
            </Form.Item>
          );
        }}
      </Form.Item>
    </Form>
  )
}