import React, { useEffect } from 'react'
import { Form, Select } from 'antd'
import { FormInstance } from 'antd/lib/form'
import { getSeparators } from 'src/api'
import { useRequest, useSelector } from 'src/hook'
import { useTranslation } from 'react-i18next'

export function SeparateSettingForm({ form }: { form?: FormInstance }) {
  const { t } = useTranslation();
  const { fileType } = useSelector((state) => state.textImport);

  const { data, run } = useRequest(getSeparators, {
    manual: true,
  })
  useEffect(() => {
    run()
  }, [run])
  
  useEffect(() => {
    if (fileType === 'csv') {
      form?.setFieldsValue({ fieldSeparator: ',' });
    } else if (fileType === 'txt') {
      form?.setFieldsValue({ fieldSeparator: '\t' });
    }
    // jt说需要设置默认值 "
    form?.setFieldsValue({ fieldRecognize: '\"' })
  },[fileType, form])

  return (
    <Form form={form} labelCol={{ span: 8 }} labelAlign="left">
      <Form.Item
        label={t("features:recordSeparator")}
        name="rowSeparator"
        rules={[{ required: true, message: t("features:pleaseSelectRecordSeparator") }]}
        initialValue={'\r\n'}
      >
        <Select>
          {data?.rowSeparator.map((r: any) => {
            return (
              <Select.Option key={r} value={r}>
                {r.replace(/\r/, '\\r').replace(/\n/, '\\n')}
              </Select.Option>
            )
          })}
        </Select>
      </Form.Item>
      <Form.Item
        label={t("features:fieldSeparator")}
        name="fieldSeparator"
        rules={[{ required: true, message: t("features:pleaseSelectFieldSeparator") }]}
        initialValue=","
      >
        <Select>
          {data?.fieldSeparator.map((f: any) => {
            return (
              <Select.Option key={f} value={f}>{f.replace(/\t/, '\\t')}</Select.Option>
            )
          })}
        </Select>
      </Form.Item>
      {/* 识别符可为空，其他为必填项 */}
      <Form.Item label={t("features:fieldIdentifier")} name="fieldRecognize">
        <Select allowClear>
          {data?.fieldRecognize.map((f: any) => {
            return (
              // eslint-disable-next-line no-useless-escape
              <Select.Option key={f} value={f}>{f.replace(/\"/, '\\"')}</Select.Option>
            )
          })}
        </Select>
      </Form.Item>
    </Form>
  )
}
