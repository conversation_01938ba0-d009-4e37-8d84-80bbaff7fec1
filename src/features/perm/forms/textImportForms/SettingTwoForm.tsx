import React, { useEffect } from 'react'
import { Form, Input, Select } from 'antd'
import { FormInstance } from 'antd/lib/form'
import { useSelector } from 'src/hook'
import { useTranslation } from 'react-i18next'

export function SettingTwoForm({ form }: { form?: FormInstance }) {
  const { t } = useTranslation()
  const { fileType } = useSelector((state) => state.textImport);

  useEffect(() => {
   
      form?.setFieldsValue({dateSort: 'YMD', dateSeparator: '-'});
  },[fileType, form])

  function posIntValidator(_:any, value: number) {
    const posIntReg = /(^[0-9]\d*$)/
    if (!value) {
      return Promise.reject(t("features:cannotBeEmpty"))
    }
    if (!posIntReg.test(String(value))) {
      return Promise.reject(t("features:onlyPositiveInteger"))
    }
    return Promise.resolve()
  }
  return (
    <Form form={form} labelCol={{ span: 8 }} labelAlign="left">
      {
        fileType !== 'json' &&
        <Form.Item
          label={t("features:fieldNameRow")}
          name="fieldRow"
          initialValue={1}
          required
          rules={[{ validator: posIntValidator }]}
        >
          <Input min={1} type="number"></Input>
        </Form.Item>
      }
      <Form.Item
        label={t("features:firstDataRow")}
        name="firstRow"
        initialValue={fileType === 'json'? 1 : 2}
        required
        rules={[{ validator: posIntValidator }]}
      >
        <Input min={1} type="number"></Input>
      </Form.Item>
      <Form.Item
        label={t("features:lastDataRow")}
        name="lastRow"
        rules={[{ validator: (_:any, value: number) => {
          const posIntReg = /(^[0-9]\d*$)/
          if (value && !posIntReg.test(String(value))) {
            return Promise.reject(t("features:onlyPositiveInteger"))
          }
          return Promise.resolve()
        } }]}
      >
        <Input min={1} type="number"></Input>
      </Form.Item>
      {/* 以下全是必填项 */}
      <Form.Item
        label={t("features:dateSort")}
        name="dateSort"
        rules={[{ required: true, message: t("features:pleaseSelectDateSort") }]}
        initialValue="YMD"
      >
        {/* MDY, DMY, YMD */}
        <Select options={dateTypeOpitons}></Select>
      </Form.Item>
      <Form.Item
        label={t("features:dateSeparator")}
        name="dateSeparator"
        initialValue="-"
        rules={[{ required: true, message: t("features:cannotBeEmpty") }]}
      >
        <Input></Input>
      </Form.Item>
      <Form.Item
        label={t("features:timeSeparator")}
        name="timeSeparator"
        initialValue=":"
        rules={[{ required: true, message: t("features:cannotBeEmpty") }]}
      >
        <Input></Input>
      </Form.Item>
      <Form.Item
        label={t("features:decimalPointSymbol")}
        name="decimalDot"
        initialValue="."
        rules={[{ required: true, message: t("features:cannotBeEmpty") }]}
      >
        <Input></Input>
      </Form.Item>
      <Form.Item
        label={t("features:dateTimeSort")}
        name="dateTimeSort"
        rules={[{ required: true, message: t("features:pleaseSelectDateTimeSort") }]}
        initialValue="DATE TIME"
      >
        {/* date time, date time zone  */}
        <Select>
          <Select.Option value={'DATE TIME'}>{t("features:dateTime")}</Select.Option>
          <Select.Option value={'DATE TIME ZONE'}>{t("features:dateTimeZone")}</Select.Option>
          <Select.Option value={'DATE'}>{t("features:date")}</Select.Option>
          <Select.Option value={'TIME'}>{t("features:time")}</Select.Option>
        </Select>
      </Form.Item>
      {fileType !== 'xls' && fileType !== 'xlsx' && (
        <Form.Item
          label={t("features:binaryDataEncoding")}
          name="binaryCode"
          rules={[{ required: true, message: t("features:pleaseSelectBinaryDataEncoding") }]}
          initialValue="HEX"
        >
          {/* base64, hex */}
          <Select options={binaryCodeTypes}></Select>
        </Form.Item>
      )}
    </Form>
  )
}

const dateTypeOpitons = ['MDY', 'DMY', 'YMD'].map((d) => ({
  label: d,
  value: d,
}))

const binaryCodeTypes = ['BASE64', 'HEX'].map((b) => ({
  label: b,
  value: b,
}))
