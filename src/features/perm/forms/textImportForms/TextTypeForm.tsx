import React from 'react'
import { Form, Radio, Space } from 'antd'
import { FormInstance } from 'antd/lib/form'
import { useDispatch, useSelector } from 'src/hook'
import { setFilePath, setFileType } from 'src/store/extraSlice/textImportSlice'
import { useTranslation } from 'react-i18next'
export function TextTypeForm({ form }: { form?: FormInstance }) {
  const { t } = useTranslation()
  const { fileType } = useSelector((state) => state.textImport)
  const { selectedNode } = useSelector((state) => state.sdt)
  const { connectionType } =selectedNode || {}
  const isMongoDB = connectionType === "MongoDB"

  const dispatch = useDispatch()

  if (isMongoDB) {
    dispatch(setFileType("json"))
  }

  return (
    <Form form={form} labelCol={{ span: 5 }} labelAlign="left">
      <Form.Item label={t("features:importType")}></Form.Item>
      <Form.Item label={t("features:textFile")} initialValue={!isMongoDB? fileType : "json"} name="textType">
        <Radio.Group
          onChange={(e) => {
            dispatch(setFilePath(''))
            dispatch(setFileType(e.target.value))
          }}
        >
          <Space direction="vertical">
            {
              !isMongoDB && 
              <>
                <Radio value="txt">{t("features:textFile")}(*.txt)</Radio>
                <Radio value="csv">{t("features:csvFile")}(*.csv)</Radio>
                <Radio value="xlsx">{t("features:excelFile")}(*.xlsx)</Radio>
              </>
            }
            <Radio value="json">{t("features:jsonFile")}(*.json)</Radio>
          </Space>
        </Radio.Group>
      </Form.Item>
    </Form>
  )
}
