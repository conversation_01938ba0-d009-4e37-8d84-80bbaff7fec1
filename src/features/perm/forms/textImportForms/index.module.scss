.editableTable {
    :global {
        .ant-table-cell {
            .editable-cell-select {
                margin: 0;
            }
            .editable-cell-input {
                margin: 0;
                max-width: 100;
                overflow: hidden;
            }
            .editable-cell-value-wrap {
                height: 20px;
                white-space: normal;
            }
        }
       
    }
}
.FilePathForm {
  :global {
    .ant-upload {
      display: contents;
    }
  }
  .uploadDiv {
    border: 1px dashed #d9d9d9;
    padding-bottom: 6px;
    padding-top: 6px;
    background-color: #fafafa;
    color: #6588fd;
    text-align: center;
    cursor: pointer;
    p {
      display: contents;
    }
  }
  .notAllowed {
    color: #999;
    cursor: not-allowed;
  }

  .previewDiv {
    white-space: pre;
    width: 100%;
    max-height: 250px;
    overflow: auto;
  }
}

.dropdownInput {
  position: relative;
  :global {
    .ant-input-group-addon {
      width: 35px;
      padding: 0 0;
    }
  }
  .inputIcon {
   
    border: none;
    height: 30px;
    background-color: #fafafa;
    span {
      font-size: 12px;
    }
  }
  .dropdownOptions {
    position: absolute;
    top: 100%;
    left: 0;
    width: 100%;
    padding: 8px 4px 0;
    background-color: #fff;
    z-index: 1000;
    max-height: 200px;
    overflow-y: auto;
    box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05);
    :global {
      .ant-menu .ant-menu-item {
        height: 38px;
        line-height: 38px;
      }
      .ant-menu-item:not(:last-child) {
        margin-bottom: 0;
      }
    }
  }
}