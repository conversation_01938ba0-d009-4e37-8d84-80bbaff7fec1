import React, { useRef } from 'react'
import { useSelector, useDispatch } from 'src/hook'
import { UIModal } from 'src/components'
import { ConnAddRoleWizard } from './ConnAddRoleWizard'
import { hideModal } from 'src/store/extraSlice/modalVisibleSlice'
import { useTranslation } from 'react-i18next'

export const ModalConnAddRoleWizard = () => {
  const { t } = useTranslation()
  const dispatch = useDispatch()
  const visible = useSelector((state) => state.modal.ModalConnAddRoleWizard?.visible || false)
  const submitterRef = useRef<HTMLDivElement>(null)

  return (
    <UIModal
      title={t('features:newRole')}
      visible={visible}
      footer={<div ref={submitterRef}></div>}
      onCancel={() => dispatch(hideModal('ModalConnAddRoleWizard'))}
      width={800}
    >
      <ConnAddRoleWizard submitterRef={submitterRef} />
    </UIModal>
  )
}
