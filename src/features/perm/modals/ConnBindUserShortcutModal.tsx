import React, { useContext, useEffect } from 'react'
import { useDispatch, useRequest, useSelector } from 'src/hook'
import { UIModal } from 'src/components'
import { hideModal } from 'src/store/extraSlice/modalVisibleSlice'
import { Form, message, Select } from 'antd'
import { ConnectionContext } from '../contexts/ConnectionContext'
import { addConnectionUsers, getInitPermSetBoundUsers } from 'src/api'
import { useTranslation } from 'react-i18next'

export const ModalConnBindUserShortcut: React.FC<{
  refreshRoles?: () => void
}> = React.memo(({ refreshRoles }) => {
  const { t } = useTranslation()
  const dispatch = useDispatch()
  const visible = useSelector((state) => state.modal.ModalConnBindUserShortcut?.visible || false)
  const { connection } = useContext(ConnectionContext)

  const [form] = Form.useForm<{ users?: string[] }>()

  const { data, loading, run: runFetch } = useRequest(
    getInitPermSetBoundUsers,
    { manual: true },
  )

  const formattedOptions = data?.allUser?.map(({ userId, userName }) => {
    const isBound = data?.users?.includes(userId)
    return {
      label: isBound ? `${userName}(${userId})（${t("features:added")}）` : `${userName}(${userId})`,
      title: userName,
      value: userId,
      disabled: isBound,
    }
  })

  const { loading: confirmLoading, run: runAdd } = useRequest(
    addConnectionUsers,
    {
      manual: true,
      onSuccess: () => {
        message.success(t("features:addSuccess"))
        refreshRoles && refreshRoles()
        dispatch(hideModal('ModalConnBindUserShortcut'))
      },
    },
  )

  useEffect(() => {
    if (!visible || !connection) return
    runFetch(connection.connectionId)
  }, [connection, runFetch, visible])

  return (
    <UIModal
      title={connection?.connectionName}
      visible={visible}
      onCancel={() => dispatch(hideModal('ModalConnBindUserShortcut'))}
      afterClose={() => form.resetFields()}
      onOk={() => form.submit()}
      confirmLoading={confirmLoading}
      width={480}
    >
      <Form
        name="bind-user-shortcut"
        form={form}
        onFinish={({ users }) => {
          if (!data?.roleName || !connection?.connectionId) return
          runAdd({
            grantUser: users,
            roleName: data.roleName,
            connectionId: connection?.connectionId,
          })
        }}
      >
        <Form.Item
          label={t("features:addUser")}
          name="users"
          required
          rules={[
            {
              validator: (_rule, value?: string[]) => {
                if (value?.length) {
                  return Promise.resolve()
                }
                return Promise.reject(t("features:pleaseSelectAtLeastOneUser"))
              },
            },
          ]}
        >
          <Select
            loading={loading}
            options={formattedOptions}
            mode="multiple"
            placeholder={t("features:pleaseSelectUserToAdd")}
            optionFilterProp="label"
            maxTagCount={4}
            maxTagTextLength={10}
            showSearch
            allowClear
          />
        </Form.Item>
      </Form>
    </UIModal>
  )
})
