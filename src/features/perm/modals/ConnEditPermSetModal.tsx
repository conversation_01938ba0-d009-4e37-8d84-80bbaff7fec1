import React, { useContext, useEffect } from 'react'
import { useDispatch, useRequest, useSelector } from 'src/hook'
import {
  addPermSet,
  editPermSet,
  getPermsByConnectionId,
  getPermTypes,
  PermSetEntity,
} from 'src/api'
import { TableTransfer, UIModal } from 'src/components'
import { hideModal } from 'src/store/extraSlice/modalVisibleSlice'
import { Form, Input, message } from 'antd'
import { ConnectionContext } from '../contexts/ConnectionContext'
import { nameValidator } from 'src/util/nameValidator'
import { PERM_TYPES_WITHOUT_PERMSET, FormLayout } from 'src/constants'
import { GetPermissionTransferTableColumns } from '../columns'
import { useTranslation } from 'react-i18next'

export const ModalConnEditPermSet: React.FC<{
  mode: 'add' | 'edit'
  record?: PermSetEntity
  refresh?: () => void
}> = ({ mode, record, refresh }) => {
  const { t } = useTranslation()
  const dispatch = useDispatch()
  const visible = useSelector((state) => state.modal.ModalConnEditPermSet?.visible || false)
  const isEditing = mode === 'edit'

  const { connection } = useContext(ConnectionContext)
  const connectionId = connection?.connectionId

  const [form] = Form.useForm<{
    name: string
    description?: string
    childPermission: string[]
  }>()

  const { data: perms, loading: permsLoading, run: fetchPerms } = useRequest(
    getPermsByConnectionId,
    {
      manual: true,
      formatResult: (data) =>
        data.map((perm) => ({
          ...perm,
          key: perm.permissionId.toString(),
        })),
    },
  )

  const { loading: addLoading, run: runAdd } = useRequest(addPermSet, {
    manual: true,
    onSuccess: () => {
      message.success(t("features:addSuccess"))
      dispatch(hideModal('ModalConnEditPermSet'))
      refresh && refresh()
    },
  })

  const { loading: editLoading, run: runEdit } = useRequest(editPermSet, {
    manual: true,
    onSuccess: () => {
      message.success(t("features:editSuccess"))
      dispatch(hideModal('ModalConnEditPermSet'))
      refresh && refresh()
    },
  })

  const { data: permTypes } = useRequest(getPermTypes, {
    cacheKey: PERM_TYPES_WITHOUT_PERMSET,
    formatResult: (permTypes) =>
      permTypes.filter(({ logo }) => logo !== 'permissionSet'),
  })

  const filteredPerms = perms?.filter(({ permissionType }) =>
    permTypes?.map(({ logo }) => logo).includes(permissionType),
  )

  useEffect(() => {
    if (visible && connectionId) {
      fetchPerms(connectionId)
    }
  }, [connectionId, fetchPerms, visible])

  useEffect(() => {
    if (visible && record && mode === 'edit') {
      const { name, description, childPerms } = record
      const childPermission = childPerms?.map(
        ({ permissionId }) => permissionId,
      )
      form.setFieldsValue({ name, description, childPermission })
    }
  }, [form, mode, record, visible])

  const columns = GetPermissionTransferTableColumns(permTypes)

  return (
    <UIModal
      title={mode === 'add' ? t("fetaures:newPermissionSet") : t("features:editPermissionSet")}
      visible={visible}
      onCancel={() => dispatch(hideModal('ModalConnEditPermSet'))}
      afterClose={() => form.resetFields()}
      onOk={() => form.submit()}
      confirmLoading={addLoading || editLoading}
      width={800}
    >
      <Form
        form={form}
        name="connEditPermSet"
        onFinish={({ name, description, childPermission }) => {
          if (mode === 'add') {
            return runAdd({ name, description, childPermission })
          }
          if (mode === 'edit' && record) {
            const { permissionId, permissionType } = record
            return runEdit({
              name,
              description,
              childPermission,
              permissionId,
              permissionType,
            })
          }
        }}
        {...FormLayout}
      >
        <Form.Item
          name="name"
          label={t("features:name")}
          {...(!isEditing && {
            rules: [
              { required: true, message: t("features:pleaseInputPermissionSetName") },
              { validator: nameValidator },
            ],
          })}
        >
          <Input readOnly={isEditing} bordered={!isEditing} maxLength={50} />
        </Form.Item>
        <Form.Item name="description" label={t("features:description")}>
          <Input.TextArea
            autoSize={{ minRows: 2, maxRows: 2 }}
            maxLength={200}
            disabled={record?.initPermSet}
          />
        </Form.Item>

        <Form.Item
          name="childPermission"
          valuePropName="targetKeys"
          rules={[{ required: true, message: t("features:pleaseSelectSubPermission") }]}
          wrapperCol={{ span: 24 }}
        >
          <TableTransfer
            titles={['', t("features:subPermission")]}
            dataSource={filteredPerms}
            tableLoading={{ left: permsLoading }}
            columns={columns}
            rowKey={(record) => record.permissionId}
            filterOption={(inputValue, record) => {
              const { name, permissionType } = record
              return (
                (name || '')?.indexOf(inputValue) !== -1 ||
                (permissionType || '')?.indexOf(inputValue) !== -1
              )
            }}
            showSearch
          />
        </Form.Item>
      </Form>
    </UIModal>
  )
}
