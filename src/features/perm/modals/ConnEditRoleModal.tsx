import React, { useContext, useEffect } from 'react'
import { useSelector, useDispatch, useRequest } from 'src/hook'
import { Form, message, Spin } from 'antd'
import { UIModal } from 'src/components'
import { hideModal } from 'src/store/extraSlice/modalVisibleSlice'
import { EditRoleForm, RoleInfoFormValues } from '../forms/EditRoleForm'
import { RoleRecordContext } from '../contexts/RoleRecordContext'
import {editRole_dataSource, getConnectionRoleInfo, getRolesInheritance} from 'src/api'
import { ConnectionContext } from '../contexts/ConnectionContext'
import { useTranslation } from 'react-i18next'

export const ModalConnEditRole = () => {
  const { t } = useTranslation()
  const dispatch = useDispatch()
  const visible = useSelector((state) => state.modal.ModalConnEditRole?.visible || false)
  const { mode, record, refreshRoles } = useContext(RoleRecordContext)
  const { connection } = useContext(ConnectionContext)
  const [form] = Form.useForm()

  const {
    data: roleInfo,
    loading: loadingRoleInfo,
    run: runFetch,
  } = useRequest(getConnectionRoleInfo, {
    manual: true,
  })

  const { loading: confirmLoading, run: runEdit } = useRequest(editRole_dataSource, {
    manual: true,
    onSuccess: () => {
      message.success(t("features:modifySuccess"))
      dispatch(hideModal('ModalConnEditRole'))
      refreshRoles && refreshRoles()
    },
  })

  const { data: roleOptions, run: fetchRoleOptions } = useRequest(
    (connectionId) =>
      getRolesInheritance({ connectionId, condition: roleInfo?.name }),
    {
      manual: true,
      formatResult: (roles) => {
        return roles.map(({ roleName }) => ({
          label: roleName,
          value: roleName,
        }))
      },
    },
  )

  useEffect(() => {
    if (visible && connection && roleInfo) {
      fetchRoleOptions(connection.connectionId)
    }
  }, [connection, fetchRoleOptions, roleInfo, visible])

  useEffect(() => {
    if (visible && record && mode === 'edit') {
      runFetch(record.roleId)
    }
  }, [mode, record, visible, runFetch])

  return (
    <UIModal
      title={t("features:editRole")}
      visible={visible}
      onCancel={() => dispatch(hideModal('ModalConnEditRole'))}
      onOk={() => {
        if (!roleInfo) return
        form.validateFields().then((values) => {
          const {
            name,
            description,
            duration,
            period,
          } = values as RoleInfoFormValues
          const [beginDate, endDate] =
            duration?.map((date) => date?.format('YYYYMMDD')) || []
          const dayMask = period?.join('')

          /** beginDate endDate dayMask
           *  传 undefined 表示不对时间限制做修改, 传 null 表示取消对应时间限制
           */
          runEdit({
            id: roleInfo.id,
            type: roleInfo.type,
            connectionId: roleInfo.connectionId,
            name,
            description,
            beginDate: beginDate || null,
            endDate: endDate || null,
            dayMask: dayMask || null,
          })
        })
      }}
      confirmLoading={confirmLoading}
      okButtonProps={{ disabled: loadingRoleInfo }}
    >
      <Spin spinning={loadingRoleInfo}>
        <EditRoleForm
          form={form}
          mode={mode}
          record={roleInfo}
          roleOptions={roleOptions}
        />
      </Spin>
    </UIModal>
  )
}
