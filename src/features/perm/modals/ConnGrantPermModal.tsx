import React, { useContext } from 'react'
import { useDispatch, useRequest, useSelector } from 'src/hook'
import { Form, message } from 'antd'
import { UIModal } from 'src/components'
import { ConnGrantPermForm } from '../forms/ConnGrantPermForm'
import { hideModal } from 'src/store/extraSlice/modalVisibleSlice'
import { RoleRecordContext } from '../contexts/RoleRecordContext'
import { getPermTypes, grantPermsToRole_dataSource } from 'src/api'
import difference from 'lodash/difference'
import { ConnectionContext } from '../contexts/ConnectionContext'
import { PERM_TYPES } from 'src/constants'
import { useTranslation } from 'react-i18next'

export const ModalConnGrantPerm = () => {
  const { t } = useTranslation()
  const dispatch = useDispatch()
  const visible = useSelector((state) => state.modal.ModalConnGrantPerm?.visible || false)
  const { mode, record, refreshRoles } = useContext(RoleRecordContext)
  const { connection } = useContext(ConnectionContext)
  const [form] = Form.useForm()

  const currentPermIds =
    record?.currentRolePerms.map(({ permissionId }) => permissionId) || []

  const { loading, run: grantPerms } = useRequest(grantPermsToRole_dataSource, {
    manual: true,
    onSuccess: () => {
      message.success(t("features:authorizationSuccess"))
      dispatch(hideModal('ModalConnGrantPerm'))
      refreshRoles && refreshRoles()
    },
  })

  const { data: permTypes } = useRequest(getPermTypes, {
    cacheKey: PERM_TYPES,
  })

  return (
    <UIModal
      visible={visible}
      title={t("features:grantPermission")}
      onCancel={() => dispatch(hideModal('ModalConnGrantPerm'))}
      onOk={async () => {
        if (!record) return
        try {
          const formValues = await form.validateFields()
          const grantedPerms = (formValues.grantedPerms as (string | number)[]) || []
          const addList = difference(grantedPerms, currentPermIds)
          const removeList = difference(currentPermIds, grantedPerms)

          grantPerms({
            grantPerms: addList,
            removePerms: removeList,
            roleName: record.roleName,
            roleId: record.roleId,
          })
        } catch {}
      }}
      confirmLoading={loading}
      width={800}
    >
      {record && (
        <div style={{ marginBottom: 16 }}>
          <strong>{t("features:roleAlias")}：</strong>
          {record.roleName}
        </div>
      )}
      <ConnGrantPermForm
        mode={mode}
        record={record}
        form={form}
        permTypes={permTypes}
        connectionId={connection?.connectionId}
      />
    </UIModal>
  )
}
