import React, { useEffect } from 'react'
import { useSelector, useDispatch, useRequest } from 'src/hook'
import { getPermSetChildren, PermSetChildEntity, PermSetEntity } from 'src/api'
import { UIModal } from 'src/components'
import { hideModal } from 'src/store/extraSlice/modalVisibleSlice'
import { Col, Row, Table, Tag } from 'antd'
import { useTranslation } from 'react-i18next'

export const ModalConnPermSetChildren: React.FC<{
  record?: PermSetEntity
}> = React.memo(({ record }) => {
  const { t } = useTranslation()
  const dispatch = useDispatch()
  const visible = useSelector((state) => state.modal.ModalConnPermSetChildren?.visible || false)

  const { data, loading, run } = useRequest(getPermSetChildren, {
    manual: true,
  })

  useEffect(() => {
    if (record?.permissionId && visible) {
      run(record.permissionId)
    }
  }, [record, run, visible])

  return (
    <UIModal
      title={record?.name}
      visible={visible}
      onCancel={() => dispatch(hideModal('ModalConnPermSetChildren'))}
      cancelText={t("features:close")}
      okButtonProps={{ hidden: true }}
    >
      <Table<PermSetChildEntity>
        columns={[
          { dataIndex: 'permissionName', title: t("features:permissionAlias"), ellipsis: true },
          { dataIndex: 'permissionType', title: t("features:permissionType"), ellipsis: true },
          {
            dataIndex: 'permissionPath',
            title: t("features:permissionObject"),
            ellipsis: true,
            render: (_, record) => {
              const { permissionPath } = record
              const isAll = permissionPath
                ?.split('/')
                .every((el) => el === t("features:all"))
              return isAll ? t("features:all") : permissionPath
            },
          },
          {
            dataIndex: 'option',
            title: t("features:operation"),
            width: 256,
            render: (_, record) => {
              const { option } = record
              return (
                <Row gutter={[0, 8]}>
                  {option?.split('&').map((el) => (
                    <Col key={el}>
                      <Tag>{el}</Tag>
                    </Col>
                  ))}
                </Row>
              )
            },
          },
        ]}
        dataSource={data}
        loading={loading}
        size="small"
        pagination={{ hideOnSinglePage: true }}
        // ! 当前 dataSource 没有主键, 因为权限没有 id, 先用 permissionName 作 key
        // ! 后续如果加上了权限 id, 权限可以同名了, rowKey 也要改成 id
        rowKey={(record) => record.permissionName}
      />
    </UIModal>
  )
})
