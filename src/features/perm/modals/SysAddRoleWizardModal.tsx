import React, { useRef } from 'react'
import { useSelector, useDispatch } from 'src/hook'
import { UIModal } from 'src/components'
import { SysAddRoleWizard } from './SysAddRoleWizard'
import { hideModal } from 'src/store/extraSlice/modalVisibleSlice'
import { useTranslation } from 'react-i18next'

export const ModalSysAddRoleWizard = () => {
  const { t } = useTranslation()
  const dispatch = useDispatch()
  const visible = useSelector((state) => state.modal.ModalSysAddRoleWizard?.visible || false)
  const submitterRef = useRef<HTMLDivElement>(null)

  return (
    <UIModal
      title={t("features:newRole")}
      visible={visible}
      footer={<div ref={submitterRef}></div>}
      onCancel={() => dispatch(hideModal('ModalSysAddRoleWizard'))}
      width={800}
    >
      <SysAddRoleWizard submitterRef={submitterRef} />
    </UIModal>
  )
}
