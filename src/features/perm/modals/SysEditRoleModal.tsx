import React, { useContext, useEffect } from 'react'
import { useSelector, useDispatch, useRequest } from 'src/hook'
import { Form, message, Spin } from 'antd'
import { UIModal } from 'src/components'
import { hideModal } from 'src/store/extraSlice/modalVisibleSlice'
import { EditRoleForm, RoleInfoFormValues } from '../forms/EditRoleForm'
import { RoleRecordContext } from '../contexts/RoleRecordContext'
import { editRole, getRoleInfo } from 'src/api'
import { useTranslation } from 'react-i18next'

export const ModalSysEditRole = () => {
  const { t } = useTranslation()
  const dispatch = useDispatch()
  const visible = useSelector((state) => state.modal.ModalSysEditRole?.visible || false)
  const { mode, record, refreshRoles } = useContext(RoleRecordContext)
  const [form] = Form.useForm()

  const {
    data: roleInfo,
    loading: loadingRoleInfo,
    run: runFetch,
  } = useRequest(getRoleInfo, {
    manual: true,
  })

  const { loading: confirmLoading, run: runEdit } = useRequest(editRole, {
    manual: true,
    onSuccess: () => {
      message.success(t("features:modifySuccess"))
      dispatch(hideModal('ModalSysEditRole'))
      refreshRoles && refreshRoles()
    },
  })

  useEffect(() => {
    if (!visible) return
    if (mode === 'edit' && record) {
      runFetch(record.roleId)
    }
  }, [mode, record, visible, runFetch])

  return (
    <UIModal
      title={t("features:editRole")}
      visible={visible}
      onCancel={() => dispatch(hideModal('ModalSysEditRole'))}
      onOk={() => {
        if (!roleInfo) return
        form.validateFields().then((values) => {
          const {
            name,
            description,
            duration,
            period,
          } = values as RoleInfoFormValues
          const [beginDate, endDate] =
            duration?.map((date) => date?.format('YYYYMMDD')) || []
          const dayMask = period?.join('')

          /** beginDate endDate dayMask
           *  传 undefined 表示不对时间限制做修改, 传 null 表示取消对应时间限制
           */
          runEdit({
            id: roleInfo.id,
            type: roleInfo.type,
            connectionId: roleInfo.connectionId,
            name,
            description,
            beginDate: beginDate || null,
            endDate: endDate || null,
            dayMask: dayMask || null,
          })
        })
      }}
      afterClose={() => form.resetFields()}
      confirmLoading={confirmLoading}
      okButtonProps={{ disabled: loadingRoleInfo }}
    >
      <Spin spinning={loadingRoleInfo}>
        <EditRoleForm
          form={form}
          mode={mode}
          record={roleInfo}
        />
      </Spin>
    </UIModal>
  )
}
