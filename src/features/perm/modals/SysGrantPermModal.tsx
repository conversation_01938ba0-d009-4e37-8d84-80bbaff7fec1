import React, { useContext } from 'react'
import { useDispatch, useRequest, useSelector } from 'src/hook'
import { Form, message } from 'antd'
import { UIModal } from 'src/components'
import { SysGrantPermForm } from '../forms/SysGrantPermForm'
import { hideModal } from 'src/store/extraSlice/modalVisibleSlice'
import { RoleRecordContext } from '../contexts/RoleRecordContext'
import { grantPermsToRole } from 'src/api'
import difference from 'lodash/difference'
import { useTranslation } from 'react-i18next'  

export const ModalSysGrantPerm = () => {
  const { t } = useTranslation()
  const dispatch = useDispatch()
  const visible = useSelector((state) => state.modal.ModalSysGrantPerm?.visible || false)
  const { mode, record, refreshRoles } = useContext(RoleRecordContext)
  const [form] = Form.useForm()

  const currentPermIds =
    record?.currentRolePerms.map(({ permissionId }) => permissionId) || []

  const { loading, run: grantPerms } = useRequest(grantPermsToRole, {
    manual: true,
    onSuccess: () => {
      message.success(t("features:authorizationSuccess"))
      dispatch(hideModal('ModalSysGrantPerm'))
      refreshRoles && refreshRoles()
    },
  })

  return (
    <UIModal
      visible={visible}
      title={t("features:grantPermission")}
      onCancel={() => dispatch(hideModal('ModalSysGrantPerm'))}
      onOk={async () => {
        if (!record) return
        try {
          const formValues = await form.validateFields()
          const grantedPerms = (formValues.grantedPerms as (string | number)[]) || []
          const addList = difference(grantedPerms, currentPermIds)
          const removeList = difference(currentPermIds, grantedPerms)

          grantPerms({
            grantPerms: addList,
            removePerms: removeList,
            roleName: record.roleName,
            roleId: record.roleId,
          })
        } catch {}
      }}
      confirmLoading={loading}
    >
      {record && (
        <div style={{ marginBottom: 16 }}>
          <strong>{t("features:roleAlias")}:</strong>
          {record.roleName}
        </div>
      )}
      <SysGrantPermForm mode={mode} record={record} form={form} />
    </UIModal>
  )
}
