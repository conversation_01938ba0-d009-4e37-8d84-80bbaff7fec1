import React, { useState, useRef, useEffect, useReducer, useMemo } from 'react'
import ReactDOM from 'react-dom'
import { useSelector, useRequest, useDispatch } from 'src/hook'
import { Button, Space, Form, Steps, message, Alert, Modal } from 'antd'
import {
  TextTypeForm,
  FilePathForm,
  SeparateSettingForm,
  SettingTwoForm,
  SelectTargetTableForm,
  FieldMapForm,
  CustomDataForm,
} from '../forms/textImportForms'
import { LazyWrapper } from 'src/components'
import { getValidFileVo, postTextImport, saveFlowApply } from 'src/api'
import { hideModal } from 'src/store/extraSlice/modalVisibleSlice'
import {
  setFilePath,
  setFileType,
  setTargetTable,
  setFileCode,
  setTargetTableList,
  setTargetTableMessage,
  setSourceTableFieldRequest,
  setTableFieldMap,
} from 'src/store/extraSlice/textImportSlice'
import styles from './textWizard.module.scss'
import { useHistory } from 'react-router-dom'
import { NOT_SHOW_SEPARATESETTINGFORM_FILETYPE } from '../forms/common'
import { useTranslation } from 'react-i18next'
import { setTabsKey } from 'src/pageTabs/taskCenter/taskCenterSlice'

enum WizardStep {
  setTextType = 0,
  setFilePath = 1,
  setSeparate = 2,
  setTwo = 3,
  selectTarget = 4,
  setFieldMap = 5,
}

export const TextImportWizard = ({
  submitterRef,
}: {
  submitterRef: React.RefObject<HTMLElement>
}) => {
  const { t } = useTranslation()
  const dispatch = useDispatch()
  const [current, setCurrent] = useState<WizardStep>(WizardStep.setTextType)
  const [targetFields, setTargetFields] = useState<string>('')
  const [isCreateTable, setIsCreateTable] = useState<boolean>(false)
  const [isApprove, setIsApprove] = useState<boolean>(false)  // 是否走审批
  const furthestStepRef = useRef<WizardStep>(WizardStep.setTextType)

  const { userInfo } = useSelector((state) => state.login)
  const { selectedNode } = useSelector((state) => state.sdt)
  const { fileType, filePath, targetTableMessage, tableFieldMap, fileImportSecuritySetting } = useSelector(
    (state) => state.textImport,
  )
  const { userId, email } = userInfo
  const { connectionId, connectionType, nodePathWithType } =selectedNode || {}
  const { settingType, count = 0, childType, } = fileImportSecuritySetting || {}

  // 获取上传文件大小
  const { data: validFileVoData, run: runGetValidFileVo } = useRequest(getValidFileVo, {
    manual: true,
    onSuccess: (res: any) => {
      if ((childType === "ALL_IMPORT_NEED_APPROVAL") && (res?.fileSize > count)) {
        setIsApprove(true)
      } else {
        setIsApprove(false)
      }
    },
  })

  const isSizeImport = (settingType === "IMPORT_FILE_SIZE_LIMIT")

  useEffect(() => {
    if (current === WizardStep.setSeparate) {
      runGetValidFileVo({filePath: filePath || ''})
    }
  }, [current, filePath])
  
  // !HACK
  const [, forceUpdate] = useReducer((r) => r + 1, 0)
  useEffect(() => {
    forceUpdate()
  }, [])

  useEffect(() => {
    const furthestStep = furthestStepRef.current
    furthestStepRef.current = current > furthestStep ? current : furthestStep
  }, [current])

  const [typeForm] = Form.useForm()
  const [pathForm] = Form.useForm()
  const [separateForm] = Form.useForm()
  const [s2Form] = Form.useForm()
  const [targetForm] = Form.useForm()
  const [mapForm] = Form.useForm()
  const [customDataForm] = Form.useForm()
  const history = useHistory()

  const steps = useMemo(() => {
    return  [
      {
        title: t('sdo_file_type'),
        key: 'textTypeForm',
        content: <TextTypeForm form={typeForm} />,
      },
      {
        title: t('sdo_file_encoding'),
        key: 'filePathForm',
        content: <FilePathForm form={pathForm} />,
      },
      {
        title: t('sdo_separator_config'),
        key: 'separateSettingForm',
        content: <SeparateSettingForm form={separateForm} />,
      },
      {
        title: t('sdo_format_config'),
        key: 'settingTwoForm',
        content: <SettingTwoForm form={s2Form} />,
      },
      {
        title: t('sdo_select_target_table'),
        key: 'selectTargetTableForm',
        content: <SelectTargetTableForm form={targetForm} />,
      },
      {
        title: t('sdo_table_field_mapping'),
        key: 'fieldMapForm',
        content: (
          <FieldMapForm form={mapForm} connectionType={connectionType} targetFields={targetFields || t("features:empty")} isCreateTable={isCreateTable}/>
        ),
      },
    ]
  },[targetFields, isCreateTable, current, t])

  useEffect(() => {
    const { targetTable, createTable = false } = targetForm.getFieldsValue()
    if (targetTable) {
      setTargetFields(targetTable)
    }
    setIsCreateTable(createTable)
  }, [targetForm,current])

  // 调用提交审批接口
  const { run: runSaveFlowApply } = useRequest(saveFlowApply, {
    manual: true,
    onSuccess: (res: any) => {
      message.success(t('sdo_submission_success'))
      dispatch(hideModal('ModalTextImportWizard'))
      resetForm()
    }
  })

  // 调用导入接口
  const { loading: loadingImport, run: runTextImport } = useRequest(
    postTextImport,
    {
      manual: true,
      onSuccess: async() => {
        message.success(t('sdo_addition_success'))
        await dispatch(hideModal('ModalTextImportWizard'))
        resetForm()
        setTimeout(() => { 
          history.push('/download');
          dispatch(setTabsKey('handleImport'))
        }, 500)
      },
    },
  )

  // 重置表单
  const resetForm = () => {
    typeForm.resetFields()
    pathForm.resetFields()
    separateForm.resetFields()
    s2Form.resetFields()
    targetForm.resetFields()
    mapForm.resetFields()
    customDataForm.resetFields()
    // 某些表单值存在redux中也要重置
    dispatch(setFilePath(''))
    dispatch(setFileType('txt'))
    dispatch(setTargetTable(''))
    dispatch(setFileCode(''))
    dispatch(setTargetTableList([]))
    dispatch(setTargetTableMessage(null))
    dispatch(setSourceTableFieldRequest(null))
    dispatch(setTableFieldMap([{
      columnName: null,
      columnTypeName: null,
      index: null,
      sourceColumnName: null,
    }]))
  }

  const constructImportParam = () => {
    let params = {}
    const pathFormValues = pathForm.getFieldsValue()
    const separateFormValues = separateForm.getFieldsValue()
    const s2FormValues = s2Form.getFieldsValue()
    const mapFormValues = mapForm.getFieldsValue()
    const { createTable } = targetForm.getFieldsValue()

    if (connectionType !== "MongoDB") {
      const isNull = tableFieldMap?.some(t => (t.columnName === null || t.index === null))
      if (isNull) {
        message.error(t('sdo_fields_not_empty'))
        return null
      }
      // 检测目标字段是否重复
      const targetListArr = tableFieldMap?.filter(t => t.columnName !== null).map(t => t.columnName)
      const noRepeatArr =  Array.from(new Set(targetListArr))
      if(targetListArr && targetListArr.length > noRepeatArr.length) {
        message.error(t('sdo_unique_target_fields'))
        return null
      }
    }
 
    //必填
    if (createTable === true) {
      //类型字段不能为空
      const hasEmptyColumnTypeName = tableFieldMap?.some(t => !t?.columnTypeName)
      if (hasEmptyColumnTypeName) {
        message.error(t('sdo_type_field_not_empty'))
        return null
      }
    }

    const {dateSort, dateTimeSort } = s2FormValues
    params = {
      charsetName: pathFormValues.codeType,
      columnMap: tableFieldMap!,
      dataStartLine: s2FormValues.firstRow,
      dataEndLine: s2FormValues.lastRow,
      dateDelimiter: s2FormValues.dateSeparator,
      dateSort,
      dateTimeSort,
      decimalPointSymbol: s2FormValues.decimalDot,
      delimiter: separateFormValues.fieldSeparator,
      encodeType: s2FormValues.binaryCode,
      fieldNameLine: s2FormValues.fieldRow,
      filePath: filePath!,
      fileType: fileType!,
      lineBreak: separateFormValues.rowSeparator,
      quote: separateFormValues.fieldRecognize,
      stopOnError: mapFormValues.isContinue,
      bulkInsert: !!mapFormValues.bulkInsert,
      timeDelimiter: s2FormValues.timeSeparator,
      createTable,
      ...targetTableMessage!
    }
    return params
  }

  const handleSaveFlowApply = (values: any, jsonObject: any) => {
    const { title = '', applyReason = '', userIds = [] } = values || {}
    const params = {
      applyUserId: userId, 
      email: email,
      remark: applyReason, 
      applyReason: applyReason, 
      flowType: "importTask",
      connectionId,
      dataSourceType: connectionType,  
      nodePathWithTypeList: [nodePathWithType], //对象路径,对应上面执行返回的 allObjectPathSting 字段  // to do
      importFileSize: validFileVoData?.fileSize, // 导入文件大小
      title: title, 
      priGranType: "importTask",
      useCartDataThinTiling: false, 
      jsonObject: jsonObject, // 导入接口生成的json值 
      ...(userIds?.length ? { connAndAssigneeMapList: [{connectionId, userIds: values?.userIds}]}: {})
    }
    // 提交
    runSaveFlowApply(params)
  }
  
  // 提交审批
  const handleApply = () => {
    const jsonObject = constructImportParam()
    if (!jsonObject) {
      return
    }
    function handleOk(closeFn: any) {
      // 提交表单
      customDataForm.validateFields().then((values) => {
        handleSaveFlowApply(values, jsonObject)
        closeFn();
      }).catch((error) => {
        console.log('标题,申请原因表单数据获取异常 :>> ', error);
      })
    }
    
    Modal.confirm({
      title: t('sdo_text_import_request'),
      icon: null,
      content: ( <CustomDataForm form={customDataForm} connectionId={connectionId} /> ),
      okText: t('sdo_ok'),
      cancelText: t('sdo_cancel'),
      onOk: (closeFn) => handleOk(closeFn),
      onCancel: (closeFn) => closeFn(),
    });
  };

  // 开始导入
  function handleConfirmWizard() {
    const params = constructImportParam()
    if (!params) {
      return
    }
    runTextImport(params as any)
  }

  // 下一步按钮
  const submitter = (
    <Space>
      <Button
        className={styles.submitterBtn}
        onClick={() => {
          if (
            current >= WizardStep.setTwo &&
            current < WizardStep.selectTarget &&
            (NOT_SHOW_SEPARATESETTINGFORM_FILETYPE.includes(fileType || ''))
          ) {
            setCurrent((current) => current - 1)
          }
          setCurrent((current) =>
            current > WizardStep.setTextType ? current - 1 : current,
          )
        }}
        disabled={current <= WizardStep.setTextType}
      >
        {t('sdo_prev_step')}
      </Button>
      {(current < WizardStep.setFieldMap && connectionType !== "MongoDB") || 
      (current < WizardStep.selectTarget && connectionType === "MongoDB")
      ? (
        <Button
          className={styles.submitterBtn}
          disabled={current === WizardStep.setFilePath && !filePath}
          onClick={async () => {
            if (
              current >= WizardStep.setFilePath &&
              current < WizardStep.setSeparate
            ) {
              await pathForm.validateFields()
              if (NOT_SHOW_SEPARATESETTINGFORM_FILETYPE.includes(fileType || '')) {
                setCurrent((current) => current + 1)
              }
            } else if (
              current >= WizardStep.setSeparate &&
              current < WizardStep.setTwo
            ) {
              await separateForm.validateFields()
            } else if (
              current >= WizardStep.setTwo &&
              current < WizardStep.selectTarget
            ) {
              await s2Form.validateFields()
              const pathFormValues = pathForm.getFieldsValue()
              const separateFormValues = separateForm.getFieldsValue()
              const s2FormValues = s2Form.getFieldsValue()
              const {dateSort, dateTimeSort } = s2FormValues
              dispatch(
                setSourceTableFieldRequest({
                  charsetName: pathFormValues.codeType,
                  delimiter: separateFormValues.fieldSeparator,
                  fieldNameLine: s2FormValues.fieldRow,
                  filePath: filePath!,
                  fileType: fileType!,
                  lineBreak: separateFormValues.rowSeparator,
                  quote: separateFormValues.fieldRecognize,
                  dateDelimiter: s2FormValues.dateSeparator,
                  dateSort,
                  dateTimeSort,
                  decimalPointSymbol: s2FormValues.decimalDot,
                  timeDelimiter: s2FormValues.timeSeparator
                }),
              )
            } else if (
              current >= WizardStep.selectTarget
            ) {
              await targetForm.validateFields()
            }
            
            setCurrent((current) =>
              current < WizardStep.setFieldMap ? current + 1 : current,
            )
            // } catch {}
          }}
        >
          {t('sdo_next_step')}
        </Button>
      ) : (
        <Button
          type="primary"
          onClick={() => {
            if (isApprove) {
              handleApply()
              return;
            };
            handleConfirmWizard()
          }}
          loading={loadingImport}
        >
          {isApprove ? t('sdo_submit_approval') : t('sdo_start_import')}
        </Button>
      )}
    </Space>
  )

  const filteredSteps = steps?.filter(({ title }) => {
    if (connectionType !== "MongoDB") {
      return !(NOT_SHOW_SEPARATESETTINGFORM_FILETYPE.includes(fileType || '')) || title !== t('sdo_separator_config')
    } else {
      return !([t('sdo_separator_config'), t('sdo_table_field_mapping')].includes(title))
    }
  })

  const getOriginalIndex = (title: string) => steps.findIndex(step => step?.title === title)

  return (
    <div className={styles.textImportWrap}>
      {isSizeImport && 
      <Alert
        className={styles.alertSty}
        message={<span>{t('sdo_import_file_size_limit')} <span style={{color: "#3262fe"}}>{`${!count? 0 : Math.floor(count / 1024) || 0}KB`}</span>, {t('sdo_approval_required_lower')}</span>}
        type="info"
        showIcon 
      />
      }
      <div className={styles.textImport}>
        
        <div>
          <Steps current={current} direction="vertical" size="small">
            {filteredSteps.map(({ title, key }, index) => {
              const originalIndex = getOriginalIndex(title);
              const num = index + 1;
              return (
                <Steps.Step title={title} key={key} 
                  icon={
                    originalIndex === current ? (
                      <div className={styles.customStepIcon}>{num}</div>
                    ) : originalIndex > current && (
                      <div className={styles.defaultStepIcon}>{num}</div>
                    )
                  }
                />
              )
            })}
          </Steps>
        </div>
        <div>
          {steps.map(({ content }, index) => (
              <LazyWrapper
                className={styles.stepsForm}
                active={current === index}
                key={index}
                style={{ overflow: 'auto' }}
              >
                {content}
              </LazyWrapper>
          ))}
          {submitterRef.current &&
            ReactDOM.createPortal(submitter, submitterRef.current)}
        </div>
      </div>
    </div>
  )
}
