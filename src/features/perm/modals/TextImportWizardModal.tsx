import React, { useRef } from 'react'
import { useSelector, useDispatch } from 'src/hook'
import { UIModal } from 'src/components'
import { TextImportWizard } from './TextImportWizard'
import { hideModal } from 'src/store/extraSlice/modalVisibleSlice'
import { Descriptions, Tooltip } from 'antd'
import { QuestionCircleOutlined } from '@ant-design/icons'
import { useTranslation } from 'react-i18next'

export const ModalTextImportWizard = () => {
  const dispatch = useDispatch()
  const visible = useSelector((state) => state.modal.ModalTextImportWizard?.visible || false)
  const submitterRef = useRef<HTMLDivElement>(null)

  return (
    <UIModal
      title={<TitleTooltip />}
      visible={visible}
      footer={<div ref={submitterRef}></div>}
      onCancel={() => dispatch(hideModal('ModalTextImportWizard'))}
      width={900}
      zIndex={600}
      style={{ overflow: 'auto' }}
    >
      <TextImportWizard submitterRef={submitterRef} />
    </UIModal>
  )
}

const TitleTooltip = () => {
  const { t } = useTranslation()
  return (
    <>
      <span>{t('sdo_text_import')}</span>
      <Tooltip title={<HelpMessage />} placement="leftBottom" color="white">
        <QuestionCircleOutlined />
      </Tooltip>
    </>
  )
}

const HelpMessage = () => {
  const { t } = useTranslation()
  return (
    <Descriptions
      size="small"
      column={1}
    >
      <Descriptions.Item label={t('sdo_encoding')}>
        {t('sdo_encoding_specify')}
      </Descriptions.Item>
      <Descriptions.Item label={t('sdo_line_separator')}>
        {t('sdo_line_break_type')} CR（\r）、LF(\n)、CRLF(\r\n)
      </Descriptions.Item>
      <Descriptions.Item label={t('sdo_field_separator')}>
        {t('sdo_field_delimiters')}
      </Descriptions.Item>
      <Descriptions.Item label={t('sdo_field_identifier')}>
        {t('sdo_string_field_identifier')}
      </Descriptions.Item>
      <Descriptions.Item label={t('sdo_field_name_row')}>
        {t('sdo_field_name_option')}
      </Descriptions.Item>
      <Descriptions.Item label={t('sdo_first_data_row')}>
        {t('sdo_data_start_row')}
      </Descriptions.Item>
      <Descriptions.Item label={t('sdo_last_data_row')}>
        {t('sdo_data_end_row')}
      </Descriptions.Item>
      <Descriptions.Item label={t('sdo_date_sort')}>
        {t('sdo_date_format')}
      </Descriptions.Item>
      <Descriptions.Item label={t('sdo_date_separator')}>
        {t('sdo_date_split')}
      </Descriptions.Item>
      <Descriptions.Item label={t('sdo_time_separator')}>
        {t('sdo_time_split')}
      </Descriptions.Item>
      <Descriptions.Item label={t('sdo_decimal_symbol')}>
        {t('sdo_time_decimal_symbol')}
      </Descriptions.Item>
      <Descriptions.Item label={t('sdo_datetime_sort')}>
        {t('sdo_specify_datetime_order')}
      </Descriptions.Item>
      <Descriptions.Item label={t('sdo_binary_encoding')}>
        {t('sdo_binary_encoding_format')}
      </Descriptions.Item>
    </Descriptions>
  )
}
