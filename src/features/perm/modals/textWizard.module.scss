.textImportWrap {
  display: table;
  .alertSty {
    border-width: 0px;
    margin-bottom: 4px;
    position: relative;
    top: -13px;
  }
  .textImport {
    display: flex;
    flex-direction: row;
    justify-content:flex-start;
  }
}

.stepsForm {
  width: 600px;
  height: 500px;
  margin-left: 20px;
  padding: 10px;
  border: 1px solid rgb(228, 224, 224);
  border-radius: 4px;
}

.submitterBtn {
  width: auto;
}

.customStepIcon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: #3262ff;
  color: white;
  font-size: 14px;
}

.defaultStepIcon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border: 1px solid #c0c0c0;
  border-radius: 50%;
  background-color: #ffffff; /* 未完成步骤的背景色 */
  color: #c0c0c0; /* 未完成步骤的文本颜色 */
  font-size: 14px;
}
