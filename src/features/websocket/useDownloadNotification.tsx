/*
 * @Author: yang<PERSON><PERSON> <EMAIL>
 * @Date: 2023-06-12 16:00:48
 * @LastEditors: yangchao <EMAIL>
 * @LastEditTime: 2023-10-08 11:34:08
 * @FilePath: /cq-enterprise-frontend/src/features/websocket/useDownloadNotification.tsx
 * @Description:
 */
import React, { useCallback } from "react";
import { checkFileExistance, TaskStatus } from "src/api";
import { notification, Modal, message } from "antd";
import { LinkButton } from "src/components";
import { handleDownload } from "src/util";
import copy from "copy-to-clipboard";
import { CopyOutlined } from "@ant-design/icons";
import { fetchGet } from 'src/api/customFetch'
import i18n from "i18next";

const placement = "bottomLeft";

export type DownloadMsg = {
  taskId: number;
  taskStatus: TaskStatus;
  message: string;
  origin: string;
  fileSecretKey: string;
  whetherToEncrypt: boolean;
};

/** websocket 从服务端收到的 “sql 导出任务状态“ notification 的渲染 */
export const useDownloadNotificationRenderer = () => {
  let createdId: number[] = [];
  const copyMet = (mes: string) => {
    copy(mes);
    message.success(i18n.t("features:copySuccess"));
  };
  const renderNotification = useCallback((downloadMsg: DownloadMsg) => {
    if (!downloadMsg) return;
    const { taskId, taskStatus, message, origin } = downloadMsg;
    const [title, description] = message.split("：");

    const onDownloadFile = () => {
      // ! BAD: 使用 a 标签下载是最符合标准的，如果资源不存在，直接报 404 或 410 即可
      checkFileExistance(taskId.toString(), origin)
        .then(() => {
          fetchGet(`/export/export/check/encrypt/${taskId}`).then((res: any) => {
            const { whetherToEncrypt, fileSecretKey } = res || {}
            if (whetherToEncrypt) {
              Modal.info({
                width: 520,
                content: (
                  <div>
                    <div style={{ fontSize: '18px', marginBottom: '30px' }}>{i18n.t("features:fileKey")}</div>
                      {i18n.t("features:exportFileEncrypted")}:
                    <span style={{ fontWeight: "bold" }}>
                      {fileSecretKey}
                    </span>
                    <CopyOutlined
                      style={{ color: "#0c2dc7" }}
                      onClick={() => {
                        copyMet(fileSecretKey);
                      }}
                    />
                  </div>
                ),
                icon: null,
                onOk: () => {
                  handleDownload({
                    href: `/${origin}/export/download/${taskId}`,
                  });
                }
              });
            } else {
              handleDownload({
                href: `/${origin}/export/download/${taskId}`,
              });
            }
          }).catch((err: any) => { console.error(err) })
        })
        .catch();
    };

    switch (taskStatus) {
      case "CREATE":
        // 避免轮询时多次创建弹窗
        if (createdId.indexOf(taskId) >= 0) return;
        createdId.push(taskId);
        notification.info({ placement, message: title, description });
        break;
      case "SUCCESS":
        notification.success({
          placement,
          duration: 15,
          message: title,
          description: (
            <>
              {description}
              <LinkButton
                onClick={() => {
                  onDownloadFile()
                }}
              >
                {i18n.t("features:download")}
              </LinkButton>
            </>
          ),
        });
   
        //自动下载
        onDownloadFile();
        break;
      case "FAILURE":
        notification.error({
          placement,
          message: title,
          description,
        });
        break;
      default:
        break;
    }
  }, []);

  return [renderNotification];
};
