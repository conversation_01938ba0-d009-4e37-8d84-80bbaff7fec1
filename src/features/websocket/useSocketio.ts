import { useCallback, useEffect } from 'react'
import { useDispatch, useSelector } from 'src/hook'
import io from 'socket.io-client'
import { addLog } from 'src/store/extraSlice/logsSlice'
import { addSegmentMsg } from 'src/pageTabs/queryPage/resultTabs/resultTabsSlice'
import { useDownloadNotificationRenderer } from './useDownloadNotification'
import { pushBatchCreateConnectionLog } from 'src/pageTabs/queryPage/sdt/sdtSlice'
import { message } from 'antd'
import { setTaskProgressMap } from 'src/pageTabs/downloadPage/downloadPageSlice'

type SocketMsg = {
  type: string
  // TODO: 需要跟后端把各个类型的消息结构明确
  msg: any
}

export const useSocketio = () => {
  const dispatch = useDispatch()
  const sessionId = useSelector((state) => state.login.userInfo.sessionId)
  const userId = useSelector((state) => state.login.userInfo.userId)

  const [renderDownloadNotification] = useDownloadNotificationRenderer()

  const init = useCallback(() => {
    if (!sessionId) return
    const socket = io({
      transports: ['websocket'],
      forceNew: false,
      query: { session: sessionId, userId },
      rememberUpgrade:true,
      timeout:5000,
      reconnectionAttempts: 5000,
      reconnectionDelay: 5000,
    })

    // socket.on('connect', () => {
    //   console.log('connected')
    //   // ! message.destroy(key)关闭特定 message 需要升级 antd 版本后才能使用，这里先关闭所有
    //   message.destroy()
    // })

    // socket.on('disconnect', (reason: string) => {
    //   console.log('socket 连接已断开', reason)
    //   if (reason === 'io server disconnect') {
    //     socket.connect()
    //   }
    // })

    // socket.io.on('reconnect_failed', () => {
    //   message.error({
    //     // M0001 表示 message 模块 0001 错误：服务状态异常
    //     content: 'message 服务状态异常，请联系运维人员。错误码【M0001】',
    //     duration: 0,
    //     key: 'reconnect_failed',
    //   })
    // })

    // socket.on('connect_error', (err: any) => {
    //   socket.connect()
    //   console.log(`connect_error due to ${err.message}`)
    // })

    // socket.io.on('reconnect_attempt', () => {
    //   console.log('reconnect')
    // })

    socket.on("connect", () => {
      console.log(socket.id, '监听客户端连接成功-connect');
    })
    // 断开连接
    socket.on("disconnect", (reason:any) => {
      console.log(socket.connected);
      console.log("断开连接-disconnect", reason);
    })
    // 错误
    socket.on("error", (err:Error) => {
      console.log("错误-error", err);
    })
    // 连接错误
    socket.on("connect_error", (err:Error) => {
      // err
      console.log("连接错误-connect_error");
    });
    // 连接超时
    socket.on("connect_timeout", (data:any) => {
      console.log("连接超时-connect_timeout", data);
    });
    // 重连成功
    socket.on("reconnect", (attemptNumber:number) => {
      // 重连尝试次数
      console.log("重连成功-reconnect", attemptNumber)
    });
    // 尝试重连时触发
    socket.on("reconnect_attempt", (attemptNumber:number) => {
      // 重连尝试次数
      console.log("尝试重连-reconnect_attempt", attemptNumber)
    });
    // 在尝试重新连接时触发
    socket.on("reconnecting", (attemptNumber:number) => {
      // 重连尝试次数
      console.log("正在尝试重连-reconnecting", attemptNumber)
    });
    // 重连尝试错误
    socket.on("reconnect_error", (err:Error) => {
      // err
      console.log(socket.connected);
      console.log("重连尝试错误-reconnect_error");
    });
    // 客户端不能重连时触发
    socket.on("reconnect_failed", () => {
      message.error({
        // M0001 表示 message 模块 0001 错误：服务状态异常
        content: 'message 服务状态异常，请联系运维人员。错误码【M0001】',
        duration: 0,
        key: 'reconnect_failed',
      })
      console.log("客户端不能连接-reconnect_failed")
    });
    // 当一个ping被发送到服务器时触发
    socket.on("ping", () => {
      console.log("一个ping发送到服务器-ping")
    });
    
    // 当服务器收到pong时触发
    socket.on("pong", (data:any) => {
      // data: 延迟多少ms
        console.log("服务器收到pong-pong", data);
    });

    socket.on('sendEvent', (data: SocketMsg, cb: Function) => {
      if (typeof cb === 'function') {
        cb('ack')
      }
      try {
        const { type, msg } = data
        switch (type) {
          case 'LOG': {
            dispatch(addLog(msg))
            return
          }

          case 'DOWNLOAD': {
            renderDownloadNotification(JSON.parse(msg))
            return
          }

          case 'SEGMENT_EXECUTION':
            dispatch(addSegmentMsg(msg))
            return

          /* 批量创建连接 */
          case 'BATCH_CREATE_CONNECTION':
            dispatch(pushBatchCreateConnectionLog(msg))
            break

          /* 批量导入用户 */
          case 'BATCH_CREATE_USER':
            //这里偷个懒，直接用批量创建连接的模块存错位信息
            dispatch(pushBatchCreateConnectionLog(msg))
            break

          /* 导出进度接收 */
          case 'PROGRESS':
            dispatch(setTaskProgressMap(msg))
            break
          default:
            break
        }
      } catch (error) {}
    })

    return socket
  }, [dispatch, renderDownloadNotification, sessionId, userId])

  useEffect(() => {
    const socket = init()
    console.log(socket?.connected, '监听客户端连接成功-init')
    return () => {
      socket?.disconnect()
    }
  }, [init])
}
