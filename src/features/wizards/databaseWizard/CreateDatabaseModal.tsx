import React, { useCallback, useEffect, useState } from 'react'
import { useSelector, useDispatch, useRequest } from 'src/hook'
import { Form, message, Input, Select } from 'antd'
import { UIModal } from 'src/components'
import { getWizardFormItem } from '../wizardFormItem'
import {
  getWizardForm,
  WizardItem,
  generateCreateSql,
  GenerateSqlRequest,
  getCharacterSetOrder,
  executeSqlStatement,
  getSqlCheck
} from 'src/api'
import { FormLayout } from 'src/constants'
import { setExamineReviewResult } from 'src/pageTabs/queryPage/queryTabs/queryTabsSlice';
import SqlExecuteAuditeModal from 'src/pageTabs/queryPage/queryTabs/monacoPane/SqlExecuteAuditeModal';
import {
  finishCreateDatabase,
  setVisibleCreateDatabase,
} from './createDatabaseSlice'
import { refreshOnRoot } from 'src/pageTabs/queryPage/sdt'
import { useTranslation } from 'react-i18next'

export const CreateDatabaseModal = () => {
  const { t } = useTranslation()
  const [confirmLoading, setConfirmLoading] = useState<boolean>(false)
  // generated sql
  const [generatedSql, setGeneratedSql] = useState('')

  const { visibleCreateDatabase } = useSelector(
    (state) => state.wizards.createDatabase,
  )
  const { connectionType = 'MySQL', connectionId = 0, nodePathWithType } = useSelector(
    (state) => state.sdt.selectedNode,
  )
  const [formSpec, setFormSpec] = useState<WizardItem[]>([])
  const [databaseForm] = Form.useForm()
  const dispatch = useDispatch()

    //sql审核
    const {run: runCheckSqlStatement } = useRequest(getSqlCheck,{
      manual: true,
      onSuccess: (res) => {
        if(res) {
          dispatch(setExamineReviewResult({queryTabKey: 'addDatabase', reviewResult: res}))
        }else {
          handleCreateDatabase()
        }
      }
  });
  
  const generateSql = () => {
    const tryGenerate = (userInputs: any) => {
      const params: GenerateSqlRequest = {
        nodeType: 'database',
        dataSourceType: connectionType!,
        userInputs,
      }
      generateCreateSql(params).then(({ generatedSql }) => {
        setGeneratedSql(generatedSql)
      })
    }
    databaseForm
      .validateFields()
      .then((userInputs) => {
        tryGenerate(userInputs)
      })
      .catch((e) => {
        const { values } = e || {}
        tryGenerate(values)
      })
  }
   
  const handleCreateDatabase = useCallback(
    () => {
      setConfirmLoading(true)

      executeSqlStatement({
        connectionId,
        dataSourceType: connectionType,
        statements: [generatedSql],
        tabKey: 'ALL',
        actionType: 'EXECUTE',
      })
        .then((data) => {
          const { executionInfos } = data
          const [{ response }] = executionInfos
          if (response?.executeError) {
            message.error(response.executeError.message)
          } else {
            message.success(t("features:databaseAddedSuccess"))
            dispatch(refreshOnRoot())
            dispatch(setVisibleCreateDatabase(false))
          }
        })
        .catch(() => {})
        .finally(() => setConfirmLoading(false))
    },
    [connectionId, generatedSql,connectionType, dispatch],
  )

  useEffect(() => {
    if (!visibleCreateDatabase) return
    getWizardForm({
      dataSourceName: connectionType,
      nodeType: 'database',
    }).then(setFormSpec)
    return () => {
      setGeneratedSql('')
    }
  }, [visibleCreateDatabase, connectionType])

  const [collationOptions, setCollationOptions] = useState([])

  const onCheckSqlResult = () => {
    if (!connectionId || !nodePathWithType) return;

    runCheckSqlStatement({
     connectionId,
     scenario: "GUI",
     dataSourceType: connectionType,
     nodePathWithType,
     statements: [generatedSql]
    })
  }

  const characterOptions = (
    (formSpec.find((spec) => spec.field === 'character') || {}).options || []
  ).map(({ key: value, title: label }: any) => ({ label, value }))

  return (
    <UIModal
      title={t("features:addDatabase")}
      visible={visibleCreateDatabase}
      onCancel={() => dispatch(finishCreateDatabase())}
      onOk={() => onCheckSqlResult()}
      afterClose={() => databaseForm.resetFields()}
      confirmLoading={confirmLoading}
    >
      <Form
        name="create-database"
        form={databaseForm}
        onValuesChange={() => {
          setTimeout(() => {
            // ? ref: https://github.com/ant-design/ant-design/issues/26747
            generateSql()
          })
        }}
        {...FormLayout}
        size="small"
      >
        {formSpec
          ?.filter((spec) => ![t("features:characterSet"), t("features:collation")].includes(spec.label))
          .map((spec) => getWizardFormItem({spec}))}

        <Form.Item label={t("features:characterSet")} name="character" hidden={!characterOptions?.length}>
          <Select
            placeholder={t("features:pleaseSelectCharacterSet")}
            options={characterOptions}
            onChange={(character) => {
              databaseForm.setFields([{ name: 'collation', value: undefined }])
              setCollationOptions([])
              const params = {
                connectionType,
                condition: String(character),
                connectionId,
              }
              getCharacterSetOrder(params).then((collations) => {
                const collationOptions = (collations || []).map(
                  ({ key: value, title: label }: any) => ({
                    value,
                    label,
                  }),
                )
                setCollationOptions(collationOptions)
              })
            }}
            showSearch
            allowClear
          />
        </Form.Item>
        <Form.Item noStyle dependencies={['character']} hidden={!collationOptions?.length}>
          {({ getFieldValue }) => (
            <Form.Item label={t("features:collation")} name="collation">
              <Select
                disabled={!Boolean(getFieldValue('character'))}
                placeholder={t("features:pleaseSelectCollation")}
                options={collationOptions}
                showSearch
                allowClear
              />
            </Form.Item>
          )}
        </Form.Item>
        <Form.Item label={t("features:generateSQL")}>
          <Input.TextArea
            readOnly
            style={{ backgroundColor: '#fefefe', height: 240 }}
            value={generatedSql}
          />
        </Form.Item>
      </Form>
       {/* sql审核结果 */}
       <SqlExecuteAuditeModal
          activePaneKey={'addDatabase'}
          execSegment={handleCreateDatabase}
        />
    </UIModal>
  )
}
