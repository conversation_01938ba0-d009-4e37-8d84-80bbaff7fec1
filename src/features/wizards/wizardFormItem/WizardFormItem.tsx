/*
 * @Author: fuz<PERSON><PERSON><PERSON>
 * @Date: 2022-09-21 11:09:52
 * @LastEditTime: 2022-09-29 10:07:04
 * @LastEditors: fuzhenghao
 * @Description:
 * @FilePath: \cq-enterprise-frontend\src\features\wizards\wizardFormItem\WizardFormItem.tsx
 */
import React from 'react'
import { Form, Select, Input, Checkbox, Radio, Tooltip, Space } from 'antd'
import { WizardItem } from 'src/api'
import { FormItemProps } from 'antd/lib/form'
import { FormTailLayout } from 'src/constants'
import { ConnectionMembersTable } from './ConnectionMembersTable'
import { looseNameValidator } from 'src/util'
import { QuestionCircleOutlined } from '@ant-design/icons'
import i18n from 'i18next'

interface IWizardFormProps {
  spec: Partial<WizardItem>
  labelWithTooltip?: boolean
}

export const getWizardFormItem = ({
  spec,
  labelWithTooltip,
}: IWizardFormProps) => {
  const { type, label, field, required, options, suffix, value, hide } = spec
  const labelWithTips = (
    <Space size="small">
      {label}
      {suffix && (
        <Tooltip title={suffix}>
          <QuestionCircleOutlined />
        </Tooltip>
      )}
    </Space>
  )
  const formProps: FormItemProps = {
    label: labelWithTooltip ? labelWithTips : label,
    name: field,
    initialValue: value,
    rules: [{ required, message: i18n.t("features:labelCannotBeEmpty", { label }) }],
  }

  const getFormItemContent = () => {
    switch (type) {
      case 'checkbox':
        formProps.valuePropName = 'checked'
        return <Checkbox>{suffix}</Checkbox>
      case 'select':
        const SelectOptions = options?.map(({ key, title }: any) => ({
          label: title,
          value: key,
        }))
        return (
          <Select
            placeholder={i18n.t("features:pleaseSelectLabel", { label })}
            showSearch
            optionFilterProp="label"
            options={SelectOptions}
          ></Select>
        )
      case 'radio':
        return (
          <Radio.Group>
            {options?.map(({ key, title }: { key: string; title: string }) => (
              <Radio key={key} value={key}>
                {title}
              </Radio>
            ))}
          </Radio.Group>
        )
      case 'password':
        return (
          <Input.Password
            visibilityToggle={false}
            placeholder={i18n.t("features:pleaseInputLabel", { label })}
          ></Input.Password>
        )
      case 'tag':
        return (
          <Select placeholder={i18n.t("features:pleaseSelectOrAddLabel", { label })} mode="tags">
            {options?.map(({ key, title }: any) => (
              <Select.Option key={key} value={key}>
                {title}
              </Select.Option>
            ))}
          </Select>
        )
      case 'textarea':
        return <Input.TextArea placeholder={i18n.t("features:pleaseInputLabel", { label })}></Input.TextArea>
      case 'table':
        // todo: 响应值格式统一之后去掉
        if (!(value instanceof Array)) {
          formProps.initialValue = []
        }
        return <ConnectionMembersTable />
      default:
        return <Input placeholder={i18n.t("features:pleaseInputLabel", { label })} />
    }
  }

  const Content = getFormItemContent()
  const style = hide ? { display: 'none' } : {}

  // ! anti pattern. 暂时处理，需要和后端约定配置化表单的 schema
  if (field === 'connectionName') {
    formProps.rules?.push({ validator: looseNameValidator })
  }

  return label ? (
    <Form.Item {...formProps} key={String(formProps.name)} style={style}>
      {Content}
    </Form.Item>
  ) : (
    <Form.Item
      {...formProps}
      key={String(formProps.name)}
      {...FormTailLayout}
      style={style}
    >
      {Content}
    </Form.Item>
  )
}
