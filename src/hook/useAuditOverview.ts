import { useEffect, useRef, useState } from "react";
import { useRequest } from "./useRequest";
import { getAllAuditMetrics } from "src/api";
import { useSelector } from ".";
import { baseCharts } from "src/pageTabs/audit/overview/CustomChartDisplay";
import { useTranslation } from "react-i18next";

export const ctrlCharts: any[] = [
  { id: -1, title: "sqlExecutionCount" },
  { id: -2, title: "sqlExecutionTime" },
]

export const useAuditOverview = (): any => {
  const { showCustomAudit } = useSelector(state => state.login)
  const { t } = useTranslation()
  const [customList, setCustomList] = useState<any[]>([]); //审计概览中展示的自定义审计指标图表
  const [displayType, setDisplayType] = useState<string>('TABLE') // 审计概览中审计指标的展示方式（包括非自定义） TABLE:表单 CHART:图表
  const chartsCtrlRef = useRef<any[]>([
    ...ctrlCharts.map((item: any) => ({ id: item.id, displayType: 'TABLE', params: {}, title: t(item.title) })),
    ...baseCharts.map((item: any) => ({ id: item.id, displayType: 'TABLE', params: {}, title: t(item.title) }))
  ])
  const [chartsCtrlList, setChartCtrlList] = useState<any[]>([...chartsCtrlRef.current])  // id：图表的id \ displayType：图表独自的展示效果 \ params：导出参数
  const checkInit = useRef<boolean>(false) // 判断是否正在loacalStorage初始化

  // 获取所有需要展示的审计指标
  const { run: runGetAuditMetrics } = useRequest(() => getAllAuditMetrics({ show: true }), {
    manual: true,
    formatResult: (data: any) => {
      setCustomList([...data])
    }
  })
  // 根据自定义审计指标列表显隐变化更新图表控制列表
  useEffect(() => {
    chartsCtrlRef.current = [
      ...ctrlCharts.map((item: any) => ({
        ...chartsCtrlRef.current.find((i: any) => i.id === item.id)
      })),
      ...baseCharts.map((item: any) => ({
        ...chartsCtrlRef.current.find((i: any) => i.id === item.id)
      })),
      ...customList?.map((item: any) => ({
        id: item.id,
        displayType: 'TABLE',
        title: item.name,
        params: { ...item },
        ...chartsCtrlRef.current.find((i: any) => i.id === item.id)
      })) || [],
    ]
    setChartCtrlList([...chartsCtrlRef.current])
  }, [customList])

  // 展示效果总开关
  useEffect(() => {
    if (!checkInit.current) {
      chartsCtrlRef.current = [
        ...chartsCtrlRef.current.map((item: any) => ({
          ...item,
          displayType
        }))
      ]
      setChartCtrlList([...chartsCtrlRef.current])
    }
    else checkInit.current = false
  }, [displayType])

  // 控制自定义审计指标显隐
  useEffect(() => {
    if (showCustomAudit) {
      runGetAuditMetrics()
    }
  }, [showCustomAudit])

  // 修改单个图表的展示效果
  const onChartDisplayTypeChange = (id: number, type: string) => {
    chartsCtrlRef.current = [
      ...chartsCtrlRef.current?.map((item: any) => {
        if (item.id === id) return { ...item, displayType: type }
        else return item
      })
    ]
    setChartCtrlList([...chartsCtrlRef.current])
  }

  // localStorage初始化渲染displayType
  const onInitLocalStorageDisplayType = (type: '', list: any[]) => {
    if (displayType !== type) {
      checkInit.current = true
      setDisplayType(type)
    }
    chartsCtrlRef.current = [
      ...chartsCtrlRef.current?.map((item: any) => {
        const initItem = list.find((i: any) => i.id === item.id)
        return { ...item, ...initItem }
      })
    ]
    setChartCtrlList([...chartsCtrlRef.current])
  }

  // 修改图表的导出参数
  const onChartExportParamsChange = (id: number, params: any) => {
    chartsCtrlRef.current = [
      ...chartsCtrlRef.current?.map((item: any) => {
        if (item.id === id) return { ...item, params }
        else return item
      })
    ]
    setChartCtrlList([...chartsCtrlRef.current])
  }

  return {
    customList,
    setCustomList,
    displayType,
    setDisplayType,
    chartsCtrlList,
    onChartDisplayTypeChange,
    onChartExportParamsChange,
    onInitLocalStorageDisplayType
  }
}

