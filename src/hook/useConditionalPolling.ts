import { useEffect } from 'react';

interface PollingOptions{
  /**
   * 轮询触发条件（返回true时启动轮询）
   * @default () => true
   */
  shouldPoll?: (...args: any) => boolean;
  /**
   * 轮询间隔（毫秒）
   * @default 1000
   */
  interval?: number;
  /**
   * 轮询触发函数
   */
  pollFn: (...args: any) => void;
  /**
   * 取消函数
   */
  cancelFn: () => void;
  /**
   * 依赖项（变化时重新初始化轮询）
   */
  deps: React.DependencyList;
}

export function useConditionalPolling({
  // 默认值为一个总是返回true的函数
  shouldPoll = () => true,
  interval = 1000,
  pollFn,
  cancelFn,
  deps = []
}: PollingOptions) {
  useEffect(() => {
   //是否轮询
    const shouldActivate = shouldPoll(...deps);
    
    if (!shouldActivate) {
      cancelFn();
      return;
    }

    // 立即执行一次
    pollFn(...deps);
    
    const timer = setInterval(() => {
      pollFn(...deps);
    }, interval);

    return () => {
      clearInterval(timer);
      cancelFn();
    };
  }, deps);
}
