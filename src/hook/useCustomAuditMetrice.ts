import { useEffect, useState } from 'react'
import { CUSTOM_AUDIT_CHART_ELEMENT, getChartApi, mutexOperations } from 'src/pageTabs/audit/customAuditMetrics/constants';
import { postFilterOpe, postFilterOpeForMatrix } from 'src/pageTabs/audit/customAuditMetrics/utils';
import { useRequest } from './useRequest';
import { deleteAuditMetrics, downloadAuditMetrics, saveAuditMetrics } from 'src/api';
import { cloneDeep, isEmpty, uniq } from 'lodash';
import moment from 'moment';
import { message } from 'antd';
import { getMomentAlias } from 'src/pageTabs/audit/customAuditMetrics/components/PreFilter';
import { useTranslation } from 'react-i18next';

export const defaultList = {
  0: [],
  1: [],
  2: [],
  3: []
}

export const useCustomAuditContext = (): any => {
  const { t } = useTranslation()
  const [chartInfo, setChartInfo] = useState<any>({
    id: 'new_0', //图表id
    chartType: 'STACKED_COLUMN_CHART',//图表类型
    chartName: '',//图表名称
    chartRemark: '',//图表备注
    chartShowType: false, //占总计的百分比
    fieldOrder: undefined, //字段排序方式
    valueOrder: undefined //值排序方式
  }) //图表类型
  const [eleNodes, setEleNodes] = useState<any>(defaultList); // 要素
  const [treeData, setTreeData] = useState<any[]>([]); // 指标树
  const [filterNodes, setFilterNodes] = useState<any[]>([]); // 过滤条件
  const [filterList, setFilterList] = useState<any>([
    { field: 'date', label: t("auays:filter_lbl.last_month"), value: [moment().subtract(30, 'd'), moment()] }
  ])
  const [chartData, setChartData] = useState<any[]>([]); // 图表数据
  const [baseData, setBaseData] = useState<any[]>([]); // 图表数据(包括全部)
  const [tabList, setTabList] = useState<any[]>([
    {
      tabId: 'new_0',
      isCreatCharts: false, // 是否可以生成图表
      isNoChange: true, // 用来判断打开的保存过的图表是否发生过修改
      chartInfo,
      eleNodes: defaultList,
      filterNodes: [],
      filterList: [{ field: 'date', label: t("auays:filter_lbl.last_month"), value: [moment().subtract(30, 'd'), moment()] }],
      oldParams: {} // 保存过的图表的存储参数
    }
  ]); // 图表tab列表

  // 重置
  const reset = (resetType: boolean) => {
    setChartData([])
    setBaseData([])
    setEleNodes(cloneDeep(defaultList))
    setFilterNodes([])
    setFilterList([{ field: 'date', label: t("auays:filter_lbl.last_month"), value: [moment().subtract(30, 'd'), moment()] }])
    if (resetType) {
      const index = tabList.length;
      setChartInfo({
        id: `new_${index}`,
        chartType: 'STACKED_COLUMN_CHART',//图表类型
        chartName: '',//图表名称
        chartRemark: '',//图表备注
        chartShowType: false,
        fieldOrder: undefined,
        valueOrder: undefined
      })
    }
  }

  // 当tabList为空时，初始化
  useEffect(()=>{
    if(tabList?.length === 0){
      setTabList([
        {
          tabId: 'new_0',
          isCreatCharts: false, // 是否可以生成图表
          isNoChange: true, // 用来判断打开的保存过的图表是否发生过修改
          chartInfo,
          eleNodes: defaultList,
          filterNodes: [],
          filterList: [{ field: 'date', label: t("auays:filter_lbl.last_month"), value: [moment().subtract(30, 'd'), moment()] }],
          oldParams: {} // 保存过的图表的存储参数
        }
      ])
    }
  },[tabList])

  // 事后过滤
  useEffect(() => {
    if (!isEmpty(baseData)) {
      let data: any = cloneDeep(baseData)
      filterNodes.map((node: any) => {
        const chartBaseInfo = CUSTOM_AUDIT_CHART_ELEMENT[chartInfo.chartType]
        const { dealType } = chartBaseInfo
        if (dealType === 1) {
          const dataObj = postFilterOpe({ data }, node)
          data = dataObj?.data
          setChartData(data)
        }
        else if (dealType === 2 || dealType === 3) {
          const { standard, assistant } = data
          let newData: any = {}
          if (node.type === 'x') newData = { standard, assistant }
          else if (node.type === 'y' && node.action) newData = { standard }
          else if (node.type === 'assistY' && node.action) newData = { assistant }
          else if (node.type === 'title') newData = { standard }
          const nData = postFilterOpe(newData, node)
          data = { ...data, ...nData }
          setChartData(data)
        }
        else {
          data = postFilterOpeForMatrix(data, node)
          setChartData(data)
        }
      })
    }
  }, [filterNodes])

  const onInfoChange = (info: any) => {
    setChartInfo({ ...chartInfo, ...info })
  }

  // 当tab切换时，保存当前图表数据
  const saveChartOnChangeTab = (): any[] => {
    const currentId = chartInfo.id
    const newTabList = tabList.map((item: any) => {
      if (item.tabId === currentId) {
        return {
          ...item,
          tabName: chartInfo?.chartName ?? '',
          chartInfo: cloneDeep(chartInfo),
          eleNodes: cloneDeep(eleNodes),
          filterNodes: cloneDeep(filterNodes),
          filterList: cloneDeep(filterList),
          isCreatCharts: !isEmpty(baseData)
        }
      }
      else return item
    })
    return newTabList
  }

  /**
   * 调整列表中的 tabId 和 chartInfo.id
   *
   * @param list 需要调整的列表
   * @returns 返回调整后的列表
   */
  const adjustListId = (list: any[]): any[] => {
    const currentId = chartInfo.id
    const newTabList = list.map((item: any, index: number) => {
      const str = String(item.tabId)
      if (str.startsWith('new_')) {
        if (item.tabId === currentId) {
          setChartInfo({
            ...item.chartInfo,
            id: `new_${index}`
          })
        }
        return {
          ...item,
          tabId: `new_${index}`,
          chartInfo: {
            ...item.chartInfo,
            id: `new_${index}`
          },
        }
      }
      else {
        return item
      }
    })
    return newTabList
  }

  // 添加图表标签页
  const addChartTab = (newTab: any) => {
    const newId = newTab.tabId
    const str = String(newId)
    if (!str.startsWith('new_')) {
      const isOpen = tabList.filter((tab: any) => tab.tabId === newId).length > 0
      if (isOpen) {
        onTabChange(newId)
        return
      }
    }
    if (tabList.length === 10) {
      message.error(t("auays:msg.maximum_chart_tabs_limit"))
      return
    }
    const list = saveChartOnChangeTab()
    const newTabList = adjustListId(list)
    newTabList.push(newTab)
    setTabList([...newTabList])
    if (str.startsWith('new_')) {
      reset(false)
      setTimeout(() => {
        const index = tabList.length;
        setChartInfo({
          id: `new_${index}`,
          chartType: 'STACKED_COLUMN_CHART',//图表类型
          chartName: '',//图表名称
          chartRemark: '',//图表备注
          chartShowType: false,
          fieldOrder: undefined,
          valueOrder: undefined
        })
      }, 0)
    }
    else {
      reset(false)
      setTimeout(() => updateChartOnTabChange(newTab), 0)
    }
  }

  // 切换图表标签页时更新图表数据
  const updateChartOnTabChange = (tabInfo: any) => {
    const { chartInfo, eleNodes, filterList, filterNodes } = tabInfo
    setChartInfo({ ...chartInfo })
    setEleNodes({ ...eleNodes })
    setFilterList([...filterList])
    setFilterNodes([...filterNodes])
  }

  // 删除图标tab页
  const delChartTab = (delId: string | number) => {
    let newList = tabList
    if (delId === chartInfo.id) {
      let delIndex: number = 0
      let highLightId: number = 0
      tabList.map((tab: any, index: number) => {
        if (tab.tabId === delId) {
          delIndex = index
        }
      })
      if (delIndex === tabList.length - 1) {
        highLightId = tabList[delIndex - 1]?.tabId
      }
      else {
        highLightId = tabList[delIndex + 1]?.tabId
      }
      const tabInfo = tabList.find((tab: any) => tab?.tabId === highLightId)
      reset(false)
      setTimeout(() => updateChartOnTabChange(tabInfo), 0)
    }
    else {
      newList = saveChartOnChangeTab()
    }
    const hasTab = newList?.find((tab: any) => tab.tabId === delId)
    if (!isEmpty(hasTab)) {
      const list = newList.filter((tab: any) => tab.tabId !== delId)
      const newTabList = adjustListId(list)
      setTabList([...newTabList])
      message.success(t("auays:msg.delete_success"))
    }
  }

  // 切换图表标签页
  const onTabChange = (tabId: string | number) => {
    if (tabId === chartInfo.id) return
    const newTabList = saveChartOnChangeTab()
    setTabList([...newTabList])
    const tabInfo = tabList.find((tab: any) => tab.tabId === tabId)
    reset(false)
    setTimeout(() => updateChartOnTabChange(tabInfo), 0)
  }

  // 初始化图表
  const initChart = (item: any) => {
    if (!isEmpty(item)) {
      const { cmdLogQuery = {}, chartKey, fieldInfos, fieldOrder = undefined, valueOrder = undefined, tableName, id, name, remark = '', metricsFilters = [] } = item
      const newChartInfo = {
        id: id,
        chartType: chartKey,//图表类型
        chartName: name || '',//图表名称
        chartRemark: remark || '',//图表备注
        chartShowType: false, //占总计的百分比 
        fieldOrder: fieldOrder,
        valueOrder: valueOrder
      }
      const chartBaseInfo = CUSTOM_AUDIT_CHART_ELEMENT[chartKey]
      const { elements } = chartBaseInfo
      const treeList = treeData?.filter((list: any) => list.key === tableName)?.[0]?.children
      const beforeFilterNodes: any = []
      // 时间筛选
      if (cmdLogQuery?.executeBeginMs && cmdLogQuery?.executeEndMs) {
        const startTime = moment(cmdLogQuery?.executeBeginMs)
        const endtTime = moment(cmdLogQuery?.executeEndMs)
        beforeFilterNodes.push({
          field: 'date',
          label: getMomentAlias(startTime, endtTime),
          value: [startTime, endtTime]
        })
      }
      const filterFieldList = ['dbTypes', 'connectionName', 'depts', 'executors']
      for (const key in cmdLogQuery) {
        if (filterFieldList.includes(key)) {
          cmdLogQuery[key]?.map((v: string) => {
            beforeFilterNodes.push({
              field: key,
              label: v,
              value: v
            })
          })
        }
      }
      const defaultEleNodes: any = cloneDeep(defaultList)
      const filterNodeList: any = []
      if (chartKey !== 'TABLE') {
        fieldInfos.map((i: any) => {
          let eleIndex: number = 0
          let source: string = ''
          let isNumerical: boolean = false
          elements.map((ele: any, index: number) => {
            if (ele.field === i.type) {
              eleIndex = index;
              source = ele.name;
              isNumerical = ele.isNumerical
            }
          })
          const node = treeList?.filter((treeItem: any) => treeItem.key === i.fieldName)?.[0] || {}
          const actionLabel = isNumerical ? (mutexOperations().filter((item: any) => item?.field === i.action)?.[0]?.label || t("auays:action_lbl.count")) : undefined
          const newNode = {
            ...node,
            title: i.alias,
            baseTitle: node?.title,
            source,
            action: isNumerical ? (i.action || 'COUNT') : undefined,
            actionLabel,
            type: i.type,
            key: i.fieldName,
            info: { parentName: tableName },
          }
          defaultEleNodes[eleIndex].push(newNode)
          const filter = metricsFilters.filter((item: any) => item.fieldName === newNode.key && item.fieldType === newNode.type)?.[0] || {}
          const hasSameNode = filterNodeList.filter((item: any) => item.key === newNode.key && item.title === newNode.title).length > 0
          filterNodeList.map((item: any) => {
            if (item.key === newNode.key && item.title === newNode.title) return { ...item, showSource: true }
            else return item
          })
          filterNodeList.push({ ...newNode, showSource: hasSameNode, isNumber: !!newNode.action, filter })
        })
      }
      else {
        fieldInfos.map((i: any) => {
          const node = treeList?.filter((treeItem: any) => treeItem.key === i.fieldName)?.[0] || {}
          const actionLabel = mutexOperations().filter((item: any) => item?.field === i.action)?.[0]?.label || undefined
          const newNode = {
            ...node,
            title: i.alias,
            baseTitle: node?.title,
            source: t("auays:custom_chart_axis.column"),
            action: i.action || undefined,
            actionLabel,
            isNumber: !!i.action,
            type: i.type,
            key: i.fieldName,
            info: { parentName: tableName },
          }
          defaultEleNodes[0].push(newNode)
          const filter = metricsFilters.filter((item: any) => item.fieldName === newNode.key && item.fieldType === newNode.type)?.[0] || {}
          filterNodeList.push({ ...newNode, showSource: false, filter })
        })
      }
      // 添加新的标签页
      const newTab = {
        tabId: id,
        tabName: name,
        chartInfo: { ...newChartInfo },
        eleNodes: { ...defaultEleNodes },
        filterNodes: [...filterNodeList],
        filterList: [...beforeFilterNodes],
        isCreatCharts: true,
        isNoChange: true,
        oldParams: {
          chartInfo: { ...newChartInfo },
          eleNodes: { ...defaultEleNodes },
          filterNodes: [...filterNodeList],
          filterList: [...beforeFilterNodes],
        }
      }
      addChartTab(newTab)
    }
  }

  // 保存图表Api
  const { run: save } = useRequest(saveAuditMetrics, {
    manual: true
  })

  // 保存
  const saveChart = (saveId: string | number, values: any, elseOpe?: () => void) => {
    let baseEle: any = {
      baseInfo: chartInfo,
      baseNodes: eleNodes,
      baseFilterList: filterList,
      baseFilterNodes: filterNodes
    }
    if (saveId !== chartInfo.id) {
      const tabInfo = tabList.find((tab: any) => tab.tabId === saveId)
      const { chartInfo, eleNodes, filterList, filterNodes } = tabInfo
      baseEle = {
        baseInfo: chartInfo,
        baseNodes: eleNodes,
        baseFilterList: filterList,
        baseFilterNodes: filterNodes
      }
    }
    const { baseInfo, baseNodes, baseFilterList, baseFilterNodes } = baseEle
    const metricsFilters = baseFilterNodes.filter((item: any) => item?.filter?.tabType)?.map((item: any) => ({
      fieldName: item.key,
      fieldType: item?.type,
      ...item.filter
    }))
    const str = String(baseInfo.id)
    const isNewChart = str.startsWith('new_')
    const id = isNewChart ? undefined : baseInfo.id
    const baseParams = elementsFormat({
      chartInfo: baseInfo,
      filterList: baseFilterList,
      eleNodes: baseNodes,
      filterNodes: baseFilterNodes
    })
    const params = {
      ...baseParams,
      id: id || undefined,
      chartKey: baseInfo.chartType,
      name: values.name,
      remark: values.remark,
      fieldOrder: values.fieldOrder,
      valueOrder: values.valueOrder,
      metricsFilters,
    }
    save(params).then((data: any) => {
      message.success(t("auays:msg.metrics_save_success"))
      const currentId = chartInfo.id
      if (saveId === currentId) {
        const sId = data?.id
        setChartInfo({ ...chartInfo, id: sId, chartName: data?.name, chartRemark: data?.remark })
        const newTabList = tabList.map((item: any) => {
          if (item.tabId === currentId) {
            return {
              ...item,
              tabId: sId,
              chartInfo: {
                ...item.chartInfo,
                id: sId,
                chartName: data?.name,
                chartRemark: data?.remark
              },
              isNoChange: true,
              oldParams: {
                chartInfo: baseInfo,
                filterList: baseFilterList,
                eleNodes: baseNodes,
                filterNodes: baseFilterNodes
              }
            }
          }
          else return item
        })
        setTabList(newTabList)
      }
      elseOpe?.()
    })
  }

  // 删除
  const deleteChart = (id: number): Promise<void> => {
    return deleteRun(id)
  }

  // 获取图表数据
  const { run } = useRequest(getChartApi(chartInfo.chartType), {
    manual: true,
    formatResult: (data: any) => {
      setChartData(cloneDeep(data))
      setBaseData(cloneDeep(data))
      // 此处获取值，用于过滤条件基本筛选的展示
      const chartBaseInfo = CUSTOM_AUDIT_CHART_ELEMENT[chartInfo.chartType]
      const { dealType } = chartBaseInfo
      const nodes: any[] = []
      if (dealType === 1) {
        filterNodes.map((node: any) => {
          if (!node.action) {
            const fieldData = uniq(data.map((item: any) => item[node.key] || '-'))
            nodes.push({ ...node, fieldData })
          }
          else {
            const fieldData = uniq(data.map((item: any) => item['amount'] || 0))
            nodes.push({ ...node, fieldData })
          }
        })
      }
      if (dealType === 2 || dealType === 3) {
        const { standard = [], assistant = [] } = data
        filterNodes.map((node: any) => {
          if (node?.type === 'title') {
            const fieldData = uniq(standard.map((item: any) => item[node.key] || '-'))
            nodes.push({ ...node, fieldData })
          }
          else if (node?.type === 'x') {
            const fieldData = uniq([...standard.map((item: any) => item[node.key] || '-'), ...assistant.map((item: any) => item[node.key] || '-')])
            nodes.push({ ...node, fieldData })
          }
          else if (node?.type === 'y' && node.action) {
            const fieldData = uniq(standard.map((item: any) => item['amount'] || 0))
            nodes.push({ ...node, fieldData })
          }
          else {
            const fieldData = uniq(assistant.map((item: any) => item['amount'] || 0))
            nodes.push({ ...node, fieldData })
          }
        })
      }
      if (dealType === 4) {
        filterNodes.map((node: any) => {
          let fieldData: any[] = []
          if (node?.type === 'x') {
            fieldData = uniq(Object.keys(data).map((key: any) => key || '-'))
          }
          else {
            let values: any[] = []
            for (const item of Object.values(data)) {
              if (item && typeof item === 'object') { // 确保 item 是一个对象且有 keys
                for (const key of Object.keys(item)) {
                  values.push(key || '-');
                }
              }
            }
            fieldData = uniq(values)
          }
          nodes.push({ ...node, fieldData })
        })
      }
      setFilterNodes([...nodes])
    },
    onError: () => {
      setChartData([])
      setBaseData([])
    }
  })

  // 删除
  const { run: deleteRun } = useRequest(deleteAuditMetrics, {
    manual: true,
    onSuccess: (res: any, params: any) => {
      delChartTab(params[0])
      message.destroy()
      message.success(t("auays:msg.metrics_delete_success"))
    }
  })

  // 导出
  const downloadChart = () => {
    const baseParams = elementsFormat({
      chartInfo,
      filterList,
      eleNodes,
      filterNodes
    })
    const params = {
      ...baseParams,
      id: chartInfo.id || undefined,
      chartKey: chartInfo.chartType,
      name: chartInfo.chartName,
      remark: chartInfo.chartRemark,
    }
    //后缀名要写，不然文件名中如果有.csv，下载后文件无法确认类型
    const fileName = chartInfo.chartName ? chartInfo.chartName + '.csv' : ''
    download(params, fileName)
  }

  // 导出数据
  const { run: download } = useRequest(downloadAuditMetrics, {
    manual: true,
    onSuccess: () => {
      message.success(t("auays:msg.data_export_success"))
    }
  })

  // 将图标要素调整为参数所需格式
  const elementsFormat = (info: any): any => {
    const { chartInfo, filterList, eleNodes } = info
    const chartBaseInfo = CUSTOM_AUDIT_CHART_ELEMENT[chartInfo.chartType]
    const { elements } = chartBaseInfo
    const requiredIndex: number[] = [] //必填项索引
    elements.map((item: any, index: number) => {
      if (item.required) {
        requiredIndex.push(index)
      }
    })
    let isRequest: boolean = true
    requiredIndex.map((i: number) => {
      if (eleNodes[i].length === 0) isRequest = false
    })
    const cmdLogQuery: any = {}
    filterList?.map((item: any) => {
      if (item.field === 'date') {
        if (item.value && Array.isArray(item.value)) {
          // 时间区间时分秒调整
          const startTime = moment(item.value[0].format('YYYY-MM-DD 00:00:00'))
          const endTime = moment(item.value[1].format('YYYY-MM-DD 23:59:59'))
          cmdLogQuery.executeBeginMs = startTime.valueOf();
          cmdLogQuery.executeEndMs = endTime.valueOf();
        }
      }
      else {
        if (!cmdLogQuery[item.field]) cmdLogQuery[item.field] = []
        cmdLogQuery[item.field]?.push(item.value)
      }
    })
    if (isRequest) {
      if (chartInfo.chartType !== 'TABLE') {
        const fieldInfos: any[] = []
        let tableName: string = ''
        Object.keys(eleNodes).map((key: any) => {
          eleNodes[key].map((item: any) => {
            const field = elements[key]?.field
            tableName = item.info.parentName
            fieldInfos.push({
              fieldName: item.key,
              alias: item.title,
              type: field,
              action: item.action
            })
          })
        })
        const params = cloneDeep({
          fieldOrder: chartInfo.fieldOrder,
          valueOrder: chartInfo.valueOrder,
          tableName,
          fieldInfos,
          metricsFilters: [],
          cmdLogQuery
        })
        return params
      }
      else {
        const hasY = eleNodes[0].filter((item: any) => item.action).length === 1
        if (hasY) {
          const fieldInfos: any[] = []
          let tableName: string = ''
          eleNodes[0].map((item: any) => {
            const field = 'x'
            tableName = item.info.parentName
            fieldInfos.push({
              fieldName: item.key,
              alias: item.title,
              type: item.action ? 'y' : field,
              action: item.action ?? undefined
            })
          })
          const params = cloneDeep({
            fieldOrder: chartInfo.fieldOrder,
            valueOrder: chartInfo.valueOrder,
            tableName,
            fieldInfos,
            metricsFilters: [],
            cmdLogQuery
          })
          return params
        }
      }
    }
    else {
      return false
    }
  }

  useEffect(() => {
    const params = elementsFormat({
      chartInfo,
      filterList,
      eleNodes,
      filterNodes
    })
    if (params) {
      // 如果当前编辑的图表是保存过的，要判断是否发生过变化
      const str = String(chartInfo.id)
      if (!str.startsWith('new_')) {
        const tab = tabList.find((item: any) => item.tabId === chartInfo.id)
        if (tab && !isEmpty(tab?.oldParams)) {
          const oldParams = elementsFormat(tab.oldParams)
          let newTabList: any[] = tabList.map((item: any) => {
            if (item.tabId === chartInfo.id) {
              return {
                ...item,
                isNoChange: JSON.stringify(oldParams) === JSON.stringify(params)
              }
            }
            else {
              return item
            }
          })
          setTabList([...newTabList])
        }
      }
      run(params)
    }
    else {
      setChartData([])
      setBaseData([])
    }
  }, [eleNodes, filterList, chartInfo.fieldOrder, chartInfo.valueOrder])

  return {
    chartInfo,
    onInfoChange,
    eleNodes,
    onFieldInfoChange: setEleNodes,
    filterNodes,
    baseData,
    onfilterChange: setFilterNodes,
    chartData,
    filterList,
    onPreFilterChange: setFilterList,
    saveChart,
    deleteChart,
    initChart,
    onTreeDataChange: setTreeData,
    reset,
    downloadChart,
    tabList,
    addChartTab,
    delChartTab,
    onTabChange
  }
}

