import { message } from "antd";
import { cloneDeep, isEmpty } from "lodash";
import { useEffect, useState } from "react";
import { deleteAuditMetrics, exportAuditMetrics, getMetricsResultBySql, postSaveSqlMetrics } from "src/api";
import { CUSTOM_AUDIT_CHART_ELEMENT } from "src/pageTabs/audit/customAuditMetrics/constants";
import { checkChartCondition, createChartData } from "src/pageTabs/audit/sqlAuditMetrics/unit";
import { useRequest } from "./useRequest";
import { useTranslation } from "react-i18next";

// 自定义审计指标（sql模式）hook context
export const useCustomSqlAudit = (): any => {
  const [sqlAuditInfo, setSqlAuditInfo] = useState<any>({
    id: 'sql_0', //指标id
    sqlMetricsName: '',//指标名称
    sqlMetricsRemark: '',//指标备注
    sql: '',//sql语句
  }) //图表类型
  const { t } = useTranslation()
  const [resColumns, setResColumns] = useState<any[]>([]) // 结果集列名
  const [resDataSource, setResDataSource] = useState<any[]>([]) // 结果集数据
  const [metricsDisplayType, setMetricsDisplayType] = useState<string>('TABLE') // 结果集指标展示效果
  const [editorSql, setEditorSql] = useState<string>() //编辑器正在编辑的语句
  const [chartInfo, setChartInfo] = useState<any>({
    type: undefined, //图表类型
    elementList: [], // 图表绑定信息
    data: {}, //图表数据
    dataFormat: {} //非数值轴的数据格式
  }) //图表绑定信息
  const [tabList, setTabList] = useState<any[]>([
    {
      tabId: 'sql_0',
      isNoChange: true, // 用来判断打开的保存过的图表是否发生过修改
      sqlAuditInfo,
      oldParams: {}, // 保存过的图表的存储参数
      chartInfo,
      displayType: 'TABLE',
      editorSql: ''
    }
  ]); // sql指标tab列表

  // 图表的data不存在时，默认将展示效果切换回表格
  useEffect(() => {
    if (isEmpty(chartInfo?.data)) {
      setMetricsDisplayType('TABLE')
    }
  }, [chartInfo?.data])

  // 根据结果集数据更新图表数据
  useEffect(() => {
    if (chartInfo.type && !isEmpty(chartInfo.elementList)) {
      const chartBaseInfo = CUSTOM_AUDIT_CHART_ELEMENT[chartInfo.type]
      const canCreate = checkChartCondition(chartBaseInfo?.dealType, chartInfo.elementList)
      if (canCreate && resColumns.length > 0 && resDataSource.length > 0) {
        const data = createChartData(chartBaseInfo?.dealType, chartInfo.elementList, resColumns, resDataSource, chartInfo?.dataFormat || {})
        setChartInfo({ ...chartInfo, data })
      }
    }
  }, [resColumns, resDataSource])

  useEffect(() => {
    if (tabList?.length === 0) {
      setTabList([
        {
          tabId: 'sql_0',
          isNoChange: true,
          sqlAuditInfo,
          oldParams: {},
          chartInfo,
          displayType: 'TABLE',
          editorSql: ''
        }
      ])
    }
  }, [tabList])

  // 编辑器内sql语句变化，并尚未生成，则清空图表绑定内容
  useEffect(() => {
    if (editorSql !== sqlAuditInfo?.sql) {
      setChartInfo({
        type: undefined,
        elementList: [],
        data: {},
        dataFormat: {}
      })
      setResColumns([])
      setResDataSource([])
    }
  }, [editorSql])

  // 判断当前图表的存储参数是否发生过修改
  useEffect(() => {
    const id = String(sqlAuditInfo?.id)
    if (!id.startsWith('sql_')) {
      const other = cloneDeep(chartInfo)
      delete other?.data
      const curParams = {
        chartInfo: other,
        sqlAuditInfo,
      }
      const tab = tabList.find((item: any) => item.tabId === sqlAuditInfo?.id)
      if (tab && !isEmpty(tab?.oldParams)) {
        const oldParams = tab.oldParams
        let newTabList: any[] = tabList.map((item: any) => {
          if (item.tabId === sqlAuditInfo?.id) {
            return {
              ...item,
              isNoChange: JSON.stringify(oldParams) === JSON.stringify(curParams) && editorSql === sqlAuditInfo?.sql
            }
          }
          else {
            return item
          }
        })
        setTabList([...newTabList])
      }
    }
  }, [sqlAuditInfo, chartInfo, editorSql])


  // 保存图表Api
  const { run: save } = useRequest(postSaveSqlMetrics, { manual: true })

  // 根据sql查询结果集数据
  const { loading: resLoading, run: getDataBySql } = useRequest(getMetricsResultBySql, {
    manual: true,
    onSuccess: (res: any, params: any) => {
      handleSelectData(res)
      onInfoChange({ sql: params[0]?.sql })
    }
  });

  // 导出数据
  const { run: download } = useRequest(exportAuditMetrics, {
    manual: true,
    onSuccess: () => {
      message.success(t("auays:msg.data_export_success"))
    }
  })

  // 删除
  const { run: deleteRun } = useRequest(deleteAuditMetrics, {
    manual: true,
    onSuccess: (res: any, params: any) => {
      delSqlAuditTab(params[0])
      message.destroy()
      message.success(t("auays:msg.metrics_delete_success"))
    }
  })

  // 重置
  const reset = () => {
    const index = tabList.length;
    setSqlAuditInfo({
      id: `sql_${index}`,
      sqlMetricsName: '',
      sqlMetricsRemark: '',
      sql: '',
    })
    setResDataSource([])
    setResColumns([])
    setChartInfo({
      type: undefined,
      elementList: [],
      data: {},
      dataFormat: {}
    })
    setEditorSql('')
    setMetricsDisplayType('TABLE')
  }

  // 将返回的select数据处理成表格数据
  const handleSelectData = (res: any) => {
    const { columnInfos, values } = res
    const resColumns = columnInfos?.map((item: any) => ({
      title: item?.columnName,
      dataIndex: item?.columnName,
      key: item?.columnName,
      width: 130,
      ellispe: true
    }))
    const resDataSource = values?.map((record: any, index: number) => {
      const formatRecord: any = {}
      Object.keys(record).forEach(key => {
        let value = record[key]
        // 后端的返回类型中会有boolean，boolean要做string处理，不然false不展示
        if (typeof value !== 'number') value = String(value)
        formatRecord[key] = value
      });
      return {
        ...formatRecord,
        key: String(index + 1),
      }
    })
    setResColumns(resColumns || [])
    setResDataSource(resDataSource || [])
  }

  // 修改当前sql审计指标信息
  const onInfoChange = (info: any) => {
    setSqlAuditInfo({ ...sqlAuditInfo, ...info })
  }

  // 同步编辑器的值
  const onEditorSqlChange = (sql: any) => {
    setEditorSql(sql)
  }

  // 修改审计指标的绑定图表信息
  const onChartInfoChange = (info: any) => {
    setChartInfo({ ...chartInfo, ...info })
  }

  // 当tab切换时，保存当前图表数据
  const saveChartOnChangeTab = (): any[] => {
    const currentId = sqlAuditInfo.id
    const newTabList = tabList.map((item: any) => {
      if (item.tabId === currentId) {
        return {
          ...item,
          tabName: sqlAuditInfo?.sqlMetricsName ?? '',
          displayType: metricsDisplayType,
          sqlAuditInfo: cloneDeep(sqlAuditInfo),
          chartInfo: cloneDeep(chartInfo),
          editorSql,
        }
      }
      else return item
    })
    return newTabList
  }

  // 调整列表中的 tabId 和 sqlAuditInfo.id
  const adjustListId = (list: any[]): any[] => {
    const currentId = sqlAuditInfo.id
    const newTabList = list.map((item: any, index: number) => {
      const str = String(item.tabId)
      if (str.startsWith('sql_')) {
        if (item.tabId === currentId) {
          setSqlAuditInfo({
            ...item.sqlAuditInfo,
            id: `sql_${index}`
          })
        }
        return {
          ...item,
          tabId: `sql_${index}`,
          sqlAuditInfo: {
            ...item.sqlAuditInfo,
            id: `sql_${index}`
          },
        }
      }
      else {
        return item
      }
    })
    return newTabList
  }

  // 切换图表标签页时更新图表数据
  const updateChartOnTabChange = (tabInfo: any) => {
    const { sqlAuditInfo, chartInfo, displayType, editorSql } = tabInfo
    const { sql } = sqlAuditInfo
    if (sql) { getDataBySql({ sql }) }
    setSqlAuditInfo({ ...sqlAuditInfo })
    setChartInfo({ ...chartInfo })
    setMetricsDisplayType(displayType)
    setEditorSql(editorSql)
  }

  // 切换当前审计指标的展示效果
  const onMetricsDisplayTypeChange = (type: string) => {
    setMetricsDisplayType(type)
  }

  // 添加审计指标标签页
  const addSqlAuditTab = (newTab: any) => {
    const newId = newTab.tabId
    const str = String(newId)
    if (!str.startsWith('sql_')) {
      const isOpen = tabList.filter((tab: any) => tab.tabId === newId).length > 0
      if (isOpen) {
        onTabChange(newId)
        return
      }
    }
    if (tabList.length === 10) {
      message.error(t("auays:msg.maximum_audit_metrics_tabs"))
      return
    }
    const list = saveChartOnChangeTab()
    const newTabList = adjustListId(list)
    newTabList.push(newTab)
    setTabList([...newTabList])
    if (str.startsWith('sql_')) {
      reset()
      setTimeout(() => {
        const index = tabList.length;
        setSqlAuditInfo({
          id: `sql_${index}`,
          sqlMetricsName: '',
          sqlMetricsRemark: '',
          sql: '',
        })
      }, 0)
    }
    else {
      reset()
      setTimeout(() => updateChartOnTabChange(newTab), 0)
    }
  }

  // 删除指标tab页
  const delSqlAuditTab = (delId: string | number) => {
    let newList = tabList
    if (delId === sqlAuditInfo.id) {
      let delIndex: number = 0
      let highLightId: number = 0
      tabList.map((tab: any, index: number) => {
        if (tab.tabId === delId) {
          delIndex = index
        }
      })
      if (delIndex === tabList.length - 1) {
        highLightId = tabList[delIndex - 1]?.tabId
      }
      else {
        highLightId = tabList[delIndex + 1]?.tabId
      }
      const tabInfo = tabList.find((tab: any) => tab?.tabId === highLightId)
      reset()
      setTimeout(() => updateChartOnTabChange(tabInfo), 0)
    }
    else {
      newList = saveChartOnChangeTab()
    }
    const list = newList.filter((tab: any) => tab.tabId !== delId)
    const newTabList = adjustListId(list)
    setTabList([...newTabList])
    message.success(t("auays:msg.delete_success"))
  }

  // 切换图表标签页
  const onTabChange = (tabId: string | number) => {
    if (tabId === sqlAuditInfo.id) return
    const newTabList = saveChartOnChangeTab()
    setTabList([...newTabList])
    const tabInfo = tabList.find((tab: any) => tab.tabId === tabId)
    reset()
    setTimeout(() => updateChartOnTabChange(tabInfo), 0)
  }

  // 保存SQL指标数据
  const saveSqlMetrics = (saveId: string | number, values: any, elseOpe?: () => void) => {
    let baseInfo: any = sqlAuditInfo
    let other: any = cloneDeep(chartInfo)
    if (saveId !== sqlAuditInfo.id) {
      const tabInfo = tabList.find((tab: any) => tab.tabId === saveId)
      const { chartInfo = {}, sqlAuditInfo } = tabInfo
      baseInfo = sqlAuditInfo
      other = chartInfo
    }
    const str = String(baseInfo.id)
    const isNewChart = str.startsWith('sql_')
    const id = isNewChart ? undefined : baseInfo.id
    delete other.data // 数据不需要传递到后台
    const params = {
      id: id,
      chartKey: 'CUSTOM_SQL',
      name: values.name,
      remark: values.remark,
      sql: baseInfo.sql,
      other,
    }
    save(params).then((data: any) => {
      message.success(t("auays:msg.metrics_save_success"))
      const currentId = sqlAuditInfo.id
      if (saveId === currentId) {
        const sId = data?.id
        setSqlAuditInfo({ ...sqlAuditInfo, id: sId, sqlMetricsName: data?.name, sqlMetricsRemark: data?.remark })
        const newTabList = tabList.map((item: any) => {
          if (item.tabId === currentId) {
            return {
              ...item,
              tabId: sId,
              sqlAuditInfo: {
                ...item.sqlAuditInfo,
                id: sId,
                sqlMetricsName: data?.name,
                sqlMetricsRemark: data?.remark
              },
              isNoChange: true,
              oldParams: {
                chartInfo: other,
                sqlAuditInfo: baseInfo
              }
            }
          }
          else return item
        })
        setTabList(newTabList)
      }
      elseOpe?.()
    })
  }

  // 导出
  const downloadSqlMetrics = () => {
    if (!sqlAuditInfo.sql || sqlAuditInfo?.sql !== editorSql) {
      message.warning(t("auays:msg.input_sql"))
      return
    }
    const { id } = sqlAuditInfo
    const strId = String(id)
    const param = {
      sql: sqlAuditInfo.sql || undefined,
      chartKey: 'CUSTOM_SQL',
      name: sqlAuditInfo.sqlMetricsName || t("auays:export_params.unnamed_metrics"),
    }
    download({
      models: [
        {
          // 新建的tab不传id，已保存的tab传id
          id: strId.startsWith('sql_') ? undefined : id,
          param
        }
      ]
    })
  }

  // 删除
  const deleteMetrics = (id: number): Promise<void> => {
    return deleteRun(id)
  }

  // 初始化指标数据
  const initSqlMetrics = (item: any) => {
    if (!isEmpty(item)) {
      const { id, name, remark, sql, other } = item
      const newSqlAuditInfo = {
        id,
        sqlMetricsName: name,
        sqlMetricsRemark: remark,
        sql,
      }
      const newTab = {
        tabId: id,
        tabName: name,
        sqlAuditInfo: { ...newSqlAuditInfo },
        chartInfo: { ...other },
        isNoChange: true,
        oldParams: {
          chartInfo: other,
          sqlAuditInfo: { ...newSqlAuditInfo }
        },
        displayType: 'TABLE',
        editorSql: sql
      }
      addSqlAuditTab(newTab)
    }
  }

  return {
    sqlAuditInfo,
    onInfoChange,
    editorSql,
    onEditorSqlChange,
    chartInfo,
    onChartInfoChange,
    resColumns,
    resDataSource,
    tabList,
    addSqlAuditTab,
    delSqlAuditTab,
    onTabChange,
    saveSqlMetrics,
    downloadSqlMetrics,
    deleteMetrics,
    metricsDisplayType,
    onMetricsDisplayTypeChange,
    initSqlMetrics,
    resLoading,
    getDataBySql
  }
}