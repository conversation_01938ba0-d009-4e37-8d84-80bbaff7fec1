import i18n from 'i18next';
import { useRequest } from './index'
import { message } from 'antd'

export const useDelete = ({
  deleteApi,
  refresh,
}: {
  deleteApi: any
  refresh?: any
}) => {
  const { fetches, run: runDelete } = useRequest(deleteApi, {
    manual: true,
    fetchKey: (keys) => {
      if (!keys.length) return ''
      return keys.length > 1 ? 'BATCH_DELETE' : keys[0].toString()
    },
    onSuccess: () => {
      message.success(i18n.t('deleteSuccess'))
      refresh && refresh()
    },
  })
  return { fetches, runDelete, batchFetch: fetches['BATCH_DELETE'] }
}
