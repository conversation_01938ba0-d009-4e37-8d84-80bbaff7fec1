import { useState, useEffect } from 'react'
import { getConnExecSqlRowNum } from 'src/api'

export const useGetConnExecRowNum = ( connectionId : number | string): [number] => {
  const [rowNum, setRowNum] = useState<number>(100)

  useEffect(() => {
    if (connectionId) {
      getConnExecSqlRowNum({ connectionId }).then((res) => {
        setRowNum(Number(res))
      }).catch((err) => {
        console.log('获取当前连接设置的查询分页===》', err);
      })
    }
  }, [connectionId])

  return [rowNum]
}
