import { useEffect } from 'react'
import { getRegisteredShortcuts } from 'src/util/shortcutRegistry'

export const useGlobalShortcuts = () => {
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      const shortcuts = getRegisteredShortcuts();
      //不需要监听的元素
      let isInputTarget = ['INPUT', 'TEXTAREA', 'SELECT'].includes(
        (e.target as HTMLElement).tagName
      )
      //一些元素不拦截快捷键，比如monaco输入框
      const isMonacoInput  = ['editor'].includes(
        (e.target as HTMLElement)?.ariaRoleDescription ?? ''
      )

      if (isMonacoInput) {
        isInputTarget = false
      }

      // 如果是输入框，直接返回，不拦截任何按键
      if (isInputTarget || !shortcuts?.length) {
        return
      }
  

      for (const shortcut of shortcuts) {
        const { keys, callback, options } = shortcut
        const isInputTarget = options?.ignoreInput
          ? ['INPUT', 'TEXTAREA', 'SELECT'].includes((e.target as HTMLElement).tagName)
          : false

        if (isInputTarget) continue

        let pressedKeys: string[] = []
        if (e.ctrlKey) pressedKeys.push('Ctrl')
        if (e.shiftKey) pressedKeys.push('Shift')
        if (e.altKey) pressedKeys.push('Alt')
        if (e.metaKey) pressedKeys.push('Meta')

        const key = e.key?.charAt(0).toUpperCase() + e.key.slice(1)
        if (key !== 'Unidentified') pressedKeys.push(key)

        const match = keys.every(k => pressedKeys.includes(k))
        console.log(match, '111', pressedKeys, shortcuts)
        if (match) {
          //直接阻止快捷键的默认行为
          e.preventDefault()
          e.stopPropagation()
          callback(e)
          break // 找到匹配项后停止遍历
        }
      }
    }

    // window.addEventListener('keydown', handleKeyDown)
    return () => {
      // window.removeEventListener('keydown', handleKeyDown)
    }
  }, [])
}