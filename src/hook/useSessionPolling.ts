import { useMemo, useEffect } from 'react'
import { useSelector } from './reactReduxHooks'
import { useRequest } from './useRequest'

export const useSessionPolling = () => {
  const { paneInfoMap, tabKeyList, tabInfoMap } = useSelector(
    (state) => state.queryTabs,
  )
  const isExecuting = useMemo(() => {
    for (const tabKey of tabKeyList) {
      if (tabInfoMap[tabKey].paneType !== 'monaco') continue
      if (paneInfoMap[tabKey].pending) {
        return true
      }
    }
    return false
  }, [paneInfoMap, tabInfoMap, tabKeyList])

  const { run, cancel } = useRequest(`/dms/common/heartbeat`, {
    pollingInterval: 1000 * 60,
  })

  useEffect(() => {
    // isExecuting ? run() : cancel()
  }, [cancel, isExecuting, run])
}
