{"bc_title.audit_analysis": "Audit Analysis", "bc_title.obj_audit": "Object Audit", "step.problem_type": "Problem Type", "step.select_object": "Select Object", "step.start_audit": "Start Audit", "tb_title.username": "Username", "tb_title.department": "Department", "tb_title.client_ip": "Client IP", "tb_title.connection": "Connection", "tb_title.database": "Database", "tb_title.table": "Table", "tb_title.operation": "Operation", "tb_title.ope_statement": "Operation Statement", "tb_title.ope_time": "Operation Time", "item_label.problem": "Problem", "item_label.select_data_source": "Select Data Source", "item_label.select_object": "Select Object", "item_label.time_range": "Time Range", "btn.select_problem": "Select Problem", "btn.start_audit": "Start Audit", "btn.export": "Export", "inp_ph.search_username_or_sql": "Please enter Username/Operation Statement", "rules_msg.select_question": "Please select a question", "ntf_title.task_obj_audit_export": "Task Object Audit Export", "ntf_desc.exe_completed_fl_genera": "Execution completed, file generated successfully", "btn.clear_all": "Clear All", "sele_ph.please_select": "Please Select", "btn.cancel": "Cancel", "btn.confirm": "Confirm", "inp_ph.search": "Search", "sele_ph.choose_conn": "Please Select Connection", "sele_ph.choose_data_source": "Please Select Data Source", "bc_title.audit_overview": "Audit Overview", "bc_title.user_audit": "User <PERSON>t", "inp_ph.search_username": "Search Username/Department", "tb_title.online_time": "Online Time", "tb_title.offline_time": "Offline Time", "tb_title.online_status": "Online Status", "tb_title.online_duration": "Online Duration", "tb.show_total": "Total {{total}} records", "tb_filter.time_asc": "Sort by Time Ascending", "tb_filter.time_desc": "Sort by Time Descending", "tb_rd.status.online": "Online", "tb_rd.status.offline": "Offline", "bc_title.user_details": "User Details", "detail_label.online_time": "Online Time: ", "detail_label.offline_time": "Offline Time: ", "detail_label.online_duration": "Online Duration: ", "detail_label.username": "Username: ", "detail_label.client_ip": "Client IP: ", "detail_label.department": "Department: ", "tab.permissions": "Permissions", "tab.sql_details": "SQL Details", "tab.operation_records": "Operation Records", "tb_title.function_name": "Function Name", "tb_title.operation_details": "Operation Details", "tb_title.execution_result": "Execution Result", "tb_title.execution_time": "Execution Time", "tb_rd.exe_status.success": "Success", "tb_rd.exe_status.failure": "Failure", "tb_filter.res_flag.success": "Success", "tb_filter.res_flag.failure": "Failure", "btn.permission_details": "Permission Details", "srh_ph.perms_level_resource": "Enter Permission Level/Resource", "srh_ph.ds_ob_sql": "Enter Data Source/Object/SQL Statement", "srh_ph.operation": "Enter Operation", "tb_title.permission_type": "Permission Type", "tb_title.permission_level": "Permission Level", "tb_title.resource": "Resource", "tb_title.resource_type": "Resource Type", "tb_title.role": "Role", "tb_title.effective_time": "Effective Time", "tb_title.authorizer": "Authorizer", "tb_title.authorization_source": "Authorization Source", "tb_title.authorization_ip": "Authorization IP", "tb_title.authorization_time": "Authorization Time", "tb_title.permission_details": "Permission Details", "tb_filter.perms_type.data_source_ope": "Data Source Operation Permission", "tb_filter.perms_type.sensitive_resource_access": "Sensitive Resource Access Permission", "tb_filter.perms_type.high_risk_ope": "High-Risk Operation Permission", "tb_filter.perms_type.tool": "Tool Permission", "tb_title.data_source": "Data Source", "tb_title.operation_object": "Operation Object", "tb_title.database_ip": "Database IP", "tb_title.database_port": "Database Port", "tb_title.data_version": "Data Version", "tb_title.start_time": "Start Time", "tb_title.end_time": "End Time", "tb_title.operation_type": "Operation Type", "tb_title.operation_result": "Operation Result", "tb_title.error_message": "Error Message", "tb_title.affected_rows": "Affected Rows", "tb_title.duration_ms": "Duration (ms)", "tb_title.source": "Source", "tb_filter.source.data_operation": "Data Operation", "tb_filter.source.data_change": "Data Modification", "tb_filter.source.batch_execution": "Batch Execution", "emp_desc.no_data_available": "No Data Available", "drawer_title.custom_list_items": "Custom List Items", "tb_title.linked_role": "Connection Role", "tb_filter.source.terminal_exe": "Terminal Execution", "bc_title.perms_dashboard": "Permission Dashboard", "btn.query": "Query", "btn.reset": "Reset", "item_label.username": "Username", "item_label.database": "Database", "item_label.permission_type": "Permission Type", "item_label.authorization_time": "Authorization Time", "sele_ph.username": "Please select a username", "sele_ph.permission_type": "Please select a permission type", "clg_err.form_reset_failure": "Form reset failed: >>", "clg_err.async_loading_failed": "Async loading failed", "sele_ph.database_element": "Please select a database element", "inp_ph.search_resource": "Search resource", "menu.custom_audit_metrics": "Custom Audit Metrics", "hd_stats.audit_data": "Total {{amount}} records · Sorted by {{sortType}} · Updated at {{lastTime}}", "tip_title.adm_display_settings": "Audit Metrics Display Settings", "card_title.user_ope_volume": "User Operation Volume", "tip_title.user_ope_records": "Total number of user operation records available for audit in the current system", "ope_stats.total_user_operations": "Total User Operations", "ope_stats.total_executed_statements": "Total Executed Statements", "ope_stats.total_system_operations": "Total System Operations", "ope_stats.successful_executed_statements": "Successful Executed Statements", "ope_stats.failed_executed_statements": "Failed Executed Statements", "ope_stats.sensitive_resource_operations": "Sensitive Resource Operations", "ope_stats.high_risk_operations": "High-Risk Operations", "ope_stats.privilege_escalation_operations": "Privilege Escalation Operations", "card_title.app_ope_volume": "Application Operation Volume", "tip_title.app_side_ope_volume": "Total application-side operation volume in the current system", "app_ope_stats.total_execution_statements": "Total Executed Statements", "app_ope_stats.successful_execution_statements": "Successful Executed Statements", "app_ope_stats.failed_execution_statements": "Failed Executed Statements", "card_title.active_users": "Active Users", "tip_title.user_count": "Total number of users available for audit in the current system", "user_stats.total_users": "Total Users", "user_stats.current_users": "Current Users", "stats_dim.today": "Today", "stats_dim.this_month": "This Month", "user_chart.online": "Online", "user_chart.offline": "Offline", "chart_cont.month_growth": "Month-over-Month", "chart_cont.month_growth_br": "Growth", "stats_dim.all": "All", "tab.all": "All", "tab.high_risk": "High Risk", "rdo_cont.time": "Time", "rdo_cont.data_object": "Data Object", "rdo_cont.organization_structure": "Organization Structure", "display.switch_to_chart": "Switch to Chart", "btn.batch_export": "Batch Export", "sp_label.path": "Path:", "card_extra.cur_query_cdt": "Current Query Conditions:", "card_title.sql_exe_count_ost": "SQL Execution Count (Operation Statement Type)", "sele_ph.operation_type": "Operation Type", "path_lbl.all_months": "All Months", "path_lbl.all_departments": "All Departments", "btn.expand_all": "Expand All", "unit.times": " times", "time_unit.quarter": "Quarter {{q}}", "time_unit.month": " Month", "time_unit.day": " Day", "time_unit.hour": " Hour", "tb_title.total_duration": "Total Duration", "msg.export_success": "Export Successful", "clg_err.batch_export_failure": "Batch Export Failed =>>>>", "md_title.confirm_export": "Confirm Export", "check.select_all": "Select All", "filter_als.data_source_type": "Data Source Type", "filter_als.company": "Company", "time_unit.year": " Year", "card_title.average_sql_exe_duration_ost": "Average SQL Execution Duration (Operation Statement Type)", "chart_title.slow_sql_top10": "Slow SQL Top 10", "chart_title.sql_exe_count_dt": "SQL Execution Count (Database Type)", "chart_title.user_usage_volume": "User Usage Volume", "chart_title.sql_exe_count_dc": "SQL Execution Count (Database Connection)", "chart_title.user_auth_status": "User Authorization Status", "chart_title.database_type": "Database Type", "chart_title.average_sql_exe_duration": "Average SQL Execution Duration", "tb_title.serial_number": "No.", "tb_title.account": "Account", "tb_title.user_name": "User Name", "tb_title.time_consume": "Time Consumed", "filter_time.today": "Today", "sele_ph.database_types": "Database Types", "filter_time.seven_days": "7 Days", "filter_time.thirty_days": "30 Days", "filter_time.date": "Date", "chart_als.number_of_users": "Number of Users", "sele_ph.conn_name": "Connection Name", "sele_ph.database_name": "Database Name", "chart_lbl.approved": "Approved", "chart_lbl.rejected": "Rejected", "tb_title.database_type": "Database Type", "tb_title.statement_count": "Statement Count", "tb_title.percentage": "Percentage", "axis_name.value_axis": "Value Axis", "tb_title.total": "Total", "filter_lbl.today": "Today", "filter_lbl.last_7_days": "Last 7 Days", "filter_lbl.last_month": "Last Month", "filter_lbl.last_three_months": "Last Three Months", "btn.sure": "Confirm", "item_label.time": "Time", "item_label.data_source_type": "Data Source Type", "item_label.data_source_name": "Data Source Name", "item_label.department": "Department", "item_label.account": "Account", "custom_chart_type.stacked_column_chart": "Stacked Column Chart", "custom_chart_type.stacked_bar_chart": "Stacked Bar Chart", "custom_chart_type.clustered_bar_chart": "Clustered Bar Chart", "custom_chart_type.clustered_column_chart": "Clustered Column Chart", "custom_chart_type.percentage_stacked_bar_chart": "Percentage Stacked Bar Chart", "custom_chart_type.percentage_stacked_column_chart": "Percentage Stacked Column Chart", "custom_chart_type.line_chart": "Line Chart", "custom_chart_type.area_chart": "Area Chart", "custom_chart_type.stacked_area_chart": "Stacked Area Chart", "custom_chart_type.percentage_stacked_area_chart": "100% Stacked Area Chart", "custom_chart_type.line_and_stacked_column_chart": "Line and Stacked Column Chart", "custom_chart_type.line_and_clustered_column_chart": "Line and Clustered Column Chart", "custom_chart_type.ribbon_chart": "Ribbon Chart", "custom_chart_type.waterfall_chart": "Waterfall Chart", "custom_chart_type.pie_chart": "Pie Chart", "custom_chart_type.doughnut_chart": "Doughnut Chart", "custom_chart_type.table": "Table", "custom_chart_type.matrix": "Matrix", "custom_chart_axis.x_axis": "X Axis", "custom_chart_axis.y_axis": "Y Axis", "custom_chart_axis.legend": "Legend", "custom_chart_axis.row_y_axis": "Row Y Axis", "custom_chart_axis.column_legend": "Column Legend", "custom_chart_axis.auxiliary_y_axis": "Auxiliary Y Axis", "custom_chart_axis.column_y_axis": "Column Y Axis", "custom_chart_axis.value": "Value", "custom_chart_axis.column": "Column", "custom_chart_axis.row": "Row", "ope_lbl.delete_field": "Delete Field", "ope_lbl.rename_for_this_visual": "<PERSON><PERSON> for This Visual", "ope_lbl.move": "Move", "ope_lbl.sort": "Sort", "ope_lbl.ascending": "Ascending", "ope_lbl.descending": "Descending", "ope_lbl.sum": "Sum", "ope_lbl.average": "Average", "ope_lbl.minimum": "Minimum", "ope_lbl.maximum": "Maximum", "ope_lbl.standard_deviation": "Standard Deviation", "ope_lbl.count_non_repeated": "Count (Non-Repeated)", "ope_lbl.count": "Count", "ope_lbl.show_value_as": "Show Value As", "ope_lbl.no_calculation": "No Calculation", "ope_lbl.percentage_of_total": "Percentage of Total", "ope_lbl.no_summary": "No Summary", "ope_lbl.up": "Up", "ope_lbl.down": "Down", "ope_lbl.to_top": "To Top", "ope_lbl.to_bottom": "To Bottom", "msg.custom_chart_display_modified_successfully": "Custom audit metrics display modified successfully", "msg.max_five_custom_chart": "A maximum of 5 custom audit metrics can be selected for display", "drawer_title.charts_display_settings": "Audit Metrics Display Settings", "div_lbl.system_default_metrics": "System Default Metrics", "div_lbl.custom_metrics": "Custom Metrics", "inp_ph.search_custom_metrics_name": "Search custom metric name", "msg.input_sql": "Please enter SQL statement and generate data first", "md_title.unsaved_confirm_navigation": "There are unsaved audit metric details. Do you want to navigate away?", "md_content.cleared_confirm": "After confirmation, all audit metric details will be cleared", "btn.open": "Open", "btn.save": "Save", "btn.to_smart_mode": "Switch to Smart Mode", "bc_title.custom_audit_metrics_sql_mode": "Custom Audit Metrics (SQL Mode)", "sql_mode_tip.data_source": "Data Source", "sql_mode_tip.department": "Department", "sql_mode_tip.role": "Role", "sql_mode_tip.user_id": "User ID", "sql_mode_tip.user_name": "User Name", "sql_mode_tip.execution_client_type": "Execution Client Type", "sql_mode_tip.execution_result_type": "Execution Result Type", "sql_mode_tip.is_manual_transaction_display": "Manual Transaction Display", "btn.generate": "Generate", "msg.enter_sql": "Please enter SQL statement", "div_lbl.custom_sql_area": "Custom SQL Area", "tip_title.dollar_to_refer": "Please enter the $ reference parameter", "editor.format": "Format", "btn.delete": "Delete", "btn.smart_chart": "Smart Chart", "display_lbl.switch_to": "Switch to", "display_lbl.chart": "Chart", "display_lbl.table": "Table", "img_lbl.sql_query": "Enter SQL query to audit data", "img_lbl.generate_chart": "Bind data to generate smart chart", "df_remark.no_remark": "No remarks", "df_name.unnamed_metrics": "Unnamed Metric", "md_title.save_settings": "Save Settings", "item_label.metrics_name": "Audit Metric Name", "item_label.remark": "Remark", "inp_ph.enter_name": "Enter audit metric name", "inp_ph.enter_remark": "Enter remark", "clg_err.save_form_val_err": "===> Save form validation error", "btn.clear": "Clear", "md_title.clear_sure": "Audit metric not saved, clear?", "md_title.confirm_delete": "Are you sure you want to delete this metric?", "msg.error_bound_data": "Bound data does not meet the conditions to create the chart", "div_lbl.chart_elements": "Chart Elements", "div_lbl.chart_type": "Chart Type", "msg.max_selection_limit": "You can select up to {{max}}", "ele_info.ele1": "Required, up to three columns can be selected. If 'Automatically generate based on result count' is selected, columns cannot be bound.", "ele_info.ele2": "Required, can bind columns or select 'Auto-generate value'", "ele_info.ele3": "Required", "ele_info.ele4": "Required, up to five numeric columns can be selected", "ele_info.ele5": "Optional", "ele_info.ele6": "Cannot be selected because Y-axis is a custom value", "ele_info.ele7": "Cannot be selected because the legend has been chosen", "ele_info.ele8": "Cannot be selected because the auxiliary Y-axis has been chosen", "sele_lbl.generate_based": "Automatically generate based on result count", "sele_lbl.auto_generate_value": "Auto-generate value", "format_unit.none": "None", "format_unit.million": "Million", "format_unit.tens_of_millions": "Ten million", "format_unit.billion": "Billion", "format_unit.trillion": "<PERSON><PERSON><PERSON>", "format_unit.percentage": "Percentage", "format_unit.custom": "Custom", "format_pre.none": "None", "format_pre.ordinal": "No.", "inp_ph.generate_auto_based": "Automatically generate based on unit", "inp_ph.enter_custom_content": "Enter custom content", "tip_title.quick_selection_or_input": "Supports quick selection or custom input", "tip_title.custom_suffix": "Select 'Custom' to input custom suffix", "sele_ph.select_or_input": "Select or input custom content", "div_lbl.data_format": "Data Format", "div_lbl.prefix": "Prefix", "div_lbl.unit": "Unit", "div_lbl.suffix": "Suffix", "div_lbl.example": "Example", "btn.do_not_save": "Do not save", "md_title.confirm_save": "Confirm save", "md_title.audit_metrics_design_changed": "Audit metric design has changed", "md_content.audit_metrics_unsaved": "Audit metric not saved, save before exiting?", "md_content.design_changed_exit": "Design has changes, save before exiting?", "df_name.unnamed_chart": "Unnamed Chart", "drawer_title.all_metrics": "All Metrics", "div_lbl.recently_saved": "Recently Saved:", "emp_desc.no_items": "No items", "axis_name.value": "Value", "axis_name.bound_column": "Bound Column", "val_info.non_numeric_data_warn": "Non-numeric data found in 'Value', unable to generate", "emp_desc.no_metric_data": "No metric data found, unable to generate chart", "emp_desc.invalid_bound_data": "Bound metric data does not meet chart creation conditions", "emp_desc.no_bound_smart_chart": "Metric not bound to smart chart", "msg.chart_creation_requirement_not_met": "Chart creation required elements are not bound, cannot save", "guide_title.visual_object_type_area": "Visual Object Type Area", "guide_title.data_area": "Data Area", "guide_title.configuration_area": "Configuration Area", "guide_title.visual_display_area": "Visual Display Area", "guide_detail.visual_object_type_area": "Choose the appropriate visualization object here and proceed to the next editing step", "guide_detail.data_area": "Existing data in the system, can be dragged to the configuration data area", "guide_detail.configuration_area": "Drag data from the data area into the configuration area and select appropriate dimensions for setup", "guide_detail.visual_display_area": "Real-time view of the visual chart based on the data settings above", "md_title.un_saved_metrics": "There are unsaved audit metric details, do you want to proceed?", "md_content.confirm_warning": "All unsaved audit metric information will be cleared if confirmed", "bc_title.custom_audit_metrics_smart_mode": "Custom Audit Metrics (Smart Mode)", "btn.switch_to_sql_mode": "Switch to SQL Mode", "div_lbl.visual_object_type": "Visual Object Type", "div_lbl.all_data": "All Data", "ele_title_lbl.count_of": "Count of {{title}}", "ele_title_lbl.of": "{{action<PERSON><PERSON><PERSON>}} of {{baseTitle}}", "action_lbl.count": "Count", "msg.mutually_exclusive": "Mutually exclusive with [{{target}}], cannot move", "msg.mutually_exclusive_with_fields": "[{{target}}] is mutually exclusive with [{{mutex}}] and contains fields, cannot move", "msg.no_multiple_selection": "[{{target}}] does not support multiple selection, cannot move", "msg.duplicate_fields": "Duplicate fields found in [{{target}}], cannot move", "msg.filter_ope_not_available": "Chart not generated yet, filtering operation is unavailable", "ele_title.of_action": "{{<PERSON><PERSON><PERSON><PERSON>}} of", "ele_tip.mutually_exclusive": ", mutually exclusive with [{{mutex}}]", "ele_tip.numerical_operation_required": ", at least one numerical operation is required", "ele_tip.multiple_selection_limit_ten": "Supports multiple selection, up to 10 items", "ele_tip.multiple_selection_limit_three": "Supports multiple selection, up to 3 items", "ele_tip.required": "Required", "ele_tip.optional": "Optional", "ele_tip.no_multiple_selection": "No multiple selection", "div_lbl.drag_metrics_here": "Drag metric fields from the left here", "div_lbl.auto_generate": "No drag required, auto-generated", "div_lbl.filter_condition": "Filter Conditions", "tip_title.filter_after_generation": "After chart generation, you can filter metric field data", "filter_title.basic_filter": "Basic Filter: {{values}}", "filter_title.top_n": "Top {{n}}", "filter_title.advanced_filter": "Advanced Filter: {{values}}", "filter_title.and": "AND", "filter_title.or": "OR", "post_filter_ope.contains": "Contains", "post_filter_ope.not_contain": "Does not contain", "post_filter_ope.starts_with": "Starts with", "post_filter_ope.not_start_with": "Does not start with", "post_filter_ope.equals": "Equals", "post_filter_ope.not_equal": "Not equal", "post_filter_type.basic_filter": "Basic Filter", "post_filter_type.advanced_filter": "Advanced Filter", "post_filter_type.top_n": "Top N", "msg.integer_only": "Only positive integers are allowed", "inp_ph.search_field": "Search fields", "div_lbl.display_rule": "Display items with the following values", "inp_ph.numeric_value": "Enter numeric value", "inp_ph.filter_content": "Enter filter content", "sele_ph.filter_method": "Select filter method", "div_lbl.filter_type": "Filter Type", "msg.val_table_display": "Chart not generated, cannot display as table", "msg.chart_create_require_val_no_save": "Required elements for chart creation not bound, cannot save", "msg.val_data_export": "Chart not generated, cannot export data", "btn.show_table": "Show as table", "btn.export_data": "Export data", "img_lbl.generate_view_obj": "Generate view object using data", "img_lbl.drag_fields": "Drag fields from the data area to the configuration area for data configuration", "md_title.chart_design_changed": "Chart design has changed", "md_content.chart_design_changed_exit": "Current chart design has unsaved changes, do you want to save before exiting?", "md_content.chart_unsaved_exit": "Chart is not saved, do you want to save before exiting?", "bc_title.critical_detail": "High-risk Details", "bc_title.sensitive_resource_ope_detail": "Sensitive Resource Operation Details", "bc_title.statement_detail": "Statement Details", "clg.download_ope_res_log_failed": "Failed to download operation result log", "tip.terminal_exe_statement_err": "Unable to fetch terminal executed statement", "bg_txt.execution_successful": "Execution successful", "tb_render.manual_mode": "Manual mode", "tb_render.automatic_mode": "Automatic mode", "rd_lbl.execution_success": "Execution successful", "rd_lbl.execution_failure": "Execution failed", "ntf_title.task_statement_detail_export": "Task statement detail export", "ntf_desc.exe_complete_fl_generated": "Execution complete, file generated successfully", "sele_ph.user": "User", "sele_ph.database_type_conn": "Database type, connection", "sele_ph.statement_type": "Statement type", "sele_ph.executor_type": "Executor type", "inp_ph.ope_statement_audit_id": "Enter operation statement / audit ID for search", "exe_type.all": "All", "exe_type.execution_editor": "Execution editor", "exe_type.terminal_editor": "Terminal editor", "pop_title.confirm_export": "Are you sure you want to export the current filter results?", "rpk_ph.start_date": "Start date", "rpk_ph.end_date": "End date", "desc_lbl.operation_sql": "Operation SQL", "desc_lbl.execution_sql": "Execution SQL", "desc_lbl.associated_sql": "Associated SQL", "desc_lbl.associated_authorization": "Associated authorization", "desc_lbl.operation_type": "Operation type", "desc_lbl.authorizer": "Authorizer", "desc_lbl.authorization_time": "Authorization time", "bc_title.operation_record": "Operation Records", "bg_txt.execution_failure": "Execution failure", "tb_title.user": "User", "tb_title.operation_detail": "Operation detail", "tb_title.client_IP": "Client IP", "tb_title.operation_time": "Operation time", "ntf_title.task_ope_record_export": "Task operation records export", "emp_desc.empty": "(empty)", "md_title.details": "{{name}} details", "bc_title.overprivileged_operation": "Unauthorized Operations", "rdo_lbl.sql_overprivilege": "SQL overprivilege", "rdo_lbl.operation_overprivilege": "Operation overprivilege", "inp_ph.ope_statement": "Operation statement", "bc_title.app_statement_details": "Application statement details", "tip_title.cannot_fetch_terminal_exe_sql": "Cannot fetch terminal execution SQL", "item_label.statement_type": "Statement type", "item_label.date": "Date", "tb_title.application_ip": "Application IP", "tb_title.database_account": "Database account", "tb_title.operation_statement": "Operation statement", "tb_title.statement_type": "Statement type", "tb_title.time_consume_ms": "Time consumed (ms)", "msg.maximum_chart_tabs_limit": "You can only open up to 10 chart tabs", "msg.delete_success": "Deletion successful", "msg.metrics_save_success": "Audit metric saved successfully", "msg.metrics_delete_success": "Audit metric deleted successfully", "msg.data_export_success": "Data export successful", "chart_als.operation_volume": "Operation Volume", "unit.items": " items", "msg.maximum_audit_metrics_tabs": "You can only open up to 10 audit metric tabs", "export_params.unnamed_metrics": "Unnamed_Metric", "dp_ph.start_time": "Start time", "dp_ph.end_time": "End time", "pop_title.confirm_permission_deletion": "Confirm deletion of {{userName}} user's '{{permissionTemplate}}' permission?", "item_title.permission_level": "Permission Level", "item_title.resource_type": "Resource Type", "item_title.authorizer": "Authorizer", "item_title.effective_time": "Effective Time", "opt_lbl.permanent": "Permanent", "opt_lbl.custom_time_period": "Custom Time Period", "range_tip.today": "Today", "sele_ph.select_resource_type": "Please select a resource type", "sele_ph.select_authorizer": "Please select an authorizer", "analysis_tip.data_delay": "The audit data is delayed. Please try again later."}