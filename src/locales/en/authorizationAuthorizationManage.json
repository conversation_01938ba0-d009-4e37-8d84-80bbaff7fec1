{"clickCreatePermissionSet": "Click Create Permission Set", "enterCreateEditPolicy": "Enter Create/Edit Policy", "clickEditPolicy": "Click edit policy,set effective users for permission set", "modifySuccess": "Modification Successful", "enterPermissionSetName": "Please Enter Permission Set Name", "permissionSetName": "Permission Set Name", "modifyPermissionSetName": "Modify Permission Set Name", "complete": "Complete", "nextStep": "Next Step", "allPermissionSets": "All Permission Sets", "databaseManagement": "Database Management", "autoAuthorization": "Automatic Authorization", "permissionSetList": "Permission Set List", "currentRole": "Your Current Role Is", "noPermissionForAutoAuthorization": "No Permission for [Automatic Authorization]", "createPermissionSetAgain": "Create Permission Set", "enterPermissionSetNameAgain": "Please Enter Permission Set Name", "permissionSetDetails": "Permission Set Details", "enterEffectConditions": "Enter Effective Conditions for Permission Set", "testSuccess": "Test Successful", "addQueryFailed": "Add Query Failed: ", "setSuccess": "Setting Successful", "confirmDelete": "Are you sure you want to delete?", "statusModifySuccess": "Status modification successful", "modify": "Modify", "delete": "Delete", "creator": "Creator", "creationTime": "Creation Time", "status": "Status", "enable": "Enable", "disable": "Disable", "wantToKnowMore": "Want to know more, click", "downloadHere": "Download Here", "exampleTemplate": "Example Template", "condition": "Condition", "enterCondition": "Please Enter Condition", "save": "Save", "cancel": "Cancel", "testCurrentDateUsers": "Test which users meet policy conditions on current date", "effectiveUsers": "Effective Users", "noUsersMeetConditions": "No Users Meet Conditions", "test": "Test", "policy": "Policy", "savePolicyFailed": "Save policy failed: ", "edit": "Edit", "operation": "Operation", "queryFailed": "Query Failed", "modifyToolPermissionFailed": "Modify tool permission failed: ", "operationSuccess": "Operation Successful", "operationFailed": "Operation failed:", "operationPermission": "Operation Permission", "submit": "Submit", "permissionName": "Permission Name", "remarks": "Remarks", "dataSourceType": "Data Source Type", "selectAll": "Select All", "operatorShouldUse": "Operator Should Use", "shouldEnterNumber": "Should Enter Number", "shouldEnterNumberThursday": "Should enter number, use on thursday", "shouldEnterNumberAfternoon": "Should enter number, use at 2:30 PM", "shouldEnterNumber27th": "Should enter number, use on 27th", "shouldEnterNumberDateTime": "Should enter number, use on July 27, 2023 at 2:30 PM", "example": "Example", "male": "Male", "confirm": "Confirm", "deleteSuccess": "Delete Successful", "deleteFailed": "Delete Failed", "username": "Username", "associatedResources": "Associated Resources", "associatedUsers": "Associated Users", "close": "Close", "securitySettingsPermissionQueryFailed": "Security settings permission query failed: ", "pleaseCompleteQueryTime": "Please complete query time", "pleaseCompleteModificationTime": "Please complete modification time", "pleaseCompleteAllowedUsageTime": "Please complete allowed usage time", "timeSettingSuccess": "Time Setting Success", "timeSettingError": "Time Setting Error", "saveFailed": "Save Failed", "function": "Function", "allowedUsageTime": "Allowed Usage Time", "default": "<PERSON><PERSON><PERSON>", "queryDenied": "Query Denied", "customTimePeriod": "Custom Time Period", "custom": "Custom", "allowedQueryTime": "Allowed Query Time", "allowedModificationTime": "Allowed Modification Time", "securitySettings": "Security Settings", "modifyPermissionLevelFailed": "Modify permission level failed： ", "confirmRemoveUser": "Confirm remove user?", "removeSuccess": "Remove Successful", "queryToolPermissionFailed": "Query tool permission failed： ", "connectRole": "Connect Role", "permissionLevel": "Permission Level", "toolPermission": "Tool Permission", "remove": "Remove", "confirmReset": "Confirm Reset?", "thisOperationWillSyncAllResourcesAtCurrentLevel": "This operation will sync all resources at the current level", "allPermissions": "All Permissions", "keepConsistentWithCurrentLevelPleaseOperateWithCaution": "Keep consistent with current level, please operate with caution", "reset": "Reset", "controlByOtherMeans": "Control by Other Means", "clearSelectedItems": "Clear Selected Items", "thisUserHasOtherPermissionsAtThisLevel": "This user has other permissions at this level", "addPermission": "Add Permission", "batchEditPolicy": "Batch Edit Policy", "addPolicy": "Add Policy", "selectUser": "Select User", "table": "Table", "tableName": "Table Name", "pleaseEnterPositiveInteger": "Please Enter a Positive Integer", "search": "Search", "currentlyOnPage": "Currently on Page", "page": "Page", "previousPage": "Previous Page", "nextPage": "Next Page", "jumpTo": "Jump to", "confirmClear": "Confirm Clear?", "thisOperationWillClearAllResources": "This operation will clear all resources in the permission set, please proceed with caution", "clear": "Clear", "resourceList": "Resource List", "selectResourceLevelThenSelectPermissions": "After selecting a resource level, select the permissions to be checked", "databaseObject": "Database Object", "selectRequiredPermissionsHere": "Check the required permissions here", "permissionSetResource": "Permission Set Resource", "displaySelectedResourcesAndSaveAllPermissions": "Display selected resources, and save all set permissions upon clicking complete", "resetSuccess": "Reset Success", "layeredQueryTreeStructureError": "Layered Query Tree Structure Error:", "permissionSetSaveSuccess": "Permission Set Save Success", "permissionSetSaveFailure": "Permission Set Save Failure", "permissionSetResetFailure": "Permission Set Reset Failure", "editPermissionSet": "Edit Permission Set", "createPermissionSet": "Create Permission Set", "pleaseEnterResourceName": "Please Enter Resource Name", "policyFileName": "/policy_US.txt"}