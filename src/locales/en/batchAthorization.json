{"permissionDetails": "Permission Details", "pleaseSelectUser": "Please select a user", "pleaseSelectEffectiveTime": "Please select effective time", "pleaseSelectTimePeriod": "Please select time period", "authorize": "Authorize", "selected": "Selected", "users": "Users", "permissionLevel": "Permission Level", "pleaseSelectPermissionLevel": "Please select permission level", "deleteSuccess": "Delete Success", "confirmBatchDelete": "Confirm batch delete of affected users and permissions under this resource?", "user": "User", "effectiveTime": "Effective Time", "permanent": "Permanent", "authorizer": "Authorizer", "authorizationTime": "Authorization Time", "operation": "Operation", "batchAuthorization": "Batch Authorization", "confirmDelete": "Confirm delete of affected users and permissions under this resource?", "ok": "OK", "cancel": "Cancel", "delete": "Delete", "affectedUsers": "Affected Users", "resourceName": "Resource Name", "batchDelete": "<PERSON><PERSON> Delete", "total": "Total", "items": "records", "clearSuccess": "Clear Success", "authorizationSuccess": "Authorization Success", "confirmClear": "Confirm clear of selected resource's affected users and permissions?", "batchClear": "Batch Clear", "selectAll": "Select All", "dataSourceType": "Data Source Type", "pleaseSelectDataSourceType": "Please select data source type", "currentRole": "Your current role is", "noOperationPermission": "No operation permission for [Batch Authorization]", "batchOperations": "Batch Operations", "resource": "Resource", "resourceType": "Resource Type", "connectionRole": "Connection Role", "permissions": "Permissions", "permissionType": "Permission Type", "startTime": "Start Time", "permissionSource": "Permission Source", "authorizationIP": "Authorization IP", "databaseManagement": "Database Management", "manualAuthorization": "Manual Authorization", "clear": "Clear", "organizationStructure": "Organization Structure", "view": "View"}