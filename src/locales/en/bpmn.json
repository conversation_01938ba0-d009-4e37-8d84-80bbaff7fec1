{"msg.process_struct_err_check_nodes": "Process structure error, please check the process nodes", "msg.missing_user_task_node": "Missing user task node", "msg.save_success": "Save successful", "msg.switch_to": "Switch to", "msg.process_model_success": "Process model successful", "msg.current_process_model_no_data": "Current process model has no data", "clg_err.fail_to_assign_form_key": "Failed to assign formKey", "clg_err.fail_to_assign_assignee": "Failed to assign assignee", "ntc_err_msg.tip": "Tip", "ntc_err_desc.import_fail": "Import failed", "mdl_title.tip": "Tip", "mdl_cont.unsaved_chg": "The current process node has changes that have not been saved, are you sure you want to switch?", "div_title.collapse": "Collapse", "btn_title.save_process": "Save Process", "btn_title.dl_bpmn_file": "Download BPMN File", "btn_title.terminate_cur_instance_process": "Terminate Current Instance Process", "msg.cur_instance_process_stop": "All processes for the current instance have been stopped", "pop_title.confirm_clear": "Confirm to clear", "pop_title.instance": "Instance", "pop_title.all_processes": "Clear all processes currently executing? (This action is irreversible!)", "val_msg.req_node_type": "User task {{name}}: Node type is a required field", "val_msg.req_approval_method": "User task {{name}}: Approval method is a required field", "val_msg.req_executor_strategy": "User task {{name}}: When an executor is configured, an executor assignment strategy must be selected", "cta_rdo.or_sign": "OR Sign: Any user in the approval group can approve for the process to proceed", "cta_rdo.specific": "Designated: The applicant designates that each connection be approved by", "cta_rdo.approver_group": "users in the approver group", "time_field.hours": "Hours", "time_field.minutes": "Minutes", "time_group.time_related_comp": "Time-related Components", "grp_entry_lbl.start_time": "Start Time", "grp_entry_desc.code_format": "ISO 8601 Format", "entry_lbl.mandatory_node": "Mandatory Node", "entry_opts_lbl.yes": "Yes", "entry_opts_lbl.no": "No", "entry_opts_lbl.none": "None", "entry_opts_lbl.parallel_mult": "Multiple People in Parallel", "entry_opts_lbl.approver_group": "Approver Group", "entry_opts_lbl.department": "Department", "entry_opts_lbl.individual": "Individual", "entry_opts_lbl.admin": "Administrator", "entry_opts_lbl.conn_admin": "Connection Administrator", "entry_tf_lbl.reminder_time": "Reminder Time", "flowbpmn1.level_1_approval": "Level 1 Approval", "flowbpmn1.level_2_approval": "Level 2 Approval", "flowbpmn1.level_3_approval": "Level 3 Approval", "flowbpmn1.level_4_approval": "Level 4 Approval", "flowbpmn1.level_5_approval": "Level 5 Approval", "flowbpmn2.initiate_approval": "Initiate <PERSON><PERSON><PERSON><PERSON>", "flowbpmn2.level_1_approval": "Level 1 Approval", "flowbpmn2.level_2_approval": "Level 2 Approval", "color_pop.enter_color_value": "Enter Color Value", "left_bar.collapse": "Collapse", "left_bar.expand": "Expand", "desc.last_approval_node_warning": "This node is the last approval node. No new approval nodes are allowed after this point, otherwise it cannot be saved.", "desc.completion_condition": "Completion condition: Completed instances / Total instances >= 0-1.0"}