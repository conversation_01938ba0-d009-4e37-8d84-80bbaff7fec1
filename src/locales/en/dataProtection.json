{"dataProtection.desens.editScanResult": "<PERSON> <PERSON><PERSON>", "dataProtection.desens.batchEdit.desensField": "Batch Edit Desensitization Fields", "dataProtection.desens.editDesensField": "Edit Desensitization Field", "dataProtection.desens.matchRule": "Match Rule", "dataProtection.desens.desensSource": "Desensitization Field Source", "dataProtection.desens.desensAlgorithm": "Desensitization Algorithm", "dataProtection.desens.desensAlgorithm.tip": "Please select a desensitization rule", "dataProtection.desens.desensAlgorithm.title": "Desensitization Algorithm Settings", "dataProtection.desens.desensPreview": "Desensitization Preview", "dataProtection.desens.startPosition": "Start Character Position", "dataProtection.desens.replaceCharacters": "Number of Characters to Replace", "dataProtection.desens.replaceWith": "Replace With", "dataProtection.desens.number": "NUM", "dataProtection.desens.dataType": "Data Type", "dataProtection.desens.range": "Range", "dataProtection.desens.date": "DATE", "dataProtection.desens.min": "Cannot be less than {{minVal}}%", "dataProtection.desens.to": "To", "dataProtection.desens.retain": "<PERSON><PERSON>", "dataProtection.desens.front": "Front", "dataProtection.desens.point": "Digits", "dataProtection.desens.encryptAlgorithm": "Encryption Algorithm", "dataProtection.desens.dataSimulationType": "Data Simulation Type", "dataProtection.desens.predesens": "Before Desensitization", "dataProtection.desens.afterDesens": "After Desensitization", "dataProtection.desens.userName": "Username", "dataProtection.desens.deptName": "Department", "dataProtection.desens.expr": "Effective Time", "dataProtection.desens.delete.tip": "Deleting will revoke this user's desensitization permissions for this data. Confirm deletion?", "dataProtection.desens.applyUserList": "Desensitization User Application List", "dataProtection.desens.connectionName": "Connection Name", "dataProtection.desens.object": "Object Name", "dataProtection.desens.columnName": "Column Name", "dataProtection.desens.ruleName": "Desensitization Rule", "dataProtection.desens.ruleParam": "Algorithm", "dataProtection.desens.source": "Source", "dataProtection.desens.applyUser": "Desensitization Application User", "dataProtection.desens.editPolicy": "Edit Policy", "dataProtection.desens.editPolicy.tip": "Users who meet the policy will not have the field desensitized", "dataProtection.desens.epolicy": "Policy", "dataProtection.desens.status": "Status", "dataProtection.desens.editPolicy.success": "Policy edited successfully", "dataProtection.desens.action.tip": "Confirm {{action}} {{count}} configurations?", "dataProtection.desens.action.content": "Deleting will also delete all desensitization permissions", "dataProtection.desens.status2": "Status:", "dataProtection.desens.search": "Please enter the field name/desensitization rule to search", "dataProtection.desens.densesStatus": {"ALL": "All", "TRUE": "Enabled", "FALSE": "Disabled"}, "dataProtection.desens.comments": "Comments", "dataProtection.desens.fieldType": "Field Type", "dataProtection.desens.example": "Data Sample", "dataProtection.desens.editRule": "Edit Rule", "dataProtection.desens.setRule": "Set Rule", "dataProtection.desens.ruleManage": "Desensitization Rule Management", "dataProtection.desens.guide.content": "Set the appropriate desensitization rules for sensitive fields here", "dataProtection.desens.searchField": "Search Field Name", "dataProtection.desens.desensFieldDetail": "Desensitization Field Details", "dataProtection.desens.setAlgorithm": "Set Desensitization Algorithm", "dataProtection.desens.preview": "Desensitization Preview", "dataProtection.dataInfo": "Desensitized Data", "dataProtection.dataInfo.resource": "Match Rule", "dataProtection.dataInfo.description": "Description", "dataProtection.dataInfo.checkMethod": "Match Method", "dataProtection.dataInfo.ruleName": "Rule Name", "dataProtection.dataInfo.checkMethod.reg": "Regular Expression", "dataProtection.dataInfo.example": "Desensitization Result", "dataProtection.dataInfo.deketeRule.tip": "Are you sure you want to delete this rule?", "dataProtection.dataInfo.guide.step2.title": "Desensitization Rule List", "dataProtection.dataInfo.guide.step1.detail": "Select the database object level here to set up desensitized data", "dataProtection.dataInfo.guide.step2.detail": "Select the desensitization rules you want to enable here. Once enabled, data matching the rules will be desensitized", "dataProtection.dataInfo.alert": "Specific settings are based on the actual hierarchy", "dataProtection.dataInfo.sampledCount": "Sampled Rows:", "dataProtection.dataInfo.sampleRate": "Hit Rate:", "dataProtection.dataInfo.search": "Enter rule name", "dataProtection.dataInfo.addRule": "Add Rule", "dataProtection.dataInfo.shortRuleName": "Rule Name", "dataProtection.dataInfo.shortRuleName.plac": "Enter Rule Name", "dataProtection.dataInfo.desc": "Rule Description", "dataProtection.dataInfo.resource.hint": "Enter Match Rule", "dataProtection.dataInfo.resource.plac": "Enter Regular Expression", "dataProtection.dataInfo.parameter": "Set built-in desensitization calculation parameters", "dataProtection.dataInfo.parameter.hitRate": "Hit rate", "dataProtection.filter.dataSourceType": "Data Source", "dataProtection.filter.objectPath": "Object Path", "dataProtection.filter.objectName": "Object Name", "dataProtection.filter.content": "Filter Rule", "dataProtection.filter.example": "Sample Data", "dataProtection.filter.comment": "Comment:", "dataProtection.filter.rule.tip": "Please enter the row filtering rule (condition expression): [Variable Name][><=!like][Value] The variable name is the field name, and the value is the user parameter. User parameters are referenced using $. Example: ID = '${USER.UserId}'", "dataProtection.filter.rule.addTip": "Add after successful testing", "dataProtection.filter.validateSql": "Validate SQL:", "dataProtection.filter.filterRule": "Row filter rule", "dataProtection.scan.guide.add": "Add <PERSON>", "dataProtection.scan.guide.content": "Add scan task at the selected level", "dataProtection.scan.jobName": "Task Name", "dataProtection.scan.path": "Task Path", "dataProtection.scan.desensRule": "Scanning Rule", "dataProtection.scan.cronExpression": "Task Schedule", "dataProtection.scan.jobRunTime": "Runtime Duration", "dataProtection.scan.jobStatus": "Task Status", "dataProtection.scan.lastExecuteTime": "Last Execution Time", "dataProtection.scan.sampledRows": "Sampled Rows", "dataProtection.scan.sampledRate": "Hit Rate", "dataProtection.scan.scanTaskManage": "Desensitization Scan Task Management", "dataProtection.scan.search": "Search Scan Tasks", "dataProtection.scan.connectionName": "Field Path", "dataProtection.scan.scanResult": "<PERSON><PERSON>", "dataProtection.scan.search.tableName": "Search by Table Name", "dataProtection.security.guide.step2.title": "Basic Settings", "dataProtection.security.guide.step2.content": "Set the allowed query time, allowed modification time, and import/export settings for the corresponding resources here.", "dataProtection.security.guide.step1.content": "Select the database object level that requires security settings here.", "dataProtection.security.guide.step3.title": "High-Risk Operation Settings", "dataProtection.security.guide.step3.content": "Click 'Edit' to set corresponding operations as high-risk and configure how to respond after triggering a high-risk operation.", "dataProtection.desensField": "Desensitization Field", "dataProtection.tab.scan": "Desensitization Scan", "dataProtection.tab.filter": "Row Filtering Settings", "dataProtection.tab.security": "Security Settings", "dataProtection.tab.security.submitSuccess": "Submission Successful", "dataProtection.tab.security.settingType": "Export Settings", "dataProtection.tab.security.settingType.plac": "Select Export Settings", "dataProtection.tab.security.count": "Row Count Limit", "dataProtection.tab.security.importSetting": "Text Import Settings", "dataProtection.tab.security.importSetting.plac": "Select Text Import Settings", "dataProtection.tab.security.size": "File Size Limit (KB)", "dataProtection.tab.security.childType": "Import Method", "dataProtection.tab.security.childType.plac": "Select Import Method", "dataProtection.tab.security.useTime.all": "<PERSON><PERSON><PERSON>", "dataProtection.tab.security.useTime.none": "Deny Modification", "dataProtection.tab.security.useTime.period": "Custom Time Period", "dataProtection.tab.security.formType": "Function", "dataProtection.tab.security.useTime": "Allowed Usage Time", "dataProtection.tab.security.searchTime": "Allowed Search Time", "dataProtection.tab.security.modifyTime": "Allowed Modification Time", "dataProtection.tab.security.searchTime.none": "<PERSON><PERSON>", "dataProtection.tab.security.operationType": "Operation Type", "dataProtection.tab.security.operationType.tip": "Custom settings exist on {{val}}", "dataProtection.tab.security.objectTypeName": "Object Type", "dataProtection.tab.security.value": "Row Count Limit", "dataProtection.tab.security.value.tip": "Row count limit only applies to UID (update, insert, delete) operations", "dataProtection.tab.security.level": "High-Risk Warning", "dataProtection.tab.security.level.tip": "Please select a high-risk warning", "dataProtection.tab.security.highRiskLevel": "High-Risk Level", "dataProtection.tab.security.highRiskLevel.tip": "Please select a high-risk level", "dataProtection.tab.security.clear": "Confirm Clear?", "dataProtection.tab.security.clear.tip": "Clearing will remove all settings for this operation at this level", "dataProtection.tab.security.editing": "Batch Editing in Progress...", "dataProtection.tab.security.search": "Search Operation Type", "dataProtection.tab.security.batchEdit": "<PERSON>ch Edit", "dataProtection.tab.security.batchClear": "Batch Clear", "dataProtection.tab.security.objectType": "Object Type:", "dataProtection.tab.security.objectType.plac": "Select Object Type", "dataProtection.tab.security.highType4": "Intercept and Prompt", "dataProtection.tab.security.highType1": "Prompt Only, No Intercept", "dataProtection.tab.security.highType2": "Intercept, Prompt, and Correct Data", "dataProtection.tab.security.highType3": "Intercept, Prompt, and Request High-Risk Operation", "dataProtection.tab.security.highLevel1": "Low", "dataProtection.tab.security.highLevel2": "Medium", "dataProtection.tab.security.highLevel3": "High", "dataProtection.delete.tip": "Are you sure you want to delete this log information?", "dataProtection.dense.denseRule": {"REPLACE": "REPLACE", "RANDOM_FLOATING": "RANDOM_FLOATING", "RANDOM_IN_RANGE": "RANDOM_IN_RANGE", "SUB": "SUB", "ENCRYPTION": "Encryption", "HIDE": "HIDE", "DATA_SIMULATION": "DATA_SIMULATION", "FILTER": "FILTER"}, "dataProtection.tab.scan.getSampleCheck.error": "Failed to get sample row count and hit rate from system settings - Basic Settings", "dataProtection.tab.scan.editScanTask": "<PERSON> <PERSON><PERSON>", "dataProtection.tab.scan.addScanTask": "Add <PERSON>", "dataProtection.tab.scan.jobName.plac": "Please enter task name", "dataProtection.tab.scan.description": "Task Description", "dataProtection.tab.scan.description.plac": "Please enter task description", "dataProtection.tab.scan.scanRange": "Scan Range", "dataProtection.tab.scan.scanRange.plac": "Please select scan range", "dataProtection.tab.scan.autoScanning": "Scan Rules", "dataProtection.tab.scan.autoScanning.plac": "Please select scan rules", "dataProtection.tab.scan.cronExpression.plac": "Please enter task cycle", "dataProtection.tab.scan.validate": "Validate", "dataProtection.tab.scan.cronGenerator": "Cron Generator", "dataProtection.tab.scan.scanResult.enable": "When scan results are enabled, the results generated by the scan task will automatically enable the default desensitization algorithm", "dataProtection.tab.scan.scanAlgoType.default": "De<PERSON><PERSON>", "common.text.selectUser": "Please select a user", "dataProtection.tab.scanTask.status0": "Stopped", "dataProtection.tab.scanTask.status1": "Running", "dataProtection.tab.scanTask.status2": "In Progress", "dataProtection.tab.scanTask.status3": "Error", "dataProtection.tab.scanTask.status4": "Completed", "dataProtection.tab.scanTask.manual": "Manual Execution", "dataProtection.tab.scanTask.cron": {"from": "From", "second": "Second", "minute": "Minute", "hour": "Hour", "day": "Day", "week": "Week", "month": "Month", "year": "Year", "assign": "Assign", "donTAssign": "Do Not Assign", "everyTime": {"second": "Every second", "minute": "Every minute", "hour": "Every hour", "day": "Every day", "week": "Every week", "month": "Every month", "year": "Every year"}, "sun": "Sunday", "mon": "Monday", "tue": "Tuesday", "wed": "Wednesday", "thu": "Thursday", "fri": "Friday", "sat": "Saturday", "aTob": {"second": "Second, executed once every second", "minute": "Minute, executed once every minute", "hour": "Hour, executed once every hour", "day": "Day, executed once every day", "week": ", executed once every week", "month": "Month, executed once every month", "year": "Year, executed once every year"}, "aStartTob": {"secondContent1": "Starting from second, every", "secondContent2": "second executes once", "minuteContent1": "Starting from minute, every", "minuteContent2": "minute executes once", "hourContent1": "Starting from hour, every", "hourContent2": "hour executes once", "dayContent1": "Starting from day, every", "dayContent2": "day executes once", "weekContent1": "This month", "weekContent2": "of the week", "weekContent3": "executes once", "week2Content1": "The last", "week2Content2": "of the month executes once", "monthContent1": "Starting from month, every", "monthContent2": "month executes once", "yearContent1": "Starting from year, every", "yearContent2": "year executes once"}}, "dataProtection.tab.denses.batchSettingPolicy": "Batch Setting Policy", "dataProtection.tab.denses.batchExport": "Batch Export", "dataProtection.tab.security.windows": "Window Period Settings", "dataProtection.tab.security.windows.tip": "High-risk operation settings during the window period will temporarily be invalid", "dataProtection.alert.msg.windows": "Currently in the window period, the set high-risk operations are temporarily ineffective", "dataProtection.msg.set_windows_success": "Window period settings successful", "dataProtection.selemsg.mult_sync_review_methods": "There are multiple synchronous review methods available", "dataProtection.selemsg.mult_types_approvers": "There are multiple types of synchronous review approvers"}