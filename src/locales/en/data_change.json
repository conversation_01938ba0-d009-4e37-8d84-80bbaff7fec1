{"bc_title.data_change": "Data Modification", "bc_title.my_apply": "My Applications", "bc_title.apply_change": "Apply for Change", "bc_title.work_order_details": "Ticket Details", "bc_title.my_review": "My Approvals", "bc_title.work_order_mgmt": "Ticket Management", "bc_title.my_approval": "My Approval", "hd_inp_ph.srh_title_db_app": "Search Title/Database/Approver", "lnk_label.request_changes": "Apply for Change", "menu_label.param_conf": "Parameter Configuration", "msg.update_success": "Update successful", "tab.data_correction": "Data Correction", "tab.pub_chg": "Publish Changes", "item_label.overall_affairs": "Overall Transaction", "rcsg_opt.pending_review": "Pending Review", "rcsg_opt.reviewed": "Reviewed", "rcsg_opt.completed": "Completed", "rcsg_opt.all": "All", "rcsg_opt.withdrawn": "Withdrawn", "tb_title.id": "ID", "tb_title.applicant": "Applicant", "tb_title.type": "Type", "tb_title.title": "Title", "tb_title.init_time": "Initiation Time", "tb_title.cur_status": "Current Status", "tb_title.database": "Database", "tb_title.cur_approver": "Current Approver", "tb_title.exe_time": "Execution Time", "tb_title.operation": "Operation", "status.draft": "Draft", "status.pending_review": "Pending", "status.reviewed": "Approved", "status.completed": "Completed", "status.approving": "Under Review", "status.pending_execution": "Awaiting Execution", "status.executing": "In Progress", "status.executed": "Executed", "status.exe_success": "Execution Successful", "status.exe_failed": "Execution Failed", "status.withdrawn": "Revoked", "status.rejected": "Rejected", "status.task_terminated": "Task Aborted", "status.rollback_success": "Rollback Successful", "status.rollback_failed": "Rollback Failed", "status.interrupted": "Interrupted", "ope_menu_label.delete_draft": "Delete Draft", "ope_menu_label.withdraw_application": "Withdraw Application", "ope_menu_label.edit": "Edit", "ope_menu_label.remind": "Remind", "ope_menu_label.execute": "Execute", "ope_menu_label.interrupt_task": "Interrupt Task", "ope_menu_label.terminate_execution": "Terminate Execution", "ope_menu_label.end_task": "End Task", "ope_menu_label.rollback_execution": "Rollback Execution", "ope_menu_label.reapply": "Reapply", "ope_menu_label.view_details": "View Details", "ope_menu_label.apply": "Apply", "msg.reminder_success": "<PERSON>mind<PERSON> completed successfully", "step.apply_data_change": "Apply for Data Modification", "step.sql_review": "SQL Review", "step.sub_app": "Submit Application", "step.sim_env_val": "Simulated Environment Validation", "btn.reset": "Reset", "btn.sql_review": "SQL Review", "item_label.sim_db": "Simulated Database", "item_label.database": "Database", "item_label.title": "Title", "item_label.data_type": "Data Type", "item_label.change_sql": "Change SQL", "item_label.sql_text": "SQL Text", "item_label.sql_att": "SQL Attachment", "item_label.exe_method": "Execution Method", "item_label.exe_time": "Execution Time", "item_label.remarks": "Remarks", "item_label.app_rem": "Approver's Remarks", "msg.delete_success": "Delete Successful", "msg.delete_failure": "Delete Failed", "msg.upload_success": "Upload Successful", "msg.upload_failure": "Upload Failed", "msg.sql_cannot_be_empty": "Execution SQL cannot be empty", "rules_msg.select_database": "Please select a database", "label.rule_template": "Rule Template: ", "rules_msg.enter_title": "Please enter a title", "item_extra.chg_st_warning": "After changing the SQL type, the entered content will be cleared", "tab.execute_sql": "Execute SQL", "tab.rollback_sql": "Rollback SQL", "rules_msg.enter_exe_sql_text": "Please enter the execution SQL text", "rdo_label.text": "Text", "rdo_label.upload_attachment": "Upload Attachment", "item_extra.supported_format": "Supported format: .sql", "btn.upload_exe_sf": "Upload Execution SQL File", "btn.upload_rb_sf": "Upload Rollback SQL File", "exe_mtd.manual_execution": "Manual Execution", "exe_mtd.automatic_execution": "Automatic Execution", "exe_mtd.execute_immediately": "Execute Immediately", "rules_msg.up_exe_sql_att": "Please upload the execution SQL attachment", "clg_err.dl_rev_res_err": "Download review result error :>>", "tab.execution_review": "Execution Review", "tab.rollback_review": "Rollback Review", "tb_title.serial_number": "No.", "tb_title.exe_statement": "Execution Statement", "tb_title.parsing_result": "Parsing Result", "tb_title.review_result": "Review Result", "tb_title.version_comparison": "Version Comparison", "btn.view": "View", "btn.details": "Details", "btn.collapse": "Collapse", "btn.expand": "Expand", "btn.dl_check_res": "Download Check Result", "btn.dl_review_res": "Download Review Result", "btn.prev_step": "Previous Step", "btn.next_step": "Next Step", "tb_render.success": "Success", "tb_render.failure": "Failure", "card_title.sql_rev_log_dp": "SQL Review Log Display", "card_title.exe_plan": "Execution Plan", "tip_title.sql_rev_err_level": "Next operation is not allowed if there are error-level results in SQL review", "tab.execution_log": "Execution Log", "tab.rollback_log": "Rollback Log", "clg_err.validation_failed": "Validation Failed", "clg_err.poll_res_failed": "Polling result failed:", "tb_title.sql_statement": "SQL Statement", "tb_title.execution_time": "Execution Time", "tb_title.affected_rows": "Affected Rows", "tb_title.result_analysis": "Result Analysis", "btn.export": "Export", "btn.execute_validation": "Execute Validation", "btn.rollback_validation": "Rollback Validation", "card_title.sim_env_val": "Simulated Environment Validation", "btn.save_draft": "Save Draft", "btn.submit": "Submit", "btn.modify": "Modify", "msg.select_exe_time": "Please select execution time", "msg.exe_time_val": "Execution time must be later than the current time", "msg.submit_success": "Submission Successful", "msg.withdraw_success": "<PERSON><PERSON><PERSON> Successful", "item_label.applicant": "Applicant", "item_label.department": "Department", "item_label.application_number": "Application Number", "item_label.application_time": "Application Time", "item_label.application_type": "Application Type", "card_title.apply_info": "Application Information", "clg_err.dl_file_failed": "Download file failed", "card_title.sql_text": "SQL Text", "card_title.work_order_flow": "Work Order Flow", "step.create": "C", "step.approval": "A", "desc.approval_passed": "<PERSON><PERSON><PERSON><PERSON> Passed", "desc.approval_in_progress": "Approval In Progress", "msg.operation_success": "Operation Successful", "btn.confirm": "Confirm", "btn.cancel": "Cancel", "md_title.remarks": "Remarks", "span.spec_approver": "Specified Approver:", "span.operation": "Operation:", "sele_ph.spec_approver": "Please select a specified approver", "btn.agree": "Agree", "btn.reject": "Reject", "btn.transfer_review": "Transfer Review", "card_title.approval_info": "Approval Information", "desc.remarks": "Remarks", "apply_status.pending_review": "Pending Review", "apply_status.completed": "Completed", "apply_status.pending_auth": "Pending Authorization", "apply_status.rejected": "Rejected", "apply_status.approved": "Approved", "apply_status.rejected_by_approval": "Rejected by <PERSON><PERSON><PERSON><PERSON>", "apply_status.terminated": "Terminated", "apply_status.withdrawn": "Withdrawn", "clg_err.recursion_failed": "====> Recursion Failed", "card_title.exe_chg_log": "Execution Change Log", "btn.dl_exe_log": "Download Execution Log", "btn.dl_rb_log": "Download Rollback Log", "div_label.time_consum": "Time Consumed:", "div_label.affected_rows": "Affected Rows:", "div_label.status": "Status:", "div_label.exception_info": "Exception Information:", "sql_exe_status.success": "Execution Successful", "sql_exe_status.failed": "Execution Failed", "hd_inp_ph.srh_app_db": "Search Applicant/Database", "ope_menu_label.task_traf": "Transfer Task", "ope_menu_label.approve": "Approve", "ope_menu_label.reject": "Reject", "msg.approval_success": "Approval Successful", "msg.reject_success": "Rejection Successful", "msg.enter_reject_reason": "Please enter rejection reason", "ope_menu_label.traf_rev": "Transfer Review", "step.conn_admin": "Connection Administrator", "step.apply_completed": "Application Completed", "step.completed": "F", "card_title.exe_chg": "Execution Change", "btn.view_log": "View Log", "tip_title.refresh": "Refresh", "hd_inp_ph.srh_tlt_ac_db_av": "Search Title/Applicant/Database/Approver", "hd_inp_ph.srh_ac_db_av": "Search Applicant/Database/Approver", "tip_title.no_perms_for_wdmng": "Your current role is [{{role}}], and you do not have permission to operate on [Ticket Management]", "msg.only_single_fl": "Only a single file upload is allowed", "msg.please_select_fl": "Please select a file first", "msg.fl_split_in_progress_wait": "File splitting in progress, please wait...", "msg.fl_already_exists": "File already exists, no need to upload again", "msg.uploaded_fl_chunk_length": "Uploaded file chunks: {{length}}", "msg.fl_upload_merge_completed": "File upload merge completed!", "spin_tip.estimated_rm_time": "Estimated remaining time: {{second}} seconds", "spin_tip.calc_rm_upload_time": "Calculating remaining upload time", "btn.single_fl_upload": "Single File Upload", "item_label.data_backup_location": "Data Backup Location: ", "msg.task_traf_success": "Transfer Successful", "icon_btn.preview": "Preview", "div_lbl.encoding": "Encoding", "tip_title.delete": "Delete", "tip_title.move_up": "Move Up", "tip_title.move_down": "Move Down", "tip_title.move_top": "Move to Top", "tip_title.move_bottom": "Move to Bottom"}