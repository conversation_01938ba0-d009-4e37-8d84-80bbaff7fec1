{"bc_title.static_desensitization": "Static Desensitization", "bc_title.task_record": "Task Record", "bc_title.edit_task": "Edit Task", "bc_title.create_task": "Create Task", "bc_title.view_details": "View Details", "hd_inp_ph.search_name": "Search Name", "btn.create_task": "Create Task", "tab_title.desensitizing": "Desensitizing", "tab_title.desen_successful": "Desensitization Successful", "tab_title.desens_removal": "Desensitization Removal", "tb_title.task_id": "Task ID", "tb_title.task_name": "Task Name", "tb_title.task_type": "Task Type", "tb_title.source": "Source", "tb_title.target": "Target", "tb_title.status": "Status", "tb_title.initiation_time": "Initiation Time", "tb_title.update_time": "Update Time", "tb_title.creator": "Creator", "tb_title.operation": "Operation", "tb_title.src_table_name": "Source Table Name", "tb_title.tgt_table_name": "Target Table Name", "tb_title.data_volume": "Data Volume", "tb_title.start_time": "Start Time", "tb_title.end_time": "End Time", "tb_title.masked_column": "Masked Column", "task_type.static_desensitization": "Static Desensitization", "task_type.data_transmission": "Data Transmission", "tb_render.status.initializing": "Initializing", "tb_render.status.in_progress": "In Progress", "tb_render.status.running": "Running", "tb_render.status.completed": "Completed", "tb_render.status.pause": "Pause", "tb_render.status.paused": "Paused", "tb_render.status.terminated": "Terminated", "tb_render.status.waiting": "Waiting", "tb_render.status.transfer_success": "Transfer Success", "tb_render.status.success": "Success", "tb_render.status.failed": "Failed", "tb_render.status.task_completed": "Task Completed", "tb_render.status.task_failed": "Task Failed", "pop_title.start": "Are you sure to start this task?", "pop_title.pause": "Are you sure to pause this task?", "pop_title.cancel": "Are you sure to cancel this task?", "pop_title.retry": "Are you sure to retry this task?", "tip_title.start": "Start", "tip_title.pause": "Pause", "tip_title.cancel": "Cancel Task", "tip_title.view_details": "View Details", "tip_title.copy": "Copy", "tb.show_total": "Total {{total}} records", "msg.cancel_success": "Cancel Successful", "msg.pause_success": "Pause Successful", "msg.resume_success": "Resume Successful", "msg.retry_success": "Retry Successful", "msg.test_conn_success": "Test Connection Successful", "msg.create_success": "Created Successfully", "step.select_src_and_dst": "Select Source and Target", "step.select_db_obj": "Select Database Object", "step.submit_task": "Submit Task", "item_label.src": "Source Connection", "item_label.ds_type": "Data Source Type", "item_label.conn": "Connection", "item_label.tgt_conn": "Target Connection", "test_connection": "Test Connection", "rules_msg.req_ds_type": "Please select data source type", "rules_msg.req_conn": "Please select connection", "rules_msg.req_test_conn": "Please test connection", "rules_msg.val_same_ds": "Please select the same data source", "tip_title.same_ds_type": "Must be consistent with the data source type of the source connection", "step_act.next": "Next Step", "step_act.prev": "Previous Step", "step_act.submit_task": "Submit Task", "item_label.select_src_db": "Select Source Database", "item_label.select_tgt_db": "Select Target Database", "item_label.select_src_schema": "Select Source <PERSON><PERSON>a", "item_label.select_tgt_schema": "Select Target Schema", "item_label.select_tb_traf": "Select Table Transfer", "rules_msg.req_src_schema": "Please select source schema", "rules_msg.req_tgt_schema": "Please select target schema", "rules_msg.req_src_db": "Please select source database", "rules_msg.req_tgt_db": "Please select target database", "rules_msg.req_tb_traf": "Please select table transfer", "rules_msg.val_diff_schema": "Please select a different Schema", "traf_title.source": "Source", "traf_title.target": "Target", "item_label.exe_method": "Execution Method", "item_label.task_name": "Task Name", "item_label.tb_overwr": "Allow Table Overwrite", "rdo_label.exe_now": "Execute Now", "rdo_label.yes": "Yes", "rdo_label.no": "No", "rules_msg.req_task_name": "Please enter task name", "desc_label.task_status": "Task Status", "btn.refresh_task": "Refresh Task", "btn.refresh_log": "Refresh", "status.wait_duration": "Wait Duration: ", "status.remaining_time": "Remaining Time: ", "tip_title.view_logs": "View Logs", "tip_title.retry": "Retry", "tab.masking_in_progress": "Masking In Progress", "tab.masking_completed": "Masking Completed", "tab.canceled": "Cancelled", "div_title.failure_count": "Failure Count", "task_type.static_desensitization.alert": "Desensitization enabled in the data protection management desensitized data does not take effect on static desensitized transmission"}