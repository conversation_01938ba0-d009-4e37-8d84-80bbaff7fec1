{"loginRequired": "User not logged in or login expired,", "pleaseLoginImmediately": "Please log in immediately!", "permissionAlias": "Permission Name", "permissionType": "Permission Type", "userName": "Username", "account": "Account", "bindUser": "Bind User", "grantPermission": "Grant Permission", "sunday": "Sunday", "monday": "Monday", "tuesday": "Tuesday", "wednesday": "Wednesday", "thursday": "Thursday", "friday": "Friday", "saturday": "Saturday", "roleName": "Role Name", "pleaseInputRoleName": "Please enter role name", "roleDescription": "Role Description", "roleValidity": "Role Validity", "timePeriod": "Time Period", "cycle": "Cycle", "allRange": "All Range", "custom": "Custom", "allUsers": "All Users", "title": "Title", "pleaseInputTitle": "Please enter title", "applyReason": "Apply Reason", "pleaseInputApplyReason": "Please enter apply reason", "pleaseSelectOrEnterTargetTable": "Please select or enter target table", "sourceField": "Source Field", "targetField": "Target Field", "type": "Type", "length": "Length", "decimalPoint": "Decimal Point", "primaryKey": "Primary Key", "operation": "Operation", "confirmDelete": "Confirm Delete", "delete": "Delete", "pleaseSelect": "Please Select", "addMapping": "Add Mapping", "targetTable": "Target Table", "importErrorContinue": "Import error, continue importing", "quickImport": "Quick Import", "fieldLoading": "Field loading", "fileUploadFailed": "File upload failed", "pleaseSelectFileFirst": "Please select file first", "pleaseSelectFileType": "Please select {{fileType}} type file", "onlySupportFileType": "Only support .txt/.csv/.xls/.xlsx/.json type files", "attachmentUpload": "Attachment Upload", "uploadAttachment": "Upload Attachment", "pleaseUploadAttachment": "Please upload attachment", "uploadingAttachment": "Uploading attachment...", "uploadLocation": "Upload Location", "localUpload": "Local Upload", "personalFolderUpload": "Personal Folder Upload", "encoding": "Encoding", "pleaseSelectEncoding": "Please select encoding format", "table": "Table", "tables": "tables", "collection": "Collection", "collections": "collections", "pleaseSelectTargetTable": "Please select target table", "newTable": "New Table", "recordSeparator": "Record Separator", "pleaseSelectRecordSeparator": "Please select record separator", "fieldSeparator": "Field Separator", "pleaseSelectFieldSeparator": "Please select field separator", "fieldIdentifier": "Field Identifier", "cannotBeEmpty": "Cannot be empty", "onlyPositiveInteger": "Only positive integer", "fieldNameRow": "Field Name Row", "firstDataRow": "First Data Row", "lastDataRow": "Last Data Row", "dateSort": "Date Sort", "pleaseSelectDateSort": "Please select date sort", "dateSeparator": "Date Separator", "timeSeparator": "Time Separator", "decimalPointSymbol": "Decimal Point Symbol", "dateTimeSort": "Date Time Sort", "pleaseSelectDateTimeSort": "Please select date time sort", "dateTime": "Date Time", "dateTimeZone": "Date Time Zone", "date": "Date", "time": "Time", "binaryDataEncoding": "Binary Data Encoding", "pleaseSelectBinaryDataEncoding": "Please select binary data encoding", "importType": "Import Type", "textFile": "Text File", "csvFile": "CSV File", "excelFile": "Excel File", "jsonFile": "JSON File", "addSuccess": "Add Success", "newRole": "New Role", "previousStep": "Previous Step", "nextStep": "Next Step", "complete": "Complete", "bindSuccess": "Bind Success", "roleAlias": "<PERSON> <PERSON><PERSON>", "unbindUser": "Unbind User", "added": "Added", "addUser": "Add User", "pleaseSelectAtLeastOneUser": "Please select at least one user", "pleaseSelectUserToAdd": "Please select the user to add", "pleaseSelectSameTypeElements": "Please select the same type of elements", "operationPermission": "Operation Permission", "addDataOperationPermission": "Add Data Operation Permission", "editDataOperationPermission": "Edit Data Operation Permission", "permissionName": "Permission Name", "pleaseInputPermissionName": "Please enter permission name", "permissionDescription": "Permission Description", "databaseElement": "Database Element", "pleaseSelectDatabaseElement": "Please select database element", "pleaseSelectOperationPermission": "Please select operation permission", "editSuccess": "Edit Success", "newPermissionSet": "New Permission Set", "editPermissionSet": "Edit Permission Set", "name": "Name", "pleaseInputPermissionSetName": "Please enter permission set name", "description": "Description", "pleaseSelectSubPermission": "Please select sub-permission", "subPermission": "Sub-Permission", "modifySuccess": "Modify Success", "editRole": "Edit Role", "authorizationSuccess": "Authorization Success", "close": "Close", "permissionObject": "Permission Object", "all": "All", "empty": "Empty", "copySuccess": "Copy Success", "exportFileEncrypted": "Export File Encrypted", "encryptionKey": "Encryption Key", "fileKey": "File Key", "download": "Download", "connectionCreatedSuccess": "Connection Created Successfully", "connectionAvailable": "Connection Available", "connectionFailed": "Connection Failed", "monitorSwitch": "Monitor Switch", "testConnection": "Test Connection", "createDataSourceConnection": "Create {{dataSourceType}} Connection", "databaseAddedSuccess": "Database Added Successfully", "addDatabase": "Add Database", "characterSet": "Character Set", "collation": "Collation", "pleaseSelectCharacterSet": "Please select character set", "pleaseSelectCollation": "Please select collation", "generateSQL": "Generate SQL", "pleaseInput": "Please enter {{title}}", "address": "Address", "port": "Port", "add": "Add", "labelCannotBeEmpty": "{{label}} cannot be empty", "pleaseSelectLabel": "Please select {{label}}", "pleaseInputLabel": "Please enter {{label}}", "pleaseSelectOrAddLabel": "Please select or add {{label}}", "item_label.file_preview": "File Preview"}