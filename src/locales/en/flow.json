{"flow_json_comment": "======== 流程 flow ========", "flow_my_request": "My Applications", "flow_access_req": "Access Application", "flow_resource_count": "Resource quantity based on the number of connections", "flow_request_list": "Application List", "flow_deletion_success": "Deletion Successful", "flow_operation": "Operation", "flow_confirm_deletion": "Confirm Deletion?", "flow_ok": "OK", "flow_cancel": "Cancel", "flow_request_resource": "Request Resource", "flow_add_resource": "Add Resource", "flow_user_permission_level": "User has additional permission settings at this level", "flow_please_select": "Select", "flow_submission_success": "Submission Successful", "flow_save_draft": "Save Draft", "flow_resel_time_end_later": "Re-select Time, End Must Be Later", "flow_submit_request": "Submit Request", "flow_effective_time": "Effective Time", "flow_permanent": "Permanent", "flow_custom_period": "Custom Period", "flow_time_period": "Time Period", "flow_title": "Title", "flow_remark": "Remark", "flow_enter_connection_ip": "Enter Connection Name/IP", "flow_permissions_lower": "permissions", "flow_confirm_clear": "Confirm Clear?", "flow_no_data": "No Data", "flow_bulk_operation": "Bulk Operation", "flow_clear": "Clear", "flow_delete": "Delete", "flow_added_permissions": "Added Permissions", "flow_removed_permissions": "Removed Permissions", "flow_collapse": "Collapse", "flow_expand_details": "Expand to View Details", "flow_operation_permissions": "Operation Permissions", "flow_to": "to", "flow_bytes": "Bytes", "flow_application_info": "Application Info", "flow_operation_success": "Operation Successful", "flow_specify_approver": "Specify Approver", "flow_select_approver": "Select Approver", "flow_approve": "Approve", "flow_reject": "Reject", "flow_transfer_review": "Transfer Review", "flow_approval_info": "Approval Info", "flow_ticket_flow": "Ticket Flow", "flow_create": "C", "flow_review": "A", "flow_copy_success": "Copy Successful", "flow_file_key": "File Key", "flow_export_encrypted_key": "Exported File is Encrypted, Key is", "flow_attachment_info": "Attachment Info", "flow_export_error": "Export Error", "flow_approved_success": "Approved Successfully", "flow_rejected_success": "Rejected Successfully", "flow_approval_comments": "Approval Comments", "flow_rejection_comments": "Rejection Comments", "flow_withdrawal_info": "<PERSON><PERSON>wal Info", "flow_permission_info": "Permission Info", "flow_submit": "Submit", "flow_select_all": "Select All", "flow_permission_details": "Permission Details", "flow_add_more_resources": "Add More Resources", "flow_other_control_methods": "Other Control Methods", "flow_clear_selections": "Clear Selections", "flow_select_permissions": "Select Permissions", "flow_cancel_batch": "Cancel Batch", "flow_reminder_success": "Reminder Successful", "flow_withdrawal_success": "<PERSON><PERSON><PERSON> Successful", "flow_added_to_application": "Successfully Added to Application", "flow_reapplication_success": "Reapplication Successful", "flow_reapplication_failed": "Reapplication Failed", "flow_confirm_withdraw_record": "Confirm <PERSON><PERSON><PERSON> of This Record?", "flow_withdraw": "Withdraw", "flow_confirm_reapply": "Confirm Reapplication?", "flow_reapply": "Reapply", "flow_confirm_withdraw_authorization": "Confirm <PERSON><PERSON><PERSON> of Granted Authorization?", "flow_confirm_reapplication": "Confirm Reapplication?", "flow_reapplication": "Reapplication", "flow_approver_change": "The Approver <PERSON><PERSON> Has Changed, Please Select a New Approver", "flow_effective_status": "Effective Status", "flow_total": "Total", "flow_items_lower": "items", "flow_give_reminder": "<PERSON>minder", "flow_select_new_approver": "Select New Approver", "flow_application": "Application", "flow_view": "View", "flow_application_number": "Application Number", "flow_my_approvals": "My Approvals", "flow_search_title_applicant": "Search Title/Applicant", "flow_rejection_success": "Rejection Success", "flow_current_role_is": "Your Current Role Is", "flow_no_permission_work_order": "No Operation Permission for [Ticket Management]", "flow_confirm_withdraw_permissions": "<PERSON><PERSON><PERSON> Will <PERSON>oke Applicant's Permissions, Confirm <PERSON><PERSON><PERSON>?", "flow_completed": "Completed", "flow_work_order_management": "Ticket Management", "flow_approval_form": "Approval Form", "flow_review_info": "Review Information", "flow_approval_passed": "<PERSON><PERSON><PERSON><PERSON> Passed", "flow_approval_rejected": "<PERSON><PERSON><PERSON><PERSON> Rejected", "flow_authorization_info": "Authorization Information", "flow_agree_to_auth": "Agree to Authorize", "flow_reject_auth": "Reject Authorization", "flow_resource_search": "Resource Search", "flow_all": "All", "flow_enter_ip_address": "Please Enter the IP Address", "flow_search": "Search", "flow_add_to_application": "Add to Application", "flow_resource_list": "Resource List", "flow_enter_resource_name_ip": "Enter Resource Name/IP Address", "flow_select_batch_resources": "Please Select the Resources to <PERSON><PERSON> on the Left Side", "flow_selected_permissions": "Selected Permissions", "flow_select_same_data_source": "Select the Same Data Source", "flow_select_same_type_elements": "Select Elements of the Same Type", "flow_enter_positive_integer": "Enter a Positive Integer", "flow_enter": "Enter", "flow_search_by_name": "search by name", "flow_current_page": "Currently on", "flow_page": "Page", "flow_pre_page": "Previous Page", "flow_next_page": "Next Page", "flow_jump_to": "Jump to", "flow_confirm_delete_record": "Are You Sure You Want to Delete This Record?", "flow_type": "Type", "flow_applicant": "Applicant", "flow_status": "Status", "flow_current_approver": "Current Approver", "flow_sub_task_number": "Sub-task Number", "flow_resource_quantity": "Resource Quantity", "flow_connection_quantity": "Connection Quantity", "flow_assigned_person": "Assigned Person", "flow_connection_name": "Connection Name", "flow_instance_name": "Instance Name", "flow_service_name": "Service Name", "flow_version_number": "Version Number", "flow_connection_user": "Connection User", "flow_connection_admin": "Connection Administrator", "flow_resource_name": "Resource Name", "flow_permission_request": "Permission Request", "flow_tool_permissions": "Tool Permissions", "flow_role_permissions": "Role Permissions", "flow_ob_name": "Object Name", "flow_comment": "Comment", "flow_permission_level_request": "Permission Level Request", "flow_tool_permission_request": "Tool Permission Request", "flow_operation_privilege_escalation": "Operation Privilege Escalation", "flow_high_risk_request": "High-Risk Request", "flow_sensitive_request": "Sensitive Request", "flow_export_request": "Export Request", "flow_import_request": "Import Request", "flow_pending_review": "Pending Review", "flow_pending_assignment": "Pending Assignment", "flow_rejected": "Rejected", "flow_passed": "Passed", "flow_dismissed": "Dismissed", "flow_aborted": "Aborted", "flow_withdrawn": "Withdrawn", "flow_expired": "Expired", "flow_pending_activation": "Pending Activation", "flow_active": "Active", "flow_approved": "Approved", "flow_search_title_current_approver": "Search by Title/Current Approver", "flow_search_title": "Search by Title", "flow_search_title_assigned_person": "Search by Title/Assigned Person", "flow_search_title_approver_assigned_person": "Search by Title/Approver/Assigned Person", "flow_pending_approval": "Pending Approval", "flow_search_title_applicant_approver": "Search by Title/Applicant/Approver", "flow_search_title_applicant_assigned_person": "Search by Title/Applicant/Assigned Person", "flow_department": "Department", "flow_application_time": "Application Time", "flow_application_type": "Application Type", "flow_data_source_connection": "Data Source Connection", "flow_database_ele": "Database Elements", "flow_operation_type": "Operation Type", "flow_application_reason": "Reason for Application", "flow_execute_sql": "Execute SQL", "flow_export_row_count": "Export Row Count", "flow_import_target_table": "Import Target Table", "flow_import_file_size": "Import File Size", "flow_import_file": "Import File", "flow_auto_execute_after_approval": "Auto-Execute After Approval", "flow_manual_execute_after_approval": "Manual Execute After Approval (Click [Correct] <PERSON><PERSON> at Bottom)", "flow_hour": "Hour", "flow_id": "ID", "flow_initiating_dept": "Initiating Department", "flow_initiating_time": "Initiating Time", "flow_usage_period": "Usage Period", "flow_approver_account": "Approver Account", "flow_approver": "Approver", "flow_database": "Database", "flow_work_transfer_review": "Work Transfer Review", "flow_view_details": "View Details", "flow_flow_type": "Flow Type", "flow_submission_date": "Submission Date", "flow_search_approver_account": "Search Approver Account...", "flow_data_correction": "Data Correction", "flow_privilege_request": "Privilege Request", "flow_connection_privilege": "Connection Privilege", "flow_export_privilege": "Export Privilege", "flow_data_masking_privilege": "Data Masking Privilege", "flow_high_risk_privilege": "High-Risk Privilege", "flow_execution_count_privilege": "Execution Count Privilege", "flow_execution_rows_privilege": "Execution Rows Privilege", "flow_ui_privilege": "UI Privilege", "flow_sensitive_resource_request": "Sensitive Resource Request", "flow_1_week": "1 Week", "flow_1_month": "1 Month", "flow_3_months": "3 Months", "flow_6_months": "6 Months", "flow_1_year": "1 Year", "flow_7_days": "7 Days", "flow_30_days": "30 Days", "flow_90_days": "90 Days", "flow_180_days": "180 Days", "flow_365_days": "365 Days", "flow_system_settings": "System Settings", "flow_db_management": "Database Management", "flow_audit_analysis": "Audit Analysis", "flow_all_status": "All Status", "flow_revoked": "Revoked", "flow_in_review": "In Review", "flow_last_7_days": "Last 7 Days", "flow_last_30_days": "Last 30 Days", "flow_approval_id": "Approval ID", "flow_correction_statement": "Correction Statement", "flow_rollback_statement": "Rollback Statement", "flow_rollback_result": "<PERSON><PERSON> Result", "flow_correction_timing": "Correction Timing", "flow_menu_permissions": "Menu Permissions", "flow_deadline": "Deadline", "flow_execution_rows": "Execution Rows", "flow_correction_affected_rows": "Correction Affected Rows", "flow_rollback_affected_rows": "Rollback Affected Rows", "flow_execution_count": "Execution Count", "flow_last_update_time": "Last Update Time", "flow_correction_result": "Correction Result", "flow_rejection_reason": "Rejection Reason", "flow_application_process": "Application Process", "flow_management": "Management Process", "flow_all_processes": "All Processes", "flow_approval_status": "Approval Status", "flow_approval_node": "Approval Node", "flow_last_operation_time": "Last Operation Time", "flow_view_process": "View Process", "flow_pending": "Pending", "flow_processed": "Processed", "flow_approval": "Approval", "flow_accept": "Accept", "flow_decline": "Decline", "flow_enter_denial_reason": "Enter Denial Reason", "flow_denial_reason": "Denial Reason", "flow_search_applicant_name": "Search Applicant Name...", "flow_current_node": "Current Node", "flow_unresponsive": "Unresponsive", "flow_duration": "Duration", "flow_create_document": "Create Document", "flow_level_1_approver": "Level 1 Approver", "flow_level_2_approver": "Level 2 Approver", "flow_complete": "Complete", "flow_reversal_successful": "Reversal Successful", "flow_correction_successful": "Correction Successful", "flow_rollback_successful": "Rollback Successful", "flow_info_retrieval_failed": "Failed to Retrieve Information", "flow_refresh_or_retry": "Click the Refresh <PERSON><PERSON>, or Try Again Later", "flow_refresh": "Refresh", "flow_process_details": "Process Details", "flow_doc_details": "Document Details", "flow_process_progress": "Process Progress", "flow_approver_list": "Approver List", "flow_select_person_for_approval": "Select the Person to Transfer for Approval", "flow_current_approval_info": "Current Approval Info", "flow_node_approval_type": "Node Approval Type", "flow_node_approval_status": "Node Approval Status", "flow_application_submitter": "Application Submitter", "flow_application_reviewer": "Application Reviewer", "flow_select_db_ele": "Select Database Element", "flow_select_an_approver": "Select Approver", "flow_select_reviewer": "Select Reviewer", "flow_enter_correction": "Enter Correction", "flow_select_same_data_source_ele": "Select the Same Data Source Type", "flow_select_same_level_elements": "Select Elements of the Same Level", "flow_select_operation": "Select Operation Type", "flow_select_at_least_one_operation": "Select at Least One Operation Type", "flow_enter_application_reason": "Enter Application Reason", "flow_enter_rollback_statement": "Enter Rollback Statement", "flow_select_sql_or_txt_file": "Select a .sql or .txt File", "flow_file_size_limit_2MB": "File Size Limit 2MB", "flow_open_local_doc": "Open Local Document", "flow_select_deadline": "Select Deadline", "flow_custom": "Custom", "flow_enter_a_positive_integer": "Enter a Positive Integer", "flow_select_same_connection_elements": "Select Elements Under the Same Connection", "flow_select_correction_execution_timing": "Select Correction Execution Timing", "flow_manual_execution": "Manual Execution", "flow_regular_pdf_export_limits": "Regular PDF Export Limits", "flow_regular_other_export_limits": "Regular Other Export Limits", "flow_selected_export_limits": "Selected Export Limits", "flow_export_type": "Export Type", "flow_select_export_type": "Select Export Type", "flow_shared_permissions_warning": "Only Shared Operation Permissions Are Displayed for Multiple Database Elements. Please Be Cautious When Requesting Privilege Escalation.", "flow_select_operation_permission": "Select Operation Permission", "flow_enter_execution_rows": "Enter Execution Rows", "flow_enter_execution_count": "Enter Execution Count", "flow_no_department_leader": "Your Department Has No Leader, Please Contact the Admin.", "flow_menu": "<PERSON><PERSON>", "flow_select_privilege_function": "Select function for privilege escalation", "flow_select_level_1_approver": "Select first-level approver", "flow_select_level_2_approver": "Select second-level approver", "flow_select_resources_for_privilege_escalation": "Select Resources for Privilege Escalation", "flow_add_resource_permissions_to_application": "Add Resource Permissions to Application", "flow_view_selected_resources_and_permissions": "View Selected Resources and Permissions", "flow_click_access_request": "Click Access Request to Enter Privilege Escalation Interface", "flow_tables": "tables", "flow_table_name": "Table Name", "flow_no_permissions": "No permissions", "flow_groups": "groups", "flow_resource": "Resource", "flow_grouping": "Grouping", "flow_list": "List", "flow_add_group_to_application": "Add Group to Application", "flow_no_sub_order": "No Sub-Order", "expand_exception": "Expand Exception", "flow_operation_noDdata": "The current resource level has not applied for any permissions. Please expand to the resource level below to view", "flow_repeat_submit": "You Already Have This Permission, But You Can Still Repeat the Application", "flow_thin_reapply_success": "Resubmitted successfully. You can now check it in the Data Operations module."}