{"home.guide.tip": "{{userName}}.Hello,  Not sure where to start? Check out", "home.guide.entry": "Beginner's Scenario Guide", "home.costSql.title": "Top 5 Time-Consuming SQL Executions", "home.costSql.table.originSql": "SQL Statement", "home.costSql.table.sqlType": "Type", "home.costSql.table.executeCostMs": "Execution Time (ms)", "home.msg.title": "Messages", "home.order.title": "Work Order Details", "home.order.dataManipulation": "Database Operation Permissions", "home.order.highRisk": "High-Risk Operation Permissions", "home.order.exportTask": "Export Tasks", "home.order.importTask": "Import Tasks", "home.order.desensitizedResource": "Sensitive Resource Access Permissions", "home.order.dataCorrection": "Data Modification", "home.order.status.ALL": "All", "home.order.status.UnderReview": "Under Review", "home.order.status.UnderReview1": "Pending", "home.order.status.Approved": "Approved", "home.order.status.Rejected": "Rejected", "home.order.status.Withdrawn": "Withdrawn", "home.order.tab.myApply": "My Applications", "home.order.tab.approval": "My Approvals", "home.overview.title": "Overview Statistics", "home.overview.datasource": "Data Sources", "home.overview.executeTital": "Total SQL Executions", "home.overview.executeFail": "Total Failed SQL Executions", "home.overview.desc1": "{{val}} compared to yesterday", "home.overview.desc2": "{{val}} permissions available", "home.freqSql.title": "Top 5 Most Frequently Executed SQL Databases", "home.freqSql.table.completeNameToView": "Database", "home.freqSql.table.totalOperations": "Total SQL Executions", "home.sqlExecute.title": "SQL Execution Statistics", "home.sqlExecute.date.tab1": "This Week", "home.sqlExecute.date.tab2": "This Month", "home.sqlExecute.dbSelect.plac": "Database Type", "home.sqlExecute.table.time": "Time", "home.sqlExecute.table.total": "Total Count", "home.sqlExecute.table.count": "Execution Count", "home.sqlExecute.chart.xAxis": "Times", "home.title": "Home"}