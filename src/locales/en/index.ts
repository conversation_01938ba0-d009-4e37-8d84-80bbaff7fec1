import systemManagement from './systemManagement.json';
import translation  from './translation.json';
import golbalSearch from './golbalSearch.json';
import common from './common.json';
import msg from './msg.json';
import database from './database.json';
import systemDataOperate from './system_data_operate.json';
import flow from './flow.json';
import dataProtection from './dataProtection.json';
import sqlBatchExecution from './sqlBatchExecution.json';
import home from './home.json';

const mergedObject = {
  ...common,
  ...systemManagement,
  ...translation,
  ...golbalSearch,
  ...msg,
  ...database,
  ...systemDataOperate,
  ...dataProtection,
  ...flow,
  ...sqlBatchExecution,
  ...home
};

export default mergedObject;
