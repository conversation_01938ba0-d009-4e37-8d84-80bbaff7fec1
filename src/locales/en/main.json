{"installApplication": "Install Application", "bindOtp": "Bind OTP", "otpBindSuccessful": "OTP bind successful", "bindNow": "Bind Now", "bindSecurityDevice": "Bind Security Device for Account {{userId}}. Please follow the steps below to complete the binding process.", "downloadAuthyGoogleAuthenticator": "Please download and install Authy/Google Authenticator (Authentication App) on your mobile device.", "searchAuthyGoogleAuthenticatorOnAppStore": "iPhone: Search for Authy/Google Authenticator (Authentication App) in the App Store.", "searchAuthyGoogleAuthenticatorOnGooglePlayStore": "Android: Search for Authy/Google Authenticator (Authentication App) in the Google Play Store.", "proceedToBindingPage": "After installation, click Next to proceed to the binding page (if already installed, proceed directly to the next step).", "scanQrCodeForVerificationCode": "Use Authy/Google Authenticator (Authentication App) to scan the QR code below and obtain a 6-digit verification code", "enterOtpVerification": "Please enter OTP verification", "otpVerificationCode": "OTP Verification Code", "mobileBindSuccessful": "Mobile bind successful", "exit": "Exit", "bindPhoneNumber": "Bind Phone Number", "phoneNumber": "Phone Number", "enterPhoneNumber": "Please enter phone number", "verificationCode": "Verification Code", "enterVerificationCode": "Please enter verification code", "getVerificationCode": "Get Verification Code", "passwordExpired": "The current user password has expired, please modify the password!", "firstTimeLogin": "Please change your password for the first time", "newPassword": "New Password", "confirmNewPassword": "Confirm New Password", "pleaseConfirmNewPassword": "Please confirm new password", "passwordsDoNotMatch": "The two new passwords do not match", "modifyPassword": "Modify Password", "confirm": "Confirm", "prevStep": "Previous Step", "nextStep": "Next Step"}