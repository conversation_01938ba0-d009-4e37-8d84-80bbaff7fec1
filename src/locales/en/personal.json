{"personalInformation": "Personal Information", "securitySettings": "Security Settings", "accessibleResources": "Accessible Resources", "preferenceSettings": "Preference Settings", "personalCenter": "Personal Center", "personalSettings": "Personal Settings", "additionalInformation": "Additional Information", "uploadAvatar": "Upload Avatar", "avatarUploadSuccessful": "Avatar Upload Successful", "modificationSuccessful": "Modification Successful", "copySuccessful": "Copy Successful", "save": "Save", "cancel": "Cancel", "edit": "Edit", "name": "Name", "name_cannot_be_empty": "Name cannot be empty", "please_enter_your_name": "Please enter your name", "systemRole": "System Role", "department": "Department", "registrationTime": "Registration Time", "email": "Email", "lastLoginTime": "Last Login Time", "phoneNumber": "Phone Number", "pleaseSelect": "Please Select", "pleaseEnter": "Please Enter", "nickName": "Name", "verification_code_has_been_sent": "Verification code has been sent", "loginPassword": "Login Password", "changePassword": "Change Password", "phoneBinding": "Phone Binding", "enterpriseWeChatBinding": "Enterprise WeChat Binding", "emailBinding": "Email Binding", "bindNow": "Bind Now", "unbind": "Unbind", "loginSettings": "<PERSON><PERSON>", "otpSettings": "OTP Settings", "sessionDuration": "Session Duration", "modificationTime": "Modification Time", "to_enhance_your_account_security": " To enhance your account security, you can set up OTP for two-factor authentication during login", "after_setting_up_virtual_OTP": "After setting up virtual OTP, you can use it for secondary verification during login and also for synchronous review of SQL commands", "you_can_set_the_login_session_duration": "You can set the login session duration (greater than or equal to 1 minute, less than or equal to 180 minutes). The system will log you out after this duration. The current setting is {{loginTime}} minutes (default is 30 minutes).", "no_unbind": "System-enforced binding, not allowed to unbind.", "settingNow": "Setting now", "emailBindingDescribe": "Bind Email, used for receiving messages", "noBind": "No bind", "enterpriseWeChatBindingDescribe": "Bind Enterprise WeChat, log in via QR code", "notEnabled": "Not Enabled", "phoneBindingDescribe": "Bind Phone, used for SMS secondary verification during login", "loginPasswordDescribe": "A strong password enhances account security. We recommend changing your password regularly，{{getPassWordTooltip}}", "changePhoneNumber": "Change Phone Number", "setting": "Setting", "systemPasswordStrongDefault": "Set a password that includes at least one number and one uppercase and lowercase letter, with a length of 9 to 16 characters", "systemPasswordStrong": "Set a password that includes at least {{result}} and is {{passwordMin}} to {{passwordMax}} characters long", "number": "Number", "lowercaseLetter": "Lowercase Letter", "uppercaseLetter": "Uppercase Letter", "specialCharacter": "Special Character", "disableAuthentication": "Disable Authentication", "otpAuthentication": "OTP Authentication", "smsAuthentication": "SMS Authentication", "two-FactorAuthenticationType": "Two-Factor Authentication Type", "configured_Two-Factor_Authentication_method_during_login": "Authentication is required via the configured Two-Factor Authentication method during login", "verifyIdentity": "Verify Identity", "to_ensure_that_account_is_operated_by_you": "To ensure that account {{userId}} is operated by you, please select any one method to verify your identity", "account": "Account", "password": "Password", "pleaseEnterPassword": "Please enter password", "verificationCode": "Verification Code", "pleaseVerificationCode": "Please Enter Verification Code", "phoneNumberPlus": "Phone Number", "the_email_address_for_receiving_the_verification_code": "The email address for receiving the verification code is the email address bound to your account.", "after_sending_the_verification_code": "After sending the verification code, you can retrieve it in your email. If you do not receive it within 1 minute, please check your spam folder.", "the_phone_number_for_receiving_the_verification_code": "The phone number for receiving the verification code is the secure phone number bound to your account.", "you_can_retrieve_it_in_your_SMS": "After sending the verification code, you can retrieve it in your SMS. If you do not receive it within 1 minute, please check your spam messages.", "unBindSuccessful": "Unbind Successful", "sendVerificationCode": "Send verification code", "getVerificationCode": "Get verification code", "please_obtain_the_dynamic_OTP_code_from_the_Authy_or_Google_Authenticator": "Please obtain the dynamic OTP code from the Authy or Google Authenticator mobile app", "OTPVerification": "OTP Verification", "accountPasswordVerification": "Account Password Verification", "phoneNumberVerification": "Phone Number Verification", "emailVerification": "Email Verification", "unbindImmediately": "Unbind Immediately", "verifyImmediately": "Verify Immediately", "unbindOTP": "Unbind OTP", "unbindEmail": "<PERSON><PERSON><PERSON>", "otpCode": "OTP Code", "twoFactor": "Two-Factor Authentication", "sms": "SMS", "pleaseSelectAuthenticationMethod": "Please Select Authentication Method.", "loginAccount": "<PERSON><PERSON> Account", "oldPassword": "Old Password", "newPassword": "New Password", "pleaseEnterOldPassword": "Please Enter Old Password", "oldPasswordNotEqualNewPassword": "New and old passwords cannot be the same.", "confirmNewPassword": "Confirm New Password", "pleaseConfirmNewPassword": "Please Confirm New Password", "passwordsDoNotMatch": "The two new passwords do not match", "contactAdminForReset": "Please contact the system administrator to reset your password", "forgotOriginalPassword": "Forgot Original Password", "confirmAndRe-login": "Confirm and Re-login", "unBindFailed": "Unbinding Failed", "unbindPhone": "Unbind Phone", "fail": "Fail", "no_Phone_Number_is_available.": "No Phone Number for Verification is available.", "modifyLoginSessionDuration": "Modify Login Session Duration", "pleaseEnterSessionDuration": "Please enter a number between 1 and 180", "the_system_will_log_you_out_after_this_duration": "The system will log you out after this duration. The default is 30 minutes, with a range of 1 to 180 minutes.", "identityVerification": "Identity Verification", "newEmailVerification": "New Email Verification", "complete": "Complete", "bindSuccessful": "Bind Successful", "next": "Next", "previous": "Previous", "newEmailAddress": "New Email Address", "installApplication": "Install Application", "bindOTP": "Bind OTP", "bindSecurityDevice": "Bind Security Device for Account {{userId}}. Please follow the steps below to complete the binding process.", "downloadInstallAuthyGoogleAuthenticator": "Please download and install Authy/Google Authenticator (Authentication App) on your mobile device.", "iPhoneSearchAppStore": "iPhone: Search for Authy/Google Authenticator (Authentication App) in the App Store.", "androidSearchGooglePlayStore": "Android: Search for Authy/Google Authenticator (Authentication App) in the Google Play Store.", "afterInstallationClickNext": "After installation, click Next to proceed to the binding page (if already installed, proceed directly to the next step).", "bindingSecurityDevice": "You have successfully bound the security device.", "doNotDeleteUninstallApp": "Do not delete or uninstall the app arbitrarily, as it will prevent you from logging into your account.", "unbindOTPFirst": "If you need to uninstall the app or change your phone, please unbind OTP first.", "to_scan_the_QR_code_below_and_obtain_a_6-digit_verification_code": "Use Authy/Google Authenticator (Authentication App) to scan the QR code below and obtain a 6-digit verification code", "otpVerificationCode": "OTP Verification Code", "pleaseEnterOTPVerificationCode": "Please Enter OTP Verification Code", "pleaseSelectPredefinedShortcutKeys": "Please Select Predefined Shortcut Keys", "manager": "Manager", "editorExitPrompt": "Editor Exit Prompt", "predefinedShortcutKeys": "Predefined Shortcut Keys", "enable": "Enable", "disable": "Disable", "continueOnExecutionError": "Continue on Execution Error", "paginationType": "Pagination Type", "scrollLoading": "<PERSON>roll Loading", "pageLoading": "Page Loading ", "encodingFormat": "Encoding Format", "enterContentAndUseShortcutKeys": "Enter Content and Use Shortcut Keys", "resetSuccessful": "Reset Successful", "resetToDefault": "Reset to De<PERSON>ult", "predefinedShortcutKeyManagement": "Predefined Shortcut Key Management", "predefinedShortcutKeysConflict_pre": "Predefined shortcut keys may conflict with browser shortcut keys, ", "predefinedShortcutKeysConflict_postfix": "please customize the settings", "enterActionShortcutKey": "Enter Action/Shortcut Key", "resetToDefaultValues": "Reset all shortcut key definitions to the predefined default values?", "shortcutKeys": "Shortcut Keys", "action": "Action", "displayOutputBasedOnInputFormat": "Display Output Based on Input Format", "generating": "Generating...", "maximum100Characters": "Maximum 100 Characters", "example": "Example", "displayOutputEffectOfCurrentInputFormat": "Display Output Effect of Current Input Format", "date": "Date", "time": "Time", "dateTime": "DateTime", "input": "Input", "resultSetDisplayFormat": "Result Set Display Format", "successful": "Successful", "permissionDetails": "Permission Details", "permissionType": "Permission Type", "permissionLevel": "Permission Level", "resource": "Resource", "resourceType": "Resource Type", "role": "Role", "effectiveTime": "Effective Time", "authorizedBy": "Authorized By", "authorizationSource": "Authorization Source", "authorizedIP": "Authorized IP", "authorizationTime": "Authorization Time", "settingSuccessful": "Setting Successful", "settingFailed": "Setting Failed", "commonFormatting": "Common Formatting", "year": "Year", "month": "Month", "day": "Day", "dayOfWeek": "Day of the Week", "organizeParameterList": "Organize Parameter List", "YYYY": "4-digit number representing the year, for example: 2023", "yyyy": "4-digit number representing the year, for example: 2023", "YY": "2-digit number representing the year, for example: 23", "MM": "2-digit number representing the month, for example: 01 represents January, 12 represents December", "MMM": "Abbreviated form of the month, for example: Jan represents January, Dec represents December", "MMMM": "Full form of the month, for example: January represents January, December represents December", "dd": "2-digit number representing the day, for example: 01 represents the 1st, 31 represents the 31st", "ddd": "Abbreviated form of the day of the week, for example: Mon represents Monday, Sun represents Sunday", "dddd": "Full form of the day of the week, for example: Monday represents Monday, Sunday represents Sunday", "HH": "24-hour clock hour, for example: 00 represents midnight, 23 represents 11 PM", "hh": "12-hour clock hour, for example: 01 represents 1 AM, 12 represents 12 PM", "mm": "Minutes, for example: 00 represents on the hour, 30 represents 30 minutes past the hour", "ss": "Seconds, for example: 00 represents on the minute, 59 represents 59 seconds past the minute", "a": "AM/PM indicator, for example: AM represents morning, PM represents afternoon", "personal.preferenceSetting.constant.example": "| Symbol | Meaning | Example |<br/>\n|--------|---------|---------|<br/>\n| G      | Era text display | AD (Anno <PERSON>) |<br/>\n| u      | Year | 2023 |<br/>\n| У      | AD Year | 2023 |<br/>\n| D      | Nth day of the year | 300 |<br/>\n| M or L | Month | 7; 07; Jul; July; |<br/>\n| d      | Nth day of the month | 9 |<br/>\n| Q or q | Quarter of the year | 2; 02; Q2; 2nd quarter |<br/>\n| Y      | Year based on weeks | 1997; 97 |<br/>\n| w      | Week of the year based on weeks | 30 |<br/>\n| W      | Nth week of the month | 3 |<br/>\n| E      | Day of the week | Tue; Tuesday; T |<br/>\n| e or c | Localized weekday | 2; 02; Tue; Tuesday; T |<br/>\n| F      | Nth week of the month | 2 |<br/>\n| a      | AM (morning) or PM (afternoon) | PM |<br/>\n| h      | 12-hour format (1 - 12) | 10 |<br/>\n| K      | 11-hour format (1 - 11) | 1 |<br/>\n| k      | 24-hour format (1 - 24) | 14 |<br/>\n| H      | 24-hour format (0 - 23) | 1 |<br/>\n| m      | Minutes in an hour | 30 |<br/>\n| s      | Seconds in a minute | 43 |<br/>\n| S      | Milliseconds of the current second | 800 |<br/>\n| A      | Milliseconds of the current day | 1111 |<br/>\n| n      | Nanoseconds of the current second | 987654321 |<br/>\n| N      | Nanoseconds of the current day | 1234000000 |<br/>\n| V      | Time zone ID | America/Los_Angeles; Z; -08:30 |<br/>\n| z      | Time zone name | Pacific Standard Time; PST |<br/>\n| O      | Localized time zone offset | GMT + 8; GMT + 08:00; UTC - 08:00 |<br/>\n| X      | Offset - X | Z; -08; -0830; -08:30; -083015; -08:30:15 |<br/>\n| x      | Offset - x | +0000; -08; -0830; -08:30; -083015; -08:30:15 |<br/>\n| Z      | Offset - Z | +0000; -0800; -08:00; |<br/>\n| p      | Pad modifier | 1 |<br/>\n| '      | Separator |  |<br/>\n| ''     | Non-parsed text |  |<br/>\n| [      | Start of optional part |  |<br/>\n| ]      | End of optional part |  |", "hello": "Hello", "newbieSceneGuide": "Newbie Scene Guide", "personalFolder": "Personal Folder", "taskCenter": "Task Center", "versionInfo": "Version Info", "auditAgentDownload": "Audit Agent Download", "executionHistory": "Execution History", "logout": "Logout", "confirm": "Confirm", "confirmExit": "Are you sure you want to exit? Exiting will cause any changes you made to the SQL statements to be lost. It is recommended to back up.", "welcomeToOnboarding": "Welcome to the Onboarding Guide", "firstLoginInstructions": "On your first login, you can complete the relevant feature setup and operations following the guide prompts. You can revisit this page in the User Center.", "databaseManagement": "Database Management", "connectionManagement": "Connection Management", "createConnectionOperations": "Create Connection Operations", "newConnectionWizard": "New Connection Wizard", "subjectAuthorization": "Subject Authorization", "authorizationByUser": "Authorization By User", "subjectAuthorizationWizard": "Subject Authorization Wizard", "objectAuthorization": "Object Authorization", "authorizationByDatabaseObject": "Authorization By Database Object", "objectAuthorizationWizard": "Object Authorization Wizard", "automaticAuthorization": "Automatic Authorization", "createPermissionSet": "Create Permission Set through conditions to automatically apply to users", "automaticAuthorizationWizard": "Automatic Authorization Wizard", "ruleManagement": "Rule Management", "sqlAuditRules": "SQL Audit Rules", "ruleTemplateWizard": "Rule Template Wizard", "sensitiveFields": "Sensitive Fields", "setMaskingAlgorithm": "Set Masking Algorithm for sensitive fields in tables", "sensitiveFieldsWizard": "Sensitive Fields Wizard", "maskedData": "Masked Data", "enableRules": "Enable rules to mask sensitive data upon discovery", "maskedDataWizard": "Masked Data Wizard", "maskingScan": "<PERSON><PERSON>", "createScanTask": "Create scan task to periodically scan sensitive data", "maskingScanWizard": "Masking <PERSON><PERSON>", "highRiskOperations": "High-Risk Operations and Import/Export Settings", "securitySettingsWizard": "Security Settings Wizard", "process": "Process", "myApplications": "My Applications", "accessRequest": "Access Request", "elevationWizard": "Elevation Wizard", "customProcessDesign": "Custom Process Design", "customChangeRequestProcess": "Custom Change Request Process", "customProcessDesignWizard": "Custom Process Design Wizard", "uploadFile": "Upload File", "newFolder": "New Folder", "detailsInfo": "Details Information", "tileView": "Tile View", "searchInCurrentFolder": "Search In Current Folder", "rename": "<PERSON><PERSON>", "copyFileAddress": "Copy File Address", "goToParentFolder": "Go to Parent <PERSON>er", "downloaded": "Downloaded", "processing": "Processing", "creating": "Creating", "inQueue": "In Queue", "fileName": "File Name", "exportStatement": "Export Statement", "exportFormat": "Export Format", "exportRows": "Export Rows", "taskCreationTime": "Task Creation Time", "fileKey": "File Key", "exportFileEncrypted": "Export file is encrypted, key is", "taskStatus": "Task Status", "operation": "Operation", "view": "View", "download": "Download", "delete": "Delete", "refreshList": "Refresh List", "downloadTask": "Download Task", "textImportTask": "Text Import Task", "confirmDeleteTask": "Confirm Delete This Task", "connectionType": "Connection Type", "log": "Log", "operationType": "Operation Type", "import": "Import", "export": "Export", "fileDownload": "File Download", "logDownload": "Log Download", "fileType": "File Type", "continueImport": "Continue Import", "enterOperationStatement": "Enter operation statement for search", "frequentConnections": "Frequent Connections", "quickOpen": "Quick Open", "totalRecords": "Total {{total}} records", "connectionName": "Connection Name", "filterConditions": "Filter Conditions", "executeConnection": "Execute Connection", "statementType": "Statement Type", "executionResult": "Execution Result", "executionSuccess": "Execution Success", "executionFailure": "Execution Failure", "startDate": "Start Date", "endDate": "End Date", "query": "Query", "reset": "Reset", "confirmExportCurrentFilter": "Confirm Export Current Filter Results", "taskExecutionHistoryExport": "Task, Execution History Export", "executionCompleted": "Execution Completed, File Generated Successfully", "dataSource": "Data Source", "operationObject": "Operation Object", "userName": "User Name", "databaseType": "Database Type", "applicationServerIP": "Application Server IP", "databaseIP": "Database IP", "databasePort": "Database Port", "operationStatement": "Operation Statement", "dataVersion": "Data Version", "clientIP": "Client IP", "operationResult": "Operation Result", "errorMessage": "Error Message", "affectedRows": "Affected Rows", "durationMs": "Duration (ms)", "executionWindowID": "Execution Window ID", "executionMode": "Execution Mode", "source": "Source", "confirmDelete": "Are you sure you want to delete?", "fileNotFound": "File not found", "confirmTerminateTask": "Are you sure you want to terminate this task?", "yes": "Yes", "no": "No", "synchronizedResources": "Synchronized Resources", "targetTable": "Target Table", "exportedResources": "Exported Resources", "exportResultSetNotSupported": "Exporting result sets does not support displaying exported resources", "cancelTask": "Cancel Task", "confirmCancelTask": "Are you sure you want to cancel this task?", "operationSuccess": "Operation Successful", "deleteSuccess": "Delete Successful", "cancelSuccess": "Cancel Successful", "terminate": "Terminate", "viewDetails": "View Details", "timePeriod": "Time Period", "batchOperation": "Batch Operation", "batchDelete": "<PERSON><PERSON> Delete", "batchDownload": "Batch Download", "batchTerminate": "Batch Terminate", "confirmTerminate": "Are you sure you want to terminate?", "last7Days": "Last 7 Days", "last15Days": "Last 15 Days", "last30Days": "Last 30 Days", "textImport": "Text Import", "batchExecute": "Batch Execute", "syncDataDictionary": "Sync Data Dictionary", "dataCopy": "Data Copy", "autoExecute": "Auto Execute", "immediateExecute": "Immediate Execute", "scheduledExecute": "Scheduled Execute", "ignore": "Ignore", "taskNumber": "Task Number", "creator": "Creator", "creationTime": "Creation Time", "status": "Status", "description": "Description", "inProgress": "In Progress", "completed": "Completed", "paused": "Paused", "pending": "Pending", "taskName": "Task Name", "target": "Target", "startTime": "Start Time", "endTime": "End Time", "taskInformation": "Task Information", "exportFile": "Export File", "taskType": "Task Type", "exportType": "Export Type", "taskEndTime": "Task End Time", "createInfo": "Create Info", "importConfiguration": "Import configuration", "mappingRelationshipDisplay": "Mapping relationship display", "importFile": "Import File", "fieldDelimiter": "Field Delimiter", "recordLineDelimiter": "Record Line Delimiter", "fieldIdentifier": "Field Identifier", "fieldNameLine": "Field Name Line", "firstDataLine": "First Data Line", "lastDataLine": "Last Data Line", "decimalPointSymbol": "Decimal Point Symbol", "dateTimeSorting": "Date Time Sorting", "binaryDataEncoding": "Binary Data Encoding", "sourceField": "Source Field", "targetField": "Target Field", "attachmentInformation": "Attachment information", "indexFile": "Index File", "sqlAuditResult": "Sql Audit Result", "executionMethod": "Execution Method", "taskCycle": "Task Cycle", "executionErrorHandler": "Execution Error <PERSON>", "executing": "Executing", "pendingExecution": "Pending Execution", "waitingForAutoExecution": "Waiting For Auto Execution", "waiting": "Waiting", "currentlyExecuting": "Currently Executing", "taskCompleted": "Task Completed", "taskFailed": "Task Failed", "taskClosed": "Task Closed", "terminating": "Terminating", "taskTerminated": "Task Terminated", "terminationSuccessful": "Termination Successful", "terminationFailed": "Termination Failed", "batchExecutionSuccessful": "Batch Execution Successful", "batchExecutionFailed": "Batch Execution Failed", "confirmTaskReExecution": "Confirm task re-execution", "reExecutionWillStartFromFirstScript": "Task re-execution will start from the first script in sequence. please ensure that the effects of previous executions have been cleared.", "confirmTermination": "Confirm Termination", "thisTerminationWillStopExecutingFiles": "This termination operation will stop the currently executing files.", "confirmReExecutionOfThisScript": "Confirm re-execution of this script", "reExecutionOfThisScriptPleaseEnsureEffectsCleared": "Re-execution of this script. please ensure that the effects of previous executions have been cleared.", "thisTerminationWillStopEntireTask": "This termination operation will terminate the entire task", "batchTerminationSuccessful": "Batch Termination Successful", "batchTerminationFailed": "Batch Termination Failed", "queryTaskListFailed": "Query Task List Failed", "downloadFileFailed": "Download File Failed", "downloadLogFailed": "Download Log Failed", "downloadErrorLogFailed": "Download Error Log Failed", "replace": "Replace", "database": "Database", "executionStatus": "Execution Status", "executionLog": "Execution Log", "taskDetails": "Task Details", "executionDetails": "Execution Details", "taskDescription": "Task Description", "reExecution": "Re Execution", "start": "Start", "taskLog": "Task Log", "newBatchExecutionTask": "Create Batch Execution", "createBatchExecutionTaskSuccessful": "Create batch execution task successful", "createBatchExecutionTaskFailed": "Create batch execution task failed", "fileParsingFailed": "File Parsing Failed", "singleUploadParsingFailed": "Single upload parsing failed", "confirmDeleteTemplateFile": "Confirm delete template file? deletion will also clear all attachments", "deleteUploadedSqlFileFailed": "Delete uploaded sql file failed", "maxLength20Characters": "Maximum length does not exceed 20 characters", "downloadSuccessful": "Download Successful", "sqlAuditResultContainsError": "Sql audit result contains error level and is not allowed to proceed to the next step", "taskCycleExpressionInvalid": "Task cycle expression is invalid", "downloadAuditResult": "Download Audit Result", "uploadAttachment": "Upload Attachment", "batchExecutionSupportsPlainTextOnly": "Batch execution supports only plain text type files", "confirmDing": "Confirm", "pleaseSelectAttachmentUpload": "Please select attachment upload", "attachmentUpload": "Attachment Upload", "clickUploadFolder": "Click Upload Folder", "uploadFolder": "Upload Folder", "supportUploadFolderAndCompressedFiles": "Support upload folder and compressed files, files can contain sql files and execution order files (index files) to be executed", "failedToGetUploadFileSizeLimit": "Failed to get upload file size limit", "sqlAudit": "SQL Audit", "pleaseSelectExecutionMethod": "Please select execution method", "pleaseEnterTaskCycle": "Please enter task cycle", "validation": "Validation", "cronGenerator": "Cron Generator", "pleaseSelectExecutionErrorHandler": "Please select execution error handler", "inputDescriptionContent": "Input description content", "downloadTemplateFileFailed": "Download template file failed", "fileSizeExceeds10MLimit": "File size exceeds 10M limit", "templateFolderDownload": "Template folder download", "templateFileContainsExecutionOrderTemplateAndSqlExample": "Template file contains execution order file template and sql file example, you can download and refer to it", "templateDownload": "Template Download", "ifYouKnowExecutionLogic": "If you already know the execution logic, you can directly select the file for upload", "selectZipArchive": "Select zip archive", "selectFolder": "Select Folder", "collapse": "Collapse", "expand": "Expand", "details": "Details", "sequenceNumber": "Serial Number", "executionStatement": "Execution Statement", "parsingResult": "Parsing Result", "versionComparison": "Version Comparison", "startTimeFa": "Start Time", "unnamedFolder": "UnnamedFolder", "newFolderCreated": "New folder 「{{folderName}}」 created", "nameCannotBeEmpty": "Name cannot be empty", "nameMaxLength25Characters": "name must not exceed 25 characters", "move": "Move", "directoryContent": "Contains {{val}} items, you can view {{count}} items", "directorySize": "Total Size", "bit": "Bytes", "fileSize": "File Size", "modifiedTime": "Last modified at {{time}}", "nickNameHint": "Name can be Up to 25 characters long", "updateTime": "Modification Date", "type": "Type", "size": "Size", "copySuccess": "Copy Successful", "moveSuccess": "Move Successful", "moveTargetPath": "Please select the target folder", "ascendingOrder": "Ascending Order", "descendingOrder": "Descending Order", "noInformation": "No Information", "exportTotalRows": "Export total rows", "refreshTask": "Refresh Task", "viewLog": "View Log", "initialize": "Initialize", "running": "Running", "pause": "Pause", "sourceLess": "Source", "waitingDuration": "Waiting Duration", "dataVolume": "Data Volume", "includesData": "Includes Data", "newTable": "New Table", "remainingTime": "Remaining Time", "targetIndexField": "Target  {{index}}  Field", "sourceIndexField": "Source  {{index}}  Field", "LoginSettingManagerTip": "Login settings are managed by the administrator", "forceSMSLogin": "The system enforces SMS authentication login", "forceOTPLogin": "The system enforces OTP authentication login", "editPhone": "If you need to change your phone number, please contact the administrator", "logDetails": "Log Details", "info": "Info", "loading": "Loading", "executionTime": "Execution Time", "failedToReplaceFile": "Failed to replace file", "logInfo": "Log Info", "databaseName": "Database Name", "tableName": "Table Name", "importFileName": "Import File Name", "fileReadProgress": "File read progress", "continueImportStart": "Continue import start", "schema": "schema", "textImportBatchDeleteTip": "This operation will directly delete the task for tasks that have already ended; for tasks that are in progress, it will immediately interrupt and delete the task after reverting the data that supports rollback.", "textImportDeleteTip1": "This operation will interrupt the task and revert the executed data.", "textImportDeleteTip2": "This operation will interrupt the task, but the executed data cannot be reverted.", "btn.batch_export": "Batch Download", "btn.download": "Download", "tip_title.select_task": "Please select the execution file record first", "tip_title.no_export_file": "The selected task has not generated a result file, download unavailable", "tip_title.no_result_file": "No result file generated, unable to download", "ntf_title.batch_execute_export": "Task Batch Execution Result Download", "ntf_desc.exe_complete_fl_generated": "Execution completed, file generated successfully", "ntf_title.execute_log_download": "Task Execution Statement Result Download", "resetSuccessfult": "Reset Successful", "abort": "abort", "aborting": "aborting", "copyFailed": "Co<PERSON> failed", "shortcutValidate": "Shortcut must be a combination of keys, please re-enter", "shortcutDisabledCustomEdit": "The currently used plugin version does not support custom editing"}