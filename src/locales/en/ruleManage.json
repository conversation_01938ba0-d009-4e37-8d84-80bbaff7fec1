{"batch_edit_rules": "Batch Edit Rules", "edit_rule": "Edit Rule", "description": "Description", "level": "Level", "parameter": "Parameter", "type_cannot_be_empty": "Type cannot be empty", "select_type": "Please select type", "value_cannot_be_empty": "Value cannot be empty", "enter_value": "Please enter value", "description_cannot_be_empty": "Description cannot be empty", "enter_description": "Please enter description", "scenario": "<PERSON><PERSON><PERSON>", "batch_update_success": "Batch update successful", "update_rule_status_success": "Rule status update successful", "select_at_least_one_scenario": "Please select at least one scenario", "update_rule_success": "Rule update successful", "batch_edit_success": "<PERSON>ch edit successful", "rule": "Rule", "status": "Status", "current_role": "Your current role is", "no_permission_for_rule_management": "No permission for rule management", "operation": "Operation", "edit": "Edit", "rule_status": "Rule Status", "all": "All", "enabled": "Enabled", "disabled": "Disabled", "batch_enable": "Batch Enable", "batch_disable": "<PERSON>ch Disable", "batch_edit": "<PERSON>ch Edit", "batch_operation": "Batch Operation", "edit_determine_values": "Edit Determine Values :>>", "parse_string_error": "Error parsing string", "template_name": "Template Name", "enter_template_name": "Please enter template name", "template_description": "Template Description", "enter_template_description": "Please enter template description", "database_type": "Database Type", "select_database_type": "Please select database type", "select_data_source": "Please select data source", "effective_connection": "Effective Connection", "select_connection": "Please select connection", "notice": "notice (Notice)", "warning": "warning (Warning)", "error": "error (Error)", "work_order": "Work Order", "sql_window": "SQL Window", "batch_execution": "Batch Execution", "gui_way_sql": "GUI Way SQL", "submit_success": "Submit successful", "return_to_rule_management": "Return to Rule Management", "new_template_success": "New template successful", "database_management": "Database Management", "new_rule_template": "New Rule Template", "return_from_rule_management": "Return from Rule Management", "fill_in_basic_information": "Fill in Basic Information", "set_rule_level": "Set Rule Level", "complete": "Complete", "reset": "Reset", "next_step": "Next Step", "previous_step": "Previous Step", "submit": "Submit", "edit_rule_template": "Edit Rule Template Attributes", "copy_rule_template": "Copy Rule Template", "view_details": "View Details", "no_effective_connection": "No Effective Connection", "rule_management_list": "Rule Management List", "display_template_info": "Display template information, click template name to view specific rules", "edit_effective_connection": "Here you can edit which connections this rule template applies to", "delete_success": "Delete successful", "copy_success": "Copy successful", "apply_data_types": "Apply Data Types", "confirm_delete": "Confirm Delete", "cancel": "Cancel", "delete": "Delete", "search_template_name": "Search Template Name", "rule_template": "Rule Template", "template_details": "Template Details", "set_specific_rules": "Here you can set the specific rules' scenarios, levels, parameters, and enabled/disabled status", "rule_name": "Rule Name", "rule_level": "Rule Level", "rule_management": "Rule Management", "return": "Return", "edit_rule_template_effective_connection": "Edit Rule Template Effective Connection", "update_success": "Update Success", "description1": "Description", "confirm": "Confirm", "copy": "Copy", "rule_template_details": "Rule Template Details", "templateName": "Template Name", "rule_edit_content": "Template Content", "rule_edit_attribute": "Template Attribute"}