{"updateSuccess": "Update Success", "noData": "No Data", "userHasOtherPermissionsAtThisLevel": "This user has other permissions set at this level", "loadMore": "Load More...", "paginationLoadMoreError": "Pagination load more error", "selectDatabaseElement": "Please select database element", "selectAll": "Select All", "cancel": "Cancel", "confirm": "Confirm", "onlyOnePermissionAllowed": "Only one permission allowed", "otherControlMethods": "Other control methods", "pleaseSelectPermission": "Please Select Permission", "retainPreviousUserPermissionLevel": "Retain Previous User Permission Level", "resetPermissionsOrSelectToolPermissions": "Please reset permissions or select tool permissions", "permissionUpgradeFailed": "Permission Upgrade Failed :>>  ", "pleaseSelectDatabaseElement": "Please Select Database Element", "onlySameDataSourceTypeAllowed": "Only Same Data Source Type Allowed", "onlySameLevelElementsAllowed": "Only Same Level Elements Allowed", "onlySameTypeElementsAllowed": "Only Same Type Elements Allowed", "addPermission": "Add Permission", "permissionDisplayDependsOnSelectedResource": "The display of permissions depends on the selected resource. If the selected resource includes schema or higher levels, the permission level (permission set) will be displayed; otherwise, the corresponding operations supported by the resource will be shown.", "toolPermission": "Tool Permission", "pleaseSelectToolPermission": "Please Select Tool Permission", "authorizeTo": "Authorize To ", "user": " User", "department": " Department", "company": " Company", "currentAuthorizationObjectIsTopLevelOrganizationNode": "The current authorization object is the Top-Level organization node (company), please operate with caution.", "add": "Add", "copy": "Copy", "selectUserToCopyTo": "Select User to Copy To", "pleaseSelectUser": "Please Select User", "additionalPermissionsAllowed": "Additional Permissions Allowed", "username": "Username", "authorizationSource": "Authorization Source", "permanent": "Permanent", "operation": "Operation", "confirmRemoval": "Confirm Removal?", "confirm2": "Confirm", "remove": "Remove", "view": "View", "viewDetails": "View Details", "confirmRemoveUserResource": "Confirm Remove This User Resource", "setSuccess": "Set Success", "copySuccess": "Copy Success", "removeSuccess": "Remove Success", "authorizeSuccess": "Authorize Success", "editSuccess": "Edit Success", "confirmBatchRemoveUserResource": "Confirm batch remove this user resource", "batchRemovePermissions": "Batch Remove Permissions", "batchCopyPermissions": "Batch Copy Permissions", "batchModifyEffectiveTime": "Batch Modify Effective Time", "userPermissionDetails": "User Permission Details", "enterResourceOrPermissionNameToSearch": "Enter resource name/permission name to search", "subjectAuthorization": "Subject Authorization", "authorization": "Authorization", "batchOperation": "Batch Operation", "returnToViewAllResources": "Return to view all resources", "deleteSuccess": "Delete Success", "enterSearchContent": "Enter Search Content", "organizationStructure": "Organization Structure", "selectDepartmentOrUserHierarchyForAuthorization": "Select the department or user hierarchy for authorization here", "clickAuthorizeAndSelectDataSourceAndPermissions": "Click authorize, select the corresponding data source and permissions", "databaseManagement": "Database Management", "manualAuthorization": "Manual Authorization", "resource": "Resource", "resourceType": "Resource Type", "permission": "Permission", "permissionType": "Permission Type", "affectedUsers": "Affected Users", "permissionDetails": "Permission Details", "linkedRole": "Linked Role", "effectiveTime": "Effective Time", "permissionSource": "Permission Source", "authorizer": "Authorizer", "authorizationIP": "Authorization IP", "authorizationTime": "Authorization Time", "tb_render.data_source_operation_permission": "Data Source Operation Permission", "tb_render.sensitive_resource_access_permission": "Sensitive Resource Access Permission", "tb_render.high_risk_operation_permission": "High-Risk Operation Permission", "tb_render.tool_permission": "Tool Permission", "tb_render.automatic_authorization": "Automatic Authorization", "tb_render.process_privilege_escalation": "Process Privilege Escalation", "tb_render.manual_authorization": "Manual Authorization"}