{"sdo_tsx_comment": "======== 数据操作 ========", "sdo_enable_display_authorized_objs": "Enable: display only authorized database objects.", "sdo_close_show_all_objs": "Close: Show all database objects", "sdo_rights_acquired": "Successful Acquisition of Rights", "sdo_deleted": "Successfully Deleted", "sdo_application_form": "Application Form", "sdo_existing_permissions": "Existing Permissions", "sdo_confirm_delete_node": "Are You Sure You Want to Delete This Node?", "sdo_ok": "OK", "sdo_cancel": "Cancel", "sdo_del": "Delete", "sdo_time_range_end_greater_current": "Please Re-select the Time, End Time Must Be Greater Than Current Time", "sdo_time_range_start_less_end": "Start Time Must Be Less Than End Time", "sdo_close": "Close", "sdo_applications_list": "Application List", "sdo_confirm_clear_application_list": "Are You Sure You Want to Clear the Application List?", "sdo_clear": "Clear", "sdo_submit_application_form": "Submit Application Form", "sdo_title": "Title", "sdo_remark": "Remark", "sdo_submit_application": "Submit Application", "sdo_executing": "Executing", "sdo_execution_time": "Execution Time", "sdo_display_line_count": "Display Line Count", "sdo_column": "Column", "sdo_select_encoding": "Select Encoding", "sdo_encoding_format_warning": "Please Reopen the File After Switching the Encoding Format", "sdo_select_line_breaks": "Select Line Breaks", "sdo_no_tree_node_selected": "No Tree Node Selected", "sdo_no_tree_node_information": "No Tree Node Information Available", "sdo_tree_node_information": "Tree Node Information", "sdo_rename": "<PERSON><PERSON>", "sdo_set_connection_color": "Set Connection Color", "sdo_set_connection_tag": "Close other tabs", "sdo_create_new_query": "Create New Query", "sdo_close_all_windows": "Close All Data Query Workspace Windows", "sdo_close_all_windows_confirmation": "Are You Sure You Want to Close All Data Query Workspace Windows?", "sdo_close_windows_error": "There Are Pending Transactions. Please Commit or Cancel and Try Again.", "sdo_force_close_confirmation": "Confirm Force Close?", "sdo_invalid_object": "Invalid Object", "sdo_function": "Function", "sdo_function_lower": "Function", "sdo_stored_procedure": "Stored Procedure", "sdo_stored_procedure_lower": "Stored Procedure", "sdo_close_confirmation": "Are You Sure You Want to Close?", "sdo_unsaved_changes_confirmation": "Unsaved Changes Will Be Lost. Are You Sure You Want to Close?", "sdo_add_table": "Add Table", "sdo_design_table": "Design Table", "sdo_add_view": "Add View", "sdo_create_view": "Create View", "sdo_design_view": "Design View", "sdo_view_basic_information": "View Basic Information", "sdo_version_name": "Version Name", "sdo_compile": "Compile", "sdo_new_item": "New", "sdo_executing_status": "Executing", "sdo_execution_completed": "Completed Execution", "sdo_view_results": "View Results", "sdo_view_errors": "View Errors", "sdo_name": "Name", "sdo_name_lower": "name", "sdo_view": "View", "sdo_compile_time": "Compilation Time", "sdo_refresh_list": "Refresh List", "sdo_error_log": "<PERSON><PERSON><PERSON>", "sdo_choose_color": "Choose Color", "sdo_new_win_name": "New Window Name", "sdo_table_creation_suc": "Table Created Successfully!", "sdo_table_name_required": "Please Complete the Table Name.", "sdo_connection_name": "Connection Name", "sdo_database": "Database", "sdo_table_name": "Table Name", "sdo_table_comment": "Table Comment", "sdo_field": "Field", "sdo_sql_preview": "SQL Preview", "sdo_submit": "Submit", "sdo_sql_preview_placeholder": "After completing the database modification, you can view the SQL statements here, confirm they are correct, and then click to save and execute.", "sdo_field_name": "Field Name", "sdo_length": "Length", "sdo_decimal_point": "Decimal Point", "sdo_default_value": "Default Value", "sdo_comment": "Comment", "sdo_virtual": "Virtual", "sdo_allow_null": "Allow <PERSON>", "sdo_auto_increment": "Automatic Increment", "sdo_primary_key": "Primary Key", "sdo_add_column": "Add Column", "sdo_del_column": "Delete Column", "sdo_save": "Save", "sdo_sort": "Sort", "sdo_column_name": "Column Name", "sdo_precision": "Precision", "sdo_miss_parameter": "Miss Parameter", "sdo_table": "Table", "sdo_tables": "tables", "sdo_generate": "Generate", "sdo_index": "Index", "sdo_sql_cannot_empty": "SQL Statement Cannot Be Empty", "sdo_view_created_suc": "View Created Successfully", "sdo_enter_view_name": "Please Enter View Name", "sdo_view_name": "View Name", "sdo_view_comment": "View Comment", "sdo_view_supper": "View", "sdo_view_lower": "view", "sdo_execute_results": "First 100 Rows of Execution Results", "sdo_test": "Test", "sdo_index_name": "Index Name", "sdo_index_type": "Index Type", "sdo_is_unique": "Is Unique", "sdo_index_statement_generation_failed": "Index Statement Generation Failed", "sdo_add_index": "Add Index", "sdo_del_index": "Delete Index", "sdo_execution_statement_empty": "Execution Statement Is Empty", "sdo_totally_executed": "Totally Executed", "sdo_records": "records", "sdo_succeeded": "Succeeded", "sdo_failed": "Failed", "sdo_execute": "Execute", "sdo_index_design": "Index Design", "sdo_execution_suc": "Execution Successful", "sdo_partial_statement_execution_fail": "Partial Statement Execution Failure", "sdo_execution_fail": "Execution Failure", "sdo_option": "Option", "sdo_engine_type": "Engine Type", "sdo_data_model": "Data Model", "sdo_constraint_name": "Constraint Name", "sdo_constraint_type": "Constraint Type", "sdo_condition": "Condition", "sdo_enable": "Enable", "sdo_referenced_table": "Referenced Table", "sdo_referenced_field": "Referenced Field", "sdo_update_rule": "Update Rule", "sdo_delete_rule": "Delete Rule", "sdo_unique": "Unique", "sdo_sort_key": "Sort Key", "sdo_trigger_name": "Trigger Name", "sdo_trigger_timing": "<PERSON><PERSON>ing", "sdo_trigger_event": "Trigger Event", "sdo_level": "Level", "sdo_refd_old_value": "Old Value Reference", "sdo_refd_new_value": "New Value Reference", "sdo_updated_field": "Updated Field", "sdo_when_clause": "WHEN Clause", "sdo_save_fail": "Save Failed", "sdo_add_1": "Add", "sdo_referenced_schema": "Referenced <PERSON><PERSON><PERSON>", "sdo_referenced_database": "Referenced Database", "sdo_statement_block": "Statement Block", "sdo_trigger_function_pattern": "Trigger Function Pattern", "sdo_param": "Parameter", "sdo_partition_name": "Partition Name", "sdo_partition_val": "Partition Value", "sdo_partitions_count": "Partitions Count", "sdo_subpartitions_count": "Subpartitions Count", "sdo_partition_type": "Type", "sdo_partition_expression": "Expression", "sdo_partition_scheme": "Scheme", "sdo_partition_function": "Function", "sdo_tablespace": "Tablespace", "sdo_preview": "Preview", "sdo_subpartition_type": "Subpartition Type", "sdo_definition": "Definition", "sdo_edit_view_sql_preview": "Generated SQL preview after editing the view name or definition SQL, Only by clicking Submit is the design considered complete", "sdo_allow": "Allow", "sdo_is_primary_key": "Is Primary Key", "sdo_table_design_suc": "Table Design Successful", "sdo_table_design_fail": "Table Design Failed", "sdo_statement_execution_suc": "Statement Executed Successfully", "sdo_table_comment_lower": "Table Comment", "sdo_restore": "Rest<PERSON>", "sdo_modify_table": "Modify Table", "sdo_right_click_menu": "Right-<PERSON><PERSON>", "sdo_copy_cell": "Copy Cell", "sdo_definition_not_found": "Definition Not Found", "sdo_editor_txt": "Maximum Execution Lines for Editor Recommended to Be No More Than 10,000", "sdo_sql_audit_result": "SQL Audit Result", "sdo_continue_execution": "Continue Execution", "sdo_return_modifications": "Return Modifications", "sdo_execute_on_err": "Execute on Error", "sdo_return_modifications_tooltip": "Check This Option to Ignore Errors and Execute Subsequent SQL Statements When an Error Is Encountered During Execution.", "sdo_modification_suc": "Modification Successful", "sdo_edit_alias": "<PERSON>", "sdo_save_suc": "Saved Successfully", "sdo_favorite_statement": "Favorite Statement", "sdo_alias": "<PERSON><PERSON>", "sdo_alias_input_placeholder": "Set alias for easy recall and search", "sdo_statement": "Statement", "sdo_statement_rule_txt": "Please enter a statement", "sdo_favorite_del_suc": "Favorite Deleted Successfully", "sdo_update_time": "Update Time", "sdo_usage_count": "Usage Count", "sdo_operation": "Operation", "sdo_use": "Use", "sdo_favorite_management": "Favorite Management", "sdo_return": "Return", "sdo_favorite_management_search": "Search by query/alias/creator", "sdo_pending_submission_statement": "Pending Submission Statement", "sdo_export_all": "Export All", "sdo_select_export_statement": "Please select export statement", "sdo_no_export_permission": "No Export Permission", "sdo_create": "Create", "sdo_case_conversion": "Case Conversion", "sdo_format": "Format", "sdo_collapse_all": "Collapse All", "sdo_expand_all": "Expand All", "sdo_enlarge_font": "Enlarge <PERSON>", "sdo_reduce_font": "Reduce <PERSON>", "sdo_preflight": "Preflight", "sdo_run_select_statement": "Run Selected Statement", "sdo_terminate": "Terminate", "sdo_execution_plan": "Execution Plan", "sdo_pending_statement": "Pending Statement", "sdo_see_more": "See More", "sdo_commit_error_txt": "Please Commit or Rollback the Current Transaction First", "sdo_auto": "Automatic", "sdo_manual": "Manual", "sdo_progress": "Progress", "sdo_fav_del_suc": "Favorite Deletion Successful", "sdo_more": "More", "sdo_no_fav_yet": "No Favorites Yet", "sdo_no_stm_selected": "No Statements Selected", "sdo_add_fav": "Add to Favorites：{{val}}", "sdo_shortcut_list": "Shortcut List", "sdo_execut_select_stm": "Execute (Selected Statements)", "sdo_execut_plan_select_stm": "Execution Plan (Selected Statements)", "sdo_tips": "Tips", "sdo_quick_replace": "Quick Find and Replace", "sdo_mul_line_edit": "Multi-Line Editing", "sdo_comment_or_not_selected_lines": "Comment/Uncomment Selected Lines", "sdo_in_transaction_error_txt": "There Are Pending Transactions. Please Commit or Cancel Them Before Proceeding.", "sdo_select_connection": "Select connection", "sdo_select": "Select", "sdo_select_db_placeholder": "Select a database", "sdo_no_recent_doc": "No Recent Documents Available", "sdo_file_size_limit": "File Size Limit", "sdo_open_local_doc": "Open Document：{{val}}", "sdo_res_page_nums": "Pagination Size for Result Set", "sdo_personal_folder": "Personal Folder", "sdo_save_doc": "Save Document：{{val}}", "sdo_save_as": "Save As", "sdo_save_local": "Save Document", "sdo_save_doc_as": "Save Document As", "sdo_no_data": "No Data Available", "sdo_untitled_doc": "Untitled", "sdo_store_as": "Store As", "sdo_file_rule_req_txt": "Name Cannot Be Empty", "sdo_file_len_rule_txt": "Name Must Be 25 Characters or Less", "sdo_encoding": "Encoding", "sdo_encoding_rule_req_txt": "Encoding Cannot Be Empty", "sdo_select_file": "Select a File", "sdo_type": "Type", "sdo_file": "File", "sdo_only_supports": "Only Supports", "sdo_open_file": "Open File", "sdo_open_upper": "Open", "sdo_flashback_upper": "Flashback", "sdo_table_view_low": "Table and View", "sdo_table_view_warn_txt": "Please Select a Table or View", "sdo_col_group_low": "Column Group", "sdo_cus_field_error_txt": "Custom Fields Have Empty Values, Please Complete Them Before Adding", "sdo_select_table_error_txt": "Please Select the Base Table", "sdo_select_field_error_txt": "Please Select or Customize Fields", "sdo_has_empty_field_error_txt": "Custom Fields Cannot Be Empty", "sdo_check_items": "Check Items", "sdo_select_check_items": "Select Check Items", "sdo_none": "None", "sdo_only_read": "Read-Only", "sdo_enter_table_view_name": "Enter Table/View Name", "sdo_field_select": "Field Selection", "sdo_basic_info": "Basic Information", "sdo_basic_table_select": "Base Table Selection", "sdo_enter_field_name": "Enter Field Name", "sdo_enter_cus_field_name": "Enter Custom Field Name", "sdo_next": "Next", "sdo_confirm_sql": "Confirm SQL", "sdo_server_name": "Server Name", "sdo_version_upper": "Version", "sdo_process_upper": "Process", "sdo_memory_upper": "Memory", "sdo_used_memory": "Used Memory", "sdo_memory_peak_usage": "Memory Peak Usage", "sdo_memory_usage": "Memory Usage", "sdo_status": "Status", "sdo_client_connections": "Client Connections", "sdo_historical_connections": "Historical Connections", "sdo_historical_commands": "Historical Commands", "sdo_key_value_statistics": "Key-Value Statistics", "sdo_full_information_set": "Full Information Set", "sdo_enter_key_for_search": "Enter Key for Search", "sdo_terminal_unavailable": "Terminal Temporarily Unavailable", "sdo_mode_name": "Mode Name", "sdo_default_character_set": "Default Character Set", "sdo_default_collation": "Default Collation", "sdo_database_size": "Database Size", "sdo_field_upper": "Field", "sdo_foreign_key": "Foreign Key", "sdo_owning_table": "Owning Table", "sdo_index_col": "Indexed Columns", "sdo_index_sort_order": "Index Sort Order", "sdo_index_table_name": "Index Table Name", "sdo_foreign_key_name": "Foreign Key Name", "sdo_col_name": "Column Name", "sdo_refer_to_schema": "Refer to Schema", "sdo_refer_to_table": "Refer to Table", "sdo_refer_to_col_name": "Refer to Column Name", "sdo_on_del": "On Delete", "sdo_on_update": "On Update", "sdo_table_view": "Table View", "sdo_execute_log": "Execution Log", "sdo_clear_log": "Clear Log", "sdo_exception_log": "Exception Log", "sdo_explanation": "Explanation", "sdo_req_export_permiss": "Request Export Permission", "sdo_export_row_limit": "Export Row Limit", "sdo_export_limit_approv": "Approval required for export row count exceeding limit", "sdo_export_approv_def": "Regular export currently supports a maximum of 100,000 rows. If you need to export more data, it is not recommended to use regular export.", "sdo_res_export": "Result Set Export", "sdo_export_tips": "Export Tips", "sdo_not_supt_ex_field_types": "The field types that are not currently supported for export are", "sdo_fold_in": "Fold In", "sdo_for_details": "For Details", "sdo_file_name": "File Name", "sdo_fill_file_name": "Enter the file name", "sdo_export_format": "Export Format", "sdo_select_export_format": "Select the export format", "sdo_export_data_rows": "Export Rows", "sdo_fill_export_rows": "Fill in the number of export lines", "sdo_data_much_export_txt": "It is not recommended to use the data export function when the amount of exported data is too large", "sdo_export_line_limit": "Export Line Limit", "sdo_less_than_60000": "It is recommended that you do not export more than 60,000 rows.", "sdo_export_related_info": "Export Related Info", "sdo_yes": "Yes", "sdo_no": "No", "sdo_export_plaintxt": "Export Plaintext", "sdo_select_row_export": "Select Row Export", "sdo_selected_rows": "Selected Rows", "sdo_not_supported": "Not Supported Yet", "sdo_save_and_close": "Save and Close", "sdo_edit": "Edit", "sdo_edit_cells": "Edit Cells", "sdo_view_cells": "View Cells", "sdo_no_primary_key_or_not_supported_for_download": "No Primary Key or Field Is Not Supported for Download", "sdo_download": "Download", "sdo_empty": "Empty", "sdo_binary_download_no_permission": "No Result Set Binary Download Permission", "sdo_result_set_fields": "Result Set Fields", "sdo_select_col": "Select Columns", "sdo_select_fields": "Select Fields", "sdo_export_suc_view_txt": "The Export Application Was Submitted Successfully, and You Can Check the Progress of the Application in the Process", "sdo_export_application_submit_fail": "Export Application Failed to Submit", "sdo_export_application": "Export Application", "sdo_applicant": "Applicant", "sdo_fill_title": "<PERSON><PERSON> in the title", "sdo_database_ele": "Database Elements", "sdo_export_sql": "Export SQL", "sdo_export_rows": "Export Rows", "sdo_application_reason": "Application Reason", "sdo_fill_application_reason": "Fill in the reason for application", "sdo_export_domain_tips": "After the export application is approved, the export task will be automatically created, which you can download in the Task Center/Application details", "sdo_regular_export_tips": "When the amount of data is too large, it is recommended to use regular export to run in batches", "sdo_export_error_handle": "Export Error Handling", "sdo_select_export_error_handle": "Select Export Error Handling", "sdo_termination": "Termination", "sdo_ignore": "Ignore", "sdo_save_first": "Save Your Changes First!", "sdo_binary_disallowed_edit": "The Result Set Contains Binary, Disallowed Edits", "sdo_user_no_copy_permission": "The Current User Does Not Have Copy Permission", "sdo_result_empty_filtered_field": "The Result is Empty; There is a Filtered Field", "sdo_view_single_line": "View a Single Line", "sdo_copy_one_or_more_lines": "Copy One or More Lines", "sdo_loaded_documents_count": "Total Number of Loaded Documents", "sdo_confirm_delete_selected_row": "Are You Sure You Want to Delete the Selected Row?", "sdo_complete_current_edit": "Complete the Current Edit Operation", "sdo_save_current_page": "Save the Current Page Operation", "sdo_refresh": "Refresh", "sdo_add_2": "Add", "sdo_lock_in": "Lock In", "sdo_view_rows_or_cells": "View Rows/Cells", "sdo_view_plaintext": "View Plaintext", "sdo_sensitive_resources": "Sensitive Resources", "sdo_regular_export": "Regular Export", "sdo_select_export": "Select Export", "sdo_zoom_out": "Zoom Out", "sdo_zoom_in": "Zoom In", "sdo_column_filtering_fields": "Column Filtering Fields", "sdo_result_total_rows": "Total Result Rows", "sdo_query_result_total_rows": "Total Query Rows", "sdo_line_details": "Line Details", "sdo_current_row": "Current Row", "sdo_prev_row_detl": "Previous Detail", "sdo_next_row_detl": "Next Detail", "sdo_prev_page": "Previous Page", "sdo_next_page": "Next Page", "sdo_format_error_parse_fail": "The Format is Wrong and Cannot Be Parsed", "sdo_loaded": "Loaded", "sdo_docs": "Documents", "sdo_total": "Total", "sdo_time_consuming": "Duration", "sdo_command_review": "Command Review", "sdo_sms_review": "SMS Review", "sdo_request_data_correction": "Request Data Correction", "sdo_or_lower": "or", "sdo_permission_to": "Permission to", "sdo_access_permission": "You can access the", "sdo_deal_with_lower": "To handle", "sdo_contact_admin": "Contact the Administrator", "sdo_statement_implicit_commit": "Statement Causes an Implicit Transaction Commit", "sdo_timeout_rollback": "Timeout Rollback", "sdo_click_icon_navigate": "Click Icon to Navigate", "sdo_execution_time_sql": "Execution Time", "sdo_affected_rows": "Affected Rows", "sdo_exception_info": "Exception Information", "sdo_warn_info": "Warning Information", "sdo_is_empty": "Is Empty", "sdo_select_approver": "Select Approver", "sdo_verf_code_sent": "Verification Code Sent", "sdo_verf_code_fail": "Failed to Send Verification Code", "sdo_approver": "Approver", "sdo_input_len_6": "Input Length 6", "sdo_enter_otp_code": "Enter OTP Code", "sdo_enter_otp_code_6_digits": "Please enter OTP code (6 digits)", "sdo_verf_code": "Verification Code", "sdo_enter_verf_code": "Enter verification code", "sdo_enter_verf_code_6_digits": "Enter verification code (6 digits)", "sdo_otp_review_verf": "OTP Review", "sdo_sms_review_verf": "SMS Review", "sdo_join_application_suc": "Successfully Joined Application", "sdo_add_group": "Add Group", "sdo_pack_nodes": "Collapse Nodes", "sdo_homogeneous_replication": "Homogeneous Replication", "sdo_load_more": "Load More", "sdo_request_operation_permission": "Request Operation Permission", "sdo_request_connection_permission": "Request Connection Permission", "sdo_developer_mode": "Developer Mode", "sdo_allow_privileged_drag_drop": "Allow Privileged Drag & Drop", "sdo_ele_name": "Element name", "sdo_ele_name_data_dict": "Element name (data dictionary search)", "sdo_data_dictionary_search": "Data Dictionary Search", "sdo_front_end_search": "Front-End Search", "sdo_no_ele": "No Elements", "sdo_sure": "Sure", "sdo_closing_connection": "Closing the Connection", "sdo_connection_closed_suc": "Connection Closed Successfully", "sdo_move_out_of_group": "Move Out of the Group", "sdo_move_out_of_group_suc": "Removed Successfully", "sdo_move_to_group": "Move to a Group", "sdo_connection_management": "Connection Management", "sdo_copy_name": "Copy Name", "sdo_copy_suc": "<PERSON><PERSON> Successfully", "sdo_nonexistent_data_source_type": "Nonexistent Data Source Type", "sdo_import_dmp_file": "Import the DMP File", "sdo_import": "Import", "sdo_new_sync_task_suc": "The New Synchronization Task Succeeded", "sdo_data_quality_fail": "Arouse DQ Failure", "sdo_reclaim_suc": "Reclaim Successfully", "sdo_rest_suc": "Reset Successfully", "sdo_copy_fail": "<PERSON><PERSON> Failed", "sdo_mode_added_suc": "Mode Added Successfully", "sdo_add_mode": "Add Mode", "sdo_schema_owner": "<PERSON><PERSON>a Owner", "sdo_select_schema_owner": "Select the schema owner", "sdo_generate_sql": "Generating SQL", "sdo_group_added_suc": "Group Added Successfully", "sdo_group_name": "Group Name", "sdo_enter_group_name": "Enter a group name", "sdo_fill_export_lines": "Fill in the Number of Export Lines", "sdo_export_stored_procedure_file": "Export Stored Procedure File", "sdo_dump_sql_file": "Dump SQL File", "sdo_node_name": "Node Name", "sdo_dump_type": "Dump Type", "sdo_select_dump_type": "Select Dump Type", "sdo_add_subgroup": "Add Subgroup", "sdo_parent_group_name": "Parent Group Name", "sdo_upload": "Upload", "sdo_tmp_file_dl": "Template File Download", "sdo_dl_instructions": "Download the template file, and according to the excel format to fill in the need to generate connections.", "sdo_tmp_dl": "Template Download", "sdo_file_upload": "File Upload", "sdo_upload_instructions": "Please upload the completed file here and click upload to generate.", "sdo_bulk_upload": "Please upload the bulk generation connection file", "sdo_upload_fail": "File Upload Failed", "sdo_batch_creation": "Batch Creation In Progress", "sdo_creation_results": "Creation Results", "sdo_create_connection": "Create Connection", "sdo_creating_connection": "Creating Connection", "sdo_creation_end": "Creation Ended", "sdo_done": "Done", "sdo_bulk_add_connections": "Bulk Add Connections", "sdo_in_check": "In Check", "sdo_on_the_fly": "On the Fly", "sdo_success": "Success", "sdo_failure": "Failure", "sdo_complete_fields": "Complete Existing Fields First!", "sdo_select_delete": "Select Items to Delete First!", "sdo_specify_function_param": "Specify New Function Parameter", "sdo_param_cannot_null": "The parameter value cannot be null", "sdo_enter_value": "Enter", "sdo_pattern": "Pattern", "sdo_create_function": "Create Function", "sdo_function_name": "Function Name", "sdo_enter_func_name": "Enter the function name", "sdo_return_type": "Return Type", "sdo_select_return_type": "Select the return type", "sdo_enter_len": "Enter the length", "sdo_specify_stored_proc_param": "Specify the New Stored Procedure Parameter", "sdo_create_stored_proc": "Create New Stored Procedure", "sdo_stored_proc_name": "Stored Procedure Name", "sdo_enter_stor_proc_name": "Enter the stored procedure name", "sdo_select_stor_proc_name": "Select stored procedure name", "sdo_export_vw_results": "Start Exporting, Please Check the Result in the Task Center", "sdo_dmp_export": "DMP Export", "sdo_start_execution": "Start Execution", "sdo_file_uploading": "Uploading File", "sdo_click_upload": "Click Upload", "sdo_rot_group": "Root Group", "sdo_move_to_group_suc": "Move to Group Successful", "sdo_move_in_group": "Move to Group", "sdo_move_in_group_empty": "Group Cannot Be Empty", "sdo_rename_suc": "Rename Successful", "sdo_new_name": "New Name", "sdo_node_name_required": "The node name cannot be null", "sdo_enter_node_name": "Enter the node name", "sdo_upload_file": "Upload the file", "sdo_max_upload_one": "The maximum upload quantity is 1", "sdo_sql_import": "SQL Import", "sdo_change_alias": "<PERSON><PERSON><PERSON>", "sdo_alias_change_suc": "<PERSON><PERSON> Changed Successfully", "sdo_connection_alias": "Connection Alias", "sdo_enter_alias": "<PERSON>ter alias", "sdo_run_task": "Run Task", "sdo_task_running": "Running Task", "sdo_node": "Node", "sdo_task_type": "Task Type", "sdo_export": "Export", "sdo_start_time": "Start Time", "sdo_field_value": "Field Value", "sdo_field_type": "Field Type", "sdo_constraints_supper": "Constraints", "sdo_trigger_supper": "<PERSON><PERSON>", "sdo_partition": "Partition", "sdo_temporary_partition": "Temporary Partition", "sdo_table_compression": "Table Compression", "sdo_row_movement": "Row Movement", "sdo_parallel_with_degrees": "<PERSON><PERSON><PERSON> with Degrees", "sdo_owner": "Owner", "sdo_inherit_from": "Inherit From", "sdo_fill_factor": "Fill Factor", "sdo_whether_to_record": "Whether to Record", "sdo_dentify_fields": "Identify Fields", "sdo_dentify_seed": "Identity Seed", "sdo_dentify_increment": "Identity Increment", "sdo_current_value": "Current Value", "sdo_record_supper": "Record", "sdo_caching": "Caching", "sdo_engine": "Engine", "sdo_auto_increment_noun": "Automatic Increment", "sdo_character_set": "Character Set", "sdo_rank_rules": "Ranking Rules", "sdo_average_len_of_line": "Average Line Length", "sdo_row_min_num": "<PERSON>s", "sdo_row_max_num": "<PERSON>s", "sdo_key_block_size": "Key Block Size", "sdo_line_format": "Line Format", "sdo_auto_recalc": "Auto Recalculate Statistics", "sdo_agg_data_persist": "Aggregate Data Persistence", "sdo_stat_sample_page": "Statistical Sample Page", "sdo_info": "Information", "sdo_table_groups": "Table Groups", "sdo_view_groups": "View Groups", "sdo_func_groups": "Groups of Functions", "sdo_proc_group": "Stored Procedure Group", "sdo_materialize_view_group": "Materialized View Group", "sdo_materialized_views": "Materialized Views", "sdo_materialized_views_lower": "Materialized Views", "sdo_auto_inc_values": "Auto-Increment Values", "sdo_change_date": "Change Date", "sdo_data_len": "Data Length", "sdo_line_upper": "Line", "sdo_table_type": "Table Type", "sdo_status_lower": "Status", "sdo_table_locking": "Table Locking", "sdo_compression": "Compression", "sdo_can_update": "Can Update", "sdo_superview_name": "Superview Name", "sdo_lang": "Language", "sdo_aggregation": "Aggregation", "sdo_debug": "Debug", "sdo_pipe_connected": "The Pipe Is Connected", "sdo_parallelism": "Parallelism", "sdo_decisive": "Decisive", "sdo_rewrite": "Rewrite", "sdo_refresh_mode": "Refresh Mode", "sdo_last_refresh_date": "Last Refresh Date", "sdo_quick_refresh": "Quick Refresh", "sdo_out_of_date": "Out of Date", "sdo_edit_status": "Edit Status", "sdo_system_view": "System View", "sdo_encrypted": "Encrypted", "sdo_func_type": "Function Type", "sdo_numbers": "Numbers", "sdo_sys_object": "System Object", "sdo_run_by": "Run By", "sdo_results": "Results", "sdo_not_executed": "Not Executed", "sdo_not_executed_lower": "Not executed", "sdo_open_table": "Open Table", "sdo_view_table_struct": "View Table Structure", "sdo_open_view": "Open View", "sdo_view_view": "View View", "sdo_view_func": "View Function", "sdo_call_func": "Call Function", "sdo_view_procedure": "View Procedure", "sdo_call_procedure": "Call Procedure", "sdo_open_materialized_view": "Open Materialized View", "sdo_view_materialized_view": "View Materialized View", "sdo_file_type": "Choose File Type", "sdo_file_encoding": "Select File and Encoding", "sdo_separator_config": "Separator Config", "sdo_format_config": "Format Config", "sdo_select_target_table": "Select Target Table", "sdo_table_field_mapping": "Target Table and Field Mapping", "sdo_submission_success": "Submission Successful", "sdo_addition_success": "Addition Successful", "sdo_fields_not_empty": "Source and Target Fields Cannot Be Empty", "sdo_unique_target_fields": "Target Fields Cannot Be the Same", "sdo_type_field_not_empty": "Type Field Cannot Be Empty", "sdo_text_import_request": "Text Import Request", "sdo_prev_step": "Previous Step", "sdo_next_step": "Next Step", "sdo_submit_approval": "Submit for Approval", "sdo_start_import": "Start Import", "sdo_import_file_size_limit": "Import File Size Limit", "sdo_approval_required_lower": "Exceeding Limit Requires Approval", "sdo_text_import": "Text Import", "sdo_encoding_specify": "Encoding Format (TXT/CSV)", "sdo_line_separator": "Record Line Separator", "sdo_line_break_type": "Line Break. Type: ", "sdo_field_separator": "Field Separator", "sdo_field_delimiters": "Split Fields. Type: Comma (,), Semicolon (;), Tab", "sdo_field_identifier": "Field Identifier", "sdo_string_field_identifier": "String Field Identifier: Single or Double Quotes", "sdo_field_name_row": "Field Name Row", "sdo_field_name_option": "1 for first row as field name, 0 for no field name", "sdo_first_data_row": "First Data Row", "sdo_data_start_row": "2 means start from second row", "sdo_last_data_row": "Last Data Row", "sdo_data_end_row": "End row of import data", "sdo_date_sort": "Date sorting", "sdo_date_format": "Date format: YMD", "sdo_date_separator": "Date separator", "sdo_date_split": "Split date as: 2021-12-12, separator -", "sdo_time_separator": "Time separator", "sdo_time_split": "Split time as: 12:12:12, separator: ", "sdo_decimal_symbol": "Decimal symbol", "sdo_time_decimal_symbol": "Time decimal symbol, typically dot (.)", "sdo_datetime_sort": "Date and time sorting", "sdo_specify_datetime_order": "Specify date and time order", "sdo_binary_encoding": "Binary data encoding", "sdo_binary_encoding_format": "Binary data encoding format", "sdo_ctrl_mouse_select": "Ctrl + Mouse Select", "sdo_structure_only": "Structure only", "sdo_structure_data": "Structure + Data", "sdo_query_window_limit_reached": "Query Window Limit Reached", "sdo_confirm_del_selected_row_data": "Confirm Delete Selected Row Data?", "sdo_otp_code": "OTP Code", "sdo_dq_protocol_not_registered": "DQ Protocol Not Registered, Please Download from DQ Official Website", "sdo_row_limit_10000000": "Row Limit is 10,000,000", "sdo_file_extension_sql": "File Extension is sql/SQL", "sdo_compile_func": "Compile Function", "sdo_compile_stored_proc": "Compile Stored Procedure", "sdo_compile_invalid_ob": "Compile Invalid Objects", "sdo_views": "views", "sdo_operation_permission": "Operation Permission", "sdo_time_permission": "Time Permission", "sdo_restricted_permission": "Restricted Permission", "sdo_privilege_escalation": "Privilege Escalation", "sdo_export_permission": "Export Permission", "sdo_execution_time_permission": "Execution Time Permission", "sdo_high_risk_resource": "High-Risk Resource", "sdo_sensitive_resource": "Sensitive Resource", "sdo_menu_permission": "Menu Permission", "sdo_system_settings": "System Settings", "sdo_connection_settings": "Connection Settings", "sdo_audit_analysis": "Audit Analysis", "sdo_process": "Process", "sdo_hide_sdt_tree": "Hide Data Source Tree", "sdo_show_sdt_tree": "Show Data Source Tree", "sdo_tip_sdt_display_type": "Display Type", "sdo_menu_display_type_normal": "Normal", "sdo_menu_display_type_group_by_type": "Group by Database Type", "sdo_large_data_export": "Data export has been split into multiple files.Excel cells have a 32767 character limit, excess characters will be truncated.", "sdo_checkout_filename_tip": "File names cannot contain special characters", "sdo_result_export_explain_noPerm": "The execution plan does not support this type of export", "sdo_select_resource_first": "Please select the resources", "sdo_search_current_page": "Search in Current Page", "sdo_case_sensitive": "Case Sensitive", "sdo_regex": "Regular Expression", "sdo_search_previous": "Previous", "sdo_search_next": "Next", "sdo_search_placeholder": "Enter search content...", "sdo_no_matches": "No matches", "sdo_search_clear": "Clear", "sdo_filter_rows": "Filter Rows", "sdo_close_search": "Close Search", "sdo_fav_isShare_label": "Share Status", "sdo_fav_isShare_plac": "Whether to Share", "sdo_fav_isShare_tip1_pre": "When shared, this saved query will be visible and usable by", "sdo_fav_isShare_tip1_postfix": "all users in the system", "sdo_fav_isShare_tip2_pre": "When not shared, this saved query is only accessible to", "sdo_fav_isShare_tip2_postfix": "yourself", "sdo_fav_search_type_label": "Query Type:", "sdo_fav_search_type_all": "All SQL", "sdo_fav_search_type_share": "Shared", "sdo_fav_search_type_noShare": "Private", "sdo_creator": "Creator", "sdo_sql_favorite_list_shotcut": "Favorites List: {{val}}", "sdo_sql_share_icon": "Share SQL", "sdo_save_document_to_local": "Local", "sdo_save_document_error_tip": "Please select a save location", "sdo_save_document_path": "Save Path:", "sdo_open_document_to_local": "Local Document", "sdo_save_document_search_plac": "Please enter the folder name to search", "sdo_open_document_search_plac": "Please enter the file name to search", "sdo_alias_hint": "Aliases only support letters, Chinese characters, numbers, and '_', with a length between 1 and 64 (pure numbers are not supported).", "sdo_sql_execute_audit_title": "Execution Content", "sdo_sql_execute_audit_copy_statement": "Copy Current Statement"}