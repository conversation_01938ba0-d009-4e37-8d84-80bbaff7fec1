{"noOperationPermission": "No operation permission for [{{title}}]", "copyrightNotice": "CloudQuery Shield Inquiry © 2024 Hangzhou Turz Information Technology Co., Ltd. All rights reserved", "accountPasswordError": "Account or password error", "confirm": "Confirm", "cancel": "Cancel", "thirdPartyLoginInProgress": "Three party automatic login in progress...", "verificationCode": "Verification code", "verificationCodeInput": "Input verification code", "loginPurpose": "Please enter the login purpose", "loginPurposeDescription": "login purpose", "passwordRestriction": "Password does not allow Chinese characters", "maxLength": "Length limit 10 characters", "parameterNameRule": "Parameter names can only be in Chinese, letters, numbers, '_', '#', '-'", "parameterLabelRule": "Parameter label words can only be letters", "emailValidation": "Please enter a valid email address", "enterPhoneNumber": "Please enter your phone number", "validPhoneNumber": "Please enter a valid mobile phone number", "enterLoginAccount": "Please enter your login account", "loginAccountLength": "The length of the login account is between 2-20", "loginAccountRule": "Login account can only be in Chinese, letters, numbers, '_', '#', '-', '.'", "passwordStrength": "Not in compliance with built-in rules: Strong, password length 9-16 characters, including numbers, uppercase letters, lowercase letters, special characters (! @ # $% ^&*?)", "passwordStrengthWeak": "Password Strength: Weak, password length 9-16 characters, must include numbers, uppercase letters, lowercase letters, and special characters (! @ # $% ^&*?)", "passwordStrengthMedium": "Password strength: Medium, password length 9-16 characters, must include numbers, uppercase letters, lowercase letters, and special characters (! @ # $% ^&*?)", "invalidSpecialCharacters": "The characters you entered contain illegal special characters", "passwordLength ": "Password Length ", "passwordRequirements": " Characters, at least including", "specialCharacters": "Special characters、", "uppercaseLetters": "Capital letters、", "lowercaseLetters": "Lowercase letters、", "numbers": "Numbers、", "customRule": "Please improve the custom rules", "passwordPolicy": "Password length is 8-16 characters, containing at least numbers and uppercase and lowercase letters", "usernameFormat": "Combination of letters, numbers, and underscores starting with a letter", "maxLength36": "Maximum length of 36 characters", "allowedCharacters": "Can only contain Chinese, English, and numbers", "allowedCharactersWithDot": "Can only contain Chinese and English, numbers, and .", "allowedCharactersWithSymbols": "Can only contain Chinese and English, numbers, and specific symbols (.: _)", "maxLength50": "Maximum length of 50 characters", "allowedCharactersWithUnderscore": "Can only contain Chinese and English, numbers, and underscores", "urlValidation": "Please enter URL", "urlProtocol": "Must start with http://or https://", "documentExecCommandExecutionFailed": "Document. execCommand execution failed", "executionSuccessful": "Success", "parsingFailed": "Parsing failed", "authenticationFailed": "<PERSON><PERSON> failed", "executionFailed": "Failed", "auditFailed": "Audit failed", "failed": "Failed", "cloudQueryLoading": "CloudQuery loading", "sqlExecutionCount": "SQL execution times (operation statement type)", "sqlExecutionTime": "SQL execution average time (operation statement type)", "closeHint": "Close this prompt", "passwordLeakWarning": "If it is not your operation, the password may have been leaked, we recommend modifying the password to ensure account security", "noPermission": "You do not have permission to access this system, please contact the administrator", "serviceNotStarted": "Service not started or network exception", "methodNotAllowed": "Method not allowed", "requestTimeout": "Request timeout", "gatewayError": "Gateway error", "serviceUnavailable": "Service unavailable", "gatewayTimeout": "Gateway timeout", "serviceException": "Service exception", "licenseNotInstalled": "License service not installed", "uploadFailed": "Upload failed", "fileSavedSuccessfully": "File saved successfully", "copyTitle": "Copy Title", "copyCellTitle": "Copy Cell Title", "copyAllTitles": "Copy All Titles", "unsupportedFieldType": "Unsupported field type exists", "fieldTooLarge": "Large field exists, unable to edit", "viewCell": "View cell", "viewRow": "View row", "copyCell": "Copy cell", "copyRow": "Copy row", "pasteRow": "Paste row", "copyAll": "Copy all", "copyCurrentPageData": "Copy all data on the current visible page", "copyWithTitle": "<PERSON><PERSON> with title", "cloneRow": "Clone row", "resultSetGenerated": "Result set generated", "ok": "Ok", "caseConversion": "Case conversion", "format": "Format", "copy": "Copy", "copySuccess": "<PERSON><PERSON> succeeded", "executeSelectedStatement": "Execute (selected statements)", "executePlan": "Execution plan (selected statements)", "splitStatement": "Sentences (selected statements)", "viewDefinition": "Go to definition", "task": "Task", "dataSource": "Data source", "pleaseSelectFile": "Please select a file first", "fileSplitting": "File splitting in progress, please wait...", "fileAlreadyExists": "File already exists, no need to upload again", "uploadedFileBlock": "Uploaded file block", "fileUploadMergeCompleted": "File upload merge completed!", "calculatingRemainingTime": "Calculating remaining upload time", "estimatedRemainingTime": "Estimated remaining time", "seconds": "Seconds", "bindingSuccess": "Binding succeeded", "otpBinding": "OTP binding", "exit": "Exit", "enterOtpVerificationCode": "Please enter OTP verification", "oneTimePasswordOtp": "OTP verification code", "useAuthenticator": "Use Authy/GoogleAuthenticator (identity verification) to scan the following QR code to get a 6-digit verification code", "iphoneAuthenticator": "iphone: Search Authy/Google Authenticator (identity verification) in AppStore", "androidAuthenticator": "Android: Search Authy/Google Authenticator (identity verification) in the application market", "maxTwoVersions": "Maximum two versions can be selected", "historyVersionList": "History Version List", "versionComparison": "Version comparison", "historyVersionName": "History version name", "modifier": "Modifier", "creationTime": "Creation time", "setConnectionAdmin": "This user will receive all operations on this connection, are you sure you want to set this user as a connection administrator?", "adminUpdateSuccess": "Administrator update succeeded", "adminUpdateFailed": "Administrator update failed", "groupAdmin": "Group Administrator", "connectionAdmin": "Connection Administrator", "change": "Change", "currentRole": "Your current role is", "noConnectionManagementPermission": "You do not have permission to operate [connection management]", "enterConnectionAdmin": "Please select the connection administrator", "pleaseSelect": "Please select", "organizationStructure": "Organization structure", "selectedUser": "Selected user", "customListItems": "Custom list items", "offlineNotification": "Offline notification", "accountLoggedInElsewhere": "Your account at", "loggedInElsewhere": "Logged in elsewhere", "potentialPasswordLeak": "If not done by yourself, the password may have been exposed, please modify the password, ensure the account security", "connectionTemporarilyUnavailable": "Connection temporarily unavailable", "connectionFailureWarning": "Connection failure warning", "generate": "Generate", "prohibitCompressedFiles": "Prohibit uploading compressed package files!", "fileSizeLimit": "File size cannot exceed", "selectFile": "Select file", "completeExistingTimeRange": "Please complete the existing time range first", "maxAdditions": "Up to 7 can be added", "add": "Add", "everyDay": "Every day", "monday": "Monday", "tuesday": "Tuesday", "wednesday": "Wednesday", "thursday": "Thursday", "friday": "Friday", "saturday": "Saturday", "sunday": "Sunday", "clear": "Clear", "item": "<PERSON><PERSON>", "confirmDeletion": "Confirm deletion", "selectedItems": "numbers Selected", "batchDeletion": "Batch deletion", "details": "Details", "pageRenderingError": "Page rendering error, please refresh or clear cache data", "refresh": "Refresh", "confirmReset": "Are you sure to reset? Reset will clear all current user data", "clearCacheData": "Clear cache data", "download": "Download", "pleaseEnter": "Please enter", "return": "Return", "close": "Close", "completed": "Completed", "nextStep": "Next Step", "skip": "<PERSON><PERSON>", "sourceDatabase": "Source Database", "pleaseSelectDatabaseValue": "Please select the value of database", "pleaseSelectTargetDatabase": "Please select the target database", "pleaseSelectSourceDatabase": "Please select the source database", "sourceSchema": "Source Schema", "pleaseSelectSchemaValue": "Please select the value of schema", "pleaseSelectTargetSchema": "Please select the target schema", "pleaseSelectSourceSchema": "Please select the source schema", "sourceObject": "Source Object", "pleaseSelectTableValue": "Please select the value of table", "pleaseSelectTargetObject": "Please select the target object", "pleaseSelectSourceObject": "Please select the source object", "operation": "Operation", "remove": "Remove", "testSuccess": "Test Succeeded", "selectSourceAndTarget": "Select Source and Target", "dataSourceType": "Data Source Type", "pleaseSelectDataSourceType": "Please select data source type", "databaseConnection": "Database Connection", "pleaseSelectDatabaseConnection": "Please select database connection", "testConnection": "Test Connection", "sourceField": "Source Field", "targetField": "Target Field", "whetherToMap": "Whether to Map", "source": "Source", "field": "Field", "target": "Target", "confirmFieldMapping": "Confirm Field Mapping", "confirmCopyInformation": "Confirm Copy Information", "newTableName": "New Table Name", "pleaseEnterNewTableName": "Please enter a new table name", "containsData": "Contains data", "cannotHaveDuplicateFields": "Cannot have duplicate fields", "pleaseSelectTargetInformation": "Please select target information", "pleaseSelectSourceInformation": "Please select source information", "pleaseTestSourceConnection": "Test Source Connection", "pleaseTestTargetConnection": "Test Target Connection", "pleaseSelectFieldsToMap": "Select Fields to Map", "newDataCopy": "New Data Copy", "shrink": "Shrink", "quickSelection": "Quick Selection", "previousStep": "Previous Step", "executionMethod": "Execution Method", "executeImmediately": "Execute Immediately", "taskName": "Task Name", "pleaseEnterTaskName": "Enter Task Name", "yes": "Yes", "no": "No", "taskExecution": "Task Execution", "createDataCopyTaskSuccess": "Data Copy Task Created", "currentlyOnlySupportMySQLPostgreEtcCopy": "Supports MySQL, PostgreSQL, Oracle, SQLServer, OracleCDB, OceanBaseMySQL", "pleaseSelectSameConnectionSource": "Select Source in Same Connection", "pleaseKeepSourceAndTargetSameDataSourceType": "Maintain Same Data Source Type", "multipleTablesSourceTargetRestriction": "Source is Multi-Table, Target Must Be Single Table or Schema", "singleTableSourceTargetRestriction": "Source is Single Table, Target Must Be Single Schema or Table", "pleaseSelectTarget": "Select Target", "copyContainsData": "Copy Contains Data", "clickExpandToCustomizeNewTableName": "Click [Expand] to Customize New Table Name", "defaultMapAllFields": "All Fields Mapped by <PERSON><PERSON><PERSON>, <PERSON><PERSON> [Expand] to Adjust", "allowDataCoverage": "Allow Data Overwrite", "manualSelection": "Manual Selection", "dataCopy": "Data Copy", "expand": "Expand", "executeSQL": "Execute SQL", "prompt": "Prompt", "operationConfirmation": "Operation Confirmation", "execute": "Execute", "pieceOfData": "piece of data", "totalCount": "Total", "turnOnOnlyDisplayDatabaseObjectsWithPermission": "Only Display Objects with Permission", "displayAllDatabaseObjects": "Display All Objects", "test": "Test", "production": "Production", "pleaseSelectDatabaseElement": "Please select database element", "pleaseEnterSystemLibrary": "Please enter system library", "create": "Add", "selectAll": "Select All", "secondsLaterToResend": "seconds later to resend", "sendVerificationCode": "Send Verification Code", "total": "Total", "piece": "piece", "page": "page", "versionInformation": "Version Information", "versionNumber": "Version Number", "coreServiceVersion": "Core Service Version", "releaseTime": "Release Time", "copyrightStatement": "Copyright Statement", "versionUpdateInformation": "Version Update Information", "vscodeDebuggerDownload": "VSCode Debugger Download", "sqlMode": "SQL Mode", "recentExecution": "Recent Execution", "executionHost": "Execution Host", "averageTime": "Avg Time (μs)", "totalTime": "Total Time (ms)", "executionTimes": "Execution Times", "password": "Password", "pleaseEnterPassword": "Please enter password", "pleaseEnterCorrectJsonFormat": "Please enter the correct JSON format", "performDualFactorAuthentication": "Please perform two-factor authentication", "parseFailed": "Parse failed", "delete": "Delete", "table": "Table", "singleToMultiTable": "Single Table to Multi Table", "multiToSingleTable": "Multi Table to Single Table", "tableSchema": "Table Schema", "selectSource": "Select Source", "selectTarget": "Select Target", "confirmCopyInfo": "Confirm Copy Information", "male": "Male", "female": "Female", "success": "Success", "failure": "Failure", "normal": "Normal", "locked": "Locked", "commonUser": "Common User", "authorizationAdmin": "Authorization Administrator", "cronExpression": "Cron Expression: [Second][Minute][Hour][Day][Month][Week][Year]", "cronExample1": "0 0/2 * * * ?    Trigger every 2 minutes", "cronExample2": "0 0 10,14,16 * * ?   Trigger every day at 10:00, 14:00, and 16:00", "cronExample3": "0 15 10 ? * *    <PERSON>gger every day at 10:15", "cronExample4": "0 15 10 ? * MON-<PERSON><PERSON>    Trigger every Monday to Friday at 10:15", "dataSourcePermission": "Data Source Operation Permission", "permissionSet": "Permission Set", "systemPermission": "System Permission", "businessAlarmType": "Business Alarm Type", "alarmCondition": "Alarm Condition", "alarmMethod": "Alarm Method", "messageReceiverType": "Message Receiver Type", "systemAlarm": "System Alarm", "businessAlarm": "Business Alarm", "highRiskOperation": "High-Risk Operation", "unauthorizedOperation": "Unauthorized Operation", "batchExecution": "Batch Execution", "sqlAudit": "SQL Audit", "slowSQL": "Slow SQL", "high": "High", "medium": "Medium", "low": "Low", "aliCloud": "<PERSON>", "tencentCloud": "Tencent Cloud", "huaweiCloud": "Huawei Cloud", "dreamNetCloud": "Dream Net Cloud", "custom": "Custom", "ascending": "Ascending", "descending": "Descending", "totalExportedRows": "Total Exported Rows", "dept": "Department", "email": "Email", "userAvatar": "User Avatar", "jobNumber": "Job Number", "userPassword": "User Password", "postName": "Post Name", "sessionId": "Session ID", "telephone": "Telephone", "userCategory": "User Category", "userGender": "User Gender", "userName": "User Name", "userStatus": "User Status", "isPrincipal": "Department Principal", "daily": "Daily", "account": "Account", "roleName": "Role Name", "description": "Description", "permissions": "Permissions", "validityPeriod": "Validity Period", "permanent": "Permanent", "currentStatus": "Current Status", "valid": "<PERSON><PERSON>", "invalid": "Invalid", "connectionName": "Connection Name", "creator": "Creator", "deleted": "Deleted", "archived": "Archived", "executionConnection": "Execution Connection", "executionDatabase": "Execution Database", "sqlStatement": "SQL Statement", "executionResult": "Execution Result", "executionTime": "Execution Time", "timeConsumed": "Time Consumed (ms)", "permissionName": "Permission Name", "permissionType": "Permission Type", "permissionDescription": "Permission Description", "operationPermissions": "Operation Permissions", "allMessage": "All Messages", "notReadMessage": "Unread Messages", "readMessage": "Read Messages", "deleteSuccess": "Delete Success", "noPermissionAccess": "Sorry, you do not have permission to access this resource", "resend": "Resend", "returnLogin": "Return to Login", "pleaseEnterAccount": "Please enter your account", "clickRefresh": "Click to refresh", "changeSuccess": "Change successful, please login", "changePassword": "Change Password", "currentPasswordChangeLinkInvalid": "The current password change link is invalid or has expired", "reApply": "Reapply", "newPassword": "New Password", "confirmNewPassword": "Confirm New Password", "pleaseConfirmNewPassword": "Please confirm your new password", "passwordsDoNotMatch": "The two passwords do not match", "forgotPassword": "Forgot Password", "confirmAccount": "Confirm Account", "codeExpiredOrNotMatch": "Verification code has expired or does not match", "confirmEmail": "Confirm Email", "indexPage": "Index Page", "closeAllTabs": "Close All Tabs", "sureCloseAllTabs": "Are you sure you want to close all tabs", "closeOtherTabs": "Close Other Tabs", "reload": "Reload", "invalidConnectionId": "Invalid connection ID", "copyColumnTitles": "Copy Titles", "copyColumnTitle": "Copy Column Title", "copyColumnData": "Copy", "clone": "<PERSON><PERSON>", "firstPage": "First Page", "previousPage": "Previous Page", "modifyPageSize": "Modify <PERSON>", "queryTotalCount": "Query Total Count", "refreshTotalCount": "Refresh Total Count", "nextPage": "Next Page", "lastPage": "Last Page", "querying": "Querying"}