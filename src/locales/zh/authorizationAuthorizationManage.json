{"clickCreatePermissionSet": "点击创建权限集,进入权限集的创建", "enterCreateEditPolicy": "编辑策略", "clickEditPolicy": "点击编辑策略,可以设置权限集生效用户", "modifySuccess": "修改成功", "enterPermissionSetName": "请输入权限集名称", "permissionSetName": "权限集名称", "modifyPermissionSetName": "修改权限集名称", "complete": "完成", "nextStep": "下一步", "allPermissionSets": "全部权限集", "databaseManagement": "数据库管理", "autoAuthorization": "自动授权", "permissionSetList": "权限集列表", "currentRole": "您当前的角色是", "noPermissionForAutoAuthorization": "对[自动授权]没有操作权限", "createPermissionSetAgain": "创建权限集", "enterPermissionSetNameAgain": "请输入权限集名", "permissionSetDetails": "权限集详情", "enterEffectConditions": "在此处填写权限集生效条件", "testSuccess": "测试成功", "setSuccess": "设置成功", "confirmDelete": "确认要删除吗？", "statusModifySuccess": "状态修改成功", "modify": "修改", "delete": "删除", "creator": "创建者", "creationTime": "创建时间", "status": "状态", "enable": "启用", "disable": "禁用", "wantToKnowMore": "想要了解更多，点击", "downloadHere": "此处下载", "exampleTemplate": "示例模板", "condition": "条件", "enterCondition": "请输入条件", "save": "保存", "cancel": "取消", "testCurrentDateUsers": "测试当前日期系统中哪些用户满足策略条件", "effectiveUsers": "生效用户", "noUsersMeetConditions": "暂无用户满足条件", "test": "测试", "policy": "策略", "savePolicyFailed": "保存策略失败: ", "edit": "编辑", "operation": "操作", "queryFailed": "查询失败", "modifyToolPermissionFailed": "修改工具权限失败: ", "operationSuccess": "操作成功", "operationFailed": "操作失败:", "operationPermission": "操作权限", "submit": "提交", "permissionName": "权限名", "remarks": "备注", "dataSourceType": "数据源类型", "selectAll": "全选", "operatorShouldUse": "运算符应使用", "shouldEnterNumber": "应填数字", "shouldEnterNumberThursday": "应填数字，周四使用", "shouldEnterNumberAfternoon": "应填数字，下午两点半使用", "shouldEnterNumber27th": "应填数字，二十七日使用", "shouldEnterNumberDateTime": "应填数字，二〇二三年七月二十七日下午两点半使用", "example": "示例", "male": "男", "confirm": "确定", "deleteSuccess": "删除成功", "deleteFailed": "删除失败", "username": "用户名", "associatedResources": "关联资源", "associatedUsers": "关联用户", "close": "关闭", "securitySettingsPermissionQueryFailed": "安全设置权限查询失败: ", "pleaseCompleteQueryTime": "请先完善查询时间", "pleaseCompleteModificationTime": "请先完善修改时间", "pleaseCompleteAllowedUsageTime": "请先完善允许使用时间", "timeSettingSuccess": "时间设置成功", "timeSettingError": "时间设置出错", "saveFailed": "保存失败", "function": "函数", "allowedUsageTime": "允许使用时间", "default": "默认", "queryDenied": "拒绝查询", "customTimePeriod": "自定义时段", "custom": "自定义", "allowedQueryTime": "允许查询时间", "allowedModificationTime": "允许修改时间", "securitySettings": "安全设置", "modifyPermissionLevelFailed": "修改权限等级失败： ", "confirmRemoveUser": "确认移除用户吗?", "removeSuccess": "移除成功", "queryToolPermissionFailed": "查询工具权限失败： ", "connectRole": "连接角色", "permissionLevel": "权限等级", "toolPermission": "工具权限", "remove": "移除", "confirmReset": "确认重置?", "thisOperationWillSyncAllResourcesAtCurrentLevel": "此操作将同步当前层级下所有资源的", "allPermissions": "全部权限", "keepConsistentWithCurrentLevelPleaseOperateWithCaution": "与当前层级保持一致,请谨慎操作", "reset": "重置", "controlByOtherMeans": "其他方式控制", "clearSelectedItems": "清空选中项", "thisUserHasOtherPermissionsAtThisLevel": "此用户在此层级下存在其他权限设置", "addPermission": "新增权限", "addQueryFailed": "添加查询失败", "batchEditPolicy": "批量编辑策略", "addPolicy": "添加策略", "selectUser": "请选择用户", "table": "表", "tableName": "表名", "pleaseEnterPositiveInteger": "请输入正整数", "search": "搜索", "currentlyOnPage": "当前处于第", "page": "页", "previousPage": "上一页", "nextPage": "下一页", "jumpTo": "跳转到", "confirmClear": "确认清空?", "thisOperationWillClearAllResources": "此操作将清空权限集所有资源，请谨慎操作", "clear": "清空", "resourceList": "资源列表", "selectResourceLevelThenSelectPermissions": "选择某资源层级后，再选择需要勾选的权限", "databaseObject": "数据库对象", "selectRequiredPermissionsHere": "在此处勾选需要的权限", "permissionSetResource": "权限集资源", "displaySelectedResourcesAndSaveAllPermissions": "展示勾选过的资源，点击完成后统一保存所有已设置的权限内容", "resetSuccess": "重置成功", "layeredQueryTreeStructureError": "逐层查询树结构报错：", "permissionSetSaveSuccess": "权限集保存成功", "permissionSetSaveFailure": "权限集保存失败", "permissionSetResetFailure": "权限集重置失败", "editPermissionSet": "编辑权限集", "createPermissionSet": "创建权限集", "pleaseEnterResourceName": "请输入资源名", "policyFileName": "/policy.txt"}