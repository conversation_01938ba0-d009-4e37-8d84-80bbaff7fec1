{"dataProtection.desens.editScanResult": "编辑扫描结果", "dataProtection.desens.batchEdit.desensField": "批量编辑脱敏字段", "dataProtection.desens.editDesensField": "编辑脱敏字段", "dataProtection.desens.matchRule": "匹配规则", "dataProtection.desens.desensSource": "脱敏字段来源", "dataProtection.desens.desensAlgorithm": "脱敏算法", "dataProtection.desens.desensAlgorithm.tip": "请选择脱敏规则", "dataProtection.desens.desensAlgorithm.title": "脱敏算法设置", "dataProtection.desens.desensPreview": "脱敏效果浏览", "dataProtection.desens.notNull": "不能为空", "dataProtection.desens.startPosition": "开始字符位置", "dataProtection.desens.replaceCharacters": "替换字符数量", "dataProtection.noPerm": "您当前的角色是[{{roleNameList}}]，对[数据保护]没有操作权限", "dataProtection.desens.replaceWith": "替换为", "dataProtection.desens.number": "数字", "dataProtection.desens.dataType": "数据类型", "dataProtection.desens.range": "范围", "dataProtection.desens.date": "日期", "dataProtection.desens.min": "不能小于{{minVal}}%", "dataProtection.desens.to": "至", "dataProtection.desens.retain": "保留", "dataProtection.desens.front": "前", "dataProtection.desens.point": "位", "dataProtection.desens.encryptAlgorithm": "加密算法", "dataProtection.desens.dataSimulationType": "数据仿真类型", "dataProtection.desens.predesens": "脱敏前", "dataProtection.desens.afterDesens": "脱敏后", "dataProtection.desens.userName": "用户名", "dataProtection.desens.deptName": "所属部门", "dataProtection.desens.expr": "生效时间", "dataProtection.desens.delete.tip": "删除后将收回此用户对此数据的脱敏权限，确认删除？", "dataProtection.desens.applyUserList": "申请脱敏用户列表", "dataProtection.desens.connectionName": "连接名", "dataProtection.desens.object": "对象名称", "dataProtection.desens.columnName": "字段名", "dataProtection.desens.ruleName": "脱敏规则", "dataProtection.desens.ruleParam": "算法", "dataProtection.desens.source": "来源", "dataProtection.desens.applyUser": "申请脱敏用户", "dataProtection.desens.editPolicy": "编辑策略", "dataProtection.desens.editPolicy.tip": "符合策略的用户，对字段不脱敏", "dataProtection.desens.epolicy": "策略", "dataProtection.desens.status": "状态", "dataProtection.desens.editPolicy.success": "编辑策略成功", "dataProtection.desens.action.tip": "确定{{action}}{{count}}个配置？", "dataProtection.desens.action.content": "删除操作将同时删除所有脱敏权限", "dataProtection.desens.status2": "状态：", "dataProtection.desens.search": "请输入字段名/脱敏规则进行检索", "dataProtection.desens.densesStatus": {"ALL": "全部", "TRUE": "启用", "FALSE": "关闭"}, "dataProtection.desens.comments": "注释", "dataProtection.desens.fieldType": "字段类型", "dataProtection.desens.example": "数据样例", "dataProtection.desens.editRule": "编辑规则", "dataProtection.desens.setRule": "设置规则", "dataProtection.desens.ruleManage": "脱敏规则管理", "dataProtection.desens.guide.content": "在这里给敏感字段设置相应的脱敏规则", "dataProtection.desens.searchField": "搜索字段名", "dataProtection.desens.desensFieldDetail": "脱敏字段详情", "dataProtection.desens.setAlgorithm": "设置脱敏算法", "dataProtection.desens.preview": "脱敏效果预览", "dataProtection.dataInfo": "脱敏数据", "dataProtection.dataInfo.resource": "匹配规则", "dataProtection.dataInfo.description": "描述", "dataProtection.dataInfo.checkMethod": "匹配方式", "dataProtection.dataInfo.ruleName": "规则名称", "dataProtection.dataInfo.checkMethod.reg": "正则表达式", "dataProtection.dataInfo.example": "脱敏结果", "dataProtection.dataInfo.deketeRule.tip": "确定删除该规则？", "dataProtection.dataInfo.guide.step2.title": "脱敏规则列表", "dataProtection.dataInfo.guide.step1.detail": "在这里选择需要进行脱敏数据设置的数据库对象层级", "dataProtection.dataInfo.guide.step2.detail": "在这里选择需要启用的脱敏规则，启用后与规则匹配的数据将被脱敏", "dataProtection.dataInfo.alert": "具体设置以实际层级为准", "dataProtection.dataInfo.sampledCount": "采样行数：", "dataProtection.dataInfo.sampleRate": "命中率：", "dataProtection.dataInfo.search": "请输入规则名称", "dataProtection.dataInfo.addRule": "添加规则", "dataProtection.dataInfo.shortRuleName": "规则名", "dataProtection.dataInfo.shortRuleName.plac": "请输入规则名", "dataProtection.dataInfo.desc": "规则描述", "dataProtection.dataInfo.resource.hint": "请输入匹配规则", "dataProtection.dataInfo.resource.plac": "请输入正则表达式", "dataProtection.dataInfo.parameter": "设置内置脱敏计算参数", "dataProtection.dataInfo.parameter.hitRate": "命中比率", "dataProtection.filter.dataSourceType": "数据源", "dataProtection.filter.objectPath": "对象路径", "dataProtection.filter.objectName": "对象名称", "dataProtection.filter.content": "过滤规则", "dataProtection.filter.example": "样例数据", "dataProtection.filter.comment": "注释：", "dataProtection.filter.rule.tip": "请输入行过滤规则（条件表达式）：[变量名][><=!like][值]变量名为字段名，值为用户参数，用户参数使用$引用示例：ID = '${USER.UserId}'", "dataProtection.filter.rule.addTip": "测试成功后允许添加", "dataProtection.filter.validateSql": "校验SQL:", "dataProtection.filter.filterRule": "行过滤规则", "dataProtection.scan.guide.add": "添加扫描任务", "dataProtection.scan.guide.content": "在选中层级添加扫描任务", "dataProtection.scan.jobName": "任务名称", "dataProtection.scan.path": "任务路径", "dataProtection.scan.desensRule": "扫描规则", "dataProtection.scan.cronExpression": "任务周期", "dataProtection.scan.jobRunTime": "耗时", "dataProtection.scan.jobStatus": "任务状态", "dataProtection.scan.lastExecuteTime": "上次执行时间", "dataProtection.scan.sampledRows": "采样行数", "dataProtection.scan.sampledRate": "命中率", "dataProtection.scan.scanTaskManage": "脱敏扫描任务管理", "dataProtection.scan.search": "搜索扫描任务", "dataProtection.scan.connectionName": "字段路径", "dataProtection.scan.scanResult": "扫描结果", "dataProtection.scan.search.tableName": "按表名搜索", "dataProtection.security.guide.step2.title": "基础设置", "dataProtection.security.guide.step2.content": "在这里设置对应资源的允许查询时间、允许修改时间、导入导出设置", "dataProtection.security.guide.step1.content": "在这里选择需要进行安全设置的数据库对象层级", "dataProtection.security.guide.step3.title": "高危操作设置", "dataProtection.security.guide.step3.content": "点击编辑，将对应操作设置为高危操作，并设置触发高危后如何响应", "dataProtection.desensField": "脱敏字段", "dataProtection.tab.scan": "脱敏扫描", "dataProtection.tab.filter": "行过滤设置", "dataProtection.tab.security": "安全设置", "dataProtection.tab.security.submitSuccess": "提交成功", "dataProtection.tab.security.settingType": "导出设置", "dataProtection.tab.security.settingType.plac": "选择导出设置", "dataProtection.tab.security.count": "行数限制", "dataProtection.tab.security.importSetting": "文本导入设置", "dataProtection.tab.security.importSetting.plac": "选择文本导入设置", "dataProtection.tab.security.size": "文件大小限制(KB)", "dataProtection.tab.security.childType": "导入方式", "dataProtection.tab.security.childType.plac": "选择导入方式", "dataProtection.tab.security.useTime.all": "默认", "dataProtection.tab.security.useTime.none": "拒绝修改", "dataProtection.tab.security.useTime.period": "自定义时段", "dataProtection.tab.security.formType": "函数", "dataProtection.tab.security.useTime": "允许使用时间", "dataProtection.tab.security.searchTime": "允许查询时间", "dataProtection.tab.security.modifyTime": "允许修改时间", "dataProtection.tab.security.searchTime.none": "拒绝查询", "dataProtection.tab.security.operationType": "操作类型", "dataProtection.tab.security.operationType.tip": "{{val}}上存在自定义设置", "dataProtection.tab.security.objectTypeName": "对象类型", "dataProtection.tab.security.value": "行数限制", "dataProtection.tab.security.value.tip": "行数限制仅对UID(update、insert、delete)生效", "dataProtection.tab.security.level": "高危提示", "dataProtection.tab.security.level.tip": "请选择高危提示", "dataProtection.tab.security.highRiskLevel": "高危等级", "dataProtection.tab.security.highRiskLevel.tip": "请选择高危等级", "dataProtection.tab.security.clear": "确认清空？", "dataProtection.tab.security.clear.tip": "清空将清空此操作在此层级下的所有设置", "dataProtection.tab.security.editing": "正在进行批量编辑 ......", "dataProtection.tab.security.search": "搜索操作类型", "dataProtection.tab.security.batchEdit": "批量编辑", "dataProtection.tab.security.batchClear": "批量清空", "dataProtection.tab.security.objectType": "对象类型:", "dataProtection.tab.security.objectType.plac": "选择对象类型", "dataProtection.tab.security.highType4": "拦截并提示", "dataProtection.tab.security.highType1": "只提示不拦截", "dataProtection.tab.security.highType2": "拦截提示走数据订正", "dataProtection.tab.security.highType3": "拦截提示申请高危操作", "dataProtection.tab.security.highLevel1": "低", "dataProtection.tab.security.highLevel2": "中", "dataProtection.tab.security.highLevel3": "高", "dataProtection.delete.tip": "确定删除此日志信息？", "dataProtection.dense.denseRule": {"REPLACE": "替换", "RANDOM_FLOATING": "随机浮动", "RANDOM_IN_RANGE": "范围内随机", "SUB": "截取", "ENCRYPTION": "加密", "HIDE": "隐藏", "DATA_SIMULATION": "数据仿真", "FILTER": "过滤"}, "dataProtection.tab.scan.getSampleCheck.error": "获取系统设置-基础设置中的采样行数、命中率失败", "dataProtection.tab.scan.editScanTask": "编辑扫描任务", "dataProtection.tab.scan.addScanTask": "添加扫描任务", "dataProtection.tab.scan.jobName.plac": "请输入任务名称", "dataProtection.tab.scan.description": "任务描述", "dataProtection.tab.scan.description.plac": "请输入任务描述", "dataProtection.tab.scan.scanRange": "扫描范围", "dataProtection.tab.scan.scanRange.plac": "请选择扫描范围", "dataProtection.tab.scan.autoScanning": "扫描规则", "dataProtection.tab.scan.autoScanning.plac": "请选择扫描规则", "dataProtection.tab.scan.cronExpression.plac": "请输入任务周期", "dataProtection.tab.scan.validate": "校验", "dataProtection.tab.scan.cronGenerator": "cron生成器", "dataProtection.tab.scan.scanResult.enable": "扫描结果启用后，扫描任务产生的扫描结果将自动启用默认的脱敏算法", "dataProtection.tab.scan.scanAlgoType.default": "默认算法", "common.text.selectUser": "请选择用户", "dataProtection.tab.scanTask.status0": "停止", "dataProtection.tab.scanTask.status1": "运行", "dataProtection.tab.scanTask.status2": "正在执行", "dataProtection.tab.scanTask.status3": "异常", "dataProtection.tab.scanTask.status4": "完成", "dataProtection.tab.scanTask.manual": "手动执行", "dataProtection.tab.scanTask.corn": {"from": "从", "second": "秒", "minute": "分", "hour": "时", "day": "日", "week": "周", "month": "月", "year": "年", "assign": "指定", "donTAssign": "不指定", "everyTime": {"second": "每一秒钟", "minute": "每一分钟", "hour": "每一小时", "day": "每一日", "week": "每一月", "month": "每一周", "year": "每年"}, "sun": "星期日", "mon": "星期一", "tue": "星期二", "wed": "星期三", "thu": "星期四", "fri": "星期五", "sat": "星期六", "aTob": {"second": "秒，每秒执行一次", "minute": "分，每分钟执行一次", "hour": "时，每小时执行一次", "day": "日，每日执行一次", "week": "，每星期执行一次", "month": "月，每月执行一次", "year": "年，每年执行一次"}, "aStartTob": {"secondContent1": "秒开始，每", "secondContent2": "秒执行一次", "minuteContent1": "分开始，每", "minuteContent2": "分执行一次", "hourContent1": "时开始，每", "hourContent2": "小时执行一次", "dayContent1": "日开始，每", "dayContent2": "日执行一次", "weekContent1": "本月第", "weekContent2": "周的", "weekContent3": "执行一次", "week2Content1": "月的最后一个", "week2Content2": "执行一次", "monthContent1": "月开始，每", "monthContent2": "月执行一次", "yearContent1": "年开始，每", "yearContent2": "年执行一次"}}, "dataProtection.tab.denses.batchSettingPolicy": "批量设置策略", "dataProtection.tab.denses.batchExport": "批量导出", "dataProtection.tab.security.windows": "窗口期设置", "dataProtection.tab.security.windows.tip": "窗口期内的高危操作设置将暂时失效", "dataProtection.alert.msg.windows": "当前处于窗口期，设置的高危操作暂不生效", "dataProtection.msg.set_windows_success": "窗口期设置成功", "dataProtection.selemsg.mult_sync_review_methods": "存在多种同步复核方式", "dataProtection.selemsg.mult_types_approvers": "存在多类型同步复核审批人"}