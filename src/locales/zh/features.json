{"loginRequired": "用户未登录或登录失效,", "pleaseLoginImmediately": "请立即登录!", "permissionAlias": "权限名", "permissionType": "权限类型", "userName": "用户名", "account": "账号", "bindUser": "绑定用户", "grantPermission": "授予权限", "sunday": "周日", "monday": "周一", "tuesday": "周二", "wednesday": "周三", "thursday": "周四", "friday": "周五", "saturday": "周六", "roleName": "角色名称", "pleaseInputRoleName": "请输入角色名称", "roleDescription": "角色描述", "roleValidity": "角色时效", "timePeriod": "时间段", "cycle": "周期", "allRange": "全部范围", "custom": "自定义", "allUsers": "全部用户", "title": "标题", "pleaseInputTitle": "请输入标题", "applyReason": "申请原因", "pleaseInputApplyReason": "请输入申请原因", "pleaseSelectOrEnterTargetTable": "请选择或输入目标表", "sourceField": "源字段", "targetField": "目标字段", "type": "类型", "length": "长度", "decimalPoint": "小数点", "primaryKey": "主键", "operation": "操作", "confirmDelete": "确定删除", "delete": "删除", "pleaseSelect": "请选择", "addMapping": "新增映射", "targetTable": "目标表", "importErrorContinue": "导入报错是否继续导入", "quickImport": "快速导入", "fieldLoading": "字段加载中", "fileUploadFailed": "文件上传失败", "pleaseSelectFileFirst": "请先选择文件", "pleaseSelectFileType": "请选择{{fileType}}类型文件", "onlySupportFileType": "只支持 .txt/.csv/.xls/.xlsx/.json 类型文件", "attachmentUpload": "附件上传", "uploadAttachment": "上传附件", "pleaseUploadAttachment": "请上传附件", "uploadingAttachment": "上传附件中...", "uploadLocation": "上传位置", "localUpload": "本地上传", "personalFolderUpload": "个人文件夹上传", "encoding": "编码", "pleaseSelectEncoding": "请选择编码格式", "table": "表", "tables": "表", "collection": "集合", "collections": "集合", "pleaseSelectTargetTable": "请选择目标表", "newTable": "新建表", "recordSeparator": "记录行分隔符", "pleaseSelectRecordSeparator": "请选择行分隔符", "fieldSeparator": "字段分隔符", "pleaseSelectFieldSeparator": "请选择字段分隔符", "fieldIdentifier": "字段识别符", "cannotBeEmpty": "不能为空", "onlyPositiveInteger": "只能输入正整数", "fieldNameRow": "字段名行", "firstDataRow": "第一个数据行", "lastDataRow": "最后一个数据行", "dateSort": "日期排序", "pleaseSelectDateSort": "请选择日期排序", "dateSeparator": "日期分隔符", "timeSeparator": "时间分隔符", "decimalPointSymbol": "小数点符号", "dateTimeSort": "日期时间排序", "pleaseSelectDateTimeSort": "请选择日期时间排序", "dateTime": "日期 时间", "dateTimeZone": "日期 时间 时区", "date": "日期", "time": "时间", "binaryDataEncoding": "二进制数据编码", "pleaseSelectBinaryDataEncoding": "请选择二进制数据编码", "importType": "导入类型", "textFile": "文本文件", "csvFile": "CSV 文件", "excelFile": "Excel 文件", "jsonFile": "JSON 文件", "addSuccess": "添加成功", "newRole": "新增角色", "previousStep": "上一步", "nextStep": "下一步", "complete": "完成", "bindSuccess": "绑定成功", "roleAlias": "角色名", "unbindUser": "从连接初始化角色下解绑用户，会同时将用户从该连接下移除", "added": "已添加", "addUser": "添加用户", "pleaseSelectAtLeastOneUser": "请选择至少一个用户", "pleaseSelectUserToAdd": "请选择要添加的用户", "pleaseSelectSameTypeElements": "请选择同类型元素", "operationPermission": "操作权限", "addDataOperationPermission": "新增数据操作权限", "editDataOperationPermission": "编辑数据操作权限", "permissionName": "权限名称", "pleaseInputPermissionName": "请输入权限名称", "permissionDescription": "权限描述", "databaseElement": "数据库元素", "pleaseSelectDatabaseElement": "请选择数据库元素", "pleaseSelectOperationPermission": "请选择操作权限", "editSuccess": "编辑成功", "newPermissionSet": "新增权限集", "editPermissionSet": "编辑权限集", "name": "名称", "pleaseInputPermissionSetName": "请输入权限集名称", "description": "描述", "pleaseSelectSubPermission": "请选择子权限", "subPermission": "子权限", "modifySuccess": "修改成功", "editRole": "编辑角色", "authorizationSuccess": "授权成功", "close": "关闭", "permissionObject": "权限对象", "all": "全部", "empty": "空", "copySuccess": "复制成功", "exportFileEncrypted": "导出文件已加密，密钥为", "encryptionKey": "密钥为", "fileKey": "文件密钥", "download": "下载", "connectionCreatedSuccess": "连接创建成功", "connectionAvailable": "连接可用", "connectionFailed": "连接失败", "monitorSwitch": "监控开关", "testConnection": "测试连接", "createDataSourceConnection": "创建 {{dataSourceType}} 连接", "databaseAddedSuccess": "数据库添加成功", "addDatabase": "添加数据库", "characterSet": "字符集", "collation": "排序规则", "pleaseSelectCharacterSet": "请选择字符集", "pleaseSelectCollation": "请选择排序规则", "generateSQL": "生成 SQL", "pleaseInput": "请输入{{title}}", "address": "地址", "port": "端口", "add": "添加", "labelCannotBeEmpty": "{{label}}不能为空", "pleaseSelectLabel": "请选择{{label}}", "pleaseInputLabel": "请输入{{label}}", "pleaseSelectOrAddLabel": "请选择或添加{{label}}", "item_label.file_preview": "文件预览"}