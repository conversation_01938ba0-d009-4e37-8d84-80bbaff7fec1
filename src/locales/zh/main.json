{"installApplication": "安装应用", "bindOtp": "绑定OTP", "otpBindSuccessful": "OTP绑定成功", "bindNow": "立即绑定", "bindSecurityDevice": "绑定安全设备 账号 {{userId}} 请按照以下步骤完成绑定操作", "downloadAuthyGoogleAuthenticator": "请在手机端下载并安装Authy/Google Authenticator（身份验证器）", "searchAuthyGoogleAuthenticatorOnAppStore": "iphone: 在AppStore搜索Authy/Google Authenticator（身份验证器）", "searchAuthyGoogleAuthenticatorOnGooglePlayStore": "Android : 在应用市场搜索Authy/Google Authenticator（身份验证器）", "proceedToBindingPage": "安装完成后点击下一步进入绑定页面（如已安装，直接进入下一步）", "scanQrCodeForVerificationCode": "使用Authy/Google Authenticator（身份验证器）扫描以下二维码，获取6位验证码", "enterOtpVerification": "请输入 OTP 验证", "otpVerificationCode": "OTP 验证码", "mobileBindSuccessful": "手机绑定成功", "exit": "退出", "bindPhoneNumber": "绑定手机号", "phoneNumber": "手机号码", "enterPhoneNumber": "请输入手机号", "verificationCode": "验证码", "enterVerificationCode": "请输入验证码", "getVerificationCode": "获取验证码", "passwordExpired": "当前用户密码已超出有效期，请修改密码！", "firstTimeLogin": "当前用户为第一次登录，请修改密码", "newPassword": "新密码", "confirmNewPassword": "确认新密码", "pleaseConfirmNewPassword": "请确认新密码", "passwordsDoNotMatch": "两次输入的密码不一致", "modifyPassword": "修改密码", "confirm": "确认", "prevStep": "上一步", "nextStep": "下一步"}