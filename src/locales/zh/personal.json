{"personalInformation": "个人信息", "securitySettings": "安全设置", "accessibleResources": "可访问资源", "preferenceSettings": "偏好设置", "personalCenter": "个人中心", "personalSettings": "个人设置", "additionalInformation": "补充信息", "uploadAvatar": "上传头像", "avatarUploadSuccessful": "头像上传成功", "modificationSuccessful": "修改成功", "copySuccessful": "复制成功", "save": "保存", "cancel": "取消", "edit": "编辑", "name": "姓名", "name_cannot_be_empty": "姓名不可为空", "please_enter_your_name": "请输入姓名", "systemRole": "系统角色", "department": "部门", "registrationTime": "注册时间", "email": "邮箱", "lastLoginTime": "上次登录时间", "phoneNumber": "手机号", "pleaseSelect": "请选择", "pleaseEnter": "请输入", "nickName": "名称", "verification_code_has_been_sent": "验证码已发送", "loginPassword": "登录密码", "changePassword": "修改密码", "phoneBinding": "手机绑定", "enterpriseWeChatBinding": "企业微信绑定", "emailBinding": "邮箱绑定", "bindNow": "马上绑定", "unbind": "解绑", "loginSettings": "登录设置", "otpSettings": "OTP设置", "sessionDuration": "登录保持时间", "modificationTime": "修改时间", "to_enhance_your_account_security": "为保障您的账号安全，可设置登录时使用OTP进行双因素认证", "after_setting_up_virtual_OTP": "绑定虚拟OTP后，您可以在登录时通过它来进行二次校验，也可以进行SQL命令的同步复核", "you_can_set_the_login_session_duration": "您可以设置保持登录的时间长度（大于等于1min，小于等于180min），超过该时间系统会退出登录，目前的设置为{{loginTime}}min（系统默认是30min）", "no_unbind": "系统强制绑定，不允许解绑", "settingNow": "马上设置", "emailBindingDescribe": "绑定邮箱，可用于接收消息", "noBind": "未绑定", "enterpriseWeChatBindingDescribe": "绑定企业微信，可通过扫码登录系统", "notEnabled": "未开启", "phoneBindingDescribe": "绑定手机, 可用于登录时短信二次认证", "loginPasswordDescribe": "安全性高的密码可以使帐号更安全。建议您定期更换密码，{{getPassWordTooltip}}", "changePhoneNumber": "修改手机号", "setting": "设置", "systemPasswordStrongDefault": "设置一个至少包含数字和大小写字母且长度9-16字符的密码", "systemPasswordStrong": "设置一个至少包含{{result}}且长度{{passwordMin}}-{{passwordMax}}字符的密码", "number": "数字", "lowercaseLetter": "小写字母", "uppercaseLetter": "大写字母", "specialCharacter": "特殊字符", "disableAuthentication": "关闭认证", "otpAuthentication": "OTP认证", "smsAuthentication": "短信认证", "two-FactorAuthenticationType": "双因素认证方式", "configured_Two-Factor_Authentication_method_during_login": "登入时需要通过设置的双因素方式进行认证", "verifyIdentity": "验证身份", "to_ensure_that_account_is_operated_by_you": "为确保账号 {{userId}}是您本人操作，请任意选择一种方式验证身份", "account": "账号", "password": "密码", "pleaseEnterPassword": "请输入密码", "verificationCode": "验证码", "pleaseVerificationCode": "请输入验证码", "phoneNumberPlus": "手机号码", "the_email_address_for_receiving_the_verification_code": "接收验证码的邮箱号为您账号中绑定的邮箱号", "you_can_retrieve_it_in_your_Email": "发送验证码后，您可以在邮箱中获取（1分钟内未收到，建议在垃圾邮件中查看）", "the_phone_number_for_receiving_the_verification_code": "接收验证码的手机号为您账号中绑定的安全手机号", "you_can_retrieve_it_in_your_SMS": "发送验证码后，您可以在手机短信中获取（1分钟内未收到，建议在垃圾短信中查看）", "unBindSuccessful": "解绑成功", "sendVerificationCode": "发送验证码", "getVerificationCode": "获取验证码", "please_obtain_the_dynamic_OTP_code_from_the_Authy_or_Google_Authenticator": "请在 Authy 或 Google 身份验证器 手机应用获取获取动态OTP码", "OTPVerification": "OTP验证", "accountPasswordVerification": "账号密码验证", "phoneNumberVerification": "手机号验证", "emailVerification": "邮箱验证", "unbindImmediately": "立即解绑", "verifyImmediately": "立即验证", "unbindOTP": "OTP解绑", "unbindEmail": "邮箱解绑", "otpCode": "OTP码", "twoFactor": "双因素认证", "sms": "短信", "pleaseSelectAuthenticationMethod": "请选择认证方式", "loginAccount": "登录账号", "oldPassword": "原密码", "newPassword": "新密码", "pleaseEnterOldPassword": "请输入原密码", "oldPasswordNotEqualNewPassword": "新旧密码不能一致", "confirmNewPassword": "确认新密码", "pleaseConfirmNewPassword": "请确认新密码", "passwordsDoNotMatch": "两次输入的新密码不一致", "contactAdminForReset": "请联系系统管理员重置密码", "forgotOriginalPassword": "忘记原密码", "confirmAndRe-login": "确定并重新登录", "unBindFailed": "解绑失败", "unbindPhone": "手机解绑", "fail": "失败", "no_Phone_Number_is_available.": "没有可验证的手机号", "modifyLoginSessionDuration": "修改登录保持时间", "pleaseEnterSessionDuration": "请输入 1-180 之间的数字", "the_system_will_log_you_out_after_this_duration": "超过该时间系统会退出登录，默认为 30 分钟，范围大于等于 1 分钟，最多 180分钟", "identityVerification": "身份验证", "newEmailVerification": "新邮箱号验证", "complete": "完成", "bindSuccessful": "绑定成功", "next": "下一步", "previous": "上一步", "newEmailAddress": "新邮箱号", "installApplication": "安装应用", "bindOTP": "绑定OTP", "bindSecurityDevice": "绑定安全设备 账号 {{userId}} 请按照以下步骤完成绑定操作", "downloadInstallAuthyGoogleAuthenticator": "请在手机端下载并安装Authy/Google Authenticator（身份验证器）", "iPhoneSearchAppStore": "iphone: 在AppStore搜索Authy/Google Authenticator（身份验证器）", "androidSearchGooglePlayStore": "Android : 在应用市场搜索Authy/Google Authenticator（身份验证器）", "afterInstallationClickNext": "安装完成后点击下一步进入绑定页面（如已安装，直接进入下一步）", "bindingSecurityDevice": "您已经成功绑定安全设备.", "doNotDeleteUninstallApp": "请勿随意删除卸载APP，否则会导致账号无法登陆。", "unbindOTPFirst": "如需要卸载APP或更换手机，请先解绑OTP。", "to_scan_the_QR_code_below_and_obtain_a_6-digit_verification_code": "使用Authy/Google Authenticator（身份验证器）扫描以下二维码，获取6位验证码", "otpVerificationCode": "OTP验证码", "pleaseEnterOTPVerificationCode": "请输入OTP验证码", "pleaseSelectPredefinedShortcutKeys": "请选择快预定义快捷键", "manager": "管理", "editorExitPrompt": "编辑器离开提示", "predefinedShortcutKeys": "预定义快捷键", "enable": "开启", "disable": "关闭", "continueOnExecutionError": "语句执行遇到错误是否继续", "paginationType": "分页类型", "scrollLoading": "滚动加载", "pageLoading": "分页加载", "encodingFormat": "编码格式", "enterContentAndUseShortcutKeys": "输入内容并使用快捷键", "resetSuccessful": "重置成功", "resetToDefault": "重置到默认值", "predefinedShortcutKeyManagement": "预定义快捷键管理", "predefinedShortcutKeysConflict_pre": "预定义快捷键可能与浏览器快捷键冲突，", "predefinedShortcutKeysConflict_postfix": "请自定义调整设置", "enterActionShortcutKey": "请输入动作/快捷键", "resetToDefaultValues": "将所有快捷键定义重置到预定义的默认值？", "shortcutKeys": "快捷键", "action": "动作", "displayOutputBasedOnInputFormat": "根据输入格式显示输出效果", "generating": "生成中...", "maximum100Characters": "最多可输入100个字符", "example": "范例", "displayOutputEffectOfCurrentInputFormat": "展示正在输入的格式输出效果", "date": "日期", "time": "时间", "dateTime": "日期时间", "input": "输出", "resultSetDisplayFormat": "结果集显示格式", "successful": "成功", "permissionDetails": "权限详情", "permissionType": "权限类型", "permissionLevel": "权限等级", "resource": "资源", "resourceType": "资源类型", "role": "角色", "effectiveTime": "生效时间", "authorizedBy": "授权人", "authorizationSource": "授权来源", "authorizedIP": "授权ip", "authorizationTime": "授权时间", "settingSuccessful": "设置成功", "settingFailed": "设置失败", "commonFormatting": "常见格式化", "year": "年份", "month": "月份", "day": "日", "dayOfWeek": "星期几 ", "organizeParameterList": "参数列表整理", "YYYY": "4位数字表示的年份，例如：2023", "yyyy": "4位数字表示的年份，例如：2023", "YY": "2位数字表示的年份，例如：23", "MM": "2位数字表示的月份，例如：01表示一月，12表示十二月", "MMM": "缩写形式的月份，例如：Jan表示一月，Dec表示十二月", "MMMM": "完整形式的月份，例如：January表示一月，December表示十二月", "dd": "2位数字表示的日期，例如：01表示1号，31表示31号", "ddd": "缩写形式的星期几，例如：Mon表示星期一，Sun表示星期日", "dddd": "完整形式的星期几，例如：Monday表示星期一，Sunday表示星期日", "HH": "24小时制的小时，例如：00表示午夜12点，23表示晚上11点", "hh": "12小时制的小时，例如：01表示上午1点，12表示下午12点", "mm": "分钟，例如：00表示整点，30表示30分钟", "ss": "秒，例如：00表示整分，59表示59秒", "a": "上午/下午标识，例如：AM表示上午，PM表示下午", "personal.preferenceSetting.constant.example": "|符号|含义|示例|<br/>\n|----|----|----|<br/>\n|G|显示纪元文本|AD（公元）|<br/>\n|u|年份|2023|<br/>\n|У|公元年份|2023|<br/>\n|D|一年的第 N 天|300|<br/>\n|M 或者 L|月份|7;07; Jul; July;|<br/>\n|d|一个月的第 N 目|9|<br/>\n|Q 或者 q|年份中的季度|2; 02; Q2; 2rd quarter|<br/>\n|Y|以周为基础的年份|1997;97|<br/>\n|w|以周为基础的年份中的周|30|<br/>\n|W|一个月的第 N 周|3|<br/>\n|E|星期|Tue; Tuesday; T|<br/>\n|e 或者 c|本地化星期|2; 02; Tue; Tuesday; T|<br/>\n|F|一个月的第 N 周|2|<br/>\n|a|一天中的上午（am）或者下午（pm）|PM|<br/>\n|h|上午下午 12 小时制（1 - 12）|10|<br/>\n|K|上午下午 11 小时制（1 - 11）|1|<br/>\n|k|上午下午（1 - 24）|14|<br/>\n|H|小时制（0 - 23）|1|<br/>\n|m|小时中的分钟|30|<br/>\n|s|分秒|43|<br/>\n|S|当前秒的毫秒|800|<br/>\n|A|当前天的毫秒数|1111|<br/>\n|n|当前秒的纳秒数|987654321|<br/>\n|N|当前天的纳秒数|1234000000|<br/>\n|V|时区 ID|America/Los_Angeles; Z; -08:30|<br/>\n|z|时区名称|Pacific Standard Time; PST|<br/>\n|O|本地化时区漂移|GMT + 8; GM T + 08:00; UTC - 08:00|<br/>\n|X|offset - X|Z; -08; -0830; -08:30; -083015; -08:30:15|<br/>\n|x|offset - x|+0000; -08; -0830; -08:30; -083015; -08:30:15|<br/>\n|Z|offset - Z|+0000; -0800; -08:00;|<br/>\n|p|pad modifier|1|<br/>\n|'|分隔符| |<br/>\n|''|不被解析的文字| |<br/>\n|[|可选部分开始| |<br/>\n|]|可选部分结束| |", "hello": "你好", "newbieSceneGuide": "新手场景引导", "personalFolder": "个人文件夹", "taskCenter": "任务中心", "versionInfo": "版本信息", "auditAgentDownload": "审计agent下载", "executionHistory": "执行历史", "logout": "退出", "confirm": "确认", "confirmExit": "是否继续退出？退出后你所更改的SQL语句将会丢失，建议备份", "welcomeToOnboarding": "欢迎来到入口引导", "firstLoginInstructions": "首次登录您可以根据以向导提示来完成相关功能设置和操作，在用户中心可再次查询到该页面。", "databaseManagement": "数据库管理", "connectionManagement": "连接管理", "createConnectionOperations": "创建连接相关操作", "newConnectionWizard": "新建连接向导", "subjectAuthorization": "主体授权", "authorizationByUser": "按用户维度进行授权", "subjectAuthorizationWizard": "主体授权向导", "objectAuthorization": "客体授权", "authorizationByDatabaseObject": "按数据库对象维度授权", "objectAuthorizationWizard": "客体授权向导", "automaticAuthorization": "自动授权", "createPermissionSet": "创建权限集通过条件使权限集自动对用户生效", "automaticAuthorizationWizard": "自动授权向导", "ruleManagement": "规则管理", "sqlAuditRules": "SQL审核规则", "ruleTemplateWizard": "规则模板向导", "sensitiveFields": "脱敏字段", "setMaskingAlgorithm": "为表敏感字段设置脱敏算法", "sensitiveFieldsWizard": "脱敏字段向导", "maskedData": "脱敏数据", "enableRules": "开启规则，发现敏感数据即脱敏", "maskedDataWizard": "脱敏数据向导", "maskingScan": "脱敏扫描", "createScanTask": "创建扫描任务，定时扫描敏感数据", "maskingScanWizard": "脱敏扫描向导", "highRiskOperations": "高危操作及导入导出设置", "securitySettingsWizard": "安全设置向导", "process": "流程", "myApplications": "我的申请", "accessRequest": "访问申请", "elevationWizard": "提权向导", "customProcessDesign": "自定义流程设计", "customChangeRequestProcess": "自定义变更申请流程", "customProcessDesignWizard": "自定义流程设计向导", "uploadFile": "上传文件", "newFolder": "新建文件夹", "detailsInfo": "详情信息", "tileView": "平铺", "searchInCurrentFolder": "在当前文件夹中搜索", "rename": "重命名", "copyFileAddress": "复制文件地址", "goToParentFolder": "返回上级", "downloaded": "已下载", "processing": "处理中", "creating": "正在创建", "inQueue": "队列中", "fileName": "文件名", "exportStatement": "导出语句", "exportFormat": "导出格式", "exportRows": "导出行数", "taskCreationTime": "任务创建时间", "fileKey": "文件密钥", "exportFileEncrypted": "导出文件已加密 , 密钥为", "taskStatus": "任务状态", "operation": "操作", "view": "查看", "download": "下载", "delete": "删除", "refreshList": "刷新列表", "downloadTask": "下载任务", "textImportTask": "文本导入任务", "confirmDeleteTask": "确定删除此任务?", "connectionType": "连接类型", "log": "日志", "operationType": "操作类型", "import": "导入", "export": "导出", "status": "状态", "creationTime": "创建时间", "fileDownload": "文件下载", "logDownload": "日志下载", "fileType": "文件类型", "continueImport": "继续导入", "enterOperationStatement": "请输入操作语句进行检索", "frequentConnections": "常用连接", "quickOpen": "快捷打开", "totalRecords": "共 {{total}} 条", "connectionName": "连接名", "creator": "创建人", "filterConditions": "过滤条件", "executeConnection": "执行连接", "statementType": "语句类型", "executionResult": "执行结果", "executionSuccess": "执行成功", "executionFailure": "执行失败", "startDate": "开始日期", "endDate": "结束日期", "query": "查询", "reset": "重置", "confirmExportCurrentFilter": "确定导出当前筛选结果", "taskExecutionHistoryExport": "任务,执行历史导出", "executionCompleted": "已经执行完成，文件生成成功", "dataSource": "数据源", "operationObject": "操作对象", "userName": "用户姓名", "databaseType": "数据库类型", "applicationServerIP": "应用服务器IP", "databaseIP": "数据库IP", "databasePort": "数据库端口", "operationStatement": "操作语句", "dataVersion": "数据版本", "startTime": "开始时间", "clientIP": "客户端IP", "operationResult": "操作结果", "errorMessage": "错误信息", "affectedRows": "影响行数", "durationMs": "耗时（ms）", "executionWindowID": "执行窗口ID", "executionMode": "执行模式", "source": "来源", "confirmDelete": "确定要删除吗", "fileNotFound": "文件不存在", "confirmTerminateTask": "确定终止该任务吗", "yes": "是", "no": "否", "synchronizedResources": "同步的资源", "targetTable": "目标表", "exportedResources": "导出的资源", "exportResultSetNotSupported": "结果集导出不支持展示导出资源", "cancelTask": "取消任务", "confirmCancelTask": "确定取消该任务吗", "operationSuccess": "操作成功", "deleteSuccess": "删除成功", "cancelSuccess": "取消成功", "terminate": "终止", "viewDetails": "查看详情", "timePeriod": "时间周期", "batchOperation": "批量操作", "batchDelete": "批量删除", "batchDownload": "批量下载", "batchTerminate": "批量终止", "confirmTerminate": "确定要终止吗", "last7Days": "近7日", "last15Days": "近15日", "last30Days": "近30日", "textImport": "文本导入", "batchExecute": "批量执行", "syncDataDictionary": "同步数据字典", "dataCopy": "数据复制", "autoExecute": "自动执行", "immediateExecute": "立即执行", "scheduledExecute": "定时执行", "ignore": "忽略", "taskNumber": "任务编号", "description": "描述", "inProgress": "进行中", "completed": "完成", "paused": "已暂停", "pending": "等待中", "taskName": "任务名称", "target": "目标", "startTimeFa": "发起时间", "endTime": "结束时间", "taskInformation": "任务信息", "exportFile": "导出文件", "taskType": "任务类型", "exportType": "导出类型", "taskEndTime": "任务结束时间", "createInfo": "创建信息", "importConfiguration": "导入配置", "mappingRelationshipDisplay": "映射关系展示", "importFile": "导入文件", "fieldDelimiter": "字段分隔符", "recordLineDelimiter": "记录行分隔符", "fieldIdentifier": "字段识别符", "fieldNameLine": "字段名行", "firstDataLine": "第一个数据行", "lastDataLine": "最后一个数据行", "decimalPointSymbol": "小数点符号", "dateTimeSorting": "日期时间排序", "binaryDataEncoding": "二进制数据编码", "sourceField": "源字段", "targetField": "目标字段", "attachmentInformation": "附件信息", "indexFile": "索引文件", "sqlAuditResult": "SQL审核结果", "executionMethod": "执行方式", "taskCycle": "任务周期", "executionErrorHandler": "执行错误处理", "executing": "执行中", "pendingExecution": "待执行", "waitingForAutoExecution": "等待自动执行", "waiting": "正在等待", "currentlyExecuting": "正在执行", "taskCompleted": "任务完成", "taskFailed": "任务失败", "taskClosed": "任务关闭", "terminating": "正在终止", "taskTerminated": "任务终止", "terminationSuccessful": "终止成功", "terminationFailed": "终止失败", "batchExecutionSuccessful": "批量执行成功", "batchExecutionFailed": "批量执行失败", "confirmTaskReExecution": "确认任务重新执行", "reExecutionWillStartFromFirstScript": "任务重新执行将从第一个脚本顺序执行，请确保之前执行的影响结果已被清除", "confirmTermination": "确认终止", "thisTerminationWillStopExecutingFiles": "此终止操作将终止正在执行的文件", "confirmReExecutionOfThisScript": "确认重新执行此脚本", "reExecutionOfThisScriptPleaseEnsureEffectsCleared": "重新执行此脚本前，请确保之前执行的影响结果已被清除", "thisTerminationWillStopEntireTask": "此终止操作将终止整个任务", "batchTerminationSuccessful": "批量终止成功", "batchTerminationFailed": "批量终止失败", "queryTaskListFailed": "查询任务列表失败", "downloadFileFailed": "下载文件失败", "downloadLogFailed": "下载日志失败", "downloadErrorLogFailed": "下载错误日志失败", "replace": "替换", "database": "数据库", "executionStatus": "执行状态", "executionLog": "执行日志", "taskDetails": "任务详情", "executionDetails": "执行详情", "taskDescription": "任务描述", "reExecution": "重新执行", "start": "开始", "taskLog": "任务日志", "newBatchExecutionTask": "新建批量执行任务", "createBatchExecutionTaskSuccessful": "创建批量执行任务成功", "createBatchExecutionTaskFailed": "创建批量执行任务失败", "fileParsingFailed": "解析文件失败", "singleUploadParsingFailed": "单个上传解析文件失败", "confirmDeleteTemplateFile": "确认删除模板文件吗?删除后所有附件也将清空", "deleteUploadedSqlFileFailed": "删除上传sql文件失败", "maxLength20Characters": "最长不超过20个字符", "downloadSuccessful": "下载成功", "sqlAuditResultContainsError": "SQL审核结果中存在error等级的暂不允许进行下一步操作", "taskCycleExpressionInvalid": "任务周期表达式未通过校验", "downloadAuditResult": "下载审核结果", "uploadAttachment": "上传附件", "batchExecutionSupportsPlainTextOnly": "批量执行仅支持纯文本类型的文件", "confirmDing": "确定", "pleaseSelectAttachmentUpload": "请选择附件上传", "attachmentUpload": "附件上传", "clickUploadFolder": "点击上传文件夹", "uploadFolder": "上传文件夹", "supportUploadFolderAndCompressedFiles": "支持上传文件夹和压缩文件，文件中支持包含要执行的sql文件和执行顺序文件（索引文件）", "failedToGetUploadFileSizeLimit": "获取上传文件大小限制失败", "sqlAudit": "SQL审核", "pleaseSelectExecutionMethod": "请选择执行方式", "pleaseEnterTaskCycle": "请输入任务周期", "validation": "校验", "cronGenerator": "cron生成器", "pleaseSelectExecutionErrorHandler": "请选择执行错误处理", "inputDescriptionContent": "输入描述内容", "downloadTemplateFileFailed": "下载模板文件失败", "fileSizeExceeds10MLimit": "文件大小超过{{uploadSize}}M限制", "templateFolderDownload": "模板文件夹下载", "templateFileContainsExecutionOrderTemplateAndSqlExample": "模板文件中包含执行顺序文件模板和sql文件示例，您可下载后参考", "templateDownload": "模板下载", "ifYouKnowExecutionLogic": "若您已知执行逻辑，可直接选择文件进行上传", "selectZipArchive": "选择zip压缩包", "selectFolder": "选择文件夹", "collapse": "收起", "expand": "展开", "details": "详情", "sequenceNumber": "序号", "executionStatement": "执行语句", "parsingResult": "解析结果", "versionComparison": "版本对比", "unnamedFolder": "未命名文件夹", "newFolderCreated": "新建文件夹「{{folderName}}」成功", "nameCannotBeEmpty": "名称不能为空", "nameMaxLength25Characters": "名称最长25个字符", "move": "移动", "directoryContent": "包含 {{val}} 个项目，您可查看的项目 {{count}} 个", "directorySize": "总大小", "bit": "字节", "fileSize": "文件大小", "modifiedTime": "最近修改于{{time}}", "nickNameHint": "名称最长 25 个字符", "updateTime": "修改日期", "type": "类型", "size": "大小", "copySuccess": "复制成功", "moveSuccess": "移动成功", "moveTargetPath": "请选择目标文件夹", "ascendingOrder": "正序", "descendingOrder": "倒序", "noInformation": "暂无信息", "exportTotalRows": "导出总行数", "refreshTask": "刷新任务", "viewLog": "查看日志", "initialize": "初始化", "running": "运行中", "pause": "暂停", "sourceLess": "源", "waitingDuration": "等待时长", "dataVolume": "数据量", "includesData": "包含数据", "newTable": "新表", "remainingTime": "剩余时间", "targetIndexField": "目标 {{index}} 字段", "sourceIndexField": "源 {{index}} 字段", "LoginSettingManagerTip": "登录设置由管理员管控", "forceSMSLogin": "系统强制开启短信认证登录", "forceOTPLogin": "系统强制开启OTP认证登录", "editPhone": "若需要修改手机号，请联系管理员", "logDetails": "日志详情", "info": "信息", "loading": "加载中", "executionTime": "执行耗时", "failedToReplaceFile": "替换文件失败", "logInfo": "日志信息", "databaseName": "数据库名", "schema": "模式", "tableName": "表名", "importFileName": "导入文件名", "fileReadProgress": "文件读取进度", "continueImportStart": "继续导入开始", "textImportBatchDeleteTip": "此操作对于已结束的任务将直接删除任务；执行中的任务将立即中断并回退支持回退的数据后删除任务", "textImportDeleteTip1": "此操作将中断任务并回退执行的数据", "textImportDeleteTip2": "此操作将中断任务，但无法回退执行的数据", "btn.batch_export": "批量下载", "btn.download": "下载", "tip_title.select_task": "请先勾选执行文件记录", "tip_title.no_export_file": "所选任务未生成结果文件，暂不支持下载", "tip_title.no_result_file": "未生成结果文件，无法下载", "ntf_title.batch_execute_export": "任务 批量执行任务结果下载", "ntf_desc.exe_complete_fl_generated": "已经执行完成，文件生成成功", "ntf_title.execute_log_download": "任务 执行语句结果下载", "resetSuccessfult": "重置成功", "abort": "中止", "aborting": "正在中止", "copyFailed": "复制失败", "shortcutValidate": "快捷键必须为组合键，请重新输入", "shortcutDisabledCustomEdit": "当前使用的插件版本不支持自定义编辑"}