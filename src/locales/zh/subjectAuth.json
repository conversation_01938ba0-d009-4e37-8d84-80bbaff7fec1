{"updateSuccess": "修改成功", "noData": "暂无数据", "userHasOtherPermissionsAtThisLevel": "此用户在此层级下存在其他权限设置", "loadMore": "加载更多...", "paginationLoadMoreError": "分页加载更多error", "selectDatabaseElement": "请选择数据库元素", "selectAll": "全选", "cancel": "取消", "confirm": "确认", "onlyOnePermissionAllowed": "只能选择一项权限", "otherControlMethods": "其他方式控制", "pleaseSelectPermission": "请选择权限", "retainPreviousUserPermissionLevel": "保留用户此前权限等级", "resetPermissionsOrSelectToolPermissions": "请重置权限或选择工具权限", "permissionUpgradeFailed": "提权失败 :>>  ", "pleaseSelectDatabaseElement": "请选择数据库元素", "onlySameDataSourceTypeAllowed": "只能选择同种数据源类型", "onlySameLevelElementsAllowed": "只能选择同等级的元素", "onlySameTypeElementsAllowed": "只能选择相同类型的元素", "addPermission": "新增权限", "permissionDisplayDependsOnSelectedResource": "权限展示取决于所选资源，所选资源中存在schema及以上层级则展示权限等级（权限集合），否则展示对应资源支持的相应操作", "toolPermission": "工具权限", "pleaseSelectToolPermission": "请选择工具权限", "authorizeTo": "授权给 ", "user": " 用户", "department": " 部门", "company": " 公司", "currentAuthorizationObjectIsTopLevelOrganizationNode": "当前授权对象为顶层组织节点（公司），请谨慎操作。", "add": "添加", "copy": "复制", "selectUserToCopyTo": "选择要复制给的用户", "pleaseSelectUser": "请选择用户", "additionalPermissionsAllowed": "可追加权限", "username": "用户名", "authorizationSource": "授权来源", "permanent": "永久", "operation": "操作", "confirmRemoval": "确认移除?", "confirm2": "确定", "remove": "移除", "view": "查看", "viewDetails": "查看详情", "confirmRemoveUserResource": "确认移除此用户资源", "setSuccess": "设置成功", "copySuccess": "复制成功", "removeSuccess": "移除成功", "authorizeSuccess": "授权成功", "editSuccess": "编辑成功", "confirmBatchRemoveUserResource": "确认批量移除此用户资源", "batchRemovePermissions": "批量移除权限", "batchCopyPermissions": "批量复制权限", "batchModifyEffectiveTime": "批量修改生效时间", "userPermissionDetails": "用户权限详情", "enterResourceOrPermissionNameToSearch": "请输入资源名称/权限名称进行检索", "subjectAuthorization": "主体授权", "authorization": "授权", "batchOperation": "批量操作", "returnToViewAllResources": "返回查看全部资源", "deleteSuccess": "删除成功", "enterSearchContent": "请输入搜索内容", "organizationStructure": "组织架构", "selectDepartmentOrUserHierarchyForAuthorization": "在这里选中需要进行授权的部门或用户层级", "clickAuthorizeAndSelectDataSourceAndPermissions": "点击授权，选择相应的数据源和权限", "databaseManagement": "数据库管理", "manualAuthorization": "手动授权", "resource": "资源", "resourceType": "资源类型", "permission": "权限", "permissionType": "权限类型", "affectedUsers": "影响用户", "permissionDetails": "权限详情", "linkedRole": "连接角色", "effectiveTime": "生效时间", "permissionSource": "权限来源", "authorizer": "授权人", "authorizationIP": "授权IP", "authorizationTime": "授权时间", "tb_render.data_source_operation_permission": "数据源操作权限", "tb_render.sensitive_resource_access_permission": "敏感资源访问权限", "tb_render.high_risk_operation_permission": "高危操作权限", "tb_render.tool_permission": "工具权限", "tb_render.automatic_authorization": "自动授权", "tb_render.process_privilege_escalation": "流程提权", "tb_render.manual_authorization": "手动授权"}