{"systemManagement.title": "系统管理", "systemManagement.personManagement.title": "用户管理", "systemManagement.personManagement.delete.success": "删除成功", "systemManagement.personManagement.group.delete.success": "组删除成功", "systemManagement.personManagement.dept": "部门", "systemManagement.personManagement.partner": "合作方", "systemManagement.personManagement.modal.delete.dept": "确定删除{{modalTitle}}：{{name}}？", "systemManagement.personManagement.modal.delete.group": "确定删除组：{{name}}？", "systemManagement.personManagement.editName": "修改名称", "systemManagement.personManagement.addEdpt": "添加部门", "systemManagement.personManagement.addGroup": "添加组", "systemManagement.personManagement.editPrincipal": "修改负责人", "systemManagement.personManagement.export.tip1": "任务 选中部门的用户信息导出", "systemManagement.personManagement.export.tip2": "已经执行完成，文件生成成功", "systemManagement.personManagement.btn.import": "导入用户", "systemManagement.personManagement.export.modal.title": "确定导出当前选中部门的用户信息？", "systemManagement.personManagement.btn.export": "导出用户", "systemManagement.personManagement.user_parameter": "用户参数管理", "systemManagement.personManagement.number_group": "组人数", "systemManagement.personManagement.number_dept": "部门人数", "systemManagement.personManagement.number_company": "公司人数", "systemManagement.personManagement.setting.success": "设置成功", "systemManagement.personManagement.setting.error": "设置失败", "systemManagement.personManagement.twoFactor.no": "部分用户未设置双因素认证", "systemManagement.personManagement.reset_password.tip": "确定重置密码?", "systemManagement.personManagement.reset_password.success": "密码重置成功", "systemManagement.personManagement.table.column.user": "用户", "systemManagement.personManagement.delete.tip": "确定删除用户?", "systemManagement.personManagement.delete.error": "删除失败", "systemManagement.personManagement.batch_delete": "批量删除用户", "systemManagement.personManagement.batch_setting_time": "批量设置生效时间", "systemManagement.personManagement.alluUsers": "全部用户", "systemManagement.personManagement.table.column.userName": "用户名", "systemManagement.personManagement.table.column.userId": "登录账号", "systemManagement.personManagement.table.column.departmentName": "所属部门", "systemManagement.personManagement.table.column.systemRoles": "用户角色", "systemManagement.personManagement.table.column.telephone": "手机", "systemManagement.personManagement.table.column.createFrom": "CQ/域", "systemManagement.personManagement.table.column.auditRange": "审计范围", "systemManagement.personManagement.table.column.ip": "允许用户登录IP", "systemManagement.personManagement.table.column.userStatus": "状态", "systemManagement.personManagement.table.column.loginAuth": "登录认证方式", "systemManagement.personManagement.table.column.twoFactor": "双因素认证方式", "systemManagement.personManagement.table.column.effectTime": "生效时间", "systemManagement.personManagement.createFrom.cqUser": "CQ用户", "systemManagement.personManagement.createFrom.ad": "AD 域", "systemManagement.personManagement.reset_otp.tip": "确定重置 OTP 密钥?", "systemManagement.personManagement.btn.reset_otp": "重置OTP", "systemManagement.personManagement.reset_user_password.tip": "确定重置该用户密码?", "systemManagement.personManagement.delete_user.tip": "确定删除该用户?", "systemManagement.personManagement.moveout.success": "移出成功", "systemManagement.personManagement.search_user.placeholder": "请输入用户名/账号", "systemManagement.personManagement.btn.members": "成员管理", "systemManagement.personManagement.btn.users": "用户管理", "systemManagement.personManagement.noPerm": "您当前的角色是[{{val}}], 对[用户管理]没有操作权限", "systemManagement.personManagement.btn.addUser": "新增用户", "systemManagement.personManagement.table.pagination.total": "共 {{val}} 条", "systemManagement.personManagement.addUser.ip.tip": "示例：", "systemManagement.personManagement.addUser.ip": "允许登录的IP", "systemManagement.personManagement.btn.editUser": "编辑用户信息", "systemManagement.personManagement.btn.editAndAdd": "编辑并新增用户信息", "systemManagement.personManagement.addUser.parameter.hint": "请输入{{val}}", "systemManagement.personManagement.addUser.dynamicFieldName": "认证生效时间", "systemManagement.personManagement.addUser.userId.placeholder": "请输入登录账号", "systemManagement.personManagement.addUser.password.placeholder": "请输入6位数以上的密码", "systemManagement.personManagement.addUserModal.password": "确认密码", "systemManagement.personManagement.addUserModal.password.hint": "请确认密码", "systemManagement.personManagement.addUserModal.password.hint2": "两次输入密码不一致", "systemManagement.personManagement.addUserModal.userName": "用户名", "systemManagement.personManagement.addUserModal.userName.hint": "请输入用户名", "systemManagement.personManagement.addUserModal.userGender": "用户性别", "systemManagement.personManagement.addUser.userGender.male": "男", "systemManagement.personManagement.addUser.userGender.female": "女", "systemManagement.personManagement.addUserModal.departmentId.hint": "请选择所属部门", "systemManagement.personManagement.addUserModal.jobNumber": "工号", "systemManagement.personManagement.addUserModal.jobNumber.hint": "请填写工号", "systemManagement.personManagement.addUserModal.jobNumber.tip": "默认为", "systemManagement.personManagement.addUserModal.role.disabled.tip": "当前没有角色管理功能，不支持编辑用户角色", "systemManagement.personManagement.addUserModal.telephone": "手机号码", "systemManagement.personManagement.addUserModal.telephone.tip": "请输入手机号码", "systemManagement.personManagement.addUserModal.rangeType": "审计范围", "systemManagement.personManagement.addUserModal.rangeType.tip": "即选择此审计员可查看的数据范围，默认可查看系统中所有用户的操作行为", "systemManagement.personManagement.addUserModal.rangeType.hint": "请选择审计范围", "systemManagement.personManagement.addUserModal.rangeType.all": "全局用户", "systemManagement.personManagement.addUserModal.rangeType.custom": "自定义", "systemManagement.personManagement.addUserModal.auditRange": "选择审计范围", "systemManagement.personManagement.addUserModal.phone.hint": "手机号码格式错误", "systemManagement.personManagement.addUserModal.Phone.tip": "请填写手机号码", "systemManagement.personManagement.addUserModal.userPassword": "用户密码", "systemManagement.personManagement.addUserModal.ip": "允许登录IP", "systemManagement.personManagement.members.edit_success": "成员编辑成功", "systemManagement.personManagement.members.groupMember": "小组成员", "systemManagement.personManagement.members.userId": "账号", "systemManagement.personManagement.batchEffectTime": "批量设置生效时间", "systemManagement.personManagement.batchEffectTime.tip": "请选择认证生效时间！", "systemManagement.personManagement.parameter.search": "输入关键词", "systemManagement.personManagement.parameter.parameterName": "参数名称", "systemManagement.personManagement.parameter.tagName": "参数标签", "systemManagement.personManagement.parameter.description": "备注", "systemManagement.personManagement.parameter.defaultValue": "默认值", "systemManagement.personManagement.parameter.needed": "必填项", "systemManagement.personManagement.parameter.dataSourceType": "数据源", "systemManagement.personManagement.parameter.objectPath": "对象路径", "systemManagement.personManagement.parameter.objectName": "对象名称", "systemManagement.personManagement.parameter.filterRule": "过滤规则", "systemManagement.personManagement.parameter.status": "状态", "systemManagement.personManagement.parameter.delete.tip": "确定删除：{{val}}？", "systemManagement.personManagement.parameter.add": "添加参数", "systemManagement.personManagement.parameter.filterRelevance": "查看 {{val}} 过滤关联", "systemManagement.personManagement.parameter.params": "参数", "systemManagement.personManagement.parameter.action.tip": "{{val}}成功", "systemManagement.personManagement.parameter.value": "参数值{{val}}", "systemManagement.personManagement.parameter.parameterName.tip": "请输入参数名称", "systemManagement.personManagement.parameter.tagName.tip": "请输入参数标签", "systemManagement.personManagement.parameter.examples": "参数枚举值", "systemManagement.personManagement.parameter.needed.hint": "必填项不可为空", "systemManagement.role": "角色管理", "systemManagement.role.save.tip": "请勾选授权权限", "systemManagement.role.edit": "修改角色", "systemManagement.role.edit.tip": "确认删除此角色？", "systemManagement.role.custom": "自定义角色", "systemManagement.role.new": "新增角色", "systemManagement.role.create": "创建角色", "systemManagement.role.create.roleName": "角色名称", "systemManagement.role.create.roleTemplateId": "角色模板", "systemManagement.role.create.description": "角色描述", "systemManagement.role.create.description.placeholder": "这是一段角色描述文案，限制最多40字。", "systemManagement.role.switchStatus.success": "授权模式开关切换成功", "systemManagement.role.roleName.tip": "角色名称，限制最多20字。", "systemManagement.role.roleDesc": "角色描述:", "systemManagement.role.associatedUser": "关联用户:", "systemManagement.role.authMode": "授权模式:", "systemManagement.role.bind.title": "绑定用户", "systemManagement.role.bind.success": "绑定成功", "systemManagement.role.edit.success": "修改成功", "systemManagement.role.bind.roleName": "角色名：", "systemManagement.role.bind.boudendUser": "已绑用户", "systemManagement.role.roleAuth": "角色授权", "systemManagement.role.status1": "全部权限 未授权", "systemManagement.role.status2": "全部权限 已授权", "systemManagement.role.status3": "全部权限 必选", "systemManagement.role.status4": "只读权限 未授权", "systemManagement.role.status5": "只读权限 已授权", "systemManagement.role.status6": "可读权限 必选", "systemManagement.role.edit.authDef": "编辑权限样式定义", "systemManagement.role.authDef": "权限样式定义", "systemManagement.system": "系统设置", "systemManagement.system.ui": "UI配置", "systemManagement.system.secretKey": "应用密钥", "systemManagement.system.email": "邮箱设置", "systemManagement.system.sms": "短信网关设置", "systemManagement.system.sso": "单点登录配置", "systemManagement.system.basic": "基础设置", "systemManagement.system.password": "密码策略", "systemManagement.system.analysis": "解析配置", "systemManagement.system.auth": "授权信息", "systemManagement.system.watermark": "水印设置", "systemManagement.system.access": "访问设置", "systemManagement.system.dataMigration": "数据迁移", "systemManagement.system.alarm": "告警配置", "systemManagement.system.log": "日志暴露配置", "systemManagement.system.ui.tip": "只支持jpeg|png|jpg格式的图片", "systemManagement.system.ui.tip2": "图片不能超过200kb!", "systemManagement.system.ui.uploadSuccess": "公司logo上传成功", "systemManagement.system.ui.delete.tip": "确定要删除公司logo吗?", "systemManagement.system.ui.logoReplace": "Logo替换", "systemManagement.system.ui.companyLogo": "公司logo", "systemManagement.system.ui.companyLogo.tip": "建议上传图片比例为11:4，图片格式为 png、jpg、jpeg，图片大小不超过 200KB", "systemManagement.system.ui.upload": "上传图片", "systemManagement.system.ui.logoReplace.tip": "下次页面刷新更新logo", "systemManagement.system.secretKey.table.createAt": "创建时间", "systemManagement.system.secretKey.table.creator": "创建人", "systemManagement.system.secretKey.table.action.reset.tip": "确定重置该 appSecret ?", "systemManagement.system.secretKey.table.action.delete.tip": "确定删除{{appKey}}的 appSecret ?", "systemManagement.system.secretKey.subTitle": "AppSecret 管理", "systemManagement.system.secretKey.search.tip": "请输入appKey", "systemManagement.system.secretKey.desc": "备注", "systemManagement.system.secretKey.desc.placeholder": "请输入备注", "systemManagement.system.secretKey.edit.title": "编辑 AppSecret", "systemManagement.system.email.test": "测试发件邮箱", "systemManagement.system.email.test.success": "测试通过", "systemManagement.system.email.test.falled": "测试未通过", "systemManagement.system.email.host": "邮件服务器", "systemManagement.system.email.host.tip": "请输入邮件服务器", "systemManagement.system.email.port": "邮件服务器端口", "systemManagement.system.email.port.tip": "请输入邮件服务器端口", "systemManagement.system.email.tls": "使用 TLS 安全协议", "systemManagement.system.email.emailAddress": "发件邮箱", "systemManagement.system.email.emailAddress.tip": "请输入发件邮箱", "systemManagement.system.email.authCode": "授权码", "systemManagement.system.email.authCode.tip": "请输入授权码", "systemManagement.system.email.testEmailAddress": "收件邮箱", "systemManagement.system.email.testEmailAddress.tip": "请输入收件邮箱", "systemManagement.system.email.submit.tip": "请测试通过后再保存", "systemManagement.system.sms.commandReview": "命令复核", "systemManagement.system.sms.twoFactorAuth": "双因素认证", "systemManagement.system.sms.alarmConfig": "告警配置", "systemManagement.system.sms.phone.overThreshold": "超过阈值", "systemManagement.system.sms.phone.check.empty": "存在为空的变量值", "systemManagement.system.sms.phone.check.success": "验证成功", "systemManagement.system.sms.phone.check.falled": "验证失败", "systemManagement.system.sms.phone.submit.tip": "请确保短信通道手机验证成功后再保存", "systemManagement.system.sms.phone.submit.tip2": "短信通道配置发生变更，请再次发送测试短信验证", "systemManagement.system.sms.smsName": "通道名称", "systemManagement.system.sms.smsName.tip": "请填写通道名称", "systemManagement.system.sms.smsName.hint": "通道名称不可为空", "systemManagement.system.sms.businessType": "生效告警类型", "systemManagement.system.sms.businessType.hint": "生效告警类型不可为空", "systemManagement.system.sms.businessType.tip": "请选择生效告警类型", "systemManagement.system.sms.supplierType": "短信通道", "systemManagement.system.sms.supplierType.tip": "请选择短信通道", "systemManagement.system.sms.supplierType.hint": "AccessKeyID不可为空", "systemManagement.system.sms.accessKeySecret.hint": "AccessSecret不可为空", "systemManagement.system.sms.signName": "短信签名名称", "systemManagement.system.sms.signName.tip": "短信签名名称不可为空", "systemManagement.system.sms.templateCode": "短信模板ID", "systemManagement.system.sms.templateCode.tip": "短信模板ID不可为空", "systemManagement.system.sms.smsAppId.tip": "AppId不可为空", "systemManagement.system.sms.channelId": "签名通道号", "systemManagement.system.sms.channelId.hint": "签名通道号不可为空", "systemManagement.system.sms.param": "参数", "systemManagement.system.sms.param.state": "签名通道号不可为空", "systemManagement.system.sms.alarmConfig.tip": "请输入内容{{val}}", "systemManagement.system.sms.content": "消息内容", "systemManagement.system.sms.content.hint": "消息内容不可为空", "systemManagement.system.sms.content.extra": "当前告警类型支持的参数", "systemManagement.system.sms.baseUrl": "请求地址（url）", "systemManagement.system.sms.baseUrl.hint": "请求地址不可为空", "systemManagement.system.sms.method": "请求类型（method）", "systemManagement.system.sms.method.hint": "请选择", "systemManagement.system.sms.urlParams": "API参数", "systemManagement.system.sms.body": "body参数", "systemManagement.system.sms.headers": "HTTP头部", "systemManagement.system.sms.phone": "接收人手机号码", "systemManagement.system.sms.phone.hint": "请输入手机号码！", "systemManagement.system.sms.testPhone": "发送测试短信", "systemManagement.system.sms.templateName": "短信通道模板名称", "systemManagement.system.sms.createTemplate": "新建短信通道模板", "systemManagement.system.sms.editTemplate": "编辑短信通道模板", "systemManagement.system.sms.copyTemplate": "复制短信通道模板", "systemManagement.system.sms.smsTemplate.step1": "填写短信通道基本信息", "systemManagement.system.sms.smsTemplate.step2": "验证通道是否可用", "systemManagement.system.sms.smsTemplate.step3": "完成", "systemManagement.system.sms.smsTemplate.back": "返回告警配置", "systemManagement.system.sms.smsTemplate.backTitle": "{{second}}s后返回告警配置", "systemManagement.system.sms.smsTemplate.backSuccess": "提交成功", "systemManagement.system.sso.ad": "AD域配置", "systemManagement.system.sso.openLdap": "OpenLdap配置", "systemManagement.system.oauth.openLdap": "CAS配置", "systemManagement.system.oauth.oauth": "Oauth2.0配置", "systemManagement.system.radius": "Radius配置", "systemManagement.system.openLdap.hint1": "请输入登录地址", "systemManagement.system.openLdap.hint2": "OpenLdap登录地址必须以ldap(s)://或者LDAP(s)://开头", "systemManagement.system.openLdap.openLdapSwitch": "是否开启", "systemManagement.system.openLdap.openLdapAdminName": "管理员账号", "systemManagement.system.openLdap.adminSecret": "管理员密码", "systemManagement.system.openLdap.openLdapUrl": "登录地址", "systemManagement.system.openLdap.searchScope": "搜索范围", "systemManagement.system.openLdap.searchFilter": "用户登录属性", "systemManagement.system.openLdap.customFormItem.hint": "请输入自定义属性", "systemManagement.system.ad.url.hint": "请输入 URL", "systemManagement.system.ad.url.hint2": "必须以 ldap:// 或者 LDAP:// 开头", "systemManagement.system.ad.suffix.hint": "必须以 @ 开头", "systemManagement.system.ad.suffix.hint2": "字符长度不能超过 30", "systemManagement.system.ad.adDirectLdapAddress": "AD 域地址", "systemManagement.system.ad.openLdapUrl": "登录地址", "systemManagement.system.ad.searchScope": "搜索范围", "systemManagement.system.ad.adDirectSearchBase": "搜索目录", "systemManagement.system.ad.adDirectSearchBase.hint": "请输入搜索目录", "systemManagement.system.ad.adDirectSearchBase.tip": "OU=测试组,DC=liu,DC=com", "systemManagement.system.ad.adDirectUsernameSuffix": "登录用户名称后缀", "systemManagement.system.ad.isCreateAccount": "是否自动创建同名账号", "systemManagement.system.cas.url.hint": "必须以 http:// 或者 https:// 开头", "systemManagement.system.cas.casServerValidateUrl": "验证地址", "systemManagement.system.cas.casServerLogoutUrl": "登出地址", "systemManagement.system.cas.casCQServerName": "服务部署地址", "systemManagement.system.cas.casUserNameSuffix": "登录用户名称后缀", "systemManagement.system.oauth.oauth2IsStandard": "版本类型", "systemManagement.system.oauth.oauth2IsStandard.tip": "标准版：认证中心为官方标准接口，逻辑已在Java中实现，客户版:因客户认证中心有自定义开发，需要使用python实现授权认证流程，需要定制使用Python获取用户信息", "systemManagement.system.oauth.oauth2IsStandard.coem": "标准版", "systemManagement.system.oauth.oauth2IsStandard.client": "客户版", "systemManagement.system.oauth.userAuthorizationUrl": "认证中心authorize地址", "systemManagement.system.oauth.accessTokenUrl": "认证中心token地址", "systemManagement.system.oauth.oauth2CQServerUrl": "服务部署地址", "systemManagement.system.oauth.clientId": "认证中心授权客户端ID", "systemManagement.system.oauth.clientSecret": "认证中心授权客户端密钥", "systemManagement.system.oauth.resourceUserInfoUrl": "用户信息openAPI地址", "systemManagement.system.oauth.resourceUserInfoParma": "用户信息openAPI字段名", "systemManagement.system.oauth.logoutUrl": "登出地址", "systemManagement.system.raduis.logoutUrl": "Radius服务器IP", "systemManagement.system.raduis.logoutUrl.tip": "请输入Radius服务器IP", "systemManagement.system.raduis.radiusPort": "Radius服务器端口", "systemManagement.system.raduis.radiusPort.tip": "请输入Radius服务器端口", "systemManagement.system.raduis.radiusPort.hint": "请输入正确端口号", "systemManagement.system.raduis.sharedSecret": "共享密钥", "systemManagement.system.raduis.sharedSecret.tip": "请输入共享密钥", "systemManagement.system.other.sdtMenu": "右键菜单", "systemManagement.system.other.resultSetOperation": "结果集操作", "systemManagement.system.other.exportFunction": "导出功能", "systemManagement.system.other.forceOtpBing": "请先关闭强制短信认证登录，再关闭强制手机绑定！", "systemManagement.system.other.forcePhoneBing": "请先关闭强制OTP认证登录，再关闭强制OTP绑定！", "systemManagement.system.other.flag.true": "全局设置", "systemManagement.system.other.flag.false": "用户自定义", "systemManagement.system.other.smsLogin": "短信认证", "systemManagement.system.other.otpLogin": "OTP认证", "systemManagement.system.other.loginSetting.tip": "确认设置所有用户登录方式为{{val}}登录吗？", "systemManagement.system.other.loginSetting.content": "设置后，平台中所有用户的登录设置将变为“{{val}}”", "systemManagement.system.other.loginTime": "登录保持时间", "systemManagement.system.other.loginTime.tip": "请输入登录保持时间", "systemManagement.system.other.loginTime.hint": "请输入1-180之间的数字", "systemManagement.system.other.loginIpImpose.tip": "最多只能输入10个用户ip", "systemManagement.system.other.debugUrl.hint": "请输入公网地址或内网地址", "systemManagement.system.other.otp.hint1": "请确保已开启强制OTP绑定", "systemManagement.system.other.otp.hint2": "请确保已开启强制手机绑定", "systemManagement.system.other.subTitle": "基础设置", "systemManagement.system.other.importNumber": "导入任务数限制", "systemManagement.system.other.importNumber.hint": "请输入1-30之间数值", "systemManagement.system.other.importNumber.tip": "请输入数值", "systemManagement.system.other.exportNumber": "导出任务数限制", "systemManagement.system.other.batchExec": "批量执行任务数限制", "systemManagement.system.other.readSize": "文件读取限制（MB）", "systemManagement.system.other.readSize.hint": "请输入1-100之间的数值", "systemManagement.system.other.uploadSize": "文件上传限制（MB）", "systemManagement.system.other.limit": "导出文件下载限制（次）", "systemManagement.system.other.limit.extra": "0为不允许下载", "systemManagement.system.other.limit.hint": "请输入0-10000之间的数值", "systemManagement.system.other.limit.tip": "请输入下载限制次数", "systemManagement.system.other.accountLockPeriod": "账号锁定期限（天）", "systemManagement.system.other.accountLockPeriod.extra": "在设定的期限内未登录的账号将被锁定", "systemManagement.system.other.days": "数据备份文件有效期设置", "systemManagement.system.other.days.extra": "自动销毁超出有效期外的数据保护文件", "systemManagement.system.other.days.tip": "请选择数据备份文件有效期", "systemManagement.system.other.basic.forceOtpBing": "强制OTP绑定", "systemManagement.system.other.basic.forceOtpBing.extra": "开启后将强制系统中所有用户绑定OTP", "systemManagement.system.other.basic.forcePhoneBing": "强制手机绑定", "systemManagement.system.other.basic.forcePhoneBing.tip": "开启的前提是短信网关设置中有短信通道可用，开启后将强制系统中所有用户绑定手机", "systemManagement.system.other.multipleDeviceLogin": "多设备登录", "systemManagement.system.other.multipleDeviceLogin.tip": "开启后同一账号在同时间段内允许多设备登录", "systemManagement.system.other.setting": "访问申请页面展示所有资源", "systemManagement.system.other.setting.tip": "关闭时访问申请页面仅通过输入IP展示匹配资源", "systemManagement.system.other.resultEnc": "结果集导出加密", "systemManagement.system.other.resultEnc.tip": "开启后结果集中导出PDF和EXCEL格式的文件将加密", "systemManagement.system.other.loginSetting": "系统用户登录设置", "systemManagement.system.other.globalEffectTime": "双因素认证生效时间", "systemManagement.system.other.loginRetentionTimeType": "系统用户登录保持时间设置", "systemManagement.system.other.sysFilterLib": "系统过滤库", "systemManagement.system.other.filterResources.tip": "请选择数据源", "systemManagement.system.other.debugUrl": "调试地址", "systemManagement.system.other.publicDebugUrl": "公网地址", "systemManagement.system.other.privateDebugUrl": "内网地址", "systemManagement.system.other.toolPermission": "工具权限设置", "systemManagement.system.other.resultSetTipOption": "无权限提示入口设置", "systemManagement.system.other.resultSetTipOption.extra": "选择的入口将在无权限操作时展示", "systemManagement.system.other.resultSetTipOption.tip": "请选择无权限提示入口", "systemManagement.system.other.cascadeRemoval.label": "级联移除设置", "systemManagement.system.other.connectionFailedCount": "连接失败冻结次数", "systemManagement.system.other.connectionFailedCount.extra": "0是不限制", "systemManagement.system.other.connectionFailedCount.tip": "请输入冻结次数", "systemManagement.system.ps.maxDay": "用户密码有效期(天)", "systemManagement.system.ps.maxDay.extra": "修改此配置之前，建议所有用户重置一次密码(天)", "systemManagement.system.ps.maxDay.tip": "修改 '配置用户密码有效期(天)'建议重置一次密码，如已重置，请忽略！", "systemManagement.system.ps.passwordMin": "长度下限", "systemManagement.system.ps.passwordMin.hint": "请输入大于0的数值，并且该数值大于下限值", "systemManagement.system.ps.passwordMin.hint2": "请输入下限-20之间的整数", "systemManagement.system.ps.passwordMax": "长度上限", "systemManagement.system.ps.min": "下限", "systemManagement.system.ps.pwdInclude": "密码中需包含", "systemManagement.system.ps.maxDay.hint": "请输入1及以上的有效数值", "systemManagement.system.ps.maxDay.tip2": "请输入密码有效期", "systemManagement.system.ps.noRepeatCount": "用户密码重复周期(次)", "systemManagement.system.ps.noRepeatCount.extra": "设定周期内不允许重复修改为相同密码", "systemManagement.system.ps.noRepeatCount.hint": "请输入1-10有效数值", "systemManagement.system.ps.noRepeatCount.tip": "请输入用户密码重复周期次数", "systemManagement.system.ps.expireWarning": "是否开启密码到期提示", "systemManagement.system.ps.expireWarning.extra": "开关关闭则不提示密码到期消息", "systemManagement.system.ps.expireWarningDay": "密码到期前提示消息(天)", "systemManagement.system.ps.expireWarningDay.tip": "请输入密码到期提示天数", "systemManagement.system.ps.expireWarningDay.hint": "请输入1-15有效数值", "systemManagement.system.ps.maxFailureCount": "锁定账号输入次数", "systemManagement.system.ps.maxFailureCount.hint": "请输入1及以上的有效数值", "systemManagement.system.ps.maxFailureCount.tip": "请输入锁定账号输入次数", "systemManagement.system.ps.lockoutMinute": "账号锁定时长(分钟)", "systemManagement.system.ps.lockoutMinute.hint": "请输入1-999999有效数值", "systemManagement.system.ps.lockoutMinute.tip": "请输入账号锁定时长", "systemManagement.system.ps.userPwdStrong": "用户密码强度", "systemManagement.system.ps.userPwdStrong.tip2": "系统内置密码强度", "systemManagement.system.ps.userPwdStrong.tip": "密码长度9-16位，需包含数字、大写字母、小写字母、特殊字符", "systemManagement.system.ps.defaultPassword": "新增用户密码", "systemManagement.system.parsing.baseline": "宽松拦截模式", "systemManagement.system.parsing.baseline.tip": "选择数据源", "systemManagement.system.parsing.export.tip": "任务 解析失败导出", "systemManagement.system.parsing.export.tip2": "已经执行完成，文件生成成功", "systemManagement.system.parsing.search": "搜索SQL语句", "systemManagement.system.parsing.fall": "解析失败语句", "systemManagement.system.parsing.sql": "SQL语句", "systemManagement.system.parsing.table.sql": "解析失败语句", "systemManagement.system.parsing.table.connectionName": "连接名称", "systemManagement.system.parsing.table.databaseType": "数据源类型", "systemManagement.system.parsing.table.resultStatus": "执行状态", "systemManagement.system.parsing.table.parsingFailedMessage": "失败原因", "systemManagement.system.parsing.table.executeType": "操作类型", "systemManagement.system.parsing.table.userId": "操作用户", "systemManagement.system.parsing.table.startTime": "执行时间", "systemManagement.system.auth.deleteLicense.success": "删除证书成功", "systemManagement.system.auth.deleteLicense.error": "删除证书失败", "systemManagement.system.auth.updateLicense.error": "证书更新失败", "systemManagement.system.auth.updateLicense.loading": "更新中", "systemManagement.system.auth.license.uninstalled": "未安装许可证服务,无法下载", "systemManagement.system.auth.fileType.tip": "请选择${{lisenceFileType}}类型文件", "systemManagement.system.auth.startTime": "授权开始时间", "systemManagement.system.auth.endTime": "授权结束时间", "systemManagement.system.auth.databaseNumber": "数据库纳管数量限制", "systemManagement.system.auth.connectionNumber": "已纳管数据库数量", "systemManagement.system.auth.databaseType": "支持数据库类型", "systemManagement.system.auth.productGrade": "产品型号", "systemManagement.system.auth.licenseDownload": "证书下载", "systemManagement.system.auth.licenseDownload.extra": "确保许可证服务已安装", "systemManagement.system.auth.updateLicense": "证书更新", "systemManagement.system.auth.newLicense": "上传新证书", "systemManagement.system.auth.updates": "一键更新", "systemManagement.system.auth.notUpdate": "未上传新证书", "systemManagement.system.auth.toBeUpdated": "待更新", "systemManagement.system.auth.updatedSuccess.tip": "License许可更新已完成！", "systemManagement.system.watermark.updated": "水印效果保存成功", "systemManagement.system.watermark.result": "水印效果：", "systemManagement.system.watermark.color": "水印颜色：", "systemManagement.system.watermark.tip": "水印内容过长，字号设置可能不会生效", "systemManagement.system.watermark.fontSize": "水印字号：", "systemManagement.system.watermark.spacing": "水印间距：", "systemManagement.system.watermark.crosswise": "横向：", "systemManagement.system.watermark.vertical": "纵向：", "systemManagement.system.watermark.watermarkName": "系统水印设置", "systemManagement.system.watermark.watermarkName.tip": "请输入$快速配置", "systemManagement.system.watermark.pdfWatermarkName": "PDF水印设置", "systemManagement.system.watermark.pdfWatermarkName.tip": "支持最多选择三项参数", "systemManagement.system.watermark.pdfWatermarkName.hint": "最多选择三项参数", "systemManagement.system.watermark.excelWatermarkName": "EXCEL水印设置", "systemManagement.system.watermark.preview": "预览：", "systemManagement.system.data.upload.tip": "只支持JSON格式的文件", "systemManagement.system.data.upload.success": "上传文件成功！", "systemManagement.system.data.tip": "可将其他机器导出的数据进行上传，迁移其数据到本机器当中", "systemManagement.system.data.upload.tip2": "导入模式为增量导入，不会覆盖原有数据，请保证当前机器无历史数据，避免导入的数据不完整", "systemManagement.system.data.log": "日志：", "systemManagement.system.data.clear": "清空日志", "systemManagement.system.data.download": "下载日志", "systemManagement.system.access.export.tip": "任务 选中访问规则导出", "systemManagement.system.access.delete.tip": "该ip正在活跃，确定删除吗？", "systemManagement.system.access.table.ipAddr": "来源", "systemManagement.system.access.table.type": "访问策略", "systemManagement.system.access.table.type1": "白名单策略", "systemManagement.system.access.table.type2": "黑名单策略", "systemManagement.system.access.table.userIds": "绑定用户", "systemManagement.system.access.batchExport.tip": "确定导出当前选中访问规则？", "systemManagement.system.access.batchDelete.tip": "确定删除当前选中访问规则？", "systemManagement.system.access.batchExport": "批量导出访问规则", "systemManagement.system.access.batchDelete": "批量删除访问规则", "systemManagement.system.access.search": "搜索来源", "systemManagement.system.access.exportRule": "导入访问规则", "systemManagement.system.access.addRule": "新增访问规则", "systemManagement.system.access.add.tip": "该ip正在使用CloudQuery，确定要执行该操作吗？", "systemManagement.system.access.add.title": "新增访问规则", "systemManagement.system.access.edit.title": "编辑访问规则", "systemManagement.system.access.add.ipAddr": "来源", "systemManagement.system.access.add.ipAddr.extra": "来源支持以下格式，单个IP：***********  CIDR:***********/25", "systemManagement.system.access.add.ipAddr.plac": "输入不能为空", "systemManagement.system.access.add.ipAddr.hint": "输入不合法，请重新检查", "systemManagement.system.access.add.type": "访问策略", "systemManagement.system.access.add.userIds.extra": "选择用户绑定后, 此用户只能在此IP下登录成功", "systemManagement.system.access.add.userIds": "绑定用户", "systemManagement.system.access.batchImport": "批量导入访问规则", "systemManagement.system.alarm.sysTemAlarmConfig": "系统告警配置", "systemManagement.system.alarm.businessAlarmConfig": "业务告警配置", "systemManagement.system.alarm.sysTemAlarmConfig.subTitle": "系统告警类型", "systemManagement.system.alarm.businessAlarmConfig.subTitle": "业务告警类型", "systemManagement.system.alarm.condition": "告警条件", "systemManagement.system.alarm.methodDetail": "告警方式", "systemManagement.system.alarm.targetPerson": "消息接收人类型", "systemManagement.system.alarm.userNameWithIds": "消息接收人", "systemManagement.system.alarm.positiveNum": "阈值(s)", "systemManagement.system.alarm.positiveNumMB": "阈值(MB)", "systemManagement.system.alarm.positiveNum.plac": "请输入阈值", "systemManagement.system.alarm.positiveNum.hint": "阈值不能小于1", "systemManagement.system.alarm.positiveNum.hint2": "阈值不能大于20", "systemManagement.system.alarm.checkLevel": "审核等级", "systemManagement.system.alarm.edit.SysTemAlarmConfig": "编辑系统告警配置", "systemManagement.system.alarm.edit.usinessAlarmConfig": "编辑业务告警配置", "systemManagement.system.alarm.alarmMethods": "告警方式", "systemManagement.system.alarm.alarmMethods.plac": "请选择告警方式", "systemManagement.system.log.syslog": "syslog方式暴露", "systemManagement.system.log.kafka": "kafka方式暴露", "systemManagement.system.log.host": "syslog服务器IP地址", "systemManagement.system.log.host.plac": "请输入syslog服务器IP地址", "systemManagement.system.log.port": "syslog服务器端口号", "systemManagement.system.log.port.plac": "请输入syslog服务器端口号", "systemManagement.system.log.protocol": "syslog服务器协议", "systemManagement.system.log.protocol.plac": "请选择syslog服务器协议", "systemManagement.system.log.level": "日志级别", "systemManagement.system.log.level.plac": "请选择日志级别", "systemManagement.system.log.kafka.host": "kafka服务器地址", "systemManagement.system.log.kafka.host.plac": "请输入kafka服务器地址", "systemManagement.system.log.kafka.topic": "kafka主题名称", "systemManagement.system.log.kafka.topic.plac": "请输入kafka主题名称", "systemManagement.system.log.kafka.clientId": "kafka客户端ID", "systemManagement.system.log.kafka.clientId.plac": "请输入kafka客户端ID", "systemManagement.basic.validPeriod": {"oneMonth": "一个月", "threeMonth": "三个月", "halfYear": "半年", "year": "一年", "forever": "永久"}, "systemManagement.system.targetPerson.PERSONAL": "个人", "systemManagement.system.targetPerson.DEPT": "部门负责人", "systemManagement.system.targetPerson.CONN": "连接管理员", "systemManagement.system.targetPerson.CPU_USAGE": "CPU使用率", "systemManagement.system.targetPerson.MEMORY_USAGE": "内存使用率", "systemManagement.system.targetPerson.UPLOAD_FILE_SIZE": "文件上传大小", "systemManagement.system.targetPerson.IMPORT_TASK_COUNT": "导入任务数", "systemManagement.system.targetPerson.EXPORT_TASK_COUNT": "导出任务数", "systemManagement.system.targetPerson.LICENSE_REMAINING": "license剩余天数", "systemManagement.system.targetPerson.OVER_PERMISSION": "越权操作", "systemManagement.system.targetPerson.HIGH_RISK": "高危操作", "systemManagement.system.targetPerson.SLOW_SQL": "慢SQL", "systemManagement.system.targetPerson.BATCH_EXECUTE": "批量执行", "systemManagement.system.targetPerson.SQL_CHECK": "SQL审核", "systemManagement.system.targetPerson.PROBLEM_CONNECTION": "问题连接", "systemManagement.system.targetPerson.SYSTEM_ERROR": "系统异常", "systemManagement.personManagement.editCompanyName": "修改公司名称", "systemManagement.personManagement.companyName": "公司名称", "systemManagement.personManagement.companyName.plac": "请输入公司名称", "systemManagement.personManagement.addDept.success": "部门添加成功", "systemManagement.personManagement.addDept.subordinateCompany": "所属公司", "systemManagement.personManagement.deptName": "部门名称", "systemManagement.personManagement.deptName.plac": "请输入部门名称", "systemManagement.personManagement.deptName.hint": "部门名称须在2-20个字之间", "systemManagement.personManagement.deptName.hint2": "只能由中文、数字、字母组成", "systemManagement.personManagement.deptType": "部门类型", "systemManagement.personManagement.outsideParties": "外部部门", "systemManagement.personManagement.editDeptName": "修改部门名称", "systemManagement.personManagement.editPartnerName": "修改合作方名称", "systemManagement.personManagement.partnerName": "合作方名称", "systemManagement.personManagement.name.hint": "不能超过100个字符", "systemManagement.personManagement.addGroup.success": "组添加成功", "systemManagement.personManagement.groupName": "组名称", "systemManagement.personManagement.groupName.plac": "请输入组名称", "systemManagement.personManagement.groupName.hint": "组名称须在2-100个字之间", "systemManagement.personManagement.desc": "描述和备注", "systemManagement.personManagement.editGroupHead": "修改部门负责人", "systemManagement.personManagement.editPartnerHead": "修改合作方负责人", "systemManagement.personManagement.principal": "负责人", "systemManagement.personManagement.principal.plac": "请输入负责人", "systemManagement.personManagement.continue.sync.error": "同步数据字典任务遇到错误是否继续", "systemManagement.system.other.sysFunctionSetting": "系统功能限制设置", "systemManagement.system.other.sysLoginSetting": "系统登录设置", "systemManagement.system.other.sysFunctionModuleSetting": "系统功能模块设置", "systemManagement.system.other.OtherSetting": "其他设置", "systemManagement.system.alarm.silentCycle.cupTip": "系统每隔一个周期检查一次CPU使用率，若满足触发条件，则触发一次告警", "systemManagement.system.alarm.silentCycle.memoryTip": "系统每隔一个周期检查一次内存使用率，若满足触发条件，则触发一次告警", "systemManagement.system.alarm.silentCycle.sysErrorTip": "系统每隔一个周期检查一次系统，若满足触发条件，则触发一次告警", "systemManagement.system.alarm.trigger.cpuTip": "CPU使用率>阈值，且持续一段时间", "systemManagement.system.alarm.trigger.memoryTip": "内存使用率>阈值，且持续一段时间", "systemManagement.system.alarm.trigger.OtherTip": "触发即告警和累计触发，触发即告警指一触发即告警；累计触发即xx时间段内累计触发xx次即告警", "systemManagement.system.alarm.trigger": "告警触发条件", "systemManagement.system.alarm.duration": "持续时间(min)", "systemManagement.system.alarm.duration.tip": "请输入1-720之间的整数", "systemManagement.system.alarm.interval": "静默周期(min)", "systemManagement.system.alarm.policyType": "触发条件", "systemManagement.system.alarm.policyType.value0": "触发即告警", "systemManagement.system.alarm.policyType.value1": "累计触发", "systemManagement.system.alarm.policyType.label": "累计触发策略", "systemManagement.system.alarm.policyType.label.extra0": "在", "systemManagement.system.alarm.policyType.label.extra1": "min内累计触发", "systemManagement.system.alarm.policyType.label.extra2": "次即告警", "systemManagement.system.alarm.policyType.interval": "请输入1-720之间的整数作为触发时间段", "systemManagement.system.alarm.policyType.count": "请输入1-720之间的整数作为累计触发次数", "systemManagement.system.alarm.value%": "阈值(%)", "systemManagement.system.alarm.valueDay": "阈值(天)", "systemManagement.system.alarm.valueItem": "阈值(个)", "systemManagement.system.other.autoDownload": "导出文件时是否自动下载", "systemManagement.system.targetPerson.FILE_EXPORT_SIZE": "文件导出大小", "systemManagement.system.alarm.processMode.label": "处理方式", "systemManagement.system.alarm.processMode.value1": "中止任务并告警", "systemManagement.system.alarm.processMode.value2": "仅告警", "systemManagement.system.alarm.positiveNumGB": "阈值(GB)", "systemManagement.system.ps.passwordMin.plac": "请输入1-19之间的整数", "systemManagement.system.authMenu.title": "权限相关设置", "systemManagement.system.authMenu.name.hint": "名称已存在", "systemManagement.system.authMenu.name.label": "名称", "systemManagement.system.authMenu.name.plac": "请输入名称", "systemManagement.system.authMenu.name.hint2": "只能由中文、数字、英文组成，2-24字", "systemManagement.system.authMenu.parameter.label": "参数值", "systemManagement.system.authMenu.parameter.hint": "请完善参数值", "systemManagement.system.authMenu.parameter.hint2": "只能输入大于等于1的整数", "systemManagement.system.authMenu.status.label": "状态", "systemManagement.system.authMenu.parameter.day": "天", "systemManagement.system.authMenu.parameter.hour": "小时", "systemManagement.system.authMenu.customAuthPeriod.label": "权限期限自定义", "systemManagement.system.authMenu.customAuthPeriod.up": "上移", "systemManagement.system.authMenu.customAuthPeriod.down": "下移", "systemManagement.system.authMenu.customAuthPeriod.moveSuccessful": "移动成功", "systemManagement.system.authMenu.customAuthPeriod.custom": "自定义"}