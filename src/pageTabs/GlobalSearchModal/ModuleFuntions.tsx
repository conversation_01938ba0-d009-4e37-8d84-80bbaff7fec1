import React, { useEffect, useState } from "react";
import { Row, Col, Tooltip } from "antd";
import classNames from "classnames";
import { useTranslation } from 'react-i18next';
import { Iconfont, LinkButton } from 'src/components';
import { useDispatch, useSelector } from 'src/hook';
import { setGlobalSearchHistory } from "src/appPages/login/loginSlice";
import { handleModuleJump } from './utils';
import { useHistory } from 'react-router-dom'
import styles from './index.module.scss';


const ModuleFuntions = ({
  searchValue,
  filteredRouteMenus = [],
  onClose
}: {
  searchValue?: string;
  filteredRouteMenus: any[];
  onClose: () => void;
}) => {
  const history = useHistory();
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const { globalSearchHistory = [] } = useSelector(state => state.login)

  const [activeGuideIndex, setActiveGuideIndex] = useState<null | number>(null);
  //功能 查看更多
  const [showMoreFuntions, setshowMoreFuntions] = useState(false);

  useEffect(() => {
    if (!searchValue) {
      setshowMoreFuntions(false)
    }
    return () => {
      setshowMoreFuntions(false)
    }
  }, [searchValue])


  const higlightTitle = (title: any, searchValue: string | undefined) => {

    if (!searchValue) return title;

    const parts = title.split(new RegExp(`(${searchValue})`, 'gi'));

    return (
      <span>
        {parts?.map((part: string, index: number) =>
          part === searchValue ?
            <span key={index} style={{ color: '#3357ff' }}>{part}</span>
            : part
        )}
      </span>
    );
  }

  const handleClick = (item: any) => {

    let cloneGlobalSearchHistory = globalSearchHistory.concat([]);
    if (cloneGlobalSearchHistory?.length === 4) {
      cloneGlobalSearchHistory = cloneGlobalSearchHistory.slice(1)

    }
    cloneGlobalSearchHistory.push({ title: item?.title, url: { pathname: item?.url }, isPosition: false })
    dispatch(setGlobalSearchHistory(cloneGlobalSearchHistory))

    let state = {};

    if (item?.permissionType === 'AUDIT_VIEW') {
      state = (() => {

        const splitTitle = item?.title?.split('/');
        const lastChar = splitTitle[splitTitle.length - 1]
        switch (lastChar) {
          case t('golbalSearch.function.desens.title'):
            return {
              skipDesens: 1,
            }
          case t('golbalSearch.function.high'):
            return {
              highFlag: 1,
            }
          case t('golbalSearch.function.unAuth_operation'):
            return {
              extratTypes: ["EXTRAT_FLAG"],
              overPermFlag: "1"
            }
          default:
            return {}
        }
      })()
    }
    onClose?.()
    handleModuleJump(dispatch, history, item?.url, state)
  }
  
  return (
    <div className="mb10">
      <h3 className={styles.moduleTitle}>{t('golbalSearch.function.title')}</h3>
      <Row gutter={[12, 12]}>
        {
          filteredRouteMenus?.slice(0, (showMoreFuntions || !searchValue) ? filteredRouteMenus.length : 3)?.map((item: any, index: number) => (
            <Col
              span={12}
              key={item?.title}
              className={classNames(styles.routerMenu)}
              onMouseEnter={() => setActiveGuideIndex(index)}
              onMouseLeave={() => setActiveGuideIndex(null)}
            >
              <div className={classNames(styles.menuItem, { [styles.activeGuideIndex]: index === activeGuideIndex })}
                onClick={() => {
                  handleClick(item)
                }}>
                <Iconfont type="icon-routeMenu" className={styles.icon} />
                <div className={styles.left}>
                  <Tooltip title={item?.title}>
                    <div className={styles.title}>{higlightTitle(item?.title, searchValue)}</div>
                  </Tooltip>
                  <div className={styles.url}>{item?.url}</div>
                </div>
              </div>
            </Col>
          ))
        }
        {filteredRouteMenus?.length > 3 && searchValue && (
          <Col span={24}>
            <LinkButton onClick={() => setshowMoreFuntions(!showMoreFuntions)}>
              {showMoreFuntions ? t('common.btn.packup') : t('common.btn.viewMore')}
            </LinkButton>
          </Col>
        )}
      </Row>
    </div>
  )
}

export default ModuleFuntions;