import React, { useEffect, useState } from "react";
import classNames from "classnames";
import { Row, Col, Tooltip } from "antd";
import { useHistory } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useModal, useSelector, useDispatch } from 'src/hook';
import { IGloablSearchResult } from 'src/api';
import { Iconfont, LinkButton } from 'src/components';
import { setGlobalSearchHistory } from "src/appPages/login/loginSlice";
import { setUnauthorizedOperationState } from 'src/pageTabs/audit/auditSlice';
import { setMineApplyPageState, setMineApplyDetailParams, setMineApprovePageState, setMineApproveDetailParams } from 'src/pageTabs/access-request/accessRequestSlice'
import styles from './index.module.scss';
import { setDataChangeMineApproveDetailParams, setDataChangeMineApprovePageState } from "src/pageTabs/data-change/dataChangeSlice";

/*
listRoute: 代表跳转的是列表页 根据position(number/10)来定位具体元素
detailRoute: 跳转的是详情页 详情页部分参数从列表object拿到
tabKey: 页面上tab
subTabKey: 页面tabkey 下的其他tab
menuRoute: 菜单权限标识
*/

export interface GloablSearchLocationState {
  globalSearchRecordPosition?: number;
  globalSearchTabKey?: string;
  globalSearchSubTabKey?: string;
  [key: string]: any;
}

const WorkOrderResult = ({
  data
}: {
  data?: IGloablSearchResult[];
}) => {

  const { closeModal } = useModal('ModalGlobalSearch');

  return (
    <>
      {
        data?.map((item: any, index: number) => (
          <Row gutter={[12, 12]} key={index}>
            <h3 className={styles.moduleTitle}>{item?.moduleName}</h3>
            {
              item?.list?.map((moduleItem: any, mIndex: number) => (
                <Row key={mIndex} gutter={[12, 12]}>
                  <Col span={24} key={moduleItem?.moduleName}>
                    <div className='flexAlignCenter'>
                      <Iconfont type="icon-routeMenu" className={styles.icon} />
                      <div className='ml10'>{moduleItem?.moduleName}</div>
                    </div>
                  </Col>
                  <OrderItem item={moduleItem || {}} menuName={item?.moduleName} onCloseModal={closeModal} menuPermissionType={moduleItem?.menuRoute} />
                </Row>
              ))
            }

          </Row>
        ))
      }
    </>
  )
}

export default WorkOrderResult;

const OrderItem = ({ item, menuName, menuPermissionType, onCloseModal }: { item: any; menuName: string; menuPermissionType: string; onCloseModal: () => void; }) => {
  
  const { t } = useTranslation();
  const history = useHistory();
  const dispatch = useDispatch();
  const { globalSearchHistory = [] } = useSelector(state => state.login)

  //功能 查看更多
  const [showMoreOrders, setShowMoreOrders] = useState(false);
  const [activeGuideIndex, setActiveGuideIndex] = useState<null | number>(null);

  useEffect(() => {

    return () => {
      setShowMoreOrders(false);
    }
  }, [])

  //处理跳转路由

  const getRouteParameter = (object: any, permissionType: string) => {

    switch (permissionType) {
      case 'DATA_TRANSFORM':
        return {
          parentId: object?.id,
          statusFlag: true,
          parentDetail: object
        }
      case 'DATA_CHANGE_MINE_APPLY':
        return {
          applyUserId: object?.applyUserId,
          frontStatus: object?.frontStatus
        }
      case 'DATA_CHANGE_MINE_APPROVE':
      case 'DATA_CHANGE_WORK_ORDER_MANAGEMENT':
        return {
          flowTaskId: object?.flowTaskId,
          applyUserId: object?.applyUserId,
          ...(permissionType === 'DATA_CHANGE_WORK_ORDER_MANAGEMENT' ? { orderType: 'FLOW_WORK_ORDER_MANAGEMENT' } : {})
        }
      case 'AUDIT_VIEW': //审计概览
        const defaultState: any = {
          timeRange: [],
          actuatorType: 0
        };
        const splitTitle = item?.moduleName?.split('/');
        const lastChar = splitTitle && splitTitle[splitTitle.length - 1];

        switch (lastChar) {
          case t('golbalSearch.function.desens.title'):
            return {
              ...defaultState,
              skipDesens: 1
            }
          case t('golbalSearch.function.high'):
            return {
              ...defaultState,
              highFlag: 1
            }
          case t('golbalSearch.order.operation_unAuth'):
            return {
              ...defaultState,
              overPermFlag: "1" //越权操作
            }
          case t('golbalSearch.order.sql_unAuth'):
            dispatch(setUnauthorizedOperationState({
              ...defaultState,
              extratTypes: ["EXTRAT_FLAG"], //sql越权
             }))
            return {
              ...defaultState,
              extratTypes: ["EXTRAT_FLAG"], //sql越权
            }
          case t('golbalSearch.order.statement'):
            return {
              ...defaultState,
            }
          default:
            return defaultState
        }
      case 'SUSER_AUDIT':
        return {
          userId: object?.userId
        }
      case 'CONNECTION_MANAGEMENT':
        return {
          object: {
            dataSourceType: object?.id,
            groupId: object?.id,
          }
        }
      case 'AUTH_MANAGEMENT':
        return {
          object: {
            nodePathWithType: object?.nodePathWithType,
            nodePath: object?.nodePath,
            nodeType: object?.nodeType,
            key: object?.nodePath,
            connection: object?.connection,
            sdt: {
              connectionId: object?.nodeType === 'connection' ? object?.id : object?.sdt?.connectionId,
              connectionType: object?.nodeType === 'connection' ? object?.connection?.connectionType : object?.sdt?.connectionType,
              groupName: object?.groupName
            }
          }
        }
      case 'DATA_PROTECT':
        return {
          object: {
            datasourceType: object?.id //只查到数据源类型层级
          }

        };
      case 'MINE_APPLY':
      case 'MINE_APPROVE':
      case 'FLOW_WORK_ORDER_MANAGEMENT':
        return object;
      default:
        return {}
    }
  }

  const updateSearchHistory = (title: string, url: any, isPosition: boolean = false) => {
    let cloneGlobalSearchHistory = globalSearchHistory.concat([]);
    if (cloneGlobalSearchHistory?.length === 4) {
      cloneGlobalSearchHistory = cloneGlobalSearchHistory.slice(1)

    }

    cloneGlobalSearchHistory.push({ title: title, url, isPosition: isPosition ?? false })
    dispatch(setGlobalSearchHistory(cloneGlobalSearchHistory))
  }

  const getHistoryUrl = (record: any) => {
    let pathname = `/data_change_mine_apply/${record?.dataChangeId}/detail`;
    let state = {
      ...record,
      mainUUID: record?.flowMainUUID,
      originType: 'message'
    }
    switch (record?.type) {
      case 'dataCorrection':
      case 'publishChanges':
        if (record?.applyType === "APPROVAL") {
          pathname = `/data_change_mine_approve/${record?.dataChangeId}/detail`;
          const params = {
            ...record,
            id: record?.dataChangeId,
            mainUUID: record?.flowMainUUID,
            originType: 'message',
          }
          history.push('/data_change_mine_approve')
          dispatch(setDataChangeMineApprovePageState('detail'))
          dispatch(setDataChangeMineApproveDetailParams(params))
        }
        break;
      default:
        const params = {
          ...record,
          id: record?.flowUUID,
          mainUUID: record?.flowMainUUID,
          originType: 'message',
        }
        if (record?.applyType === "APPROVAL") {
          pathname = `/mine_approve/${record?.flowUUID}/detail`;
          history.push('/mine_approve')
          dispatch(setMineApprovePageState('detail'))
          dispatch(setMineApproveDetailParams(params))
        } else {
          pathname = `/mine_apply/${record?.flowUUID}/detail`;
          history.push('/mine_apply')
          dispatch(setMineApplyPageState('detail'))
          dispatch(setMineApplyDetailParams(params))
        }
        break;
    }
    return {
      pathname,
      state
    }
  }
  const onItemClick = (order: any) => {
    const { tab: tabKey, subTab: subTabKey } = item;
    const { object = {}, position, sdtObject = {} } = order;
    // 获取菜单权限标识
    const lastIndex = menuPermissionType?.lastIndexOf('/');
    const permissionType = menuPermissionType?.substring(lastIndex + 1);

    let newState = getRouteParameter({ ...object, ...sdtObject }, permissionType);
    if (item?.moduleName?.includes(t('golbalSearch.order.folder'))) {
      newState = { ...sdtObject, selectedPath: object?.path };
    }

    //消息通知详情由前端控制路由
    if ((menuName === t('golbalSearch.function.mes-management.title') && object?.type !== 'other')) {
      const historyParams = getHistoryUrl(object);
      updateSearchHistory(order?.content, historyParams);
    } else if (order?.listRoute && (position || tabKey)) {
      const urlInfo: any = {
        pathname: `/${order?.listRoute}`,
        state: {
          globalSearchRecordPosition: position,
          globalSearchTabKey: tabKey,
          globalSearchSubTabKey: subTabKey,
          ...newState
        }
      }
      history.push(urlInfo);
      updateSearchHistory(order?.content, urlInfo, true);
    } else if (order?.detailRoute && object) {
      history.push({
        pathname: `/${order?.detailRoute}`,
        state: newState
      });
      updateSearchHistory(order?.content, {
        pathname: `/${order?.detailRoute}`,
        state: newState
      });
    }
    onCloseModal();
  }

  return (
    <>
      {
        item?.list?.slice(0, showMoreOrders ? item.list.length : 2)?.map((order: any, index: number) => (
          <Col
            span={24}
            key={item?.content}
            className={classNames(styles.orderItemCol, { [styles.activeGuideIndex]: index === activeGuideIndex })}
            onMouseEnter={() => setActiveGuideIndex(index)}
            onMouseLeave={() => setActiveGuideIndex(null)}
          >
            <Tooltip title={<div dangerouslySetInnerHTML={{ __html: order?.content }}></div>}>
              <div className={classNames(styles.orderItem,)}
                onClick={() => {
                  onItemClick(order)
                }} dangerouslySetInnerHTML={{ __html: order?.content }}
              >
              </div>
            </Tooltip>
          </Col>
        ))
      }
      {item?.list?.length > 2 && (
        <Col span={24}>
          <LinkButton className={styles.linkBtnStyle} onClick={() => setShowMoreOrders(!showMoreOrders)}>
            {showMoreOrders ?  t('common.btn.packup') : t('common.btn.viewMore')}
          </LinkButton>
        </Col>

      )}
    </>
  )
}