/**
 permissionType 表示当前所在菜单权限控制字段

*/
export const ALL_PAGES_MAPPING = (t: (key: string) => string) => {

  return {
    SYSTEM_DATA_OPERATE: [
      { title: t('golbalSearch.function.systemDataOperate.title'), url: '/system_data_operate', keywords: t('golbalSearch.function.systemDataOperate.keyword') },
    ],
    DATABASE_MANAGEMENT: [
      { permissionType: "CONNECTION_MANAGEMENT", title: t('golbalSearch.function.connectionManagement.title'), url: '/connection_management', keywords: t('golbalSearch.function.connectionManagement.keyword') },
      { permissionType: "AUTH_MANAGEMENT", title: t('golbalSearch.function.authManagement.title'), url: '/subject-authorization', keywords: t('golbalSearch.function.authManagement.keyword') },
      { permissionType: "AUTH_MANAGEMENT", title: t('golbalSearch.function.automaticAuthorization.title'), url: '/auth_management', keywords: t('golbalSearch.function.automaticAuthorization.keyword') },
      { permissionType: "AUTOMATIC_AUTHORIZATION", title: t('golbalSearch.function.automatic_authorization.title'), url: '/automatic_authorization', keywords: t('golbalSearch.function.automatic_authorization.keyword') },
      { permissionType: "HIERARCHICAL_AUTH_MANAGEMENT", title: t('golbalSearch.function.hierarchical_auth_management.title'), url: '/hierarchical_auth_management', keywords: t('golbalSearch.function.hierarchical_auth_management.keyword') },
      { permissionType: "RULE_MANAGEMENT", title: t('golbalSearch.function.rule_management.title'), url: '/rule_management', keywords: t('golbalSearch.function.rule_management.keyword') },
      { permissionType: 'DATA_PROTECT', title: t('golbalSearch.function.data_protect.title'), url: '/data_protect', keywords: t('golbalSearch.function.data_protect.keyword') },
      { permissionType: "CLIENT_MANAGER", title: t('golbalSearch.function.client_config.title'), url: '/client_config', keywords: t('golbalSearch.function.client_config.keyword') },
      { permissionType: "CLIENT_MANAGER", title: t('golbalSearch.function.client_manager_records.title'), url: '/client_manager_records', keywords: t('golbalSearch.function.client_manager_records.keyword') },
    ],
    FLOW_APPLY: [
      { permissionType: "MINE_APPLY", title: t('golbalSearch.function.mine_apply.title'), url: '/mine_apply', keywords: t('golbalSearch.function.mine_apply.keyword') },
      { permissionType: "MINE_APPLY", title: t('golbalSearch.function.mine_apply_search.title'), url: '/mine_apply/search', keywords: t('golbalSearch.function.mine_apply_search.keyword') },
      { permissionType: "MINE_APPLY", title: t('golbalSearch.function.mine_apply_application.title'), url: '/mine_apply/application', keywords: t('golbalSearch.function.mine_apply_application.keyword') },
      { permissionType: "MINE_APPROVE", title: t('golbalSearch.function.mine_approve.title'), url: '/mine_approve', keywords: t('golbalSearch.function.mine_apply_application.keyword') },
      { permissionType: "FLOW_WORK_ORDER_MANAGEMENT", title: t('golbalSearch.function.flow_work_order_management.title'), url: '/flow_work_order_management', keywords: t('golbalSearch.function.flow_work_order_management.keyword') },
      { permissionType: "FLOW_DESIGN", title: t('golbalSearch.function.flow_design.title'), url: '/flow_design', keywords: t('golbalSearch.function.flow_design.keyword') },
    ],
    DATA_TRANSFORM: [
      { permissionType: "DATA_TRANSFORM", title: t('golbalSearch.function.data_transform.title'), url: '/data_transform', keywords: t('golbalSearch.function.data_transform.keyword') },
      { permissionType: "DATA_TRANSFORM", title: t('golbalSearch.function.create-transfer-task.title'), url: '/create-transfer-task', keywords: t('golbalSearch.function.create-transfer-task.keyword') },
    ],
    DATA_CHANGE: [
      { permissionType: "DATA_CHANGE_MINE_APPLY", title: t('golbalSearch.function.data_change_mine_apply.title'), url: '/data_change_mine_apply', keywords: t('golbalSearch.function.data_change_mine_apply.keyword') },
      { permissionType: "DATA_CHANGE_MINE_APPLY", title: t('golbalSearch.function.data_change_mine_apply_apply.title'), url: '/data_change_mine_apply/addOrEdit', keywords: t('golbalSearch.function.data_change_mine_apply_apply.keyword') },
      { permissionType: "DATA_CHANGE_MINE_APPROVE", title: t('golbalSearch.function.data_change_mine_approve.title'), url: '/data_change_mine_approve', keywords: t('golbalSearch.function.data_change_mine_approve.keyword') },
      { permissionType: "DATA_CHANGE_WORK_ORDER_MANAGEMENT", title: t('golbalSearch.function.data_change_work_order_management.title'), url: '/data_change_work_order_management', keywords: t('golbalSearch.function.data_change_work_order_management.keyword') },
      { permissionType: "DATA_CHANGE_FLOW_DESIGN", title: t('golbalSearch.function.data_change_flow_design.title'), url: '/data_change_flow_design', keywords: t('golbalSearch.function.data_change_flow_design.keyword') },
    ],
    AUDIT_ANALYZE: [
      { permissionType: "AUDIT_VIEW", title: t('golbalSearch.function.audit_view.title'), url: '/audit_view', keywords: t('golbalSearch.function.audit_view.keyword') },
      { permissionType: "AUDIT_VIEW", title: t('golbalSearch.function.statement_detail.title'), url: '/statement_detail', keywords: t('golbalSearch.function.statement_detail.keyword') },
      { permissionType: "AUDIT_VIEW", title: t('golbalSearch.function.operate_record.title'), url: '/operate_record', keywords: t('golbalSearch.function.operate_record.keyword') },
      { permissionType: "AUDIT_VIEW", title: t('golbalSearch.function.statement_detail1.title'), url: '/statement_detail', keywords: t('golbalSearch.function.statement_detail1.keyword') },
      { permissionType: "AUDIT_VIEW", title: t('golbalSearch.function.statement_detail2.title'), url: '/statement_detail', keywords: t('golbalSearch.function.statement_detail2.keyword') },
      { permissionType: "AUDIT_VIEW", title: t('golbalSearch.function.operate_unauthorized.title'), url: '/operate_unauthorized', keywords: t('golbalSearch.function.operate_unauthorized.keyword') },
      { permissionType: "AUDIT_VIEW", title: t('golbalSearch.function.custom_audit_metrics.title'), url: '/custom_audit_metrics', keywords: t('golbalSearch.function.custom_audit_metrics.keyword') },
      { permissionType: "OBJECT_AUDIT", title: t('golbalSearch.function.object_audit.title'), url: '/object_audit', keywords: t('golbalSearch.function.object_audit.keyword') },
      { permissionType: "SUSER_AUDIT", title: t('golbalSearch.function.suser_audit.title'), url: '/suser_audit', keywords: t('golbalSearch.function.suser_audit.keyword') },
      { permissionType: "PERMISSION_DASHBOARD", title: t('golbalSearch.function.permission_dashboard.title'), url: '/permission_dashboard', keywords: t('golbalSearch.function.permission_dashboard.keyword') },
    ],
    SYSTEM_MANAGEMENT: [
      { permissionType: "USER_MANAGEMENT", title: t('golbalSearch.function.person_management.title'), url: '/person_management', keywords: t('golbalSearch.function.person_management.keyword') },
      { permissionType: "ROLE_MANAGEMENT", title: t('golbalSearch.function.role_management.title'), url: '/role_management', keywords: t('golbalSearch.function.role_management.keyword') },
      { permissionType: "SYSTEM_SETTINGS", title: t('golbalSearch.function.system_settings.title'), url: '/system_settings', keywords: t('golbalSearch.function.system_settings.keyword') },
      { permissionType: "SYSTEM_MONITOR", title: t('golbalSearch.function.container_monitor.title'), url: '/container_monitor', keywords: t('golbalSearch.function.container_monitor.keyword') },
      { permissionType: "SYSTEM_MONITOR", title: t('golbalSearch.function.machine_monitor.title'), url: '/machine_monitor', keywords: t('golbalSearch.function.machine_monitor.keyword') },
    ],
    COMMON: [
      { title: t('golbalSearch.function.personal-center.title'), url: '/personal-center', keywords: t('golbalSearch.function.personal-center.keyword') },
      { title: t('golbalSearch.function.my-folder.title'), url: '/my-folder', keywords: t('golbalSearch.function.my-folder.keyword') },
      { title: t('golbalSearch.function.sql-log.title'), url: '/sql-log', keywords: t('golbalSearch.function.sql-log.keyword') },
      { title: t('golbalSearch.function.mes-management.title'), url: '/mes-management', keywords: t('golbalSearch.function.mes-management.keyword') },
      { title: t('golbalSearch.function.download.title'), url: '/download', keywords: t('golbalSearch.function.download.keyword') },
    ]
  }
}
