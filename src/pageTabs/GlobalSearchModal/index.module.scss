@import 'src/styles/variables';

.sceneGuideModal {

  :global {

    .ant-modal-header {
      border-bottom: 0;
    }

    .ant-modal-body {
      padding: 0 0;
      max-height: calc(100%) !important;
      overflow-y: hidden !important;
    }
  }

  .guideLogo {
    width: 18px;
    height: 18px;
    margin-right: 8px;
  }

}
.guideContent {
  padding: 24px 24px;

  max-height: 500px !important;
  min-height: 184px;
  overflow: hidden;
  display: flex;
  justify-content: flex-start;
  flex-direction: column;

  .searchContent {
    .searchHistory {
      border: none;
      cursor: pointer;
      border-radius: 16px;
      padding: 4px 16px;
      margin: 0 10px 10px 0;
      color: #4E5969;
      background-color: #F7F8FA;
    }
    .orderTitlt {
      max-width: 420px;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }

  .searchInput {
    width: 100%;
    margin-bottom: 10px;
    border-radius: 16px;
    height: 38px;

    .searchIcon {
      color: #86909C;
      font-size: 16px;
      margin-right: 10px;
    }
  }

  .moduleTitle {
    font-size: 14px;
    font-weight: 600;
    margin-top: 4px;
    margin-left: 6px;
  }

  .routeMenuList {
    flex: 1;
    height: calc(100% - 89px);
    overflow-y: auto;
    overflow-x: hidden;
    :global {
      .ant-row {
        width: 100%;
        margin: 0 0!important;
      }
    }
    .routerMenu {
      width: 100%;
      height: 62px;

      .menuItem {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        padding: 12px 12px;

        .icon {
          margin-right: 12px;
          font-size: 18px;
        }

        .left {
          display: flex;
          flex-direction: column;
          align-items: flex-start;
          width: calc(100% - 20px);
          overflow: hidden;
          .title {
            width: 100%;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            color: $primary-text-color;
          }

          .url {
            color: $sub-text-color;
            font-size: 12px;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            display: inline-block;
            width: 100%;
          }
        }
      }

    }

    .orderItemCol {
      cursor: pointer;
      padding: 2px 4px !important;
      border-radius: 6px;

      .orderItem {
        width: 98%;
        margin: 4px 4px;
      
        overflow: hidden;
        text-overflow: ellipsis;
        text-wrap: nowrap;

        em {
          color: #3357ff;
        }
        
      }

    }

    .linkBtnStyle {
      padding: 0 0;
    }

    .activeGuideIndex {
      cursor: pointer;
      background: #F7F8FA;
    }
  }
}
.searchModalPositionNav {
  width: 600px;
  position: absolute;
  top: 37px;
  z-index: 1000;
  background:#fff;
  .noData {
    width: 100%;
    height: 100px;
    line-height: 100px;
    text-align: center;
    color: #86909C;
  }
}