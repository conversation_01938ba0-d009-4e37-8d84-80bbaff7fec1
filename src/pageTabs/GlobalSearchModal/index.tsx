import React, { useEffect, useMemo, useState, memo } from "react";
import { Tag, Spin, Tooltip, message } from "antd";
import { useHistory } from 'react-router-dom'
import { useTranslation } from 'react-i18next';
import { UIModal } from 'src/components';
import { getGlobalContentSearch, IGloablSearchResult } from 'src/api';
import { useRequest, useSelector, useDispatch } from 'src/hook';
import { hideModal } from 'src/store/extraSlice/modalVisibleSlice'
import ModuleFuntions from "./ModuleFuntions";
import WorkOrderResult from './WorkOrderResult';
import { ALL_PAGES_MAPPING } from './contstant';
import { handleModuleJump } from './utils';
import styles from './index.module.scss';

const GlobalSearchModal = memo(({
  width,
  visible = false,
  searchValue,
  globalSearchModalType,
  resetGlobalSearchModalType
}: {
  visible: boolean
  width: number;
  searchValue: string | undefined;
  globalSearchModalType: null | 'modal' | 'overlay';
  resetGlobalSearchModalType: () => void;
}) => {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const history = useHistory();
  const { showCustomAudit, userInfo, globalSearchHistory = [] } = useSelector(state => state.login)
  const { permissionMenus = [] } = userInfo;
 
  //搜索工单内容
  const [searchResultData, setSearchResultData] = useState<IGloablSearchResult[]>([]);

  //工单搜索
  const { loading, run: runGetGlobalContentSearch } = useRequest<IGloablSearchResult[]>(getGlobalContentSearch, {
    manual: true,
    debounceInterval: 200,
    onSuccess: (res: IGloablSearchResult[]) => { setSearchResultData(res || []) }
  });

  useEffect(() => {

    if (searchValue && visible) {
      // runGetGlobalContentSearch({ keyword: searchValue });
    } else {
      setSearchResultData([])
    }
  }, [searchValue, visible])

  useEffect(() => {
    if (!visible) {
      setSearchResultData([]);
    }
  }, [visible])

  const hideModalAction = () => {
    resetGlobalSearchModalType();
    dispatch(hideModal('GlobalSearchModal'));
  }
  // 过滤出有权限的路由菜单
  const routesWithPermissions = useMemo(() => {
    // 控制自定义审计指标显隐
    let ALL_PAGES_MAPPING_EUM: any = ALL_PAGES_MAPPING(t)
    if (!showCustomAudit) {
      ALL_PAGES_MAPPING_EUM = {
        ...ALL_PAGES_MAPPING(t),
        AUDIT_ANALYZE: ALL_PAGES_MAPPING(t)?.AUDIT_ANALYZE?.filter((i: any) => i.url !== '/custom_audit_metrics')
      }
    }

    let hasPermissionMenus: any = [];
    ///获取当前拥有的菜单权限 + common 
    for (let menu of permissionMenus) {

      const menuType = menu?.menuType;
      //无子菜单
      if (['SYSTEM_DATA_OPERATE', 'DATA_PROTECT'].includes(menuType)) {
        const routeMenus = ALL_PAGES_MAPPING_EUM[menuType];
        hasPermissionMenus = hasPermissionMenus.concat(routeMenus);

      } else {

        const realMenuPermissionTypes = menu?.items?.map(i => i.permissionType) || [];
        const routeMenus = ALL_PAGES_MAPPING_EUM[menuType] || [];
        for (let i of routeMenus) {
          if (realMenuPermissionTypes.includes(i?.permissionType)) {
            hasPermissionMenus.push(i);
          }
        }
      }

    };
    return hasPermissionMenus.concat(ALL_PAGES_MAPPING_EUM['COMMON']);

  }, [permissionMenus, showCustomAudit])

  //根据筛选条件过滤出有权限的路由菜单
  const filteredRouteMenus = useMemo(() => {
    if (searchValue) {
      return routesWithPermissions.filter((i: any) => {
        const jsonString = i?.keywords?.replace(/'/g, '"');

        const parseKeywords = JSON.parse(jsonString);
        return (i?.title?.includes(searchValue) || parseKeywords?.join(',').toLowerCase()?.includes(searchValue.toLowerCase()))
      })
    }
    return routesWithPermissions;
  }, [JSON.stringify(routesWithPermissions), searchValue])

  const renderSearchResult = useMemo(() => {

    if (!filteredRouteMenus?.length && !searchResultData?.length && !loading) {
      return <div className={styles.noData}>{t("common.noData")}</div>
    }

    const handleClickSearchHistory = (item: any) => {
      if (item?.isPosition) {
        return message.warning(t("golbalSearch.jump.error"))
      }
      hideModalAction()
      handleModuleJump(dispatch, history, item?.url?.pathname)
    }

    return (
      <Spin spinning={loading}>
        <div className={styles.guideContent}>
          <div className={styles.searchContent}>
            <div>
              {
                globalSearchHistory?.map((i) =>
                  <Tooltip key={i.title} title={<div dangerouslySetInnerHTML={{ __html: i?.title }}></div>}>
                    <Tag className={styles.searchHistory}
                      onClick={() => handleClickSearchHistory(i)}
                    >{''}
                      <div className={styles.orderTitlt} dangerouslySetInnerHTML={{ __html: i?.title }}></div>
                    </Tag>
                  </Tooltip>
                )
              }
            </div>
          </div>
          <div className={styles.routeMenuList}>
            {/* 功能 */}
            <ModuleFuntions searchValue={searchValue} filteredRouteMenus={filteredRouteMenus} onClose={() => { hideModalAction(); }} />
            {/* 工单 */}
            {/* <WorkOrderResult data={searchResultData} /> */}
          </div>
        </div>
      </Spin>
    )
  }, [loading, JSON.stringify(filteredRouteMenus), searchResultData, searchValue])

  if (globalSearchModalType === 'overlay' && visible) {
    return <div id="searchModalPositionNav" className={styles.searchModalPositionNav} style={{ width: width > 600 ? width : 600 }}>
      {renderSearchResult}
    </div>
  }

  return (
    <UIModal
      visible={visible && globalSearchModalType === 'modal'}
      width={693}
      centered
      closable={true}
      className={styles.sceneGuideModal}
      footer={null}
      onCancel={() => { hideModalAction(); }}
    >
      {renderSearchResult}
    </UIModal >
  )
})

export default GlobalSearchModal;