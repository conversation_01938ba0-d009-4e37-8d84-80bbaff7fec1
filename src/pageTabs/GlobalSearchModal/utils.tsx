import { 
  setMineApplyPageState, 
  setMineApplyDetailParams, 
  setMineApprovePageState, 
  setMineApproveDetailParams,
  setFlowWorkOrderManagementPageState, 
  setFlowWorkOrderManagementDetailParams 
} from 'src/pageTabs/access-request/accessRequestSlice'
import {
  setDataChangeMineApplyPageState,
  setDataChangeMineApprovePageState,
  setDataChangeOrderManagementPageState,
  setDataChangeMineApplyDetailParams,
  setDataChangeMineApproveDetailParams,
  setDataChangeOrderManagementDetailParams
} from 'src/pageTabs/data-change/dataChangeSlice'
import { 
	setTransferTaskRecordPageState,
  setTransferTaskRecordDetailParams
} from 'src/pageTabs/dataTransfer/transferTaskRecord/transferTaskRecordSlice'
import { 
  setOverviewPageState,
  setOverviewPageDetailParams 
} from 'src/pageTabs/audit/overview/overviewSlice'

import {
  setSystemManageUserManageState,
  setSystemManageUserManageDetailParams
} from 'src/pageTabs/organizationPage/organizationSlice'


// 全局搜索-功能/历史记录跳转
export const handleModuleJump = (dispatch: any, history: any, url: string, state={}) => {
  
  const gotoPathname = (targetPathname: string) => {
    const curPathname = history.location.pathname;
    if(curPathname !==  targetPathname){
      history.push(targetPathname)
    }
  }

  switch (url) {
    // ---------- 流程 -----------
    // 流程-我的申请
    case '/mine_apply':
      gotoPathname('/mine_apply');
      dispatch(setMineApplyPageState(''))
      dispatch(setMineApplyDetailParams({}))
      break;
      
    // 流程-我的申请-购物车
    case '/mine_apply/application':
      gotoPathname('/mine_apply');
      const curPathname = history.location.pathname;
      if(curPathname !== '/mine_apply'){
        history.push('/mine_apply')
      }
      dispatch(setMineApplyPageState('application'))
      dispatch(setMineApplyDetailParams({}))
      break;

    // 流程-我的申请-申请清单
    case '/mine_apply/search':
      gotoPathname('/mine_apply');
      dispatch(setMineApplyPageState('search'))
      dispatch(setMineApplyDetailParams({}))
      break;

    // 流程-我的申请-申请详情
    case '/mine_apply/detail':
      gotoPathname('/mine_apply');
      dispatch(setMineApplyPageState('detail'))
      dispatch(setMineApplyDetailParams(state))
      break;

    // 流程-我的审批-审批详情
    case '/mine_approve/detail':
      gotoPathname('/mine_approve');
      dispatch(setMineApprovePageState('detail'))
      dispatch(setMineApproveDetailParams(state))
      break;

    // 流程-工单管理-工单详情
    case '/work_order/detail':
      gotoPathname('/work_order');
      dispatch(setFlowWorkOrderManagementPageState('detail'))
      dispatch(setFlowWorkOrderManagementDetailParams(state))
      break;

    // ----------- 数据变更 --------------
    // 数据变更-我的申请
    case '/data_change_mine_apply':
      gotoPathname('/data_change_mine_apply');
      dispatch(setDataChangeMineApplyPageState(''))
      dispatch(setDataChangeMineApplyDetailParams(state))
      break;

    // 数据变更-我的申请-工单详情
    case '/data_change_mine_apply/detail':
      gotoPathname('/data_change_mine_apply');
      dispatch(setDataChangeMineApplyPageState('detail'))
      dispatch(setDataChangeMineApplyDetailParams(state))
      break;

    // 数据变更-我的申请-申请变更add/再次申请edit
    case '/data_change_mine_apply/addOrEdit':
      gotoPathname('/data_change_mine_apply');
      dispatch(setDataChangeMineApplyPageState('addOrEdit'))
      dispatch(setDataChangeMineApplyDetailParams(state))
      break;

    // 数据变更-我的审批
    case '/data_change_mine_approve':
      gotoPathname('/data_change_mine_approve');
      dispatch(setDataChangeMineApprovePageState(''))
      dispatch(setDataChangeMineApproveDetailParams(state))
      break;

    // 数据变更-我的审批-工单详情
    case '/data_change_mine_approve/detail':
      gotoPathname('/data_change_mine_approve');
      dispatch(setDataChangeMineApprovePageState('detail'))
      dispatch(setDataChangeMineApproveDetailParams(state))
      break;

    // 数据变更-工单管理
    case '/data_change_work_order_management':
      gotoPathname('data_change_work_order_management')
      dispatch(setDataChangeOrderManagementPageState(''))
      dispatch(setDataChangeOrderManagementDetailParams({}))
      break;

    // 静态脱敏-任务记录
    case '/data_transform':
      gotoPathname('/data_transform');
      dispatch(setTransferTaskRecordPageState(''))
      dispatch(setTransferTaskRecordDetailParams({}))
      break;
    
    // 静态脱敏-任务记录-创建任务
    case '/create-transfer-task':
      gotoPathname('/data_transform');
      dispatch(setTransferTaskRecordPageState('create'))
      dispatch(setTransferTaskRecordDetailParams({}))
      break;

    // 审计分析-审计概览
    case '/audit_view':
      gotoPathname('/audit_view');
      dispatch(setOverviewPageState(''))
      dispatch(setOverviewPageDetailParams({}))
      break;

    // 审计分析-审计概览-语句明细
    case '/statement_detail':
      gotoPathname('/audit_view');
      dispatch(setOverviewPageState('statement_detail'))
      dispatch(setOverviewPageDetailParams(state))
      break;

    // 审计分析-审计概览-操作记录
    case '/operate_record':
      gotoPathname('/audit_view');
      dispatch(setOverviewPageState('operate_record'))
      dispatch(setOverviewPageDetailParams(state))
      break; 
    
    // 审计分析-审计概览-越权操作
    case '/operate_unauthorized':
      gotoPathname('/audit_view');
      dispatch(setOverviewPageState('operate_unauthorized'))
      dispatch(setOverviewPageDetailParams(state))
      break; 
    
    // 审计分析-审计概览-自定义审计指标
    case '/custom_audit_metrics':
      gotoPathname('/audit_view');
      dispatch(setOverviewPageState('custom_audit_metrics'))
      dispatch(setOverviewPageDetailParams(state))
      break; 

    // 系统管理-用户管理
    case '/person_management':
      gotoPathname('/person_management');
      dispatch(setSystemManageUserManageState(''))
      dispatch(setSystemManageUserManageDetailParams({}))
      break;

    default:
      // 其他路由正常跳转
      url && history.push(url, state);
      break;
  }
}