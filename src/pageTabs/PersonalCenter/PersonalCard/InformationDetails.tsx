import React, { useCallback, useContext, useEffect, useState } from 'react'
import { Card, Col, Row, Upload, Form, Typography, message, Input, Select } from 'antd'
import { useDispatch, useRequest, useSelector } from 'src/hook'
import { FormInstance } from 'antd/lib/form'
import { PersonalFormContext } from '../PersonalFormContext'
import { getUserDetail, IMaskStatus, updateUserSupplementInfo, changeUserName } from 'src/api'
import ImageCropper from 'antd-image-cropper'
import { Iconfont, LinkButton } from 'src/components'
import styles from './index.module.scss'
import { CopyOutlined, FormOutlined } from '@ant-design/icons'
import { UploadFile, UploadProps } from 'antd/lib/upload/interface'
import classNames from 'classnames'
import copy from 'copy-to-clipboard'
import { setUserInfo } from 'src/appPages/login/loginSlice'
import { setUserAvatar } from 'src/store/extraSlice/userAvatarSlice'
import { effectTimeTypeOptions } from 'src/constants'
import moment from 'moment'
import { useTranslation } from 'react-i18next';


export const InformationDetails = () => {
  const dispatch = useDispatch()
  const form = useContext(PersonalFormContext) as FormInstance
  const baseAddress = "/user";
  const { userInfo } = useSelector((state) => state.login)
  const userId = userInfo?.userId
  const { t } = useTranslation();
  const [userInfoState, setUserInfoState] = useState<any>()
  const [maskStatus, setMaskStatus] = useState<IMaskStatus>({
    emailMask: true,
    phoneMask: true,
  })
  const [editing, setEditing] = useState(false)
  // 以下为仅做展示（不可修改的）用户参数tagName
  const siftUserInfoTagNames = ['LoginAuth', 'TwoFactorAuth', 'TwoFactorEffectTime']
  const [editingUserName, setEditingUserName] = useState(false)
  const [logoImg, setlogoImg] = useState<UploadFile[]>([
    //@ts-ignore
    {
      uid: '-1',
      name: 'image.png',
      status: 'done',
      url: '',
    }
  ]);
  const userInfoEntries = Object.entries(userInfoState?.user || {})
  userInfoEntries.push(['dept', userInfoState?.departmentName])
  const getUserInfo = useCallback(
    () => getUserDetail(maskStatus, userId!).then((e) => {
      form.resetFields();
      if (e) {
        dispatch(setUserInfo({ ...userInfo, userName: e?.user?.userName }))
        form.setFields([
          {
            name: 'userName',
            value: e?.user?.userName,
          }
        ])
        form.setFieldsValue({
          "parameterValues": e?.user?.parameterValues?.map((a: any) => {
            const userValue = a;
            return userValue
          })
        })

        let img = {
          uid: '-1',
          name: 'image.png',
          status: 'done',
          url: e?.user && e?.user?.avatarUrl ? baseAddress + e?.user?.avatarUrl : '',
        }
        logoImg.pop()
        //@ts-ignore
        logoImg.push(img)
        setlogoImg([...logoImg])
      }
      setUserInfoState(e)
    }),
    [userId, maskStatus, editing],
  )
  // 修改补充信息
  const { run: undateInfo } = useRequest(updateUserSupplementInfo, {
    manual: true,
    onSuccess() {
      setEditing(false)
      message.success(t("personal:modificationSuccessful"))
      getUserInfo()
    }
  })

  // 修改姓名
  const { run: updateUserName } = useRequest(changeUserName, {
    manual: true,
    onSuccess() {
      message.success(t("personal:modificationSuccessful"))
      setEditingUserName(false)
      getUserInfo()
    }
  })

  const handleMask = (key: keyof IMaskStatus) => {
    setMaskStatus({ ...maskStatus, [key]: !maskStatus[key] })
  }

  useEffect(() => {
    getUserInfo()
  }, [getUserInfo])
  if (!userInfoState) return null

  // const beforeUpload = (file: RcFile) => {
  // const isJpgOrPng = file.type === 'image/png' || file.type === 'image/jpg';
  // if (!isJpgOrPng) {
  //   message.error('只支持png|jpg格式的图片');
  // }
  // const isLt2M = file.size / 1024 < 200;
  // if (!isLt2M) {
  //   message.error('图片不能超过200kb!');
  // }
  // return isJpgOrPng
  // };

  const onChangeLogo: UploadProps['onChange'] = ({ fileList: newFileList, event, file }) => {
    if (file?.response?.resCode === 10000) {
      message.success(t("personal:avatarUploadSuccessful"))
      file.url = baseAddress + file?.response?.data
      dispatch(setUserAvatar(file.url))
      setlogoImg([file]);
    } else {
      file?.response?.resMsg && message.error(file?.response?.resMsg)
    }
  };

  const setSave = () => {
    form.validateFields().then((values) => {
      const { dynamicFieldName } = values
      let parameterValues: any[] = []
      form.getFieldValue("parameterValues")?.forEach((item: any) => {
        parameterValues.push({
          id: item?.id,
          parameter: item.parameter,
          value: dynamicFieldName && dynamicFieldName[item?.parameter?.tagName] ? dynamicFieldName[item?.parameter?.tagName] : ''
        })
      })
      undateInfo({
        userId,
        parameterValues
      })
    })
  }

  const onBlurUserName = (e: any) => {
    form.validateFields(['userName']).then(({ userName }) => {
      const params = {
        userId,
        userName
      }
      updateUserName(params)
    })
  }

  const formatEffectTime = (value: any, tagName: string) => {
    if (tagName === 'TwoFactorEffectTime' && value) {
      const realValue = JSON.parse(value)
      const { day, startTime, endTime } = realValue
      const dayLabel = effectTimeTypeOptions.find((item: any) => item.value === day)?.label ?? ''
      const start = moment(startTime, 'HH:mm:ss').format('HH:mm')
      const end = moment(endTime, 'HH:mm:ss').format('HH:mm')
      return `${dayLabel} ${start} - ${end}`
    }
    else return value
  }

  const imgprops = {
    aspectRatio: 1 / 1,
    grid: true,
    crop: true,
    modalTitle: '上传图片',
    modalOk: '上传',
    minWidth: 50,
    minHeight: 50,
  }

  const extraOperations = (
    <div className={styles.settingCardExtra}>
      {editing ? (
        [
          <LinkButton key="save" onClick={setSave}>
            {t("personal:save")}
          </LinkButton>,
          <LinkButton
            key="cancel"
            onClick={() => {
              form.resetFields()
              form.setFields([
                {
                  name: 'userName',
                  value: userInfoState?.user?.userName,
                }
              ])
              form.setFieldsValue({
                "parameterValues": userInfoState?.user?.parameterValues?.map((a: any) => {
                  const userValue = a;
                  return userValue
                })
              })
              setEditing(false)
            }}
          >
            {t("personal:cancel")}
          </LinkButton>,
        ]
      ) : (
        <LinkButton onClick={() => setEditing(true)}>  {t("personal:edit")}</LinkButton>
      )}
    </div>
  )

  const canEdit = !!userInfoState?.user?.parameterValues?.filter((i: any)=>["EDIT_ONLY", "EDIT_DELETE"].includes(i?.parameter?.accessMode))?.length

  return (
    <div>
      <section className="cq-new-card flow-card" style={{ marginBottom: '10.5px' }}>
        <div className="cq-card__headerbar">
          <h3 className="cq-card__title">{t("personal:personalInformation")}</h3>
        </div>
        <section className="card__content">
          <Card
            bordered={false}
            bodyStyle={{ display: 'flex' }}
          >
            <div className={styles.portraitWrap}>
              <ImageCropper {...imgprops}>
                <Upload
                  className={classNames(styles.uploadImg)}
                  action="/user/users/uploadAvatar"
                  listType="picture-card"
                  showUploadList={false}
                  fileList={logoImg}
                  // beforeUpload={beforeUpload}
                  onChange={onChangeLogo}
                >
                  {
                    logoImg && logoImg[0]?.url ? <img src={logoImg[0]?.url} alt="avatar" style={{ width: '100%', height: '100%', borderRadius: '50%' }} /> : t("personal:uploadAvatar")
                  }
                </Upload>
              </ImageCropper>
              <div>{userInfoState?.user?.userId} <CopyOutlined onClick={() => {
                copy(userInfoState?.user?.userId)
                message.success(t("personal:copySuccessful"))
              }}
              /></div>
            </div>
            <div className={styles.infoWrap}>
              <Row style={{alignItems: 'center'}}>
                <Col span={10} style={{ display: 'flex', alignItems: 'center' }}>
                  <label className={styles.tagNames}>{t("personal:name")}：</label>
                  <Form.Item>
                    <Form.Item
                      name="userName"
                      noStyle
                      hidden={!editingUserName}
                      rules={[{ required: true, message: t("personal:name_cannot_be_empty") }]}
                    >
                      <Input
                        autoFocus
                        placeholder={t("personal:please_enter_your_name")}
                        style={{ width: '150px' }}
                        onBlur={onBlurUserName}
                      />
                    </Form.Item>
                  </Form.Item>
                  {
                    !editingUserName && <div>
                      <span style={{ display: 'inline-block', width: '120px' }}>{userInfoState?.user?.userName}</span>
                      <FormOutlined onClick={() => {
                        setEditingUserName(true)
                      }} />
                    </div>
                  }
                </Col>
                <Col span={14}>
                  <label className={styles.tagNames}>{t("personal:systemRole")}：</label>
                  <span>
                    {
                      userInfoState?.systemRoles && userInfoState?.systemRoles.length ? userInfoState?.systemRoles?.join('、') : '-'
                    }
                  </span>
                </Col>
              </Row>
              <Row className={styles.mb17}>
                <Col span={10}>
                  <label className={styles.tagNames}>{t("personal:department")}：</label>
                  <span>{userInfoState?.departmentName}</span>
                </Col>
                <Col span={14}>
                  <label className={styles.tagNames}>{t("personal:registrationTime")}：</label>
                  <span>{userInfoState?.user?.createdAt}</span>
                </Col>
              </Row>
              <Row className={styles.mb17}>
                <Col span={10}>
                  <label className={styles.tagNames}>{t("personal:email")}：</label>
                  <span>{userInfoState?.user?.email}</span>
                  <LinkButton onClick={() => handleMask('emailMask')}>
                    <Iconfont type={`icon-kejian-${!maskStatus.emailMask}`} />
                  </LinkButton>
                </Col>
                <Col span={14}>
                  <label className={styles.tagNames}>{t("personal:lastLoginTime")}：</label>
                  <span>{userInfoState?.lastLoginTime}</span>
                </Col>
              </Row>
              <Row className={styles.mb17}>
                <Col span={10}>
                  <label className={styles.tagNames}>{t("personal:phoneNumber")}：</label>
                  <span>{userInfoState?.user?.telephone}</span>
                  <LinkButton onClick={() => handleMask('phoneMask')}>
                    <Iconfont type={`icon-kejian-${!maskStatus.phoneMask}`} />
                  </LinkButton>
                </Col>
              </Row>
            </div>
          </Card>
        </section>
      </section>
      <section className="cq-new-card flow-card" style={{ marginBottom: '10.5px' }}>
        <div className="cq-card__headerbar">
          <h3 className="cq-card__title">{t("personal:additionalInformation")}</h3>
          {
            canEdit && extraOperations
          }
        </div>
        <section className="card__content">
          <Card
            className={styles.settingCardContent}
            bordered={false}
          // loading={loading}
          >
            {
              form.getFieldValue("parameterValues") && form.getFieldValue("parameterValues")
                .map((item: any, index: number) => {
                  const options = item?.parameter?.examples.map((item: string) => {
                    return {value: item, label: item}
                  })
                  return <div key={index}>
                    {
                      (!editing || siftUserInfoTagNames.includes(item?.parameter?.tagName as string)) &&
                      <div className={classNames(styles.mb10, styles.flex)}>
                        <p className={classNames(styles.flex2, styles.textAlignRight)}>{item?.parameter?.parameterName} : </p>
                        <p className={classNames(styles.flex8, styles.ml30)}>{formatEffectTime(item?.value, item?.parameter?.tagName as string)}</p>
                      </div>
                    }

                    <Form.Item
                      hidden={!editing || siftUserInfoTagNames.includes(item?.parameter?.tagName as string)}
                      initialValue={item?.value} name={["dynamicFieldName", item?.parameter?.tagName as string]}
                      label={item?.parameter?.parameterName}
                      rules={[
                        { required: item?.parameter?.needed, message: `${t("personal:pleaseEnter")} ${item?.parameter?.parameterName}${t("personal:nickName")}` },
                      ]}>
                       {
                          item?.parameter?.examples.length > 0?
                          <Select 
                            options={options}
                            placeholder={t("personal:pleaseSelect")}
                            allowClear
                            disabled={!["EDIT_ONLY", "EDIT_DELETE"].includes(item?.parameter?.accessMode)}
                          />
                          : 
                          <Input
                            placeholder={`${t("personal:pleaseEnter")} ${item?.parameter?.parameterName}`}
                            allowClear
                            disabled={!["EDIT_ONLY", "EDIT_DELETE"].includes(item?.parameter?.accessMode)}
                          />
                        }
                    </Form.Item>
                  </div>
                })
            }
          </Card>
        </section>
      </section>
    </div>
  )
}
