.flex{
  display: flex;
}
.flex2 {
  flex: 2
}
.flex8 {
  flex: 8
}
.textAlignRight {
  text-align: right;
}
.ml30 {
  margin-left: 30px;
}
.mb20px {
  margin-bottom: 20px;
}
.mb17 {
  margin-bottom: 17px;
}
.portraitWrap {
  flex: 2;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  // margin-left: 50px;
  // text-align: center;
}
.infoWrap {
  flex: 8;
  font-size: 14px;
}
.tagNames {
  color: #868FA3;
}
.uploadImg {
  :global {
    .ant-upload-list-item-list-type-picture-card {
      border-radius: 50%;
    }
    .ant-upload-select-picture-card {
      border-radius: 50%;
      img {
        border-radius: 50%;
      }
    }
  }
}