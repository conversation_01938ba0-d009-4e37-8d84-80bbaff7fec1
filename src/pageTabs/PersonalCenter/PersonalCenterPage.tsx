import React, { useState, useEffect } from 'react'
import { useLocation } from 'react-router-dom';
import { Layout, Breadcrumb, Tabs } from 'antd'
import 'src/styles/layout.scss'
import { Iconfont } from 'src/components'
import { GloablSearchLocationState } from 'src/pageTabs/GlobalSearchModal/WorkOrderResult';
import { PersonalBasicPage } from './PersonalBasicPage'
import styles from './index.module.scss'
import { useTranslation } from 'react-i18next';
const { Header, Sider, Content } = Layout;

export const PersonalCenterPage = () => {
  
  const location = useLocation<GloablSearchLocationState>();
  const { state = {} } = location;
  const [tabsKey, setTabesKey] = useState('Information')
  const { t } = useTranslation();

  //全局搜索
  useEffect(() => {
 
    const globalSearchTabKey = location?.state?.globalSearchTabKey;
    if (globalSearchTabKey) {
      setTabesKey(globalSearchTabKey);
    }
  }, [state?.globalSearchTabKey])
   
  const SettingTabs = (
    <Tabs
      className={styles.personalTabs}
      tabPosition={'left'}
      type="card"
      activeKey={tabsKey}
      onChange={(e) => {
        setTabesKey(e)
      }}
    >
      <Tabs.TabPane tab={
        <>
          <Iconfont type="icon-gerenxinxi"></Iconfont>
          <span>{t("personal:personalInformation")}</span>
        </>
      } key="Information" />
      <Tabs.TabPane tab={
        <>
          <Iconfont type="icon-anquanshezhi"></Iconfont>
          <span>{t("personal:securitySettings")}</span>
        </>
      } key="SecuritySettings" />
      <Tabs.TabPane tab={
        <>
          <Iconfont type="icon-kefangwenziyuan"></Iconfont>
          <span>{t("personal:accessibleResources")}</span>
        </>
      } key="AccessibleResource" />
      <Tabs.TabPane tab={
        <>
          <Iconfont type="icon-pianhaoshezhi"></Iconfont>
          <span>{t("personal:preferenceSettings")}</span>
        </>
      } key="Preferences" />
    </Tabs>
  )
  return (
    <Layout className="cq-container">
      <Header className="breadcrumb-header">
        <Breadcrumb className="breadcrumb" separator=''>
          <Breadcrumb.Item>{t("personal:personalCenter")}</Breadcrumb.Item>
          <Breadcrumb.Separator>|</Breadcrumb.Separator>
          <Breadcrumb.Item>{t("personal:personalSettings")}</Breadcrumb.Item>
        </Breadcrumb>
      </Header>
      <Layout className="cq-main" style={{padding: '0 10px 0 0'}}>
        <Sider className="cq-aside" width={246}>
          <section className="cq-card" style={{ background: '#F7F9FC', border: 'none' }}>
            <div className={styles.personalTabsTitle} >
              {t("personal:personalSettings")}
            </div>
            <div className="cq-card__content" style={{ width: '94%', margin: '0 auto' }}>
              {SettingTabs}
            </div>
          </section>
        </Sider>
        <Content className="cq-content" id="anchor-box" style={{marginLeft: '10px'}}>
          <div className='cq-content-card' style={{}}>
            <PersonalBasicPage activeKey={tabsKey} />
          </div>
        </Content>
      </Layout>
    </Layout>
  )
}
