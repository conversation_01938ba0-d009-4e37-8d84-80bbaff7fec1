import { InfoCircleOutlined } from "@ant-design/icons"
import { Form, Input, Tooltip } from "antd"
import React, { useState } from "react"
import styles from './component.module.scss'
import { ResTimeFormTooltipContent } from "../constant"
import { debounce } from "lodash"
import { useRequest } from "src/hook"
import { getDateTimeConfigOutput } from "src/api"
import { useTranslation } from 'react-i18next';

const TimeDisplayForm = (props: any) => {
  const { t } = useTranslation();
  const { onChange, form } = props
  const INITIAL_PROMPT = t("personal:displayOutputBasedOnInputFormat")
  const LOADING_PROMPT = t("personal:generating")
  const [outputValue, setOutputValue] = useState<string>(INITIAL_PROMPT)
  // 控制字体颜色提示用户输入格式是否能正常输出
  const [dateInputErr, setDateInputErr] = useState<boolean>(false)
  const [timeInputErr, setTimeInputErr] = useState<boolean>(false)
  const [dateTimeInputErr, setDateTimeInputErr] = useState<boolean>(false)

  const { run: runGetDateTimeOutput } = useRequest(getDateTimeConfigOutput, {
    manual: true,
    onSuccess: (data: any, params: any[]) => {
      setOutputValue(data)
      const { type, pattern } = params?.[0]
      batchSetOutputErr(type, false)
      const values = form?.getFieldsValue()
      const saveParams = {
        date: type === 1 ? pattern : !dateInputErr ? values?.resFormatDate : '',
        time: type === 2 ? pattern : !timeInputErr ? values?.resFormatTime : '',
        datetime: type === 3 ? pattern : !dateTimeInputErr ? values?.resFormatDateTime : ''
      }
      onChange?.(JSON.stringify(saveParams))
    },
    onError: (_, params: any[]) => {
      setOutputValue('')
      const { type } = params?.[0]
      batchSetOutputErr(type, true)
    }
  })

  // 批量处理字体颜色
  const batchSetOutputErr = (type: number, value: boolean) => {
    switch (type) {
      case 1:
        setDateInputErr(value)
        break
      case 2:
        setTimeInputErr(value)
        break
      case 3:
        setDateTimeInputErr(value)
        break
    }
  }

  // 获取输出实例并保存效果
  const getOutputAndSave = debounce((e: any, type: number) => {
    runGetDateTimeOutput({
      pattern: e?.target?.value,
      type
    })
  }, 500)

  // input onchange
  const handleInputChange = (e: any, type: number) => {
    if (!e?.target?.value) {
      setOutputValue(INITIAL_PROMPT)
    }
    else {
      setOutputValue(LOADING_PROMPT)
      const currentEvent = e.nativeEvent
      getOutputAndSave(currentEvent, type)
    }
  }

  return <section>
    <Form.Item
      className={styles.titleFormItem}
      label={<div className={styles.timeDisplayTitle}>
        {t("personal:resultSetDisplayFormat")}
        <Tooltip
          title={ResTimeFormTooltipContent}
          overlayClassName={styles.timeDisplayTooltip}
        >
          <InfoCircleOutlined className={styles.tipIcon} />
        </Tooltip>
      </div>}
    />
    <Form.Item
      name={'resFormatDate'}
      label={t("personal:date")}
      rules={[{ max: 100, message: t("personal:maximum100Characters")}]}
    >
      <Input
        style={{ color: dateInputErr ? '#FF4D4F' : 'rgba(0, 0, 0, 0.85)' }}
        onChange={(e: any) => {
          handleInputChange(e, 1)
        }}
        onFocus={(e: any) => {
          handleInputChange(e, 1)
        }}
      />
    </Form.Item>
    <Form.Item
      name={'resFormatTime'}
      label={t("personal:time")}
      rules={[{ max: 100, message: t("personal:maximum100Characters") }]}
    >
      <Input
        style={{ color: timeInputErr ? '#FF4D4F' : 'rgba(0, 0, 0, 0.85)' }}
        onChange={(e: any) => {
          handleInputChange(e, 2)
        }}
        onFocus={(e: any) => {
          handleInputChange(e, 2)
        }}
      />

    </Form.Item>
    <Form.Item
      name={'resFormatDateTime'}
      label={t("personal:dateTime")}
      rules={[{ max: 100, message: t("personal:maximum100Characters") }]}
    >
      <Input
        style={{ color: dateTimeInputErr ? '#FF4D4F' : 'rgba(0, 0, 0, 0.85)' }}
        onChange={(e: any) => {
          handleInputChange(e, 3)
        }}
        onFocus={(e: any) => {
          handleInputChange(e, 3)
        }}
      />
    </Form.Item>
    <Form.Item label={t("personal:example")}>
      2024/12/23 12:34
    </Form.Item>
    <Form.Item
      label={<div className={styles.timeDisplayTitle}>
        {t("personal:input")}
        <Tooltip
          title={t("personal:displayOutputEffectOfCurrentInputFormat")}
          overlayClassName={styles.timeDisplayTooltip}
        >
          <InfoCircleOutlined className={styles.tipIcon} />
        </Tooltip>
      </div>}
      style={{
        color: [LOADING_PROMPT, INITIAL_PROMPT].includes(outputValue) ? '#BFBFBF' : '#22223F'
      }}
    >
      {outputValue}
    </Form.Item>
  </section>
}

export default TimeDisplayForm