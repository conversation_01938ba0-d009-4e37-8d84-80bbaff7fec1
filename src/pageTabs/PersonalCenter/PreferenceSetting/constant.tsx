import React from "react";
import i18n from 'i18next';


export const ResTimeFormTooltipContent:React.ReactNode = () => <p>{i18n.t("personal:commonFormatting")}:<br/>
{i18n.t("personal:year")}：<br/>
<br/>
YYYY：{i18n.t("personal:YYYY")}<br/>
yyyy：{i18n.t("personal:yyyy")}<br/>
YY：{i18n.t("personal:YY")}<br/>
<br/>
{i18n.t("personal:month")}：<br/>
<br/>
MM：{i18n.t("personal:MM")}<br/>
MMM：{i18n.t("personal:MMM")}<br/>
MMMM：{i18n.t("personal:MMMM")}<br/>
<br/>
{i18n.t("personal:day")}：<br/>
<br/>
dd：{i18n.t("personal:dd")}<br/>
<br/>
{i18n.t("personal:dayOfWeek")}：<br/>
<br/>
ddd：{i18n.t("personal:ddd")}<br/>
dddd：{i18n.t("personal:dddd")}<br/>
<br/>
{i18n.t("personal:time")}：<br/>
<br/>
HH：{i18n.t("personal:HH")}<br/>
hh：{i18n.t("personal:hh")}<br/>
mm：{i18n.t("personal:mm")}<br/>
ss：{i18n.t("personal:ss")}<br/>
a：{i18n.t("personal:a")}<br/>
<br/>
{i18n.t("personal:organizeParameterList")}：<br/>
<br/>
 <span dangerouslySetInnerHTML={{__html: i18n.t("personal:personal.preferenceSetting.constant.example")}}></span>
</p>