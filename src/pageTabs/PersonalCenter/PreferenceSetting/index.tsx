import React, { useEffect, useState, memo } from 'react'
import { Card, Form, Switch, Select, Radio } from 'antd'
import { useRequest, useDispatch, useSelector } from 'src/hook'
import {
  getUserConfig,
  patchUserConfig
} from 'src/api'
import { setEditorPromptOnClose } from 'src/store/extraSlice/appearanceSlice'
import { ENCODINGS, FormLayout } from 'src/constants'
import { setUserInfo } from 'src/appPages/login/loginSlice'
import TimeDisplayForm from './components/TimeDisplayForm'
import { useTranslation } from 'react-i18next';

const encodingList = ENCODINGS.map(e => {
  const v = e.valueOf()
  return { label: v, value: v }
})

export const PreferenceSetting = memo(() => {

  const dispatch = useDispatch()
  const [configForm] = Form.useForm()
  const { execute, execPlan, prompt } = useSelector(state => state.setting.hotKeys)
  const [value, setValue] = useState<string>('UTF-8')
  const { t } = useTranslation();

  const { userInfo } = useSelector(state => state.login)
  const { encoding: encod, ...rest } = userInfo

  const { data: userConfig, run: tryGetUserConfig } = useRequest(
    getUserConfig,
    {
      onSuccess: ({ editorPromptOnClose, dateDisplayFormat = '' }) => {
        dispatch(setEditorPromptOnClose(editorPromptOnClose))
        const { date, time, datetime } = JSON.parse(dateDisplayFormat || '{}')
        configForm.setFieldsValue({
          resFormatDate: date ?? '',
          resFormatTime: time ?? '',
          resFormatDateTime: datetime ?? '',
          editorPromptOnClose,
          executeEvenIfError
        })
      },
    },
  )
  const { run: trySetUserConfig } = useRequest(patchUserConfig, {
    manual: true,
  })
  const { encoding: encodingSet, executeEvenIfError, resultDisplayMode } = userConfig || {}

  useEffect(() => {
    configForm.setFieldsValue({ execPlan: execPlan, execute: execute, prompt: prompt, resultDisplayMode})
  }, [execPlan, execute, prompt, configForm, resultDisplayMode])

  useEffect(() => {
    if (encodingSet) {
      setValue(encodingSet)
    }
  }, [encodingSet])

  return (
    <section className="cq-new-card flow-card">
      <div className="cq-card__headerbar">
        <h3 className="cq-card__title">{t("personal:preferenceSettings")}</h3>
      </div>
      <section className="card__content">
        <Card bordered={false}>
          <Form form={configForm} {...FormLayout}>
            <Form.Item label={t("personal:editorExitPrompt")}>
              <Form.Item noStyle name="editorPromptOnClose" valuePropName="checked">
                <Switch
                  checkedChildren={t("personal:enable")}
                  unCheckedChildren={t("personal:disable")}
                  onChange={(editorPromptOnClose) => {
                    trySetUserConfig({ editorPromptOnClose }).finally(() =>
                      tryGetUserConfig(),
                    )
                  }}
                ></Switch>
              </Form.Item>
            </Form.Item>
            <Form.Item label={t("personal:encodingFormat")}>
              <Select
                value={value}
                options={encodingList}
                onChange={(encoding: string) => {
                  setValue(encoding)
                  trySetUserConfig({ encoding }).finally(() => {
                    tryGetUserConfig()
                    const userInfoCopy = Object.assign({}, userInfo, { personalSettings: { encoding: encoding, ...rest } })
                    dispatch(setUserInfo(userInfoCopy))
                  })
                }}
              ></Select>
            </Form.Item>
          </Form>
          <Form form={configForm} {...FormLayout}>
            <Form.Item label={t("personal:paginationType")} name="resultDisplayMode" initialValue='page'>
              <Radio.Group 
                options={
                  [
                   { label: t("personal:pageLoading"), value: 'page' },
                   { label: t("personal:scrollLoading"), value: 'scroll' }
                  ]
                }
                onChange={(e: any) => {
                  trySetUserConfig({ resultDisplayMode: e.target.value }).finally(() =>
                    tryGetUserConfig(),
                  )
                }}
              />
            </Form.Item>
            {/* 结果集时间格式 */}
            <TimeDisplayForm
              form={configForm}
              onChange={(value: string) => {
                trySetUserConfig({ dateDisplayFormat: value })
              }}
            />
          </Form>
        </Card>
      </section>
    </section>
  )
})

export const hotKeyValidate = (key: string) => {
  const varArr = key.split('+')
  // 数字字母
  const reg = /^[0-9a-z]$/
  const keyModCheck = ['ctrl', 'alt', 'shift', 'win', 'command'].includes(varArr[0])
  const keyCodeCheck = reg.test(varArr[1])
  return keyModCheck && keyCodeCheck
}
