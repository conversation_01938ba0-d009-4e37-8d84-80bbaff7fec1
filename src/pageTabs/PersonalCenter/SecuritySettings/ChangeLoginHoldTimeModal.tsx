import React, { useCallback, useEffect } from 'react'
import { useDispatch, useRequest, useSelector } from 'src/hook'
import { Form, message } from 'antd'
import { CustomInputNumber, UIModal } from 'src/components'
import { FormLayout } from 'src/constants'
import { getExpireTimeByMin, postOvertime } from 'src/api'
import { hideModal } from 'src/store/extraSlice/modalVisibleSlice'
import { useTranslation } from 'react-i18next';

export const ModalChangeLoginHodlTime: React.FC<{ loginTime?: any, refresh: () => Promise<number> }> = ({
  loginTime,
  refresh
}) => {
  const dispatch = useDispatch()
  const { t } = useTranslation();
  const visible = useSelector(
    (state) => state.modal.LoginHoldTimeModal?.visible || false,
  )
  const [form] = Form.useForm()

  // const { data, run: getData } = useRequest(getExpireTimeByMin, {
  //   manual: true
  // })

  const { loading, run } = useRequest(postOvertime, {
    manual: true,
    onSuccess: () => {
      message.success(t("personal:modificationSuccessful"))
      dispatch(hideModal('LoginHoldTimeModal'))
      refresh()
    },
  })

  // useEffect(() => {
  //   if (visible) {
  //     getData()
  //   }
  // }, [visible])

  const setFormVal = useCallback(() => {
    if (loginTime) {
      form.setFields([
        { name: 'loginOvertime', value: Number(loginTime) },
      ])
    }
  }, [form, loginTime])

  useEffect(() => {
    setFormVal()
  }, [loginTime, setFormVal, form])

  return (
    <UIModal
      title={t("personal:modifyLoginSessionDuration")}
      visible={visible}
      onOk={() => {
        form.validateFields().then(({ loginOvertime }) => {
          run(Number(loginOvertime))
          // run({ userId, oldP, newP })
        })
      }}
      // todo: 修改密码后端返回成功失败而非成功后 401
      // todo: 重新登录交互
      okText={t('common.btn.confirm')}
      onCancel={() => {
        dispatch(hideModal('LoginHoldTimeModal'))
        // form.resetFields(['oldP', 'newP', 'confirm'])
      }}
      confirmLoading={loading}
    // width={480}
    >
      <Form form={form} {...FormLayout}>
        <Form.Item
          label={t("personal:sessionDuration")}
          name="loginOvertime"
          extra={t("personal:the_system_will_log_you_out_after_this_duration")}
          rules={[
            {
              required: true,
              message: t('personal:pleaseEnterSessionDuration'),
              min: 1,
              max: 180,
              type: 'number',
              transform: (v) => {
                return v !== '' ? Number(v) : null;
              },
            },
          ]}
        >
          <CustomInputNumber
            autoFocus
            style={{ width: 130 }}
            type={'int'}
            addonAfter={"min"}
          />
        </Form.Item>
      </Form>
    </UIModal>
  )
}
