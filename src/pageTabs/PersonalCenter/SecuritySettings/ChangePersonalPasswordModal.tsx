import React, { useEffect, useState } from 'react'
import { useDispatch, useRequest, useSelector } from 'src/hook'
import { Form, Input, message, Popover } from 'antd'
import { LinkButton, UIModal } from 'src/components'
import { FormLayout } from 'src/constants'
import { changeUserPassword, getSysPasswordPolicy } from 'src/api'
import { hideModal } from 'src/store/extraSlice/modalVisibleSlice'
import { passwordSpecialCharacterValidator, passwordValidator, passwordValidatorSelf } from 'src/util/nameValidator'
import { userLogout } from 'src/api'
import { persistor } from 'src/store'
import { useTranslation } from 'react-i18next';

export const ModalChangePersonalPassword = () => {
  const dispatch = useDispatch()
  const visible = useSelector(
    (state) => state.modal.ModalChangePersonalPassword?.visible || false,
  )
  const { userId } = useSelector((state) => state.login.userInfo)
  const [form] = Form.useForm()
  const [passwordPolicyData, setPasswordPolicyData] = useState<any>()
  const { t } = useTranslation();

  // 获取密码策略
  const { data, run: getSysPasswordPolicyRun } = useRequest(getSysPasswordPolicy, { 
    manual: true,
    onSuccess: (res) => {
      setPasswordPolicyData(res)
    }
  },)

  const { loading, run } = useRequest(changeUserPassword, {
    manual: true,
    onSuccess: () => {
      message.success(t("personal:modificationSuccessful"))
      dispatch(hideModal('ModalChangePersonalPassword'))
      userLogout().then(async () => {
        // https://github.com/rt2zz/redux-persist/issues/1015#issuecomment-494492949
        persistor.purge()
      })
    },
  })

  useEffect(() => {
    if (!userId) return
    getSysPasswordPolicyRun()
    form.setFields([{ name: 'userId', value: userId }])
  }, [form, userId])

  const customValidator = (rule: any, value: string, callback: any) => {
    passwordValidatorSelf(rule, value, callback,
      {
        passwordMax : passwordPolicyData?.passwordMax || 16, 
        passwordMin : passwordPolicyData?.passwordMin || 9, 
        containDigits : passwordPolicyData?.containDigits, 
        containUpperLetters : passwordPolicyData?.containUpperLetters, 
        containLowerLetters : passwordPolicyData?.containLowerLetters, 
        containSymbols : passwordPolicyData?.containSymbols, 
      })
  }

  return (
    <UIModal
      title={t("personal:changePassword")}
      visible={visible}
      onOk={() => {
        form.validateFields().then(({ userId, oldP, newP }) => {
          run({ userId, oldP, newP })
        })
      }}
      // todo: 修改密码后端返回成功失败而非成功后 401
      // todo: 重新登录交互
      okText={t("personal:confirmAndRe-login")}
      onCancel={() => {
        dispatch(hideModal('ModalChangePersonalPassword'))
        form.resetFields(['oldP', 'newP', 'confirm'])
      }}
      confirmLoading={loading}
    // width={480}
    >
      {
        passwordPolicyData && 
        <Form form={form} {...FormLayout}>
          <Form.Item label={t("personal:loginAccount")}>
            <Form.Item name="userId" noStyle>
              <Input disabled hidden />
            </Form.Item>
            {userId}
          </Form.Item>
          <Form.Item
            label={t("personal:oldPassword")}
            name="oldP"
            rules={[{ required: true, message: t("personal:pleaseEnterOldPassword")}]}
          >
            <Input.Password />
          </Form.Item>
          <Form.Item
            label={t("personal:newPassword")}
            name="newP"
            dependencies={['oldP']}
            required
            rules={[
              {
                validator: passwordPolicyData?.systemPasswordStrong ? 
                passwordValidator : 
                (_rule, value, callback) => customValidator(_rule, value, callback),
              },
              { validator: passwordSpecialCharacterValidator },
              ({ getFieldValue }) => ({
                validator(_rule, value) {
                  if (!value || getFieldValue('oldP') !== value) {
                    return Promise.resolve()
                  }
                  return Promise.reject(t("personal:oldPasswordNotEqualNewPassword"))
                },
              }),
            ]}
          >
            <Input.Password />
          </Form.Item>
          <Form.Item
            label={t("personal:confirmNewPassword")}
            name="confirm"
            dependencies={['newP']}
            rules={[
              { required: true, message: t("personal:pleaseConfirmNewPassword") },
              ({ getFieldValue }) => ({
                validator(_rule, value) {
                  if (!value || getFieldValue('newP') === value) {
                    return Promise.resolve()
                  }
                  return Promise.reject(t("personal:passwordsDoNotMatch"))
                },
              }),
            ]}
          >
            <Input.Password />
          </Form.Item>
          <Popover content={t("personal:contactAdminForReset")} trigger="click">
            <LinkButton style={{ marginLeft: '32%' }}>{t("personal:forgotOriginalPassword")}？</LinkButton>
          </Popover>
        </Form>
      }
    </UIModal>
  )
}
