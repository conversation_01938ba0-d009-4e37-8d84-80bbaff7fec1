import React, { useCallback, useMemo, useState, useEffect } from 'react'
import { useSelector, useDispatch, useRequest } from 'src/hook'
import { Form, Input, message, Radio, TreeSelect, Select, Tooltip, Button, Modal } from 'antd'
import {
  addUser,
  editUser,
  setClose,
  setOpenSms,
  setOpenUserOTPKey,
} from 'src/api'
import { useTranslation } from 'react-i18next';
import { FormLayout } from 'src/constants'

interface IProps {
  userId?: string
  option: any
  loginSetVisible: boolean
  setLoginSetVisible: (b: boolean) => void
  refresh: () => void
  setLoginSettingFuncStr: (s: string) => void
}
export const LoginSetting = (props: IProps) => {

  const { userId, option, loginSetVisible, setLoginSetVisible, refresh, setLoginSettingFuncStr } = props
  const [form] = Form.useForm();
  const { t } = useTranslation();

  const handleOk = () => {
    form.validateFields()
      .then(values => {
        if (userId) {
          switch(values.func) {
            case "smsAuth":
            case !values.func:
              setOpenSms(userId).then((res) => {
                if (res) {
                  message.success(t("personal:settingSuccessful"))
                  setLoginSetVisible(false)
                  refresh()
                } else {
                  message.success(t("personal:settingFailed"))
                }
              })
              break
            case "otpAuth":
              setOpenUserOTPKey(userId).then((res) => {
                if (res) {
                  message.success(t("personal:settingSuccessful"))
                  setLoginSetVisible(false)
                  refresh()
                } else {
                  message.success(t("personal:settingFailed"))
                }
              })
              break
            case "closeAuth":
              setClose(userId).then((res) => {
                if (res) {
                  message.success(t("personal:settingSuccessful"))
                  setLoginSetVisible(false)
                  refresh()
                  setLoginSettingFuncStr("")
                } else {
                  message.success(t("personal:settingFailed"))
                }
              })
              break
          }
        }
      })
      .catch(errorInfo => {
        console.log('登录方式-表单校验失败:', errorInfo);
      });
  };
  
  return (
    <Modal
      visible={loginSetVisible}
      title={t("personal:loginSettings")}
      onCancel={() => setLoginSetVisible(false) }
      onOk={handleOk}
    >
      <Form form={form}>
        <Form.Item
          name="func" 
          label={t("personal:two-FactorAuthenticationType")}
          rules={[
            {
              required: true,
            },
          ]}>
        <Select
          // defaultValue="smsAuth"
          style={{ width: 120 }}
          options={option}
        />
        </Form.Item>
      </Form>
      <div>{t("personal:configured_Two-Factor_Authentication_method_during_login")}</div>
    </Modal>
  );
}
