import React, { useCallback, useEffect, useRef, useState } from 'react'
import { useDispatch, useRequest, useSelector } from 'src/hook'
import { Button, Form, Input, Steps } from 'antd'
import { UIModal } from 'src/components'
import { FormLayout } from 'src/constants'
import { hideModal, showModal } from 'src/store/extraSlice/modalVisibleSlice'
import { getUserOTPKey, refreshUserOTPKey, bindUserOTPKey } from 'src/api'
import * as OriginQRCode from 'src/assets/js/qrcode.js'
import styles from './index.module.scss'
import classNames from 'classnames'
import { useTranslation } from 'react-i18next';
import i18n from 'i18next';

const QRCode = OriginQRCode.default as any
const steps = [
  {
    title: i18n.t("personal:identityVerification"),
  },
  {
    title: i18n.t("personal:installApplication"),
  },
  {
    title: i18n.t("personal:bindOTP"),
  },
  {
    title: i18n.t("personal:complete"),
  }
];

export const ModalOtpBinding: React.FC<{ refresh: () => Promise<void> }> = ({
  refresh
}) => {
  const dispatch = useDispatch()
  const visible = useSelector(
    (state) => state.modal.OtpBindingtModal?.visible || false,
  )
  const { userId } = useSelector((state) => state.login.userInfo)
  const { loginType } = useSelector(
    (state) => state.personalCenter)

  const [current, setCurrent] = useState(1)
  const [form] = Form.useForm()
  const qrRef = useRef() as any
  const qrDom = document.getElementById('qrcode')

  const [checkCodeKey, setCheckCodeKey] = useState('')
  const { t } = useTranslation();

  //初次 二维码
  const { run: getInitialCheckCodeKey } = useRequest(getUserOTPKey, {
    manual: true,
    formatResult: ({ optKey }) => optKey,
    onSuccess: (optKey: string) => {
      setCheckCodeKey(optKey)
    },
  })
  // 刷新二维码
  const { run: getRefreshCheckCodeKey } = useRequest(refreshUserOTPKey, {
    manual: true,
    formatResult: ({ optKey }) => optKey,
    onSuccess: (optKey: string) => {
      setCheckCodeKey(optKey)
    },
  })

  // 绑定otpKey
  const {run: bindOtpKey} = useRequest(bindUserOTPKey, {
    manual: true,
    onSuccess(data) {
      setCurrent(3)
    }
  })

  useEffect(() => {
    if (visible && userId && current == 2) {
      getInitialCheckCodeKey(userId)
    }
    if (userId) {
      let arr=userId.split('');
      arr.splice(2,3,"*","*","*");
      let str=arr.join('');
    }
  }, [userId, current, visible])

  const createQR = useCallback(() => {
    if (qrRef.current) {
      qrRef.current?.clear()
    }
    if (qrDom) {
      qrDom.innerHTML = ''
    }

    if (!qrDom) return
    let uri = 'otpauth://totp/'
    uri += encodeURIComponent(userId ? `${userId}-otp` : '')
    const code = checkCodeKey?.slice(0, 32)
    uri += '?secret=' + code
    uri += '&algorithm=SHA1'
    uri += '&digits=6'
    uri += '&period=30'

    var qrImg = new QRCode(qrDom, {
      text: uri,
      width: 200,
      height: 200,
      correctLevel: QRCode.CorrectLevel.H,
    })
    qrRef.current = qrImg
  }, [checkCodeKey, qrDom, qrRef, userId])

  useEffect(() => {
    if (checkCodeKey) {
      createQR()
    }
  }, [createQR, checkCodeKey, qrDom])

  useEffect(() => {
    setCurrent(1)
    form.resetFields()
  }, [visible])

  return (
    <UIModal
      title={t("personal:bindOTP")}
      visible={visible}
      footer={[]}
      onCancel={() => {
        dispatch(hideModal('OtpBindingtModal'))
      }}
    >
      <>
        <Steps current={current} labelPlacement="vertical">
          {steps.map(({ title }, index) => (
            <Steps.Step title={title} key={index} />
          ))}
        </Steps>
        {
          current === 1 && (
            <div className={styles.boxMargin}>
              <div style={{ marginLeft: '20%' }}>
                <p className={styles.otpRed}>{t("personal:bindSecurityDevice", {userId})}</p>
                <p>{t("personal:downloadInstallAuthyGoogleAuthenticator")}</p>
                <p>{t("personal:iPhoneSearchAppStore")}</p>
                <p>{t("personal:androidSearchGooglePlayStore")}</p>
                <h3 className={styles.otpHint}>{t("personal:afterInstallationClickNext")}</h3>
              </div>
              <div className={styles.buttonRight}>
                <Button className={styles.mr10} 
                 disabled={loginType !== 'cq'}
                 onClick={() => {
                  dispatch(hideModal('OtpBindingtModal'))
                  dispatch(showModal('VerificationModal'))

                }}>
                 { t('common.btn.pre')}
                </Button>
                <Button type="primary" onClick={() => {
                  setCurrent(2)
                }}>
                  {t('common.btn.next')}
                </Button>
              </div>
            </div>
          )
        }
        {
          current === 2 && (
            <div className={styles.boxMargin}>
              <div >
                <p className={classNames(
                  styles.otpRed,
                  styles.textAlignCenter
                )}>{t("personal:bindSecurityDevice", {userId})}</p>
                <p className={styles.textAlignCenter}>{t("personal:to_scan_the_QR_code_below_and_obtain_a_6-digit_verification_code")}</p>

                <div
                  id="qrcode"
                  style={{ display: 'flex', justifyContent: 'center', cursor: 'pointer', width: 100, height: 100, marginLeft: '44%' }}
                  onClick={() => userId && getRefreshCheckCodeKey(userId)}
                ></div>
                <Form form={form} {...FormLayout} style={{marginLeft: '36%', marginTop: '10px'}}>
                  <Form.Item
                    name='otpPassword'
                    rules={[{ required: true, message: t("personal:pleaseEnterOTPVerificationCode")}]}
                  >
                    <Input placeholder= {t("personal:otpVerificationCode")} />
                  </Form.Item>
                </Form>
              </div>
              <div className={styles.buttonRight}>
              <Button className={styles.mr10}  onClick={() => {
                  setCurrent(1)
                  
                }}>
                  {t("personal:previous")}
                </Button>
                <Button type="primary" onClick={() => {
                  form.validateFields().then((values) => {
                    userId && bindOtpKey({userId, otpPassword: values?.otpPassword})
                  })
                }}>
                  {t("personal:next")}
                </Button>
              </div>
            </div>
          )
        }
        {current === 3 && (
          <div className={styles.boxMargin}>
            <div className={styles.otpBindBack}>
              <h3 className={styles.otpMessageTitle}>{t("personal:bindSuccessful")}</h3>
              <p className={styles.otpMessageCon}>{t("personal:bindingSecurityDevice")}</p>
              <p className={styles.otpMessageCon}>{t("personal:doNotDeleteUninstallApp")}</p>
              <p className={styles.otpMessageCon}>{t("personal:unbindOTPFirst")}</p>
            </div>
            <div className={styles.buttonRight}>
              <Button type="primary" onClick={() => {
                form.validateFields().then(() => {
                  dispatch(hideModal('OtpBindingtModal'))
                  refresh()
                  // setCurrent(3)
                })
              }}>
                {t("personal:complete")}
              </Button>
            </div>
          </div>
        )}
      </>
    </UIModal>
  )
}
