/**
 * 手机号解绑
 */
import React, { useState, useEffect, useRef } from 'react'
import { Form, Input, Tabs, Button, message } from 'antd'
import { useCounter, useInterval } from 'react-use' 
import { useRequest, useSelector } from 'src/hook'
import { UIModal } from 'src/components'
import { sendPhoneCode, unBindPhone, sendEmailVerificationCode } from 'src/api'
import { useTranslation } from 'react-i18next';

const FormLayout = {
  labelCol: { span: 4 },
  wrapperCol: { span: 10 },
}

interface IProps {
  visible: boolean
  setVisible: (b: boolean) => void
  [p: string]: any
}
const PhoneUnbindModal = ({visible, setVisible, refresh}: IProps) => {
  const [form] = Form.useForm()
  const isLangEn = useSelector(state => state.login.locales) === 'en'
  const { userPhone, userEmail, loginType } = useSelector((state) => state.personalCenter)
  const { userId } = useSelector((state) => state.login.userInfo)
  const [tabsKey, setTabsKey] = useState<string>('1')
  const [phoneCount, { dec:phoneDec, reset: phoneReset, set: phoneSet }] = useCounter(60, 60, 0)
  const { t } = useTranslation();

  useInterval(() => phoneDec(1), 1000)
  useEffect(() => {
    phoneSet(0)
  }, [phoneSet])

  const [emailCount, { dec: emailDec, reset: emailReset, set: emailSet }] = useCounter(60, 60, 0)
  useInterval(() => emailDec(1), 1000)
  useEffect(() => {
    emailSet(0)
  }, [emailSet])

  useEffect(() => {
    if (loginType === 'cq') {
      setTabsKey('1')
    } else if (userPhone) {
      setTabsKey('2')
    }else if (userEmail){
      setTabsKey('3')
    }
  },[loginType, userPhone, userEmail])

  const handleTabChange = (val: string) => {
    setTabsKey(val)
  }

  /** 发送手机验证码 */
  const { run: sendPhoneCodeRun } = useRequest(sendPhoneCode, {
    manual: true,
    onSuccess() {
      message.success(t("personal:verification_code_has_been_sent"))
      phoneReset()
    }
  })

  /** 发送邮箱验证码 */
  const { run: sendEmailCode } = useRequest(sendEmailVerificationCode, {
    manual: true,
    onSuccess(data) {
      message.success(data?.message)
      emailReset()
    }
  })
  
  const handleSubmit = () => {
    form.validateFields().then((values: any) => {
      console.log(values)
      let params: any = { }
      if (tabsKey === '1') {
        params = {
          type:'PWD',
          password: values?.password
        }
      }else if(tabsKey === '2'){
        params = {
          type:'SMS',
          phoneCode: values?.phoneCode
        }
      }else {
        params = {
          type:'EMAIL',
          emailCode: values?.emailCode
        }
      }
      unBindPhone(params).then(() => {
        message.success(t("personal:unBindSuccessful"))
        setVisible(false)
        refresh && refresh()
        form.resetFields();
      }).catch((err: any) => {
        console.error(t("personal:unBindFailed"), err)
      })
    })
  }

  const handleCancel = () => {
    form.resetFields()
    setVisible(false)
  }

  const getValidateCode = () => {
    if(!userPhone){
      message.warning(t("personal:no_Phone_Number_is_available"))
      return
    }
    sendPhoneCodeRun(userPhone)?.then().catch((err: any)=>{console.error(t("personal:sendVerificationCode") +" "+ t("personal:fail"), err)})
  }

  const AccountPassword = () => {
    return (
      <Form form={form} {...FormLayout}>
        <Form.Item
          label={t("personal:account")}
          name="userId"
        >
          <span>{userId}</span>
        </Form.Item>
        <Form.Item
          label={t("personal:password")}
          name="password"
          rules={[{ required: true, message: t("personal:pleaseEnterPassword") }]}
        >
          <Input.Password autoFocus />
        </Form.Item>
      </Form>
    )
  }

  const PhoneElement = () => {
    return (
      <Form form={form} {...FormLayout}>
        <Form.Item
          label={t("personal:phoneNumberPlus")}
          name="phone"
        >
          <span>{userPhone && userPhone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')}</span>
        </Form.Item>
        <Form.Item
          label={t("personal:verificationCode")}
        >
          <div className="flex">
            <Form.Item
              name="phoneCode"
              rules={[{ required: true, message: t("personal:pleaseVerificationCode") }]}
            >
              <Input autoFocus />
            </Form.Item>
            <Button
              disabled={!!phoneCount}
              type='primary'
              className="ml10"
              onClick={getValidateCode}>
              {phoneCount ? `${phoneCount}s` : t("personal:getVerificationCode") }
            </Button>
          </div>
        </Form.Item>
        <div style={{ marginLeft: '35px' }}>
          <p>1. {t("personal:the_phone_number_for_receiving_the_verification_code")}</p>
          <p>2. {t("personal:you_can_retrieve_it_in_your_SMS")}</p>
        </div>
      </Form>
    )
  }

  const EmailElement = () => {
    return (
      <Form form={form} {...FormLayout}>
        <Form.Item
          label={t("personal:email")}
          name="email"
        >
          <span>{userEmail}</span>
        </Form.Item>
        <Form.Item
          label={t("personal:verificationCode")}

        >
          <div className="flex">
            <Form.Item
              name="emailCode"
              rules={[{ required: true, message: t("personal:pleaseVerificationCode") }]}
            >
              <Input autoFocus />
            </Form.Item>
            <Button
              className="ml10"
              type='primary'
              disabled={!!emailCount}
              onClick={() => {
                sendEmailCode()?.then().catch((err)=>{console.error(t("personal:sendVerificationCode") +" "+ t("personal:fail"), err)})
              }}>
              {emailCount ? `${emailCount}s` : t("personal:sendVerificationCode")}
            </Button>
          </div>
        </Form.Item>
        <div style={{ marginLeft: '35px' }}>
          <p>1. {t("personal:the_email_address_for_receiving_the_verification_code")}</p>
          <p>2. {t("personal:you_can_retrieve_it_in_your_Email")}</p>
        </div>
      </Form>
    )
  }

  return (
    <UIModal 
      title={t("personal:unbindPhone")}
      visible={visible} 
      okText={t("personal:unbindImmediately")}
      onCancel={handleCancel}
      onOk={handleSubmit}
    >
      <div style={{display:'flex', margin: '10px 0'}}>
        <div style={{ flex: 2 }}>
          <Tabs
            tabPosition={'left'}
            activeKey={tabsKey}
            onChange={handleTabChange}
          >
            {
              loginType === 'cq' &&
              <Tabs.TabPane tab={t("personal:accountPasswordVerification")} key="1" />
            }
            {
              userPhone && !isLangEn &&
              <Tabs.TabPane tab={t("personal:phoneNumberVerification")} key="2" />
            }
            {
              userEmail && 
              <Tabs.TabPane tab={t("personal:emailVerification")} key="3" />
            }
          </Tabs>
        </div>
        <div style={{ flex: 8, background: '#F7F9FC', padding: '10px 0' }}>
          {
            tabsKey === '1' && <AccountPassword />
          }
          {
            tabsKey === '2' && <PhoneElement />
          }
          {
            tabsKey === '3' && <EmailElement />
          }
        </div>
      </div>
    </UIModal>
  )
}
export default PhoneUnbindModal