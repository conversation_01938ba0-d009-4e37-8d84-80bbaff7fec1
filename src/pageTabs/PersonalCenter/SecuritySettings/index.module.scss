.pt20 {
  padding-top: 20px;
}
.pt10 {
  padding-top: 10px;
}
.textAlignCenter {
  text-align: center;
}
.mt20 {
  margin-top: 20px;
}
.ml10 {
  margin-left: 10px;
}
.mr10 {
  margin-right: 10px;
}
.mr80 {
  margin-right: 80px;
}
.disflex {
  display: flex;
}
.securityWrap {
  display: flex;
  .disable {
    color: rgba(0, 0, 0, 0.25);
    cursor: not-allowed;
  }
}
.securityItem {
  flex: 1;
  align-items: center;
}
.securityItemTitle {
  font-size: 16px;
  color: #0F244C;
}
.securityIcon {
  width: 15px;
  display: inline-block;
  margin: 0 11px 0 0;
}
.securityIconSuccess {
  color: #209C22;
}
.securityIconError {
  color: #EA0000;
}
.securityItemDescribe {
  font-size: 14px;
  color: #868FA3;
  margin: 0 0 0 26px;
}
.boxMargin {
  margin: 20px 10px;
}
.buttonRight {
  text-align: right;
  margin-top: 20px;
}
.otpBindBack {
  background: #E2F4E9;
  padding: 20px 15px;
}
.otpMessageTitle {
  font-weight: 700;
  font-size: 18px;
}
.otpMessageCon {
  font-size: 14px;
  color: #333;
}
.otpRed {
  color: #FF3232;
  font-size: 14px;
}
.otpHint {
  color: #3D3D3D;
  font-size: 18px;
}
.securityIconSuccessIcon {
  color: #209C22;
  font-size: 56px;
}
.verificationTab {
  :global {
    .ant-tabs-content-holder {
      border: none;
    }
    .ant-tabs-ink-bar-animated {
      height: 0;
    }
  }
}

.qrCodeBox {
  display: flex;
  justify-content: center;

  .otherAccountBox {
    text-align: right;
    display: block;
    width: 100%;

    .otherAccountContent {
      width: 400px;
      margin: 50px auto;
      text-align: left;
    }

    :global {
      .ant-btn+.ant-btn {
        margin: 8px;
      }
    }
  }
}
