import React, { useCallback, useEffect, useMemo, useState } from 'react'
import { useDispatch, useRequest, useSelector } from 'src/hook'
import { CheckCircleOutlined, QuestionCircleOutlined } from '@ant-design/icons'
import { Card, Divider, Tooltip } from 'antd'
import { LinkButton } from 'src/components'
import classnames from 'classnames'
import { ModalChangePersonalPassword } from './ChangePersonalPasswordModal'
import { ModalChangeLoginSettings } from './ChangeLoginSettingsModal'
import { ModalChangeLoginHodlTime } from './ChangeLoginHoldTimeModal'
import { ModalVerification } from './VerificationModal'
import { ModalEmail } from './EmailModal'
import { ModalOtpBinding } from './OtpBindingModal'
import {
  getCurrentLoginType,
  getEmailStatus,
  getExpireTimeByMin,
  getOtpStatus,
  getUserEmail,
  canEditPhone,
  canUnbindPhone,
  getBindPhoneStatus,
  getSmsStatus,
  checkWwBoundChat,
  getForceLogin,
  getSettingType,
  getSysPasswordPolicy,
  getRetentionTimeType,
} from 'src/api'
import { showModal } from 'src/store/extraSlice/modalVisibleSlice'
import {
  setEmailBindFlag,
  setIsBindEmail,
  setLoginType,
  setUserEmail,
  setSmsBindFlag,
  setIsBindPhone,
  setUserPhone,
  setIsBindOtp,
  setIsOpenOtp,
  setIsOpenSms,
} from '../personalCenterSlice'
import styles from './index.module.scss'
import { ModalBindPhone } from './BindPhoneModal'
import { LoginSetting } from './LoginSetting'
import { WwChatBindModal } from './WwChatBindModal'
import { WwChatUnBindModal } from './WwChatUnBindModal'
import PhoneUnbindModal from './PhoneUnbindModal'
import { useTranslation } from 'react-i18next';

export const SecuritySettings = () => {
  const dispatch = useDispatch()
  const isLangEn = useSelector((state) => state.login.locales) === 'en';
  const { t } = useTranslation();
  const { userInfo, wwLoginInfo, isLoginCtrlByManager } = useSelector((state) => state.login)
  const { userId } = useSelector((state) => state.login.userInfo)
  const [verificationMark, setVerificationMark] = useState('')
  const [loginSetVisible, setLoginSetVisible] = useState<boolean>(false)
  const [loginSettingFuncStr, setLoginSettingFuncStr] = useState<string | undefined>('')
  const [showUnBindPhoneModal, setShowUnBindPhoneModal] = useState(false)
  const [otpForceStatusData, setOtpForceStatusData] = useState<boolean>(false)
  // 登录设置
  const [loginSetting, setLoginSetting] = useState<string>('')

  const { smsBindFlag, isBindPhone, isBindOtp, isOpenOtp, isOpenSms, loginType } = useSelector(
    (state) => state.personalCenter,
  )

  const smsContent = t('systemManagement.system.other.smsLogin')
  const otpContent = t('systemManagement.system.other.otpLogin')
  /** 检测手机号是否绑定 */
  const { data: bindPhoneStatusData, loading: bindPhoneLoading, run: bindPhoneStatusDataRun } = useRequest(getBindPhoneStatus, {
    onSuccess(data) {
      dispatch(setIsBindPhone(data))
      if (userInfo?.telephone) {
        dispatch(setUserPhone(userInfo?.telephone))
      }
    }
  })


  /* 查看消息开启状态 */
  const { data: smsStatusData, loading: smsStatusLoading, run: getOpenSmsStatusRun } = useRequest(getSmsStatus, {
    onSuccess(res) {
      dispatch(setSmsBindFlag(res?.hasBind))
      if (res?.hasOpen) {
        setLoginSettingFuncStr(smsContent)
      }
      setIsOpenSms(res?.hasOpen)
    }
  })

  /* 查看OTP开启状态 */
  const { data: otpStatusData, loading: otpLoading, run: getOtpStatusRun } = useRequest(getOtpStatus, {
    onSuccess(res) {
      dispatch(setIsBindOtp(res?.hasBind))
      if (res?.hasOpen) {
        setLoginSettingFuncStr(otpContent)
      }
      setIsOpenOtp(res?.hasOpen)
    }
  })

  // 获取登录设置
  const { loading: settingTypeLoading } = useRequest(getSettingType, {
    onSuccess(res) {
      const loginSetting = res?.loginSetting

      setLoginSetting(loginSetting)

      if (loginSetting === "FORCE_SMS_LOGIN") {
        setLoginSettingFuncStr(smsContent)
      } else if (loginSetting === "FORCE_OTP_LOGIN") {
        setLoginSettingFuncStr(otpContent)
      }
    }
  })

  // 是否能够修改手机号 true不能修改
  const { data: editPhone, loading: editPhoneLoading, run: runCanEditPhone } = useRequest(canEditPhone)

  const { data: unbindPhone, loading: unbindPhoneLoading, run: runCanUnbindPhone } = useRequest(canUnbindPhone)

  /** 获取email */
  const { data: getUserEmailData, run: getUserEmailDataRun } = useRequest(getUserEmail, {
    onSuccess(data) {
      dispatch(setUserEmail(data?.email))
    }
  })

  /** 获取邮箱状态 */
  const { data: emailStatusData, loading: emailLoading, run: emailStatusDataRun } = useRequest(getEmailStatus, {
    onSuccess(data) {
      dispatch(setEmailBindFlag(data?.userHasBindEmail))
    }
  })

  const { data: loginTypeData, loading: loginTypeLoading } = useRequest(getCurrentLoginType, {
    onSuccess(data) {
      dispatch(setLoginType(data))
    }
  })

  /** 获取企微是否绑定状态 */
  const { data: isBoundWw, run: checkIsBoundWw } = useRequest(checkWwBoundChat, { manual: true })

  /**  登录保持时间 */
  const { data: loginTime, run: loginTimeRefresh } = useRequest(getExpireTimeByMin)

  /** 查看OTP全局开启状态 */
  const { data } = useRequest(getForceLogin, {
    onSuccess: (res) => {
      setOtpForceStatusData(res?.forceOtpBing)
    }
  })

  // 获取密码策略
  const { data: passwordPolicyData, run: getSysPasswordPolicyRun } = useRequest(getSysPasswordPolicy, {
    manual: true,
    onSuccess: (res) => { }
  },)

  useEffect(() => {
    if (wwLoginInfo?.enable) {
      checkIsBoundWw()
    }
    getSysPasswordPolicyRun()
  }, [])

  const refreshLoginSetting = useCallback(async () => {
    getOpenSmsStatusRun()
    getOtpStatusRun()
  }, [emailStatusData, getUserEmailData])

  const refreshEmail = useCallback(async () => {
    emailStatusDataRun()
    getUserEmailDataRun()
  }, [emailStatusData, getUserEmailData])

  const refreshPhone = () => {
    bindPhoneStatusDataRun();
    runCanEditPhone();
    runCanUnbindPhone();
  }

  const { data: RetentionTimeType, loading: retentionTimeLoading } = useRequest(getRetentionTimeType)

  const loginSettingFuncOption = useMemo(() => {
    let option = [
      {
        value: 'closeAuth',
        label: t("personal:disableAuthentication"),
      },
    ]
    if (isBindOtp) {
      option.push({
        value: 'otpAuth',
        label: t("personal:otpAuthentication"),
      })
    }
    if (isBindPhone) {
      option.push({
        value: 'smsAuth',
        label: t("personal:smsAuthentication"),
      })
    }
    return option
  }, [isBindPhone, isBindOtp])

  const renderLoginSetShow = useMemo(() => {
    // 非用户自定义的话禁用
    const notCustomSetting = !(loginSetting === "USER_CUSTOM")

    const getTitle = () => {
      if (isLoginCtrlByManager) {
        return t('personal:LoginSettingManagerTip')
      } else if (loginSetting === "FORCE_SMS_LOGIN") {
        return t('personal:forceSMSLogin')
      } else if (loginSetting === "FORCE_OTP_LOGIN") {
        return t('personal:forceOTPLogin')
      }
      return null
    }
    
    return (
      <Tooltip
        title={getTitle()}
      >
        <LinkButton
          onClick={() => {
            if (notCustomSetting) return
            userId && setLoginSetVisible(true)
          }}
          className={notCustomSetting ? styles.disable : ''}
        >

          {t("personal:setting")} {loginSettingFuncStr ? "[" + loginSettingFuncStr + "]" : ""}
        </LinkButton>
      </Tooltip>
    )
  }, [loginSetting, loginSettingFuncStr, userId])

  const renderLoginSetting = useMemo(() => {
    return (
      <LoginSetting
        userId={userId}
        option={loginSettingFuncOption}
        loginSetVisible={loginSetVisible}
        setLoginSetVisible={setLoginSetVisible}
        refresh={refreshLoginSetting}
        setLoginSettingFuncStr={setLoginSettingFuncStr}
      />
    )
  }, [loginSetVisible, loginSettingFuncOption, refreshLoginSetting, userId])

  const getPassWordTooltip = useMemo(() => {
    const customValidator = () => {
      const {
        passwordMax = 16,
        passwordMin = 9,
        containDigits = false,
        containUpperLetters = false,
        containLowerLetters = false,
        containSymbols = false,
      } = passwordPolicyData || {};
      let includeParam =
        (containDigits ? t("personal:number") + "、" : "") +
        (containLowerLetters ? t("personal:lowercaseLetter") + "、" : "") +
        (containUpperLetters ? t("personal:uppercaseLetter") + "、" : "") +
        (containSymbols ? t("personal:specialCharacter") + "、" : "");
      let lastIndex = includeParam.lastIndexOf("、");
      let result = includeParam.substring(0, lastIndex) + includeParam.substring(lastIndex + 1) || "-";
      return t("personal:systemPasswordStrong",result,passwordMin,passwordMax)
    }

    if (passwordPolicyData?.systemPasswordStrong) {
      return t("personal:systemPasswordStrongDefault")
    } else {
      return customValidator()
    }
  }, [passwordPolicyData])

  return (
    <section className="cq-new-card flow-card" style={{ marginBottom: '10.5px' }}>
      <div className="cq-card__headerbar">
        <h3 className="cq-card__title">{t("personal:securitySettings")}</h3>
      </div>
      <section className="card__content">
        <Card
          bordered={false}
          loading={
            otpLoading || smsStatusLoading ||
            bindPhoneLoading || emailLoading ||
            loginTypeLoading || settingTypeLoading ||
            editPhoneLoading || unbindPhoneLoading || 
            retentionTimeLoading
          }
        >
          <div className={styles.securityWrap}>
            <div className={styles.securityItem}>
              <p className={styles.securityItemTitle}>  <CheckCircleOutlined className={classnames(
                styles.securityIcon,
                styles.securityIconSuccess
              )} />
                {t("personal:loginPassword")}
              </p>
              <p className={styles.securityItemDescribe}>{t("personal:loginPasswordDescribe",{getPassWordTooltip})}</p>
            </div>
            <LinkButton
              onClick={() => {
                dispatch(showModal('ModalChangePersonalPassword'))
              }}
            >
              {t("personal:changePassword")}
            </LinkButton>
          </div>

          {userInfo?.version !== 'community' && !isLangEn &&
            <>
              <Divider />
              <div className={styles.securityWrap}>
                <div className={styles.securityItem}>
                  <p className={styles.securityItemTitle}>  <CheckCircleOutlined className={classnames(
                    styles.securityIcon,
                    isBindPhone ? styles.securityIconSuccess : styles.securityIconError // to do
                  )} />
                    {!isBindPhone &&
                      <span className={styles.securityIconError}>
                        {t("personal:noBind")}
                        &nbsp;&nbsp;
                      </span>
                    }
                    {t("personal:phoneBinding")}
                  </p>
                  <p className={styles.securityItemDescribe}>{t("personal:phoneBindingDescribe")}</p>
                </div>

                <LinkButton
                  disabled={!editPhone || isLoginCtrlByManager}
                  onClick={() => {
                    if (isBindPhone && unbindPhone) {
                      setShowUnBindPhoneModal(true)
                    } else {
                      setVerificationMark('phone')
                      //马上绑定
                      if (loginType !== 'cq' && !isBindPhone) {
                        dispatch(showModal('PhoneEditModal'))
                      } else {
                        dispatch(showModal('VerificationModal'))
                      }
                    }
                  }}
                >
                  {/* 不是管理员 则需要判断editPhone */}
                  {
                    !editPhone
                      ? t("personal:editPhone")
                      : !isBindPhone
                        ? t("personal:bindNow")
                        : unbindPhone
                          ? t("personal:unbind")
                          : t("personal:changePhoneNumber")
                  }
                </LinkButton>
              </div>
            </>
          }
          <Divider />
          {/* wwwwwww */}
          {
            userInfo?.version !== 'community' && wwLoginInfo?.enable && !isLangEn && <div>
              <div className={styles.securityWrap}>
                <div className={styles.securityItem}>
                  <p className={styles.securityItemTitle}>
                    <CheckCircleOutlined className={classnames(
                      styles.securityIcon,
                      isBoundWw ? styles.securityIconSuccess : styles.securityIconError
                    )} />
                    {
                      !isBoundWw && <span className={styles.securityIconError}>[{t("personal:noBind")}] &nbsp;&nbsp;</span>
                    }
                    {t("personal:enterpriseWeChatBinding")}
                  </p>
                  <p className={styles.securityItemDescribe}>{t("personal:enterpriseWeChatBindingDescribe")}</p>
                </div>
                {
                  <LinkButton
                    disabled={isLoginCtrlByManager}
                    onClick={() => {
                      if (!isBoundWw && loginTypeData !== 'cq') {
                        dispatch(showModal('WwChatBindModal'))
                      } else {
                        dispatch(showModal('VerificationModal'))
                      }
                      setVerificationMark(isBoundWw ? 'unBindWwChat' : 'bindWwChat')
                    }}
                  >
                    {isBoundWw ? t("personal:unbind") : t("personal:bindNow")}
                  </LinkButton>
                }
              </div>
              <Divider />
            </div>
          }
          {
            emailStatusData?.systemEmailEnable && <div>
              <div className={styles.securityWrap}>
                <div className={styles.securityItem}>
                  <p className={styles.securityItemTitle}>  <CheckCircleOutlined className={classnames(
                    styles.securityIcon,
                    emailStatusData?.userHasBindEmail ? styles.securityIconSuccess : styles.securityIconError
                  )} />
                    {
                      !emailStatusData?.userHasBindEmail && <span className={styles.securityIconError}>[{t("personal:noBind")}] &nbsp;&nbsp;</span>
                    }
                    {t("personal:emailBinding")}
                  </p>
                  <p className={styles.securityItemDescribe}>{t("personal:emailBindingDescribe")}</p>
                </div>
                {
                  !emailStatusData?.userHasBindEmail && <LinkButton
                    onClick={() => {
                      dispatch(setIsBindEmail(true))
                      if (!emailStatusData?.userHasBindEmail && loginTypeData !== 'cq') {
                        dispatch(showModal('EmailEditModal'))
                      } else {
                        setVerificationMark('email')
                        dispatch(showModal('VerificationModal'))
                      }
                    }}
                  >
                      {t("personal:bindNow")}
                  </LinkButton>
                }
                {
                  emailStatusData?.userHasBindEmail && <LinkButton
                    onClick={() => {
                      dispatch(setIsBindEmail(false))
                      if (!emailStatusData?.userHasBindEmail && loginTypeData !== 'cq') {
                        dispatch(showModal('EmailEditModal'))
                      } else {
                        setVerificationMark('email')
                        dispatch(showModal('VerificationModal'))
                      }
                    }}
                  >
                      {t("personal:unbind")}
                  </LinkButton>
                }
              </div>
              <Divider />
            </div>
          }
          <div className={styles.securityWrap}>
            <div className={styles.securityItem}>
              <p className={styles.securityItemTitle}>  <CheckCircleOutlined className={classnames(
                styles.securityIcon,
                loginSettingFuncStr ? styles.securityIconSuccess : styles.securityIconError
              )} />
                {
                  !loginSettingFuncStr && <span className={styles.securityIconError}>[{t("personal:notEnabled")}] &nbsp;&nbsp;</span>
                }
                {t("personal:loginSettings")}
              </p>
              <p className={styles.securityItemDescribe}> {t("personal:to_enhance_your_account_security")}</p>
            </div>
            {renderLoginSetShow}
            {renderLoginSetting}
          </div>
          <Divider />
          <div className={styles.securityWrap}>
            <div className={styles.securityItem}>
              <p className={styles.securityItemTitle}>  <CheckCircleOutlined className={classnames(
                styles.securityIcon,
                otpStatusData?.hasBind ? styles.securityIconSuccess : styles.securityIconError
              )} />
                {
                  !otpStatusData?.hasBind && <span className={styles.securityIconError}>[{t("personal:noBind")}] &nbsp;&nbsp;</span>
                }
                {t("personal:otpSettings")}
              </p>
              <p className={styles.securityItemDescribe}>{t("personal:after_setting_up_virtual_OTP")}</p>
            </div>
            <LinkButton
              // otp全局设置开启并且绑定过，则禁用
              disabled={(otpForceStatusData && otpStatusData?.hasBind) || isLoginCtrlByManager}
              onClick={() => {
                if (!otpStatusData?.hasBind) {
                  // 如果用户没有绑定邮箱 并且登录类型不是cq 就跳过验证
                  if (!emailStatusData?.userHasBindEmail && loginTypeData !== 'cq') {
                    dispatch(showModal('OtpBindingtModal'))
                  } else {
                    setVerificationMark('otp')
                    dispatch(showModal('VerificationModal'))
                  }
                } else {
                  setVerificationMark('otpUnBind')
                  dispatch(showModal('VerificationModal'))
                }
              }}
            >
              {!otpStatusData?.hasBind ? t("personal:settingNow") : t("personal:unbind")}

            </LinkButton>
            {
              otpForceStatusData && otpStatusData?.hasBind && <Tooltip placement="topRight" title={t("personal:no_unbind")}>
                <QuestionCircleOutlined style={{ lineHeight: '24px' }} />
              </Tooltip>
            }
          </div>
          <Divider />
          <div className={styles.securityWrap}>
            <div className={styles.securityItem}>
              <p className={styles.securityItemTitle}>  <CheckCircleOutlined className={classnames(
                styles.securityIcon,
                true ? styles.securityIconSuccess : styles.securityIconError
              )} /> {t("personal:sessionDuration")} </p>
              <p className={styles.securityItemDescribe}> {t("personal:you_can_set_the_login_session_duration", {loginTime})} </p>
            </div>
            <LinkButton
              onClick={() => {
                dispatch(showModal('LoginHoldTimeModal'))
              }}
              disabled={RetentionTimeType?.flag}
            >
              {t("personal:modificationTime")}
            </LinkButton>
          </div>
        </Card>
      </section>
      <ModalChangePersonalPassword />
      <ModalChangeLoginSettings />
      <ModalBindPhone record={userInfo} refresh={refreshPhone} />
      <ModalChangeLoginHodlTime loginTime={loginTime} refresh={loginTimeRefresh} />
      <ModalVerification
        markFlag={verificationMark}
        refreshLoginSetting={refreshLoginSetting}
        refresh={refreshLoginSetting}
        refreshEmail={refreshEmail}
        setLoginSettingFuncStr={setLoginSettingFuncStr}
      />
      <ModalEmail record={userInfo} refresh={refreshEmail} />
      <ModalOtpBinding refresh={refreshLoginSetting} />
      <WwChatBindModal refresh={checkIsBoundWw} />
      <WwChatUnBindModal refresh={checkIsBoundWw} />
      {/* 手机解绑 */}
      {
        showUnBindPhoneModal &&
        <PhoneUnbindModal
          visible={true}
          setVisible={setShowUnBindPhoneModal}
          refresh={refreshPhone}
        />
      }
    </section>
  )
}
