
.personalTabsTitle {
  font-size: 14px;
  margin: 11px 0 0 10px;
  color: #667084;
}
.personalTabs {
  :global {
    .ant-tabs-nav{
      width: 100% ;
    }
    .ant-tabs-nav-list .ant-tabs-tab {
      margin-bottom: 4px !important;
      color: #667084;
      border-radius: 4px !important;
      background: #fff;
      font-size: 14px;
      height: 32px;
      border: none;

    }
    .ant-tabs-nav-list .ant-tabs-tab:hover {
      color: #667084;
    }
    .ant-tabs-content-holder {
      border: none;
    }
    .ant-tabs-nav-list .ant-tabs-tab {
      color:#667084;
      .anticon {
        color: #B2B9C8;
        font-size: 16px;
      }
    }
    .ant-tabs-nav-list .ant-tabs-tab-active  {
      background: #d6e3ff;
    }
    .ant-tabs-nav-list .ant-tabs-tab-active div {
      color: #0F244C;
      // font-weight: 700;
      .anticon {
        color: #2B58BA;
        font-size: 16px;
      }
    }
    .ant-tabs-tab-btn:focus,
    .ant-tabs-tab-remove:focus, .ant-tabs-tab-btn:active, 
    .ant-tabs-tab-remove:active{
      color: #667084;
    }
  }
}