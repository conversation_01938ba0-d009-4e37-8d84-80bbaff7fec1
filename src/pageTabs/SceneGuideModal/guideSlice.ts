import { createSlice, PayloadAction } from '@reduxjs/toolkit'

export interface InitialState {
  guideUniqueIds: string[] //当前展示引导唯一标识
  curGuideUniqueId: string | null; //当前正在展示的
  steps: any[],
  guideVisible: boolean;

}

const initialState: InitialState = {
  guideUniqueIds: [],
  steps: [],
  guideVisible: false,
  curGuideUniqueId: null
}

export const guideSlice = createSlice({
  name: 'guide',
  initialState,
  reducers: {
    setguideUniqueIds: (state, action: PayloadAction<{guideUniqueIds: string[]}>) => {
      const { guideUniqueIds} = action.payload
      state.guideUniqueIds = guideUniqueIds
    },
    setGuideSteps: (state, action: PayloadAction<{steps: any[];guideUniqueId : string}>) => {
      const { guideUniqueId, steps } = action.payload;
      state.steps =  steps;
      state.curGuideUniqueId = guideUniqueId;
    },
    setGuideVisible: (state, action: PayloadAction<boolean>) => {
      state.guideVisible =  action.payload
    },
    resetGuideSteps: (state) => {
      
      state.steps =  [];
      state.guideUniqueIds = state.guideUniqueIds.filter((key) => key !== state.curGuideUniqueId)
      state.curGuideUniqueId = null;
      state.guideVisible =  false;
    },
    clearGuideSteps: (state) => {
  
      state.steps =  [];
      state.guideUniqueIds = []
      state.curGuideUniqueId = null;
      state.guideVisible =  false;
    },
  },
})

export const guideReducer = guideSlice.reducer

export const { setguideUniqueIds, setGuideSteps, setGuideVisible, resetGuideSteps, clearGuideSteps } = guideSlice.actions
