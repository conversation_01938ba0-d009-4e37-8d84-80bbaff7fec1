@import 'src/styles/variables';

.sceneGuideModal {

  :global {
    .ant-modal-header {
      border-bottom: 0;
    }

    .ant-modal-body {
      padding: 0 0;
    }
  }

  .guideLogo {
    width: 18px;
    height: 18px;
    margin-right: 8px;
  }

  .arialFontFamily {
    font-family: "Arial";
  }
  .guideContent {
    display: flex;
    align-items: center;

    .stepList {
      width: 296px;
      padding: 0 18px 78px 24px;
      background: linear-gradient(160deg, #FFFFFF 0%, #F3F5F9 70%, rgba(195, 219, 254, 0) 122%);

      .imgWrapper {
        width: 100%;
        text-align: center;

        img {
          width: 127px;
          height: 120px;
        }
      }

      h2 {
        position: relative;
        margin-top: -14px;
      }

      div {
        color: $sub-text-color
      }

      ul {
        width: 100%;
        padding-left: 0;
        list-style-type: none;
        margin-top: 34px;

        li {
          width: 100%;
          height: 46px;
          line-height: 46px;
          cursor: pointer;
          padding-left: 8px;
          margin-bottom: 14px;
          background-color: #fff;
          border-radius: 0px 4px 4px 0px;

          img {
            margin-right: 16px;
          }
        }
        
        .activeStepTab {
          color: #3262FF;
          border-left: 2px solid var(--primary-color)
        }
      }

    }

    .stepMenus {
      flex: 1;
      min-height: 477px;
      max-height: 500px!important;
      overflow-y: auto;
      list-style-type: none;
      flex-direction: column;
      width: 100%;
      margin-right: 32px;

      li {
        width: 100%;
        height: 52px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 12px 16px;
        background: #F9FAFF;
        margin-bottom: 24px;

        .left {
          .desc {
            color: $sub-text-color;
            margin-left: 10px;
            font-size: 12px;
          }
        }
        .fontSize12 {
          font-size: 12px;
        }
        .disabledLink {
        
          color: #ccc;
        }
      }
      .activeGuideIndex {
         background: #F1F4FF;
      }
    }
  }
}