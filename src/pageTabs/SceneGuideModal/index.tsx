import React, { useEffect, useMemo, useState } from "react";
import { But<PERSON> } from "antd";
import classNames from "classnames";
import { useHistory } from 'react-router-dom'
import { getGuideMenuConnections } from 'src/api';
import newLogo from 'src/assets/img/new-logo.png';
import { Iconfont, UIModal } from 'src/components';
import step1Icon from 'src/assets/img/scene-guide/step1.png';
import step3Icon from 'src/assets/img/scene-guide/step3.png';
import guideLogo from 'src/assets/img/scene-guide/guide-logo.png'
import { setUserInfo } from "src/appPages/login/loginSlice";
import { useDispatch, useModal, useRequest, useSelector } from 'src/hook';
import { setActiveTab } from "src/pageTabs/dataProtection/DataProtectionPageSlice";
import { setGuideVisible, setguideUniqueIds } from "./guideSlice";
import {
  setAuthorizationPageState, 
  setAuthorizationPageDetailParams
} from 'src/pageTabs/automaticAuthorizationManagePage/authorizationAuthorizationManagePageSlice'


import styles from './index.module.scss';
import i18n from 'src/i18n';
import { useTranslation } from 'react-i18next';

/**
 guideId 表示当前菜单拥有引导的标识
 permissionType 表示当前所在菜单
 activeTab 当前选中tab
*/
const STEP_TABS_MAPPING: any = {
  DATABASE_MANAGEMENT: {
    title: i18n.t("personal:databaseManagement"),
    icon: step1Icon,
    menus: [
      { guideId: ['CONNECTION_MANAGEMENT_ADD'], permissionType: 'CONNECTION_MANAGEMENT', title: i18n.t("personal:connectionManagement"), desc: i18n.t("personal:createConnectionOperations"), to: i18n.t("personal:newConnectionWizard"), link: `/connection_management` },
      { guideId: ['AUTH_MANAGEMENT_SUBJIECT'], permissionType: 'AUTH_MANAGEMENT', title: i18n.t("personal:subjectAuthorization"), desc: i18n.t("personal:authorizationByUser"), key: 'subjectAuth', to: i18n.t("personal:subjectAuthorizationWizard"), link: `/subject-authorization` },
      { guideId: ['AUTH_MANAGEMENT_SDT', 'AUTH_MANAGEMENT_ADD_USER'], permissionType: 'AUTH_MANAGEMENT', title: i18n.t("personal:objectAuthorization"), key: 'objectAuth', desc: i18n.t("personal:authorizationByDatabaseObject"), to: i18n.t("personal:objectAuthorizationWizard"), link: `/auth_management` },
      { guideId: ['AUTOMATIC_ADD_BTN', 'AUTOMATIC_EDIT', 'AUTOMATIC_EDIT_POLICY', 'AUTOMATIC_DETAIL'], permissionType: 'AUTOMATIC_AUTHORIZATION', title: i18n.t("personal:automaticAuthorization"), desc: i18n.t("personal:createPermissionSet"), to: i18n.t("personal:automaticAuthorizationWizard"), link: `/automatic_authorization` },
      { guideId: ['RULE_TEMPLATE_LIST', 'RULE_TEMPLATE_DETAIL'], permissionType: 'RULE_MANAGEMENT', title: i18n.t("personal:ruleManagement"), desc: i18n.t("personal:sqlAuditRules"), to: i18n.t("personal:ruleTemplateWizard"), link: `/rule_management` },
      { guideId: ['DATA_PROTECT_SDT', 'DATA_PROTECT_DESENS'], permissionType: 'DATA_PROTECT', title: i18n.t("personal:sensitiveFields"), desc: i18n.t("personal:setMaskingAlgorithm"), to: i18n.t("personal:sensitiveFieldsWizard"), link: `/data_protect`, activeTab: 'DESENS' },
      { guideId: ['DATA_PROTECT_DATA_INFO'], permissionType: 'DATA_PROTECT', title: i18n.t("personal:maskedData"), desc: i18n.t("personal:enableRules"), to: i18n.t("personal:maskedDataWizard"), link: `/data_protect`, activeTab: 'DATA_INFO' },
      { guideId: ['DATA_PROTECT_SDT', 'DATA_PROTECT_SCAN'], permissionType: 'DATA_PROTECT', title: i18n.t("personal:maskingScan"), desc: i18n.t("personal:createScanTask"), to: i18n.t("personal:maskingScanWizard"), link: `/data_protect`, activeTab: 'SCAN' },
      { guideId: ['DATA_PROTECT_SECURITY', 'DATA_PROTECT_SECURITY_HIGH'], permissionType: 'DATA_PROTECT', title: i18n.t("personal:securitySettings"), desc: i18n.t("personal:highRiskOperations"), to: i18n.t("personal:securitySettingsWizard"), link: `/data_protect`, activeTab: 'SECURITY' },
    ]
  },
  FLOW_APPLY: {
    title: i18n.t("personal:process"),
    icon: step3Icon,
    menus: [
      { guideId: ['MINE_APPLY', 'MINE_APPLY_ADD'], permissionType: 'MINE_APPLY', title: i18n.t("personal:myApplications"), desc: i18n.t("personal:accessRequest"), to: i18n.t("personal:elevationWizard"), link: `/mine_apply` },
      { guideId: ['FLOW_DESIGN'], permissionType: 'FLOW_DESIGN', title: i18n.t("personal:customProcessDesign"), desc: i18n.t("personal:customChangeRequestProcess"), to: i18n.t("personal:customProcessDesignWizard"), link: `/flow_design` },
    ]
  }
}

const SceneGuideModal = () => {

  const history = useHistory();
  const dispatch = useDispatch();
  const { userInfo } = useSelector(state => state.login)
  const isLangEn = useSelector(state => state.login.locales) === 'en'

  const { permissionMenus, roles = [] } = userInfo;
  const { visible, closeModal } = useModal('ModalSceneGuide');

  const [activeStepTab, setActiveStepTab] = useState('DATABASE_MANAGEMENT');
  const [activeGuideIndex, setActiveGuideIndex] = useState<null | number>(null);
  const { t } = useTranslation();

  //获取连接信息
  const { data: connectionMapping = {}, run: getConnectionMapping } = useRequest(getGuideMenuConnections, { manual: true, refreshDeps: [visible] })

  useEffect(() => {
    if (visible) {
      getConnectionMapping();
    }

  }, [visible])

  const curStepMenus = useMemo(() => {
    //普通用户 只展示我的申请
    const isJustNormalRole = roles?.length === 1 && roles?.[0]?.type === 'NORMAL_ROLE';
    if (isJustNormalRole) {
      return [STEP_TABS_MAPPING['FLOW_APPLY'].menus[0]]
    }

    //连接管理（有连接的话）
    //默认当前选中step展示向导
    const defaultStepMenus = STEP_TABS_MAPPING[activeStepTab]?.menus;

    //当前用户拥有菜单
    const subPermissionMenu = permissionMenus?.find((menu) => menu?.menuType === activeStepTab);
    const realMenus = subPermissionMenu?.items || [];

    if (!realMenus?.length) return [];
    // 有资源

    let hasWritePermissionMenus: any = [];
    for (let i of realMenus) {
      const filteredMenus = defaultStepMenus?.filter((menu: any) => menu.permissionType === i?.permissionType);

      if (filteredMenus) {
        hasWritePermissionMenus = hasWritePermissionMenus.concat(filteredMenus);
      }
    }
    return hasWritePermissionMenus;

  }, [activeStepTab, permissionMenus])

  const stepMenu = useMemo(() => {
    const stepList = Object.keys(STEP_TABS_MAPPING).filter(permissionType => {
      //当前菜单项
      const curMenus = permissionMenus?.find(menu => menu?.menuType === permissionType);
   
      if (curMenus) {
        //要展示的子菜单项
        const showItemPermissionTypes = STEP_TABS_MAPPING[permissionType]?.menus?.map((p: any) => p?.permissionType);
        //是否有子菜单项权限
        const hasMenusItems = curMenus?.items?.some(item => showItemPermissionTypes.includes(item?.permissionType))
      
        return hasMenusItems;
      }
     
      return curMenus
    })
    setActiveStepTab(stepList?.[0])
    return stepList
  }, [permissionMenus, visible])

  const getClickableStatus = (item: any) => {

    switch (item?.permissionType) {
      case 'CONNECTION_MANAGEMENT':
        //有权限就引导
        return roles?.some(role => role.type.startsWith('CUSTOM_SYSTEM') || role.type === 'DBA_ROLE' || role.type === 'SENIOR_ROLE');
      case 'AUTH_MANAGEMENT':
        if (item?.key === 'objectAuth') {
          return connectionMapping?.objectAuth;
        }
        return connectionMapping?.subjectAuth;
      case 'AUTOMATIC_AUTHORIZATION':
        return connectionMapping?.automatic;
      case 'DATA_PROTECT':
        return connectionMapping?.desensData;
      case 'MINE_APPLY':
        return connectionMapping?.applyAuth;
      default:
        return true;
    }
  }

  return (
    <UIModal
      visible={visible}
      width={950}
      closable={false}
      title={t("personal:newbieSceneGuide")}
      className={styles.sceneGuideModal}
      footer={<Button type="primary" onClick={() => {
        closeModal()
        dispatch(setUserInfo({ ...userInfo, ifHaveShowGuide: false }))
      }}>{t("personal:disable")}</Button>}
    >
      <div className={classNames(styles.guideContent, {
        [styles.arialFontFamily]: isLangEn
      })}>
        <div className={styles.stepList}>
          <div className={styles.imgWrapper}>
            <img alt="logo" src={guideLogo} width={20} />
          </div>
          <h2>{t("personal:welcomeToOnboarding")}</h2>
          <div>{t("personal:firstLoginInstructions")}</div>
          <ul>
            {
              stepMenu?.map((key, index) => (
                <li
                  key={key}
                  onClick={() => setActiveStepTab(key)}
                  className={classNames({ [styles.activeStepTab]: activeStepTab === key })}
                >
                  <img alt="icon" width={16} src={STEP_TABS_MAPPING[key].icon} />
                  {`Step${index + 1}.${STEP_TABS_MAPPING[key].title}`}
                </li>
              ))
            }
          </ul>
        </div>
        <ul className={styles.stepMenus}>
          {
            curStepMenus?.map((item: any, index: number) => (
              <li
                key={item?.title}
                className={classNames({ [styles.activeGuideIndex]: index === activeGuideIndex })}
                onMouseEnter={() => setActiveGuideIndex(index)}
                onMouseLeave={() => setActiveGuideIndex(null)}
              >
                <div className={styles.left}>
                  <label>{item?.title}</label>
                  <span className={classNames(styles.desc, {
                    [styles.fontSize12]: isLangEn
                  })}>{item?.desc}</span>
                </div>
                <div className={classNames('linkStyle', {
                  [styles.disabledLink]: !getClickableStatus(item),
                  [styles.fontSize12]: isLangEn
                })}

                  onClick={() => {
                    if (!getClickableStatus(item)) return;
                    dispatch(setGuideVisible(true));
                    dispatch(setguideUniqueIds({ guideUniqueIds: item?.guideId }))
                    if (item?.permissionType === 'DATA_PROTECT') {
                      dispatch(setActiveTab(item?.activeTab))
                    }
                    // 跳转到数据库管理-自动授权（状态和参数设置）
                    if(item.link === '/automatic_authorization'){
                      dispatch(setAuthorizationPageState(''))
                      dispatch(setAuthorizationPageDetailParams({}))
                    }
                    history.push(item.link)
                    closeModal()
                  }}>
                  {item?.to} <Iconfont type="icon-jiantou1" rotate={90} />
                </div>
              </li>
            ))
          }
        </ul>
      </div>
    </UIModal>
  )
}

export default SceneGuideModal;