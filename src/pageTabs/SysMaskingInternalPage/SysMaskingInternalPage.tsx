import React, { useEffect } from 'react'
import { useRequest } from 'src/hook'
import {
  Layout,
  Breadcrumb,
  Form,
  Table,
  Input,
  Tooltip
} from 'antd'
import { SearchOutlined } from '@ant-design/icons'
import { ColumnsType } from 'antd/lib/table'
import { LinkButton } from 'src/components'
import {getSysInternalDenseRules,updateSysInternalDenseStatus} from 'src/api'
import 'src/styles/layout.scss'

const { Header,Content } = Layout

export const SysMaskingInternalPage = ()=>{

  const [queryForm] = Form.useForm()

  const {
    data: rules,
    run: fetchRules,
    refresh: refreshRules,
    loading: fetching,
  } = useRequest(getSysInternalDenseRules, { manual: true })

  useEffect(() => {
    const condition = queryForm.getFieldValue('ruleName')
    if (condition !== undefined && condition !== "") {
      fetchRules(condition);
    } else {
      fetchRules({});
    }
  }, [fetchRules,queryForm])

  const { run: changeDenseStatus, fetches: updateRequestFetches } = useRequest(
    updateSysInternalDenseStatus,
    {
      manual: true,
      fetchKey: ({ ruleId }) => ruleId.toString(),
      onSuccess: () => {
        refreshRules()
      },
    },
  )

  const columns: ColumnsType<any> = [
    {
      dataIndex: 'ruleName',
      title: '规则名称',
      width: 120,
      ellipsis: true,
    },
    {
      dataIndex: 'description',
      title: '描述',
      width: 120,
      ellipsis: true,
    },
    {
      dataIndex: 'resource',
      title: '匹配规则',
      ellipsis: { showTitle: false },
      render: (text) => <Tooltip title={text}>{text}</Tooltip>,
    },
    {
      dataIndex: 'checkMethod',
      title: '匹配方式',
      width: 120,
      render: (text) => {
        // ! 当前只有两种匹配方式，如果类型再增多的话，从维护性考虑，需要定义好对应的类型
        if (text === 'EXPRESSING') {
          return '正则表达式'
        }
        if (text === 'ALGORITHM') {
          return '算法'
        }
        return text
      },
    },
    {
      dataIndex: 'example',
      title: '脱敏结果',
      ellipsis: true,
    },
    {
      key: 'action',
      width: 120,
      render: (_, record) => {
        const { valid, id: ruleId,ruleName } = record
        return valid ? (
          <LinkButton
            danger
            onClick={() => {
              changeDenseStatus({
                ruleId,
                ruleName
              })
            }}
            loading={updateRequestFetches[ruleId]?.loading}
          >
            停用
          </LinkButton>
        ) : (
          <LinkButton
            onClick={() => {
              changeDenseStatus({
                ruleId,
                ruleName
              })
            }}
            loading={updateRequestFetches[ruleId]?.loading}
          >
            启用
          </LinkButton>
        )
      },
    },
  ]

 return (
  <Layout className="cq-container">
  <Header className="cq-header">
    <Breadcrumb className="breadcrumb">
      <Breadcrumb.Item>系统管理</Breadcrumb.Item>
      <Breadcrumb.Item>系统权限</Breadcrumb.Item>
      <Breadcrumb.Item>内置脱敏管理</Breadcrumb.Item>
    </Breadcrumb>
    <div className="cq-header__main">
      <h1>内置脱敏管理</h1>
    </div>
  </Header>
  <Layout className="cq-main">
  <Content className="cq-content">
  <section className="cq-card cq-card--padding">
    <div className="cq-card__headerbar">
    <div></div>
    <div>
          <Form
            name="maskingInternalFilter"
            layout="inline"
            form={queryForm}
            onFinish={(ruleName) => {
              if (ruleName.ruleName !== undefined && ruleName.ruleName !== "") {
                fetchRules(ruleName);
              } else {
                fetchRules({});
              }
            }}
          >
            <Form.Item name="ruleName" noStyle>
              <Input
                prefix={<SearchOutlined />}
                placeholder="请输入规则名称"
                allowClear
              />
            </Form.Item>
          </Form>
        </div>
    </div>
    <section className="cq-table">
        <Table
          columns={columns}
          dataSource={rules?.list}
          rowKey="id"
          loading={fetching}
          size="small"
          pagination={{showSizeChanger: true}}
        />
      </section>
    </section>
  </Content>
  </Layout>
  </Layout>
 )
}