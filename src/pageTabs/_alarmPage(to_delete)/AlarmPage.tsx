import React from 'react'
import { Route, Switch } from 'react-router-dom'
import { Layout } from 'antd'
import { AlarmAnalysis } from './analysis/AlarmAnalysis'
import { HighRiskReport } from './detail/HighRiskReport'
import { AbnormalUserReport } from './detail/AbnormalUserReport'
import { AlertAuthorityReport } from './detail/AlertAuthorityReport'
import 'src/styles/layout.scss'

const alarmRoutes = [
  { pathname: '/alarm', children: <AlarmAnalysis /> },
  { pathname: '/alarm/highrisk', children: <HighRiskReport /> },
  { pathname: '/alarm/abnormal', children: <AbnormalUserReport /> },
  { pathname: '/alarm/alert_authority', children: <AlertAuthorityReport /> },
]

export const AlarmPage = () => {
  return (
    <Layout className="cq-container">
      <Switch>
        {alarmRoutes.map(({ pathname, children }) => (
          <Route exact path={pathname} key={pathname} render={() => children} />
        ))}
      </Switch>
    </Layout>
  )
}
