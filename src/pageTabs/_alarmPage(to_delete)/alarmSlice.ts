import { createSlice, PayloadAction } from '@reduxjs/toolkit'

interface InitialState {
  path: string
}

const initialState: InitialState = {
  path: '/alarm',
}

export const alarmSlice = createSlice({
  name: 'alarm',
  initialState,
  reducers: {
    setPath(state, action: PayloadAction<string>) {
      state.path = action.payload
    },
  },
})

export const alarmReducer = alarmSlice.reducer

export const { setPath } = alarmSlice.actions
