import React, { useEffect } from 'react'
import { useRouteMatch } from 'react-router-dom'
import { Layout, Breadcrumb } from 'antd'
import { Row, Col } from 'antd'
import { useDispatch } from 'src/hook'
import { setPath } from '../alarmSlice'
import { Overview } from './Overview'
import {
  ChartCard,
  HighRiskChart,
  HostStatusChart,
} from './Charts'

const { Header, Content } = Layout 

export const AlarmAnalysis: React.FC = () => {
  const match = useRouteMatch()
  const dispatch = useDispatch()

  useEffect(() => {
    if (match) {
      dispatch(setPath(match.path))
    }
  }, [dispatch, match])
  return (
    <>
      <Header className="cq-header">
        <Breadcrumb className="breadcrumb">
          <Breadcrumb.Item>告警</Breadcrumb.Item>
          <Breadcrumb.Item>告警分析</Breadcrumb.Item>
        </Breadcrumb>
        <div className="cq-header__main">
          <h1>告警分析</h1>
          <div className="cq-header__action">
            
          </div>
        </div>
      </Header>
      <Layout className="cq-main">
        <Content className="cq-content">
          <Overview />
          <div style={{ margin: 16 }}></div>
          <Row gutter={24}>
            <Col span={16}>
              <ChartCard title={'SQL 高危执行趋势'}>
                <HighRiskChart></HighRiskChart>
              </ChartCard>
            </Col>
            <Col span={8}>
              <ChartCard title={'资源告警'}>
                <HostStatusChart></HostStatusChart>
              </ChartCard>
            </Col>
          </Row>
        </Content>
      </Layout>
    </>
  )
}