import { Chart } from '@antv/g2'
import { Radio, Spin } from 'antd'
import React, { useEffect, useState } from 'react'
import { getHighRiskCountPerUnit } from 'src/api'
import { useRequest } from 'src/hook'
import chartStyles from './chart.module.scss'
import { EmptyChart } from './EmptyChart'
import { ChartColors, getUnit, getUnixTimeRange, getUnitBase } from './util'
import DataSet from '@antv/data-set'
import dayjs from 'dayjs'
import './charts.scss'

export const HighRiskChart = () => {
  const [timeRange, setTimeRange] = useState<1 | 7 | 30>(7)
  const { data = [], loading } = useRequest(
    () =>
      getHighRiskCountPerUnit({
        unit: getUnit(timeRange),
        ruleNames: [],
        ...getUnixTimeRange(timeRange)
      }),
    {
      formatResult: (data) => 
        new DataSet.DataView()
          .source(data.concat(getUnitBase(timeRange)))
          // 填充刻度，例如值为 2021-02-8 00:00:00，unit 为 DAY，需要填充之前 timeRange 天的 unit string
          .transform({
            // 补全行
            type: 'fill-rows',
            groupBy: ['ruleName'],
            orderBy: ['unit'],
            fillBy: 'order',
          })
          .transform({
            // 补全列
            type: 'impute',
            field: 'amount', // 待补全字段
            groupBy: ['ruleName'], // 分组字段集（传空则不分组）
            method: 'value', // 补全为常量
            value: 0,
          })
          .rows.filter(({ ruleName }) => ruleName)
          .map(({ unit = '', ...rest }) => ({
            unit: timeRange === 1 ? new Date(unit).getHours() : unit,
            ...rest,
          })),
      refreshDeps: [timeRange],
    },
  )
  const renderHighRiskChart = (container: string, data: any[]) => {
    const chart = new Chart({
      container,
      autoFit: true,
    })

     const ruleNameWeightByTotal = data
      .reduce((prev, curr) => {
        const record = prev.find((record: any) => record.ruleName === curr.ruleName)
        if (!record) return prev.concat({ ...curr })
        record.amount += curr.amount
        return prev
      }, [])
      .sort((a: any, b: any) => b.amount - a.amount)
      .map((record: any) => record.ruleName)
      .reverse()
    data.sort((a, b) => {
      // 双条件排序：日期升序，日期一样，总数更大的数据库类型靠前
      if (a.unit === b.unit) {
        return (
          ruleNameWeightByTotal.indexOf(b.ruleName) -
          ruleNameWeightByTotal.indexOf(a.ruleName)
        )
      }
      return a.unit < b.unit ? -1 : 1
    })

    chart.data(data)
    
    chart.scale({
      unit: {
        range: [0, 0.9],
        formatter: (v) => (v < 25 ? v + ' 时' : dayjs(v).format('YYYY-MM-DD')),
      },
      amount: {
        minTickInterval: 1,
        range: [0, 0.95],
        formatter: (v) => (v || '0') + ' 次',
      },
    })

    chart.tooltip({
      showCrosshairs: true,
      shared: true,
    })

    chart.axis('amount', {
      label: {
        formatter: (val) => {
          return val
        },
      },
    })

    chart.line().position('unit*amount').color('ruleName', ChartColors)
    chart.area().position('unit*amount').color('ruleName', ChartColors)

    chart
      .point()
      .position('unit*amount')
      .color('ruleName', ChartColors)
      .shape('circle')
      .style({
        stroke: '#fff',
        lineWidth: 1,
      })
    chart.render()
    return chart
  }

  useEffect(() => {
    if (!data || !data[0]) return
    const chart = renderHighRiskChart('highrisk-chart', data)
    return () => chart.destroy()
  }, [data])

  return (
    <Spin spinning={loading}>
      <div className={chartStyles.sqlCountWrapper}>
        <div className={chartStyles.toolbar}>
          <div>
            <Radio.Group
              buttonStyle="solid"
              className="cq-radio"
              value={timeRange}
              onChange={(e) => setTimeRange(e.target.value)}
            >
              <Radio.Button value={1}>今日</Radio.Button>
              <Radio.Button value={7}>7 天</Radio.Button>
              <Radio.Button value={30}>30 天</Radio.Button>
            </Radio.Group>
          </div>
          <div></div>
        </div>
        <div id="highrisk-chart" className={chartStyles.sqlCount}>
          {!data[0] && !loading && <EmptyChart></EmptyChart>}
        </div>
      </div>
    </Spin>
  )
}
