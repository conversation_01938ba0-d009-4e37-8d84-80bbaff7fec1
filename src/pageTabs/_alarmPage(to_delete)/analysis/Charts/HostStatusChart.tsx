import { Chart } from '@antv/g2'
import { Spin } from 'antd'
import React, { useState, useEffect } from 'react'
import { getHostStatus } from 'src/api'
import { useRequest } from 'src/hook'
import chartStyles from './chart.module.scss'
import { EmptyChart } from './EmptyChart'
import { Iconfont } from 'src/components'

interface ChartData {
  cpuCoreSize: number
  cpuUsage: number
  diskSize: number
  diskUsage: number
  memorySize: number
  memoryUsage: number
}

const StatusCard = (props: {
  iconType: string
  title: string
  value: number
  unit: string
}) => {
  const { iconType, title, value, unit } = props
  return (
    <div className={chartStyles.block}>
      <div className={chartStyles.blockMain}>
        <div className={chartStyles.icon}>
          <Iconfont
            type={iconType}
            style={{ fontSize: 22, color: '#6C7293' }}
          ></Iconfont>
        </div>
        <div className={chartStyles.info}>
          <h5 className={chartStyles.infoTitle}>{value + unit}</h5>
          <span className={chartStyles.tag}>{title}</span>
        </div>
      </div>
    </div>
  )
}
export const HostStatusChart = () => {
  const [chartData, setChartData] = useState<ChartData>({
    cpuCoreSize: 0,
    cpuUsage: 0,
    diskSize: 0,
    diskUsage: 0,
    memorySize: 0,
    memoryUsage: 0,
  })

  const { data = [], loading } = useRequest(getHostStatus, {
    onSuccess: (data: any) => {
      renderSqlCountChart('host-count', data)
    },
  })

  const renderSqlCountChart = (container: string, data: ChartData) => {
    const _data = [
      { type: 'CPU', percent: data.cpuUsage, total: data.cpuCoreSize },
      { type: '磁盘', percent: data.diskUsage, total: data.diskSize },
      { type: '内存', percent: data.memoryUsage, total: data.memorySize },
    ]
    setChartData(data)

    const chart = new Chart({
      container,
      autoFit: true,
      // appendPadding: [20, 20, 20, 20]
    })

    chart.data(_data)
    chart.coordinate('polar', { innerRadius: 0.5 }).transpose()

    chart.axis('type', {
      grid: null,
      tickLine: null,
      line: null,
      label: {
        style: {
          fill: '#595959',
        },
      },
    })

    chart.scale('percent', {
      min: 0,
      max: 100,
    })
    chart.legend(false)
    chart
      .interval({
        theme: { style: { fill: '#F0F0F0', fillOpacity: 1 } },
      })
      .position('type*percent')
      .color('type', (val) => {
        if (val === 'CPU') {
          return '#58cfff'
        }
        if (val === '磁盘') {
          return '#7a79ff'
        }
        return '#333fff'
      })
      .label('percent', () => ({
        content: (data) => data.item,
        offset: 24,
      }))
    chart.tooltip({
      showTitle: false,
      showMarkers: false,
      itemTpl: `<li class="g2-tooltip-list-item"><span style="background-color:{color};" class="g2-tooltip-marker"></span>{name}：{value}</li>`,
    })

    chart.interaction('element-active')
    chart.render()
  }

  useEffect(() => {}, [data])
  return (
    <Spin spinning={loading}>
      <div id="host-count" className={chartStyles.hostCount}>
        {!data && !loading && <EmptyChart></EmptyChart>}
      </div>
      <div className={chartStyles.hostBlock}>
        <StatusCard
          iconType={'icon-neicun'}
          title={'内存'}
          value={chartData.memorySize}
          unit={''}
        />
        <StatusCard
          iconType={'icon-yingpan_HDD'}
          title={'磁盘'}
          value={chartData.diskSize}
          unit={''}
        />
        <StatusCard
          iconType={'icon-cpu'}
          title={'CPU'}
          value={chartData.cpuUsage}
          unit={'%'}
        />
      </div>
    </Spin>
  )
}
