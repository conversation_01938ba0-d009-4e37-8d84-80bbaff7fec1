.card {
  box-shadow: 0px 8px 15px 0px rgba(149, 156, 182, 0.15);
  border-radius: 4px;
  border: 1px solid #eaebf0;
}
.sqlCountWrapper {
  height: 45vh;
  min-height: 450px;
  .sqlCount {
    height: calc(45vh - 48px);
  }
}
.userCountWrapper {
  height: 45vh;
  min-height: 450px;
  .userCount {
    height: calc(45vh - 60px);
  }
}
.dbCount {
  height: 45vh;
  min-height: 450px;
}
.hostCount {
  height: calc(45vh - 90px);
  min-height: 360px;
}
.executionTimeWrapper {
  height: 55vh;
  .executionTime {
    height: calc(55vh - 48px);
  }
}
.toolbar {
  padding-bottom: 24px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 90%;
}
.hostBlock {
  margin: 0 -24px -16px;
  display: flex;
  align-items: center;
  border-top: 1px solid #eaebf0
}
.block {
  flex: 1;
  display: flex;
  position: relative;
  padding-top: 16px;
  box-sizing: border-box;
  height: 106px;
  &+.block {
    &::before {
      content: '';
      display: block;
      width: 1px;
      height: 100%;
      position: absolute;
      left: 0;
      top: 0;
      background: #eaebf0
    }
  }
}
.blockMain {
  margin: 0 auto;
  display: flex;
  .icon {
    margin-right: 15px;
    margin-top: 17px;
    width: 22px;
    height: 22px;
  }
  .info {
    min-width: 51px;
    .infoTitle {
      display: block;
      font-size: 18px;
      color: #333333;
      letter-spacing: 0;
      padding-bottom: 5px;
      min-width: 51px;
      border-bottom: 1px solid #EAEBF0
    }
    .tag {
      font-size: 14px;
      color: #979BAF;
      padding-top: 4px;
      line-height: 20px;
    }
  }
}