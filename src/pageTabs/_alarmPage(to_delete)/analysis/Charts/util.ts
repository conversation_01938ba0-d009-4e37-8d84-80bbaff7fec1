import dayjs from 'dayjs'
import { AuditUnit } from 'src/api'

export const getUnixTimeRange = (
  days: 1 | 7 | 30,
): { beginTimeMs: number; endTimeMs: number } => {
  const date = new Date()
  const y = date.getFullYear()
  const m = date.getMonth()
  const d = date.getDate()
  const Today = {
    beginTimeMs: Number(new Date(y, m, d)),
    endTimeMs: Number(new Date(y, m, d, 24)) - 1,
  }
  const Last7Days = {
    beginTimeMs: Number(new Date(y, m, d - 6, 0)),
    endTimeMs: Number(new Date(y, m, d, 24)) - 1,
  }
  const Last30Days = {
    beginTimeMs: Number(new Date(y, m, d - 29, 0)),
    endTimeMs: Number(new Date(y, m, d, 24)) - 1,
  }
  switch (days) {
    case 30:
      return Last30Days
    case 7:
      return Last7Days
    case 1:
    default:
      return Today
  }
}

export const getUnit = (days: 1 | 7 | 30): AuditUnit => {
  switch (days) {
    case 30:
      return 'DAY'
    case 7:
      return 'DAY'
    case 1:
    default:
      return 'HOUR'
  }
}

export const getUnitBase = (days: 1 | 7 | 30): any[] => {
  const date = new Date()
  const y = date.getFullYear()
  const m = date.getMonth()
  const d = date.getDate()
  if (days === 1) {
    const unitBase = Array.from({ length: 24 }, (_, i) => ({
      unit: dayjs(new Date(y, m, d, i)).format('YYYY-MM-DD HH:mm:ss'),
    }))
    return unitBase
  }
  const unitBase = Array.from({ length: days }, (_, i) => ({
    unit: dayjs(new Date(y, m, d - days + i + 1)).format('YYYY-MM-DD HH:mm:ss'),
  }))
  return unitBase
}

export const ChartColors = [
  '#58cfff',
  '#9d58ff',
  '#23b899',
  '#ff7979',
  '#333fff',
  '#9e58fa',
]
