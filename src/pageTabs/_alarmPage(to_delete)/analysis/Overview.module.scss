.card,
.cardWithAction {
  box-shadow: 0px 8px 15px 0px rgba(149, 156, 182, 0.15);
  border-radius: 4px;
  border: 1px solid #eaebf0;
}
.cardWithAction::after {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  box-shadow: 0 8px 17px 0 rgba(0, 0, 0, 0.05), 0 6px 20px 0 rgba(0, 0, 0, 0.05);
  content: '';
  opacity: 0;
  z-index: -1;
}
.cardWithAction:hover,
.cardWithAction:focus {
  transform: scale3d(1.003, 1.003, 1.003);
  opacity: 0.88;
}
.cardWithAction:hover::after,
.cardWithAction:focus::after {
  opacity: 1;
}
.cardBody {
  height: 100%;
  padding: 0 56px;
  flex-wrap: nowrap;
}
.countText {
  display: 'flex';
  flex-direction: 'column';
}
.countNumber {
  font-size: 32px;
  font-family: Roboto-Regular, <PERSON><PERSON>;
  font-weight: 400;
  color: #454458;
  line-height: 43px;
  margin-right: 16px;
  overflow: hidden;
}
.countTitle {
  font-size: 14px;
  font-family: Roboto-<PERSON>, <PERSON><PERSON>;
  font-weight: 400;
  color: rgba(108, 114, 147, 1);
  line-height: 19px;
  margin: 8px 0 3px;
}
.iconCircle {
  width: 76px;
  height: 76px;
  border-radius: 38px;
  display: flex;
  align-items: center;
  justify-content: center;
}
