import React from 'react'
import { useRequest } from 'src/hook'
import { Card, Col, Row } from 'antd'
import { getAlarmOverview } from 'src/api'
import { Iconfont } from 'src/components'
import { Link } from 'react-router-dom'
import styles from './Overview.module.scss'

export const Overview = ({ daysSpan = 7 }: any) => {
  const {
    data: auditOverview = {
      ruleAmount: 0,
      ruleAuthorityAmount: 0,
      ruleOperateAmount: 0,
    },
    loading: loadingGetOverview,
  } = useRequest(() => getAlarmOverview(), { refreshDeps: [daysSpan] })
  const { ruleAmount, ruleAuthorityAmount, ruleOperateAmount } = auditOverview

  const OverviewCount = (props: {
    title: string
    link?: string
    count: number
    iconType: string
    iconColor: string
    loading?: boolean
  }) => {
    const { title, link, count, iconType, iconColor, loading } = props
    const CardContent = (
      <Card
        loading={loading}
        size="small"
        className={link ? styles.cardWithAction : styles.card}
        bodyStyle={{ height: 136 }}
      >
        <Row align="middle" justify="space-between" className={styles.cardBody}>
          <div className={styles.countText}>
            <Row align="middle">
              <pre className={styles.countNumber}>
                {parseFloat(String(count)).toLocaleString()}
              </pre>
            </Row>
            <div className={styles.countTitle}>{title}</div>
          </div>
          <div
            className={styles.iconCircle}
            style={{ backgroundColor: iconColor }}
          >
            <Iconfont
              type={iconType}
              style={{ fontSize: 40, color: 'white' }}
            ></Iconfont>
          </div>
        </Row>
      </Card>
    )
    return (
      <Col span={8}>
        {link ? <Link to={link}>{CardContent}</Link> : CardContent}
      </Col>
    )
  }

  return (
    <Row gutter={24}>
      <OverviewCount
        title={'高危动作'}
        link="/alarm/highrisk"
        count={ruleAmount}
        iconType={'icon-alarm-full'}
        iconColor="rgba(255, 121, 121, 1)"
        loading={loadingGetOverview}
      />
      <OverviewCount
        title={'异常用户'}
        link="/alarm/abnormal"
        count={ruleOperateAmount}
        iconType={'icon-chongtukehu'}
        iconColor="rgba(157, 88, 255, 1)"
        loading={loadingGetOverview}
      />
      <OverviewCount
        title={'越权执行'}
        link="/alarm/alert_authority"
        count={ruleAuthorityAmount}
        iconType={'icon-quanxian1'}
        iconColor="rgba(51, 87, 255, 1)"
        loading={loadingGetOverview}
      />
    </Row>
  )
}
