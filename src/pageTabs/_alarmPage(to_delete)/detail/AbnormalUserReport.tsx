import React, { useState, useEffect, useMemo, useCallback } from 'react'
import { Link, useRouteMatch } from 'react-router-dom'
import Watermark from '@pansy/react-watermark'
import { useDispatch, useSelector } from 'src/hook'
import {
  Radio,
  Layout,
  Breadcrumb,
  Table,
  Select,
  Popconfirm,
  Button,
} from 'antd'
import { DatePicker } from 'src/components'
import dayjs, { Dayjs } from 'dayjs'
import { ColumnsType } from 'antd/lib/table'
import { setPath } from '../alarmSlice'
import { getUnixTimeRange } from '../analysis/Charts/util'
import {
  getAbnormalUserReportDetail,
  AbnormalUserRequest,
  getAllUsers,
  exportAbnormalUserReports,
  RuleName,
  OperateName,
  RiskLevel,
  RiskLevels,
} from 'src/api'
import './detail.scss'
import { formatDateRange } from 'src/util'

const { Header, Content } = Layout

const { RangePicker } = DatePicker
const { Option } = Select

interface FilterParams {
  executors: string[]
  timeRange: [number, number] | null
  riskLevels?: RiskLevel[]
  ruleNames?: RuleName[]
  operateNames?: OperateName[]
}

// 根据组件 filterParams 状态构造出查询请求所需要的参数
const getRequestParams = (filterParams: FilterParams) => {
  const { executors, timeRange } = filterParams
  // 构造查询参数
  const params: Partial<AbnormalUserRequest> = {
    executeBeginMs: timeRange?.[0],
    executeEndMs: timeRange?.[1],
  }
  if (executors.length) {
    params.executors = executors
  }
  return params
}

export const AbnormalUserReport: React.FC = () => {
  // 时间
  const [timeRadioRange, setTimeRadioRange] = useState<1 | 7 | 30>(7)

  const [filterParams, setFilterParams] = useState<FilterParams>({
    executors: [],
    timeRange: [
      dayjs().subtract(7, 'day').startOf('d').valueOf(),
      dayjs().endOf('d').valueOf(),
    ],
  })
  // 全部用户列表
  const [userList, setUserList] = useState<any[]>([])

  // 分页器状态
  const [pageState, setPageState] = useState({
    current: 1,
    pageSize: 10,
  })
  const { watermarkEffect } = useSelector((state: any) => state.login)

  // table dataSource, total and loading status
  const [dataSource, setDataSource] = useState<any[]>([])
  const [total, setTotal] = useState<number>()
  const [loadingTableData, setLoadingTableData] = useState<boolean>(false)
  const match = useRouteMatch()
  const dispatch = useDispatch()

  const { executors, timeRange } = filterParams

  //切换天数
  const changeTimeRadioRange = (val: any) => {
    setTimeRadioRange(val)
    const { beginTimeMs, endTimeMs } = getUnixTimeRange(val)
    setFilterParams({
      ...filterParams,
      timeRange: [beginTimeMs, endTimeMs],
    })
  }
  const rangeValue = useMemo(() => {
    if (timeRange === null) {
      return null
    } else {
      const range = timeRange.map((timestamp) => dayjs(timestamp)) as [
        Dayjs,
        Dayjs,
      ]
      return range
    }
  }, [timeRange])

  // 获取分页数据
  const getDataSource = useCallback(
    async (params) => {
      const { current, pageSize } = pageState
      const offset = (current - 1) * pageSize
      const limit = pageSize
      try {
        setLoadingTableData(true)
        const { data, total } = await getAbnormalUserReportDetail({
          ...params,
          limit,
          offset,
        })
        setDataSource(data)
        setTotal(total)
      } catch {
        setDataSource([])
        setTotal(undefined)
      } finally {
        setLoadingTableData(false)
      }
    },
    [pageState],
  )

  useEffect(() => {
    const params = getRequestParams(filterParams)
    getDataSource(params)
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [pageState, getDataSource])

  useEffect(() => {
    setPageState({ current: 1, pageSize: 10 })
  }, [filterParams])

  useEffect(() => {
    // alarm 下子路由状态保持
    if (match) {
      dispatch(setPath(match.path))
    }
  }, [dispatch, match])

  useEffect(() => {
    // 获取用户列表
    const getUserList = async () => {
      try {
        const users = await getAllUsers()
        setUserList(users)
      } catch {}
    }
    getUserList()
  }, [])

  const columns: ColumnsType<any> = [
    // todo: ColumnsType const
    { dataIndex: 'executor', title: '操作人', width: 120, ellipsis: true },
    {
      dataIndex: 'ruleName',
      title: '规则名称',
      width: 192,
      ellipsis: true,
    },
    {
      dataIndex: 'operateName',
      title: '动作名称',
      width: 192,
      ellipsis: true,
    },
    {
      dataIndex: 'riskLevel',
      title: '危险等级',
      width: 108,
      render: (riskLevel) => {
        return RiskLevels[riskLevel]
      },
    },
    {
      dataIndex: 'executeBegin',
      title: '开始时间',
      ellipsis: true,
    },
    {
      dataIndex: 'executeEnd',
      title: '结束时间',
      ellipsis: true,
    },
    { dataIndex: 'clientIp', title: 'IP地址', width: 120, ellipsis: true },
  ]

  // watermark
  const { watermarkSetting, watermarkValue } = useSelector(
    (state) => state.login.userInfo,
  )
  
  return (
    <>
      <Header className="cq-header">
        <Breadcrumb className="breadcrumb">
          <Breadcrumb.Item>告警</Breadcrumb.Item>
          <Breadcrumb.Item>告警分析</Breadcrumb.Item>
          <Breadcrumb.Item>异常用户明细</Breadcrumb.Item>
        </Breadcrumb>
        <div className="cq-header__main">
          <h1>异常用户明细</h1>
          <div className="cq-header__action">
            <Button>
              <Link to="/alarm">告警分析</Link>
            </Button>
          </div>
        </div>
      </Header>
      <Layout className="cq-main">
        <Content className="cq-content">
          <section className="cq-card">
            <div className="cq-card__header">
              <div className="cq-header__main">
                <div>
                  <Radio.Group
                    buttonStyle="solid"
                    className="cq-radio"
                    value={timeRadioRange}
                    defaultValue={1}
                    onChange={(e) => changeTimeRadioRange(e.target.value)}
                  >
                    <Radio.Button value={1}>今日</Radio.Button>
                    <Radio.Button value={7}>7 天</Radio.Button>
                    <Radio.Button value={30}>30 天</Radio.Button>
                  </Radio.Group>
                  <RangePicker
                    value={rangeValue}
                    onChange={(dates) => {
                      if (dates === null) {
                        return setFilterParams({
                          ...filterParams,
                          timeRange: null,
                        })
                      }
                      const [start, end] = formatDateRange(dates)
                      if (start && end) {
                        setFilterParams({
                          ...filterParams,
                          timeRange: [start, end],
                        })
                      }
                    }}
                    allowClear
                  />
                </div>
                <div className="cq-header__action">
                  <Select
                    mode="multiple"
                    placeholder="用户"
                    className="action-block"
                    value={executors}
                    onChange={(selected) => {
                      setFilterParams({ ...filterParams, executors: selected })
                    }}
                    maxTagCount={2}
                    allowClear
                    style={{ minWidth: 144 }}
                  >
                    {userList.map((user) => {
                      const { userId, userName } = user
                      return (
                        <Option key={userId} value={userId}>
                          {userName}
                        </Option>
                      )
                    })}
                  </Select>
                  <Popconfirm
                    title="确定导出当前筛选结果？"
                    className="action-block"
                    onConfirm={() =>
                      exportAbnormalUserReports(getRequestParams(filterParams))
                    }
                    placement="bottomRight"
                  >
                    <Button type="primary">导出</Button>
                  </Popconfirm>
                </div>
              </div>
            </div>
            <div className="cq-main pd-20">
              <Table
                loading={loadingTableData}
                dataSource={dataSource}
                columns={columns}
                pagination={{
                  ...pageState,
                  onChange: (page, pageSize) => {
                    setPageState({ current: page, pageSize: pageSize || 10 })
                  },
                  total,
                  showQuickJumper: true,
                }}
                rowKey="id"
              />
              <Watermark
                text={watermarkValue?.length ? watermarkValue?.join(' ') : ''}
                pack={false}
                zIndex={99}
                rotate={20}
                visible={watermarkSetting}
                {...watermarkEffect}
              />
            </div>
          </section>
        </Content>
      </Layout>
    </>
  )
}
