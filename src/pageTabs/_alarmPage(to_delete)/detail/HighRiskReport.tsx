import React, { useState, useEffect, useMemo, useCallback } from 'react'
import { Link, useRouteMatch } from 'react-router-dom'
import Watermark from '@pansy/react-watermark'
import { useDispatch, useRequest, useSelector } from 'src/hook'
import {
  Radio,
  Layout,
  Breadcrumb,
  Table,
  Select,
  Popconfirm,
  Button,
} from 'antd'
import { ConnectionIcon, DatePicker } from 'src/components'
import dayjs, { Dayjs } from 'dayjs'
import { ColumnsType } from 'antd/lib/table'
import { setPath } from '../alarmSlice'
import { getUnixTimeRange } from '../analysis/Charts/util'
import {
  getHighRiskReportDetail,
  AlertLogRequest,
  DataSourceType,
  getAllUsers,
  getSupportedConnectionTypes,
  exportHighRiskReports,
  SqlType,
  SqlTypes,
  RiskLevel,
  RuleNames,
  RuleName,
} from 'src/api'
import './detail.scss'
import { formatDateRange } from 'src/util'

const { Header, Content } = Layout

const { RangePicker } = DatePicker
const { Option } = Select

interface FilterParams {
  executors: string[]
  timeRange: [number, number] | null
  ruleNames?: RuleName[]
  dbTypes?: DataSourceType[]
  sqlTypes?: SqlType[]
  riskLevels?: RiskLevel[]
}

// 根据组件 filterParams 状态构造出查询请求所需要的参数
const getRequestParams = (filterParams: FilterParams) => {
  const { executors, dbTypes, ruleNames, sqlTypes, riskLevels, timeRange } =
    filterParams
  // 构造查询参数
  const params: Partial<AlertLogRequest> = {
    executeBeginMs: timeRange?.[0],
    executeEndMs: timeRange?.[1],
  }
  if (executors.length) {
    params.executors = executors
  }
  if (ruleNames && ruleNames[0]) {
    params.ruleNames = ruleNames
  }
  if (dbTypes && dbTypes[0]) {
    params.dbTypes = dbTypes.map((dataSourceType) =>
      dataSourceType.toUpperCase(),
    )
  }
  if (sqlTypes && sqlTypes[0]) {
    params.sqlTypes = sqlTypes
  }
  if (riskLevels && riskLevels[0]) {
    params.riskLevels = riskLevels
  }
  return params
}

export const HighRiskReport: React.FC = () => {
  // 时间
  const [timeRadioRange, setTimeRadioRange] = useState<1 | 7 | 30>(7)

  const [filterParams, setFilterParams] = useState<FilterParams>({
    executors: [],
    timeRange: [
      dayjs().subtract(7, 'day').startOf('d').valueOf(),
      dayjs().endOf('d').valueOf(),
    ],
  })
  // 全部用户列表
  const [userList, setUserList] = useState<any[]>([])

  // 分页器状态
  const [pageState, setPageState] = useState({
    current: 1,
    pageSize: 10,
  })
  const { watermarkEffect } = useSelector((state: any) => state.login)

  // table dataSource, total and loading status
  const [dataSource, setDataSource] = useState<any[]>([])
  const [total, setTotal] = useState<number>()
  const [loadingTableData, setLoadingTableData] = useState<boolean>(false)
  const match = useRouteMatch()
  const dispatch = useDispatch()

  const { executors, timeRange, ruleNames, dbTypes, sqlTypes, riskLevels } =
    filterParams

  //切换天数
  const changeTimeRadioRange = (val: any) => {
    setTimeRadioRange(val)
    const { beginTimeMs, endTimeMs } = getUnixTimeRange(val)
    setFilterParams({
      ...filterParams,
      timeRange: [beginTimeMs, endTimeMs],
    })
  }
  const rangeValue = useMemo(() => {
    if (timeRange === null) {
      return null
    } else {
      const range = timeRange.map((timestamp) => dayjs(timestamp)) as [
        Dayjs,
        Dayjs,
      ]
      return range
    }
  }, [timeRange])

  // 获取分页数据
  const getDataSource = useCallback(
    async (params) => {
      const { current, pageSize } = pageState
      const offset = (current - 1) * pageSize
      const limit = pageSize
      try {
        setLoadingTableData(true)
        const { data, total } = await getHighRiskReportDetail({
          ...params,
          limit,
          offset,
        })
        setDataSource(data)
        setTotal(total)
      } catch {
        setDataSource([])
        setTotal(undefined)
      } finally {
        setLoadingTableData(false)
      }
    },
    [pageState],
  )

  useEffect(() => {
    const params = getRequestParams(filterParams)
    getDataSource(params)
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [pageState, getDataSource])

  useEffect(() => {
    setPageState({ current: 1, pageSize: 10 })
  }, [filterParams])

  useEffect(() => {
    // alarm 下子路由状态保持
    if (match) {
      dispatch(setPath(match.path))
    }
  }, [dispatch, match])

  useEffect(() => {
    // 获取用户列表
    const getUserList = async () => {
      try {
        const users = await getAllUsers()
        setUserList(users)
      } catch {}
    }
    getUserList()
  }, [])

  const columns: ColumnsType<any> = [
    // todo: ColumnsType const
    { dataIndex: 'executor', title: '操作人', width: 120, ellipsis: true },
    {
      dataIndex: 'ruleName',
      title: '规则名字',
      width: 192,
      ellipsis: true,
    },
    {
      dataIndex: 'sqlType',
      title: '语句类型',
      ellipsis: true,
    },
    {
      dataIndex: 'executeSql',
      title: '执行SQL',
      ellipsis: true,
    },
    {
      dataIndex: 'executeBegin',
      title: '开始时间',
      ellipsis: true,
    },
    {
      dataIndex: 'executeEnd',
      title: '结束时间',
      ellipsis: true,
    },
    { dataIndex: 'serverIp', title: 'IP地址', width: 120, ellipsis: true },
    { dataIndex: 'executeCost', title: '耗时(ms)', width: 88, ellipsis: true },
  ]

  const { data: connectionTypes, loading: loadingConnectionTypes } = useRequest(
    getSupportedConnectionTypes,
    {
      formatResult: (data) => {
        return data?.map((dataSource) => {
          const { connectionForm } = dataSource
          const connectionFormFiltered = connectionForm.filter(
            ({ label }) => label !== '监控开关',
          )
          return Object.assign({}, dataSource, {
            connectionForm: connectionFormFiltered,
          })
        })
      },
    },
  )
  const ConnectionTypeOptions = connectionTypes?.map(({ dataSourceName }) => ({
    label: (
      <>
        <ConnectionIcon type={dataSourceName} />
        {dataSourceName}
      </>
    ),
    value: dataSourceName,
  }))

  const SqlTypeOptions = SqlTypes.map((type) => ({ label: type, value: type }))
  const RuleNameOptions = RuleNames.map((type) => ({
    label: type,
    value: type,
  }))

  // watermark
  const { watermarkSetting, watermarkValue } = useSelector(
    (state) => state.login.userInfo,
  )

  return (
    <>
      <Header className="cq-header">
        <Breadcrumb className="breadcrumb">
          <Breadcrumb.Item>告警</Breadcrumb.Item>
          <Breadcrumb.Item>告警分析</Breadcrumb.Item>
          <Breadcrumb.Item>高危动作明细</Breadcrumb.Item>
        </Breadcrumb>
        <div className="cq-header__main">
          <h1>高危动作明细</h1>
          <div className="cq-header__action">
            <Button>
              <Link to="/alarm">告警分析</Link>
            </Button>
          </div>
        </div>
      </Header>
      <Layout className="cq-main">
        <Content className="cq-content">
          <section className="cq-card">
            <div className="cq-card__header">
              <div className="cq-header__main">
                <div>
                  <Radio.Group
                    buttonStyle="solid"
                    className="cq-radio"
                    value={timeRadioRange}
                    defaultValue={1}
                    onChange={(e) => changeTimeRadioRange(e.target.value)}
                  >
                    <Radio.Button value={1}>今日</Radio.Button>
                    <Radio.Button value={7}>7 天</Radio.Button>
                    <Radio.Button value={30}>30 天</Radio.Button>
                  </Radio.Group>
                  <RangePicker
                    value={rangeValue}
                    onChange={(dates) => {
                      if (dates === null) {
                        return setFilterParams({
                          ...filterParams,
                          timeRange: null,
                        })
                      }
                      const [start, end] = formatDateRange(dates)
                      if (start && end) {
                        setFilterParams({
                          ...filterParams,
                          timeRange: [start, end],
                        })
                      }
                    }}
                    allowClear
                  />
                </div>
                <div className="cq-header__action">
                  <Select
                    mode="multiple"
                    placeholder="用户"
                    className="action-block"
                    value={executors}
                    onChange={(selected) => {
                      setFilterParams({ ...filterParams, executors: selected })
                    }}
                    maxTagCount={2}
                    allowClear
                    style={{ minWidth: 144 }}
                  >
                    {userList.map((user) => {
                      const { userId, userName } = user
                      return (
                        <Option key={userId} value={userId}>
                          {userName}
                        </Option>
                      )
                    })}
                  </Select>
                  <Select
                    placeholder="规则名称"
                    className="action-block"
                    value={ruleNames}
                    onChange={(ruleNames) => {
                      setFilterParams({ ...filterParams, ruleNames })
                    }}
                    allowClear
                    style={{ minWidth: 130 }}
                    mode="multiple"
                    maxTagCount={2}
                    options={RuleNameOptions}
                  ></Select>
                  <Select
                    placeholder="数据源类型"
                    className="action-block"
                    value={dbTypes}
                    onChange={(dbTypes) => {
                      setFilterParams({ ...filterParams, dbTypes })
                    }}
                    allowClear
                    style={{ minWidth: 144 }}
                    mode="multiple"
                    maxTagCount={2}
                    options={ConnectionTypeOptions}
                    loading={loadingConnectionTypes}
                  ></Select>
                  <Select
                    placeholder="语句类型"
                    className="action-block"
                    value={sqlTypes}
                    onChange={(sqlTypes) => {
                      setFilterParams({ ...filterParams, sqlTypes })
                    }}
                    allowClear
                    style={{ minWidth: 96 }}
                    mode="multiple"
                    maxTagCount={2}
                    options={SqlTypeOptions}
                  ></Select>
                  <Select
                    placeholder="危险等级"
                    className="action-block"
                    value={riskLevels}
                    onChange={(riskLevels) => {
                      setFilterParams({ ...filterParams, riskLevels })
                    }}
                    allowClear
                    style={{ minWidth: 96 }}
                    mode="multiple"
                  >
                    <Option key="2" value="2">
                      高
                    </Option>
                    <Option key="1" value="1">
                      中
                    </Option>
                    <Option key="0" value="0">
                      低
                    </Option>
                  </Select>
                  <Popconfirm
                    title="确定导出当前筛选结果？"
                    className="action-block"
                    onConfirm={() =>
                      exportHighRiskReports(getRequestParams(filterParams))
                    }
                    placement="bottomRight"
                  >
                    <Button type="primary">导出</Button>
                  </Popconfirm>
                </div>
              </div>
            </div>
            <div className="cq-main pd-20">
              <Table
                loading={loadingTableData}
                dataSource={dataSource}
                columns={columns}
                pagination={{
                  ...pageState,
                  onChange: (page, pageSize) => {
                    setPageState({ current: page, pageSize: pageSize || 10 })
                  },
                  total,
                  showQuickJumper: true,
                }}
                rowKey="id"
              />
              <Watermark
                text={watermarkValue?.length ? watermarkValue?.join(' ') : ''}
                pack={false}
                rotate={20}
                zIndex={99}
                visible={watermarkSetting}
                {...watermarkEffect}
              />
            </div>
          </section>
        </Content>
      </Layout>
    </>
  )
}
