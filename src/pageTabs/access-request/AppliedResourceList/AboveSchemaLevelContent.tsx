import React, { useEffect } from "react";
import {
  getApplyCartPermissionsPanelUpObject,
  getAutomaticObjectPermission,
  IObjectPermissionRes,
} from 'src/api';
import { useRequest } from 'src/hook';
import AboveSchemaLevelComponent from '../components/AboveSchemaLevelComponent';
import _ from "lodash";

export const AboveSchemaLevelContent = ({
  isGroupTab,
  selectTreeItem,
  flowUUID,
  flowMainUUID,
  cardClassName,
}: {
  isGroupTab?: boolean;
  selectTreeItem?: any;
  needRefresh?: boolean;
  flowUUID?: string;
  flowMainUUID: string | undefined;
  cardClassName?: any
}) => {

  const { data: list, loading, run: runGetApplyAutomaticObjectPermission } = useRequest(getApplyCartPermissionsPanelUpObject, {
    manual: true
  })

  const {
    loading: groupTabPermissionLoading,
    data: groupTabPermissionList,
    run: runGetGroupTabPermission
  } = useRequest<IObjectPermissionRes>(
    getAutomaticObjectPermission, {
    manual: true
  })

  const queryPermissionsInstance = () => {
    const { connection, dataSourceType, nodePathWithType } = selectTreeItem || {};
    const { connectionType } = connection || {};

    if (nodePathWithType && (connectionType || dataSourceType)) {
      runGetApplyAutomaticObjectPermission({
        flowMainUUID,
        nodePath: nodePathWithType,
        dataSourceType: dataSourceType || connectionType
      })
    }
  }

  const queryPermissionsGroup = () => {
    const { connection, dataSourceType, nodePathWithType, roleId } = selectTreeItem || {};
    const { connectionType } = connection || {};
    if (selectTreeItem.newNodeType === "datasource") {
      runGetGroupTabPermission({
        roleId,
        dataSourceType: selectTreeItem.id,
        flowMainUUID: flowUUID,
        isFlow: true
      });
    }else if (selectTreeItem.newNodeType === "group") {
      runGetGroupTabPermission({
        roleId,
        groupId: selectTreeItem.id,
        dataSourceType: dataSourceType || connectionType,
        flowMainUUID: flowUUID,
        isFlow: true
      });
    }else if (nodePathWithType && (connectionType || dataSourceType)) {
      runGetGroupTabPermission({
        roleId,
        nodePath: nodePathWithType,
        dataSourceType: dataSourceType || connectionType,
        flowMainUUID: flowUUID,
        isFlow: true
      })
    }
  }

  useEffect(() => {
    if(_.isEmpty(selectTreeItem)) return
    if (!flowMainUUID) return

    if (isGroupTab) {
      queryPermissionsGroup();
    } else {
      queryPermissionsInstance();
    }
  }, [selectTreeItem.key, isGroupTab, selectTreeItem, flowMainUUID]);

  return (
    <AboveSchemaLevelComponent
      selectTreeItem={selectTreeItem}
      list={isGroupTab?groupTabPermissionList : list}
      isEdit={false}
      loading={isGroupTab? groupTabPermissionLoading : loading}
      viewAppliedDetail={true}
      cardClassName={cardClassName}
    />
  )
}