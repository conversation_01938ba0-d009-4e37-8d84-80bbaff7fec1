import React from "react";
import styles from './index.module.scss';
import { TitleWithPipe } from "./components/TitleWithPipe";
import { Button, Card, Divider } from "antd";
import { DownOutlined, UpOutlined } from "@ant-design/icons";
import { isNonEmptyObject, mergePermissions } from "src/util";
import { useTranslation } from "react-i18next";

interface Iprops {
  data: any;
  visible: boolean;
  callback: () => void;
}

export const AboveSchemaLevelContentOperationDetail = (props: Iprops) => {
  const { data, visible, callback } = props;
  const { t } = useTranslation()
  const addPermission = mergePermissions("addPermission", data);
  const removePermission = mergePermissions("removePermission", data);

  const renderCard = (cardClassName: string, cardTitle: string, data: any) => {
    if (!data) {
      return;
    }

    return (
      <Card title={cardTitle} className={styles[cardClassName]} bordered={false}>
        {
          !isNonEmptyObject(data) && <div className={styles.cardItem}>{t('flow_no_data')}</div>
        }
        {
          Object?.entries(data)?.map(([key, value]) => {
            return (
              <div className={styles.cardItemWrap} key={key}>
                <TitleWithPipe title={key} />
                {
                  Array.isArray(value) && value?.map((item: any, index: number) => {
                    return (
                      <div className={styles.cardItem} key={index+item}>{item}</div>
                    )
                  })
                }
              </div>
            )
          })
        }
      </Card>
    )
  }

  return (
    <>
      <div className={styles.contentTopDetail}>
        {renderCard("addPermission", t('flow_added_permissions'), addPermission)}
        {renderCard("rmPermission", t('flow_removed_permissions'), removePermission)}
      </div>
      <Divider orientation="center" className={styles.dividerWrap}>
        <Button type='link' onClick={() => { callback() }}>
          {visible
            ? <>
              {t('flow_collapse')} <UpOutlined />
            </>
            : <>
              {t('flow_expand_details')} <DownOutlined />
            </>
          }
        </Button>
      </Divider>
    </>
  )
}