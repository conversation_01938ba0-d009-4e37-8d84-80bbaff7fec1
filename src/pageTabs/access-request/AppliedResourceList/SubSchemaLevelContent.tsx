import React, { useEffect } from "react";
import {
  getApplyCartPermissionsPanelObject,
  getPermissionsPanelObject,
} from 'src/api';
import { useRequest } from 'src/hook';
import SubchemaLevelComponent from '../components/SubSchemaLevelComponent';

export const SubSchemaLevelContent = ({
  isGroupTab,
  selectTreeItem,
  flowMainUUID,
  flowUUID
}: {
  isGroupTab?: boolean;
  selectTreeItem?: any;
  flowMainUUID: string | undefined;
  flowUUID?: string;
}) => {

  const { data: objectResult, loading, run: runGetApplyPermissionsPanelObject } = useRequest(
    (params) => {
      if (isGroupTab) {
        return getPermissionsPanelObject({
          ...params,
          isFlow: true,
          flowMainUUID: flowUUID,
          roleId: selectTreeItem?.roleId,
          filter: true
        })
      }
      return getApplyCartPermissionsPanelObject(params)
    },
    { manual: true }
  )

  useEffect(() => {

    if (!selectTreeItem || !flowMainUUID) return;
    
    const {
      nodeName,
      nodePathWithType,
      nodeType,
      dataSourceType,
      sdt,
    } = selectTreeItem;
    const connectionType = dataSourceType || sdt?.connectionType
    const params = {
      connectionId: null,
      connectionType,
      nodeType,
      nodeName,
      nodePathWithType,
      flowMainUUID,
    };
    runGetApplyPermissionsPanelObject(params)
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectTreeItem?.key, flowMainUUID]);


  return (
    <SubchemaLevelComponent
      loading={loading}
      objectResult={objectResult}
      selectTreeItem={selectTreeItem}
      viewAppliedDetail={true}
      canEdit={false}
    />
  )
}