import { Card, Checkbox, Col, Row, Empty } from "antd"
import React, { useEffect, useMemo, memo } from "react"
import { getDBTypeAndNodeTypeAuth } from "src/api"
import { useRequest, useSelector } from "src/hook"
import { useTranslation } from "react-i18next"
import styles from "./index.module.scss"

const OpePermissionDetails = memo((props: any) => {
  const {  dataList = []} = props
  const { t } = useTranslation()
  const { selectedTreeNode } = useSelector(state => state.appliedResourceList);
  const { dataSourceType, nodeType, nodePathWithType } = selectedTreeNode || {}
  const { businessDataList } = dataList?.[0];

  // 申请提权的节点nodePathWithType和提权列表operationList
  const applyList = useMemo(() => businessDataList?.map((item: any) => ({
    nodePathWithType: item?.nodePathWithType,
    operationList: JSON.parse(item?.operationList)
  })), [JSON.stringify(businessDataList)])

  //操作权限
  const { data: opePermissions = [], run: runGetOpePermissions, loading } = useRequest(getDBTypeAndNodeTypeAuth, {
    manual: true,
    formatResult(res: any) {
      return res?.map((item: any) => ({ label: item?.operationName, value: item?.operation, checked: false }))
    }
  })

  // 获取当前所选节点下的操作权限
  useEffect(() => {
    // 调用接口，获取操作权限options
    if(!dataSourceType || !nodeType) return
    runGetOpePermissions({
      dataSourceType,
      objectType: nodeType === 'oracleUser' ? 'schema' : nodeType,
    })
  }, [dataSourceType, nodeType])


  const optRes = useMemo(() => {
  
     if (loading) return []

    if (!nodePathWithType || !applyList?.length) {
      return opePermissions
    }
    else {
      const isApplyNode = applyList?.find((item: any) => item.nodePathWithType === nodePathWithType)
    
      if (isApplyNode) {
        const selectedPerms = opePermissions?.filter((perm: any) => isApplyNode?.operationList?.includes(perm.value) )?.map((item: any) => ({ ...item, checked: true }));
 
      return selectedPerms;
      }
      else {
        const hasSelectedPerm = opePermissions?.filter((o: any) => o?.hasSelected);
        return hasSelectedPerm
      }
    }

  }, [nodePathWithType, nodeType, applyList, opePermissions, loading])

  return <Card
    bordered={false}
    className={styles.opePermissionDetails}
    title={t('flow_operation_permissions')}
    loading={loading}
  >
    {
      !optRes?.length && !loading ? 
        <div className={styles.noSelectedPerm}>
          <Empty description={t('flow_operation_noDdata')} />
        </div>
      :
        <Row gutter={[24, 6]}>
          {
          
          optRes.map((item: any) => <Col key={item.value} span={8}>
              <div className={styles.opePerItem}>
                <Checkbox disabled checked={item.checked}>{item?.label}</Checkbox>
                <div className={styles.itemValue}>{item?.value}</div>
              </div>
            </Col>)
          }
        </Row>
     }
  </Card>
})

export default OpePermissionDetails