
.titleBeforeWrap {
  display: flex;
  font-size: 14px;
  font-weight: 550;

  .titleBefore {
    width: 5px;
    height: 14px;
    margin-top: 5px;
    margin-right: 5px;
    background-color: #2366fe,;
    border-radius: 2px;
  }
}
.opePermissionDetails {

  .noSelectedPerm {
    margin: auto auto;
    color: #00000073;
  }
  .opePerItem {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .itemValue {
      color: rgba(0, 0, 0, 0.25);
    }
  }
    :global {
      .ant-card-head {
        border: none;
  
        .ant-card-head-title {
          font-size: 14px;
          position: relative; 
          display: inline-block; 
          padding-left: 10px;
          
        }
        .ant-card-head-title::before {
            content: "";
            position: absolute;
            left: 0px;
            top: 50%;
            transform: translateY(-50%);
            width: 4px;
            height: 16px;
            background-color: #2366fe;
          }
      }
  
      .ant-card-body {
        padding-top: 0;
        max-height: 500px;
        min-height: 212px;
        overflow: hidden auto;    
      }
    }
}