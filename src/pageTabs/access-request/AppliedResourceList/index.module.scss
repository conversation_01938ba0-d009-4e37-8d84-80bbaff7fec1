@import 'src/styles/variables';

.mt10 {
	margin-top: 10px;
}

.mt20 {
	margin-top: 20px;
}

.mt30 {
	margin-top: 30px;
}

.mr4 {
	margin-right: 4px;
}

.mr8 {
	margin-right: 8px;
}

.mr10 {
	margin-right: 10px;
}

.mr20 {
	margin-right: 20px;
}

.mb2 {
	margin-bottom: 2px;
}

.mb4 {
	margin-bottom: 4px;
}

.mb10 {
	margin-bottom: 10px;
}

.mb20 {
	margin-bottom: 20px;
}

.ml4 {
	margin-left: 4px;
}

.ml10 {
	margin-left: 10px;
}

.ml20 {
	margin-left: 20px;
}

.padding0 {
	padding: 0;
}

.colorRed {
	color: red;
}

.displayInlineFlex {
	display: inline-flex;
}

.flex1 {
	flex: 1
}

.flexItem {
	display: flex;
	align-items: center;
}

.colore999 {
	color: #999;
}

.color008dff {
	color: #3357ff;
}

.colorgreen {
	color: green;
}

.colorf00 {
	color: #f00;
}

.fb {
	font-weight: bold;
}

.db {
	display: block;
}

.indb {
	display: inline-block;
	padding-right: 0;
}

.deleteLine {
	text-decoration: line-through;
}

.listItems {
	display: flex;
	align-items: flex-start;

	.desc {
		flex: 1;
		flex-wrap: wrap;
	}
}

.disabled {
	cursor: not-allowed;
	color: #999;
}

.options {
	cursor: pointer;
	color: var(--primary-color);

	:global {
		.ant-dropdown-menu-item {
			color: var(--primary-color);
			max-width: 300px;
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;
		}

		.ant-dropdown-menu-item-disabled,
		.ant-dropdown-menu-submenu-title-disabled {
			color: rgba(0, 0, 0, 0.25);
		}
	}
}

.optionTxt {
	cursor: pointer;

	&:hover {
		color: var(--primary-color);
	}

	:global {
		.ant-dropdown-menu-item {
			&:hover {
				color: var(--primary-color);
			}
		}
	}
}

.searchBtn {
	width: 100%;

	:global {
		.ant-input-search-icon {
			margin-left: 4px;
			padding: 0 9px 0 0;
		}

		.ant-input-clear-icon {
			padding: 4px;
			color: rgba(0, 0, 0, 0.52);
		}
	}
}

.menuWrap {
	max-height: 46vh;
	overflow-y: auto;
}

.groupAuthorizationWrap {
	height: fit-content;
	padding: 0 10px 10px;

	.breadcrumb {
		padding: 10px;
		font-size: 16px;
	}

	.content {
		min-height: 300px;
		display: flex;
		height: 100%;

		.leftWrap,
		.rightWrap {
			background-color: #F7F9FC;
			border-radius: 4px;
			height: 100%;
			// height: calc(100vh - 110px);
		}

		.leftWrap {
			padding: 16px 14px;
			margin-right: 10px;

			.treeHeader {
				display: flex;
				align-items: center;
				justify-content: space-between;
				margin-bottom: 10px;
			}

			.addBtn {
				width: 100%;
				margin-bottom: 10px;
				display: flex;
				align-items: center;
				justify-content: cessnter;
			}

			.treeWrap, .treeContent {
				max-height: calc(100vh - 216px);
				overflow-y: auto;
				background-color: unset;

				:global {
					.ant-tree-treenode {
						width: 100%;
						padding: 0;
						margin-bottom: 4px;
						background-color: #fff;
						height: 32px;
						align-items: center;
						border-radius: 4px;
					}

					.ant-tree-node-content-wrapper {
						flex: 1;
					}

					.ant-tree-node-content-wrapper:hover,
					.ant-tree-treenode:hover {
						background-color: #d6e5ff;
					}

					.ant-tree-node-content-wrapper {
						transition: none;
					}

					.ant-tree-treenode-selected {
						background-color: #d6e5ff;
					}

					.ant-tree-treenode-selected .ant-tree-switcher,
					.ant-tree-treenode .ant-tree-node-content-wrapper.ant-tree-node-selected {
						color: initial;
						background-color: #d6e5ff;
					}
				}
			}

			.treeTitleItem {
				display: flex;
				align-items: center;

				.titleTxt {
					flex: 1;
					text-overflow: ellipsis;
					white-space: nowrap;
					overflow: hidden;
				}
				.titleTxtWrap {
					display: flex;
					flex: 1 1;
					text-overflow: ellipsis;
					white-space: nowrap;
					overflow: hidden;
				}

				.deleteNodeIcon {
					margin: 0 10px;
					font-size: 10px;
					color: #D8D8D8;
				}
			}
		}

		.rightWrap {
			padding: 16px;
			flex: 1;
      height: auto;

			.operationContent {
				position: relative;
				background-color: #fff;

				// height: calc(100vh - 148px);
				.title {
					height: 56px;
					padding: 12px 0px;
					display: flex;
					align-items: center;
					justify-content: space-between;
					border-bottom: 1px solid #e5e5e5;

					.boldFont {
						font-weight: bold;
						font-size: 18px;
						padding-left: 20px;
					}
				}

        .dividerWrap {
          margin: 0px;
        }

        .contentTopDetail {
					padding: 10px 0px;
					overflow-y: auto;
          display: flex;
          .addPermission, .rmPermission {
            width: 50%;
            border-radius: 0;
            :global {
              .ant-card-body {
                display: flex;
              }
            }
            .cardItemWrap {
              width: 25%;
              .cardItem {
                padding: 3px;
                padding-left: 10px;
              }
            }
          }
          .addPermission {
            :global {
              .ant-card-body {
                border-right: 2px solid #f0f0f0;
              }
            }
          }
				}

				.contentDetail {
					padding: 0px 20px 10px 20px;
					overflow-y: auto;

					.contentCardClassName {
						height: auto;
					}

					.userContent {
						display: inline-block;
						width: 320px;
						height: 200px;
						overflow-y: auto;
						border-radius: 4px;
						padding: 4px 4px;
						border: 1px solid #d9d9d9;

						:global {
							.ant-tag {
								margin: 2px 0 2px 2px;
							}
						}
					}
				}

				.addRequestFooter {
					z-index: 100;
					height: 48px;
					line-height: 48px;
					position: absolute;
					bottom: 0;
					width: 100%;
					padding: 0 30px;
					text-align: right;
					background-color: #fff;
					box-shadow: 0px 4px 16px 0px rgba(0, 0, 0, 0.16);

					.addRequestBtn {
						background-color: #F0F1F5;
						display: inline-block;
						height: 32px;
						padding: 0 13px;
						line-height: 32px;
						border-radius: 4px;
						cursor: pointer;
					}

					.hightlightBtn {
						background-color: #3357ff;
						color: #fff;
					}
				}
			}
		}
	}
}

.tooltipStyle {
	:global {
		.ant-tooltip-inner {
			width: 500px;
		}
	}

	.tooltipTitle {
		text-align: center;
	}
}

.addUserModal {
	.content {
		display: flex;
		margin-bottom: 20px;
		padding-right: 12px;
		height: 370px;

		.left {
			width: 350px;

			.treeWrap {
				padding-right: 20px;
				max-height: 300px;
				overflow-y: auto;
			}
		}

		.right {
			padding-left: 16px;
			width: 222px;
			border-left: 1px solid #e5e5e5;

			.userWrap {
				padding-right: 20px;
				max-height: 338px;
				overflow-y: auto;

				.selectItem {
					margin-bottom: 10px;
					display: flex;
					align-items: center;
					justify-content: space-between;
				}
			}
		}

		.title {
			margin-bottom: 10px;
			font-weight: 600;
		}
	}

	.radioGroupWrap {
		width: 100%;
		max-height: 120px;
		overflow-y: auto;

		:global {
			.ant-radio-wrapper {
				max-width: 136px;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
			}
		}
	}

	.searchUserListWrap {
		padding-right: 20px;
		max-height: 300px;
		overflow-y: auto;
	}
}

.resizableBox {
	height: auto !important;
}

.resizeHandle {
	position: absolute;
	right: -3px;
	top: calc(50% - 24px);
	font-size: 16px;
	cursor: col-resize;
	color: rgba(0, 0, 0, 0.85);
}

.historyContentWrap {
	margin-top: 30px;
	padding-right: 20px;
	height: calc(100vh - 220px);
	overflow-y: auto;
}

.customAuthorizeTab {
	:global {
		.ant-tabs-content {
			max-height: 46vh;
			overflow-y: auto;
		}
	}
}

.customUaTable {
	:global {
		.invalidGreyBg {
			color: #999;

			&>td {
				color: #999 !important;
			}
		}
	}
}