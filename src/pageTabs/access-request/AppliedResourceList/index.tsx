import React, { useEffect, useMemo, useState } from 'react'
import { SearchOutlined, PlusOutlined } from '@ant-design/icons'
import { Card, Button, Input, Tabs } from 'antd'
import { useHistory } from 'react-router-dom'

import * as _ from 'lodash';
import classnames from 'classnames'
import { ResizableBox, ResizableProps } from 'react-resizable'

import { useDispatch, useSelector } from 'src/hook'
import { Iconfont } from "src/components"
import {
  setNewTreeData,
  fetchConnections,
  setFetchConnectionLoading,
} from './appliedResourceListSlice'
import TreeComponent from './TreeComponent'
import { AboveSchemaLevelContent } from './AboveSchemaLevelContent';
import { SubSchemaLevelContent } from './SubSchemaLevelContent';
import styles from './index.module.scss'
import { AboveSchemaLevelContentOperationDetail } from './AboveSchemaLevelContentOperationDetail'
import OpePermissionDetails from './components/OpePermissionDetails';
import { useTranslation } from 'react-i18next';
import GroupTabTreeComponent from './GroupTabTreeComponent';
import { setMineApplyPageState, setMineApplyDetailParams } from 'src/pageTabs/access-request/accessRequestSlice'

const ResizableBoxProps: ResizableProps = {
  axis: 'x',
  width: 320,
  height: 0,
  minConstraints: [260, 0],
  maxConstraints: [620, 0],
}

const AppliedResourceList = ({
  flowUUID,
  flowMainUUID, //申请单号
  data
}: {
  flowUUID?: string;
  flowMainUUID: string | undefined;
  data?: any
}) => {
  const history = useHistory()
  const dispatch = useDispatch()
  const { t } = useTranslation()
  const { source } = data
  const isDataManipulation = (source === 1) // 数据操作申请提权工单
  const { permissionList } = useSelector((state) => state?.login)
  const { selectedTreeNode, newTreeData = [], fetchConnectionLoading } = useSelector(state => state.appliedResourceList);
  //是否触发切换  切换tab后 不再控制无数据时默认进去分组视图
  const [isUserToggled, setIsUserToggled] = useState<boolean>(false);
  const [searchValue, setSearchValue] = useState<string>("");
  const [rightWrapWidth, setRightWrapWidth] = useState<string>("");
  const [permissionlist, setPermissionlist] = useState<{ isOnlyRead: boolean, roleNameList: any }>()

  // 展开/收起 visible
  const [permissionDetailBtnVisiable, setPermissionDetailBtnVisiable] = useState<boolean>(true);
  // 新增、删除权限Card visible
  const [isShowPermissionDetail, setIsShowPermissionDetail] = useState<boolean>(false);
  // 获取对应节点的权限列表
  const [permissionDetailData, setPermissionDetailData] = useState<any[]>([]);
  const [tabsActiveKey, setTabsActiveKey] = useState<string>('instanceTab');
  
  // treeData
  useEffect(() => {
    if ( tabsActiveKey === 'groupTab' ) return;

    if (!flowMainUUID) return
    const deboundeFetchInstanceConn = _.debounce(() => {
      dispatch(fetchConnections({flowMainUUID}))
    }, 200)
    deboundeFetchInstanceConn();
    return () => {
      deboundeFetchInstanceConn.cancel();
    }
  }, [dispatch, flowMainUUID, tabsActiveKey])

  useEffect(() => {
 
    //默认实例视图数据为空 且未手动切换tab 自动跳转到分组视图
    setTimeout(() => {
      if (tabsActiveKey === 'instanceTab' && !isUserToggled && !newTreeData?.length && !fetchConnectionLoading) {
     
        setTabsActiveKey('groupTab');
      }
    }, 300)
   
  },[isUserToggled, tabsActiveKey, newTreeData?.length, fetchConnectionLoading])

  useEffect(() => {
    handleLeftWrapResize();

    const pathName = (window.location.pathname.split("/")[1].toUpperCase() as keyof typeof permissionList)
    setPermissionlist(permissionList?.DATABASE_MANAGEMENT?.[pathName])

    return () => {
      dispatch(setNewTreeData([]));
      dispatch(setFetchConnectionLoading(true));
      setTabsActiveKey('instanceTab');
      setIsUserToggled(false);
    }

  }, []);

  const tabsHandleOnChange = (activeKey: string) => {
    setTabsActiveKey(activeKey);
    setIsUserToggled(true);
  }

  const handleSearch = (e: any) => {
    const value = e.target.value;
    setSearchValue(value?.trim());
  };

  const handleLeftWrapResize = () => {
    // @ts-ignore
    const lWidth = document.getElementsByClassName("react-resizable")[0]?.style.width ||
      "320px";
    const width = `calc(100vw - 30px - ${lWidth} )`;
    setRightWrapWidth(width);
  };

  const renderResizeHandle = (
    <div className={styles.resizeHandle}>
      <Iconfont type="icon-handle-8"></Iconfont>
    </div>
  );

  const renderRightContent = useMemo(() => {
    const isGroupTab = tabsActiveKey === "groupTab"
    return (
      <>
      {
        isDataManipulation ? <OpePermissionDetails  dataList={data?.dataList}/>
          : <>
            {
              ['datasource', 'connection', 'database', 'schema', 'oracleUser'].includes(selectedTreeNode?.nodeType) &&
              <AboveSchemaLevelContent
                selectTreeItem={selectedTreeNode}
                cardClassName={styles.contentCardClassName}
                flowMainUUID={flowMainUUID}
                flowUUID={flowUUID}
                isGroupTab={isGroupTab}
              />
            }
            {/* 对象级权限设置 */}
            {
              selectedTreeNode?.nodeType?.toUpperCase()?.endsWith("GROUP") &&
              <SubSchemaLevelContent
                selectTreeItem={selectedTreeNode}
                flowMainUUID={flowMainUUID}
                flowUUID={flowUUID}
                isGroupTab={isGroupTab}
              />
            }
          </>
      }
      </>
    )
  }, [data?.dataList, flowMainUUID, isDataManipulation, selectedTreeNode, tabsActiveKey])

  const handleRenderToSearch = () => {
    history.push('/mine_apply');
    dispatch(setMineApplyPageState('search'))
    dispatch(setMineApplyDetailParams({}))
  }

  return (
    <Card
      title={t('flow_request_resource')}
      className={classnames(styles.borderShow, 'mb20') }
      extra={
        !isDataManipulation && 
        <Button type="primary">
          <span 
            onClick={handleRenderToSearch}
          >
            <PlusOutlined />
            &nbsp;&nbsp;{t('flow_add_resource')}
          </span>
        </Button>
      }
    >
      <div className={styles.groupAuthorizationWrap}>
        <div className={styles.content}>
          <ResizableBox
            className={styles.resizableBox}
            handle={renderResizeHandle}
            onResize={handleLeftWrapResize}
            {...ResizableBoxProps}
          >
            <div className={styles.leftWrap}>
              {/* 搜索框 */}
              <Input
                className={classnames(styles.searchBtn, styles.mb10)}
                prefix={<SearchOutlined />}
                placeholder={t('flow_enter_connection_ip')}
                allowClear
                onChange={handleSearch}
                value={searchValue}
              />
              <Tabs
                defaultActiveKey="instanceTab"
                size="small"
                activeKey={tabsActiveKey}
                onChange={tabsHandleOnChange}
              >
                <Tabs.TabPane tab={t('db.connection.sdt.instance')} key="instanceTab" />
                <Tabs.TabPane tab={t('db.connection.sdt.group')} key="groupTab" />
              </Tabs>

              {/* 树形目录 */}
              {
                tabsActiveKey === "groupTab" ?
                <GroupTabTreeComponent
                  searchValue={searchValue}
                  flowUUID={flowUUID}
                  flowMainUUID={flowMainUUID}
                  permissionlist={permissionlist}
                /> 
                :
                <TreeComponent
                  searchValue={searchValue}
                  flowMainUUID={flowMainUUID}
                  permissionlist={permissionlist}
                />
              }
            </div>
          </ResizableBox>
          <div className={styles.rightWrap} style={{ width: rightWrapWidth }}>
            <div className={styles.operationContent}>
              {
                // base-2.4.20.1 后端去掉了权限移除的逻辑,暂时屏蔽掉
                false && isShowPermissionDetail &&
                <AboveSchemaLevelContentOperationDetail
                  data={permissionDetailData}
                  visible={permissionDetailBtnVisiable}
                  callback={() => {
                    setPermissionDetailBtnVisiable((val: boolean) => { return !val })
                  }}
                />
              }
              {
                (permissionDetailBtnVisiable || !isShowPermissionDetail) &&
                <div className={styles.contentDetail}>
                  {renderRightContent}
                </div>
              }
            </div>
          </div>
        </div>
      </div>
    </Card>
  );
}

export default AppliedResourceList
