.mt27 {
	margin-top: 27px;
}
.mb27 {
	margin-bottom: 27px;
}
.mr20 {
  margin-right: 20px;
}
.ml20 {
  margin-left: 20px;
}
.ml10 {
  margin-left: 10px;
}
.submitApplicationWrap {
  height: 100vh;
  padding: 10px 32px;
  .headers {
    display: flex;
    // padding: 10px;
    justify-content: space-between;
    align-items: center;
  }
	.breadcrumb {
		// padding: 10px;
		font-size: 16px;
	}
  .content {
		min-height: 300px;
		display: flex;
		flex-direction: column;
	}
  :global {
		.ant-breadcrumb-separator {
			color: #D8D8D8;
		}
	}
}
.searchTableWrap {
  background: #FFFFFF;
  border-radius: 4px;
  width: 100%;
  height: 100%;
  padding: 10px;
  .headers {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 10px;
    .headerLeft {
      display: flex;
    }
    .headerRight {
      .btn {
        display: flex;
        align-items: center;
        .btnText {
          margin: 0 10px;
        }
        .count {
          display: inline-block;
          width: 25px;
          height: 25px;
          border: 3px solid red;
          border-radius: 50%;
          font-size: 8px;
          color: red;
        }
      }
    }
  }
}
.tablePage {
	:global {
		.ant-table-thead > tr > th {
			// color: $sub-text-color;
      background-color: #F7F9FC;
      height: 48px;
		}
		.ant-table-row{
			height: 48px;
		}
    .ant-table-expanded-row .ant-table-wrapper{
			padding: 0 0 0 12px;
		}
	}
	.actionsBtn {
		span {
			cursor: pointer;
			color: #3262FF;
			text-decoration: none;
			margin: 5px
		}
	}
}
.subBtn {
  display: flex;
  align-items: center;
}
.icon {
  font-size: 16px;
  color: #D8D8D8;
  cursor: pointer;
}
.options {
	cursor: pointer;
  color: var(--primary-color);
	:global {
		.ant-dropdown-menu-item {
			color: var(--primary-color);
			max-width: 300px;
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;
		}
		.ant-dropdown-menu-item-disabled, .ant-dropdown-menu-submenu-title-disabled {
			color: rgba(0, 0, 0, 0.25);
		}
	}
}
.optionTxt {
	cursor: pointer;
	&:hover {
		color: var(--primary-color);
	}
	:global {
		.ant-dropdown-menu-item {
			&:hover {
				color: var(--primary-color);
			}
		}
	}
}
.menuWrap {
	max-height: 46vh;
	overflow-y: auto;
}
.deleteLine {
	text-decoration: line-through;
}
.disabled {
	cursor: not-allowed;
	color: #999;
}
.colore999 {
	color: #999;
}

.permissionTooltip {
  max-width: none;
  max-height: 200px;
  overflow-y: scroll;
}