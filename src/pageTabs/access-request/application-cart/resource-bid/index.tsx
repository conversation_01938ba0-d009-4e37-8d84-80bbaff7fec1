// 访问申请-我的申请-申请清单（申请资源：可编辑）
import React, { FC, useEffect, useMemo, useState } from 'react';
import { Button, Card, Dropdown, Menu, Table, message, Tooltip, Popconfirm } from 'antd';
import { useHistory } from 'react-router-dom';
import {
  CloseCircleOutlined,
  DownOutlined,
  ExclamationCircleOutlined,
  PlusOutlined,
} from '@ant-design/icons';
import { columnsDetail, detailExpamdedRow } from '../../common/columns';
import { useDispatch, useRequest, useSelector } from 'src/hook';
import {
  getPermissionTemplateInfo,
  getUserRoleNocheck,
  saveDraftFat,
  getToolTemplateInfo,
  getPermissionTemplate,
  getPermissionList
} from 'src/api';
import classnames from 'classnames';
import { setRoleChange } from '../../accessRequestSlice';
import styles from '../index.module.scss';
import { Iconfont } from 'src/components';
import PermissionLevelModal from '../../components/PermissionLevelModal'
import { setStr } from 'src/util/connectionManage';
import { useTranslation } from 'react-i18next';
import { setMineApplyPageState, setMineApplyDetailParams } from 'src/pageTabs/access-request/accessRequestSlice'

const getRoleTypeValue = (data: any[], key: any) =>
  data?.filter((i: any) => i?.roleType == key)?.[0]?.roleName;

const getPermissionValue = (data: any[], key: any) =>
  data?.filter((i: any) => i?.id == key)?.[0]?.name;

interface ListProps {
  delScFn: (val: any) => void;
  refreshAppResource: () => void;
}

export const ResourceBidPage: FC<ListProps> = (props) => {
  const history = useHistory();
  const dispatch = useDispatch();
  const { delScFn, refreshAppResource } = props;
  const { requestListData } = useSelector((state) => state.accessRequest);
  const { t } = useTranslation();

  const scrollX = columnsDetail()?.length * 100;

  //保存草稿， 当作删除接口
  const { run: draftRun } = useRequest(saveDraftFat, {
    manual: true,
    onSuccess: () => {
      message.success(t('flow_deletion_success'));
      refreshAppResource();
    },
  });

  const newColumns = useMemo(() => {
    let newColumns = columnsDetail()?.map((item: any) => {
      if (item?.dataIndex === 'connectionName') {
        return {
          ...item,
          render: (_: any, record: any) => {
            return (
              <span>
                <Iconfont
                  type={`icon-connection-${record?.connection?.connectionType}`}
                  style={{ marginRight: 4 }}
                />
                {record?.connection?.connectionName}
              </span>
            );
          },
        };
      }
      if (item?.dataIndex === 'instanceName') {
        return {
          ...item,
          render: (_: any, record: any) => {
            const { connectionUrl, connectionPort, connectionMembers } = record?.connection?.userInputs || {};
            return connectionMembers?.length 
                  ? <Tooltip title={connectionMembers?.map((i: any)=> <div>{i?.connectionUrl + ':' + i?.connectionPort}</div>)}>
                    {
                    connectionMembers?.length > 1 ? 
                    `${connectionMembers?.[0]?.connectionUrl} :${connectionMembers?.[0]?.connectionPort} ...`:
                     `${connectionMembers?.[0]?.connectionUrl} : ${connectionMembers?.[0]?.connectionPort}`
                     }
                  </Tooltip>
                  : connectionUrl + ':' + connectionPort
          },
        };
      }
      return item;
    });
    newColumns = newColumns.concat([{
        title: t('flow_operation'),
        dataIndex: 'action',
        render: (val: any, record: any) => (
          <Popconfirm
          title={t('flow_confirm_deletion')}
          onConfirm={() => {
             //base-2.4.7  使用保存草稿接口直接删除
             const deleteItem = requestListData?.map((item) => {
              if (item?.id === record?.id) {
                return {
                  ...item,
                  businessDataListNoChild:[]
                };
              }
              return item;
             });
            draftRun({
              list: deleteItem,
            });
          }}
          okText={t('flow_ok')}
          cancelText={t('flow_cancel')}
        >
             <CloseCircleOutlined className={styles.icon}/>
          </Popconfirm>
        ),
      }]);
    return newColumns;
  }, [requestListData, t]);

  const handleRenderToSearch = () => {
    history.push('/mine_apply');
    dispatch(setMineApplyPageState('search'))
    dispatch(setMineApplyDetailParams({}))
  }

  return (
    <Card
      title={t('flow_request_resource')}
      className={styles.borderShow}
      extra={
        <Button type="primary">
          <span 
            onClick={handleRenderToSearch}
          >
            <PlusOutlined />
            &nbsp;&nbsp;{t('flow_add_resource')}
          </span>
        </Button>
      }
    >
      <Table
        rowKey="id"
        className={styles.tablePage}
        expandable={{
          expandedRowRender: (record) => {
            return (
              <ExpandedRowContent
                record={record}
                data={record?.businessDataListNoChild}
                delScFn={delScFn}
                refreshAppResource={refreshAppResource}
              />
            );
          },
        }}
        columns={newColumns}
        dataSource={requestListData}
        size="small"
        scroll={{ x: scrollX, y: 490 }}
      />
    </Card>
  );
};

export const ExpandedRowContent = ({
  record,
  data,
  delScFn,
  refreshAppResource,
}: {
  record: any;
  data: any;
  delScFn: (val: any) => void;
  refreshAppResource: () => void;
}) => {
  const {dataSourceType, nodeType: selectNodeType} = data?.[0] || {}
  const dispatch = useDispatch();
  const { t } = useTranslation();
  const { requestListData } = useSelector((state) => state.accessRequest);

  const [toolsModalVisible, setToolsModalVisible] = useState(false)
  const [editToolItem, setEditToolItem] = useState<any>({})
  const [templateOperationsSet, setTemplateOperationsSet] = useState<any[]>([])
  const [templateEnum, setTemplateEnum] = useState<any[]>([])
  const [permissionsOptsSet, setPermissionsOptsSet] = useState<any>();

	// 查询工具权限模板
	useEffect(()=>{
		if(dataSourceType && selectNodeType){
			queryToolsAuthTemplate(dataSourceType, selectNodeType)
		}
	},[dataSourceType, selectNodeType])
	const queryToolsAuthTemplate = (dataSourceType: string, nodeType: string) =>{
		getToolTemplateInfo(dataSourceType, nodeType).then((res: any)=>{
			setTemplateEnum(res)
		}).catch((err: any)=>{
			console.error('工具权限模板查询失败', err)
		})
	}

  const { data: userRoleData = [] } = useRequest(getUserRoleNocheck);
  // 可选权限模版
  const {
    data: permissionData = [],
    run: queryTemplate,
    loading,
  } = useRequest(getPermissionTemplateInfo, {
    manual: true,
    formatResult(data) {
      let newArr = data?.filter((i: any) => {
        if (
          i?.status !== 'DISABLE_TEMPLATE' &&
          i?.status !== 'DISABLE_TEMPLATE_AND_PERMISSION'
        ) {
          return i;
        }
      });
      return newArr;
    },
  });
  //保存草稿， 当作删除接口
  const { run: draftRun } = useRequest(saveDraftFat, {
    manual: true,
    onSuccess: () => {
      message.success(t('flow_deletion_success'));
      refreshAppResource();
    },
  });

  useEffect(() => {
    if (record?.connection) {
      queryTemplate(record?.connection?.connectionType);
    }
  }, [record]);

  // 查询不同数据源下对应的所有权限信息
  const { run: getPermissionTemplateEnum } = useRequest(
    getPermissionTemplate,
    { manual: true },
  )

  // 获取权限等级列表
  const { run: getPermissionListEnum } = useRequest(
    getPermissionList,
    { manual: true,
      onSuccess: (res: any) => {
        getPermissionTemplateEnum(dataSourceType).then((templateRes: any) => {
        let options: { [key: string]: any[] } = {};
        res?.forEach((item: any) => {
          if (item?.id) {
            let templateOperationsSetTmp = item?.templateOperationsSet;
            options[item?.id] = templateOperationsSetTmp?.flatMap((optionsItem: any) => {
              const matchedTemplate = templateRes?.find((templateItem: any) => templateItem?.objectType === optionsItem?.objectType);
              if (matchedTemplate) {
                return optionsItem?.operations?.map((operation: any) => {
                  const matchedOperation = matchedTemplate?.operations?.find((templateOperation: any) => templateOperation?.operation === operation);
                  if (matchedOperation) {
                    return `${operation}(${matchedOperation?.operationName})`;
                  }
                  return operation;
                });
              } else {
                return [];
              }
            });
          }
        });
        setPermissionsOptsSet(options)
        })
      }
    },
  )

  useEffect(()=>{
		if(dataSourceType){
			getPermissionListEnum(dataSourceType)
		}
	},[dataSourceType, getPermissionListEnum])

  const roleMenu = (record: any) => (
    <Menu
      className={classnames(styles.optionTxt, styles.menuWrap)}
      onClick={(info) =>
        dispatch(setRoleChange({ info, item: record, name: 'roleId' }))
      }
    >
      {userRoleData?.map((item: any) => (
        <Menu.Item key={item?.roleType}>{item?.roleName}</Menu.Item>
      ))}
    </Menu>
  );

  const permissionMenu = (record: any) => (
    <Menu
      className={classnames(styles.optionTxt, styles.menuWrap)}
      onClick={async(info) =>{
        dispatch(setRoleChange({ info, item: {...record,valid: true}, name: 'permissionType' }))
      }
      }
    >
      {permissionData?.map((item: any) => (
        <Menu.Item
          key={item?.id}
          disabled={[
            'DISABLE_TEMPLATE_AND_PERMISSION',
            'DISABLE_TEMPLATE',
          ].includes(item?.status) || [t('flow_no_permissions')].includes(item?.name)}
        >
          {item?.name}
        </Menu.Item>
      ))}
    </Menu>
  );

  // 编辑工具权限
  const handleEditToolPermission = (item: any) => {
    setEditToolItem(item)
    setTemplateOperationsSet(item?.templateOperationsSet)
    setToolsModalVisible(true)
  }

  // 工具权限修改
  const updateToolsAuth = (params: any) => {
    dispatch(setRoleChange({ info:{key: params}, item: editToolItem, name: 'templateOperationsSet' }))
  }

  const newExpandColumns = useMemo(() => {
    const newColumns = detailExpamdedRow()?.map((item: any) => {
      if (item?.dataIndex === 'nodeName') {
        return {
          ...item,
          render: (val: any, record: any) => (
            <div>
              <Iconfont
                type={`${
                  record?.nodeType === 'datasource'
                    ? `icon-connection-${record?.nodeName}`
                    : record?.nodeType === 'group'
                    ? 'icon-shujukuwenjianjia'
                    : record?.nodeType === 'connection'
                    ? `icon-${record?.dataSourceType}`
                    : `icon-${record?.nodeType}`
                } `}
              />
              <span>{record?.nodeName}</span>
            </div>
          ),
        };
      }
      if (item?.dataIndex === 'actions') {
        return {
          ...item,
          render: (val: any, childRecord: any) => (
            <Popconfirm
            title={t('flow_confirm_deletion')}
            onConfirm={() => {
               // delScFn(record?.flowUUID)
                // console.log('=====删除', record)
                // 如果这里使用调接口删除资源，会刷新整个表格重置原来选好的申请权限数据
                // 2.4.5版本暂定，可优化为权限申请和删除都调接口这样用户刷新再回来数据不会丢失
                // dispatch(setOnDeleteItem(record))
                //base-2.4.7  使用保存草稿接口直接删除
                const deleteItem = requestListData?.map((item) => {
                  if (item?.id === record?.id) {
                    return {
                      ...item,
                      businessDataListNoChild:
                        item?.businessDataListNoChild.filter(
                          (child: any) => child?.id !== childRecord?.id
                        ),
                    };
                  }
                  return item;
                });
                draftRun({
                  list: deleteItem,
                });
            }}
            >
              <CloseCircleOutlined className={styles.icon}  />
            </Popconfirm>
           
          ),
        };
      }
      // 权限申请
      if (item?.dataIndex === 'permissionTypeName') {
        return {
          ...item,
          render: (val: any, record: any) => {
            const permissionsOptsSetKeys = !!permissionsOptsSet ? Object.keys(permissionsOptsSet) : []
            const isShowPermissionTooltip: boolean = permissionsOptsSet && (permissionsOptsSetKeys.includes(record?.permissionType?.toString()))
            let str = ''
            if (isShowPermissionTooltip) {
              str = setStr(permissionsOptsSet[record?.permissionType?.toString()])
            }
            return (
              <Tooltip
                title={ isShowPermissionTooltip && (str || '-') }
                overlayClassName={ isShowPermissionTooltip && !!str ? styles.permissionTooltip : ''}
                overlayStyle={{ whiteSpace: 'pre-line' }}
              >
                <span>
                  <>
                    {
                      !record?.valid && 
                      <Tooltip title={t('flow_user_permission_level')} placement="leftTop">
                        <ExclamationCircleOutlined />
                      </Tooltip>
                    }
                    {getPermissionValue(permissionData, record?.permissionType)?.length > 10 ? `${getPermissionValue(permissionData, record?.permissionType)?.slice(0,10)}...`:getPermissionValue(permissionData, record?.permissionType)}
                  </>
                </span>
                <Dropdown overlay={() => permissionMenu(record)}>
                  <DownOutlined
                    className={classnames(styles.ml10, styles.options)}
                  />
                </Dropdown>
              </Tooltip>
            );
          },
        };
      }
      // 角色
      if (item?.dataIndex === 'roleName') {
        return {
          ...item,
          render: (val: any, record: any, index: number) => (
            <>
              <span>{getRoleTypeValue(userRoleData, record?.roleId)}</span>
              {/* {
								index === 0 && <Dropdown overlay={() => roleMenu(record)}>
									<DownOutlined
										className={classnames(styles.ml10, styles.options)}
									/>
								</Dropdown>
							} */}
						</>
					),
				}
			}

      // 工具权限申请
      if(item?.dataIndex === 'toolsAuth') {
        return {
          ...item,
          render: (val: any, record: any) => {
            let resultName: any = []
            record?.templateOperationsSet?.forEach((item: any) => {
              const matchedObejct = templateEnum?.find((res:any) => res?.objectType === item?.objectType);
              if (matchedObejct) {
                item?.operations?.forEach((val: any)=>{
                  const matchedItem = matchedObejct?.operations?.find((res:any) => res?.operation === val);
                  if(matchedItem?.operationName){
                    resultName.push(matchedItem?.operationName)
                  }
                })
              }
            });
					  const title = resultName?.join(',') || '-'
            return (
              (!record?.children) && 
              <Tooltip
                title={title}
              >
                <span
                  className={styles.options}
                  onClick={() =>
                    handleEditToolPermission(record)
                  }
                >
                  {t('flow_please_select')}
                  <DownOutlined className={classnames('ml4 options')} style={{fontSize: '14px'}} />
                </span>
              </Tooltip>
            )
          },
        }
      }

			return item
		})
		return newColumns
	}, [requestListData, permissionsOptsSet, permissionData, permissionMenu, userRoleData, templateEnum, t])

  return (
    <>
      <Table
        rowKey="id"
        loading={loading}
        columns={newExpandColumns}
        dataSource={data}
        pagination={false}
        size="middle"
      />
      {/* 工具权限 */}
      {toolsModalVisible && (
        <PermissionLevelModal
          visible={toolsModalVisible}
          optionType="editToolPermission"
          selectNodeType={editToolItem?.nodeType}
          dataSourceType={editToolItem?.dataSourceType}
          setModalVisible={setToolsModalVisible}
          templateOperationsSet={templateOperationsSet}
          callback={updateToolsAuth}
        />
      )}
    </>
  );
};
