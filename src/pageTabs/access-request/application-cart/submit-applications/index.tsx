import React, { FC, useEffect } from 'react'
import { But<PERSON>, Card, DatePicker, Form, Input, message ,Radio } from 'antd'
import classnames from 'classnames'
import { useForm } from 'antd/lib/form/Form'
import moment from 'moment'
import 'moment/locale/zh-cn'
import { useRequest, useSelector } from 'src/hook'
import { saveDraftFat, startFlowInstance } from 'src/api'
import { useHistory } from 'react-router-dom'
import styles from '../index.module.scss'
import { disabledDate, disabledTime } from 'src/util'
import { setMineApplyPageState, setMineApplyDetailParams } from 'src/pageTabs/access-request/accessRequestSlice'
import { useDispatch } from 'src/hook'
import { useTranslation } from 'react-i18next'
moment.locale('zh-cn')

const { RangePicker } = DatePicker;

const FormLayout = {
  labelCol: { span: 2},
  wrapperCol: { offset: 0, span: 8 },
}

interface ListProps {
  beginTime?: string
  endTime?: string
  remark?: string
  title?: string
}

export const SubmitApplicationsPage: FC<ListProps> = (props) => {
  const dispatch = useDispatch()
  const { t } = useTranslation()
  const { beginTime, endTime, remark, title } = props
  const [form] = useForm()
  const { requestListData } = useSelector((state) => state.accessRequest)

  const { run: draftRun } = useRequest(saveDraftFat, { manual: true })

  const handleRenderToApply = () => {
    dispatch(setMineApplyPageState('apply'))
    dispatch(setMineApplyDetailParams({}))
  }

  const { data: startFlow, run: runStartFlow } = useRequest(startFlowInstance, {
    manual: true,
    onSuccess(data) {
      message.success(t('flow_submission_success'))
      handleRenderToApply()
    }
  })

  useEffect(() => {
    if (beginTime && endTime) {
      form.setFields([
        { name:"timeType",value:'custom'},
        { name: "time", value: (beginTime && endTime) ? [ moment(beginTime), moment(endTime)] : []},
        { name: "title", value: title },
        { name: "remark", value: remark },
      ])
    } else {
      form.setFields([
        { name:"timeType",value:'forever'},
        { name: "title", value: title },
        { name: "remark", value: remark },
      ])
    }
 
  }, [beginTime, endTime, remark, title])

  const onSaveDraft = () => {
    form.validateFields().then((res) => {
      let parmas
      if (res.timeType === 'custom') {
        parmas = {
          title: res?.title,
          remark: res?.remark,
          beginTime: res?.time[0].format('YYYY-MM-DD HH:mm:ss'),
          endTime: res?.time[1].format('YYYY-MM-DD HH:mm:ss'),
          list: requestListData
        }
      } else {
        parmas = {
          title: res?.title,
          remark: res?.remark,
          list: requestListData
        }
      }
   
      draftRun(parmas).then(res => {
        message.success(t('flow_save_draft'))
      })
    })
  }

  const onSubmit = () => {
    form.validateFields().then().then((values: any) => {
   

      let params : ListProps
      if (values.timeType === 'custom') {
        const  beginTime =  values?.time[0].format('YYYY-MM-DD HH:mm:ss');
        const  endTime = values?.time[1].format('YYYY-MM-DD HH:mm:ss');
   
        const e = moment(endTime).valueOf();
        
        if (e < moment().valueOf()) {
          return message.error(t('flow_resel_time_end_later'))
        }
        params = {
          title: values?.title,
          remark: values?.remark,
          beginTime,
          endTime
        }
      } else {
        params = {
          title: values?.title,
          remark: values?.remark,
        }
      }
    
           
      draftRun({ ...params, list: requestListData }).then(res => {
        runStartFlow({
          ...params, priGranType: 'FAT',
          flowType: 'dataManipulation'
        })
      })
    })
  }

  return (
    <Card
      title={t('flow_submit_request')}
      className={classnames(styles.borderShow, styles.mt27, styles.mb27, styles.detailCard)}
      actions={[
        <div className={styles.subBtn}>
          <Button
            type='primary'
            className={classnames(styles.ml20, styles.mr20)}
            onClick={() => onSubmit()}
            disabled={!requestListData?.length}
          >
            {t('flow_submit_request')}
          </Button>
          <Button
            onClick={onSaveDraft}
            disabled={!requestListData?.length}
          >
            {t('flow_save_draft')}
          </Button>
        </div>
      ]}
    >
      <Form form={form} {...FormLayout} component={false}>
        <Form.Item label={t('flow_effective_time')} name="timeType">
          <Radio.Group>
            <Radio value="forever">{t('flow_permanent')}</Radio>
            <Radio value="custom">{t('flow_custom_period')}</Radio>
          </Radio.Group>
        </Form.Item>
        <Form.Item
          noStyle
          shouldUpdate={(prevValues, currentValues) =>
            prevValues?.timeType !== currentValues?.timeType
          }
        >
          {({ getFieldValue }) =>
            getFieldValue("timeType") === "custom" ? (
              <Form.Item
                label={t('flow_time_period')}
                name="time"
                rules={[
                  { required: true },
                  {
                    validator(_rule, value, cb) {
                      const e = moment(value?.[1]).valueOf();

                      if (e < moment().valueOf()) {
                        cb(t('flow_resel_time_end_later'));
                      }
                      cb();
                    },
                  },
                ]}
              >
                <RangePicker
                  disabledDate={disabledDate}
                  disabledTime={disabledTime}
                  ranges={{
                    近7日: [moment(), moment().add(7, "d")],
                    近15日: [moment(), moment().add(15, "d")],
                    近30日: [moment(), moment().add(30, "d")],
                  }}
                  showTime={{
                    hideDisabledOptions: true,
                    defaultValue: [
                      moment("00:00:00", "HH:mm:ss"),
                      moment("11:59:59", "HH:mm:ss"),
                    ],
                  }}
                  format="YYYY-MM-DD HH:mm:ss"
                />
              </Form.Item>
            ) : null
          }
        </Form.Item>
        <Form.Item
          label={t('flow_title')}
          name="title"
          rules={[{ required: true }, {max: 20, min: 2}]}
        >
          <Input allowClear />
        </Form.Item>
        <Form.Item
          label={t('flow_remark')}
          name="remark"
          // rules={[{ required: true }]}
        >
          <Input.TextArea allowClear maxLength={100}/>
        </Form.Item>
      </Form>
    </Card>
  )
}
