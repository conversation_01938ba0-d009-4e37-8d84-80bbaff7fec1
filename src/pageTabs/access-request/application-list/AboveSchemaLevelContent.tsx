import React, { useCallback, useEffect } from "react";
import * as _ from 'lodash';
import {
  getAutomaticObjectPermission,
  getCartPermissionsPanelUpObject,
  IObjectPermissionRes,
} from 'src/api';
import { useRequest, useSelector, useDispatch } from 'src/hook';
import { updateConnectionObjectPermissions } from './applicationListSlice'
import AboveSchemaLevelComponent from '../components/AboveSchemaLevelComponent';

export const AboveSchemaLevelContent = ({
  isGroupTab,
  selectTreeItem,
  permissionCollectionVOS,
  viewAppliedDetail = false,
  isInShoppingCart = true,
  updatePermissionCollectionVOS,
}: {
  isGroupTab?: boolean;
  isEdit?: boolean;
  selectTreeItem?: any;

  isInShoppingCart?: boolean;
  viewAppliedDetail?: boolean;
  permissionCollectionVOS?: any;
  updatePermissionCollectionVOS?: (params: any) => void

}) => {

  const dispatch = useDispatch();
  const { connectionObjectPermissions } = useSelector(state => state.applicationList)

  const { data: list, loading, run: runGetAutomaticObjectPermission } = useRequest(getCartPermissionsPanelUpObject, {
    manual: true
  })

  const {
    loading: groupTabPermissionLoading,
    data: groupTabPermissionList,
    run: runGetGroupTabPermission
  } = useRequest<IObjectPermissionRes>(
    getAutomaticObjectPermission, {
    manual: true
  })

  useEffect(() => {
    if(_.isEmpty(selectTreeItem)) return

    if (isGroupTab) {
      queryPermissionsGroup();
    } else {
      queryPermissionsInstance();
    }
  }, [selectTreeItem?.key, isGroupTab]);

  const queryPermissionsInstance = useCallback(() => {
    const { connection, dataSourceType, nodePathWithType } = selectTreeItem || {};
    const { connectionType } = connection || {};

    if (nodePathWithType && (connectionType || dataSourceType)) {
      let params: any = {
        nodePath: nodePathWithType,
        dataSourceType: dataSourceType || connectionType
      };
      runGetAutomaticObjectPermission(params);
    }
  }, [runGetAutomaticObjectPermission, selectTreeItem])

  
  const queryPermissionsGroup = () => {
    const { connection, dataSourceType, nodePathWithType, roleId } = selectTreeItem || {};
    const { connectionType } = connection || {};
    if (selectTreeItem.newNodeType === "datasource") {
      runGetGroupTabPermission({
        roleId,
        dataSourceType: selectTreeItem.id,
        flowMainUUID: selectTreeItem.flowMainUUID,
        isFlow: true
      });
    }else if (selectTreeItem.newNodeType === "group") {
      runGetGroupTabPermission({
        roleId,
        groupId: selectTreeItem.id,
        dataSourceType: dataSourceType || connectionType,
        flowMainUUID: selectTreeItem.flowMainUUID,
        isFlow: true
      });
    }else if (nodePathWithType && (connectionType || dataSourceType)) {
      runGetGroupTabPermission({
        roleId,
        nodePath: nodePathWithType,
        dataSourceType: dataSourceType || connectionType,
        flowMainUUID: selectTreeItem.flowMainUUID,
        isFlow: true
      })
    }
  }

  return (
    <AboveSchemaLevelComponent
      selectTreeItem={selectTreeItem}
      list={selectTreeItem ?  (isGroupTab? groupTabPermissionList : list): []}
      isEdit={!isGroupTab}
      viewAppliedDetail={viewAppliedDetail}
      loading={isGroupTab?groupTabPermissionLoading : loading}
      permissionCollectionVOS={permissionCollectionVOS}
      updatePermissionCollectionVOS={updatePermissionCollectionVOS}
      isInShoppingCart={isInShoppingCart}
      connectionObjectPermissions={connectionObjectPermissions}
      onUpdateConnectionObjectPermissions={(params: any) =>dispatch(updateConnectionObjectPermissions(params))}
    />
  )
}