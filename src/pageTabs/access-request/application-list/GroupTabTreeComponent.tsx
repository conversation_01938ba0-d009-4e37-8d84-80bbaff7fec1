import React, { useCallback, useEffect, useMemo, useState } from 'react'
import { Modal, Tree } from 'antd'
import { Iconfont } from 'src/components'
import styles from './index.module.scss'
import classnames from 'classnames'
import * as _ from 'lodash';
import { useSelector, useDispatch } from 'src/hook';
import {
  setLoadedKeys,
  getTreeNodeChildrenGroupTab,
  refreshOnRootGroupTab,
  setSelectedNode,
  getPermissionTreeNodeChildren,
} from './applicationListSlice';
import { ConnectionFailWarnImg } from 'src/components/ConnectionFailWarnImg';
import { CloseOutlined, ExclamationCircleOutlined } from '@ant-design/icons'
import { nodeTypeIsPermission } from 'src/util/validateStr'
import { useTranslation } from 'react-i18next'

interface IProps {
  searchValue: string;
  onDeleteNodeItem?: (params: {collectionIds: (number | undefined)[]}) => any;
  [p: string]: any
}


const GroupTabTreeComponent = (props: IProps) => {
  const {
    searchValue,
    onDeleteNodeItem,
  } = props
  
  const { loadedKeys, newTreeData, selectedTreeNode} = useSelector(state => state.applicationList);
  const allFailedCountConnectionIds = useSelector((state) => state.login.allFailedCountConnectionIds);

  const dispatch = useDispatch();
  const { t } = useTranslation();
  const [isBatchOperation, setIsBatchOperation] = useState(false);
  const [checkedKeys, setCheckedKeys] = useState<any[]>([])
  const [checkedNodesParams, setCheckedNodesParams] = useState<number[]>([])

  const [expandedKeys, setExpandedKeys] = useState<any[]>([]);
 
  useEffect(() => {
    return () => {
      dispatch(setLoadedKeys([]));
    }
  },[dispatch])

  const requestTreeData = () => {
    dispatch(refreshOnRootGroupTab())
  }
 
  useEffect(() => {
    requestTreeData()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  useEffect(() => {
    if (nodeTypeIsPermission(selectedTreeNode?.nodePathWithType)) {
      handleLoadData(selectedTreeNode);
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  },[selectedTreeNode])

  const handleSelect = (_: any[], info: any) => {
    if (!info.selected) {
      return;
    }
    dispatch(setSelectedNode(info?.node as any))
  };

  const handleExpand = (newExpandedKeys: React.Key[]) => {
    // 收起时，过滤掉该组层级下所有被展开的节点
    setExpandedKeys(newExpandedKeys)
  }

  // 异步逐级加载数据 (连接及以下层级加载树内容)
  const handleLoadData = useCallback( async(node: any) => {
    const {
      id,
      roleId,
      nodePathWithType,
      nodeType,
      dataSourceType,
      connectionId,
      nodeName,
      nodePath,
      key,
    } = node
    let params: any = {
      connectionId: connectionId || id,
      connectionType: dataSourceType,
      nodeType,
      nodeName,
      nodePath,
      nodePathWithType,
      roleId,
      key,
      isFlow: true,
    }
    const isPermission = nodeTypeIsPermission(params?.nodePathWithType);
    const func = isPermission ? getPermissionTreeNodeChildren : getTreeNodeChildrenGroupTab;
    await dispatch(func({ 
      params,
      callback: (list) => {
        // 权限集层级: 展开节点，并选中第一个子节点
        if (isPermission && list?.length > 0) {
          dispatch(setSelectedNode(list?.[0]));
          setExpandedKeys((item) => {
            if (!item?.includes(key) && key) {
              return [...item, key]
            }
            return item
          })
        }
      }
    }));
  // eslint-disable-next-line react-hooks/exhaustive-deps
  },[dispatch])

  // 权限集删除 确认弹窗
  const showDelPermissionConfirm = (roleId?: number) => {
    Modal.confirm({
      icon: <ExclamationCircleOutlined />,
      content: t('common.text.delete.tip'),
      onOk() {
        onDeleteNodeItem?.({collectionIds: [roleId]})
      },
    })
  }

  // 生成tree搜索标题
  const generatorSearchTitle = (
    node: any
  ) => {
    const {
      title,
      nodeType,
      id,
      isFolder = false,
      dataSourceType
    } = node;


    return (
      <>
        <Iconfont
          className={classnames(styles.mr4, styles.color008dff)}
          type=
          {`${isFolder
              ? `icon-quanxianji`
              : nodeType?.endsWith("GROUP")
                ? "icon-shujukuwenjianjia"
                : nodeType === "connection"
                  ? `icon-${dataSourceType}`
                  : `icon-${nodeType}`
            } `}
        />
        <span className={styles.titleTxtWrap}>
          <span className={styles.titleTxt}>{title}</span>
          {
            nodeType === "connection" && allFailedCountConnectionIds?.includes(Number(id)) && 
            <ConnectionFailWarnImg />
          }
        </span>
      </>
    );
  };

  // 渲染tree title完整内容
  const treeTitleRender = (node: any) => {
    const result = (
      <div className={styles.treeTitleItem}>
        {generatorSearchTitle(node)}
        {
          !node?.nodeType &&
          <CloseOutlined
            className={styles.deleteNodeIcon}
            onClick={() => showDelPermissionConfirm(node?.roleId)}
          />
        }
      </div>
    );
    return result
  }

  // 递归遍历搜索
  const recursionSearch = (treeData : any[], searchValue: string) => {
    const newTreeData: any[] = [];
    treeData?.map((item: any) => {
      if (item?.title?.toLowerCase().includes(searchValue?.toLowerCase())) {
        newTreeData.push(item)
      }
      else if (item?.children && item?.children?.length > 0) {
        const children = recursionSearch(item?.children, searchValue)
        if (children?.length > 0) {
          newTreeData.push({ ...item, children })
        }
      }
    })
    return newTreeData
  }

  const filteredTreeData = useMemo(() => {
    if (!searchValue) return newTreeData;
    let cloneNewTreeData = _.cloneDeep(newTreeData) || [];
    return recursionSearch(cloneNewTreeData || [], searchValue)
  // eslint-disable-next-line react-hooks/exhaustive-deps
  },[searchValue, JSON.stringify(newTreeData)])

  const handleOpenBatchAction = () => {
    setIsBatchOperation(true)
  }
  
  const handleCancelBatchAction = () => {
    setIsBatchOperation(false)
    setCheckedKeys([])
    setCheckedNodesParams([])
  }

  const handleBatchDelete = () => {
    if(!checkedNodesParams?.length){
      return
    }
    Modal.confirm({
      icon: <ExclamationCircleOutlined />,
      content: t('common.text.delete.tip'),
      onOk() {
        onDeleteNodeItem?.({collectionIds: checkedNodesParams})?.then(() => {
          // 重置批量操作状态
          handleCancelBatchAction()
        })
      },
    })
    }

  const handleBatchDeleteAll = () => {
    
    Modal.confirm({
      icon: <ExclamationCircleOutlined />,
      content: t('flow_confirm_clear'),
      onOk() {
        const connectionLevel = filteredTreeData?.filter((i: any)=>!i?.nodeType)
        const params = connectionLevel?.map((item: any)=>{
          return item?.roleId
        })
        onDeleteNodeItem?.({collectionIds: params || []})?.then(() => {
          // 重置批量操作状态
          handleCancelBatchAction()
        })
      }
    })
  }

  const handleCheck = (checkedKeys: any, e: any) => {
    const checkedNodesParams = e.checkedNodes?.filter((node: any) => !node?.nodeType)?.map((node: any)=>{
      return node?.roleId
    })
    setCheckedNodesParams(checkedNodesParams)
    setCheckedKeys(checkedKeys)
  }

  if(!filteredTreeData?.length){
    return <div className='color667084 tc'>{t('flow_no_data')}</div>
  }

  return (
    <div className={`${styles.treeWrapper} guide-apply-search-sdt`}>
      <div className={styles.batchAction}>
        <div>
          <span onClick={() => handleOpenBatchAction()}>
            {!isBatchOperation && <Iconfont type='icon-batch-operation' />}
            <span className={classnames({ 'nonmodifiableColor': isBatchOperation })}>&nbsp;{t('flow_bulk_operation')}</span>
          </span>
          {
            !isBatchOperation &&
            <span className='ml10' onClick={() => handleBatchDeleteAll()}>{t('flow_clear')}</span>
          }
        </div>
        {
          isBatchOperation &&
          <div>
            <span
              className={
                classnames(
                  "mr10 padding4", 
                  {'nonmodifiableColor': !checkedNodesParams?.length},
                )
              }
              style={{cursor: !checkedNodesParams?.length ? 'not-allowed': "pointer"}}
              onClick={() => handleBatchDelete()}
            >
              {t('flow_delete')}
            </span>
            <span
              className="nonmodifiableColor"
              onClick={() => handleCancelBatchAction()}
            >
              {t('flow_cancel')}
            </span>
          </div>
        }
      </div>
      <Tree
        // isBatchOperation改变不会触发整个Tree重新渲染,而改变时需要重置勾选状态,所以需要手动设置key的形式来强制渲染 
        key={isBatchOperation ? 'batch' : 'normal'} 
        className={styles.treeContent}
        titleRender={treeTitleRender} // 自定义渲染节点 --- 节点前图标的配置
        treeData={filteredTreeData} // treeNodes 数据
        onSelect={handleSelect} // 点击树节点触发 ---  控制右侧组件的切换
        selectedKeys={selectedTreeNode ? [selectedTreeNode?.key] : []}
        onExpand={handleExpand} // 展开/收起节点时触发 --- 控制节点下一级的显示
        expandedKeys={expandedKeys} // （受控）展开指定的树节点 
        loadData={handleLoadData} // 异步加载数据 --- handleExpand 后执行
        loadedKeys={loadedKeys}
        onLoad={(keys) => dispatch(setLoadedKeys(keys))}
        checkable={isBatchOperation}
        checkedKeys={checkedKeys}
        onCheck={handleCheck}
      />
    </div>
  )
}

export default React.forwardRef(GroupTabTreeComponent);