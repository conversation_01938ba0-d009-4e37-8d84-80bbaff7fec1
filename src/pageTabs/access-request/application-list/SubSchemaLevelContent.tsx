import React, { useEffect } from "react";
import {
  getCartPermissionsPanelObject,
  getPermissionsPanelObject,

} from 'src/api';
import { useRequest, useSelector, useDispatch } from 'src/hook';
import { updatePermissionCollections } from './applicationListSlice'
import SubchemaLevelComponent from '../components/SubSchemaLevelComponent';

export const SubSchemaLevelContent = ({
  isGroupTab,
  isEdit = true,
  selectTreeItem,
  viewAppliedDetail = false,
  isInShoppingCart = true,
  filter,
  permissionCollectionVOS,
  handleUpdateBelowSchemaParams,
}: {
  isGroupTab?: boolean;
  isEdit?: boolean;
  selectTreeItem?: any;
  needRefresh?: boolean;
  isInShoppingCart?: boolean;
  viewAppliedDetail?: boolean;
  permissionCollectionVOS?: any;
  filter?: any;
  handleUpdateBelowSchemaParams?: (params: any) => void

}) => {

  const dispatch = useDispatch();
  const { permissionCollections } = useSelector(state => state.applicationList)

  const { data: objectResult, loading, run: queryPermissionsObject } = useRequest(
    (params) => {
      if (isGroupTab) {
        return getPermissionsPanelObject({
          ...params,
          isFlow: true,
          roleId: selectTreeItem?.roleId
        })
      }
      return getCartPermissionsPanelObject(params)
    },
    { manual: true }
  )

  useEffect(() => {

    if (!selectTreeItem) {
      return;
    }
    const {
      nodeName,
      nodePathWithType,
      nodeType,
      dataSourceType,
      sdt,
    } = selectTreeItem;
    const connectionType = dataSourceType || sdt?.connectionType
    const params = {
      connectionId: null,
      connectionType,
      nodeType,
      nodeName,
      nodePathWithType,
      filter
    };
    queryPermissionsObject(params)
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectTreeItem?.key]);


  return (
    <SubchemaLevelComponent
      loading={loading}
      objectResult={objectResult}
      selectTreeItem={selectTreeItem}
      isInShoppingCart={isInShoppingCart}
      viewAppliedDetail={viewAppliedDetail}
      canEdit={isEdit}
      permissionCollections={permissionCollections}
      onUpdateConnectionObjectPermissions={(params: any) => dispatch(updatePermissionCollections(params))}
      onUpdataParmas={handleUpdateBelowSchemaParams}
    />
  )
}