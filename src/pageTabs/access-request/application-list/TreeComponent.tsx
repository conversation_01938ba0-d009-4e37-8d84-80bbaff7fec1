import React, { useCallback, useEffect, useMemo, useState } from 'react'
import { ExclamationCircleOutlined, CloseOutlined } from '@ant-design/icons';
import * as _ from 'lodash';
import classnames from 'classnames'
import { Tree, Modal, Tooltip, Spin } from 'antd'
import { Iconfont } from 'src/components'
import { useSelector, useDispatch } from 'src/hook';
import { getExpandKeysAboutContainSearchValue } from 'src/util';
import {
  setLoadedTreeNodeMap,
  setLoadedKeys,
  getTreeNodeChildren,
  setSelectedNode,
  setExpandedKeys
} from './applicationListSlice'
import styles from './index.module.scss'
import { IDeleteCartNodeParams } from 'src/api/flow'
import { useTranslation } from 'react-i18next';
interface IProps {
  searchValue?: string;
  onDeleteNodeItem?: (params: IDeleteCartNodeParams[]) => any;
}

const TreeComponent = (props: IProps) => {
  const {
    searchValue,
    onDeleteNodeItem
  } = props
  const { loadedKeys, fetchConnectionLoading, newTreeData, expandedKeys, selectedTreeNode } = useSelector(state => state.applicationList);
  const dispatch = useDispatch();
  const { t } = useTranslation();
  const [isBatchOperation, setIsBatchOperation] = useState(false)
  const [checkedKeys, setCheckedKeys] = useState<any[]>([])
  const [checkedNodesParams, setCheckedNodesParams] = useState<IDeleteCartNodeParams[]>([])

  useEffect(() => {
    return () => {
      dispatch(setLoadedKeys([]));
      dispatch(setLoadedTreeNodeMap({}))
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  const handleSelect = (item: any[], info: any) => {
    if (!info.selected) {
      return;
    }
    dispatch(setSelectedNode(info?.node as any))
  }

  // 异步逐级加载数据 (连接及以下层级加载树内容)
  const handleLoadData = useCallback(async (node: any) => {
    if (node?.newNodeType === 'datasource') {
      return
    }
    const { id, roleId, nodePathWithType, nodeType, dataSourceType, connectionId, nodeName,
      nodePath, key
    } = node
    const params = {
      connectionId: connectionId || id,
      connectionType: dataSourceType,
      nodeType,
      nodeName,
      nodePath,
      nodePathWithType,
      roleId,
      key
    }
    await dispatch(getTreeNodeChildren(params))
  }, [dispatch])

  // 权限集删除 确认弹窗
  const showDelPermissionConfirm = (params: IDeleteCartNodeParams[], t: any) => {
    Modal.confirm({
      icon: <ExclamationCircleOutlined />,
      content: t('flow_confirm_deletion'),
      onOk() {
        onDeleteNodeItem?.(params)
      },
    })
  }

  const generatorSearchTitle = (node: any) => {
    const { nodeName, title, nodeType, connection = {}, testModel, dataSourceType } = node || {}
    return (
      <>
        <Iconfont
          className={classnames(styles.mr4, styles.color008dff, {
            [styles.colorf00]: testModel === 1 && nodeType === "connection",
            [styles.colorgreen]: !!testModel && testModel !== 1 && nodeType === "connection",
          })}
          type={`${nodeType === "datasource"
            ? `icon-connection-${nodeName}`
            : nodeType === "group"
              ? "icon-shujukuwenjianjia"
              : nodeType === "connection"
                ? `icon-${dataSourceType}`
                : `icon-${nodeType}`
            } `}
        />
        <span className={styles.titleTxt}>
          <Tooltip title={nodeType === "connection" && <>
            {connection?.ip + ':' + connection?.port}
          </>}>
            {title}
          </Tooltip>
          {["datasource", "group"].includes(nodeType) && `(${node?.children?.length || 0})`}
        </span>
      </>
    );
  };

  // 渲染tree title完整内容
  const treeTitleRender = (node: any) => {
    const { nodeType, nodePath, nodePathWithType } = node
    const result = (
      <div className={styles.treeTitleItem}>
        {generatorSearchTitle(node)}
        <CloseOutlined
          className={styles.deleteNodeIcon}
          onClick={() => showDelPermissionConfirm([{ nodePathWithType, nodePath, nodeType }], t)}
        />
      </div>
    );
    return result
  }

  const matchKeyword = (target = '', substring = '') => {
    if (!target) return false
    return target.toLowerCase().indexOf(substring.toLowerCase()) > -1
  }

  // /* 实现全局节点搜索 */
  const filterNodesNotMatch = useCallback(
    (nodes: any[]): any[] =>
      nodes.filter((node) => {
        //后端搜索 筛选信息只有一条， 前端筛选模糊匹配
        const keywordHit = matchKeyword(`${node?.title}${node?.connection?.ip}`, searchValue)
        if (!keywordHit && node.children) {
          node.children = filterNodesNotMatch(node.children)
        }
        return keywordHit || node.children?.length
      }),
    [searchValue],
  )

  const filteredTreeDataOnLocal = useMemo(() => {
    return searchValue ? filterNodesNotMatch(_.cloneDeep(newTreeData)) : newTreeData
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [JSON.stringify(newTreeData), searchValue])

  useEffect(() => {
    // 只获取包含search 的父节点
    if (searchValue) {
      let keys = getExpandKeysAboutContainSearchValue(filteredTreeDataOnLocal, searchValue)
      dispatch(setExpandedKeys(keys))
    } else {
      dispatch(setExpandedKeys([newTreeData?.[0]?.key]))
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [searchValue])

  const handleOpenBatchAction = () => {
    setIsBatchOperation(true)
  }

  const handleCancelBatchAction = () => {
    setIsBatchOperation(false)
    setCheckedKeys([])
    setCheckedNodesParams([])
  }

  const handleBatchDelete = () => {
    if(!checkedNodesParams?.length){
      return
    }
    Modal.confirm({
      icon: <ExclamationCircleOutlined />,
      content: t('flow_confirm_deletion'),
      onOk() {
        onDeleteNodeItem?.(checkedNodesParams)?.then(() => {
          // 重置批量操作状态
          handleCancelBatchAction()
        })
      },
    })
  }

  const handleBatchDeleteAll = () => {
    Modal.confirm({
      icon: <ExclamationCircleOutlined />,
      content: t('flow_confirm_clear'),
      onOk() {
        const connectionLevel = filteredTreeDataOnLocal?.filter((i: any)=>i?.nodeType==='connection')
        const params = connectionLevel?.map((item: any)=>{
          const { nodeType, nodePath, nodePathWithType } = item || {}
          return {
            nodeType, 
            nodePath, 
            nodePathWithType
          }
        })
        onDeleteNodeItem?.(params)?.then(() => {
          // 重置批量操作状态
          handleCancelBatchAction()
        })
      }
    })
  }

  const handleCheck = (checkedKeys: any, e: any) => {
    const checkedNodesParams = e.checkedNodes?.map((node: any)=>{
      const { nodeType, nodePath, nodePathWithType } = node || {}
      return {
        nodeType, nodePath, nodePathWithType
      }
    })
    setCheckedNodesParams(checkedNodesParams)
    setCheckedKeys(checkedKeys)
  }

  if(!filteredTreeDataOnLocal?.length){
    return <div className='color667084 tc'>{t('flow_no_data')}</div>
  }

  return (
    <>
      <div className={styles.batchAction}>
        <div>
          <span onClick={() => handleOpenBatchAction()}>
            {!isBatchOperation && <Iconfont type='icon-batch-operation' />}
            <span className={classnames({ 'nonmodifiableColor': isBatchOperation })}>&nbsp;{t('flow_bulk_operation')}</span>
          </span>
          {
            !isBatchOperation &&
            <span className='ml10' onClick={() => handleBatchDeleteAll()}>{t('flow_clear')}</span>
          }
        </div>
        {
          isBatchOperation &&
          <div>
            <span
              className={
                classnames(
                  "mr10 padding4", 
                  {'nonmodifiableColor': !checkedNodesParams?.length},
                )
              }
              style={{cursor: !checkedNodesParams?.length ? 'not-allowed': "pointer"}}
              onClick={() => handleBatchDelete()}
            >
              {t('flow_delete')}
            </span>
            <span
              className="nonmodifiableColor"
              onClick={() => handleCancelBatchAction()}
            >
              {t('flow_cancel')}
            </span>
          </div>
        }
      </div>
      <Spin spinning={fetchConnectionLoading}>
        <Tree
          // isBatchOperation改变不会触发整个Tree重新渲染,而改变时需要重置勾选状态,所以需要手动设置key的形式来强制渲染 
          key={isBatchOperation ? 'batch1' : 'normal1'} 
          className={styles.treeWrap}
          titleRender={treeTitleRender}
          treeData={filteredTreeDataOnLocal}
          onSelect={handleSelect}
          selectedKeys={selectedTreeNode ? [selectedTreeNode?.key] : []}
          onExpand={(expandedKeys) => dispatch(setExpandedKeys(expandedKeys))}
          expandedKeys={expandedKeys}
          loadData={handleLoadData}
          loadedKeys={loadedKeys || []}
          onLoad={(keys) => dispatch(setLoadedKeys(keys))}
          checkable={isBatchOperation}
          checkedKeys={checkedKeys}
          onCheck={handleCheck}
        />
      </Spin>
    </>
  )
}
export default TreeComponent