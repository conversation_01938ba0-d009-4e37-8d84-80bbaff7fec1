import { createSlice, PayloadAction , createAsyncThunk} from '@reduxjs/toolkit'
import { AppThunk } from 'src/store'
import { batch } from 'react-redux'
import * as _ from 'lodash'
import {
  getAllCollectionForFlowDesc,
  getFlowPermissionCollectionNodes,
  getPermissionList,
  getSelectedPermissionCollectionNode,
  getSelectedPermissionCollectionNodes
} from 'src/api'
import { getTargetNode, specialFormatTree } from './utils';
import {
  getCartNode, 
  getCartConnection,
  getAccessPermissionConnections
} from 'src/api'
import { specialFormatTree as specialFormatTreeGroupTab } from 'src/pageTabs/automaticAuthorizationManagePage/utils'

interface AuthorizationAuthorizationManageState {
  permissionList: any[];
  authorizeUserList: any[];
  expandedKeys: any[];
  //权限集tree
  loadedTreeNodeMap:  Record<string, any[]>,
  loadedKeys: string[];
  selectedTreeNode: any;
  newTreeData: any[];
  treeNodeChildrenFetching: any;
  permissionCollections: any;
  connectionObjectPermissions: any,
  resourceTotal: number;
  fetchConnectionLoading: boolean;
}

const initialState: AuthorizationAuthorizationManageState = {
  permissionList: [],
  authorizeUserList: [],
  expandedKeys: [],
  loadedTreeNodeMap:{},
  loadedKeys: [],
  newTreeData: [],
  selectedTreeNode: {},
  treeNodeChildrenFetching: {},
  permissionCollections: {},
  connectionObjectPermissions: {},
  resourceTotal: 0,
  fetchConnectionLoading: true //默认进入实例视图查询状态
}


export const getTreeNodeChildren = createAsyncThunk<
  any[],
  any,
  { state: any }
>('applicationList/getTreeNodeChildren', async (params, { dispatch, getState }) => {
  
  const {
    sdt: { treeNodeChildrenMap, treeNodeChildrenFetching },
    sdtPermission: { status = false }
  } = getState()

  const list = await dispatch(fetchTreeNodeChildren({
    ...params,
  })).unwrap()
 
  return list
})

// 更新树节点 children
export const fetchTreeNodeChildren = createAsyncThunk<
  any,
  any,
  { state: any }
>(
  'applicationList/fetchTreeNodeChildren',
  async (params) => {
    let children = [];
    if (params?.nodeType) {
      children = await getCartNode(params);
    }
    return children
  },
  {
    condition({ nodePath }, { getState }) {
      const {
        sdt: { treeNodeChildrenFetching },
      } = getState()
      const fetching = treeNodeChildrenFetching[nodePath]
      // 应用当前已经在获取数据，则不再重复请求
      return !fetching
    },
  },
)

export const getTreeNodeChildrenGroupTab = createAsyncThunk<
  any[],
  { params: any, callback?: (list: any) => void },
  { state: any }
>('applicationList/getTreeNodeChildrenGroupTab', async ({params, callback}, { dispatch, getState }) => {
  const list = await dispatch(fetchTreeNodeChildrenGroupTab({
    ...params,
  })).unwrap()
  if (callback) {
    callback(list);  // 执行回调
  }
  return list
})

// 更新树节点 children
export const fetchTreeNodeChildrenGroupTab = createAsyncThunk<
  any,
  any,
  { state: any }
>(
  'applicationList/fetchTreeNodeChildrenGroupTab',
  async (params) => {
    let children = [];
    if (params?.nodeType) {
      children = await getSelectedPermissionCollectionNode({
        ...params,
        notFilterFlowStdNode: true
      });
    }else {
      const res = await getSelectedPermissionCollectionNodes(params?.roleId);
      children = res?.nodeList || []
    }
    return children
  },
  {
    condition({ nodePath }, { getState }) {
      const {
        sdt: { treeNodeChildrenFetching },
      } = getState()
      const fetching = treeNodeChildrenFetching[nodePath]
      // 应用当前已经在获取数据，则不再重复请求
      return !fetching
    },
  },
)

export const getPermissionTreeNodeChildren = createAsyncThunk<
  any[],
  { params: any, callback?: (list: any) => void },
  { state: any }
>('applicationList/getPermissionTreeNodeChildren', async ({params, callback}, { dispatch, getState }) => {
  const list = await dispatch(fetchPermissionTreeNodeChildren({
    ...params,
  })).unwrap()
  if (callback) {
    callback(list);  // 执行回调
  }
  return list
})

// 更新树节点 children
export const fetchPermissionTreeNodeChildren = createAsyncThunk<
  any,
  any,
  { state: any }
>(
  'applicationList/fetchPermissionTreeNodeChildren',
  async (params) => {
    let children = [];
    const res = await getFlowPermissionCollectionNodes({roleId: params?.roleId});
    children = res?.nodeList || []
    return children
  },
  {
    condition({ nodePath }, { getState }) {
      const {
        sdt: { treeNodeChildrenFetching },
      } = getState()
      const fetching = treeNodeChildrenFetching[nodePath]
      // 应用当前已经在获取数据，则不再重复请求
      return !fetching
    },
  },
)

export const fetchConnections = (): AppThunk => async (dispatch, getState) => {
  const {
    sdt: { expandedKeys },
    sdtPermission: { status }
  } = getState()
  dispatch(setFetchConnectionLoading(true))
  getCartConnection()
    .then((list) => {
  
    const filteredConnections = list?.nodeList?.filter(((node: any)=> node?.nodeType === 'connection'));
    const result = filteredConnections?.map((curr: any) => ({
        ...curr,
        key: curr?.nodePathWithType,
        title: curr?.nodeName,
        dataSourceType: curr?.connection?.connectionType,
        isLeaf: !curr?.hasChild,
    }))

      dispatch(setNewTreeData(result))
      dispatch(setSelectedNode(result?.[0]))
      dispatch(setResourceTotal(list?.total || 0))
     
      batch(() => {
        dispatch(setLoadedKeys([]))
        // dispatch(setExpandedKeys([...expandedKeys]))
      })
    })
    .finally(() => {
      dispatch(setFetchConnectionLoading(false))
    })
}
export const refreshOnRoot = (): AppThunk => (dispatch) => {
 
  dispatch(fetchConnections())
}
export interface IGroupTabNodeParams {
  pageNo: number;
  pageSize: number;
  roleName: string;
}
export const fetchConnectionsGroupTab = (): AppThunk => async (dispatch, getState) => {
  getAllCollectionForFlowDesc()
    .then((list) => {
      const result = list?.list?.length? specialFormatTreeGroupTab(list.list, true) : []
      dispatch(setNewTreeData(result))
      dispatch(setSelectedNode(result?.[0]))
      dispatch(setResourceTotal(list?.total || 0))
      batch(() => {
        dispatch(setLoadedKeys([]))
      })
    })
}

export const refreshOnRootGroupTab = (): AppThunk => (dispatch) => {
  dispatch(fetchConnectionsGroupTab())
}

export const applicationListSlice = createSlice({
  name: "applicationList",
  initialState,
  reducers: {
    setPermissionList: (state, action: PayloadAction<any[]>) => {
      state.permissionList = action.payload;
    },
    setAuthorizeUserList: (state, action: PayloadAction<any[]>) => {
      state.authorizeUserList = action.payload;
    },
    setResourceTotal:  (state, action: PayloadAction<any>) => {
      state.resourceTotal = action.payload;
    },
    setSelectedNode: (state, action: PayloadAction<any>) => {
      state.selectedTreeNode = action.payload
    },
    setExpandedKeys: (state, action: PayloadAction<any[]>) => {
      state.expandedKeys = action.payload;
    },
    setLoadedTreeNodeMap: (state, action: PayloadAction<any>) => {
      state.loadedTreeNodeMap = action.payload;
    },
    setLoadedKeys: (state, action: PayloadAction<any[]>) => {
      state.loadedKeys = action.payload;
    },
    setNewTreeData: (state, action: PayloadAction<any[]>) => {
      state.newTreeData = action.payload;
    },
    setTreeDataNodeChildren: (
      state,
      action: PayloadAction<{
        nodePath: string
        children: any[]
      }>,
    ) => {
      const { nodePath, children } = action.payload
      const parent = getTargetNode(nodePath, state.newTreeData)
      if (parent) {
        parent.children = children
      }
    },
    //存储当前选中权限内容
    updatePermissionCollections: (state, action: PayloadAction<any>) => {
      const newValue = action.payload
      state.permissionCollections = {...state.permissionCollections, ...newValue};
    },
    resetPermissionCollections: (state) => {
      state.permissionCollections = {}
    },
    updateConnectionObjectPermissions: (state, action: PayloadAction<any>) => {
      const settings = action.payload
      state.connectionObjectPermissions = settings;
    },
    resetConnectionObjectPermissions: (state) => {
      state.connectionObjectPermissions = {}
    },
    setFetchConnectionLoading: (state, action: PayloadAction<boolean>) => {
      state.fetchConnectionLoading = action.payload;
    },
  },
  extraReducers: (builder) => {
    // fetchTreeNodeChildren
    builder.addCase(fetchTreeNodeChildren.pending, (state, action) => {
      const { key } = action.meta.arg
      const { treeNodeChildrenFetching } = state
      treeNodeChildrenFetching[key] = true
    })
    builder.addCase(fetchTreeNodeChildren.fulfilled, (state, action) => {

      const list = action.payload
      const { key } = action.meta.arg
    
      const target: any = getTargetNode(key, state.newTreeData)
      
      if (target) {
        
        if (list?.length) target.children = specialFormatTree(list,false,target?.roleId)
        else target.children = []
      }
      state.treeNodeChildrenFetching[key] = false
    })
    builder.addCase(fetchTreeNodeChildren.rejected, (state, action) => {
      const { nodePath } = action.meta.arg
      state.loadedKeys = state.loadedKeys.filter((key) => key !== nodePath)
      state.treeNodeChildrenFetching[nodePath] = false
    })

    // fetchTreeNodeChildrenGroupTab
    builder.addCase(fetchTreeNodeChildrenGroupTab.pending, (state, action) => {
      const { key } = action.meta.arg
      const { treeNodeChildrenFetching } = state
      treeNodeChildrenFetching[key] = true
    })
    builder.addCase(fetchTreeNodeChildrenGroupTab.fulfilled, (state, action) => {
      const list = action.payload
      const { key } = action.meta.arg
      const target: any = getTargetNode(key, state.newTreeData)
      if (target) {
        if (list?.length) target.children = specialFormatTreeGroupTab(list,false,target?.roleId)
        else target.children = []
      }
      state.treeNodeChildrenFetching[key] = false
    })
    builder.addCase(fetchTreeNodeChildrenGroupTab.rejected, (state, action) => {
      const { nodePath } = action.meta.arg
      state.loadedKeys = state.loadedKeys.filter((key) => key !== nodePath)
      state.treeNodeChildrenFetching[nodePath] = false
    })

    // fetchPermissionTreeNodeChildren
    builder.addCase(fetchPermissionTreeNodeChildren.pending, (state, action) => {
      const { key } = action.meta.arg
      const { treeNodeChildrenFetching } = state
      treeNodeChildrenFetching[key] = true
    })
    builder.addCase(fetchPermissionTreeNodeChildren.fulfilled, (state, action) => {
      const list = action.payload
      const { key } = action.meta.arg
      const target: any = getTargetNode(key, state.newTreeData)
      if (target) {
        if (list?.length) target.children = specialFormatTreeGroupTab(list,false,target?.roleId)
        else target.children = []
      }
      state.treeNodeChildrenFetching[key] = false
    })
    builder.addCase(fetchPermissionTreeNodeChildren.rejected, (state, action) => {
      const { nodePath } = action.meta.arg
      state.loadedKeys = state.loadedKeys.filter((key) => key !== nodePath)
      state.treeNodeChildrenFetching[nodePath] = false
    })
  }
});

export const applicationListReducer = applicationListSlice.reducer

export const { setPermissionList, setAuthorizeUserList,
  setLoadedTreeNodeMap,
  setLoadedKeys,
  setNewTreeData,
  setSelectedNode,
  setExpandedKeys,
  setResourceTotal,
  updatePermissionCollections, 
  resetPermissionCollections,
  updateConnectionObjectPermissions,
  resetConnectionObjectPermissions,
  setFetchConnectionLoading
 } = applicationListSlice.actions;

export const queryPermissionList =
  (dataSourceType: string): AppThunk =>
    (dispatch) => {
    getPermissionList(dataSourceType).then((res) => {
      dispatch(setPermissionList(res))
    })
  }


