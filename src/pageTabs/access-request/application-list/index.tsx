/**
 * 我的申请 - 申请清单
*/
import React, { useCallback, useEffect, useMemo, useState } from 'react'
import { SearchOutlined } from '@ant-design/icons'
import { useHistory } from 'react-router-dom'
import { Input, message, Space, Tabs } from 'antd'
import * as _ from 'lodash';
import classnames from 'classnames'
import { ResizableBox, ResizableProps } from 'react-resizable'
import {
  saveCartDrafte,
  deleteCartNode,
  startFlowAccessPermission,
  deleteCartNodeGroupTab,
  getAllConnectionInfo,
  getCartConnection
} from 'src/api'
import { useDispatch, useRequest, useSelector } from 'src/hook'
import { SimpleBreadcrumbs, Iconfont } from "src/components"
import {
  refreshOnRoot,
  setNewTreeData,
  fetchConnections,
  setLoadedKeys,
  setExpandedKeys,
  resetConnectionObjectPermissions,
  resetPermissionCollections,
  setSelectedNode,
  refreshOnRootGroupTab,
  setFetchConnectionLoading,
} from './applicationListSlice'
import TreeComponent from './TreeComponent'
import { SubmitApplicationModal } from './modal';
import { AboveSchemaLevelContent } from './AboveSchemaLevelContent';
import { SubSchemaLevelContent } from './SubSchemaLevelContent';
import styles from './index.module.scss'
import { useTranslation } from 'react-i18next';
import { MENU_FLOW } from 'src/constants';
import GroupTabTreeComponent from './GroupTabTreeComponent';
import { setMineApplyPageState, setMineApplyDetailParams,  } from 'src/pageTabs/access-request/accessRequestSlice'

const ResizableBoxProps: ResizableProps = {
  axis: 'x',
  width: 320,
  height: 0,
  minConstraints: [260, 0],
  maxConstraints: [620, 0],
}

const ApplicationListPage = () => {
  const dispatch = useDispatch()
  const { t } = useTranslation();

  const { newTreeData, selectedTreeNode, fetchConnectionLoading } = useSelector(state => state.applicationList);

  const [searchValue, setSearchValue] = useState<string>("");
  const [rightWrapWidth, setRightWrapWidth] = useState<string>("");
  const [permissionCollectionVOS, setPermissionCollectionVOS] = useState<any[]>([])
  const [submitModalState, setSubmitModalState] = useState(false);

  const handleRenderToApply = () => {
    dispatch(setMineApplyPageState('apply'))
    dispatch(setMineApplyDetailParams({}))
  }

  const breadcrumbData = [
    { title: t(MENU_FLOW) },
    {
      title: <span className='breadcrumbLink' onClick={handleRenderToApply}>{t('flow_my_request')}</span>,
    },
    {
      separator: '/',
      title: t('flow_request_list'),
    },
  ];

  const [tabsActiveKey, setTabsActiveKey] = useState<string>('instanceTab');
  //是否触发切换  切换tab后 不再控制无数据时默认进去分组视图
  const [isUserToggled, setIsUserToggled] = useState<boolean>(false);
  const [allConnections, setAllConnections] = useState<any[]>([]);
  const [isGetIntanceTabConnections, setIsGetIntanceTabConnections] = useState<boolean>(false);
   
  //删除节点
  const { run: runDeleteCartNode } = useRequest(
    (params) => {
      if (tabsActiveKey === 'groupTab') {
        return deleteCartNodeGroupTab(params)
      }
      return deleteCartNode(params)
    }
    , {
    manual: true,
    onSuccess: () => {
      message.success(t('flow_deletion_success'));
      setPermissionCollectionVOS([]);
      dispatch(resetConnectionObjectPermissions());
      dispatch(resetPermissionCollections());
      requestTreeData();
      dispatch(setLoadedKeys([]));
      dispatch(setExpandedKeys([]));
    }
  });

  //提交后 先保存草稿 再正式提交
  const {run: runSaveCartDraft, loading: draftLoading } = useRequest(saveCartDrafte, { manual: true});
  //正式提交
  const { run: runStartFlow, loading: submitBtnLoading } = useRequest(startFlowAccessPermission, {
    manual: true,
    onSuccess(data) {
      message.success(t('flow_submission_success'));
      setSubmitModalState(false);
      dispatch(resetConnectionObjectPermissions());
      dispatch(resetPermissionCollections());
      handleRenderToApply()
    }
  })

  // 获取权限集下所有连接
  const { run: runGetGroupConnectionInfo } = useRequest(getAllConnectionInfo, {
    manual: true
  })

  //获取实例视图数据
  const { run: getAllInstance } = useRequest(getCartConnection, {
    manual: true,
    formatResult: (res) => {
      const filteredConnections = res?.nodeList?.filter(((node: any)=> node?.nodeType === 'connection'));
      const result = filteredConnections?.map((curr: any) => ({
           connectionId: Number(curr?.id),
           nodeName: curr?.nodeName,
          dataSourceType: curr?.connection?.connectionType,
      }))
      return result;
    }
  })

  const getIntanceTabConnections = useCallback(() => {
    const res = newTreeData?.map((i: any) => ({nodeName: i?.nodeName, connectionId: Number(i?.id), dataSourceType: i?.dataSourceType}))
    if (res?.length > 0) {
      setIsGetIntanceTabConnections(true)
    }
  }, [JSON.stringify(newTreeData)])

  useEffect(() => {
    if (!isGetIntanceTabConnections && tabsActiveKey === 'instanceTab') {
      getIntanceTabConnections()
    }
  }, [tabsActiveKey, isGetIntanceTabConnections])

  const requestTreeData = () => {
    if (tabsActiveKey === 'groupTab'){
      dispatch(refreshOnRootGroupTab())
    } else {
      dispatch(refreshOnRoot())
    }
  }

  // treeData
  useEffect(() => {
    const debouncedFetchConnections = _.debounce(() => {
      if (tabsActiveKey === 'groupTab') {
        setIsGetIntanceTabConnections(true);
        return;
      }
      dispatch(fetchConnections());
    }, 200); // 延迟 500 毫秒
  
    debouncedFetchConnections();
  
    return () => {
      debouncedFetchConnections.cancel(); // 清除未执行的 debounce 调用
    };
  }, [dispatch, tabsActiveKey])

  useEffect(() => {

    //默认实例视图数据为空 且未手动切换tab 自动跳转到分组视图
    if (tabsActiveKey === 'instanceTab' && !isUserToggled && !newTreeData?.length && !fetchConnectionLoading) {
      setTabsActiveKey('groupTab');
    }
   
  },[isUserToggled, tabsActiveKey, newTreeData?.length, fetchConnectionLoading])

  useEffect(() => {
    return () => {
      dispatch(setNewTreeData([]));
      dispatch(resetConnectionObjectPermissions());
      dispatch(resetPermissionCollections());
      setTabsActiveKey('instanceTab');
      setIsGetIntanceTabConnections(false);
      setIsUserToggled(false);
      dispatch(setFetchConnectionLoading(true))
    }
  }, [dispatch])

  useEffect(() => {
    handleLeftWrapResize();
  }, []);

  const tabsHandleOnChange = (activeKey: string) => {
    setIsUserToggled(true);
    setTabsActiveKey(activeKey);
  }

  const handleSearch = (e: any) => {
    const value = e.target.value;
    setSearchValue(value?.trim());
  };

  const handleUpdateParams = (params: any[], compare?: boolean) => {
    setPermissionCollectionVOS(params)
  }
  const handleUpdateBelowSchemaParams = (params: any[], compare?: boolean) => {
    setPermissionCollectionVOS((s: any[]) => {
      if (compare) {
        params.forEach((res: any) => {
          if (s.map(i => i?.permissionCollectionObjectVO?.nodePathWithType)?.includes(res?.permissionCollectionObjectVO?.nodePathWithType)) {
            s = s.map((i: any) => {
              if (i?.permissionCollectionObjectVO?.nodePathWithType === res?.permissionCollectionObjectVO?.nodePathWithType) {
                i = {
                  permissionCollectionObjectVO: {
                    ...i.permissionCollectionObjectVO,
                    operations: res?.permissionCollectionObjectVO?.operations || []
                  }

                }
              }
              return { ...i }
            })
          } else {
            s = [...s, res]
          }
        })
        return [...s]
      }
      return [...s, ...params]
    })
  }

  const handleLeftWrapResize = () => {
    // @ts-ignore
    const lWidth = document.getElementsByClassName("react-resizable")[0]?.style.width ||
      "320px";
    const width = `calc(100vw - 30px - ${lWidth} )`;
    setRightWrapWidth(width);
  };

  const hasEmptyObjSetting = useMemo(() => {
      //判断是否所有的 permissionSdtUserAddVo.templateId 和userToolVo?.templateOperationsSet[0].operations  permissionCollectionObjectVO.operations

    return permissionCollectionVOS?.every(i => {
      const {permissionSdtUserAddVo, userToolVo, permissionCollectionObjectVO} = i;
      if (permissionSdtUserAddVo?.templateId) {
        return false;
      }
      if (permissionCollectionObjectVO?.operations?.length) {
        return false
      }

      if (userToolVo) {
        let  allOperations: string[] = [];
        userToolVo?.templateOperationsSet?.map((o: any) => allOperations = allOperations.concat(o?.operations));
        if (allOperations?.length) return false;
      }
      return true
  })
  }, [permissionCollectionVOS])

  const getAllConnections = useCallback(async () => {
    // 获取分组视图连接
    const res = await runGetGroupConnectionInfo();
    const ins = await getAllInstance();

    const groupTabConnectionsRes = res.map((i: any) => ({
      nodeName: i?.connectionName,
      connectionId: Number(i?.connectionId),
      dataSourceType: i?.dataSourceName
    }));

    const mergedConnections = new Map(
      [...ins, ...groupTabConnectionsRes].map(item => [item.connectionId, item])
    );
    // 去重处理，保证连接id唯一，避免重复添加相同连接
    const filteredArray = Array.from(mergedConnections.values());

    setAllConnections(filteredArray);

  }, [])

  const renderResizeHandle = (
    <div className={styles.resizeHandle}>
      <Iconfont type="icon-handle-8"></Iconfont>
    </div>
  );

  const renderRightContent = useMemo(() => {
    const isInstanceTab = tabsActiveKey === 'instanceTab';
    return (
      <>
        {
          (['datasource', 'connection', 'database', 'schema', 'oracleUser'].includes(selectedTreeNode?.nodeType) || !selectedTreeNode) &&
          <AboveSchemaLevelContent
            isGroupTab={!isInstanceTab}
            isEdit={isInstanceTab}
            selectTreeItem={selectedTreeNode}
            permissionCollectionVOS={permissionCollectionVOS}
            updatePermissionCollectionVOS={handleUpdateParams}
          />
        }
        {/* 对象级权限设置 */}
        {
          selectedTreeNode?.nodeType?.toUpperCase()?.endsWith("GROUP") &&
          <SubSchemaLevelContent
            isGroupTab={!isInstanceTab}
            isEdit={isInstanceTab}
            selectTreeItem={selectedTreeNode}
            handleUpdateBelowSchemaParams={handleUpdateBelowSchemaParams}
          />
        }
      </>
    )
  }, [permissionCollectionVOS, selectedTreeNode, tabsActiveKey])

  const leftSideContent = useMemo(() => {
    return  tabsActiveKey === "groupTab" ?
    <GroupTabTreeComponent
      searchValue={searchValue}
      onDeleteNodeItem={(params: any) => runDeleteCartNode(params)}
    /> 
    :
    <TreeComponent
      searchValue={searchValue}
      onDeleteNodeItem={(params: any) => runDeleteCartNode(params)}
    />
  },[tabsActiveKey, JSON.stringify(newTreeData)])


  return (
    <div>
      <div className={styles.groupAuthorizationWrap}>
        {/* 数据库管理/自动授权   顶部模块 */}
        <div style={{ padding: "0 10px" }}>
          <SimpleBreadcrumbs items={breadcrumbData} />
        </div>
        <div className={styles.content}>
          <ResizableBox
            className={styles.resizableBox}
            handle={renderResizeHandle}
            onResize={handleLeftWrapResize}
            {...ResizableBoxProps}
          >
            <div className={styles.leftWrap}>

              {/* 搜索框 */}
              <Input
                className={classnames(styles.searchBtn, styles.mb10)}
                prefix={<SearchOutlined />}
                placeholder={t('flow_enter_connection_ip')}
                allowClear
                onChange={handleSearch}
                value={searchValue}
              />
              <Tabs
                defaultActiveKey="instanceTab"
                size="small"
                activeKey={tabsActiveKey}
                onChange={tabsHandleOnChange}
              >
                <Tabs.TabPane tab={t('db.connection.sdt.instance')} key="instanceTab" />
                <Tabs.TabPane tab={t('db.connection.sdt.group')} key="groupTab" />
              </Tabs>

              {/* 树形目录 */}
              {
               leftSideContent
              }
            </div>
          </ResizableBox>
          <div className={styles.rightWrap} style={{ width: rightWrapWidth }}>
            <div className={styles.operationContent}>
              <div className={styles.contentDetail}>
                {renderRightContent}
              </div>
              <div className={styles.addRequestFooter}>
                <Space>
                  <span
                    className={classnames(styles.addRequestBtn, {
                      [styles.hightlightBtn]: newTreeData?.length,
                      [styles.disabledBtn]: hasEmptyObjSetting && permissionCollectionVOS?.length
                    })}
                    onClick={async () => {
                      // 合并实例、分组视图的连接
                      await getAllConnections();
                      
                      if (!newTreeData?.length || (hasEmptyObjSetting && permissionCollectionVOS?.length)) return;

                      // base2414: 从 权限等级必选，改为 权限等级、工具权限 必选一个
                      const tipItem = permissionCollectionVOS?.find(item =>
                        _.isEmpty(item?.userToolVo) &&
                        _.isEmpty(item?.permissionSdtUserAddVo) &&
                        Object.keys(item)?.length === 0);
                      
                      if (!_.isEmpty(tipItem)) {
                        return message.warning(` ${t('flow_please_select')} ${selectedTreeNode?.nodeName} ${t('flow_permissions_lower')}`)
                      }                    
                      setSubmitModalState(true)
                    }}
                  >
                    {t('flow_submit_request')}
                  </span>
                </Space>
              </div>
            </div>
          </div>
        </div>
      </div>
      {
        submitModalState &&
        <SubmitApplicationModal
          submitBtnLoading={draftLoading || submitBtnLoading}
          connections={allConnections}
          onCancel={() => setSubmitModalState(false)}
          onOk={(params: any) => {

            runSaveCartDraft({
              ...params,
              ...(permissionCollectionVOS?.length ? { permissionCollectionVOS } : {})
            }).then(() => {
              runStartFlow({ ...params, priGranType: "FAT", flowType: "dataManipulation" })
            })
          }}
        />
      }
    </div>
  );
}


export default ApplicationListPage
