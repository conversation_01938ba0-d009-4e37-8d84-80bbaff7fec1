import React, { FC, useEffect } from 'react'
import { Modal, Form, Input, message } from 'antd'
import { useForm } from 'antd/lib/form/Form'
import moment from 'moment'
import 'moment/locale/zh-cn'
import { FormTailLayoutTwo } from 'src/constants';
import { FormItemEffectiveTime, FormItemMultiConectionApprover } from 'src/pageTabs/flowPages/flowFormItems'
import { useTranslation } from 'react-i18next'

moment.locale('zh-cn')

interface ListProps {
  submitBtnLoading: boolean;
  connections: any
  onOk: (values: any) => void;
  onCancel: () => void;
}

export const SubmitApplicationModal: FC<ListProps> = (props) => {

  const { submitBtnLoading, connections, onCancel, onOk } = props
  const [form] = useForm()
  const { t } = useTranslation()

  useEffect(() => {
    
    form.setFieldsValue({
      connAndAssigneeMapList: connections
    })
   
  }, [JSON.stringify(connections)])
  
  const onSubmit = () => {
    form.validateFields().then().then((values: any) => {


      let params: any = {
        title: values?.title,
        remark: values?.remark,
        ...(values?.connAndAssigneeMapList ?
          { connAndAssigneeMapList: values?.connAndAssigneeMapList?.map((i: any) => ({ connectionId: i?.connectionId, userIds: i?.userIds })) }
          : {}
        )
      }

      if (values?.timeType !== 'forever') {
        const beginTime = values?.time[0].format('YYYY-MM-DD HH:mm:ss');
        const endTime = values?.time[1].format('YYYY-MM-DD HH:mm:ss');

        const e = moment(endTime).valueOf();

        if (e < moment().valueOf()) {
          return message.error(t('flow_resel_time_end_later'))
        }
        params = {
          ...params,
          beginTime,
          endTime,
        }
      }

      onOk({ ...params })
    })
  }

  return (
    <Modal title={t('flow_submit_request')} visible
      onOk={() => onSubmit()}
      onCancel={onCancel}
      okButtonProps={{
        loading: submitBtnLoading
      }}
    >
      <Form form={form} {...FormTailLayoutTwo} >
        <FormItemEffectiveTime
          form={form}
          effectiveTimeRequired={true}
          effectiveTimeKey="timeType"
          timeKey="time"
        />
        <Form.Item
          label={t('flow_title')}
          name="title"
          rules={[{ required: true }, { max: 20, min: 2 }]}
        >
          <Input allowClear />
        </Form.Item>
        <Form.Item
          label={t('flow_remark')}
          name="remark"
        >
          <Input.TextArea allowClear maxLength={100} />
        </Form.Item>
        <FormItemMultiConectionApprover
          flowType='FAT'
          isRender={true}
          isMultiConnection={true}
          multiConnectionIds={connections?.map((i: any) => i?.connectionId)}
        />
      </Form>
    </Modal>
  )
}
