import React, { <PERSON> } from "react";
import { Card, Descriptions } from "antd";
import classnames from "classnames";
import { Iconfont, renderDatabaseElementIcon } from 'src/components';
import {
  HIGH_RISK_BASIC_INFO,
  HIGH_RISK_BASIC_FIELD,
  BASIC_ODD_FIELDS,
  HIGH_RISK_ODD_FIELDS,
  priGranTypeMap,
  DESENSITIZED_RESOURCE_ODD_FIELDS,
  PriGranType,
  EXPORT_TASK_ODD_FIELDS,
  IMPORT_TASK_ODD_FIELDS,
  IMPORT_TASK_BASIC_INFO
} from '../../constants';
import styles from "../index.module.scss";
import { DownloadOutlined } from "@ant-design/icons";
import { useTranslation } from "react-i18next";

interface ListProps {
  beginTime: string;
  endTime: string;
  remark: string;
  title: string;
  priGranType:  PriGranType   
}

export const ApplicationsInfoPag: FC<ListProps> = (props:any) => {
  const { beginTime, endTime, priGranType, applyId } = props;
  
  const { t } = useTranslation();
  let descriptionFields = BASIC_ODD_FIELDS;
  let descriptionFieldsInfo = HIGH_RISK_BASIC_INFO;

  if (priGranType === 'highRisk') {
    descriptionFields = HIGH_RISK_ODD_FIELDS;
  } else if (priGranType === 'exportTask') {
    descriptionFields = EXPORT_TASK_ODD_FIELDS;
  } else if (priGranType === 'desensitizedResource') {
    descriptionFields = DESENSITIZED_RESOURCE_ODD_FIELDS;
  } else if (priGranType === 'importTask') {
    descriptionFields = IMPORT_TASK_ODD_FIELDS;
    descriptionFieldsInfo = IMPORT_TASK_BASIC_INFO;
  }
  if (priGranType === 'exportTask' && !Object.keys(props).includes("exportNumLimit")) {
    descriptionFields = descriptionFields.filter((str: string) => str !== "exportNumLimit")
  }

  const renderHighRiskItemValue = (field: HIGH_RISK_BASIC_FIELD) => { 
    if (field === 'connectionName') {
      return <><Iconfont type={`icon-${props?.dataSourceType}`} style={{fontSize: '16px', marginRight: "6px" }}/>{props?.[field]}</>
    }else if (field === 'nodePathWithTypeList') {
      return props?.nodePathWithTypeList?.map((path: string) => renderDatabaseElementIcon({nodePathWithType: path,dataSourceType: props?.dataSourceType})) 
    }else if (field === 'time') {
      if (!beginTime && !endTime) return t('flow_permanent')
      return  `${beginTime} ${t('flow_to')} ${endTime}`
    }else if (field === 'flowType') {
      return t(priGranTypeMap[props?.[field]])
    }else if (field === 'exportNumLimit') {
      return <>{[props?.[field]]} {t('common.lines')}</>
    }else if (field === 'importFileSize') {
      const size = props?.[field]
      if (!size) {
        return "-"
      } else {
        if (Number(size) < 1024) {
          return <>{size} {t('flow_bytes')}</>
        } else {
          return <>{Math.floor(size / 1024)} KB</>
        }
      }
    }else if (field === 'fileName') {
      const fileName = props?.[field]
      if (!fileName) {
        return "-"
      } else {
        return  (
          <>
          {fileName}
          <DownloadOutlined
            onClick={()=>window.open(`/export/importer/download/dataFileByFlow/${applyId}`)}
            style={{marginLeft: 3, color: '#3262ff'}}
          />
          </>
        )
      }
    }
    return props?.[field];
  }

  return (
    <Card
      title={t('flow_application_info')}
      className={classnames(styles.borderShow, 'mb20')}
    >
        <Descriptions column={1} className={styles.oddDescription}>
          {descriptionFields?.map((key: any) => (
            <Descriptions.Item label={t(descriptionFieldsInfo[key])} key={key}>
                {renderHighRiskItemValue(key)}
              </Descriptions.Item>
          ))}
        </Descriptions>
    </Card>
  );
};
