import React, { FC, useState } from 'react'
import { Card, Descriptions, Select, Modal, Input, Space, message } from 'antd'
import classnames from 'classnames';
import { useRequest, useDispatch } from 'src/hook';
import { approveApplication, queryPointApprover } from 'src/api';
import { LinkButton } from 'src/components';
import ModalDataChangeSelectNewApprover from 'src/pageTabs/flowPages/flowDetails/ModalDataChangeSelectNewApprover';
import { applyStatusMap } from '../../constants'
import { useTranslation } from 'react-i18next';
import { setMineApprovePageState, setMineApproveDetailParams } from 'src/pageTabs/access-request/accessRequestSlice'
import styles from '../index.module.scss'

interface ListProps {
  remarkData: []
  cardSty?: any
  isOnlyReadMenu?: boolean;
  record?: any;
  onRefresh?: () => void;
  flowStatus?: any;
  moduleType?: 'myApproval'
}
const opeEum: any = {
  pending: 'stayApprove',
  pass: 'yetApprove',
  power: 'power',
  finish: 'finish',
  withdraw: 'withdraw',
  refuse: 'yetApprove',
}

export const ApprovalRemarksPag: FC<ListProps> = (props) => {
  const dispatch = useDispatch();
  const { t } = useTranslation()
  const { remarkData, cardSty, isOnlyReadMenu, record, onRefresh, flowStatus, moduleType = null} = props;

  const [visible_setNewApprover, setVisible_setNewApprover] = useState(false); // 新审批人 modal visible
  const [currentTransferApprover, setCurrentTransferApprover] = useState<Array<string>>([]); // 新审批人 modal visible
  // 同意审批
  const { run: runApprove } = useRequest( approveApplication,
    {
      manual: true,
      onSuccess: () => {
        message.success(t("data_chg:msg.operation_success"));
        //跳转我的审批
        dispatch(setMineApprovePageState('')) 
        dispatch(setMineApproveDetailParams({}))
      },
    }
  );

  // 驳回|拒绝
  const confirm = (applyId: string, flowTaskId: any) => {
    function isEmpty(closeFn: any, val?: string) {
      let approveParams = {
        flowId: applyId,
        taskId: flowTaskId || '',
        approvedFlag: false,
        approvedComment: val,
        approvedTime: '',
      };

      //@ts-ignore
      runApprove(approveParams).then(() => {
        closeFn();
      })
    }

    const textOnchange = (val: string) => {
      update({
        onOk: (closeFn) => isEmpty(closeFn, val),
      });
    };

    const { update } = Modal.confirm({
      title: t("data_chg:md_title.remarks"),
      content: (
        <Input.TextArea
          rows={5}
          onChange={(e) => textOnchange(e.target.value)}
        />
      ),
      okText: t("data_chg:btn.confirm"),
      cancelText: t("data_chg:btn.cancel"),
      onOk: (closeFn) => isEmpty(closeFn),
    });
  };

  // 同意
  const agreeBtn = async(record: any) => {
    let approvers = []
    try {
      approvers = record?.flowInstanceId && await queryPointApprover(record.flowInstanceId)
    } catch (error) {

    }

    let agreeRemark: string = '';
    let designatedApproveList: string = '';
    function isEmpty(closeFn: () => void) {
      let approveParams = {
        flowId: record?.applyId,
        taskId: flowStatus?.flowTaskId || '',
        approvedFlag: true,
        approvedComment: agreeRemark,
        approvedTime: '',
        ...(designatedApproveList ? { designatedApproveList: [designatedApproveList] } : [])
      };

      //@ts-ignore
      runApprove(approveParams).then(() => {
        closeFn();
      }); 
    }

    Modal.confirm({
      title: t("data_chg:md_title.remarks"),
      content: (
        <>
        <Input.TextArea
          rows={5}
          onChange={(e) => {agreeRemark = e.target.value}}
        />
        {
          flowStatus?.nextNodeIsAssigneeGroup &&
          <div style={{ marginLeft: -37, marginTop: 10 }}>
            <span>{t("data_chg:span.spec_approver")}</span>
            <Select
              allowClear
              style={{ width: 320, marginTop: 10, marginLeft: 32 }}
              options={approvers?.map((a: any) => ({ label: `${a.userName}(${a.userId})`, value: a.userId }))}
              placeholder={t("data_chg:sele_ph.spec_approver")}
              onChange={(value: string) =>{designatedApproveList = value}}
            />
          </div>
        }
        </>
      ),
      okText: t("data_chg:btn.confirm"),
      cancelText: t("data_chg:btn.cancel"),
      onOk: (closeFn) => isEmpty(closeFn),
    });
  };

  // 转审
  const turretBtn = (record: any) => {
    setVisible_setNewApprover(true);
    setCurrentTransferApprover([record.flowTaskId]); //应该使用当前userId
  };
  const renderTabAction = (record: any) => {

    const judgeKey = opeEum[flowStatus?.approvedResult];

    if (judgeKey === 'stayApprove') {
      return (
        <Space>
          {t("data_chg:span.operation")}
          <LinkButton
            type='link'
            onClick={() => agreeBtn(record)}
            className={styles.padding0}
          >
            {t("data_chg:btn.agree")}
          </LinkButton>
          <LinkButton
            type='link'
            className={styles.padding0}
            onClick={() =>
              confirm(record?.applyId as string, record?.flowTaskId)
            }
          >
            {t("data_chg:btn.reject")}
          </LinkButton>
          <LinkButton
            type='link'
            className={styles.padding0}
            onClick={() => turretBtn(record)}
          >
            {t("data_chg:btn.transfer_review")}
          </LinkButton>
        </Space>
      );
    }
  };

  // 流程-我的审批
  const handleToApprove = () => {
    dispatch(setMineApprovePageState(''))
    dispatch(setMineApproveDetailParams({}))
  }

  return (
    <>
      <Card
        title={t("data_chg:card_title.approval_info")}
        className={classnames(styles.borderShow, 'mb20')}
        style={cardSty}
        // 解决当审批流程为审批人组的时候，高度显示问题
        bodyStyle={cardSty?.height ? {
          overflow: "auto",
          padding: "8px 24px",
          maxHeight: '163px'
        } : undefined}
        extra={!!isOnlyReadMenu && moduleType === 'myApproval' && flowStatus?.canOperation && renderTabAction(record)}
      >
        {
         remarkData?.length ? remarkData?.map((item: any, index: number) => {
            return <Descriptions key={index}>
              <Descriptions.Item label={item?.approvedUser} span={1}>{t(applyStatusMap[item?.approvedResult])}</Descriptions.Item>
              <Descriptions.Item label={t("data_chg:desc.remarks")} span={1}>{item?.approvedComment ? item?.approvedComment : '-'}</Descriptions.Item>
            </Descriptions>
          }) : ''
        }
      </Card>
      <ModalDataChangeSelectNewApprover
        cleanParentComponentData={() => {
          setCurrentTransferApprover([]);
          handleToApprove()
        }}
        flowId={flowStatus?.applyId}
        userTasks={currentTransferApprover}
        visible_setNewApprover={visible_setNewApprover}
        setVisible_setNewApprover={setVisible_setNewApprover}
      />
    </>
  )
}
