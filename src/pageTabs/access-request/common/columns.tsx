import React from 'react'
import { Tooltip } from 'antd'
import dayjs from 'dayjs'
import classNames from 'classnames'
import { ColumnsType } from 'antd/lib/table'
import {
  getSpecialConnectionTypePermissions,
} from 'src/constants'
import { MyApprovalTabKeyType }from 'src/pageTabs/access-request/constants';
import { getSdtThinPermissionIconType } from 'src/util';
import { Iconfont, TooltipWithQuestionIcon } from 'src/components'
import { applyStatusMap, priGranTypeMap } from '../constants'
import i18next from 'i18next';
import styles from "./index.module.scss";

export const columnsRequest = ({
  priGranTypeValue = null,
  activeKey
}: {
  priGranTypeValue?: any;
  activeKey?: MyApprovalTabKeyType;
}): ColumnsType<any> => [
  {
    title: i18next.t('flow_application_number'),
    dataIndex: 'uuid',
    width: 200,
  },
  {
    title: i18next.t('flow_title'),
    dataIndex: 'title',
    width: 200,
    render: (title, record) => {
      return <span>{title ? title : '-'}</span>
    }
  },
  {
    title: i18next.t('flow_type'),
    dataIndex: 'priGranType',
    width: 150,
    filters: Object.keys(priGranTypeMap).map((key) => ({
      text: i18next.t(priGranTypeMap[key]),
      value: key,
    })),
    filteredValue: priGranTypeValue,
    filterMultiple: true,
    render: (_, { priGranType }) => {
      return <span>{ priGranTypeMap[priGranType] ? i18next.t(priGranTypeMap[priGranType])  : '-'}</span>
    },
  },
  {
    title: i18next.t('flow_applicant'),
    dataIndex: 'applyUserName',
    width: 150,
    render: (applyUserName, record) => (
      <div>
        {
          record?.applyUserName && record?.applyUserId ? 
          (record?.applyUserName + '(' + record?.applyUserId + ')') : 
          (
            record?.applyUserName ? record?.applyUserName : 
            (record?.applyUserId ? '(' + record?.applyUserId + ')' : '-')
          )
        }
        {/* <span>{applyUserName}</span>
        <span className={styles.dept}>{deptName}</span> */}
      </div>
    ),
  },
  {
    title: i18next.t('flow_status'),
    dataIndex: 'applyStatus',
    width: 100,
    render: (_, { applyStatus }) => {
      return <span>
        <span
          className={classNames(
            styles.statusDot,
            applyStatus === 'pending' && styles.pendingBack,
            (applyStatus === 'pass' || applyStatus === 'already') && styles.alreadyBack,
            (applyStatus === 'power' || activeKey === 'power') && styles.powerBack,
            applyStatus === 'refuse' && styles.rejectBack,
            applyStatus === 'withdraw' && styles.rejectBack,
          )}
        ></span>
        {activeKey === 'power'? i18next.t(applyStatusMap['power']) : i18next.t(applyStatusMap[applyStatus])}
      </span>
    },
  },
  // {
  //   title: '开始时间',
  //   dataIndex: 'beginTime',
  //   width: 200,
  //   render: (_, { beginTime }) => {
  //     return beginTime ? dayjs(beginTime).format('YYYY-MM-DD HH:mm:ss') : '-'
  //   },
  //   sorter: true
  // },
  // {
  //   title: '结束时间',
  //   dataIndex: 'endTime',
  //   width: 200,
  //   render: (_, { endTime }) =>{
  //     return endTime ? dayjs(endTime).format('YYYY-MM-DD HH:mm:ss') : '-'
  //   },
  // },
  {
    title: i18next.t('flow_effective_time'),
    dataIndex: 'time',
    ellipsis: {
      showTitle: false,
    },
    width: 200,
    render: (currentAssigneeName, record) => (
      <Tooltip placement="topLeft" title={record?.priGranType === 'exportTask' ? '--'  :  (record?.endTime && record?.beginTime) ? `${dayjs(record?.beginTime).format('YYYY-MM-DD HH:mm:ss')} - ${dayjs(record?.endTime).format('YYYY-MM-DD HH:mm:ss')} ` : i18next.t('flow_permanent') } >
        {record?.priGranType === 'exportTask' ? '--'  :  (record?.endTime && record?.beginTime) ? `${dayjs(record?.beginTime).format('YYYY-MM-DD HH:mm:ss')} - ${dayjs(record?.endTime).format('YYYY-MM-DD HH:mm:ss')} ` : i18next.t('flow_permanent') } 
      </Tooltip>
      
    ),
  },
  {
    title: i18next.t('flow_current_approver'),
    dataIndex: 'currentAssignee',
    width: 160,
    render: (currentAssigneeName, record) => (
        <div>
          {
            !record?.currentAssigneeName ?
                (!record?.currentAssignee ? '-': record?.currentAssignee)
                :
                (record?.currentAssigneeName + '(' + record?.currentAssignee + ')')
          }
        </div>
    ),
  },
  {
    title: i18next.t('flow_operation'),
    dataIndex: 'actions',
    render: () => '-',
    width: 200,
    // align: 'center',
  },
]

export const columnsRequestExpandRow = (): ColumnsType<any> => [
  {
    title: i18next.t('flow_sub_task_number'),
    dataIndex: 'uuid',
    key: 'uuid',
    width: 200,
  },
  {
    title: i18next.t('flow_connection_quantity'),
    dataIndex: 'connectionCount',
    key: 'connectionCount',
  },
  {
    title: i18next.t('flow_assigned_person'),
    dataIndex: 'connectionManager',
    key: 'connectionManager',
    render: (_: string, record: any) =>`${_} ${record?.connectionManagerId ? `(${record?.connectionManagerId})` : ''}`
  },
  {
    title: i18next.t('flow_status'),
    dataIndex: 'applyStatus',
    key: 'status',
    render: (_, { applyStatus }) => {
      return <span>
        <span
          className={classNames(
            styles.statusDot,
            applyStatus === 'pending' && styles.pendingBack,
            applyStatus === 'already' && styles.alreadyBack,
            applyStatus === 'power' && styles.powerBack,
            applyStatus === 'rejected' && styles.rejectBack,
            applyStatus === 'stop' && styles.rejectBack,
          )}
        ></span>
        {i18next.t(applyStatusMap[applyStatus])}
      </span>
    },
  },
]

export const columnsDetail = (): ColumnsType<any> => [
  {
    title: i18next.t('flow_connection_name'),
    dataIndex: 'connectionName',
    render: (_, record) => {
      return <span>
        <Iconfont type={`icon-connection-${record?.connection?.connectionType}`} style={{ marginRight: 4 }} />
        {record?.connection?.connectionName}
      </span>
    }
  },
  {
    title: i18next.t('flow_instance_name'),
    dataIndex: 'instanceName',
    render: (_, record) => {

      if (record?.connection?.connectionType === 'Redis') {
        return record?.connection?.userInputs?.connectionMembers?.map((item: any) =>item?.connectionUrl + ':' + item?.connectionPort )
      }
      return <span>
        {record?.connection?.userInputs?.connectionUrl + ':' + record?.connection?.userInputs?.connectionPort}
      </span>
    },
  },
  {
    title: i18next.t('flow_service_name'),
    dataIndex: 'serverName',
    width: 100,
    render: (_, record) => {
      return <span>{(record?.connection?.userInputs?.serviceName) ? (record?.connection?.userInputs?.serviceName) : '-'}</span>
    }
  },
  {
    title: i18next.t('flow_version_number'),
    dataIndex: 'version',
    render: (_, record) => {
      return <span>{record?.connection?.userInputs?.dataSourceVersion}</span>
    }
  },
  {
    title: i18next.t('flow_connection_user'),
    dataIndex: 'connectionUser',
    render: (_, record) => {
      return <span>{record?.connection?.userInputs?.userName}</span>
    }
  },
  {
    title: i18next.t('flow_connection_admin'),
    dataIndex: 'connectionAdminis',
    render: (_, record) => {
      return <span>
        {
          record?.connection?.managerName && record?.connection?.manager ? 
          (record?.connection?.managerName + '(' + record?.connection?.manager + ')') : 
          (
            record?.connection?.managerName ? record?.connection?.managerName : 
            (record?.connection?.manager ? '(' + record?.connection?.manager + ')' : '-')
          )
        }
      </span>
    }
  }
]

// 申请单 粗粒度
export const detailExpamdedRow = (): ColumnsType<any> => [
  {
    title: i18next.t('flow_resource_name'),
    dataIndex: 'nodeName',
    key: 'nodeName',
    width: '20%',
    ellipsis: true
  },
  {
    title: i18next.t('flow_remark'),
    dataIndex: 'remark',
    key: 'remark',
    width: '20%',
    ellipsis: true,
    render: (_, record) => {
      return <span>{_ ? _ : '-'}</span>
    }
  },
  // { title: '分类', dataIndex: 'serveName', key: 'serveName', width: '10%', ellipsis: true },
  {
    title: i18next.t('flow_permission_request'),
    dataIndex: 'permissionTypeName',
    key: 'permissionTypeName',
    width: '20%',
  },
  {
    title: i18next.t('flow_tool_permissions'),
    dataIndex: 'toolsAuth',
    key: 'toolsAuth',
    width: '20%',
    ellipsis: true
  },
  {
    title: i18next.t('flow_role_permissions'),
    dataIndex: 'roleName',
    key: 'roleName',
    width: '10%',
    ellipsis: true
  },
  { title: '', dataIndex: 'actions', key: 'actions', width: '10%', ellipsis: true },
]

// 申请单 细粒度
export const detailFineGritExpamdedRow = (): ColumnsType<any> => [
  { title: i18next.t('flow_ob_name'), dataIndex: 'nodeName', key: 'nodeName', width: '20%', ellipsis: true },
  {
    title: i18next.t('flow_type'),
    dataIndex: 'groupName',
    key: 'groupName',
    // render: (_, { flowType }) => priGranTypeMap[flowType],
    width: '10%'
  },
  {
    title: i18next.t('flow_remark'),
    dataIndex: 'remark',
    key: 'remark',
    width: '20%',
    ellipsis: true,
    render: (_, record) => {
      return <span>{_ ? _ : '-'}</span>
    }
  },
  {
    title: i18next.t('flow_comment'),
    dataIndex: 'connectionNum',
    key: 'connectionNum',
    width: '20%',
    ellipsis: true,
    render: (_, record) => {
      return <span>{_ ? _ : '-'}</span>
    }
  },
  {
    title: i18next.t('flow_permission_request'),
    dataIndex: 'sourceOperationList',
    key: 'sourceOperationList',
    width: '20%',
    ellipsis: true,
    render: (sourceOperationList, record) => {

      let aa = getSpecialConnectionTypePermissions(record?.dataSourceType)[record?.nodeType]?.map((type: string) => {
        let permissionTypeStatus = sourceOperationList[type]
        const permissionType = getSdtThinPermissionIconType(type);
        let iconType = permissionTypeStatus === '1' ? `icon-type-${permissionType}` : (
          permissionTypeStatus === '0' ? `icon-type-${permissionType}_disabled` : `icon-type-${permissionType}_add`
        )
        return (
          <Tooltip title={type} key={type}>
            <Iconfont
              type={iconType}
              className={styles.permissionLimitIcon}
            />
          </Tooltip>
        )
      })
      return aa
    }
  },
  // { title: '', dataIndex: 'actions', key: 'actions' },
]

export const resourceExpamdedRow = (): ColumnsType<any> => [
  { title: i18next.t('flow_resource_name'), dataIndex: 'nodeName', key: 'nodeName', width: 200, ellipsis: true },
  { title: i18next.t('flow_remark'), dataIndex: 'remark', key: 'remark', width: 300, ellipsis: true },
  { title: i18next.t('flow_permission_level_request'), dataIndex: 'authLevel', key: 'authLevel'},
  { title: i18next.t('flow_tool_permission_request'), dataIndex: 'toolsAuth', key: 'toolsAuth', width: 160, ellipsis: true },
  { title: i18next.t('flow_operation'), dataIndex: 'actions', key: 'actions' }
]


