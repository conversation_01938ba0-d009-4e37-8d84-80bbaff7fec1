.mt27 {
	margin-top: 27px;
}
.mb27 {
	margin-bottom: 27px;
}
.mr10 {
	margin-right: 10px;
}
.m0 {
	margin: 0;
}
.ml10 {
	margin-left: 10px;
}
.fs14 {
	font-size: 14px;
}
.stepsWrap {
	:global {
		.ant-steps-item-container {
			display: flex;
			// align-items: center;
		}
	}
}
.borderShow {
	box-shadow: 0px 8px 15px 0px rgba(149, 156, 182, 0.15),inset -2px 0px 0px 0px rgba(255, 255, 255, 0.07);
}
.instanceName {
	background: rgba(50, 98, 255, 0.1);
	padding: 2px 5px;
	border-radius: 4px;
}
.step-icon {
	background-color: #eaefff!important;
	border-radius: 50%;
	width: 32px;
	height: 32px;
	line-height: 32px;
	font-size: 14px;
	color: #3262FF;
	position: relative;
	.icon {
		display: inline-block;
		width: 32px;
		height: 32px;
		line-height: 32px;
		border-radius: 50%;
	}
	.icon-badge {
		position: absolute;
	}
}
.oddDescription {
	:global {
		.ant-descriptions-item-content {
			margin-left: 10px;
		}
	}
}
.start {
	background-color: #e2f4e9!important;
	color: #36B839;
}
.pending {
}
.success {
	background: #36b839 !important;
}
.error {
	background: red !important;
}

.statusDot {
	display: inline-block;
	width: 8px;
	height: 8px;
	border-radius: 50%;
	margin-right: 8px;
}
.pendingBack {
	background: #0256FF;
}
.alreadyBack {
	background: #1AD42D;
}
.powerBack {
	background: #D6D7DB;
}
.rejectBack {
	background: #EA3223;
}
.pendingColor {
	color: #0256FF;
}
.alreadyColor {
	color: #1AD42D;
}
.rejectColor {
	color: #EA3223;
}
.desc {
	display: flex;
	flex-direction: column;
}
// .time {
//     color: #868fa3;
//     margin-left: 7px;
//   }

.permissionLimitIcon {
  margin-left: 4px;
  color: rgba(0, 0, 0, 0.1);
}
.dept {
	background: rgba(50, 98, 255, .1);
	color: #3262FF;
	padding: 4px 8px;
	border-radius: 4px;
	margin-left: 6px;
}
.descColor{
	color: rgba(0, 0, 0, 0.45);
}