import React from 'react'
import { <PERSON><PERSON>, <PERSON>, Modal, message } from 'antd'
import { CopyOutlined } from "@ant-design/icons";
import classnames from 'classnames'
import styles from '../index.module.scss'
import { VerticalAlignBottomOutlined } from '@ant-design/icons'
import { fetchGet } from 'src/api/customFetch'
import copy from "copy-to-clipboard";
import { useTranslation } from 'react-i18next';

interface IProps {
  uploadFileData: {
    fileName: string,
    taskId: number;
    taskStatus: 'DOWNLOADED' | 'SUCCESS';
    errorLog?: string;
  }
}

export const UploadFilePag = (props: IProps) => {
  const { uploadFileData } = props
  const { taskId, fileName } = uploadFileData
  const { t } = useTranslation()

  const copyMet = (mes: string) => {
    copy(mes);
    message.success(t('flow_copy_success'));
  };

  // 下载附件
  const downloadFile = (taskId: number) => {
    fetchGet(`/export/export/check/encrypt/${taskId}`).then((res: any)=>{
      const { whetherToEncrypt, fileSecretKey } = res || {}
      if(whetherToEncrypt){
        Modal.info({
          width: 520,
          content: (
            <div>
              <div style={{ fontSize: '18px', marginBottom:'30px'}}>{t('flow_file_key')}</div>
              {t('flow_export_encrypted_key')}:
              <span style={{ fontWeight: "bold" }}>
                {fileSecretKey}
              </span>
              <CopyOutlined
                style={{ color: "#0c2dc7" }}
                onClick={() => {
                  copyMet(fileSecretKey);
                }}
              />
            </div>
          ),
          icon: null,
          onOk: () => {
            window.open(`/export/export/download/${taskId}`, '_self')
          }
        });
      }else {
        window.open(`/export/export/download/${taskId}`, '_self')
      }
    }).catch((err: any)=>{console.error(err)})


    
  }

  return (
    <Card
      title={t('flow_attachment_info')}
      className={classnames(styles.borderShow, 'mb20')}
    >
      {
        ['DOWNLOADED', 'SUCCESS'].includes(uploadFileData?.taskStatus) ?
          <Button
            type="link"
            onClick={() => downloadFile(taskId)}
          >
            {fileName}<VerticalAlignBottomOutlined />
          </Button>
          : `${t('flow_export_error')}：${uploadFileData?.errorLog}`
      }
    </Card>
  )
}
