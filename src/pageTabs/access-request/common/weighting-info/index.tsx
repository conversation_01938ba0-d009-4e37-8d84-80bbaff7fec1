import React, { FC, useState } from 'react'
import { Card, Descriptions, message , Space, Input, Modal} from 'antd'
import classnames from 'classnames'
import { useRequest, useDispatch } from 'src/hook'
import { LinkButton } from 'src/components';
import { dealWithAccessPermission } from 'src/api'
import { useTranslation } from 'react-i18next'
import { setMineApprovePageState, setMineApproveDetailParams } from 'src/pageTabs/access-request/accessRequestSlice'
import styles from '../index.module.scss'

interface ListProps {
  remarkData: []
  withdrawalState?: boolean;
  uuid?: string;
  isShowWeightingOperation?: boolean;
}

export const WeightRemarksPag: FC<ListProps> = (props) => {
  const dispatch = useDispatch();
  const { remarkData, withdrawalState =  false, uuid, isShowWeightingOperation = false } = props
  const { t } = useTranslation()
  const [approveType, setApproveType] = useState<'rejected' | 'fullilled' | 'pending'>('pending');

  // 流程-我的审批
  const handleToApprove = () => {
    dispatch(setMineApprovePageState(''))
    dispatch(setMineApproveDetailParams({}))
  }

  // 落权
   const { run: weightingRun } = useRequest(dealWithAccessPermission, {
    manual: true,
    onSuccess() {
      if (approveType === 'fullilled') {
        message.success(t('flow_approved_success'))
      } else {
        message.success(t('flow_rejected_success'))
      }
      handleToApprove()
    }
  })

  // 同意
  const agreeBtn = (actionType: boolean) => {
    if(actionType) {
      setApproveType('fullilled')
    }else {
      setApproveType('rejected')
    }
   
    function isEmpty(closeFn: any, val?: string) {
      let params = {
        flowUUID: uuid,
        powerStatus: actionType ?'already' : 'rejected',
        remarks: val
      };
      //@ts-ignore
    weightingRun(params).finally(() => {
      setApproveType('pending')
    })
      closeFn();
    }
    const textOnchange = (val: string) => {
      update({
        onOk: (closeFn) => isEmpty(closeFn,val),
      });
    };
    const { update } = Modal.confirm({
      title: actionType?  t('flow_approval_comments'): t('flow_rejection_comments'),
      content: (
        <Input.TextArea
          rows={5}
          onChange={(e) => textOnchange(e.target.value)}
        />
      ),
      okText: t('flow_ok'),
      cancelText: t('flow_cancel'),
      onOk: (closeFn) => isEmpty(closeFn),
    });
  }
  
  return (
    <Card
      title={ withdrawalState ? t('flow_withdrawal_info'): t('flow_permission_info') }
      className={classnames(styles.borderShow, 'mb20')}
      extra={isShowWeightingOperation && <Space>{t('flow_operation')}:
        <LinkButton onClick={() => agreeBtn(true)}>{t('flow_approve')}</LinkButton>
        <LinkButton onClick={() => agreeBtn(false)}>{t('flow_reject')}</LinkButton>
      </Space>}
    >
      {
        remarkData && remarkData?.length ? remarkData?.map((item: any, index: number) => {
          return <Descriptions key={index}>
            <Descriptions.Item label={item?.approvedUser}> {item?.approvedResultDesc}</Descriptions.Item>
            <Descriptions.Item label={t('flow_remark')}>{item?.approvedComment ? item?.approvedComment : '-'}</Descriptions.Item>
          </Descriptions>
        }) : ''
      }
    </Card>
  )
}
