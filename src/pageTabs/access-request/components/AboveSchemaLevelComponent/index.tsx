import React, { useState, useEffect } from "react";
import * as _ from "lodash";
import classnames from 'classnames'
import { useHistory } from 'react-router-dom'
import { DeleteOutlined, ExclamationCircleOutlined, ShoppingCartOutlined, RollbackOutlined } from '@ant-design/icons'
import { Card, Row, Col, Checkbox, Radio, Spin, Tooltip, Button, Badge } from "antd";
import {
  getPermissionList,
  getPermissionTemplate,
} from 'src/api';

import type { CheckboxValueType } from "antd/es/checkbox/Group";
import { useRequest, useDispatch } from "src/hook";
import { setStr } from "src/util/connectionManage";
import { useTranslation } from "react-i18next";
import styles from "./index.module.scss";
import { setMineApplyPageState, setMineApplyDetailParams } from 'src/pageTabs/access-request/accessRequestSlice'

const AboveSchemaLevelComponent = ({
  selectTreeItem,
  loading = false,
  list = [],
  isEdit = false,
  needRefresh,
  isInShoppingCart = false, //是否在购物清单中
  viewAppliedDetail = false, //仅查看
  permissionCollectionVOS,
  updatePermissionCollectionVOS,
  connectionObjectPermissions,
  onUpdateConnectionObjectPermissions,
  cardClassName
}: {
  isEdit?: boolean;
  list?: any;
  loading?: boolean;
  selectTreeItem?: any;
  needRefresh?: boolean;
  isInShoppingCart?: boolean;
  viewAppliedDetail?: boolean;
  permissionCollectionVOS?: any;
  onUpdateConnectionObjectPermissions?: (params: any) => void;
  connectionObjectPermissions?: any;
  updatePermissionCollectionVOS?: (params: any) => void
  cardClassName?: any //card样式
}) => {
  const dispatch = useDispatch()
  const { connection, dataSourceType } = selectTreeItem || {};
  const { connectionType } = connection || {};
  const history = useHistory();
  const { t } = useTranslation();
  const radioTypes = ["permissionTemplate", "roleConnection"];
  const [changedObjectTypes, setChangedObjectTypes] = useState<any>({});
  const [permissionsOptsSet, setPermissionsOptsSet] = useState<any>();

  // 查询不同数据源下对应的所有权限信息
  const { run: getPermissionTemplateEnum } = useRequest(
    getPermissionTemplate,
    { manual: true },
  )

  // 获取权限等级列表
  const { run: getPermissionListEnum } = useRequest(
    getPermissionList,
    {
      manual: true,
      onSuccess: (res: any) => {
        getPermissionTemplateEnum(dataSourceType).then((templateRes: any) => {
          let options: { [key: string]: any[] } = {};
          res?.forEach((item: any) => {
            if (item?.id) {
              let templateOperationsSetTmp = item?.templateOperationsSet;
              options[item?.id] = templateOperationsSetTmp?.flatMap((optionsItem: any) => {
                const matchedTemplate = templateRes?.find((templateItem: any) => templateItem?.objectType === optionsItem?.objectType);
                if (matchedTemplate) {
                  return optionsItem?.operations?.map((operation: any) => {
                    const matchedOperation = matchedTemplate?.operations?.find((templateOperation: any) => templateOperation?.operation === operation);
                    if (matchedOperation) {
                      return `${operation}(${matchedOperation?.operationName})`;
                    }
                    return operation;
                  });
                } else {
                  return [];
                }
              });
            }
          });
          setPermissionsOptsSet(options)
        })
      }
    },
  )

  const getInitCheckedValues = (list: any[]) => {
    let obj: any = {};
    list?.map((item: any) => {
      if (!radioTypes.includes(item.objectType)) {
        let checkedTypes: string[] = [];
        item?.operations?.map((o: any) => {
          if (o?.hasSelected === true) {
            checkedTypes.push(o.operation)
          }
        })
        if (!_.isEmpty(checkedTypes)) {
          obj[item.objectType] = checkedTypes;
        }

      } else {
        const radioCheckedItem = item?.operations?.find((o: any) => o?.hasSelected === true) || {};

        if (!_.isEmpty(radioCheckedItem)) {
          obj[item.objectType] = radioCheckedItem.operation;
        }
      }
    })
    return obj
  }

  useEffect(() => {

    const initCheckedValues = getInitCheckedValues(list?.operationsVoList)

    const cloneAllChangedObjectSetting = _.cloneDeep(connectionObjectPermissions)
    const curItem = cloneAllChangedObjectSetting?.[selectTreeItem?.key]

    if (!_.isEmpty(curItem) && isInShoppingCart) {
      onUpdateConnectionObjectPermissions?.({
        ...cloneAllChangedObjectSetting,
        [selectTreeItem?.key]: curItem
      })
      setChangedObjectTypes(curItem);
    } else {
      setChangedObjectTypes(initCheckedValues);
    }
  }, [JSON.stringify(list), isInShoppingCart])

  useEffect(() => {
    if (dataSourceType) {
      getPermissionListEnum(dataSourceType)
    }
  }, [dataSourceType, getPermissionListEnum])

  const onChangeCheckbox = (
    checkedValues: CheckboxValueType[],
    type: string
  ) => {

    let cloneChangeObjectTypes = _.cloneDeep(changedObjectTypes)
    cloneChangeObjectTypes = {
      ...cloneChangeObjectTypes,
      [type]: checkedValues,
    }
    setChangedObjectTypes(cloneChangeObjectTypes);
  };

  const onCheckAllChange = (checked: boolean, type: string) => {

    const changedItem = list?.operationsVoList?.find((i: any) => i.objectType === type);
    const curAllTypes = changedItem?.operations?.filter((ope: any) => ope.canSelect)?.map((o: any) => o.operation);
    const cloneChangeObjectTypes = _.cloneDeep(changedObjectTypes);
    const params = {
      ...cloneChangeObjectTypes,
      [type]: checked ? curAllTypes : [],
    }
    setChangedObjectTypes(params);

  };

  const onChangeRadioStatus = async (value: string | null, type: string, valid?: boolean) => {
    const cloneChangeObjectTypes = _.cloneDeep(changedObjectTypes)
    const params = {
      ...cloneChangeObjectTypes,
      [type]: value
    }
    setChangedObjectTypes(params);

  }


  const getUserToolVoValue = (nodeType: string, dataSourceType: string, connectionType: string, templateOperationsSet: any,) => {
    let res = {};
    if (nodeType === "datasource") {
      res = {
        dataSourceType: selectTreeItem?.nodeName,
        nodeName: selectTreeItem?.nodeName,
        nodePath: selectTreeItem?.nodePath,
        nodeType: selectTreeItem?.nodeType,
        templateOperationsSet
      };
    } else {
      res = {
        dataSourceType: dataSourceType || connectionType,
        nodePathWithType: selectTreeItem?.nodePathWithType,
        nodeName: selectTreeItem?.nodeName,
        nodePath: selectTreeItem?.nodePath,
        nodeType: selectTreeItem?.nodeType,
        templateOperationsSet
      };
    }
    return res;
  }

  const translateSubmitParams = (changedObjectTypes: any) => {
    let permissionAndRoleParams: any = {};
    let templateOperationsSet = [];

    for (const i in changedObjectTypes) {
      const checkedValue = changedObjectTypes[i];

      switch (i) {
        case 'permissionTemplate':
          if (selectTreeItem?.nodeType === "datasource") {
            permissionAndRoleParams.permissionSdtUserAddVo = {
              templateId: checkedValue ? Number(checkedValue) : checkedValue,
              dataSourceType: selectTreeItem?.nodeName,
              nodeName: selectTreeItem?.nodeName,
              nodePath: selectTreeItem?.nodePath,
              nodeType: selectTreeItem?.nodeType,
            }
          } else {
            permissionAndRoleParams.permissionSdtUserAddVo = {
              nodePathWithType: selectTreeItem?.nodePathWithType,
              templateId: checkedValue ? Number(checkedValue) : checkedValue,
              dataSourceType: selectTreeItem?.dataSourceType || connectionType,
              nodeName: selectTreeItem?.nodeName,
              nodePath: selectTreeItem?.nodePath,
              nodeType: selectTreeItem?.nodeType,
            }
          }
          break;
        case 'roleConnection':
          if (selectTreeItem?.nodeType === "datasource") {
            permissionAndRoleParams.roleUpdateVo = {
              roleType: checkedValue,
              dataSourceType: selectTreeItem?.nodeName,
              nodeName: selectTreeItem?.nodeName,
              nodePath: selectTreeItem?.nodePath,
              nodeType: selectTreeItem?.nodeType,
            }
          } else {
            permissionAndRoleParams.roleUpdateVo = {
              nodePathWithType: selectTreeItem?.nodePathWithType,
              roleType: checkedValue,
              dataSourceType: selectTreeItem?.dataSourceType || connectionType,
              nodeName: selectTreeItem?.nodeName,
              nodePath: selectTreeItem?.nodePath,
              nodeType: selectTreeItem?.nodeType,
            }
          }
          break;
        case 'sdtMenu':
        case 'resultSetOperation':
        case 'exportTool':
          templateOperationsSet.push({
            objectType: i,
            operations: checkedValue,
          })
          break
      }
    }
    //  更新
    const formattedChangedItem = {
      ...permissionAndRoleParams,
      ...(!_.isEmpty(templateOperationsSet) ? {
        userToolVo:
          getUserToolVoValue(selectTreeItem?.nodeType, dataSourceType, connectionType, templateOperationsSet)
      } : [])
    }

    const cloneAllChangedObjectSetting = _.cloneDeep(connectionObjectPermissions)

    if (!_.isEmpty(changedObjectTypes)) {
      onUpdateConnectionObjectPermissions?.({
        ...cloneAllChangedObjectSetting,
        [selectTreeItem?.key]: changedObjectTypes
      })
    }

    let clonePermissionCollectionVOS = _.cloneDeep(permissionCollectionVOS);
    //是否修改过
    let isChangedObjectItem;
    const nodeType = selectTreeItem?.nodeType;
    if (nodeType === "datasource") {
      isChangedObjectItem = selectTreeItem && permissionCollectionVOS?.find((item: any) => (
        item?.permissionSdtUserAddVo?.dataSourceType === selectTreeItem?.nodeName ||
        item?.roleUpdateVo?.dataSourceType === selectTreeItem?.nodeName ||
        item?.userToolVo?.dataSourceType === selectTreeItem?.nodeName ||
        item?.permissionCollectionObjectVO?.dataSourceType === selectTreeItem?.nodeName));
    } else {
      isChangedObjectItem = selectTreeItem && permissionCollectionVOS?.find((item: any) => (
        item?.permissionSdtUserAddVo?.nodePathWithType === selectTreeItem?.nodePathWithType ||
        item?.roleUpdateVo?.nodePathWithType === selectTreeItem?.nodePathWithType ||
        item?.userToolVo?.nodePathWithType === selectTreeItem?.nodePathWithType ||
        item?.permissionCollectionObjectVO?.nodePathWithType === selectTreeItem?.nodePathWithType));
    }


    if (_.isEmpty(isChangedObjectItem)) {
      clonePermissionCollectionVOS = clonePermissionCollectionVOS.concat([formattedChangedItem]);

    } else {
      clonePermissionCollectionVOS = clonePermissionCollectionVOS?.map((item: any) => {
        const nodeType = selectTreeItem?.nodeType;
        if (nodeType === "datasource") {
          if (item?.permissionSdtUserAddVo?.dataSourceType === selectTreeItem?.nodeName ||
            item?.roleUpdateVo?.dataSourceType === selectTreeItem?.nodeName ||
            item?.userToolVo?.dataSourceType === selectTreeItem?.nodeName) {
            return {
              ...item,
              ...formattedChangedItem
            }
          }
        } else if (nodeType === "group") {
          if (item?.permissionSdtUserAddVo?.dataSourceType === (selectTreeItem?.dataSourceType || connectionType) ||
            item?.roleUpdateVo?.dataSourceType === (selectTreeItem?.dataSourceType || connectionType) ||
            item?.userToolVo?.dataSourceType === (selectTreeItem?.dataSourceType || connectionType)) {
            return {
              ...item,
              ...formattedChangedItem
            }
          }
        } else {
          if (item?.permissionSdtUserAddVo?.nodePathWithType === selectTreeItem?.nodePathWithType ||
            item?.roleUpdateVo?.nodePathWithType === selectTreeItem?.nodePathWithType ||
            item?.userToolVo?.nodePathWithType === selectTreeItem?.nodePathWithType) {

            return {
              ...item,
              ...formattedChangedItem
            }
          }
        }
        return item
      })

    }

    //购物车中数据可存储多条修改 数据  ，访问申请只存储最新数据
    if (isInShoppingCart) {
      if (!_.isEmpty(changedObjectTypes)) {
        onUpdateConnectionObjectPermissions?.({
          ...cloneAllChangedObjectSetting,
          [selectTreeItem?.key]: changedObjectTypes
        })
        updatePermissionCollectionVOS?.(clonePermissionCollectionVOS)
      }

    } else {
      updatePermissionCollectionVOS?.([formattedChangedItem])
    }
  }

  useEffect(
    () => {
      isEdit && translateSubmitParams(changedObjectTypes)
    },
    [changedObjectTypes],
  );

  const handleRenderToSearch = () => {
    dispatch(setMineApplyPageState('search'))
    dispatch(setMineApplyDetailParams({}))
  }

  return (
    <Spin spinning={loading} style={{ padding: "10px 10px" }}>
      <Card
        title={<div className={styles.connectionTitle}>
          <div>{(isInShoppingCart || viewAppliedDetail) ? t('flow_clear_selections') : t('flow_select_permissions')} </div>
          {
            !viewAppliedDetail &&
            <>
              {
                isInShoppingCart ?
                  < Button type="primary" icon={<RollbackOutlined />}
                    onClick={handleRenderToSearch}
                  >{t('flow_add_more_resources')}</Button>
                  :
                  <Badge count={1} size="small" className="mr10">
                    < Button type="primary" icon={<ShoppingCartOutlined />}>{t('flow_request_list')}</Button>
                  </Badge>
              }
            </>
          }
        </div>}
        bordered={false}
        headStyle={{ borderBottom: "1px solid #EBEEF6" }}
        bodyStyle={{ padding: "16px 28px", height: 'calc(100% - 80px)', overflowY: 'scroll' }}
        className={classnames(styles.connectionSettingCard, cardClassName)}
      >
        <div className={styles.settingContent}>
          {list?.operationsVoList?.map((item: any) => {
            // 导出功能非权限控制
            const exportCheckedAllDisabled: boolean = item?.objectType === "exportTool" && item?.operations.filter((i: any) => i.canSelect).length === 0
            const childNode: any = <Checkbox
              disabled={isEdit && !exportCheckedAllDisabled ? !list?.canOperation : true}
              checked={
                changedObjectTypes?.[item.objectType]?.length ===
                  item?.operations.filter((o: any) => o.canSelect)?.length
                  ? true
                  : false
              }
              onChange={(e: any) =>
                onCheckAllChange(e.target.checked, item?.objectType)
              }
            >
              {item?.objectTypeName}
            </Checkbox>
            // 根据导出功能 是否是根据权限控制来控制tooltip显隐
            const showTooltip = exportCheckedAllDisabled ?
              <Tooltip title={t('flow_other_control_methods')}>
                {childNode}
              </Tooltip> : childNode
            return <Row
              key={item?.objectType}
              className={styles.settingItem}
              style={{ width: `calc(100% / ${list?.operationsVoList?.length || 0})` }}
            >
              <Col span={24} className={styles.itemHeader}>
                <Row align="top" justify="start">
                  <Col span={24}>
                    {radioTypes.includes(item.objectType) ? (
                      <>{item.objectTypeName}
                        {
                          !viewAppliedDetail &&
                          <Tooltip title={t('flow_clear_selections')}>
                            <DeleteOutlined className={styles.clearIcon} onClick={() => {
                              const itemInfo = item?.operations?.find((operation: any) => operation?.childObjects?.length > 0);
                              isEdit && onChangeRadioStatus(null, item.objectType, _.isEmpty(itemInfo))
                            }} />
                          </Tooltip>
                        }
                      </>
                    ) : showTooltip}
                  </Col>
                </Row>
              </Col>
              <Col span={24} className={styles.itemBody}>
                {radioTypes.includes(item.objectType) ? (
                  <Radio.Group
                    disabled={isEdit ? list?.canOperation === false ? true : false : true}
                    value={changedObjectTypes?.[item.objectType] || []}
                    onChange={(e: any) => {

                      const itemInfo = item?.operations?.find((operation: any) => operation?.childObjects?.length > 0);

                      onChangeRadioStatus(e.target.value, item?.objectType, _.isEmpty(itemInfo))
                    }}
                  >
                    <Row >
                      {item?.operations?.map((operation: any) => (
                        <Col span={24}
                          className={styles.bodyCol}
                          key={operation?.operation}>
                          <Radio value={operation?.operation} className={classnames(styles.fontColor, {
                            [styles.deleteLine]: operation?.childObjects?.length > 0
                          })}>
                            <Tooltip
                              title={
                                permissionsOptsSet &&
                                Object.keys(permissionsOptsSet).includes(operation?.operation) &&
                                (setStr(permissionsOptsSet[operation?.operation?.toString()]) || '-')
                              }
                              overlayClassName={
                                permissionsOptsSet &&
                                  Object.keys(permissionsOptsSet).includes(operation?.operation) &&
                                  !!permissionsOptsSet[operation?.operation?.toString()].join(",\n")
                                  ? styles.permissionTooltip : ''
                              }
                              overlayStyle={{ whiteSpace: 'pre-line' }}
                            >

                              {operation?.childObjects?.length > 0 ?
                                <>
                                  <Tooltip title={t('flow_user_permission_level')} placement="leftTop">
                                    <ExclamationCircleOutlined />
                                  </Tooltip>
                                  {operation?.operationName}
                                </>
                                : operation?.operationName}
                            </Tooltip>
                          </Radio>
                        </Col>
                      ))}
                    </Row>
                  </Radio.Group>
                ) : (
                    <>
                      <Checkbox.Group
                        value={changedObjectTypes?.[item.objectType] || [] || []}
                        onChange={(values: CheckboxValueType[]) =>
                          onChangeCheckbox(values, item?.objectType)
                        }
                      >
                        <Row>
                          {item?.operations?.map((operation: any) => {
                            // 文本导入非权限控制
                            const importCheckedDisabled: boolean = item?.objectType === "sdtMenu" && operation?.operation === 'CQ_DATA_IMPORT' && !operation?.canSelect
                            const disabled = isEdit && !exportCheckedAllDisabled && !importCheckedDisabled ? !list?.canOperation : true
                            const colChild = <Col span={24} key={operation?.operation} className={styles.bodyCol}>
                              <Checkbox value={operation?.operation} className={styles.fontColor} disabled={disabled}>
                                {operation?.operationName}
                              </Checkbox>
                            </Col>
                            const showTooltip = exportCheckedAllDisabled ?
                              <Tooltip title={t('flow_other_control_methods')} placement="bottomLeft">
                                {colChild}
                              </Tooltip> : colChild
                            return importCheckedDisabled ? null : showTooltip
                          })}
                        </Row>
                      </Checkbox.Group>
                      {
                        item?.objectType === "sdtMenu" && <Row>
                          {item?.operations?.filter((o: any) => o?.operation === 'CQ_DATA_IMPORT' && !o?.canSelect)?.map((operation: any) => {
                            return <Tooltip title={t('flow_other_control_methods')} placement="bottomLeft" key={operation?.operation}>
                              <Col span={24} key={operation?.operation} className={styles.bodyCol}>
                                <Checkbox value={operation?.operation} className={styles.fontColor} disabled={true}>
                                  {operation?.operationName}
                                </Checkbox>
                              </Col>
                            </Tooltip>
                          })}
                        </Row>
                      }
                    </>
                )}
              </Col>
            </Row>
          })}
        </div>
      </Card>
    </Spin>
  );
};

export default AboveSchemaLevelComponent;