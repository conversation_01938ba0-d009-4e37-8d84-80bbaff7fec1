export const priGranTypeMap: Record<any, string> = {
  THIN: 'flow_operation_privilege_escalation',
  FAT: 'flow_access_req',
  highRisk: 'flow_high_risk_request',
  desensitizedResource: 'flow_sensitive_request',
  exportTask: 'flow_export_request',
  importTask: 'flow_import_request'
}

export type PriGranType = keyof typeof priGranTypeMap;

export const applyStatusMap: Record<any, string> = {
  pending: "data_chg:apply_status.pending_review",
  already: "data_chg:apply_status.completed",
  power: "data_chg:apply_status.pending_auth",
  rejected: "data_chg:apply_status.rejected",
  pass: "data_chg:apply_status.approved",
  refuse: "data_chg:apply_status.rejected_by_approval",
  stop: "data_chg:apply_status.terminated",
  withdraw: "data_chg:apply_status.withdrawn",
}

//不需要落权
export const NO_NEED_TO_LAND_TYPES = ['desensitizedResource', 'highRisk', 'exportTask', 'importTask'];

//需要 附件上传
export const NEED_TO_UPLOAD_FILE = ['exportTask'];

export const APP_EFFECTIVE_STATES = {
  EXPIRED: 'flow_expired',
  PENDING: 'flow_pending_activation',
  ACTIVE: 'flow_active',
}

export type EffectStatusType = 'flow_pending_activation' | 'flow_active' | 'flow_expired'

export type MyApplyTabKey = 'pending' | 'pass' | 'power' | 'already' | 'withdraw';
export type MyApplyTabKeyType = keyof typeof MY_APPLY_TABS;

export const MY_APPLY_TABS: Record<any, string> = {
  pending: 'flow_pending_review',
  pass: 'flow_approved',
  power: 'flow_pending_assignment',
  already: 'flow_completed',
  withdraw: 'flow_withdrawn',
  all: 'flow_all',
}

export const APPLY_INPUT_PLACEHOLD_EUM: Record<any, string> = {
  pending: 'flow_search_title_current_approver',
  pass: 'flow_search_title',
  power: 'flow_search_title_assigned_person',
  already: 'flow_search_title',
  withdraw: 'flow_search_title',
  all: 'flow_search_title_approver_assigned_person',
}

export type MyApprovalTabKey = 'stayApprove' | 'yetApprove' | 'power' | 'finish' | 'withdraw' | 'all';
export type MyApprovalTabKeyType = keyof typeof MY_APPROVAL_TABS;

export const MY_APPROVAL_TABS: Record<any, string> = {
  stayApprove: 'flow_pending_approval',
  yetApprove: 'flow_approved',
  power: 'flow_pending_assignment',
  finish: 'flow_completed',
  withdraw: 'flow_withdrawn',
  all: 'flow_all',
}

export const WORKORDER_INPUT_PLACEHOLD_EUM: Record<any, string> = {
  stayApprove: 'flow_search_title_applicant_approver',
  yetApprove: 'flow_search_title_applicant',
  power: 'flow_search_title_applicant_assigned_person',
  finish: 'flow_search_title_applicant',
  withdraw: 'flow_search_title_applicant',
  all: 'flow_search_title_applicant_approver'
}

export type HIGH_RISK_BASIC_FIELD = keyof typeof MY_APPROVAL_TABS;

export const HIGH_RISK_BASIC_INFO: Record<any, string> = {
  applyUserName: 'flow_applicant',
  applyUserDept: 'flow_department',
  title: 'flow_title',
  createTime: 'flow_application_time',
  dataSourceType: 'flow_application_type',
  connectionName: 'flow_data_source_connection',
  nodePathWithTypeList: 'flow_database_ele',
  operationList: 'flow_operation_type',
  remark: 'flow_application_reason',
  time: 'flow_effective_time',
  flowType: 'flow_application_type',
  executeText: 'flow_execute_sql',
  exportNumLimit: 'flow_export_row_count',
}

export const IMPORT_TASK_BASIC_INFO: Record<any, string> = {
  applyUserName: 'flow_applicant',
  applyUserDept: 'flow_department',
  createTime: 'flow_application_time',
  flowType: 'flow_application_type',
  nodePathWithTypeList: 'flow_import_target_table',
  importFileSize: 'flow_import_file_size',
  fileName: 'flow_import_file',
}

//申请单 基本信息
export const BASIC_ODD_FIELDS = [
  'time',
  'title',
  'remark'
]

export const HIGH_RISK_ODD_FIELDS = [
  'applyUserName',
  'applyUserDept',
  'title',
  'createTime',
  'flowType',
  'connectionName',
  'nodePathWithTypeList',
  'operationList',
  'remark',
  'time',
]

export const EXPORT_TASK_ODD_FIELDS = [
  'applyUserName',
  'applyUserDept',
  'title',
  'createTime',
  'flowType',
  'connectionName',
  'nodePathWithTypeList',
  'executeText',
  'exportNumLimit',
  'remark'
]

export const DESENSITIZED_RESOURCE_ODD_FIELDS = [
  'applyUserName',
  'applyUserDept',
  'title',
  'createTime',
  'flowType',
  'connectionName',
  'nodePathWithTypeList',
  'remark',
  'time',
]

export const IMPORT_TASK_ODD_FIELDS = [
  'applyUserName',
  'applyUserDept',
  'createTime',
  'flowType',
  'nodePathWithTypeList',
  'importFileSize',
  'fileName',
]
