.mt27 {
	margin-top: 27px;
}

.mb27 {
	margin-bottom: 27px;
}

.myApplyRequestDetailWrap {
	.pb60 {
		.content {
			padding: 10px 10px 60px 10px !important;
		}
	}
	.myApplyRequestDetailContent {
		height: calc(100vh  - 30px - 48px);
		padding: 10px 32px;

		.headers {
			display: flex;
			// padding: 10px;
			justify-content: space-between;
			align-items: center;
		}

		.breadcrumb {
			// padding: 10px;
			font-size: 16px;
		}

		.content {
			width: 100%;
			min-height: 300px;
			display: flex;
			flex-direction: column;
			padding: 10px;

			.oddBgText {
				display: inline-block;
				background: #F0F1F5;
				padding: 5px 10px;
				// width: 120px;
				font-size: 12px;
				border-radius: 4px;
			}
		}

		:global {
			.ant-breadcrumb-separator {
				color: #D8D8D8;
			}
		}
	}
	.reapplyFooter {
		position: fixed;
		bottom: 0;
		padding: 10px 40px;
		margin-left: -20px;
		width: 100%;
		z-index: 100;
		text-align: right;
		border-top: 1px solid rgba(149, 156, 182, 0.15);
		background-color: #fff;
	}
}

.borderShow {
	box-shadow: 0px 8px 15px 0px rgba(149, 156, 182, 0.15), inset -2px 0px 0px 0px rgba(255, 255, 255, 0.07);
}

.detailCard {
	:global {
		.ant-card-actions>li {
			text-align: left;
			margin-left: 25px;

			.ant-btn {
				margin-right: 23px;
			}
		}
	}
}

.tablePage {
	:global {
		.ant-table-thead>tr>th {
			// color: $sub-text-color;
			background-color: #F7F9FC;
			height: 48px;
		}

		.ant-table-row {
			height: 48px;
		}

		.ant-table-expanded-row .ant-table-wrapper {
			padding: 0 0 0 12px;
		}
	}

	.actionsBtn {
		span {
			cursor: pointer;
			color: #3262FF;
			text-decoration: none;
			margin: 5px
		}
	}
}

.permissionTooltip {
	max-width: none;
	max-height: 200px;
	overflow-y: scroll;
}