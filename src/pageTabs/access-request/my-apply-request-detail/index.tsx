// 访问申请-我的申请-申请单
import React, { useEffect } from 'react';
import { useRequest, useDispatch, useSelector } from 'src/hook';
import { useParams, useLocation, useHistory } from 'react-router-dom';
import ErrorBoundary from 'antd/lib/alert/ErrorBoundary';
import { Button, Descriptions, Spin, message, Modal } from 'antd';
import { MENU_FLOW } from 'src/constants';
import { SimpleBreadcrumbs, LinkButton } from 'src/components';
import { ResourceBidPage } from './resource-bid';
import { OrderFlowPage } from '../common/order-flow';
import { ApplicationsInfoPag } from '../common/application-info';
import {
  checkFlowInfo,
  getFlowRemarkInfo,
  getFlowStatus,
  getInventoryData,
  getNewFlowProcess,
  getPowerRemarkInfo,
  getWithdrawRemarkInfo,
  reApplyFlowOrder
} from 'src/api';
import { openFlowForm } from 'src/pageTabs/flowPages';
import { setVisibleApplicationShop } from 'src/pageTabs/queryPage/queryPageSlice'
import { ApprovalRemarksPag } from '../common/approval-remarks';
import { WeightRemarksPag } from '../common/weighting-info';
import { NEED_TO_UPLOAD_FILE, NO_NEED_TO_LAND_TYPES } from '../constants'
import { UploadFilePag } from '../common/upload-file';
import AppliedResourceList from '../AppliedResourceList'
import { useTranslation } from 'react-i18next';
import { setMineApplyPageState, setMineApplyDetailParams } from 'src/pageTabs/access-request/accessRequestSlice'
import classnames from 'classnames';
import styles from './index.module.scss';

export const MyApplyRequestDetailPage = () => {

  const history = useHistory();
  const dispatch = useDispatch();
  const { mineApplyDetailParams } = useSelector(state => state.accessRequest);
  const { id } = mineApplyDetailParams || {};
  const location = mineApplyDetailParams;
  const { t } = useTranslation();

  const handleRenderToApply = () => {
    dispatch(setMineApplyPageState('apply'))
    dispatch(setMineApplyDetailParams({curTab: mineApplyDetailParams?.curTab}))
  }

  const breadcrumbData = [
    { title: t(MENU_FLOW) },
    {
      title: <span className='breadcrumbLink' onClick={handleRenderToApply}>{t('flow_my_request')}</span>,
    },
    {
      separator: '/',
      title: t('flow_application'),
    },
  ];

  const {
    data: thinData = {},
    run: thinRun,
    loading: thinLoading,
  } = useRequest(getInventoryData, {
    manual: true,
  });

  //附件
  const { data: uploadFileData = [], run: runCheckFlowInfo } = useRequest(
    checkFlowInfo,
    {
      manual: true,
    }
  );

  //备注
  const { data: remarkData = [], run: getRemarkInfoRun } = useRequest(
    getFlowRemarkInfo,
    { manual: true }
  );

  //落权
  const { data: powerData = [], run: getPowerRun } = useRequest(
    getPowerRemarkInfo,
    {
      manual: true,
    }
  );
  //详情
  const { data: list = [], run: getFlowSteps } = useRequest(getNewFlowProcess, {
    manual: true,
  });

  //工单状态
  const { data: flowStatus = [], run: getFlowStatusRun } = useRequest(
    getFlowStatus,
    {
      manual: true,
    }
  );

  //撤回备注
  const { data: retractRemarks = [], run: runGetRetractRemarks } = useRequest(
    getWithdrawRemarkInfo,
    {
      manual: true,
    }
  );
 
  const handleRenderToApplication = () => {
    dispatch(setMineApplyPageState('application'))
    dispatch(setMineApplyDetailParams({}))
  }
  const viewReapplyOrder = (orderType: string) => {
    
    switch (orderType) {
      case 'FAT':
        handleRenderToApplication()
        break;
      case 'THIN':
        dispatch(setVisibleApplicationShop(true))
        history.push(`/system_data_operate`);
        break;
      default:
        handleRenderToApply()
        break;
    }
  }
  //再次申请
  const { run: runReApplyFlowOrder } = useRequest(reApplyFlowOrder, {
    manual: true,
    onSuccess: () => {
      //消息通知进入根据type判断类型
      message.success(<span>{t('flow_added_to_application')},<LinkButton onClick={() => {viewReapplyOrder(location?.priGranType)}}>{t('flow_view')}</LinkButton></span>);
    },
  });

  useEffect(() => {
    if (location?.mainUUID) {
      thinRun({
        flowMainUUID: location?.mainUUID,
        connectionIds: location?.connectionIds,
        canOperation: location?.canOperation ? location?.canOperation : null,
      });
      getFlowSteps(location?.mainUUID);
      getFlowStatusRun(location?.mainUUID);
      getRemarkInfoRun(location?.mainUUID);
      getPowerRun(location?.mainUUID);
      runCheckFlowInfo(location?.applyId);
    }

  }, [location]);

  useEffect(() => {
    if (flowStatus?.approvedResult === 'withdraw' && location?.mainUUID) {
      runGetRetractRemarks(location?.mainUUID)
    }

  }, [flowStatus, location])
  // 再次申请
  const reApplyMethods = async (record: any) => {
    if (record?.priGranType === 'highRisk') {
      const orderDetail = await getInventoryData({
        flowMainUUID: record?.mainUUID,
        connectionIds: record?.connectionIds,
        canOperation: record?.canOperation ? record?.canOperation : null,
      });

      dispatch(
        openFlowForm({
          type: 'highRisk',
          fields: {
            elements: [
              {
                connectionId: record?.connectionIds?.[0],
                connectionType: orderDetail?.dataSourceType,
                nodeName: orderDetail?.nodePathName,
                nodePath: orderDetail?.nodePathList?.[0] || '',
                nodePathWithType: orderDetail?.nodePathWithTypeList?.[0] || '',
                nodeType: orderDetail?.nodeType,
              },
            ],
            operationList: orderDetail?.operationList || [],
            callbackUrl: `/mine_apply`
          },
        })
      );
    } else if (record?.priGranType === 'desensitizedResource') {
      const orderDetail = await getInventoryData({
        flowMainUUID: record?.mainUUID,
        connectionIds: record?.connectionIds,
        canOperation: record?.canOperation ? record?.canOperation : null,
      });
      //无权限的脱敏资源
      const noPermissionSensitiveResourceElements = orderDetail?.nodePathWithTypeList?.map((key: any) => ({
        label: '',
        value: key
      }));
      dispatch(
        openFlowForm({
          type: 'desensitizedResource',
          fields: {
            //@ts-ignore
            elements: noPermissionSensitiveResourceElements,
            //@ts-ignore
            connectionId: record?.connectionIds?.[0],
            connectionType: orderDetail?.dataSourceType,
            nodeType: orderDetail?.nodeType,
            callbackUrl: `/mine_apply`
          },
        })
      );
    } else {
      await runReApplyFlowOrder(record?.applyId);
    }
  };

  const reapplyOrder = () => {
    Modal.confirm({
      title: t('flow_confirm_reapply'),
      centered: true,
      onOk: () => {
        reApplyMethods(location)
      },
    });
  }
 
  return (
    <div className={styles.myApplyRequestDetailWrap}>
      <div 
        className={
          classnames(styles.myApplyRequestDetailContent, {[styles.pb60]: location?.applyType === 'RE_APPLY'})
        }
      >
        <SimpleBreadcrumbs items={breadcrumbData} />
        <Spin spinning={thinLoading}>
          <div className={styles.content}>
            <Descriptions>
              <Descriptions.Item label={t('flow_application_number')}>
                <span className={styles.oddBgText}>{id}</span>
              </Descriptions.Item>
            </Descriptions>
            {!(
              NO_NEED_TO_LAND_TYPES.includes(location?.priGranType) ||
              NO_NEED_TO_LAND_TYPES.includes(thinData?.flowType)
            ) && (
                <ErrorBoundary>
                  {/* 申请资源 */}
                  {
                    (location?.priGranType === 'FAT' || thinData?.flowType === 'FAT') ?
                      <AppliedResourceList
                        flowMainUUID={id}
                        data={thinData} 
                        flowUUID={
                          location?.originType === 'message'
                          ? location?.flowUUID
                          : location?.mainUUID
                        }
                      />
                      : <ResourceBidPage
                        data={thinData}
                        priGranType={location?.priGranType}
                      />
                  }
                </ErrorBoundary>
              )}
            <ErrorBoundary>
              {/* 申请信息 */}

              <ApplicationsInfoPag
                {...thinData}
                priGranType={location?.priGranType || thinData?.flowType}
                applyId={location?.applyId}
              />

            </ErrorBoundary>
            <ErrorBoundary>
              {/* 工单流转 */}
              <OrderFlowPage dataList={list} flowStatus={flowStatus} />
            </ErrorBoundary>
            <ErrorBoundary>
              {/* 审批信息 */}
              <ApprovalRemarksPag remarkData={remarkData} />
            </ErrorBoundary>
            {!(
              NO_NEED_TO_LAND_TYPES.includes(location?.priGranType) ||
              NO_NEED_TO_LAND_TYPES.includes(thinData?.flowType)
            ) && (
                <ErrorBoundary>
                  {/* 撤回信息/落权信息 */}
                  <WeightRemarksPag remarkData={powerData} />
                </ErrorBoundary>
              )}
            {flowStatus?.approvedResult === 'withdraw' && (
              <ErrorBoundary>
                {/* 撤回信息/落权信息 */}
                <WeightRemarksPag remarkData={retractRemarks} withdrawalState />
              </ErrorBoundary>
            )}
            {(
              NEED_TO_UPLOAD_FILE.includes(location?.priGranType) ||
              NEED_TO_UPLOAD_FILE.includes(thinData?.flowType)
            ) && (flowStatus.approvedResult === "pass") && (
                <ErrorBoundary>
                  {/* 附件上传 */}
                  <UploadFilePag uploadFileData={uploadFileData} />
                </ErrorBoundary>
              )
            }
          </div>
        </Spin>
      </div>
      {
        location?.applyType === 'RE_APPLY' &&
        <div className={styles.reapplyFooter}>
          <Button
            type="primary"
            onClick={() => reapplyOrder()}
          >
            {t('flow_reapply')}
          </Button>
        </div>
      }
    </div>
  );
};
