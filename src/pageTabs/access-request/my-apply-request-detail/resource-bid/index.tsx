// 访问申请-我的申请-申请单（申请资源：仅展示）
import React, { FC, useEffect, useState } from 'react'
import classNames from 'classnames';
import { Button, Card, Table, Tooltip } from 'antd'
import { PlusOutlined } from '@ant-design/icons'
import { getPermissionList, getPermissionTemplate, getToolTemplateInfo } from 'src/api'
import { columnsDetail, detailExpamdedRow, detailFineGritExpamdedRow } from '../../common/columns'
import styles from '../index.module.scss'
import { useRequest, useDispatch } from 'src/hook'
import { setStr } from 'src/util/connectionManage';
import { useTranslation } from 'react-i18next';
import { setMineApplyPageState, setMineApplyDetailParams } from 'src/pageTabs/access-request/accessRequestSlice'

interface ListProps {
	data: any,
	priGranType: string
 }

export const ResourceBidPage: FC<ListProps> = (props) => {
	const dispatch = useDispatch()
	const {data, priGranType} = props
  const { t } = useTranslation();
	const { dataSourceType, nodeType } = data?.dataList?.[0]?.businessDataListNoChild?.[0] || {}
	const [rowId, setRowId] = useState<any>([])
	const [templateEnum, setTemplateEnum] = useState<any[]>([])
  const [permissionsOptsSet, setPermissionsOptsSet] = useState<any>();

	// 查询工具权限模板
	useEffect(()=>{
		if(dataSourceType && nodeType){
			queryToolsAuthTemplate(dataSourceType, nodeType)
		}
	},[dataSourceType, nodeType])
	const queryToolsAuthTemplate = (dataSourceType: string, nodeType: string) =>{
		getToolTemplateInfo(dataSourceType, nodeType).then((res: any)=>{
			setTemplateEnum(res)
		}).catch((err: any)=>{
			console.error('工具权限模板查询失败', err)
		})
	}

  // 查询不同数据源下对应的所有权限信息
  const { run: getPermissionTemplateEnum } = useRequest(
    getPermissionTemplate,
    { manual: true },
  )

  // 获取权限等级列表
  const { run: getPermissionListEnum } = useRequest(
    getPermissionList,
    { manual: true,
      onSuccess: (res: any) => {
        getPermissionTemplateEnum(dataSourceType).then((templateRes: any) => {
        let options: { [key: string]: any[] } = {};
        res?.forEach((item: any) => {
          if (item?.id) {
            let templateOperationsSetTmp = item?.templateOperationsSet;
            options[item?.id] = templateOperationsSetTmp?.flatMap((optionsItem: any) => {
              const matchedTemplate = templateRes?.find((templateItem: any) => templateItem?.objectType === optionsItem?.objectType);
              if (matchedTemplate) {
                return optionsItem?.operations?.map((operation: any) => {
                  const matchedOperation = matchedTemplate?.operations?.find((templateOperation: any) => templateOperation?.operation === operation);
                  if (matchedOperation) {
                    return `${operation}(${matchedOperation?.operationName})`;
                  }
                  return operation;
                });
              } else {
                return [];
              }
            });
          }
        });
        setPermissionsOptsSet(options)
        })
      }
    },
  )

  useEffect(()=>{
		if(dataSourceType){
			getPermissionListEnum(dataSourceType)
		}
	},[dataSourceType, getPermissionListEnum])

	const expandedRowRender = (record: any) => {
			const newColumns = (priGranType === 'THIN' ? detailFineGritExpamdedRow() : detailExpamdedRow())?.map((item: any)=>{
				// 工具权限申请
				if(item?.dataIndex === 'toolsAuth') {
					return {
						...item,
						render: (val: any, record: any) => {
							let resultName: any = []
							record?.templateOperationsSet?.forEach((item: any) => {
								const matchedObejct = templateEnum?.find((res:any) => res?.objectType === item?.objectType);
								if (matchedObejct) {
									item?.operations?.forEach((val: any)=>{
										const matchedItem = matchedObejct?.operations?.find((res:any) => res?.operation === val);
										if(matchedItem?.operationName){
											resultName.push(matchedItem?.operationName)
										}
									})
								}
							});
							return resultName?.join(',') || '-'
						},
					}
				}

        // 权限申请 字段
        if(item?.dataIndex === 'permissionTypeName') {
					return {
						...item,
						render: (txt: string, record: any) => {
              const isShowPermissionTooltip: boolean = permissionsOptsSet && Object.keys(permissionsOptsSet).includes(record?.permissionType?.toString())
              let str = ''
              if (isShowPermissionTooltip) {
                str = setStr(permissionsOptsSet[record?.permissionType?.toString()])
              }
              return (
                <>
                  <Tooltip 
                    title={ isShowPermissionTooltip && (str || '-') }
                    overlayClassName={ isShowPermissionTooltip && !!str ? styles.permissionTooltip : ''}
                    overlayStyle={{ whiteSpace: 'pre-line' }}
                  >
                    <span>{txt} </span>
                  </Tooltip>
                </>
              )
            },
					}
				}
				return item
			})
		
		return <Table columns={newColumns} dataSource={record?.businessDataListNoChild} pagination={false} size="middle" />;
	}

	useEffect(() => {
		if (data?.dataList?.length) {
			const newArr: any[] = []
			data?.dataList?.forEach((item: any) => {
				newArr.push(item?.id)
			})
			setRowId(newArr)
		}
	},[data])

	const scrollX = columnsDetail()?.length * 100;

	const handleRenderToSearch = () => {
		dispatch(setMineApplyPageState('search'))
		dispatch(setMineApplyDetailParams({}))
	}

	return (
		<Card
			title={t('flow_request_resource')}
			className={classNames(styles.borderShow, 'mb20')}
			extra={
				<Button
					type="primary"
				>
					<span onClick={handleRenderToSearch}>
						<PlusOutlined /> 
						&nbsp;&nbsp;{t('flow_add_resource')}
					</span>
				</Button>
			}
		>
			<Table
				rowKey="id"
				className={styles.tablePage}
				expandable={{
					expandedRowRender: (record) => {
						return expandedRowRender(record)
					},
					defaultExpandAllRows: true,
					expandedRowKeys: rowId,
					onExpandedRowsChange: (e) => {
						setRowId(e)
					}
				}}
				
				columns={columnsDetail()}
				dataSource={data?.dataList}
				size="small"
				scroll={{ x: scrollX, y: 490 }}
			/>
		</Card>
	)
}
