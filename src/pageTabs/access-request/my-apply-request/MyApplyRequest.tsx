import React, { useEffect, useState } from 'react'
import { useDispatch, useRequest, useSelector } from 'src/hook'
import { useLocation } from 'react-router-dom'
import { getCartConnection } from 'src/api'
import { Badge, Button, Input, Tooltip } from 'antd'
import ErrorBoundary from 'antd/lib/alert/ErrorBoundary'
import { MENU_FLOW } from 'src/constants';
import { setGuideSteps, setGuideVisible } from 'src/pageTabs/SceneGuideModal/guideSlice';
import { Iconfont, SimpleBreadcrumbs,RenderTourStepContent, STEP_OPTION } from 'src/components'
import { SearchOutlined } from '@ant-design/icons'
import { MyApplyTabsPage } from './my-apply-tabs'
import classnames from 'classnames'
import { setApplySearchValue } from '../accessRequestSlice'
import styles from './index.module.scss'
import _ from 'lodash'
import { APPLY_INPUT_PLACEHOLD_EUM } from '../constants'
import { useTranslation } from 'react-i18next'
import { setMineApplyPageState, setMineApplyDetailParams } from 'src/pageTabs/access-request/accessRequestSlice'


export const MyApplyRequest = () => {
  const { t } = useTranslation()
  const dispatch = useDispatch()
  const { guideUniqueIds } = useSelector(state => state.guide);

  const [searchValue, setSearchValue] = useState('')
  const [activeKey, setActiveKey] = useState('pending')

  const breadcrumbData = [
    { title: t(MENU_FLOW) },
    { title: t('flow_my_request') }
  ];

  const steps = [
  {
    target: '.guide-my-apply',
    content: RenderTourStepContent({
      title: t('flow_access_req'),
      detail: t('flow_click_access_request')
     }),
    ...STEP_OPTION,
  }
]

  //购物车数量
  const { data: totalSchema, refresh } = useRequest(getCartConnection, {
    formatResult(res) {
      return res?.total || 0
    }
  })

  useEffect(() => {
		
    if (guideUniqueIds?.length && guideUniqueIds.includes('MINE_APPLY')) {
     dispatch(setGuideSteps({steps, guideUniqueId: 'MINE_APPLY'}));
     dispatch(setGuideVisible(true));
    }
  },[guideUniqueIds])


  const onChangeValue = (str: string) => {
    setSearchValue(str)
  }

  const onSearch = _.debounce((e) => {
    dispatch(setApplySearchValue(e?.target?.value))
  }, 500)

  const handleInputChange = (e: { persist: () => void; target: { value: React.SetStateAction<string> } }) => {
    e.persist()
    setSearchValue(e?.target?.value)
    onSearch(e)
  }

  // 流程--我的申请-申请清单
  const handleRenderToSearch = () => {
    dispatch(setMineApplyPageState('search'))
    dispatch(setMineApplyDetailParams({}))
  }

  // 流程-我的申请-购物车
  const handleRenderToApplication = () => {
    dispatch(setMineApplyPageState('application'))
    dispatch(setMineApplyDetailParams({}))
  }

  return (
    <div className={styles.myApplyRequestWrap}>
      <div className={styles.headers}>
        <SimpleBreadcrumbs items={breadcrumbData} />
        <div className={styles.flexRow}>
          <Input
            prefix={<SearchOutlined className="site-form-item-icon" />}
            placeholder={t(APPLY_INPUT_PLACEHOLD_EUM[activeKey])}
            value={searchValue}
            allowClear
            onChange={handleInputChange}
          />
          <Button
            className={classnames(styles.textAlign, styles.ml10, styles.mr8,'guide-my-apply')}
            style={{ textAlign: "right" }}
            type="primary"
          >
            <span onClick={handleRenderToSearch}>{t('flow_access_req')}</span>
          </Button>

          <Badge count={totalSchema} showZero>
            <Tooltip title={t('flow_resource_count')} placement='bottomRight'>
              <Button
                type="primary"
                className={styles.shoppBtn}
              >
                <span onClick={handleRenderToApplication}>
                  <Iconfont type='icon-gouwuche' style={{ color: '#fff', fontSize: '16px' }} />
                </span>
              </Button>
            </Tooltip>
          </Badge>
        </div>
      </div>
      <div className={styles.content}>
        <ErrorBoundary>
          <MyApplyTabsPage onChangeValue={onChangeValue} refreshShoppingCart={refresh} callbackActiveKey={setActiveKey} />
        </ErrorBoundary>
      </div>
    </div>
  )
}
