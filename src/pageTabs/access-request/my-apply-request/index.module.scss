@import 'src/styles/variables';

.ml10 {
	margin-left: 10px;
}

.mr8 {
	margin-right: 8px;
}

.textAlignCenter {
	text-align: center;
}

.flexRow {
	display: flex;
}

.subtextColor {
	color: #868FA3;
}

.card {
	:global {
		.ant-tabs-nav {
			margin: 0;
		}

		.ant-tabs-tab {
			background-color: #f0f1f5 !important;
		}

		.ant-tabs-tab-active {
			background-color: #fff !important;
			border-radius: 4px !important;
		}

		.ant-tabs-nav-list {
			height: 32px;
			border-radius: 4px;
			padding: 3px !important;
			background-color: #f0f1f5 !important;
			// margin-bottom: 15px;
		}

		.ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn {
			color: #0f244c;
		}

		.ant-tabs-tab:hover {
			color: #0f244c;
		}

		.ant-tabs-tab-btn:active {
			color: #0f244c;
		}

		.ant-tabs-tab-btn {
			color: #868FA3;
		}
	}
}

.myApplyRequestWrap {
	height: 100vh;
	padding: 0px;

	.headers {
		display: flex;
		// padding: 10px;
		justify-content: space-between;
		align-items: center;
	}

	.breadcrumb {
		// padding: 10px;
		font-size: 16px;
	}

	.content {
		min-height: 300px;
		display: flex;
		flex-direction: column;
		width: 100%;

	}

	:global {
		.ant-breadcrumb-separator {
			color: #D8D8D8;
		}
	}
}

.tabsPageWrap {
	.tabsHeader {
		display: flex;
		align-items: center;
		justify-content: space-between;

		.tabsLeft,
		.tabsRight {
			display: flex;
			align-items: center;
		}
	}
}

.tablePage {
	margin-top: 10px;

	.notInEffect {
		opacity: 0.5;
	}

	:global {
		.ant-table-thead>tr>th {
			// color: $sub-text-color;
			background-color: #F7F9FC;
			height: 48px;
		}

		.ant-table-row {
			height: 48px;
		}

		// .ant-table-expanded-row .ant-table-wrapper{
		// 	padding: 0 0 0 0px;
		// }
	}

	.actionsBtn {
		span {
			cursor: pointer;
			color: #3262FF;
			text-decoration: none;
			margin: 5px
		}
	}
}

.iconStyle {
	color: #D8D8D8;
	font-size: 20px;
	margin: 0 6px;
	cursor: pointer;
}

.underLine {
	text-decoration: underline !important;
}

.optionsBtnText {
	cursor: pointer;

	:first-child {
		color: #3262FF;
		margin-right: 14px;
	}

	.deleteBtn {
		color: #EA0000;
	}
}

.optionsRefuseBtn {
	cursor: no-drop;

	:first-child {
		color: #ccc;
		margin-right: 14px;
	}
}

.shoppBtn {
	padding: 0;
	width: 32px;
}

.statusDot {
	display: inline-block;
	width: 8px;
	height: 8px;
	border-radius: 50%;
	margin-right: 8px;
}

.pendingBack {
	background: #0256FF;
}

.alreadyBack {
	background: #1AD42D;
}

.powerBack {
	background: #D6D7DB;
}

.rejectBack {
	background: #EA3223;
}