import React, { FC, useEffect } from 'react'
import ErrorBoundary from 'antd/lib/alert/ErrorBoundary'
import { Layout } from 'antd'
import classNames from 'classnames'
import { MyApplyRequest } from './MyApplyRequest'
import { MyApplyRequestDetailPage } from '../my-apply-request-detail'
import ApplicationListPage from '../application-list';
import VisitRequestOperation from '../visit-request-operation';
import { useSelector, useDispatch } from 'src/hook';
import { setMineApplyPageState, setMineApplyDetailParams } from '../accessRequestSlice'

const { Content } = Layout

interface ListProps { }


export const MyApplyRequestPage: FC<ListProps> = ({ ...rest }) => {
  const dispatch = useDispatch()
  const { mineApplyPageState } = useSelector((state) => state.accessRequest); 

  useEffect(()=>{
    return () => {
      // 恢复 初始状态
      dispatch(setMineApplyPageState('')) 
      dispatch(setMineApplyDetailParams({}))
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  const contentRender = () => {
    switch (mineApplyPageState) {
      case 'detail':
        return <MyApplyRequestDetailPage />;  // 申请详情 
      case 'search':
        return <VisitRequestOperation />;     // 申请清单
      case 'application':
        return <ApplicationListPage />;       // 购物车
      default:
        return <MyApplyRequest />             // 我的申请
    }
  }

  return (
    <Layout className="cq-container">
      <Layout className="cq-main">
        <Content
          className={classNames('cq-content')}
        >
          <ErrorBoundary>
            {
              contentRender()
            }
          </ErrorBoundary>
        </Content>
      </Layout>
    </Layout>
  )
}
