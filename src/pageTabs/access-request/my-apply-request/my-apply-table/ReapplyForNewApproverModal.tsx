
import React, { useEffect } from "react";
import useRequest from "@ahooksjs/use-request";
import { Form } from 'antd';
import { UIModal } from "src/components";
import {
  queryOrderConnectionInfo
} from 'src/api'
import { FormItemMultiConectionApprover } from 'src/pageTabs/flowPages/flowFormItems'
import { useTranslation } from "react-i18next";

export default ({
  flowType,
  applyId,
  loading,
  onCancel,
  onSubmit
}: {
  flowType: string
  applyId: string
  loading: boolean
  onCancel: () => void;
  onSubmit: (params: { flowId: string; connAndAssigneeMapList: any[] }) => void;
}) => {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  //调用接口拿到连接信息
  const { run: runQueryOrderConnectionInfoFetch } = useRequest(queryOrderConnectionInfo, {
    manual: true,
    formatResult: (res) =>{
     return res?.map( (i: any) => ({connectionId: i.connectionId, nodeName: i?.connectionName, dataSourceType: i?.dataSourceName}))
    },
    onSuccess: (res: any) => {
      form.setFieldsValue({
        connAndAssigneeMapList: res || []
      })
    },
    onError: () => {
      onCancel();
    }
  });

  useEffect(() => {
    if (applyId) {
      runQueryOrderConnectionInfoFetch(applyId);
    }
  }, [applyId]);

  const onOk = () => {
    form.validateFields().then((values) => {
      onSubmit({
        flowId: applyId,
        connAndAssigneeMapList: values?.connAndAssigneeMapList
      })
    })
  }
  return (
    <UIModal
      title={t('flow_select_new_approver')}
      visible={true}
      okButtonProps={{ loading }}
      onCancel={onCancel}
      width={500}
      onOk={onOk}
    >
      <Form form={form} labelCol={{span: 6}}>
        <FormItemMultiConectionApprover
          flowType={flowType}
          isRender={true}
          isMultiConnection={true}
          hiddenMultiConnectionTitle={true}
        />
      </Form>
    </UIModal>
  )
}