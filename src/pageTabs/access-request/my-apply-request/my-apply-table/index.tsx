import React, { FC, useCallback, useEffect, useMemo, useState } from 'react';
import * as _ from 'lodash';
import { Table, message, Tooltip } from 'antd';
import { useHistory } from 'react-router-dom';
import { useRequest, useSelector, useDispatch } from 'src/hook';
import { BtnWithConfirmModal } from 'src/components';
import { APP_EFFECTIVE_STATES, MyApplyTabKeyType, NO_NEED_TO_LAND_TYPES, applyStatusMap } from '../../constants';
import { columnsRequest, columnsRequestExpandRow } from '../../common/columns';
import {
  getMyApplications,
  getUrgeReminders,
  myApplyChildList,
  withdrawOrder,
  getInventoryData,
  reopenFlowOrder,
  reApplyFlowOrder,
  deleteOrder,
  getMyApplicationsPending,
  getMyApplicationsPower,
  getMyApplicationsAll,
  needReSpecifyAssignee,
} from 'src/api';
import { setAddToCartFlag } from 'src/pageTabs/queryPage/queryPageSlice'
import { openFlowForm } from 'src/pageTabs/flowPages';
import { getEffectiveStatus } from '../../utils';
import styles from '../index.module.scss';
import classNames from 'classnames';
import ReapplyForNewApproverModal from './ReapplyForNewApproverModal';
import { setMineApplyPageState, setMineApplyDetailParams } from 'src/pageTabs/access-request/accessRequestSlice'
import { t } from 'i18next';

export interface IProps {
  activeKey: any;
  refreshShoppingCart: () => void;
}

const MyApplyTablePage: FC<IProps> = ({ ...props }) => {
  const { activeKey, refreshShoppingCart } = props;

  const defaultSearchParams = {
    currentPage: 1,
    pageSize: 10,
    sort: 'desc',
  }

  const dispatch = useDispatch();
  const history = useHistory();
  const { addToCartFlag } = useSelector((state) => state.queryPage);
  const { applySearchValue, mineApplyDetailParams } = useSelector((state) => state.accessRequest);
  const userId = useSelector((state) => state.login.userInfo.userId);
  const [allSearchParams, setAllSearchParams] = useState<any>(defaultSearchParams)
  const [expandedRowKeys, setExpandedRowKeys] = useState<string[]>([]);
  const [expandChildData, setExpandChildData] = useState<any>({}); // 展开子表数据

  //重新申请-数据 选择新的审批人
  const [reapplyForNewApproverRecord, setReapplyForNewApproverRecord] = useState<any>(null);

  // api
  const apiEum: any = {
    pending: getMyApplicationsPending,
    pass: getMyApplications,
    power: getMyApplicationsPower,
    already: getMyApplications,
    withdraw: getMyApplications,
    all: getMyApplicationsAll,
  }

  // operationRender
  const opeEum: any = {
    pending: 'pending',
    pass: 'pass',
    power: 'power',
    finish: 'already',
    withdraw: 'withdraw',
    refuse: 'pass',
  }

  // 我的申请列表
  const { data, loading, refresh, run } = useRequest(apiEum[activeKey], {
    manual: true,
    debounceInterval: 200,
    formatResult: (data) => {
      const list = data.myApplyResponseVoList;
      const total = data.totalElements;
      return { list, total };
    },
  });

  // 格式化参数并run
  const formatParamsAndRun = () => {
    let params: any = {
      pageSize: allSearchParams?.pageSize,
      currentPage: allSearchParams?.currentPage,
      priGranType: allSearchParams?.priGranType || undefined,
    }
    switch (activeKey) {
      case 'pending':
      case 'power':
        params.sort = allSearchParams?.sort;
        params.keyWord = allSearchParams?.applySearchValue;
        break
      case 'all':
        params.sort = allSearchParams?.sort;
        params.keyWord = allSearchParams?.applySearchValue;
        params.tab = allSearchParams?.tab || undefined;
        break
      case 'already':
        params = {
          ...params,
          userId,
          title: allSearchParams?.applySearchValue,
          timer: allSearchParams?.sort,
          applyStatus: allSearchParams?.applyStatus || undefined,
          powerStatus: 'already'
        }
        break
      default:
        params = {
          ...params,
          userId,
          title:  allSearchParams?.applySearchValue,
          timer: allSearchParams?.sort,
          applyStatus: activeKey,
        }
        break
    }
    run(params);
  }

  useEffect(() => {
    setAllSearchParams({
      ...defaultSearchParams,
      priGranType: mineApplyDetailParams?.priGranType || undefined,
      tab: mineApplyDetailParams?.flowApplyStatus || undefined,
      applySearchValue
    })
  }, [JSON.stringify(mineApplyDetailParams),applySearchValue, activeKey])

  // 根据state变化run
  useEffect(() => {
    if (userId) {
      formatParamsAndRun()
    }
  }, [allSearchParams]);

  // 催一下
  const { run: urgeRun } = useRequest(getUrgeReminders, {
    manual: true,
    throttleInterval: 800,
    onSuccess() {
      message.success(t('flow_reminder_success'));
      refresh();
    },
  });

  //撤回申请
  const { run: runWithDrawApplication } = useRequest(withdrawOrder, {
    manual: true,
    onSuccess: () => {
      message.success(t('flow_withdrawal_success'));
      setAllSearchParams({
        ...allSearchParams,
        currentPage: 1
      })
    },
  });
  //再次申请
  const { run: runReApplyFlowOrder } = useRequest(reApplyFlowOrder, {
    manual: true,
  });

  //重新申请
  const { loading: reapplyLoading, run: runReopenFlowOrder } = useRequest(reopenFlowOrder, {
    manual: true,
    onSuccess: () => {
      setReapplyForNewApproverRecord(null)
      message.success(t('flow_reapplication_success'));
      refresh();
    },
    onError: () => {
      //判断是否节点发生改变
      message.error(t('flow_reapplication_failed'));
    },
  });

  //删除
  const { run: runDeleteFlowOrder } = useRequest(deleteOrder, {
    manual: true,
    onSuccess: () => {
      message.success(t('flow_deletion_success'));
      refresh();
    },
  });

  // 再次申请
  const reApplyMethods =  async(record: any, thinCartNumber: number) => {
    if (record?.priGranType === 'highRisk') {
      const orderDetail = await getInventoryData({
        flowMainUUID: record?.mainUUID,
        connectionIds: record?.connectionIds,
        canOperation: record?.canOperation ? record?.canOperation : null,
      });

      dispatch(
        openFlowForm({
          type: 'highRisk',
          fields: {
            elements: [
              {
                connectionId: record?.connectionIds?.[0],
                connectionType: orderDetail?.dataSourceType,
                nodeName: orderDetail?.nodePathName,
                nodePath: orderDetail?.nodePathList?.[0] || '',
                nodePathWithType: orderDetail?.nodePathWithTypeList?.[0] || '',
                nodeType: orderDetail?.nodeType,
              },
            ],
            operationList: orderDetail?.operationList || [],
          },
        })
      );
    } else if (record?.priGranType === 'desensitizedResource') {
      const orderDetail = await getInventoryData({
        flowMainUUID: record?.mainUUID,
        connectionIds: record?.connectionIds,
        canOperation: record?.canOperation ? record?.canOperation : null,
      });
      //无权限的脱敏资源
      const noPermissionSensitiveResourceElements = orderDetail?.nodePathWithTypeList?.map((key: any) => ({
        label: '',
        value: key
      }));
      dispatch(
        openFlowForm({
          type: 'desensitizedResource',
          fields: {
            //@ts-ignore
            elements: noPermissionSensitiveResourceElements,
            //@ts-ignore
            connectionId: record?.connectionIds?.[0],
            connectionType: orderDetail?.dataSourceType,
            nodeType: orderDetail?.nodeType,
          },
        })
      );
    } else {
      runReApplyFlowOrder(record?.applyId).then(async() => {
        const { priGranType } = record;
        if (priGranType === 'THIN') {
          message.success(t('flow_thin_reapply_success'));
          setTimeout(() => {
            const num = Number(thinCartNumber) + 1;
            dispatch(setAddToCartFlag(num))
            history.push('/system_data_operate');
          }, 600)
         
        }else {
          message.success(t('flow_added_to_application'));
          record?.priGranType === 'FAT' && refreshShoppingCart();
        }
      });
      
    }
  };

  // 操作栏render
  const renderTabAction = (record: any, activeKey: MyApplyTabKeyType, thinCartNum: number) => {
    // 全部视图根据tab字段转换成对应的activeKey
    const judgeKey = activeKey === 'all' ? opeEum[record?.tab] : activeKey

    switch (judgeKey) {
      case 'pending':
        return (
          <>
            <span
              onClick={() => {
                urgeRun({
                  flowId: Number(record?.applyId),
                  senderIds: record?.currentAssignee,
                });
              }}
            >
              {t('flow_give_reminder')}
            </span>
            <BtnWithConfirmModal
              title={t('flow_confirm_withdraw_record')}
              btnText={t('flow_withdraw')}
              onClick={(reason?: string) =>
                runWithDrawApplication({
                  flowId: record?.applyId,
                  withdrawRemark: reason,
                  isMyApply: true,
                })
              }
            />
          </>
        );
      case 'pass':
        return (
          <>
            {
              //已驳回 || 已通过但已失效 可以再次申请, '导出申请'无'再次申请'操作
              (record?.applyStatus === 'refuse' || record?.flowApplyStatus === 'refuse' ||
                getEffectiveStatus(record?.beginTime, record?.endTime) ===
                APP_EFFECTIVE_STATES.EXPIRED) && record.applyType !== 'exportTask' && (
                <BtnWithConfirmModal
                  title={t('flow_confirm_reapply')}
                  btnText={t('flow_reapply')}
                  hideReason={true}
                  onClick={() => reApplyMethods(record, thinCartNum)}
                />
              )
            }
            {/* 已审批tab,类型是敏感申请或高危申请,且生效状态是生效中,新增撤回操作 */}
            {
              getEffectiveStatus(record?.beginTime, record?.endTime) === APP_EFFECTIVE_STATES.ACTIVE && (
                <BtnWithConfirmModal
                  title={t('flow_confirm_withdraw_authorization')}
                  btnText={t('flow_withdraw')}
                  onClick={(reason?: string) =>
                    runWithDrawApplication({
                      flowId: record?.applyId,
                      withdrawRemark: reason,
                      isMyApply: true,
                    })
                  }
                />
              )
            }
          </>
        );
      case 'already':
        return (
          <>
            {/* ! 全部通过且未失效的 */}
            {!(getEffectiveStatus(record?.beginTime, record?.endTime) !==
              APP_EFFECTIVE_STATES.EXPIRED && record?.allChildApprove) && (
                <BtnWithConfirmModal
                  title={t('flow_confirm_reapply')}
                  btnText={t('flow_reapply')}
                  hideReason={true}
                  onClick={() => reApplyMethods(record, thinCartNum)}
                />
              )}
            {/* 已完成生效中的 */}
            {
              getEffectiveStatus(record?.beginTime, record?.endTime) === APP_EFFECTIVE_STATES.ACTIVE && (
                <BtnWithConfirmModal
                  title={t('flow_confirm_withdraw_authorization')}
                  btnText={t('flow_withdraw')}
                  onClick={(reason?: string) =>
                    runWithDrawApplication({
                      flowId: record?.applyId,
                      withdrawRemark: reason,
                      isMyApply: true,
                    })
                  }
                />
              )
            }
          </>
        );
      case 'withdraw':
        return (
          <>
            {
              // 未失效都能重新申请
              getEffectiveStatus(record?.beginTime, record?.endTime) !==
              APP_EFFECTIVE_STATES.EXPIRED && (
                <BtnWithConfirmModal
                  title={t('flow_confirm_reapplication')}
                  btnText={t('flow_reapplication')}
                  hideReason={true}
                  onClick={async () => {
                    const res: number = await needReSpecifyAssignee({ flowId: record?.applyId });
                    if (res === -1) {
                      runReopenFlowOrder({ flowId: record?.applyId })
                    } else {
                      message.warning(t('flow_approver_change'));
                      setReapplyForNewApproverRecord(record)
                    }
                  }}
                />
              )
            }
            <BtnWithConfirmModal
              title={t('flow_confirm_deletion')}
              btnText={t('flow_delete')}
              hideReason={true}
              onClick={() => runDeleteFlowOrder(Number(record?.applyId))}
            />
          </>
        );
      default:
        return '-';
    }
  };

  const handleToDetail = (val: string, record: any) => {
    // 设置我的申请-详情标识和参数 
    const params = {
      ...record,
      id: val,
      curTab: activeKey, //当前tab标识 方便后续回退到我的申请页面对应tab
    }
    dispatch(setMineApplyPageState('detail'))
    dispatch(setMineApplyDetailParams(params))
  }
  // 外层表格column -- 不同的api字段名称不一样，要处理
  const newColumns = useMemo(() => {
    let newColumns = columnsRequest({
      priGranTypeValue: allSearchParams?.priGranType
    })?.map((item: any) => {
      if (item?.dataIndex === 'uuid') {
        return {
          ...item,
          dataIndex: ['pending', 'power', 'all'].includes(activeKey) ? 'mainUUID' : 'uuid',
          render: (val: any, record: any) => (
            <span 
              className={`${styles.underLine} options`}
              onClick={() => handleToDetail(val, record)}
            >
              {val}
            </span>
          ),
        };
      }
      if (item?.dataIndex === 'actions') {
        return {
          ...item,
          fixed: 'right',
          width: 150,
          render: (_: any, record: any) => (
            <div className={styles.optionsBtnText}>
              {renderTabAction(record, activeKey, addToCartFlag)}
            </div>
          ),
        };
      }
      if (item?.dataIndex === 'applyUserName' && ['pending', 'power', 'all'].includes(activeKey)) {
        return {
          ...item,
          render: (applyUserName: string, record: any) => (
            <div>
              {
                applyUserName ? applyUserName
                  : record?.applyUserId ?
                    record?.applyUserId
                    : '-'
              }
            </div>
          ),
        }
      }
      if (item?.dataIndex === 'currentAssignee' && ['pending', 'power', 'all'].includes(activeKey)) {
        return {
          ...item,
          render: (currentAssignee: string, record: any) => (
            <div>
              {
                record?.currentAssigneeName ? <Tooltip title={record?.currentAssigneeName}>{`${record?.currentAssigneeName?.slice(0, 18)}...`}</Tooltip>
                  : currentAssignee ?
                    currentAssignee
                    : '-'
              }
            </div>
          ),
        }
      }
      if (item?.dataIndex === 'applyStatus' && ['pending', 'power'].includes(activeKey)) {
        return {
          ...item,
          dataIndex: 'flowApplyStatus',
          render: (_: any, { flowApplyStatus }: any) => {
            return <span>
              <span
                className={classNames(
                  styles.statusDot,
                  flowApplyStatus === 'pending' && styles.pendingBack,
                  (flowApplyStatus === 'pass' || flowApplyStatus === 'already') && styles.alreadyBack,
                  (flowApplyStatus === 'power' ||activeKey === 'power' ) && styles.powerBack,
                  flowApplyStatus === 'refuse' && styles.rejectBack,
                  flowApplyStatus === 'withdraw' && styles.rejectBack,
                )}
              ></span>
              {activeKey ==='power' ? t(applyStatusMap['power']) : t(applyStatusMap[flowApplyStatus])}
            </span>
          },
        }
      }
      if (item?.dataIndex === 'applyStatus' && activeKey === 'all') {
        return {
          ...item,
          dataIndex: 'flowApplyStatus',
          render: (_: any, { flowApplyStatus, tab }: any) => {
            return <span>
              <span
                className={classNames(
                  styles.statusDot,
                  flowApplyStatus === 'pending' && styles.pendingBack,
                  (flowApplyStatus === 'pass' || flowApplyStatus === 'already') && styles.alreadyBack,
                  (flowApplyStatus === 'power' || tab === 'power') && styles.powerBack,
                  flowApplyStatus === 'refuse' && styles.rejectBack,
                  flowApplyStatus === 'withdraw' && styles.rejectBack,
                )}
              ></span>
              {tab ==='power' ? t(applyStatusMap['power']) : t(applyStatusMap[flowApplyStatus])}
            </span>
          },
          filters: Object.keys(applyStatusMap).filter((key) => !['rejected', 'stop'].includes(key)).map((key) => ({
            text: <span>
              <span
                className={classNames(
                  styles.statusDot,
                  key === 'pending' && styles.pendingBack,
                  (key === 'pass' || key === 'already') && styles.alreadyBack,
                  key === 'power' && styles.powerBack,
                  key === 'refuse' && styles.rejectBack,
                  key === 'withdraw' && styles.rejectBack,
                )}
              ></span>
              {t(applyStatusMap[key])}
            </span>,
            value: key === 'already' ? 'finish' : key,
          })),
          filteredValue: allSearchParams?.tab || undefined,
          filterMultiple: true,
        }
      }
      return item;
    });
    if (['pass', 'already'].includes(activeKey)) {
      newColumns.splice(6, 1, {
        title: t('flow_effective_status'),
        dataIndex: 'effectiveState',
        width: 100,
        render: (_: string, record: any) => {
          return (record?.applyStatus === 'pass' && activeKey === 'pass') ||
            activeKey === 'already'
            ? t(getEffectiveStatus(record?.beginTime, record?.endTime, record.applyType))
            : '-';
        },
      });
    }
    if (activeKey === 'all') {
      newColumns.splice(7, 0, {
        title: t('flow_effective_status'),
        dataIndex: 'effectiveState',
        width: 100,
        render: (_: string, record: any) => {
         
          return (record?.flowApplyStatus === 'pass' && record?.tab !== 'power') || record?.flowApplyStatus === 'already'
            ? t(getEffectiveStatus(record?.beginTime, record?.endTime, record.applyType))
            : '-';
        },
      });
    }
    //样式
    return newColumns;
  }, [activeKey, t, allSearchParams?.priGranType, allSearchParams?.tab, addToCartFlag]);

  // 外层表格onchange
  const onChange = (pagination: any, filters: any, sorter: any) => {

    // 参数处理
    let params: any = {
      priGranType:  filters?.priGranType || undefined,
      pageSize: pagination?.pageSize,
      currentPage: pagination?.current,
      tab: filters?.flowApplyStatus || undefined,
      sort:  sorter?.order === 'ascend' ? 'asc' : 'desc'
    }

    setAllSearchParams({
      ...allSearchParams,
      ...params
    })
  };

  // onExpand 事件处理
  const onExpand = async (expanded: boolean, record: any) => {
    const rowKey = ['pending', 'power', 'all'].includes(activeKey) ? record?.mainUUID : record?.uuid;
    if (expanded) {
      try {
        // 调用接口判断是否可以展开
        const childData = await myApplyChildList(rowKey, '1');
        if (childData?.length > 0) {
          // 可以展开
          setExpandedRowKeys([...expandedRowKeys, rowKey]);
          setExpandChildData({...(expandChildData ?? {}), [rowKey]: childData});
        } else {
          // 不可以展开
          message.error(t('flow_no_sub_order'));
        }
      } catch (error) {
        message.error(t('expand_exception'));
      }
    } else {
      // 收起行
      setExpandedRowKeys(expandedRowKeys?.filter(key => key !== rowKey));
    }
  };

  const tableRowKey = ['pending', 'power', 'all'].includes(activeKey) ? 'mainUUID' : 'uuid'
  return (
    <>
      <Table
        rowKey={tableRowKey}
        loading={loading}
        className={styles.tablePage}
        rowClassName={(record, index) =>
          ((record?.applyStatus === 'pass' && activeKey === 'pass') ||
            activeKey === 'already' ||
            ['pass', 'finish'].includes(record?.tab)) &&
            getEffectiveStatus(record?.beginTime, record?.endTime) ===
            APP_EFFECTIVE_STATES.EXPIRED && record.applyType !== 'exportTask' // 导出申请表单不置灰
            ? styles.notInEffect
            : ''
        }
        expandable={{
          expandedRowRender: (record: any) =>
          <ExpandedRowContent
            record={record}
            activeKey={activeKey}
            urgeRun={urgeRun}
            data={expandChildData?.[record?.[tableRowKey]] ?? []}
          />,
          rowExpandable: (record) => !NO_NEED_TO_LAND_TYPES.includes(record?.priGranType),
          onExpand,
          expandedRowKeys,
        }}
        columns={newColumns}
        dataSource={data?.list}
        size="small"
        pagination={{
          current: allSearchParams?.currentPage || 1,
          pageSize: allSearchParams?.pageSize || 10,
          total: data?.total,
          showTotal: () => `${t('flow_total')} ${data?.total || 0} ${t('flow_items_lower')}`,
          size: 'default',
          showSizeChanger: true,
          showQuickJumper: true,
        }}
        onChange={onChange}
        scroll={{ x: "1200", y: `calc(100vh - 280px)` }}
      />
      {
        reapplyForNewApproverRecord &&
        <ReapplyForNewApproverModal
          loading={reapplyLoading}
          flowType={reapplyForNewApproverRecord?.priGranType}
          applyId={reapplyForNewApproverRecord?.applyId}
          onCancel={() => setReapplyForNewApproverRecord(null)}
          onSubmit={(params: any) => { runReopenFlowOrder(params) }}
        />
      }
    </>
  );
};

// 展开后的表格
export const ExpandedRowContent = ({
  record,
  activeKey,
  urgeRun,
  data
}: {
  record: any;
  activeKey: string;
  urgeRun: any;
  data: any[]
}) => {
  const [dataSource, setDataSource] = useState<any[]>([]);
  const flowUUID = ['pending', 'power', 'all'].includes(activeKey) ? record?.mainUUID : record?.uuid;
  const dispatch = useDispatch();
  const {
    data: childData,
    loading,
  } = useRequest(myApplyChildList, {
    manual: true,
    refreshDeps: [flowUUID],
  });

  useEffect(() => {
    if(data?.length > 0) {
      setDataSource(data)
    }
    if(childData?.length > 0) {
      setDataSource(childData)
    }
  }, [childData, data]);

  const handleToDetail = (val: string, record: any) => {
    // 流程-我的申请-详情
    const params = {
      ...record,
      id: val
    }
    dispatch(setMineApplyPageState('detail'))
    dispatch(setMineApplyDetailParams(params))
  }

  let newExpandColumns = [];
  newExpandColumns = columnsRequestExpandRow()?.map((item: any) => {
    if (item?.dataIndex === 'uuid') {
      return {
        ...item,
        render: (val: any, record: any) => (
          <span 
            className={`${styles.underLine} options`}
            onClick={() => handleToDetail(val,record)}
          >
            {val}
          </span>
        ),
      };
    }
    return item;
  });

  // 全部的内层表格如果是待落权也要添加操作，要协调处理
  if ((activeKey === 'power' || record?.tab === 'power')) {
    newExpandColumns.push({
      title: t('flow_operation'),
      dataIndex: 'actions',
      width: '20%',
      render: (_: any, record: any) =>
        record?.applyStatus === 'power' && (
          <div
            className={
              record?.flowApplyStatus === 'refuse'
                ? styles.optionsRefuseBtn
                : styles.optionsBtnText
            }
          >
            <span
              onClick={() => {
                urgeRun({
                  flowId: Number(record?.applyId),
                  senderIds: record?.connectionManagerId,
                  childMainUUID: record?.uuid,
                });
              }}
            >
              {t('flow_give_reminder')}
            </span>
          </div>
        ),
    });
  }

  return (
    <Table
      rowKey="uuid"
      loading={loading}
      columns={newExpandColumns}
      dataSource={dataSource}
      pagination={false}
      size="middle"
      className={styles.childTable}
    />
  );
};

export { MyApplyTablePage };
