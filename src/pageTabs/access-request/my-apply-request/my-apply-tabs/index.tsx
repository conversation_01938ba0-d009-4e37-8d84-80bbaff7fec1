import React, { FC, useEffect, useState } from "react";
import { Tabs } from "antd";
import { useDispatch, useSelector } from 'src/hook'
import ErrorBoundary from 'antd/lib/alert/ErrorBoundary'
import { MY_APPLY_TABS } from '../../constants';
import { MyApplyTablePage } from '../my-apply-table'
import { setApplySearchValue } from "../../accessRequestSlice";
import { useTranslation } from 'react-i18next';
import styles from "../index.module.scss";

export interface IProps {
	onChangeValue: (str: string) => void
	refreshShoppingCart: () => void;
	callbackActiveKey: (key: string) => void
}

const MyApplyTabsPage: FC<IProps> = ({ ...props }) => {
	const { onChangeValue, refreshShoppingCart, callbackActiveKey } = props

	const { mineApplyDetailParams } = useSelector(state => state.accessRequest);
	const dispatch = useDispatch()
  const { t } = useTranslation()
	// const [selectValue, setSelectValue] = useState('desc')
	const [activeKey, setActiveKey] = useState('pending')

	useEffect(() => {
		setActiveKey(mineApplyDetailParams?.curTab || 'pending')
		//防止切换tab后再次进入不更新tab
	},[JSON.stringify(mineApplyDetailParams)])

	return (
		<div className={styles.tabsPageWrap}>
			<div className={styles.tabsHeader}>
				<div className={styles.tabsLeft}>
					<Tabs
						size="small"
						className={styles.card}
						type="card"
						tabPosition="top"
						animated
						activeKey={activeKey}
						onChange={(e: string) => {
							setActiveKey(e)
							callbackActiveKey(e)
							dispatch(setApplySearchValue(''))
							onChangeValue('')
						}}
					>
						{
							Object.keys(MY_APPLY_TABS).map((key) => (
								<Tabs.TabPane
									tab={t(MY_APPLY_TABS[key])}
									key={key}
								/>
							))
						}
					</Tabs>
					{/* <div className={classNames(styles.ml10, styles.subtextColor)}>共 20条数据·按创建时间排序·更新于 2022-05-20 13:15:11</div> */}
				</div>
			</div>
			<div>
				<ErrorBoundary>
					<MyApplyTablePage activeKey={activeKey} refreshShoppingCart={refreshShoppingCart} />
				</ErrorBoundary>
			</div>
		</div>
	);
};

export { MyApplyTabsPage };
