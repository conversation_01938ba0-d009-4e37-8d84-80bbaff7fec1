import React, {  useState } from 'react'
import { useDispatch } from 'src/hook'
import ErrorBoundary from 'antd/lib/alert/ErrorBoundary'
import { Input } from 'antd'
import { MENU_FLOW } from 'src/constants'
import { SimpleBreadcrumbs } from 'src/components'
import { SearchOutlined } from '@ant-design/icons'
import { MyApprovalTabsPage } from './my-approval-tabs'
import { setApplySearchValue } from '../accessRequestSlice'
import styles from './index.module.scss'
import { useLocation } from 'react-router-dom'
import _ from 'lodash'
import { useTranslation } from 'react-i18next'

export const MyApprovalRequestPage= () => {
  const dispatch = useDispatch()
  const { pathname } = useLocation();
  const { t } = useTranslation();
  const [searchValue, setSearchValue] = useState('')

  const breadcrumbData = [
    { title: t(MENU_FLOW) },
    {
      title: t('flow_my_approvals'),
    },
  ];

  // useEffect(() => {
  //   dispatch(setApplySearchValue(''))
  // }, [pathname])

  const onChangeValue = (str: string) => {
    setSearchValue(str)
  }

  const onSearch = _.debounce((e) => {
    dispatch(setApplySearchValue(e?.target?.value))
  }, 500)

  const handleInputChange = (e: { persist: () => void; target: { value: React.SetStateAction<string> } })=> {
    e.persist()
    setSearchValue(e?.target?.value)
    onSearch(e)
  }

  return (
    <div className={styles.myApplyRequestWrap}>
      <div className={styles.headers}>
        <SimpleBreadcrumbs items={breadcrumbData} />
        <div className={styles.flexRow}>
          <Input
            prefix={<SearchOutlined className="site-form-item-icon" />}
            placeholder={t('flow_search_title_applicant')}
            allowClear
            value={searchValue}
            onChange={handleInputChange}
          />
        </div>
      </div>
      <div className={styles.content}>
        <ErrorBoundary>
          <MyApprovalTabsPage onChangeValue={onChangeValue}/>
        </ErrorBoundary>
      </div>
    </div>
  )
}
