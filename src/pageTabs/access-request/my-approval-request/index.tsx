import React, { FC, useEffect } from 'react'
import ErrorBoundary from 'antd/lib/alert/ErrorBoundary'
import { Layout } from 'antd'
import classNames from 'classnames'
import { MyApprovalRequestPage } from './MyApprovalRequest'
import { MyApprovalRequestDetailPage } from '../my-apprpval-request-detail'
import { useSelector, useDispatch } from 'src/hook';
import styles from './index.module.scss'
import { setMineApproveDetailParams, setMineApprovePageState } from '../accessRequestSlice'

const { Content } = Layout

interface ListProps { }


export const MyApplyApprovalPage: FC<ListProps> = ({ ...rest }) => {
  const dispatch = useDispatch()
  const { mineApprovePageState } = useSelector((state) => state.accessRequest); 

  useEffect(()=>{
    return () => {
      // 恢复 初始状态
      dispatch(setMineApprovePageState('')) 
      dispatch(setMineApproveDetailParams({}))
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])
  
  const contentRender = () => {
    switch (mineApprovePageState) {
      case 'detail':
        return <MyApprovalRequestDetailPage />;     // 审批详情 
      default:
        return <MyApprovalRequestPage />            // 我的审批
    }
  }
  return (
    <Layout className="cq-container">
      <Layout className="cq-main" style={{padding: 0}}>
        <Content
          className={classNames('cq-content', { [styles['change-request-page']]: true })}
        >
          <ErrorBoundary>
            {
              contentRender()
            }
          </ErrorBoundary>
        </Content>
      </Layout>
    </Layout>
  )
}
