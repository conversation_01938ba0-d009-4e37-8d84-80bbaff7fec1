import React, { FC, useEffect, useMemo, useState, memo } from 'react';
import { useRequest, useSelector, useDispatch } from 'src/hook';
import { Input, Modal, Table, message, Button, Tooltip, Select } from 'antd';
import { columnsRequest, columnsRequestExpandRow } from '../../common/columns';
import { BtnWithConfirmModal } from 'src/components';
import {
  approveApplication,
  queryPointApprover,
  getResolvedApproval,
  getApproveListFinish,
  myApplyChildList,
  myApplyChildListAppraved,
  dealWithAccessPermission,
  withdrawOrder,
  getApprovalPending,
  getApprovalAll,
} from 'src/api';
import ModalDataChangeSelectNewApprover from 'src/pageTabs/flowPages/flowDetails/ModalDataChangeSelectNewApprover';
import classNames from 'classnames';
import {
  applyStatusMap,
  APP_EFFECTIVE_STATES,
  MyApprovalTabKeyType,
  MyApprovalTabKey,
  NO_NEED_TO_LAND_TYPES
} from '../../constants';
import { getEffectiveStatus } from '../../utils';
import { getCurrentModulePermissionByUrl } from 'src/util';
import { useTranslation } from 'react-i18next';
import { setMineApprovePageState, setMineApproveDetailParams } from 'src/pageTabs/access-request/accessRequestSlice'
import styles from '../index.module.scss';

export interface IProps {
  activeKey: MyApprovalTabKey;
}

const defaultSearchParams = {
  currentPage: 1,
  pageSize: 10,
  sorted: 'desc',
}
const MyApprovalTablePage: FC<IProps> = memo(({ ...props }) => {
  const dispatch = useDispatch();
  const { activeKey } = props;
  const { applySearchValue, mineApproveDetailParams} = useSelector((state) => state.accessRequest);
  const { t } = useTranslation();
  const [visible_setNewApprover, setVisible_setNewApprover] = useState(false); // 新审批人 modal visible
  const [currentTransferApprover, setCurrentTransferApprover] = useState<{userTasks:  Array<string>,flowId?: number } | null>(null); // 新审批人 modal visible
  const [approveType, setApproveType] = useState<
    'rejected' | 'fullilled' | 'pending'
  >('pending');
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [newData, setNewdData] = useState<any>([]);
  const [allSearchParams, setAllSearchParams] = useState<any>(defaultSearchParams)
  const [filterType, setFilterType] = useState<any>(null); // 类型 -- 筛选
  const [statusType, setStatusType] = useState<any>(null); // 状态 -- 全部tab中的表头筛选项
  const [timeSort, setTimeSort] = useState<string>('desc');
  const [expandedRowKeys, setExpandedRowKeys] = useState<string[]>([]);
  const [expandChildData, setExpandChildData] = useState<any>({}); // 展开子表数据

  const userId = useSelector((state) => state.login.userInfo.userId);

  //模块权限查询
  const { permissionList } = useSelector((state) => state?.login)
  let modulePermissionObj: { isOnlyRead: boolean; roleNameList: string[] } = useMemo(() => {
    return getCurrentModulePermissionByUrl(permissionList, 'FLOW_APPLY') || { isOnlyRead: false, roleNameList: [] };
  }, [JSON.stringify(permissionList)])

  // api
  const apiEum: any = {
    stayApprove: getApprovalPending,
    yetApprove: getResolvedApproval,
    power: getResolvedApproval,
    finish: getApproveListFinish,
    withdraw: getResolvedApproval,
    all: getApprovalAll,
  }

  // operationRender
  const opeEum: any = {
    pending: 'stayApprove',
    pass: 'yetApprove',
    power: 'power',
    finish: 'finish',
    withdraw: 'withdraw',
    refuse: 'yetApprove',
  }

  // 我的审批列表
  const { data, loading, refresh, run } = useRequest(apiEum[activeKey], {
    manual: true,
    debounceInterval: 200,
    formatResult: (data) => {
      const judgeKey = ['stayApprove', 'all'].includes(activeKey)
      if (judgeKey) {
        let { totalElements, myApplyResponseVoList: list } = data;
        let newList = [];
        newList = list.map((item: any) => {
          item.indexKey = String(item.mainUUID) + String(item.flowTaskId) //rowKey
          return item;
        });
        const total = totalElements;
        setNewdData(newList);
        return { list, total };
      }
      else {
        let { pageNumber, pageSize, list } = data;
        const newList = list.map((item: any, index: number) => {
          item.indexKey = (pageNumber - 1) * pageSize + index + 1; //rowKey
          return item;
        });
        setNewdData(newList);
        const total = data.totalCount;
        return { list: newList, total };
      }
    }
  });

  // 同意审批
  const { run: runApprove, fetches: fetchesApprove } = useRequest(
    approveApplication,
    {
      manual: true,
      // fetchKey: (id) => id,
      onSuccess: () => {
        if (approveType === 'fullilled') {
          message.success(t('flow_approved_success'));
        } else {
          message.success(t('flow_rejection_success'));
        }
        refreshAll();
      },
    }
  );

  //撤回
  const { run: runWithdrawOrder } = useRequest(withdrawOrder, {
    manual: true,
    onSuccess: () => {
      refreshAll();
    },
  });

  const refreshAll = () => {
    const params: any = {
      pageSize: allSearchParams?.pageSize,
      currentPage: allSearchParams?.currentPage,
      priGranType: allSearchParams?.priGranType || undefined,
    }
    switch (activeKey) {
      case 'stayApprove':
        params.keyWord = allSearchParams?.applySearchValue
        params.sorted = allSearchParams?.sorted
        break;
      case 'yetApprove':
      case 'finish':
        params.userId = userId
        params.title = allSearchParams?.applySearchValue
        params.timer =  allSearchParams?.sorted
        break;
      case 'power':
      case 'withdraw':
        params.userId = userId
        params.title = allSearchParams?.applySearchValue
        params.timer =  allSearchParams?.sorted
        params.applyStatus = activeKey
        break;
      case 'all':
        params.keyWord = allSearchParams?.applySearchValue
        params.sorted =  allSearchParams?.sorted
        params.tab = allSearchParams?.tab || undefined
        break;
    }
    run(params)
  };

  useEffect(() => {
    setAllSearchParams({
      ...defaultSearchParams,
      priGranType: mineApproveDetailParams?.priGranType || undefined,
      tab: mineApproveDetailParams?.flowApplyStatus || undefined,
      applySearchValue
    })
  }, [JSON.stringify(mineApproveDetailParams),applySearchValue, activeKey])

  useEffect(() => {
    if (userId) {
      refreshAll()
    }
  }, [userId, allSearchParams]);

  // 驳回|拒绝
  const confirm = (applyId: string, flowTaskId: any) => {
    function isEmpty(closeFn: any, val?: string) {
      let approveParams = {
        flowId: applyId,
        taskId: flowTaskId || '',
        approvedFlag: false,
        approvedComment: val,
        approvedTime: '',
      };
      setApproveType('rejected');
      //@ts-ignore
      runApprove(approveParams).finally(() => {
        setApproveType('pending');
      });

      closeFn();
    }

    const textOnchange = (val: string) => {
      update({
        onOk: (closeFn) => isEmpty(closeFn, val),
      });
    };

    const { update } = Modal.confirm({
      title: t('flow_remark'),
      content: (
        <Input.TextArea
          rows={5}
          onChange={(e) => textOnchange(e.target.value)}
        />
      ),
      okText: t('flow_ok'),
      cancelText: t('flow_cancel'),
      onOk: (closeFn) => isEmpty(closeFn),
    });
  };

  // 同意
  const agreeBtn = async (record: any) => {
   
    let approvers = []
    try {
      approvers = record?.flowInstanceId && await queryPointApprover(record.flowInstanceId)
    } catch (error) {

    }

    let agreeRemark: string = '';
    let designatedApproveList: string = '';
    function isEmpty(closeFn: () => void) {
      let approveParams = {
        flowId: record?.applyId,
        taskId: record?.flowTaskId || '',
        approvedFlag: true,
        approvedComment: agreeRemark,
        approvedTime: '',
        ...(designatedApproveList ? { designatedApproveList: [designatedApproveList] } : [])
      };

      setApproveType('fullilled');
      //@ts-ignore
      runApprove(approveParams).finally(() => {
        setApproveType('pending');
      });
      closeFn();
    }

    const { update } = Modal.confirm({
      title: t('flow_remark'),
      content: (
        <>
          <Input.TextArea
            rows={5}
            onChange={(e) => {agreeRemark = e.target.value}}
          />
          {
            record?.nextNodeIsAssigneeGroup &&
            <div style={{ marginLeft: -62 }}>
              <span>{t('flow_specify_approver')}:</span>
              <Select
                allowClear
                style={{ width: 287, marginTop: 10, marginLeft: 10 }}
                options={approvers?.map((a: any) => ({ label: `${a.userName}(${a.userId})`, value: a.userId }))}
                placeholder={t('flow_select_approver')}
                onChange={(value: string) => {designatedApproveList = value}}
              />
            </div>
          }
        </>
      ),
      okText: t('flow_ok'),
      cancelText: t('flow_cancel'),
      onOk: (closeFn) => isEmpty(closeFn),
    });
  };

  // 转审
  const turretBtn = (record: any) => {
    setVisible_setNewApprover(true);
    setCurrentTransferApprover({
      flowId: record?.applyId,
      userTasks: [record.flowTaskId]
    }); //应该使用当前userId
  };

  const renderTabAction = (record: any, activeKey: MyApprovalTabKeyType) => {
    // 全部视图根据tab字段转换成对应的activeKey
    const judgeKey = activeKey === 'all' ? opeEum[record?.tab] : activeKey

    if (judgeKey === 'stayApprove') {
      return (
        <>
          <Tooltip
            title={modulePermissionObj?.isOnlyRead ?
              `${t('flow_current_role_is')} [${modulePermissionObj?.roleNameList?.join(", ")}], ${t('flow_no_permission_work_order')}`
              : null
            }
          >
            <Button
              type='link'
              onClick={() => agreeBtn(record)}
              className={styles.padding0}
              disabled={modulePermissionObj?.isOnlyRead}
            >{t('flow_approve')}</Button>
            <Button
              type='link'
              className={styles.padding0}
              onClick={() =>
                confirm(record?.applyId as string, record?.flowTaskId)
              }
              disabled={modulePermissionObj?.isOnlyRead}
            >{t('flow_reject')}</Button>
            <Button
              type='link'
              className={styles.padding0}
              onClick={() => turretBtn(record)}
              disabled={modulePermissionObj?.isOnlyRead}
            >{t('flow_transfer_review')}</Button>
          </Tooltip>
        </>
      );
    } else if (judgeKey === 'yetApprove') {
      return (
        <>
          {/*已通过且（生效中 || 待生效） */}
          {(record?.applyStatus === 'pass' || record?.flowApplyStatus === 'pass') &&
            getEffectiveStatus(record?.beginTime, record?.endTime) !==
            APP_EFFECTIVE_STATES.EXPIRED && (
              <BtnWithConfirmModal
                title={t('flow_confirm_withdraw_permissions')}
                btnText={t('flow_withdraw')}
                onClick={(reason?: string) => runWithdrawOrder({
                  flowId: Number(record?.applyId),
                  withdrawRemark: reason
                })}
              />
            )}
        </>
      );
    } else if (judgeKey === 'finish') {
      return (
        <>
          {/* 所有子工单单都通过 */}
          {!record?.allChildRefuse && record?.approvedUser && getEffectiveStatus(record?.beginTime, record?.endTime) !==
            APP_EFFECTIVE_STATES.EXPIRED && (
              <BtnWithConfirmModal
                title={t('flow_confirm_withdraw_permissions')}
                btnText={t('flow_withdraw')}
                onClick={(reason?: string) => runWithdrawOrder({
                  flowId: Number(record?.applyId),
                  withdrawRemark: reason
                })}
              />
            )}
        </>
      );
    } else {
      return '-';
    }
  };

  // 流程-我的审批-详情
  const handleToApproveDetail = (id: string, record: any) => {
    const params = {
      ...record,
      id,
      curTab: activeKey // 当前tab页
    }
    dispatch(setMineApprovePageState('detail'))
    dispatch(setMineApproveDetailParams(params))
  }

  // 外层列表的column
  const newColumns = useMemo(() => {
    const newColumns = columnsRequest({
      priGranTypeValue: allSearchParams?.priGranType,
      activeKey
    })?.map((item: any) => {
      if (item?.dataIndex === 'uuid') {
        return {
          ...item,
          dataIndex: ['stayApprove', 'all'].includes(activeKey) ? 'mainUUID' : 'uuid',
          render: (val: any, record: any) => (
            <span
              className={`${styles.underLine} options`}
              onClick={() => handleToApproveDetail(val, record)}
            >
              {val}
            </span>
          ),
        };
      }
      if (item?.dataIndex === 'applyStatus' && activeKey === 'stayApprove') {
        return {
          ...item,
          dataIndex: 'flowApplyStatus',
          render: (_: any, { flowApplyStatus }: any) => {
            return <span>
              <span
                className={classNames(
                  styles.statusDot,
                  flowApplyStatus === 'pending' && styles.pendingBack,
                  (flowApplyStatus === 'pass' || flowApplyStatus === 'already') && styles.alreadyBack,
                  flowApplyStatus === 'power' && styles.powerBack,
                  flowApplyStatus === 'refuse' && styles.rejectBack,
                  flowApplyStatus === 'withdraw' && styles.rejectBack,
                )}
              ></span>
              {t(applyStatusMap[flowApplyStatus])}
            </span>
          },
        }
      }
      if (item?.dataIndex === 'applyUserName' && ['stayApprove', 'all'].includes(activeKey)) {
        return {
          ...item,
          render: (applyUserName: string, record: any) => (
            <div>
              {
                applyUserName ? applyUserName
                  : record?.applyUserId ?
                    record?.applyUserId
                    : '-'
              }
            </div>
          ),
        }
      }
      if (item?.dataIndex === 'currentAssignee' && ['stayApprove', 'all'].includes(activeKey)) {
        return {
          ...item,
          render: (currentAssignee: string, record: any) => (
            <div>
              {
                record?.currentAssigneeName ? <Tooltip title={record?.currentAssigneeName}>{`${record?.currentAssigneeName?.slice(0,18)}...`}</Tooltip>
                  : currentAssignee ?
                    currentAssignee
                    : '-'
              }
            </div>
          ),
        }
      }
      if (activeKey === 'finish' && item?.dataIndex === 'applyStatus') {
        return {
          ...item,
          render: (applyStatus: any, record: any) => (
            <span>
              <span
                className={classNames(
                  styles.statusDot,
                  applyStatus === 'pending' && styles.pendingBack,
                  (applyStatus === 'pass' || applyStatus === 'already') &&
                  styles.alreadyBack,
                  applyStatus === 'power' && styles.powerBack,
                  applyStatus === 'refuse' && styles.rejectBack
                )}
              ></span>
              {applyStatus === 'pass' ? t('flow_completed') : t(applyStatusMap[applyStatus])}
            </span>
          ),
        };
      }
      if (item?.dataIndex === 'actions') {
        return {
          ...item,
          fixed: 'right',
          render: (_: any, record: any) => (
            <div className={styles.actionsBtn}>
              {renderTabAction(record, activeKey)}
            </div>
          ),
        };
      }
      if (activeKey === 'all' && item?.dataIndex === 'applyStatus') {
        return {
          ...item,
          dataIndex: 'flowApplyStatus',
          render: (_: any, { flowApplyStatus, tab }: any) => {
            return <span>
              <span
                className={classNames(
                  styles.statusDot,
                  flowApplyStatus === 'pending' && styles.pendingBack,
                  (flowApplyStatus === 'pass' || flowApplyStatus === 'already') && styles.alreadyBack,
                  (flowApplyStatus === 'power' || tab === 'power') && styles.powerBack,
                  flowApplyStatus === 'refuse' && styles.rejectBack,
                  flowApplyStatus === 'withdraw' && styles.rejectBack,
                )}
              ></span>
              {tab === 'power' ? t(applyStatusMap['power']) : t(applyStatusMap[flowApplyStatus])}
            </span>
          },
          filters: Object.keys(applyStatusMap).filter((key) => !['rejected', 'stop'].includes(key)).map((key) => ({
            text: <span>
              <span
                className={classNames(
                  styles.statusDot,
                  key === 'pending' && styles.pendingBack,
                  (key === 'pass' || key === 'already') && styles.alreadyBack,
                  key === 'power' && styles.powerBack,
                  key === 'refuse' && styles.rejectBack,
                  key === 'withdraw' && styles.rejectBack,
                )}
              ></span>
              {t(applyStatusMap[key])}
            </span>,
            value: key === 'already' ? 'finish' : key,
          })),
          filteredValue: allSearchParams?.tab || undefined,
          filterMultiple: true,
        };
      }
      return item;
    });
    if (['yetApprove', 'finish'].includes(activeKey)) {
      newColumns.splice(7, 0, {
        title: t('flow_effective_status'),
        dataIndex: 'effectiveState',
        width: 100,
        render: (_: string, record: any) => {
          return ((record?.applyStatus === 'pass' && activeKey === 'yetApprove') || activeKey === 'finish') ? t(getEffectiveStatus(record?.beginTime, record?.endTime)) : '-';
        },
      });
    }
    if (activeKey === 'all') {
      newColumns.splice(7, 0, {
        title: t('flow_effective_status'),
        dataIndex: 'effectiveState',
        width: 100,
        render: (_: string, record: any) => {
          return (record?.flowApplyStatus === 'pass' && record?.tab !== 'power') || record?.tab === 'finish' ? 
          t(getEffectiveStatus(record?.beginTime, record?.endTime)) 
          : '-';
        },
      });
    }
    //样式
    return newColumns;
  }, [activeKey, t, allSearchParams?.priGranType, allSearchParams?.tab]);

  // table的onchange
  const onChange = (pagination: any, filters: any, sorter: any) => {
   
    const params: any = {
      pageSize: pagination?.pageSize,
      currentPage: pagination?.current,
      priGranType: filters?.priGranType ? filters?.priGranType: undefined,
      tab: filters?.flowApplyStatus || undefined,
      sorted: sorter?.order === 'ascend' ? 'asc' : 'desc'
    } 

    setAllSearchParams({
      ...allSearchParams,
      ...params
    })
  };

  // onExpand 事件处理
  const onExpand = async (expanded: boolean, record: any) => {
    const rowKey = ['stayApprove', 'all'].includes(activeKey) ? record?.indexKey : record?.uuid;
    if (expanded) {
      try {
        const flowUUID = ['stayApprove', 'all'].includes(activeKey) ? record?.mainUUID : record?.uuid;
        const flowTaskId = record?.flowTaskId;
        let childData: any = [];
        // 调用接口判断是否可以展开
        const fn = (activeKey !== 'stayApprove' || record?.tab !== 'pending') ? myApplyChildListAppraved : myApplyChildList
        if ((activeKey === 'stayApprove' || record?.tab === 'pending')) {
          childData = await fn(flowUUID, flowTaskId);
        } else {
          childData = await fn(flowUUID, '1');
        }
        if (childData?.length > 0) {
          // 可以展开
          setExpandedRowKeys([...expandedRowKeys, rowKey]);
          setExpandChildData({...(expandChildData ?? {}), [rowKey]: childData});
        } else {
          // 不可以展开
          message.error(t('flow_no_sub_order'));
        }
      } catch (error) {
        message.error(t('expand_exception'));
      }
    } else {
      // 收起行
      setExpandedRowKeys(expandedRowKeys?.filter(key => key !== rowKey));
    }
  };

  const tableRowKey = ['stayApprove', 'all'].includes(activeKey) ? 'indexKey' : 'uuid';
  return (
    <div>
      <Table
        rowKey={tableRowKey}
        loading={loading}
        className={styles.tablePage}
        expandable={{
          expandedRowRender: (record: any) => (
            <ExpandedRowContent 
              record={record} 
              activeKey={activeKey} 
              data={expandChildData?.[record?.[tableRowKey]] ?? []}
            />
          ),
          rowExpandable: (record) => !NO_NEED_TO_LAND_TYPES.includes(record?.priGranType),
          onExpand,
          expandedRowKeys,
        }}
        columns={newColumns}
        dataSource={newData}
        pagination={{
          current: allSearchParams?.currentPage,
          pageSize: allSearchParams?.pageSize,
          total: data?.total,
          size: 'default',
          showTotal: (total: number) => `${t('flow_total')} ${total} ${t('flow_items_lower')}`,
          showSizeChanger: true,
          showQuickJumper: true,
        }}
        size="small"
        rowClassName={(record, index) =>
          ((record?.applyStatus === 'pass' && activeKey === 'yetApprove') ||
            activeKey === 'finish' ||
            ['pass', 'finish'].includes(record?.tab)) &&
            getEffectiveStatus(record?.beginTime, record?.endTime) ===
            APP_EFFECTIVE_STATES.EXPIRED
            ? styles.notInEffect
            : ''
        }
        scroll={{ x: "1200", y: `calc(100vh - 280px)` }}
        onChange={onChange}
      />
      <ModalDataChangeSelectNewApprover
        cleanParentComponentData={() => {
          setCurrentTransferApprover(null);
          if (['stayApprove', 'all'].includes(activeKey)) {
            refresh();
          }
        }}
        flowId={currentTransferApprover?.flowId}
        userTasks={currentTransferApprover?.userTasks || []}
        visible_setNewApprover={visible_setNewApprover}
        setVisible_setNewApprover={setVisible_setNewApprover}
      />
    </div>
  );
});

export const ExpandedRowContent = ({
  record,
  activeKey,
  data,
}: {
  record: any;
  activeKey: string;
  data: any[],
}) => {
  const [dataSource, setDataSource] = useState<any[]>([]);
  const dispatch = useDispatch();
  const { t } = useTranslation();
  const flowTaskId = record?.flowTaskId;
  const flowUUID = ['stayApprove', 'all'].includes(activeKey) ? record?.mainUUID : record?.uuid;;
  const {
    data: childData,
    run: childRefresh,
    loading,
  } = useRequest(() => {
    const fn = (activeKey !== 'stayApprove' || record?.tab !== 'pending') ? myApplyChildListAppraved : myApplyChildList
      if ((activeKey === 'stayApprove' || record?.tab === 'pending')) {
        return fn(flowUUID, flowTaskId);
      } else {
        return fn(flowUUID, '1');
      }
    },
    {
      manual: true,
      refreshDeps: [flowUUID],
    }
  );

  useEffect(() => {
    if(data?.length > 0) {
      setDataSource(data)
    }
    if(childData?.length > 0) {
      setDataSource(childData)
    }
  }, [JSON.stringify(childData), JSON.stringify(data)]);

  // 落权
  const { run: weightingRun } = useRequest(dealWithAccessPermission, {
    manual: true,
    onSuccess(data) {
      childRefresh();
    },
  });

  // 驳回|拒绝
  const confirm = (record: any) => {
    function isEmpty(closeFn: any, val?: string) {
      weightingRun({
        flowUUID: record?.uuid,
        powerStatus: 'rejected',
        remarks: val,
      });
      closeFn();
    }

    const textOnchange = (val: string) => {
      update({
        onOk: (closeFn) => isEmpty(closeFn, val),
      });
    };

    const { update } = Modal.confirm({
      title: t('flow_rejection_comments'),
      content: (
        <Input.TextArea
          rows={5}
          onChange={(e) => textOnchange(e.target.value)}
        />
      ),
      okText: t('flow_ok'),
      cancelText: t('flow_cancel'),
      onOk: (closeFn) => isEmpty(closeFn),
    });
  };

  // 同意
  const agreeBtn = (record: any) => {
    function isEmpty(closeFn: any, val?: string) {
      weightingRun({
        flowUUID: record?.uuid,
        powerStatus: 'already',
        remarks: val,
      });
      closeFn();
    }

    const textOnchange = (val: string) => {
      update({
        onOk: (closeFn) => isEmpty(closeFn, val),
      });
    };

    const { update } = Modal.confirm({
      title: t('flow_approval_comments'),
      content: (
        <Input.TextArea
          rows={5}
          onChange={(e) => textOnchange(e.target.value)}
        />
      ),
      okText: t('flow_ok'),
      cancelText: t('flow_cancel'),
      onOk: (closeFn) => isEmpty(closeFn),
    });
  };

  // 流程-我的审批-详情
  const handleToApproveDetail = (id: string, record: any) => {
    const params = {
      ...record,
      id,
      curTab: activeKey, // 当前tab
    }
    dispatch(setMineApprovePageState('detail'))
    dispatch(setMineApproveDetailParams(params))
  }

  const newExpandColumns = useMemo(() => {
    let newExpandColumns = columnsRequestExpandRow()?.map((item: any) => {
      if (item?.dataIndex === 'uuid') {
        return {
          ...item,
          render: (val: any, r: any) => (
            <span
              className={`${styles.underLine} options`}
              onClick={() => handleToApproveDetail(val, r)}
            >
              {val}
            </span>
          ),
        };
      }
      return item;
    });

    if ((activeKey === 'power' || record?.tab === 'power')) {
      newExpandColumns.splice(5, 0, {
        title: t('flow_operation'),
        dataIndex: 'actions',
        render: (val: any, record: any) => {
          return record?.canOperation ? (
            <div className={styles.actionsBtn}>
              <Button type="link" onClick={() => agreeBtn(record)}>{t('flow_approve')}</Button>
              <Button type="link" onClick={() => confirm(record)}>{t('flow_reject')}</Button>
            </div>
          ) : (
            '-'
          );
        },
      });
    }
    return newExpandColumns;
  }, [activeKey, record, t]);

  return (
    <Table
      rowKey="uuid"
      loading={loading}
      columns={newExpandColumns}
      dataSource={dataSource}
      pagination={false}
      size="middle"
      className={styles.childTable}
    />
  );
};

export { MyApprovalTablePage };
