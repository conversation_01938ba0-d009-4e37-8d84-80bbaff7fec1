import React, { FC, useEffect, useState, memo } from "react";
import { Select, Tabs } from "antd";
import { useDispatch, useSelector } from 'src/hook'
import ErrorBoundary from 'antd/lib/alert/ErrorBoundary'
import { MY_APPROVAL_TABS, MyApprovalTabKeyType } from '../../constants';
import { MyApprovalTablePage } from '../my-approval-table'
import { setApplySearchValue } from "../../accessRequestSlice";
import { useTranslation } from 'react-i18next';
import styles from "../index.module.scss";

export interface IProps {
  onChangeValue: (str: string) => void
}

const MyApprovalTabsPage: FC<IProps> = memo(({...props}) => {
	const { onChangeValue } = props
  const dispatch = useDispatch()
  const { t } = useTranslation()
	const { mineApproveDetailParams } = useSelector(state => state.accessRequest);
	// const [selectValue, setSelectValue] = useState('desc')
	const [activeKey, setActiveKey] = useState<MyApprovalTabKeyType>('stayApprove')

	useEffect(() => {
	
		setActiveKey(mineApproveDetailParams?.curTab ||'stayApprove')
	},[JSON.stringify(mineApproveDetailParams)])

	return (
		<div className={styles.tabsPageWrap}>
			<div className={styles.tabsHeader}>
				<div className={styles.tabsLeft}>
					<Tabs
						size="small"
						className={styles.card}
						type="card"
						tabPosition="top"
						animated
						activeKey={activeKey}
						onChange={(e: string) => {
							setActiveKey(e)
							dispatch(setApplySearchValue(''))
							onChangeValue('')
						}}
					>
						{
							Object.keys(MY_APPROVAL_TABS).map(key => (
								<Tabs.TabPane
									tab={t(MY_APPROVAL_TABS[key])}
									key={key}
								/>
							))
						}
					</Tabs>
					{/* <div className={classNames(styles.ml10, styles.subtextColor)}>共 20条数据·按创建时间排序·更新于 2022-05-20 13:15:11</div> */}
				</div>
			</div>
			<div>
				<ErrorBoundary>
					<MyApprovalTablePage  activeKey={activeKey}/>
				</ErrorBoundary>
			</div>
		</div>
	);
});

export { MyApprovalTabsPage };
