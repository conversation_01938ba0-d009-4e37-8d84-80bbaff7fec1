import React, { FC, useEffect, useState } from 'react'
import classnames from 'classnames'
import { useForm } from 'antd/lib/form/Form'
import { Button, Card, Form, Input, message } from 'antd'
import { useRequest, useDispatch } from 'src/hook'
import { approveApplication } from 'src/api'
import ModalDataChangeSelectNewApprover from 'src/pageTabs/flowPages/flowDetails/ModalDataChangeSelectNewApprover';
import { useTranslation } from 'react-i18next'
import { setMineApprovePageState, setMineApproveDetailParams } from 'src/pageTabs/access-request/accessRequestSlice'
import styles from '../index.module.scss'

const FormLayout = {
  labelCol: { span: 0 },
  wrapperCol: { offset: 0, span: 8 },
}

interface ListProps {
  approvedComment?: any
  applyId: string,
  flowTaskId: string
  className?: any
}

export const AuditInfoPage: FC<ListProps> = (props) => {
  const dispatch = useDispatch()
  const { approvedComment, flowTaskId, applyId, className } = props
  const { t } = useTranslation();
  const [form] = useForm()
  const [approveType, setApproveType] = useState<
    'rejected' | 'fullilled' | 'pending'
  >('pending')
  const [visible_setNewApprover, setVisible_setNewApprover] = useState(false); // 新审批人 modal visible


  useEffect(() => {
    form.setFields([
      { name: "approvedComment", value: approvedComment }
    ])
  }, [approvedComment])

  // 流程-我的审批
  const handleToApprove = () => {
    dispatch(setMineApprovePageState(''))
    dispatch(setMineApproveDetailParams({}))
  }

  // 同意审批
  const { run: runApprove, fetches: fetchesApprove } = useRequest(
    approveApplication,
    {
      manual: true,
      // fetchKey: (id) => id,
      onSuccess: () => {
        if (approveType === 'fullilled') {
          message.success(t('flow_approved_success'))
        } else {
          message.success(t('flow_rejected_success'))
        }
        handleToApprove()
      },
    },
  )

  // 同意
  const agreeBtn = () => {
    setApproveType('fullilled')
    form.validateFields().then((res) => {
      let approveParams = {
        flowId: applyId,
        taskId: flowTaskId,
        approvedFlag: true,
        approvedComment: res?.approvedComment,
        approvedTime: '',
      }
      runApprove(approveParams).finally(() => {
        setApproveType('pending')
      })
    })


    // setApproveType('fullilled')
    //@ts-ignore
  }

  const isEmpty = () => {
    form.validateFields().then((res) => {
      let approveParams = {
        flowId: applyId,
        taskId: flowTaskId || '',
        approvedFlag: false,
        approvedComment: res?.approvedComment,
        approvedTime: '',
      }
      setApproveType('rejected')
      //@ts-ignore
      runApprove(approveParams).finally(() => {
        setApproveType('pending')
      })
    })
  }
  return (
    <>
      <Card
        title={t('flow_review_info')}
        className={classnames(styles.borderShow, styles.mt27, styles.mb27, styles.detailCard, className)}
        actions={[
          <div>
            <Button type='primary' onClick={() => agreeBtn()}>{t('flow_approval_passed')}</Button>
            <Button onClick={() => isEmpty()}>{t('flow_approval_rejected')}</Button>
            <Button onClick={() => setVisible_setNewApprover(true)}>{t('flow_transfer_review')}</Button>
          </div>
        ]}
      >
        <Form form={form} {...FormLayout}>
          <Form.Item
            label={t('flow_remark')}
            name="approvedComment"
          // rules={[{ required: true }, {min: 1, max: 100}]}
          >
            <Input.TextArea allowClear maxLength={100} />
          </Form.Item>
        </Form>
      </Card>
      <ModalDataChangeSelectNewApprover
        cleanParentComponentData={() => {
          setVisible_setNewApprover(false);
          handleToApprove()
        }}
        userTasks={[flowTaskId]}
        visible_setNewApprover={visible_setNewApprover}
        setVisible_setNewApprover={setVisible_setNewApprover}
      />

    </>
  )
}
