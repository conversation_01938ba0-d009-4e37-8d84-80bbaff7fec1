.mt27 {
	margin-top: 27px;
}

.mb27 {
	margin-bottom: 27px;
}

.myApplyRequestDetailWrap {
	height: calc(100vh  - 30px - 48px);
	padding: 10px 32px;

	.headers {
		display: flex;
		// padding: 10px;
		justify-content: space-between;
		align-items: center;
	}

	.breadcrumb {
		// padding: 10px;
		font-size: 16px;
	}

	.content {
		width: 100%;
		min-height: 300px;
		display: flex;
		flex-direction: column;
		padding: 10px;

		.oddBgText {
			display: inline-block;
			background: #F0F1F5;
			padding: 5px 10px;
			// width: 120px;
			font-size: 12px;
			border-radius: 4px;
		}

		.fixOpe {
			position: -webkit-sticky;
			position: sticky;
			bottom: 0px;
			border: 1px solid #d9d9d9;
			box-shadow: 0 0 8px 0 #c3c7ce;
			:global {
				.ant-card-head {
					background-color: #f0f1f5;
					border-bottom: 2px solid #d9d9d9;
				}
				.ant-card-body {
					background-color: #f0f1f5
				}
				.ant-card-actions {
					background-color: #f0f1f5;
					border-top: 1px solid #d9d9d9;
				}
			}
		}
	}

	:global {
		.ant-breadcrumb-separator {
			color: #D8D8D8;
		}
	}
}

.borderShow {
	box-shadow: 0px 8px 15px 0px rgba(149, 156, 182, 0.15), inset -2px 0px 0px 0px rgba(255, 255, 255, 0.07);
}

.detailCard {
	:global {
		.ant-card-actions>li {
			text-align: left;
			margin-left: 25px;

			.ant-btn {
				margin-right: 23px;
			}
		}
	}
}

.tablePage {
	:global {
		.ant-table-thead>tr>th {
			// color: $sub-text-color;
			background-color: #F7F9FC;
			height: 48px;
		}

		.ant-table-row {
			height: 48px;
		}

		.ant-table-expanded-row .ant-table-wrapper {
			padding: 0 0 0 12px;
		}
	}

	.actionsBtn {
		span {
			cursor: pointer;
			color: #3262FF;
			text-decoration: none;
			margin: 5px
		}
	}
}

.permissionTooltip {
	max-width: none;
	max-height: 200px;
	overflow-y: scroll;
}