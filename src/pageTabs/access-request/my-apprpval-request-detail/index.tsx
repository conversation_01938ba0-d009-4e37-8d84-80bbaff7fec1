// 访问申请-我的审批-审批单
import React, { useEffect, useMemo, useState } from 'react';
import ErrorBoundary from 'antd/lib/alert/ErrorBoundary';
import { Descriptions, Spin } from 'antd';
import { MENU_FLOW } from 'src/constants'
import { SimpleBreadcrumbs } from 'src/components';
import { ResourceBidPage } from './resource-bid';
import { OrderFlowPage } from '../common/order-flow';
import { ApplicationsInfoPag } from '../common/application-info';
import { useRequest, useSelector, useDispatch } from 'src/hook';
import {
  getFlowRemarkInfo,
  getFlowStatus,
  getInventoryDataForMessage,
  getInventoryData,
  getNewFlowProcess,
  getPowerRemarkInfo,
  getWithdrawRemarkInfo,
} from 'src/api';
import { getCurrentModulePermissionByUrl } from 'src/util';

import { ApprovalRemarksPag } from '../common/approval-remarks';
import styles from './index.module.scss';
import { WeightRemarksPag } from '../common/weighting-info';
import AppliedResourceList from '../AppliedResourceList'
import { NO_NEED_TO_LAND_TYPES } from '../constants'
import { useTranslation } from 'react-i18next';
import { setMineApprovePageState, setMineApproveDetailParams, setFlowWorkOrderManagementPageState, setFlowWorkOrderManagementDetailParams } from 'src/pageTabs/access-request/accessRequestSlice'

export const MyApprovalRequestDetailPage = () => {
  const dispatch = useDispatch();
  const { t } = useTranslation();
  const { mineApproveDetailParams, flowWorkOrderManagementDetailParams } = useSelector((state) => state.accessRequest);
  // 我的审批详情和工单管理详情是共用的组件,所以参数需要判断
  const location = flowWorkOrderManagementDetailParams?.flag === 'worksheetManage' ? flowWorkOrderManagementDetailParams : mineApproveDetailParams;
  const id = location?.id;

  //模块权限查询
  const { permissionList } = useSelector((state) => state?.login);
  
  const [isRefresh, setIsRefresh] = useState(false);
  
  let modulePermissionObj: { isOnlyRead: boolean; roleNameList: string[] } = useMemo(() => {
    return getCurrentModulePermissionByUrl(permissionList, 'FLOW_APPLY') || { isOnlyRead: false, roleNameList: [] };
  }, [JSON.stringify(permissionList)])

  const {
    data: thinData = {},
    run: thinRun,
    loading: thinLoading,
  } = useRequest(
    (params) => {
      if (location?.originType === 'message') {
        delete params?.originType;
        return getInventoryDataForMessage(params);
      } else {
        return getInventoryData(params);
      }
    },
    {
      manual: true,
    }
  );
  //详情
  const { data: list = [], run: getFlowSteps } = useRequest(getNewFlowProcess, {
    manual: true,
  });

  //工单状态
  const { data: flowStatus = {}, run: getFlowStatusRun } = useRequest(
    getFlowStatus,
    {
      manual: true,
    }
  );

  //落权
  const { data: powerData = [], run: getPowerRun } = useRequest(
    getPowerRemarkInfo,
    {
      manual: true,
    }
  );

  //备注
  const { data: remarkData = [], run: getRemarkInfoRun } = useRequest(
    getFlowRemarkInfo,
    {
      manual: true,
    }
  );
  //撤回备注
  const { data: retractRemarks = [], run: runGetRetractRemarks } = useRequest(
    getWithdrawRemarkInfo,
    {
      manual: true,
    }
  );

  useEffect(() => {
    if (location?.mainUUID) {
      thinRun({
        flowMainUUID:
          location?.originType === 'message'
            ? location?.flowUUID
            : location?.mainUUID,
        connectionIds: location?.connectionIds,
        canOperation:
          location?.canOperation === false || location?.canOperation === true
            ? location?.canOperation
            : null,
      });
      getFlowSteps(location?.mainUUID);
      getFlowStatusRun(location?.mainUUID);
      getRemarkInfoRun(location?.mainUUID);
      getPowerRun(location?.mainUUID);
    }

  }, [location, isRefresh]);

  useEffect(() => {

    if (flowStatus?.approvedResult === 'withdraw' && location?.mainUUID) {

      runGetRetractRemarks(location?.mainUUID)
    }

  }, [flowStatus?.approvedResult])

  // 流程-工单管理
  const handleToFlowWorkOrder = () => {
    dispatch(setFlowWorkOrderManagementPageState(''))
    dispatch(setFlowWorkOrderManagementDetailParams({ curTab: flowWorkOrderManagementDetailParams?.curTab }))
  }
  // 流程-我的审批
  const handleToApprove = () => {
    dispatch(setMineApprovePageState(''))
    dispatch(setMineApproveDetailParams({ curTab: mineApproveDetailParams?.curTab }))
  }

  const renderBreadcrumb = () => {
    if(location?.flag === 'worksheetManage'){
      return <span className='breadcrumbLink' onClick={handleToFlowWorkOrder}>{t('flow_work_order_management')}</span>
    }
    return <span className='breadcrumbLink' onClick={handleToApprove}>{t('flow_my_approvals')}</span>
  };

  const breadcrumbData = [
    { title: t(MENU_FLOW) },
    {
      title: renderBreadcrumb(),
    },
    {
      separator: '/',
      title: t('flow_approval_form'),
    },
  ];
  
  return (
    <div className={styles.myApplyRequestDetailWrap}>
      <SimpleBreadcrumbs items={breadcrumbData} />
      <Spin spinning={thinLoading}>
        <div className={styles.content}>
          <Descriptions>
            <Descriptions.Item label={t('flow_application_number')}>
              <span className={styles.oddBgText}>{id}</span>
            </Descriptions.Item>
          </Descriptions>
          {!(
            NO_NEED_TO_LAND_TYPES.includes(location?.priGranType) ||
            NO_NEED_TO_LAND_TYPES.includes(thinData?.flowType)
          ) && (
              <ErrorBoundary>
                {/* 申请资源 */}
                {
                  (location?.priGranType === 'FAT' ||  thinData?.flowType === 'FAT') ?
                  <AppliedResourceList
                    flowMainUUID={id}
                    data={thinData}
                    flowUUID={
                      location?.originType === 'message'
                      ? location?.flowUUID
                      : location?.mainUUID
                    }
                  />
                  :  <ResourceBidPage
                      data={thinData}
                      priGranType={location?.priGranType}
                    />
                }
              </ErrorBoundary>
            )}
          <ErrorBoundary>
            {/* 申请信息 */}
            <ApplicationsInfoPag
              {...thinData}
              priGranType={location?.priGranType || thinData?.flowType}
              applyId= {location?.applyId}
            />
          </ErrorBoundary>
          <ErrorBoundary>
            {/* 工单流转 */}
            <OrderFlowPage dataList={list} flowStatus={flowStatus} />
          </ErrorBoundary>
          <ErrorBoundary>
            {/* 审批信息 */}
            {(location?.flag === 'worksheetManage' || //工单管理
              location?.applyStatus !== 'pending') && ( //子工单
                <ApprovalRemarksPag 
                  moduleType='myApproval'
                  remarkData={remarkData} 
                  record={location}
                  flowStatus={flowStatus}
                  isOnlyReadMenu={modulePermissionObj?.isOnlyRead || true} 
                  onRefresh={() => setIsRefresh(!isRefresh)}
                />
              )}
          </ErrorBoundary>
          {!(
            NO_NEED_TO_LAND_TYPES.includes(location?.priGranType) ||
            NO_NEED_TO_LAND_TYPES.includes(thinData?.flowType)
          ) && (
              <ErrorBoundary>
                {/* 撤回信息|落权信息 */}
                <WeightRemarksPag remarkData={powerData} 
                isShowWeightingOperation = {
                  ((Boolean(location?.canOperation) && !location?.originType) ||
                  (Boolean(location?.canOperation) &&
                    location?.type === 'power' &&
                    location?.originType === 'message'))
                }
                uuid={id}
                />
              </ErrorBoundary>
            )}
          {flowStatus?.approvedResult === 'withdraw' && (
            <ErrorBoundary>
              {/* 撤回信息|落权信息 */}
              <WeightRemarksPag remarkData={retractRemarks} withdrawalState />
            </ErrorBoundary>
          )}
        </div>
      </Spin>
    </div>
  );
};
