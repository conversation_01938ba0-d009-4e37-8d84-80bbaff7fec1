import React, { FC, useState } from 'react'
import { Button, Card, Form, Input, message } from 'antd'
import classnames from 'classnames'
import { useForm } from 'antd/lib/form/Form'
import { useRequest, useDispatch } from 'src/hook'
import { dealWithAccessPermission } from 'src/api'
import { useTranslation } from 'react-i18next'
import { setMineApprovePageState, setMineApproveDetailParams } from 'src/pageTabs/access-request/accessRequestSlice'
import styles from '../index.module.scss'

const FormLayout = {
  labelCol: { span: 0 },
  wrapperCol: { offset: 0, span: 8 },
}

interface ListProps {
  uuid: string
  className?: any
}

export const WeightingPage: FC<ListProps> = (props) => {
  const dispatch = useDispatch()
  const { uuid, className } = props
  const [form] = useForm()
  const { t } = useTranslation()
  const [approveType, setApproveType] = useState<
    'rejected' | 'fullilled' | 'pending'
  >('pending')


  // 流程-我的审批
  const handleToApprove = () => {
    dispatch(setMineApprovePageState(''))
    dispatch(setMineApproveDetailParams({}))
  }

  // 落权
  const { run: weightingRun } = useRequest(dealWithAccessPermission, {
    manual: true,
    onSuccess(data) {
      if (approveType === 'fullilled') {
        message.success(t('flow_approved_success'))
      } else {
        message.success(t('flow_rejected_success'))
      }
      handleToApprove()
    }
  })

  // 同意
  const agreeBtn = () => {
    setApproveType('fullilled')
    let params = {
      flowUUID: uuid,
      powerStatus: 'already',
      remarks: form.getFieldValue('approvedComment')
    }
    //@ts-ignore
    weightingRun(params).finally(() => {
      setApproveType('pending')
    })

  }

  const isEmpty = () => {
    setApproveType('rejected')
    let params = {
      flowUUID: uuid,
      powerStatus: 'rejected',
      remarks: form.getFieldValue('approvedComment')
    }
    //@ts-ignore
    weightingRun(params).finally(() => {
      setApproveType('pending')
    })
  }
  return (
    <Card
      title={t('flow_authorization_info')}
      className={classnames(styles.borderShow, styles.mt27, styles.mb27, styles.detailCard, className)}
      actions={[
        <div>
          <Button type='primary' onClick={() => agreeBtn()}>{t('flow_agree_to_auth')}</Button>
          <Button onClick={() => isEmpty()}>{t('flow_reject_auth')}</Button>
        </div>
      ]}
    >
      <Form form={form} {...FormLayout}>
        <Form.Item
          label={t('flow_remark')}
          name="approvedComment"
        // rules={[{ required: true }, {min: 1, max: 100}]}
        >
          <Input.TextArea allowClear maxLength={100} />
        </Form.Item>
      </Form>
    </Card>
  )
}
