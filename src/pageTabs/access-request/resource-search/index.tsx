import React from 'react'
import ErrorBoundary from 'antd/lib/alert/ErrorBoundary'
import { MENU_FLOW } from 'src/constants';
import { SimpleBreadcrumbs } from 'src/components'
import { SearchTablePage } from './search-table'
import { useTranslation } from 'react-i18next'
import { setMineApplyPageState, setMineApplyDetailParams } from 'src/pageTabs/access-request/accessRequestSlice'
import { useDispatch } from 'src/hook'
import styles from './index.module.scss'

export const MyApplyResourceSearchPage = () => {
  const dispatch = useDispatch()
  const { t } = useTranslation();
  
  const handleRenderToApply = () => {
    dispatch(setMineApplyPageState('apply'))
    dispatch(setMineApplyDetailParams({}))
  }

  const breadcrumbData = [
    { title: t(MENU_FLOW) },
    {
      title: <span className='breadcrumbLink' onClick={handleRenderToApply}>{t('flow_my_request')}</span>,
    },
    {
      separator: '/',
      title: t('flow_resource_search')
    }
  ];

	return (
		<div className={styles.resourceWrap}>
			<SimpleBreadcrumbs items={breadcrumbData} />
			<div className={styles.content}>
        <ErrorBoundary>
				  <SearchTablePage />
        </ErrorBoundary>
			</div>
		</div>
	)
}
