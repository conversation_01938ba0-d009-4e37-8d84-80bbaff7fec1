
import moment from 'moment';
import type { EffectStatusType } from './constants';

export const getEffectiveStatus = (s:string,e: string, a?: string ): EffectStatusType => {

   const sTime: number = moment(s).valueOf();
   const eTime: number = moment(e).valueOf();
   const curTime: number = moment().valueOf();
   if ((a === 'exportTask') || (!sTime && !eTime)) {  // 导出申请 没有时间，一直处于生效状态
    return 'flow_active'
   }
   if (curTime < sTime) {
    return 'flow_pending_activation';
   }else if (sTime <= curTime && curTime <= eTime) {
    return 'flow_active'
   }else {
    return 'flow_expired'
   }
}