.connectionSettingCard {
    border-radius: 4px;
    flex: 1;
    height: calc(100vh - 200px);

    .connectionTitle {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .settingContent {
        display: flex;
        align-items: flex-start;
        justify-content: space-between;
        min-height: 300px;
        height: 100%;

        .settingItem {

            .itemHeader {
                height: 48px;
                line-height: 48px;
                color: #667084;
                padding: 0 24px !important;
                background: #F7F9FC;
                margin-bottom: 6px;

                .clearIcon {
                    color: #3357ff;
                    margin-left: 4px;
                }
            }

            .itemBody {
                padding: 0 24px !important;

                .bodyCol {
                    padding: 7px 0;

                    .fontColor {
                        color: #0F244C;
                    }

                    .deleteLine {
                        text-decoration: line-through;
                    }
                }
            }
        }
    }
}

.permissionTooltip {
    max-width: none;
    max-height: 200px;
    overflow-y: scroll;
}