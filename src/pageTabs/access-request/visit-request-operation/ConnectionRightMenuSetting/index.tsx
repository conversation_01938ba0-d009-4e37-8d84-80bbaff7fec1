import React, { useState, useEffect } from "react";
import * as _ from "lodash";
import classnames from 'classnames'
import { DeleteOutlined, ExclamationCircleOutlined, ShoppingCartOutlined } from '@ant-design/icons'
import { useSelector, useDispatch } from 'src/hook';
import { Card, Row, Col, message, Checkbox, Radio, Spin, Tooltip, Button, Badge, Alert } from "antd";
import {
  getPermissionsPanelUpObject,
  getPermissionList,
  getPermissionTemplate,
  IObjectPermissionRes,
  getAutomaticObjectPermission,
} from 'src/api';
import { updateConnectionObjectPermissions } from '../visitRequestOperateSlice'
import type { CheckboxValueType } from "antd/es/checkbox/Group";
import { useRequest, } from "src/hook";
import { useTranslation } from 'react-i18next';
import styles from "./index.module.scss";
import { setStr } from "src/util/connectionManage";
import { setMineApplyPageState, setMineApplyDetailParams } from 'src/pageTabs/access-request/accessRequestSlice'
import { Iconfont } from "src/components";

const ConnectionRightMenuSetting = ({
  isGroupTab,
  selectTreeItem,
  roleId,
  isEdit = false,
  needRefresh,
  cartNumber,
  viewAppliedDetail = false, //仅查看
  permissionCollectionVOS,
  updatePermissionCollectionVOS,
}: {
  isGroupTab?: boolean;
  isEdit?: boolean;
  selectTreeItem?: any;
  needRefresh?: boolean;
  cartNumber?: boolean;
  roleId?: string | number;
  viewAppliedDetail?: boolean;
  permissionCollectionVOS?: any;
  updatePermissionCollectionVOS?: (params: any) => void
}) => {

  const { connection, dataSourceType } = selectTreeItem || {};
  const { connectionType } = connection || {};
  const dispatch = useDispatch();
  const { connectionObjectPermissions } = useSelector(state => state.visitRequestOperate)

  const { t } = useTranslation()

  const radioTypes = ["permissionTemplate", "roleConnection"];
  //页面复选框存储内容{key：[]}
  const [changedObjectTypes, setChangedObjectTypes] = useState<any>({});
  const [permissionsOptsSet, setPermissionsOptsSet] = useState<any>();
  const [hasPermissionData, setHasPermissionData] = useState<any>();  // 已有权限数据

  const [list, setList] = useState<any>([]);

  const {
    loading: groupTabPermissionLoading,
    run: runGetGroupTabPermission
  } = useRequest<IObjectPermissionRes>(
    getAutomaticObjectPermission, {
    manual: true,
    onSuccess(data, params) {
      dealPermissionData(data);
    },
  })

  const { data: permPanelUpObjectData, loading, run: runGetAutomaticObjectPermission } = useRequest<IObjectPermissionRes>(
    getPermissionsPanelUpObject, {
    manual: true,
    onSuccess(data, params) {
      dealPermissionData(data);
    },
  })

  const dealPermissionData = (data: any) => {
    setList(data);
    const initCheckedValues = getInitCheckedValues(data?.operationsVoList)

    const cloneAllChangedObjectSetting = _.cloneDeep(connectionObjectPermissions)
    const curItem = cloneAllChangedObjectSetting?.[selectTreeItem?.key]

    if (!_.isEmpty(curItem)) {
      dispatch(updateConnectionObjectPermissions({
        ...cloneAllChangedObjectSetting,
        [selectTreeItem?.key]: curItem
      }))
      setHasPermissionData(curItem)
    } else {
      setHasPermissionData(initCheckedValues)
    }
    setChangedObjectTypes([])
  }

  // 查询不同数据源下对应的所有权限信息
  const { run: getPermissionTemplateEnum } = useRequest(
    getPermissionTemplate,
    { manual: true },
  )

  // 获取权限等级列表
  const { run: getPermissionListEnum } = useRequest(
    getPermissionList,
    {
      manual: true,
      onSuccess: (res: any) => {
        getPermissionTemplateEnum(dataSourceType).then((templateRes: any) => {
          let options: { [key: string]: any[] } = {};
          res?.forEach((item: any) => {
            if (item?.id) {
              let templateOperationsSetTmp = item?.templateOperationsSet;
              options[item?.id] = templateOperationsSetTmp?.flatMap((optionsItem: any) => {
                const matchedTemplate = templateRes?.find((templateItem: any) => templateItem?.objectType === optionsItem?.objectType);
                if (matchedTemplate) {
                  return optionsItem?.operations?.map((operation: any) => {
                    const matchedOperation = matchedTemplate?.operations?.find((templateOperation: any) => templateOperation?.operation === operation);
                    if (matchedOperation) {
                      return `${operation}(${matchedOperation?.operationName})`;
                    }
                    return operation;
                  });
                } else {
                  return [];
                }
              });
            }
          });
          setPermissionsOptsSet(options)
        })
      }
    },
  )

  const getInitCheckedValues = (list: any[]) => {
    let obj: any = {};
    list?.map((item: any) => {
      if (!radioTypes.includes(item.objectType)) {
        let checkedTypes: string[] = [];
        item?.operations?.map((o: any) => {
          if (o?.hasSelected === true) {
            checkedTypes.push(o.operation)
          }
        })
        if (!_.isEmpty(checkedTypes)) {
          obj[item.objectType] = checkedTypes;
        }

      } else {
        const radioCheckedItems: any[] = []
        item?.operations?.forEach((o: any) => {
          if (o?.hasSelected === true) {
            radioCheckedItems.push(o.operation)
          }
        })
        obj[item.objectType] = radioCheckedItems;
      }
    })
    return obj
  }

  const queryPermissionsGroupTab = () => {
    const { connection, dataSourceType, nodePathWithType, roleId } = selectTreeItem || {};
    const { connectionType } = connection || {};
    if (selectTreeItem.newNodeType === "datasource") {
      runGetGroupTabPermission({
        roleId,
        dataSourceType: selectTreeItem.id,
        flowMainUUID: selectTreeItem.flowMainUUID
      });
    }else if (selectTreeItem.newNodeType === "group") {
      runGetGroupTabPermission({
        roleId,
        groupId: selectTreeItem.id,
        dataSourceType: dataSourceType || connectionType,
        flowMainUUID: selectTreeItem.flowMainUUID
      });
    }else if (nodePathWithType && (connectionType || dataSourceType)) {
      runGetGroupTabPermission({
        roleId,
        nodePath: nodePathWithType,
        dataSourceType: dataSourceType || connectionType,
        flowMainUUID: selectTreeItem.flowMainUUID
      })
    }
  }

  const queryPermissionsInstanceTab = () => {
    const { connection, dataSourceType, nodePathWithType } = selectTreeItem || {};
    const { connectionType } = connection || {};

    if (selectTreeItem?.newNodeType === "datasource") {
      runGetAutomaticObjectPermission({
        roleId,
        dataSourceType: selectTreeItem.id
      });
    } else if (nodePathWithType && (connectionType || dataSourceType)) {
      runGetAutomaticObjectPermission({
        roleId,
        nodePath: nodePathWithType,
        dataSourceType: dataSourceType || connectionType
      })
    }
  }

  useEffect(() => {
    if (isGroupTab) {
      queryPermissionsGroupTab();
    } else {
      queryPermissionsInstanceTab();
    }
  }, [selectTreeItem.key, needRefresh, isGroupTab])

  useEffect(() => {
    if (dataSourceType) {
      getPermissionListEnum(dataSourceType)
    }
  }, [dataSourceType, getPermissionListEnum])

  const onChangeCheckbox = (
    checkedValues: CheckboxValueType[],
    type: string
  ) => {

    let cloneChangeObjectTypes = _.cloneDeep(changedObjectTypes);
    if (checkedValues?.length) {
      cloneChangeObjectTypes = {
        ...cloneChangeObjectTypes,
        [type]: checkedValues,
      }
    } else {
      //判空处理 过滤该条数据
      cloneChangeObjectTypes = Object.fromEntries(
        Object.entries(cloneChangeObjectTypes).filter(([itemType]) => itemType !== type)
      );
    }

    setChangedObjectTypes(cloneChangeObjectTypes);
  };

  const onCheckAllChange = (checked: boolean, type: string) => {

    const changedItem = list?.operationsVoList?.find((i: any) => i.objectType === type);
    const curAllTypes = changedItem?.operations?.filter((ope: any) => ope.canSelect)?.map((o: any) => o.operation);
    let cloneChangeObjectTypes = _.cloneDeep(changedObjectTypes);

    //过滤为空数据
    if (checked) {
      cloneChangeObjectTypes = {
        ...cloneChangeObjectTypes,
        [type]: curAllTypes
      }
    } else {

      const filteredChangeObjectTypes = Object.fromEntries(
        Object.entries(cloneChangeObjectTypes).filter(([itemType]) => itemType !== type)
      );
      cloneChangeObjectTypes = {
        ...filteredChangeObjectTypes,
      }
    }

    setChangedObjectTypes(cloneChangeObjectTypes);

  };

  const onChangeRadioStatus = async (value: string | null, type: string, valid?: boolean) => {

    const cloneChangeObjectTypes = _.cloneDeep(changedObjectTypes);
    let params: any;
    //过滤为空数据
    if (value) {
      params = {
        ...cloneChangeObjectTypes,
        [type]: value
      }
    } else {

      const filteredChangeObjectTypes = Object.fromEntries(
        Object.entries(cloneChangeObjectTypes).filter(([itemType]) => itemType !== type)
      );
      params = {
        ...filteredChangeObjectTypes,
      }
    }

    setChangedObjectTypes(params);

  }

  //获取指定字段的默认值
  const findSelectedOperationsWithFilterAndMap = (data: any) => {
    const result: any = {};
    data?.forEach((item: any)=> {
    if (item.objectType !== 'permissionTemplate') {
        item?.operations?.forEach((op: any) => {
            if (op?.hasSelected) {
                result[op?.operation] = true;
            }
        });
    }
    });
    return result
  } 

  const getUserToolVoValue = (nodeType: string, dataSourceType: string, connectionType: string, templateOperationsSet: any,) => {
    const otherSelectedOptions =  findSelectedOperationsWithFilterAndMap(list?.operationsVoList || [])
    let res: any = {
      jsonObject: _.isEmpty(otherSelectedOptions) ? null : otherSelectedOptions
    };
    if (nodeType === "datasource") {
      res = {
        ...res,
        dataSourceType: selectTreeItem?.nodeName,
        nodeName: selectTreeItem?.nodeName,
        nodePath: selectTreeItem?.nodePath,
        templateOperationsSet
      };
    } else {
      res = {
        ...res,
        dataSourceType: dataSourceType || connectionType,
        nodePathWithType: selectTreeItem?.nodePathWithType,
        nodeName: selectTreeItem?.nodeName,
        nodePath: selectTreeItem?.nodePath,
        templateOperationsSet
      };
    }
    return res;
  }

  const translateSubmitParams = (changedObjectTypes: any) => {

    //接口返回默认值
    const defalutSelectedPerms = list?.operationsVoList || [];
    const defaultTemplateIdItem = defalutSelectedPerms?.find((item: any) => item?.objectType === 'permissionTemplate')?.operations?.find((operation: any) => !!operation?.hasSelected);
    let permissionAndRoleParams: any = {
      //默认权限模板
      permissionSdtUserAddVo: {
        ...(selectTreeItem?.nodeType !== "datasource"? { nodePathWithType: selectTreeItem?.nodePathWithType,} : {}),
        dataSourceType: selectTreeItem?.dataSourceType || connectionType,
        nodeName: selectTreeItem?.nodeName,
        nodePath: selectTreeItem?.nodePath,
        templateIdOld: _.isEmpty(defaultTemplateIdItem) ? null : defaultTemplateIdItem?.operation
      }
    };
    let templateOperationsSet = [];
    // 选择了工具权限，则权限等级为 非必填
    if (
      !_.isEmpty(changedObjectTypes) && 
      !Object.keys(changedObjectTypes)?.some((item: any) => ['sdtMenu', 'resultSetOperation', 'exportTool'].includes(item)) && 
      !changedObjectTypes.hasOwnProperty('permissionTemplate')
    ) {
      return message.warning(t('flow_select_permissions'))
    }
    for (const i in changedObjectTypes) {
      const checkedValue = changedObjectTypes[i];

      switch (i) {
        case 'permissionTemplate':
          if (selectTreeItem?.nodeType === "datasource") {
            permissionAndRoleParams.permissionSdtUserAddVo = {
              templateId: checkedValue ? Number(checkedValue) : checkedValue,
              dataSourceType: selectTreeItem?.nodeName,
              nodeName: selectTreeItem?.nodeName,
              nodePath: selectTreeItem?.nodePath,
              templateIdOld: _.isEmpty(defaultTemplateIdItem) ? null : defaultTemplateIdItem?.operation
            }
          } else {
            permissionAndRoleParams.permissionSdtUserAddVo = {
              nodePathWithType: selectTreeItem?.nodePathWithType,
              templateId: checkedValue ? Number(checkedValue) : checkedValue,
              dataSourceType: selectTreeItem?.dataSourceType || connectionType,
              nodeName: selectTreeItem?.nodeName,
              nodePath: selectTreeItem?.nodePath,
              templateIdOld: _.isEmpty(defaultTemplateIdItem) ? null : defaultTemplateIdItem?.operation
            }
          }
          break;
        case 'roleConnection':
          if (selectTreeItem?.nodeType === "datasource") {
            permissionAndRoleParams.roleUpdateVo = {
                roleType: checkedValue,
                dataSourceType: selectTreeItem?.nodeName,
                nodeName: selectTreeItem?.nodeName,
                nodePath: selectTreeItem?.nodePath
                //好像是没有这个类型了 暂不增加
            }
          } else {
            permissionAndRoleParams.roleUpdateVo = {
              nodePathWithType: selectTreeItem?.nodePathWithType,
              roleType: checkedValue,
              dataSourceType: selectTreeItem?.dataSourceType || connectionType,
              nodeName: selectTreeItem?.nodeName,
              nodePath: selectTreeItem?.nodePath
            }
          }
          break;
        case 'sdtMenu':
        case 'resultSetOperation':
        case 'exportTool':
          templateOperationsSet.push({
            objectType: i, operations: checkedValue,
          })
          break
      }
    }
    //  更新
    const formattedChangedItem = {
      ...permissionAndRoleParams,
        userToolVo:
          getUserToolVoValue(selectTreeItem?.nodeType, dataSourceType, connectionType, templateOperationsSet)
    }

    //过滤为空数据
    if (_.isEmpty(changedObjectTypes)) {
      updatePermissionCollectionVOS?.([])
      return
    } else {
      updatePermissionCollectionVOS?.([formattedChangedItem])
    }

  }

  useEffect(
    () => {
      isEdit && translateSubmitParams(changedObjectTypes)

    },
    [changedObjectTypes],
  );


  const handleRenderToApplication = () => {
    dispatch(setMineApplyPageState('application'))
    dispatch(setMineApplyDetailParams({}))
  }

  return (
    <Spin spinning={isGroupTab? groupTabPermissionLoading : loading} style={{ padding: "10px 10px" }}>
      <Card
        title={<div className={styles.connectionTitle}>
          <div>{(viewAppliedDetail) ? t('flow_selected_permissions') : isGroupTab? selectTreeItem?.nodeName || "" : t('flow_select_permissions')} </div>
          {
            !viewAppliedDetail &&
            <Tooltip title={t('flow_resource_count')}>
              <Badge count={cartNumber} showZero size="small" className="mr20">
                <Button 
                  type="primary" 
                  icon={<ShoppingCartOutlined />}
                  className="guide-apply-search-list"
                  onClick={handleRenderToApplication}
                  >
                    {t('flow_request_list')}
                </Button>
              </Badge>
            </Tooltip>
          }
        </div>}
        bordered={false}
        headStyle={{ borderBottom: "1px solid #EBEEF6" }}
        bodyStyle={{ padding: "16px 28px", height: 'calc(100% - 80px)', overflowY: 'scroll' }}
        className={styles.connectionSettingCard}
      >
        {!permPanelUpObjectData?.canOperation && permPanelUpObjectData?.canOperationMessage && <Alert type="info" showIcon message={permPanelUpObjectData?.canOperationMessage} className="mb10"/>}
        <div className={`${styles.settingContent} guide-apply-search-content`}>
          {list?.operationsVoList?.map((item: any) => {
            // 导出功能非权限控制
            const exportCheckedAllDisabled: boolean = item?.objectType === "exportTool" && item?.operations.filter((i: any) => i.canSelect).length === 0
            // header的childNode
            const childNode: any = <Checkbox
              disabled={isEdit && !exportCheckedAllDisabled ? !list?.canOperation : true}
              checked={
                changedObjectTypes?.[item.objectType]?.length ===
                  item?.operations.filter((o: any) => o.canSelect)?.length
                  ? true
                  : false
              }
              onChange={(e: any) =>
                onCheckAllChange(e.target.checked, item?.objectType)
              }
            >
              {item?.objectTypeName}
            </Checkbox>
            // 根据导出功能 是否是根据权限控制来控制tooltip显隐
            const showTooltip = exportCheckedAllDisabled ?
              <Tooltip title={t('flow_other_control_methods')}>
                {childNode}
              </Tooltip> : childNode
            return <Row
              key={item?.objectType}
              className={styles.settingItem}
              style={{ width: `calc(100% / ${list?.operationsVoList?.length || 0})` }}
            >
              <Col span={24} className={styles.itemHeader}>
                <Row align="top" justify="start">
                  <Col span={24}>
                    {radioTypes.includes(item.objectType) ? (
                      <>
                        {item.objectTypeName}
                        <Tooltip title={t('flow_clear_selections')}>
                          {
                            isEdit && changedObjectTypes?.permissionTemplate &&
                            <DeleteOutlined className={styles.clearIcon} onClick={() => {
                              setChangedObjectTypes((values: any) => {
                                let cloneChangeObjectTypes = _.cloneDeep(values);
                                if (cloneChangeObjectTypes?.permissionTemplate) {
                                  delete cloneChangeObjectTypes?.permissionTemplate
                                  return { ...cloneChangeObjectTypes }
                                }
                                return values
                              })
                            }} />
                          }
                        </Tooltip>
                      </>
                    ) : showTooltip}
                  </Col>
                </Row>
              </Col>
              <Col span={24} className={styles.itemBody}>
                {radioTypes.includes(item.objectType) ? (
                  <Radio.Group
                    value={changedObjectTypes?.[item.objectType] || []}
                    disabled={isEdit ? list?.canOperation === false ? true : false : true}
                    onChange={(e: any) => {
                      const itemInfo = item?.operations?.find((operation: any) => operation?.childObjects?.length > 0);
                      onChangeRadioStatus(e.target.value, item?.objectType, _.isEmpty(itemInfo))
                    }}
                  >
                    <Row >
                      {item?.operations?.map((operation: any) => (
                        <Col span={24}
                          className={styles.bodyCol}
                          key={operation?.operation}>
                          <Radio value={operation?.operation} className={classnames(styles.fontColor, {
                            [styles.deleteLine]: operation?.childObjects?.length > 0
                          })}>
                            <Tooltip
                              title={
                                permissionsOptsSet &&
                                Object.keys(permissionsOptsSet).includes(operation?.operation) &&
                                (setStr(permissionsOptsSet[operation?.operation?.toString()]) || '-')
                              }
                              overlayClassName={
                                permissionsOptsSet &&
                                  Object.keys(permissionsOptsSet).includes(operation?.operation) &&
                                  !!permissionsOptsSet[operation?.operation?.toString()].join(",\n")
                                  ? styles.permissionTooltip : ''
                              }
                              overlayStyle={{ whiteSpace: 'pre-line' }}
                            >
                              {operation?.childObjects?.length > 0 ?
                                <>
                                  <Tooltip title={t('flow_user_permission_level')} placement="leftTop">
                                    <ExclamationCircleOutlined />
                                  </Tooltip>
                                  {operation?.operationName}
                                </>
                                : operation?.operationName}
                            </Tooltip>
                            {/* 添加已有权限标记 */}
                            {
                              hasPermissionData?.[item.objectType]?.includes(operation?.operation) &&
                              <Tooltip title={t('flow_repeat_submit')}>
                                &nbsp;<Iconfont type="icon-quanxianshitu" />
                              </Tooltip>
                            }
                          </Radio>
                        </Col>
                      ))}
                    </Row>
                  </Radio.Group>
                ) : (
                  <>
                    <Checkbox.Group
                      value={changedObjectTypes?.[item.objectType] || []}
                      onChange={(values: CheckboxValueType[]) =>
                        onChangeCheckbox(values, item?.objectType)
                      }
                    >
                      <Row>
                        {item?.operations?.map((operation: any) => {
                          // 文本导入非权限控制
                          const importCheckedDisabled: boolean = item?.objectType === "sdtMenu" && operation?.operation === 'CQ_DATA_IMPORT' && !operation?.canSelect
                          const disabled = isEdit && !exportCheckedAllDisabled && !importCheckedDisabled ? !list?.canOperation : true
                          const colChild = <Col span={24} key={operation?.operation} className={styles.bodyCol}>
                            <Checkbox value={operation?.operation} className={styles.fontColor} disabled={disabled}>
                              {operation?.operationName}
                              {/* 添加已有权限标记 */}
                              {
                                hasPermissionData?.[item.objectType]?.includes(operation?.operation) &&
                                <Tooltip title={t('flow_repeat_submit')}>
                                  &nbsp;<Iconfont type="icon-quanxianshitu" />
                                </Tooltip>
                              }
                            </Checkbox>
                          </Col>
                          const showTooltip = exportCheckedAllDisabled ?
                            <Tooltip title={t('flow_other_control_methods')} placement="bottomLeft">
                              {colChild}
                            </Tooltip> : colChild
                          return importCheckedDisabled ? null : showTooltip
                        })}
                      </Row>
                    </Checkbox.Group>
                    {
                      item?.objectType === "sdtMenu" && <Row>
                        {item?.operations?.filter((o: any) => o?.operation === 'CQ_DATA_IMPORT' && !o?.canSelect)?.map((operation: any) => {
                          return <Tooltip title={t('flow_other_control_methods')} placement="bottomLeft" key={operation?.operation}>
                            <Col span={24} key={operation?.operation} className={styles.bodyCol}>
                              <Checkbox value={operation?.operation} className={styles.fontColor} disabled={true}>
                                {operation?.operationName}
                              </Checkbox>
                            </Col>
                          </Tooltip>
                        })}
                      </Row>
                    }
                  </>
                )}
              </Col>
            </Row>
          })}
        </div>
      </Card>
    </Spin>
  );
};

export default ConnectionRightMenuSetting;