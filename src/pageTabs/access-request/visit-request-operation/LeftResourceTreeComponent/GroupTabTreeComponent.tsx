import React, { useCallback, useEffect, useMemo, useState } from 'react'
import { Tree } from 'antd'
import { Iconfont } from 'src/components'
import styles from './index.module.scss'
import classnames from 'classnames'
import * as _ from 'lodash';
import { useSelector, useDispatch } from 'src/hook';
import {
  setLoadedKeys,
  getTreeNodeChildrenGroupTab,
  refreshOnRootGroupTab,
  setSelectedNode
} from '../visitRequestOperateSlice';
import { ConnectionFailWarnImg } from 'src/components/ConnectionFailWarnImg';
import { nodeTypeIsPermission } from 'src/util/validateStr'
import { IQueryGroupNodesAutomicParams } from 'src/api'
import { useTranslation } from 'react-i18next'

interface IProps {
  searchValue: string;
  [p: string]: any
}


const GroupTabTreeComponent = (props: IProps) => {
  const {
    searchValue,
  } = props
  
  const { loadedKeys, newTreeD<PERSON>, selectedTreeNode} = useSelector(state => state.visitRequestOperate);
  const allFailedCountConnectionIds = useSelector((state) => state.login.allFailedCountConnectionIds);

  const dispatch = useDispatch();
  const { t } = useTranslation();
  const [expandedKeys, setExpandedKeys] = useState<any[]>([]);
  const [roleName, setRoleName] = useState<string>("");  // 权限集名称（ 模糊搜索, 可为空 )

  const initPaginationParams = useMemo(() => ({
    pageNo: 0,
    pageSize: 300000000,
  }), []);
 
  useEffect(() => {
    return () => {
      dispatch(setLoadedKeys([]));
    }
  },[dispatch])

  const requestTreeData = (params: IQueryGroupNodesAutomicParams) => {
    dispatch(refreshOnRootGroupTab(params))
  }

  useEffect(() => {
    requestTreeData({
      ...initPaginationParams,
      roleName: roleName,
      isFlow: true,
    })
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [initPaginationParams, roleName])

  useEffect(() => {
    if (nodeTypeIsPermission(selectedTreeNode?.nodePathWithType)) {
      handleLoadData(selectedTreeNode);
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  },[selectedTreeNode])

  const handleSelect = (_: any[], info: any) => {
    if (!info.selected) {
      return;
    }
    dispatch(setSelectedNode(info?.node as any))
  };

  const handleExpand = (newExpandedKeys: React.Key[]) => {
    // 收起时，过滤掉该组层级下所有被展开的节点
    setExpandedKeys(newExpandedKeys)
  }

  // 异步逐级加载数据 (连接及以下层级加载树内容)
  const handleLoadData = useCallback( async(node: any) => {
    const {
      id,
      roleId,
      nodePathWithType,
      nodeType,
      dataSourceType,
      connectionId,
      nodeName,
      nodePath,
      key,
    } = node
    const params = {
      connectionId: connectionId || id,
      connectionType: dataSourceType,
      nodeType,
      nodeName,
      nodePath,
      nodePathWithType,
      roleId,
      key,
      flow: true,
    }
    await dispatch(getTreeNodeChildrenGroupTab({ 
      params, 
      callback: (list) => {
        // 权限集层级: 展开节点，并选中第一个子节点
        if (nodeTypeIsPermission(params?.nodePathWithType) && list?.length > 0) {
          dispatch(setSelectedNode(list?.[0]));
          setExpandedKeys((item) => {
            if (!item?.includes(key) && key) {
              return [...item, key]
            }
            return item
          })
        }
      }
    }));
  // eslint-disable-next-line react-hooks/exhaustive-deps
  },[dispatch])

  // 生成tree搜索标题
  const generatorSearchTitle = (
    node: any
  ) => {
    const {
      title,
      nodeType,
      id,
      isFolder = false,
      dataSourceType
    } = node;


    return (
      <>
        <Iconfont
          className={classnames(styles.mr4, styles.color008dff)}
          type=
          {`${isFolder
              ? `icon-quanxianji`
              : nodeType?.endsWith("GROUP")
                ? "icon-shujukuwenjianjia"
                : nodeType === "connection"
                  ? `icon-${dataSourceType}`
                  : `icon-${nodeType}`
            } `}
        />
        <span className={styles.titleTxtWrap}>
          <span className={styles.titleTxt}>{title}</span>
          {
            nodeType === "connection" && allFailedCountConnectionIds?.includes(Number(id)) && 
            <ConnectionFailWarnImg />
          }
        </span>
      </>
    );
  };

  // 渲染tree title完整内容
  const treeTitleRender = (node: any) => {
    const result = (
      <div className={styles.treeTitleItem}>
        {generatorSearchTitle(
          node
        )}
      </div>
    );
    return result
  }

  // 递归遍历搜索
  const recursionSearch = (treeData : any[], searchValue: string) => {
    const newTreeData: any[] = [];
    treeData?.map((item: any) => {
      if (item?.title?.toLowerCase().includes(searchValue?.toLowerCase())) {
        newTreeData.push(item)
      }
      else if (item?.children && item?.children?.length > 0) {
        const children = recursionSearch(item?.children, searchValue)
        if (children?.length > 0) {
          newTreeData.push({ ...item, children })
        }
      }
    })
    return newTreeData
  }

  const filteredTreeData = useMemo(() => {
    setRoleName(searchValue);
    let cloneNewTreeData = _.cloneDeep(newTreeData) || [];
    return recursionSearch(cloneNewTreeData || [], searchValue)
  // eslint-disable-next-line react-hooks/exhaustive-deps
  },[searchValue, JSON.stringify(newTreeData)])

  return (
    <div className={`${styles.treeWrapper} guide-apply-search-sdt`}>
      {
        newTreeData?.length > 0 ? (
          <Tree
            className={styles.treeContent}
            titleRender={treeTitleRender} // 自定义渲染节点 --- 节点前图标的配置
            treeData={filteredTreeData} // treeNodes 数据
            onSelect={handleSelect} // 点击树节点触发 ---  控制右侧组件的切换
            selectedKeys={selectedTreeNode ? [selectedTreeNode?.key] : []}
            onExpand={handleExpand} // 展开/收起节点时触发 --- 控制节点下一级的显示
            expandedKeys={expandedKeys} // （受控）展开指定的树节点 
            loadData={handleLoadData} // 异步加载数据 --- handleExpand 后执行
            loadedKeys={loadedKeys}
            onLoad={(keys) => dispatch(setLoadedKeys(keys))}
          />
        ) : <div className='color667084 tc'>{t('flow_no_data')}</div>
      }
    </div>
  )
}

export default React.forwardRef(GroupTabTreeComponent);