import React, { useCallback, useEffect, useMemo, useState } from 'react'
import * as _ from 'lodash';
import classnames from 'classnames'
import { Tree, Tooltip, message } from 'antd'
import { Iconfont } from 'src/components'
import { useSelector, useDispatch } from 'src/hook';
import { getExpandKeysAboutContainSearchValue } from 'src/util';
import {
  setLoadedTreeNodeMap,
  setLoadedKeys,
  getTreeNodeChildren,
  setSelectedNode,
  setExpandedKeys
} from '../visitRequestOperateSlice';
import { useTranslation } from 'react-i18next';
import styles from './index.module.scss';
import { ConnectionFailWarnImg } from 'src/components/ConnectionFailWarnImg';

interface IProps {
  searchValue?: string;
  isBatchOperation: boolean;
  checkedTreeNodes: string[]
  setCheckedTreeNodes: (keys: string[]) => void;
  onCancelBatchAction: () => void;
  onOpenBatchAction: () => void;
}


const TreeComponent = (props: IProps) => {
  const {
    searchValue,
    isBatchOperation,
    checkedTreeNodes,
    setCheckedTreeNodes,
    onCancelBatchAction,
    onOpenBatchAction
  } = props
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const { loadedKeys, newTreeData, expendKeys, selectedTreeNode } = useSelector(state => state.visitRequestOperate);
  const allFailedCountConnectionIds = useSelector((state) => state.login.allFailedCountConnectionIds)

  const [treeHeight, setTreeHeight] = useState<number>(300)

  useEffect(()=>{
    queryTreeHeight()
    window.addEventListener('resize', queryTreeHeight)
    return () => {
      window.removeEventListener('resize', queryTreeHeight)
    }
  }, [])
	
  const queryTreeHeight = () => {
		const clientHeight = document.documentElement.clientHeight
    const treeHeight = clientHeight > 558 ? clientHeight - 335 : 300
    setTreeHeight(treeHeight)
  }
  
  useEffect(() => {

    return () => {
      dispatch(setLoadedKeys([]));
      dispatch(setLoadedTreeNodeMap({}))
    }

  }, [])


  const handleSelect = (item: any[], info: any) => {

    if (!info.selected) {
      return;
    }
    dispatch(setSelectedNode(info?.node as any))
  }

  // 异步逐级加载数据 (连接及以下层级加载树内容)
  const handleLoadData = useCallback(async (node: any) => {
    if (node?.newNodeType === 'datasource') {
      return
    }

    const { id, roleId, nodePathWithType, nodeType, dataSourceType, connectionId, nodeName,
      nodePath, key
    } = node

    const params = {
      connectionId: connectionId || id,
      connectionType: dataSourceType,
      nodeType,
      nodeName,
      nodePath,
      nodePathWithType,
      roleId,
      key
    }
    await dispatch(getTreeNodeChildren(params))

  }, [dispatch])

  const handleCheck = (checkedKeys: any, e: any) => {

    const { node, checked, checkedNodes } = e;
    //@ts-ignore
    if ((node?.dataSourceType !== checkedTreeNodes?.[0]?.dataSourceType) && checkedTreeNodes?.length) {
      return message.warning(t('flow_select_same_data_source'))

    }
    //@ts-ignore
    if ((node?.nodeType !== checkedTreeNodes?.[0]?.nodeType) && checkedTreeNodes?.length) {
      return message.warning(t('flow_select_same_type_elements'))

    }
    let cloneCheckedTreeNodes = _.cloneDeep(checkedTreeNodes);
    cloneCheckedTreeNodes = checkedNodes;
    setCheckedTreeNodes(cloneCheckedTreeNodes)
  }

  const generatorSearchTitle = (node: any) => {

    const { id, nodeName, title, nodeType, connection = {}, connectionInfos = [], testModel, dataSourceType } = node || {};

    const renderConnectionInfo = (node: any) => {
      
      return connectionInfos?.length
      ? connectionInfos?.map((i: any) => <div key={i?.ip}>{i?.ip + ':' + i?.port}</div>)
        
      : connection?.ip + ':' + connection?.port

    }

    return (
      <>
        <Iconfont
          className={classnames(styles.mr4, styles.color008dff, {
            [styles.colorf00]: testModel === 1 && nodeType === "connection",
            [styles.colorgreen]: !!testModel && testModel !== 1 && nodeType === "connection",
          })}
          type={`${nodeType === "datasource"
            ? `icon-connection-${nodeName}`
            : nodeType === "group"
              ? "icon-shujukuwenjianjia"
              : nodeType === "connection"
                ? `icon-${dataSourceType}`
                : `icon-${nodeType}`
            } `}
        />
        <span className={styles.titleTxtWrap}>
          <Tooltip title={nodeType === "connection" && renderConnectionInfo(node)}>
            <span className={styles.titleTxt}>{title}</span>
          </Tooltip>
          {["datasource", "group"].includes(nodeType) && `(${node?.children?.length || 0})`}
          {
            nodeType === "connection" && allFailedCountConnectionIds?.includes(Number(id)) && 
            <ConnectionFailWarnImg />
          }
        </span>
      </>
    );
  };

  // 渲染tree title完整内容
  const treeTitleRender = (node: any) => {
    const result = (
      <div className={styles.treeTitleItem}>
        {generatorSearchTitle(node)}
      </div>
    );
    return result
  }

  const matchKeyword = (target = '', substring = '') => {
    if (!target) return false
    return target.toLowerCase().indexOf(substring.toLowerCase()) > -1
  }


  // /* 实现全局节点搜索 */
  const filterNodesNotMatch = useCallback(
    (nodes: any[]): any[] =>
      nodes.filter((node) => {
        const ips = node?.connectionInfos?.length ? node?.connectionInfos?.map((item: any)=> item?.ip) : node?.connection?.ip;
        //后端搜索 筛选信息只有一条， 前端筛选模糊匹配
        const keywordHit = matchKeyword(`${node?.title}${ips}`, searchValue)
        if (!keywordHit && node.children) {
          node.children = filterNodesNotMatch(node.children)
        }
        return keywordHit || node.children?.length
      }),
    [searchValue],
  )

  const filteredTreeDataOnLocal = useMemo(() => {

    return searchValue ? filterNodesNotMatch(_.cloneDeep(newTreeData)) : newTreeData;
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [JSON.stringify(newTreeData), searchValue])

  useEffect(() => {
    // 只获取包含search 的父节点
    if (searchValue) {
      let keys = getExpandKeysAboutContainSearchValue(filteredTreeDataOnLocal, searchValue)
      dispatch(setExpandedKeys(keys))
    } else {
      dispatch(setExpandedKeys([newTreeData?.[0]?.key]))
    }
  }, [searchValue])

  return (
    <div className={`guide-apply-search-sdt`}>
      {
        filteredTreeDataOnLocal?.length > 0 &&
        <div className={styles.batchAction} >
          <div onClick={() => onOpenBatchAction()}>
            {!isBatchOperation && <Iconfont type='icon-batch-operation' />}
            <span className={classnames({ 'nonmodifiableColor': isBatchOperation })}>&nbsp;{t('flow_bulk_operation')}</span>
          </div>
          {
            isBatchOperation &&
            <div
              className="nonmodifiableColor"
              onClick={() => onCancelBatchAction()}
            >
              {t('flow_cancel')}
            </div>
          }
        </div>
      }
      {
        filteredTreeDataOnLocal?.length > 0 ? (
          <Tree
            className={styles.treeContent}
            titleRender={treeTitleRender}
            treeData={filteredTreeDataOnLocal}
            onSelect={handleSelect}
            checkStrictly={true}
            selectedKeys={selectedTreeNode ? [selectedTreeNode?.key] : []}
            onExpand={(expandedKeys) => dispatch(setExpandedKeys(expandedKeys))}
            checkable={isBatchOperation}
            checkedKeys={checkedTreeNodes?.map((node: any) => node.key) || []}
            expandedKeys={expendKeys}
            loadData={handleLoadData}
            loadedKeys={loadedKeys || []}
            onCheck={handleCheck}
            onLoad={(keys) => dispatch(setLoadedKeys(keys))}
            height={treeHeight}
          />
        ) : <div className='color667084 tc'>{t('flow_no_data')}</div>
      }
    </div>
  )
}
export default TreeComponent