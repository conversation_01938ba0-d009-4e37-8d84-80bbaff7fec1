.mr4 {
	margin-right: 4px;
}
.mr8 {
	margin-right: 8px;
}
.mr10 {
	margin-right: 10px;
}
.mr20 {
	margin-right: 20px;
}
.color008dff {
	color: #3357ff;
}
.colorgreen {
	color: green;
}
.colorf00 {
	color: #f00;
}
.batchAction {
  margin-bottom: 4px;
  background-color: #fff;
  height: 32px;
  line-height: 32px;
  font-size: 12px;
  color: #3357ff;
  border-radius: 4px;
  padding:0 12px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
}
.treeWrapper {
  height: calc(100vh - 290px);
  overflow-y: auto;
}
.treeContent {
  :global {
    .ant-tree-treenode {
      width: 100%;
      padding: 0;
      border-bottom: 4px solid #F7F9FC;
      background-color: #fff;
      height: 32px;
      align-items: center;
      border-radius: 4px;
    }
    .ant-tree-node-content-wrapper {
      flex: 1;
    }

    .ant-tree-node-content-wrapper:hover,
    .ant-tree-treenode:hover {
      background-color: #d6e5ff;
    }
    .ant-tree-node-content-wrapper {
      transition: none;
    }
    .ant-tree-treenode-selected {
      background-color: #d6e5ff;
    }
    .ant-tree-treenode-selected .ant-tree-switcher,
    .ant-tree-treenode
      .ant-tree-node-content-wrapper.ant-tree-node-selected {
      color: initial;
      background-color: #d6e5ff;
    }
  }
}
.treeTitleItem {
  display: flex;
  align-items: center;
  .titleTxtWrap {
    display: flex;
    flex: 1;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    .titleTxt {
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
}