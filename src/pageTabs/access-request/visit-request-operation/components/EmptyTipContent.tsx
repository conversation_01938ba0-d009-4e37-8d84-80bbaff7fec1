import React from "react";
import { ShoppingCartOutlined } from '@ant-design/icons';
import { Card, Button, Badge, Tooltip } from "antd";
import { useTranslation } from 'react-i18next';
import styles from './index.module.scss';
import { useDispatch } from 'src/hook';
import { setMineApplyPageState, setMineApplyDetailParams } from 'src/pageTabs/access-request/accessRequestSlice'

export const EmptyTipContent = ({
  cartNumber
}: {
  cartNumber: number
}) => {

  const { t } = useTranslation();
  const dispatch = useDispatch();

  const handleRenderToApplication = () => {
    dispatch(setMineApplyPageState('application'))
    dispatch(setMineApplyDetailParams({}))
  }

  return (
    <Card
      title={<div className={styles.emptyTipTitle}>
        <div>{t('flow_select_permissions')}</div>
        <Tooltip title={t('flow_resource_count')}>
          <Badge count={cartNumber} showZero size="small" className="mr20">
            <Button
              type="primary"
              icon={<ShoppingCartOutlined />}
              onClick={handleRenderToApplication}
            >
              {t('flow_request_list')}
            </Button>
          </Badge>
        </Tooltip>
      </div>}
      bordered={false}
      headStyle={{ borderBottom: "1px solid #EBEEF6" }}
      className={styles.emptyTipCard}
    >
      <div className={styles.emptyContent}>
        {t('flow_select_batch_resources')}
      </div>
    </Card>
  )
}