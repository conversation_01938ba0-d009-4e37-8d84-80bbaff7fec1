.mt10 {
	margin-top: 10px;
}
.mt20 {
	margin-top: 20px;
}
.mt30 {
	margin-top: 30px;
}
.mr4 {
	margin-right: 4px;
}
.mr8 {
	margin-right: 8px;
}
.mr10 {
	margin-right: 10px;
}
.mr20 {
	margin-right: 20px;
}
.mb2 {
	margin-bottom: 2px;
} 
.mb4 {
	margin-bottom: 4px;
} 
.mb10 {
	margin-bottom: 10px;
}
.mb20 {
	margin-bottom: 20px;
}
.ml10 {
	margin-left: 10px;
}
.ml20 {
	margin-left: 20px;
}
.flex1 {
	flex: 1
}
.flexItem {
	display: flex;
	align-items: center;
}
.colore999 {
	color: #999;
}
.color008dff {
	color: #3357ff;
}
.colorgreen {
	color: green;
}
.colorf00 {
	color: #f00;
}
.tooltipStyle {
	:global {
		.ant-tooltip-inner {
			width: 500px;
		}
	}
	.tooltipTitle {
		text-align: center;
	}
}

.automaticAuthorizationWrap {
	height: calc(100vh - 30px - 48px);
  overflow-y: hidden;
	padding: 0 10px 10px;
  .breadcrumbsWrap {
    padding: 0px 10px;
    position: sticky;
    top: 0px;
    background-color: white;
  }
	.breadcrumb {
		padding: 10px;
		font-size: 16px;
	}
	.content {
		min-height: 300px;
		display: flex;
		.leftWrap,
		.rightWrap,
		.rightSelectedResourceWrap {
			background-color: #F7F9FC;
			border-radius: 4px;
			height: calc(100vh - 110px);
		}
		.leftWrap {
			padding: 16px 14px;
			margin-right: 10px;
			.addBtn {
				width: 100%;
				margin-bottom: 10px;
				display: flex;
				align-items: center;
				justify-content: center;
			}

			.treeWrap {
				max-height: calc(100vh - 258px);
    			overflow-y: auto;
				background-color: unset;
				:global {
					.ant-tree-treenode {
						width: 100%;
						padding: 0;
						margin-bottom: 4px;
						background-color: #fff;
						height: 32px;
						align-items: center;
						border-radius: 4px;
					}
					.ant-tree-node-content-wrapper {
						flex: 1;
					}

					.ant-tree-node-content-wrapper:hover,
					.ant-tree-treenode:hover {
						background-color: #d6e5ff;
					}
					.ant-tree-node-content-wrapper {
						transition: none;
					}
					.ant-tree-treenode-selected {
						background-color: #d6e5ff;
					}
					.ant-tree-treenode-selected .ant-tree-switcher,
					.ant-tree-treenode
						.ant-tree-node-content-wrapper.ant-tree-node-selected {
						color: initial;
						background-color: #d6e5ff;
					}
				}
			}
			.treeTitleItem {
				display: flex;
				align-items: center;
				.titleTxt {
					flex: 1;
					text-overflow: ellipsis;
					white-space: nowrap;
					overflow: hidden;
				}
			}
		}
		.rightWrap {
			flex: 1;
			padding: 16px 20px 20px;
			
			.addRequestFooter{
				z-index: 100;
				height: 48px;
				line-height: 48px;
			  position: absolute;
				bottom: 0;
				width: 100%;
				padding: 0 30px;
				text-align: right;
				background-color: #fff;
				box-shadow: 0px 4px 16px 0px rgba(0, 0, 0, 0.16);
				.addRequestBtn {
					background-color: #F0F1F5;
					display: inline-block;
					height: 32px;
					padding: 0 13px;
					line-height: 32px;
					border-radius: 4px;
					cursor: pointer;
				}
				.hightlightBtn {
					background-color: #3357ff;
					color: #fff;
				}
			}
			.contentStyle {
				position: relative;
				background-color: #fff;
				height: calc(100vh - 168px);
				.title {
					margin-bottom: 20px;
					height: 56px;
					padding: 12px 0px;
					display: flex;
					align-items: center;
					justify-content: space-between;
					border-bottom: 1px solid #e5e5e5;
					.boldFont{
						font-weight: bold;
						font-size: 18px;
					}
				}
			}
		}
		.rightSelectedResourceWrap {
			width: 250px;
			padding: 16px 16px 16px 0;
      .resourceContainer {
				background-color: #fff;
				height: 100%;
				position: relative;
          .resourcHeader {
						padding: 10px 20px;
						border-bottom: 1px solid #e5e5e5;
					}
					.resourceContent {
						max-height: calc(100vh - 240px);
						overflow: auto;
				  	background-color: unset;
						padding: 10px 10px 0 0;
						.resourceEmpty {
							margin-top: 65px;
						}
						.resourceFooter {
							position: absolute;
							text-align: right;
							bottom: 0;
							width: 100%;
							padding: 10px 24px;
							border-top: 1px solid #e5e5e5;
							background-color: #fff;
						}
					}
			}
		}
	}
}
.searchBtn {
	width: 100%;
	:global {
		.ant-input-search-icon {
			margin-left: 4px;
			padding: 0 9px 0 0;
		}
		.ant-input-clear-icon {
			padding: 4px;
			color: rgba(0,0,0,0.52);
		}
	}
}
.userContent {
	display: inline-block;
	width: 320px;
	height: 200px;
	overflow-y: auto;
	border-radius: 4px;
	padding: 4px 4px;
	border:1px solid #d9d9d9;
	:global {
		.ant-tag {
			margin: 2px 0 2px 2px;
		}
	}
}

.addUserModal {
	.content{
		display: flex;
		margin-bottom: 20px;
		padding-right: 12px;
		height: 370px;
		.left{
			width: 350px;
			.treeWrap {
				padding-right: 20px;
				max-height: 300px;
				overflow-y: auto;
			}
		}
		.right{
			padding-left: 16px;
			width: 222px;
			border-left: 1px solid #e5e5e5;
			.userWrap {
				padding-right: 20px;
				max-height: 338px;
				overflow-y: auto;
				.selectItem {
					margin-bottom: 10px;
					display: flex;
					align-items: center;
					justify-content: space-between;
				}
			}
		}
		.title{
			margin-bottom: 10px;
			font-weight: 600;
		}
	}
	.radioGroupWrap {
		width: 100%;
		max-height: 120px;
		overflow-y: auto;
		:global {
			.ant-radio-wrapper {
				max-width: 136px;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
			}
		}
	}
	.searchUserListWrap {
		padding-right: 20px;
		max-height: 300px;
		overflow-y: auto;
	}
}

.resizableBox {
	height: 100% !important;
}
.resizeHandle {
	position: absolute;
	right: -3px;
	top: calc(50% - 24px);
	font-size: 16px;
	cursor: col-resize;
	color: rgba(0,0,0, 0.85);
}
