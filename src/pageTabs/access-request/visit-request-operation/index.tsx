/**
 * 流程-访问申请
 */
import React, { useState, useEffect, useMemo } from "react"
import { SearchOutlined } from '@ant-design/icons'
import * as _ from 'lodash';
import classnames from 'classnames'
import { Spin, Input, message, Tabs } from 'antd'
import { useLocation } from 'react-router-dom'
import { ResizableBox, ResizableProps } from 'react-resizable'
import {
  getCartConnection,
  addFlowRequisitionToCart,
  getApplyResourceSearchSetting
} from 'src/api'
import { useRequest, useDispatch, useSelector } from 'src/hook'
import { SimpleBreadcrumbs, Iconfont, RenderTourStepContent, STEP_OPTION } from 'src/components'
import { setGuideSteps, setGuideVisible } from 'src/pageTabs/SceneGuideModal/guideSlice';
import {
  setNewTreeData,
  fetchConnections,
  setResourceTotal,
  resetPermissionCollections,
  resetConnectionObjectPermissions,
} from './visitRequestOperateSlice'
import ObjectLevelAuthorization from "./objectLevelAuthorization"
import ConnectionRightMenuSetting from './ConnectionRightMenuSetting'
import TreeComponent from './LeftResourceTreeComponent/TreeComponent'
import { EmptyTipContent } from "./components/EmptyTipContent";
import { useTranslation } from 'react-i18next';
import styles from './index.module.scss'
import { MENU_FLOW } from "src/constants";
import GroupTabTreeComponent from "./LeftResourceTreeComponent/GroupTabTreeComponent";
import { nodeTypeIsPermission } from "src/util/validateStr";
import { setMineApplyPageState, setMineApplyDetailParams } from 'src/pageTabs/access-request/accessRequestSlice'

interface IProps {
  [p: string]: any
}
const ResizableBoxProps: ResizableProps = {
  axis: 'x',
  width: 320,
  height: 0,
  minConstraints: [260, 0],
  maxConstraints: [620, 0],
}

const VisitRequestOperation = (props: IProps) => {
  const { t } = useTranslation();
  const dispatch = useDispatch()
  const location = useLocation<any>()
  // TODO: 已确认这个参数roleId没有继续使用,2429以后版本可直接删掉相关使用
  const roleId = location.state?.roleId

	const { guideUniqueIds } = useSelector(state => state.guide);

  const { selectedTreeNode, resourceTotal } = useSelector(state => state.visitRequestOperate);

  const [searchValue, setSearchValue] = useState<string>("");
  const [debouncedSearchValue, setDebouncedSearchValue] = useState('');

  const [permissionCollectionVOS, setPermissionCollectionVOS] = useState<any[]>([])

  const [rightWrapWidth, setRightWrapWidth] = useState<string>("")
  //是否刷新
  const [needRefresh, setNeedRefresh] = useState(false);
  //是否批量操作
  const [isBatchOperation, setIsBatchOperation] = useState(false)

  //批量选中数据类型
  const [checkedTreeNodes, setCheckedTreeNodes] = useState<any[]>([]);

  const handleRenderToApply = () => {
    dispatch(setMineApplyPageState('apply'))
    dispatch(setMineApplyDetailParams({}))
  }
  const breadcrumbData = [
    { title: t(MENU_FLOW) },
    {
      title: <span className='breadcrumbLink' onClick={handleRenderToApply}>{t('flow_my_request')}</span>,
    },
    {
      separator: '/',
      title: t('flow_access_req'),
    },
  ];

  const steps = [
    {
      target: '.guide-apply-search-sdt',
      content: RenderTourStepContent({
        title: t('flow_resource_list'),
        position: '1/3',
        detail: t('flow_select_resources_for_privilege_escalation')
       }),
      ...STEP_OPTION,
      placement: 'right-start'
    },
    {
      target: '.guide-apply-search-content',
      content: RenderTourStepContent({
        title: t('flow_select_permissions'),
        position: '2/3',
        detail: t('flow_add_resource_permissions_to_application')
       }),
      ...STEP_OPTION,
      hideBackButton: true,
      placement: 'left-start'
    },
    {
      target: '.guide-apply-search-list',
      content: RenderTourStepContent({
        title: t('flow_request_list'),
        position: '3/3',
        detail: t('flow_view_selected_resources_and_permissions')
       }),
      ...STEP_OPTION,
      hideBackButton: true,
      placement: 'left'
    }
  ]

  const [tabsActiveKey, setTabsActiveKey] = useState<string>('instanceTab');
  //购物车数量
  const { data: totalSchema, refresh: refreshCartNum } = useRequest(getCartConnection, {
    formatResult(res) {
      return res?.total || 0
    }
  })
  //是否开启 资源搜索页展示所有资源
  const { data: isShowAllResource } = useRequest(getApplyResourceSearchSetting)

  //加入购物车
  const { run: runAddRequisitionToCart } = useRequest(addFlowRequisitionToCart, {
    manual: true,
    onSuccess: () => {
      message.success(t('flow_added_to_application'));
      setPermissionCollectionVOS([]);
      dispatch(resetConnectionObjectPermissions());
      dispatch(resetPermissionCollections());
      setIsBatchOperation(false);
      setCheckedTreeNodes([]);
      setNeedRefresh(!needRefresh);
      refreshCartNum();
    }
  });

  useEffect(() => {
    handleLeftWrapResize();
  }, []);


  useEffect(() => {
    //每次只提交当前页申请 不做存储
    if (!isBatchOperation) {
      setPermissionCollectionVOS([]);
    }

  }, [selectedTreeNode?.key, isBatchOperation])

  useEffect(() => {
    dispatch(setNewTreeData([]));
  }, [tabsActiveKey])

  // treeData
  useEffect(() => {
    //isShowAllResource === undefined 禁止重复调用接口
    if (tabsActiveKey === 'groupTab' || isShowAllResource === undefined ) return;

    //增加参数
    if (isShowAllResource) {
      dispatch(fetchConnections())
    }else {
      dispatch(fetchConnections({
        ip: searchValue
      }))
    }
  }, [dispatch, isShowAllResource, searchValue, tabsActiveKey])

  useEffect(() => {

  },[])

  useEffect(() => {
    return () => {
      setTabsActiveKey('instanceTab');
      dispatch(setNewTreeData([]));
      if (!isShowAllResource) {
        dispatch(setResourceTotal(0))
      }
      dispatch(resetConnectionObjectPermissions());
      dispatch(resetPermissionCollections());
    }
  }, [dispatch, isShowAllResource]);

  useEffect(() => {
	
    if (guideUniqueIds?.length && guideUniqueIds.includes('MINE_APPLY_ADD')) {
      dispatch(setGuideSteps({steps, guideUniqueId: 'MINE_APPLY_ADD'}));
      dispatch(setGuideVisible(true));
    }
  },[guideUniqueIds])

 useEffect(() =>{
    const timer = setTimeout(() => {
      setDebouncedSearchValue(searchValue);
    }, 500);

    return () => clearTimeout(timer);
  }, [searchValue]);

  const tabsHandleOnChange = (activeKey: string) => {
    setTabsActiveKey(activeKey);
  }

  const handleSearch = (e: any) => {
    const value = e.target.value;
    setSearchValue(value?.trim());
  };

  const handleUpdateParams = (params: any[], compare?: boolean) => {
    setPermissionCollectionVOS(params)
  }

  const onAddApplicationToCart = () => {
    // 判断选择数据源层级选择加入购物车，参数特殊处理
    const {nodeType, children, roleId } = selectedTreeNode || {}
    const checkOnDataSourceLevel = nodeType==='datasource' && children?.length

    let param: any = {};
    if (tabsActiveKey === 'groupTab') {
      param.roleId = roleId;
    }
    //批量且 选中节点信息
    if (isBatchOperation && checkedTreeNodes?.length) {
      let params: any = [];
      const permission = permissionCollectionVOS?.[0];
      checkedTreeNodes?.forEach(node => {
        let value: any = {};

        if (permission?.permissionSdtUserAddVo) {
          value.permissionSdtUserAddVo = {
            ...(permission?.permissionSdtUserAddVo || {}),
            nodePathWithType: node?.nodePathWithType,
            nodeName: node?.nodeName,
            nodePath: node?.nodePath,
          }
        }
        if (permission?.userToolVo) {
          value.userToolVo = {
            ...(permission?.userToolVo || {}),
            nodePathWithType: node?.nodePathWithType,
            nodeName: node?.nodeName,
            nodePath: node?.nodePath,
          }
        }
        params.push(value);
      })

      param = {
        ...param,
        flowType: "FAT",
        nodeType: checkedTreeNodes?.[0]?.nodeType, // 批量选择nodeType要传具体的类型
        permissionCollectionVOS: params,
      }
      runAddRequisitionToCart(param);
    } else {
      
      param = {
        ...param,
        flowType: "FAT",
        nodeType: checkOnDataSourceLevel ? 'connection' : selectedTreeNode?.nodeType,
        permissionCollectionVOS: handlePermissionCollectionVOS(permissionCollectionVOS),
      }
      runAddRequisitionToCart(param);
    }
  }

  const handlePermissionCollectionVOS = (params: any[]) => {
    const {nodeType, children} = selectedTreeNode || {}
    // 判断选择数据源层级选择加入购物车，参数特殊处理
    if(nodeType==='datasource' && children?.length ){
      const {permissionSdtUserAddVo, userToolVo} = params?.[0] || {}
      let newParams: any[] = Array.from({ length: children?.length })?.map(() => ({}));
      children.forEach((item: any, index: number)=>{
        const { dataSourceType, nodeName, nodePath, nodePathWithType } = item || {}
        if(permissionSdtUserAddVo){
          newParams[index]['permissionSdtUserAddVo'] = {
            ...permissionSdtUserAddVo, 
            nodeName, 
            nodePath,
            dataSourceType, 
            nodePathWithType
          }
        }
        if(userToolVo){
          newParams[index]['userToolVo'] = {
            ...userToolVo, 
            nodeName, 
            nodePath,
            dataSourceType, 
            nodePathWithType
          }
        }
      })
      return newParams
    }
    return params
  }

  const handleLeftWrapResize = () => {
    // @ts-ignore
    const lWidth = document.getElementsByClassName("react-resizable")[0]?.style.width ||
      "320px";

    const width = `calc(100vw - 30px - ${lWidth} - 250px )`;
    setRightWrapWidth(width);
  };

  const renderResizeHandle = (
    <div className={styles.resizeHandle}>
      <Iconfont type="icon-handle-8"></Iconfont>
    </div>
  );

  // 右侧内盒子组件-分组视图
  const renderRightContentGroupTab = useMemo(() => {
    const isGroupTab = tabsActiveKey === 'groupTab';
    return (
      <>
        {/* permission、connection、db、schema层级授权*/}
        {
          (
            ['connection', 'database', 'schema', 'oracleUser'].includes(selectedTreeNode?.nodeType) ||
            nodeTypeIsPermission(selectedTreeNode?.nodePathWithType)
          ) &&
          <ConnectionRightMenuSetting
            isGroupTab={isGroupTab}
            roleId={roleId}
            selectTreeItem={selectedTreeNode}
            cartNumber={totalSchema}
          />
        }
        {
          selectedTreeNode?.nodeType?.toUpperCase()?.endsWith("GROUP") &&
          <ObjectLevelAuthorization
            cartNumber={totalSchema}
            isGroupTab={isGroupTab}
            roleId={roleId ?? selectedTreeNode?.roleId}
            canEdit={false}
            viewAppliedDetail={true}
            canGoToApply={true}
            selectTreeItem={selectedTreeNode}
            filter={true}
          />
        }
      </>
    );
  }, [tabsActiveKey, selectedTreeNode, roleId, totalSchema]);

  // 右侧内盒子组件-实例视图
  const renderRightContentInstanceTab = useMemo(() => {
    return (
      <>
        {/* connection、db、schema层级授权*/}
        {

        (!isBatchOperation && ['datasource', 'group', 'database', 'connection', 'schema', 'oracleUser'].includes(selectedTreeNode?.nodeType) ||
          (isBatchOperation && ['datasource', 'group', 'database', 'connection', 'schema', 'oracleUser'].includes(checkedTreeNodes?.[0]?.nodeType))
        ) &&
        <ConnectionRightMenuSetting
          isEdit={true}
          roleId={roleId}
          cartNumber={totalSchema}
          needRefresh={needRefresh}
          selectTreeItem={isBatchOperation ? checkedTreeNodes?.[0] : selectedTreeNode}
          permissionCollectionVOS={permissionCollectionVOS}
          updatePermissionCollectionVOS={handleUpdateParams}
        />
        }
        {
        isBatchOperation && !checkedTreeNodes?.length && !selectedTreeNode?.nodeType?.toUpperCase()?.endsWith("GROUP") &&
        <EmptyTipContent   cartNumber={totalSchema}/>
        }
        {/* 对象级权限设置 */}
        {
        selectedTreeNode?.nodeType?.toUpperCase()?.endsWith("GROUP") && !checkedTreeNodes?.length &&
        <ObjectLevelAuthorization
          selectTreeItem={selectedTreeNode}
          updateParams={handleUpdateParams}
          canEdit={true}
          cartNumber={totalSchema}
          roleId={roleId}
          needRefresh={needRefresh}
          onCancelBatchOperation={() => { setIsBatchOperation(false); setCheckedTreeNodes([]) }}
        />
        }
      </>
    );
  }, [
    isBatchOperation,
    selectedTreeNode,
    checkedTreeNodes,
    roleId,
    totalSchema,
    needRefresh,
    permissionCollectionVOS
  ]);


  return (
    <Spin spinning={false}>
      <div className={styles.automaticAuthorizationWrap}>
        <div className={styles.breadcrumbsWrap}>
          <SimpleBreadcrumbs items={breadcrumbData} />
        </div>
        <div className={styles.content}>
          <ResizableBox
            className={styles.resizableBox}
            handle={renderResizeHandle}
            onResize={handleLeftWrapResize}
            {...ResizableBoxProps}
          >
            <div className={styles.leftWrap}>
              <div className={classnames(styles.title, styles.mb10)} style={{ color: '#667084' }}>
                {tabsActiveKey === "instanceTab"? t('flow_resource') : t('flow_grouping')} {t('flow_list')}: {resourceTotal}
              </div>
              <Input
                allowClear
                className={classnames(styles.searchBtn, styles.mb10)}
                prefix={<SearchOutlined />}
                placeholder={isShowAllResource ? t('flow_enter_resource_name_ip') : t('flow_enter_ip_address')}
                onChange={handleSearch}
                value={searchValue}
              />
              <Tabs
                defaultActiveKey="instanceTab"
                size="small"
                activeKey={tabsActiveKey}
                onChange={tabsHandleOnChange}
              >
                <Tabs.TabPane tab={t('db.connection.sdt.instance')} key="instanceTab" />
                <Tabs.TabPane tab={t('db.connection.sdt.group')} key="groupTab" />
              </Tabs>
              {
                tabsActiveKey === "groupTab" ?
                <GroupTabTreeComponent
                  searchValue={searchValue}
                /> 
                :
                <TreeComponent
                  searchValue={isShowAllResource ? debouncedSearchValue : ''}
                  checkedTreeNodes={checkedTreeNodes}
                  setCheckedTreeNodes={setCheckedTreeNodes}
                  isBatchOperation={isBatchOperation}
                  onCancelBatchAction={() => {
                    setIsBatchOperation(false);
                    setCheckedTreeNodes([]);
                    setPermissionCollectionVOS([]);
                    setNeedRefresh(!needRefresh)
                  }}
                  onOpenBatchAction={() => {
                    setIsBatchOperation(true);
                    setPermissionCollectionVOS([]);
                  }}
              />
              }
            </div>
          </ResizableBox>
          <div className={styles.rightWrap} style={{ width: rightWrapWidth }}>
            {/* 数据源和组级别右侧有内容 没有选中内容 */}
            <div className={styles.contentStyle}>
              <div className={styles.operationList}>
                {
                  tabsActiveKey === "groupTab" ?
                  renderRightContentGroupTab
                  :renderRightContentInstanceTab
                }
              </div>
              <div className={styles.addRequestFooter}>
                <span
                  className={classnames(styles.addRequestBtn, {
                    [styles.hightlightBtn]: !_.isEmpty(permissionCollectionVOS) || tabsActiveKey === "groupTab"
                  })}
                  onClick={() => {
                    if (tabsActiveKey === "groupTab") {
                      onAddApplicationToCart()
                    } else {
                      !_.isEmpty(permissionCollectionVOS) && onAddApplicationToCart()
                    }
                  }}
                >
                  {
                    tabsActiveKey === "instanceTab"?
                    t('flow_add_to_application')
                    : t('flow_add_group_to_application')
                  }
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Spin>
  )
}

export default VisitRequestOperation