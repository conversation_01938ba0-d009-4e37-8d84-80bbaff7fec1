import React, { useState } from 'react'
import { useDispatch } from 'src/hook'
import ErrorBoundary from 'antd/lib/alert/ErrorBoundary'
import { Input } from 'antd'
import { MENU_FLOW } from 'src/constants';
import { SimpleBreadcrumbs } from 'src/components'
import { SearchOutlined } from '@ant-design/icons'
import { VisitWorkOrderTabsPage } from './visit-work-order-tabs'
import { setApplySearchValue } from '../accessRequestSlice'
import styles from './index.module.scss'
import _ from 'lodash'
import { MyApprovalTabKeyType, WORKORDER_INPUT_PLACEHOLD_EUM } from '../constants';
import { useTranslation } from 'react-i18next';

export const VisitWorkOrderPage = () => {
  const dispatch = useDispatch()
  const { t } = useTranslation()
  const [searchValue, setSearchValue] = useState('')
  const [activeKey, setActiveKey] = useState<MyApprovalTabKeyType>('stayApprove')

  const breadcrumbData = [
    { title: t(MENU_FLOW) },
    {
      title: t('flow_work_order_management'),
    },
  ];

  const onChangeValue = (str: string) => {
    setSearchValue(str)
  }

  const onSearch = _.debounce((e) => {
    dispatch(setApplySearchValue(e?.target?.value))
  }, 500)

  const handleInputChange = (e: { persist: () => void; target: { value: React.SetStateAction<string> } }) => {
    e.persist()
    setSearchValue(e?.target?.value)
    onSearch(e)
  }

  return (
    <div className={styles.myApplyRequestWrap}>
      <div className={styles.headers}>
        <SimpleBreadcrumbs items={breadcrumbData} />
        <div className={styles.flexRow}>
          <Input
            prefix={<SearchOutlined className="site-form-item-icon" />}
            placeholder={t(WORKORDER_INPUT_PLACEHOLD_EUM[activeKey])}
            value={searchValue}
            allowClear
            style={{ width: 220 }}
            onChange={handleInputChange}
          />
        </div>
      </div>
      <div className={styles.content}>
        <ErrorBoundary>
          <VisitWorkOrderTabsPage onChangeValue={onChangeValue} callbackActiveKey={setActiveKey} />
        </ErrorBoundary>
      </div>
    </div>
  )
}
