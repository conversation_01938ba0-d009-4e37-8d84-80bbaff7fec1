import React, { FC, useEffect } from 'react'
import {
  useSelector, 
  useDispatch
} from 'src/hook'
import ErrorBoundary from 'antd/lib/alert/ErrorBoundary'
import { Layout } from 'antd'
import classNames from 'classnames'
import { VisitWorkOrderPage } from './MyVisitWorkOrder'
import { MyApprovalRequestDetailPage } from '../my-apprpval-request-detail'
import { setFlowWorkOrderManagementPageState, setFlowWorkOrderManagementDetailParams } from 'src/pageTabs/access-request/accessRequestSlice'
import styles from './index.module.scss'

const { Content } = Layout

interface ListProps { }

export const MyWorkOrderVisitPage: FC<ListProps> = ({ ...rest }) => {
  const dispatch = useDispatch()
  const { flowWorkOrderManagementPageState } = useSelector((state) => state.accessRequest); 

  useEffect(()=>{
    return () => {
      // 恢复 初始状态
      dispatch(setFlowWorkOrderManagementPageState('')) 
      dispatch(setFlowWorkOrderManagementDetailParams({}))
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])
  
  const contentRender = () => {
    switch (flowWorkOrderManagementPageState) {
      case 'detail':
        return <MyApprovalRequestDetailPage />;     // 工单管理详情和我的审批详情是一个组件
      default:
        return <VisitWorkOrderPage />               // 工单管理
    }
  }

  return (
    <Layout className="cq-container">
      <Layout className="cq-main" style={{padding: 0}}>
        <Content
          className={classNames('cq-content', { [styles['change-request-page']]: true })}
        >
          <ErrorBoundary>
            {
              contentRender()
            }
          </ErrorBoundary>
        </Content>
      </Layout>
    </Layout>
  )
}
