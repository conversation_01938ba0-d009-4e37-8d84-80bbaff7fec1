import React, { FC, useEffect, useMemo, useState } from "react";
import { useRequest, useSelector, useDispatch } from "src/hook";
import { Table, Tooltip, message } from "antd";
import { columnsRequestExpandRow } from "../../common/columns";
import {
  myApplyChildList,
  withdrawOrder,
  deleteOrder,
  getAllApprovePending,
  IAllApproveParams,
  getAllApproveWithdraw,
  getAllApproveFinish,
  getAllApprovePower,
  getAllApproveApproved,
  getAllApproveAll
} from "src/api";
import { BtnWithConfirmModal } from 'src/components';
import ModalDataChangeSelectNewApprover from "src/pageTabs/flowPages/flowDetails/ModalDataChangeSelectNewApprover";
import {
  APP_EFFECTIVE_STATES,
  MyApprovalTabKeyType,
  NO_NEED_TO_LAND_TYPES,
  applyStatusMap,
  priGranTypeMap
} from "../../constants";
import { getEffectiveStatus } from '../../utils';
import { useTranslation } from 'react-i18next';
import styles from "../index.module.scss";
import classnames from 'classnames';
import classNames from "classnames";
import dayjs from "dayjs";
import { ColumnsType } from 'antd/lib/table'
import { setFlowWorkOrderManagementPageState, setFlowWorkOrderManagementDetailParams } from 'src/pageTabs/access-request/accessRequestSlice'

export interface IProps {
  activeKey: MyApprovalTabKeyType;
}

const VisitWorkOrderTablePage: FC<IProps> = ({ ...props }) => {
  const dispatch = useDispatch();
  const { activeKey } = props;
  const { applySearchValue } = useSelector((state) => state.accessRequest);
  const { permissionList } = useSelector((state) => state?.login)
  const isReadOnly = permissionList?.FLOW_APPLY?.FLOW_WORK_ORDER_MANAGEMENT?.isOnlyRead
  const roleNameList = permissionList?.FLOW_APPLY?.FLOW_WORK_ORDER_MANAGEMENT?.roleNameList
  const [visible_setNewApprover, setVisible_setNewApprover] = useState(false); // 新审批人 modal visible
  const [currentTransferApprover, setCurrentTransferApprover] = useState<Array<string>>([]); // 新审批人 modal visible
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(10);
  const [filterType, setFilterType] = useState<any>(undefined); // 类型 -- 筛选
  const [statusType, setStatusType] = useState<any>(undefined); // 状态 -- 全部tab中的表头筛选项
  const [newData, setNewdData] = useState<any>([]);
  const [newTotal, setNewTotal] = useState<any>(0);
  const [timeSort, setTimeSort] = useState<string>("desc");
  const [expandedRowKeys, setExpandedRowKeys] = useState<string[]>([]);
  const [expandChildData, setExpandChildData] = useState<any>({}); // 展开子表数据

  const userId = useSelector((state) => state.login.userInfo.userId);

  const { t } = useTranslation()

  const apiEum: any = {
    stayApprove: getAllApprovePending, // 待审批
    yetApprove: getAllApproveApproved, // 已审批
    power: getAllApprovePower, // 待落权
    finish: getAllApproveFinish, // 已完成
    withdraw: getAllApproveWithdraw, // 已撤回
    all: getAllApproveAll, //全部
  }

  // operationRender
  const opeEum: any = {
    pending: 'stayApprove',
    pass: 'yetApprove',
    power: 'power',
    finish: 'finish',
    withdraw: 'withdraw',
    refuse: 'yetApprove',
  }

  const { loading, run } = useRequest(apiEum[activeKey], {
    manual: true,
    debounceInterval: 200,
    formatResult: (data) => {
      let { totalElements, myApplyResponseVoList: list } = data;
      let newList = [];
      newList = list.map((item: any) => {
        item.indexKey = String(item.mainUUID) + String(item.flowTaskId) //rowKey
        return item;
      });
      const total = totalElements;
      setNewTotal(total);
      setNewdData(newList);
      return { list: newList, total };
    }
  })

  //撤回
  const { run: runWithdrawOrder } = useRequest(withdrawOrder, { manual: true, onSuccess: () => { refreshAll() } })
  //删除
  const { run: runDeleteOrder } = useRequest(deleteOrder, { manual: true, onSuccess: () => { refreshAll() } })

  const refreshAll = () => {
    let params: IAllApproveParams = {
      pageSize,
      currentPage: 1,
      keyWord: applySearchValue,
      priGranType: filterType,
      sorted: timeSort,
      tab: statusType
    }
    run(params)
    setCurrentPage(1);
  };

  useEffect(() => {
    refreshAll()
  }, [activeKey, applySearchValue]);

  // 转审
  const turretBtn = (record: any) => {
    setVisible_setNewApprover(true);
    setCurrentTransferApprover([record.flowTaskId]); //应该使用当前userId
  };

  // 操作栏render
  const renderTabAction = (record: any, activeKey: MyApprovalTabKeyType) => {
    // 全部视图根据tab字段转换成对应的activeKey
    const judgeKey = activeKey === 'all' ? opeEum[record?.tab] : activeKey

    switch (judgeKey) {
      case "stayApprove":
        return isReadOnly ?
          <Tooltip title={`${t('flow_current_role_is')} [${roleNameList?.join(',')}], ${t('flow_no_permission_work_order')}`}>
            <span className="disabled">{t('flow_transfer_review')}</span>
          </Tooltip>
          : <span onClick={() => turretBtn(record)}>{t('flow_transfer_review')}</span>
      case "yetApprove":
        return isReadOnly ? (<Tooltip title={`${t('flow_current_role_is')} [${roleNameList?.join(',')}], ${t('flow_no_permission_work_order')}`} >
          <span className="disabled">{t('flow_delete')}</span>
          {/* 待生效或生效中 */}
          {
            ['pass'].includes(record?.flowApplyStatus) && getEffectiveStatus(record?.beginTime, record?.endTime) !== APP_EFFECTIVE_STATES.EXPIRED &&
            <span className="ml14 disabled">{t('flow_withdraw')}</span>
          }
        </Tooltip>)
          : (<>
            <BtnWithConfirmModal title={t('flow_confirm_delete_record')} btnText={t('flow_delete')} hideReason={true} onClick={() => runDeleteOrder(Number(record?.applyId))} />
            {/* 待生效或生效中 */}
            {
              ['pass'].includes(record?.flowApplyStatus) && getEffectiveStatus(record?.beginTime, record?.endTime) !== APP_EFFECTIVE_STATES.EXPIRED && (
                <BtnWithConfirmModal title={t('flow_confirm_withdraw_permissions')} btnText={t('flow_withdraw')} onClick={(reason?: string) => runWithdrawOrder({
                  flowId: Number(record?.applyId),
                  withdrawRemark: reason
                })} />
              )}
          </>)
      case "finish":
        return isReadOnly ? (<Tooltip title={`${t('flow_current_role_is')} [${roleNameList?.join(',')}], ${t('flow_no_permission_work_order')}`} >
          <span className="disabled">{t('flow_delete')}</span>
          {
            (!record?.allChildRefuse && getEffectiveStatus(record?.beginTime, record?.endTime) !== APP_EFFECTIVE_STATES.EXPIRED) &&
            <span className="ml14 disabled">{t('flow_withdraw')}</span>
          }
        </Tooltip>)
          : (<>
            <BtnWithConfirmModal title={t('flow_confirm_delete_record')} btnText={t('flow_delete')} hideReason={true} onClick={() => runDeleteOrder(Number(record?.applyId))} />
            {(!record?.allChildRefuse && getEffectiveStatus(record?.beginTime, record?.endTime) !== APP_EFFECTIVE_STATES.EXPIRED) && (
              <BtnWithConfirmModal title={t('flow_confirm_withdraw_permissions')} btnText={t('flow_withdraw')} onClick={(reason?: string) => runWithdrawOrder({
                flowId: Number(record?.applyId),
                withdrawRemark: reason
              })} />
            )}
          </>)
      case "withdraw":
        return isReadOnly ?
          <Tooltip title={`${t('flow_current_role_is')} [${roleNameList?.join(',')}], ${t('flow_no_permission_work_order')}`} >
            <span className="disabled">{t('flow_delete')}</span>
          </Tooltip>
          :
          <BtnWithConfirmModal title={t('flow_confirm_delete_record')} btnText={t('flow_delete')} hideReason={true} onClick={() => runDeleteOrder(Number(record?.applyId))} />
      default: return '-'
    }
  };

  // 流程-工单管理-详情
  const handleToFlowWorkOrderManagemenDetail = (id: string, record: any) => {
    const params = {
      ...record,
      id,
      flag: "worksheetManage",
      curTab: activeKey
    }
    dispatch(setFlowWorkOrderManagementPageState('detail'))
    dispatch(setFlowWorkOrderManagementDetailParams(params))
  }

  const columns = useMemo(() => {
    let baseColumns: ColumnsType<any> = [{
      title: t('flow_application_number'),
      dataIndex: 'mainUUID',
      width: 200,
      render: (val: any, record: any) => (
        <span
          className={`${styles.underLine} options`}
          onClick={() => handleToFlowWorkOrderManagemenDetail(val, record)}
        >
          {val}
        </span>
      ),
    },
    {
      title: t('flow_title'),
      dataIndex: 'title',
      width: 200,
      render: (title: any, record: any) => {
        return <span>{title ? title : '-'}</span>
      }
    },
    {
      title: t('flow_type'),
      dataIndex: 'priGranType',
      width: 150,
      filters: Object.keys(priGranTypeMap).map((key) => ({
        text: t(priGranTypeMap[key]),
        value: key,
      })),
      filterMultiple: true,
      render: (_: any, { priGranType }: any) => {
        return <span>{priGranTypeMap[priGranType] ? t(priGranTypeMap[priGranType]) : '-'}</span>
      },
    },
    {
      title: t('flow_applicant'),
      dataIndex: 'applyUserName',
      width: 150,
      render: (applyUserName: any, record: any) => (
        <div>
          {
            record?.applyUserName ? record?.applyUserName :
              (record?.applyUserId ? '(' + record?.applyUserId + ')' : '-')
          }
        </div>
      ),
    },
    {
      title: t('flow_status'),
      dataIndex: 'flowApplyStatus',
      width: 100,
      render: (_: any, { flowApplyStatus, tab }: any) => {
        return <span>
          <span
            className={classNames(
              styles.statusDot,
              flowApplyStatus === 'pending' && styles.pendingBack,
              (flowApplyStatus === 'pass' || flowApplyStatus === 'already') && styles.alreadyBack,
              (flowApplyStatus === 'power' || activeKey === 'power' || tab === 'power') && styles.powerBack,
              flowApplyStatus === 'refuse' && styles.rejectBack,
              flowApplyStatus === 'withdraw' && styles.rejectBack,
            )}
          ></span>
          { (activeKey === 'power' || tab === 'power') ? t(applyStatusMap['power']) : t(applyStatusMap[flowApplyStatus])}
        </span>
      },
      ...activeKey === 'all' && {
        filters: Object.keys(applyStatusMap).filter((key) => !['rejected', 'stop'].includes(key)).map((key) => ({
          text: <span>
            <span
              className={classNames(
                styles.statusDot,
                key === 'pending' && styles.pendingBack,
                (key === 'pass' || key === 'already') && styles.alreadyBack,
                key === 'power' && styles.powerBack,
                key === 'refuse' && styles.rejectBack,
                key === 'withdraw' && styles.rejectBack,
              )}
            ></span>
            {t(applyStatusMap[key])}
          </span>,
          value: key === 'already' ? 'finish' : key,
        })),
        filterMultiple: true,
      }
    },
    {
      title: t('flow_effective_time'),
      dataIndex: 'time',
      ellipsis: {
        showTitle: false,
      },
      width: 200,
      render: (_: any, record: any) => (
        <Tooltip placement="topLeft" title={record?.priGranType === 'exportTask' ? '--' : (record?.endTime && record?.beginTime) ? `${dayjs(record?.beginTime).format('YYYY-MM-DD HH:mm:ss')} - ${dayjs(record?.endTime).format('YYYY-MM-DD HH:mm:ss')} ` : t('flow_permanent')} >
          {record?.priGranType === 'exportTask' ? '--' : (record?.endTime && record?.beginTime) ? `${dayjs(record?.beginTime).format('YYYY-MM-DD HH:mm:ss')} - ${dayjs(record?.endTime).format('YYYY-MM-DD HH:mm:ss')} ` : t('flow_permanent')}
        </Tooltip>

      ),
    },
    {
      title: t('flow_current_approver'),
      dataIndex: 'currentAssignee',
      width: 150,
      render: (_: any, record: any) => (
        <div>
          {
            !record?.currentAssigneeName ?
              (!record?.currentAssignee ? '-' : record?.currentAssignee)
              :
              (record?.currentAssigneeName)
          }
        </div>
      ),
    },
    {
      title: t('flow_operation'),
      dataIndex: 'actions',
      fixed: 'right',
      width: 150,
      render: (_: any, record: any) => (
        <div className={classnames(styles.actionsBtn, {
          [styles.disabled]: isReadOnly
        })}>
          {renderTabAction(record, activeKey)}
        </div>
      ),
    }]
    if (["yetApprove", "finish", "all"].includes(activeKey)) {
      baseColumns.splice(7, 0, {
        title: t('flow_effective_status'),
        dataIndex: "effectiveState",
        width: 100,
        render: (_: string, record: any) => {
          return ((record?.flowApplyStatus === 'pass' && activeKey === 'yetApprove') ||
            activeKey === 'finish' ||
            ['pass', 'finish'].includes(record?.tab)) ?
            t(getEffectiveStatus(record?.beginTime, record?.endTime)) : '-';
        },
      });
    }
    return baseColumns;
  }, [activeKey, t])

  const onChange = (pagination: any, filters: any, sorter: any) => {
    setFilterType(filters?.priGranType ? filters?.priGranType : undefined);
    setStatusType(filters?.flowApplyStatus ? filters?.flowApplyStatus : undefined);
    let params: IAllApproveParams = {
      pageSize: pagination?.pageSize,
      currentPage: pagination?.current,
      keyWord: applySearchValue,
      priGranType: filters?.priGranType ? filters?.priGranType: undefined,
      sorted: sorter?.order === 'ascend' ? "asc" : "desc",
      tab: filters?.flowApplyStatus ? filters?.flowApplyStatus : undefined
    }
    run(params)
    setCurrentPage(pagination?.current);
    setPageSize(pagination?.pageSize);
    setTimeSort(sorter?.order === "ascend" ? "asc" : "desc");
  };

  // onExpand 事件处理
  const onExpand = async (expanded: boolean, record: any) => {
    const rowKey = record?.indexKey 
    if (expanded) {
      try {
        // 调用接口判断是否可以展开
        const childData = await myApplyChildList(record?.mainUUID, '1');
        if (childData?.length > 0) {
          // 可以展开
          setExpandedRowKeys([...expandedRowKeys, rowKey]);
          setExpandChildData({...(expandChildData ?? {}), [rowKey]: childData});
        } else {
          // 不可以展开
          message.error(t('flow_no_sub_order'));
        }
      } catch (error) {
        message.error(t('expand_exception'));
      }
    } else {
      // 收起行
      setExpandedRowKeys(expandedRowKeys?.filter(key => key !== rowKey));
    }
  };

  return (
    <div>
      <Table
        rowKey="indexKey"
        loading={loading}
        className={styles.tablePage}
        pagination={{
          current: currentPage,
          pageSize,
          total: newTotal,
          showTotal: () => `${t('flow_total')} ${newTotal || 0} ${t('flow_items_lower')}`,
          size: 'default',
          showSizeChanger: true,
          showQuickJumper: true,
        }}
        expandable={{
          expandedRowRender: (record: any) => (
            <ExpandedRowContent record={record} activeKey={activeKey} data={ expandChildData?.[record?.indexKey] ?? [] } />
          ),
          rowExpandable: (record) => !NO_NEED_TO_LAND_TYPES.includes(record?.priGranType),
          onExpand,
          expandedRowKeys,
        }}
        rowClassName={(record, index) =>
          ((record?.applyStatus === 'pass' && activeKey === 'yetApprove') ||
            activeKey === 'finish' ||
            ['pass', 'finish'].includes(record?.tab)) &&
            getEffectiveStatus(record?.beginTime, record?.endTime) === APP_EFFECTIVE_STATES.EXPIRED
            ? styles.notInEffect
            : ""
        }
        columns={columns}
        dataSource={newData}
        size="small"
        scroll={{ x: "1200", y: `calc(100vh - 280px)` }}
        onChange={onChange}
      />
      <ModalDataChangeSelectNewApprover
        cleanParentComponentData={() => {
          setCurrentTransferApprover([]);
          refreshAll();
        }}
        userTasks={currentTransferApprover}
        visible_setNewApprover={visible_setNewApprover}
        setVisible_setNewApprover={setVisible_setNewApprover}
      />
    </div>
  );
};

export const ExpandedRowContent = ({
  record,
  activeKey,
  data
}: {
  record: any;
  activeKey: string;
  data: any[]
}) => {
  const [dataSource, setDataSource] = useState<any[]>([]);
  const dispatch = useDispatch();
  const flowUUID = record?.mainUUID;

  const {
    data: childData,
    loading,
  } = useRequest(myApplyChildList, {
    manual: true,
    refreshDeps: [flowUUID],
  });

  useEffect(() => {
    if(data?.length > 0) {
      setDataSource(data)
    }
    if(childData?.length > 0) {
      setDataSource(childData)
    }
  }, [childData, data]);

  // 流程-工单管理-详情
  const handleToFlowWorkOrderManagemenDetail = (id: string, record: any) => {
    const params = {
      ...record,
      id,
      flag: "worksheetManage",
      curTab: activeKey
    }
    dispatch(setFlowWorkOrderManagementPageState('detail'))
    dispatch(setFlowWorkOrderManagementDetailParams(params))
  }

  const newExpandColumns = useMemo(() => {
    const newExpandColumns = columnsRequestExpandRow()?.map((item: any) => {
      if (item?.dataIndex === "uuid") {
        return {
          ...item,
          render: (val: any, record: any) => (
            <span
              className={`${styles.underLine} options`}
              onClick={() => handleToFlowWorkOrderManagemenDetail(val, record)}
            >
              {val}
            </span>
          ),
        };
      }
      return item;
    });

    return newExpandColumns;
  }, [activeKey, record]);

  return (
    <Table
      rowKey="uuid"
      loading={loading}
      columns={newExpandColumns}
      dataSource={dataSource}
      pagination={false}
      size="middle"
    />
  );
};

export { VisitWorkOrderTablePage };
