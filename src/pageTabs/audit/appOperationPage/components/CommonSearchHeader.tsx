import { Col, Select, Row, Input, DatePicker, Button } from "antd";
import styles from './component.module.scss';
import { SelectValue } from "antd/lib/select";
import React, { useEffect, useState } from "react";
import { formatDateRange } from "src/util";
import dayjs, { Dayjs } from "dayjs";
import { useTranslation } from "react-i18next";
const { Option } = Select;
const { RangePicker } = DatePicker;

export interface ICommonSearchHeader {
  leftNode?: JSX.Element | React.ReactNode;
  searchConfigList: ISearchConfigItem[];
  rightExtraButton?: JSX.Element | React.ReactNode;
  defaultSearchParams?: any;
  callBack: (params: any) => void;
  reset?: () => void
}

export interface ISearchConfigItem {
  field: string;
  placeholder?: string | string[];
  name?: string; // 不显示的label，作为key的一部分，为了好区分
  defaultValue?: any;
  nodeType: string | "input" | "select" | "dateRange";
  nodeStyle?: any;
  itemRender?: (callBack: React.Dispatch<any>) => React.ReactNode;
  nodeProps?: any;
  selectOptions?: ISelectOption[];
  selectLableField?: string;
  selectValueField?: string;
}

interface ISelectOption {
  [key: string]: any;
}

export const CommonSearchHeader = (props: ICommonSearchHeader) => {
  const { searchConfigList, callBack, leftNode, rightExtraButton, defaultSearchParams, reset } = props;
  const { t } = useTranslation()
  const [searchParams, setSearchParams] = useState<any>(defaultSearchParams);

  // 初始化参数的值
  useEffect(() => {
    setSearchParams(defaultSearchParams)
  }, [defaultSearchParams])

  // 筛选项
  const renderConfigItem = (item: ISearchConfigItem) => {
    const { field, placeholder, name, nodeType, nodeProps, itemRender, selectOptions, selectLableField, selectValueField, nodeStyle, } = item;
    const key = name ? (name + '-' + field) : field

    switch (nodeType) {
      case "input":
        return (<Col key={key}>
          <Input
            allowClear
            placeholder={placeholder}
            value={searchParams ? searchParams[field] : undefined}
            onChange={(e: any) => {
              setSearchParams({ ...searchParams, [field]: e.target.value });
            }}
            {...nodeProps}
            style={{ ...(nodeStyle || {}) }}
          />
        </Col>)
      case "select":
        return (<Col key={key}>
          <Select
            showArrow
            allowClear
            placeholder={placeholder}
            value={searchParams ? searchParams[field] : undefined}
            onChange={(value: SelectValue) => {
              setSearchParams({ ...searchParams, [field]: value });
            }}
            {...(nodeProps || {})}
            style={{ ...(nodeStyle || {}) }}
          >
            {(selectOptions || []).map((ele, index) => (
              <Option key={index} value={ele[selectValueField || "value"]}>
                {ele[selectLableField || "label"]}
              </Option>
            ))}
          </Select>
        </Col >)
      case "dateRange":
        return (<Col key={key}>
          <RangePicker
            allowClear
            format="YYYY-MM-DD"
            placeholder={placeholder}
            value={
              searchParams && searchParams[field] ?
                searchParams[field].map((timestamp: any) => dayjs(Number(timestamp))) as [Dayjs, Dayjs]
                : null
            }
            onChange={(values: any) => {
              if (values === null) {
                return setSearchParams({ ...searchParams, [field]: null });
              }
              const [start, end] = formatDateRange(values)
              if (start && end) {
                setSearchParams({ ...searchParams, [field]: [start, end] });
              }
            }}
            {...(nodeProps || {})}
            style={{ ...(nodeStyle || {}) }}
          />
        </Col>)
      default: 
        return (
          <Col key={key}>
            {/* 将dispatch作为callback抛出去，以便于自定义node来修改searchParams */}
            {itemRender && itemRender(setSearchParams)}
          </Col>
        )
    }
  }

  return (
    <div className={styles.searchHeader}>
      {
        leftNode && <div className={styles.leftNode}>
          {leftNode}
        </div>
      }
      <Row justify="end" gutter={16} style={{ alignItems: 'center' }}>
        <label>{t("auays:div_lbl.filter_condition")}：</label>
        {searchConfigList.map((configItem: ISearchConfigItem) =>
          renderConfigItem(configItem)
        )}
        <Col key='btns'>
          <Button onClick={() => callBack({ ...searchParams, offset: 0 })} type="primary" className="mr8">
            {t("auays:btn.query")}
          </Button>
          <Button
            onClick={() => {
              setSearchParams(null)
              reset && reset()
            }}
            className="mr8"
          >
            {t("auays:btn.reset")}
          </Button>
          {rightExtraButton}
        </Col>
      </Row>
    </div>
  );
};
