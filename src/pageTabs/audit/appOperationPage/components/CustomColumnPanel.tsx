import React, { useEffect, useState } from 'react'
import { Checkbox } from 'antd'
import { DrawerWrapper } from 'src/components'
import { useTranslation } from 'react-i18next'

interface IProps {
  defaultColumnFields: string[]
  allCheckColumnFields: { [key: string]: string }
  notAllowCheckColumn?: string[]
  visible: boolean
  onCancel: () => void
  onOk: (fields: string[]) => void
}

export const CustomColumnPanel = ({
  defaultColumnFields,
  allCheckColumnFields,
  notAllowCheckColumn,
  visible = false,
  onCancel,
  onOk,
}: IProps) => {
  const { t } = useTranslation()
  const [checkedColumnFields, setCheckedColumnFields] = useState<string[]>([])

  useEffect(() => {
    setCheckedColumnFields(defaultColumnFields)
  }, [defaultColumnFields])

  return (
    <DrawerWrapper
      title={t("auays:drawer_title.custom_list_items")}
      width={400}
      visible={visible}
      onCancel={() => {
        onCancel();
        setCheckedColumnFields(defaultColumnFields);
      }}
      onOk={() => onOk(checkedColumnFields)}
    >
      <Checkbox.Group
        defaultValue={defaultColumnFields}
        value={checkedColumnFields}
        //@ts-ignore
        onChange={(checkedValue: string[]) =>
          checkedValue && setCheckedColumnFields(checkedValue)
        }
      >
        {Object.keys(allCheckColumnFields).map((key: any) => (
          <span key={key}>
            <Checkbox
              disabled={notAllowCheckColumn?.includes(key)}
              value={key}
            >
              {allCheckColumnFields[key]}
            </Checkbox>
            <div style={{ padding: '8px 0' }} />
          </span>
        ))}
      </Checkbox.Group>
    </DrawerWrapper>
  )
}
