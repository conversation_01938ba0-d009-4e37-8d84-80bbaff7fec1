import { Button, Table } from "antd";
import React, { forwardRef, useEffect, useImperativeHandle, useMemo, useState } from "react";
import { CommonSearchHeader, ISearchConfigItem } from "./CommonSearchHeader";
import { SettingOutlined } from "@ant-design/icons";
import styles from './component.module.scss';
import { ColumnsType, TableProps } from "antd/lib/table";
import { useRequest, useSelector } from "src/hook";
import { isEmpty } from "lodash";
import { CustomColumnPanel } from "./CustomColumnPanel";
import { useTranslation } from "react-i18next";

export interface ICustomSearchHeader {
  leftNode?: (params: any, callBack: (params: any, AsyncSearchItem?: boolean) => void, controlValue: React.Dispatch<any>) => React.ReactNode; // 筛选栏左侧自定义node
  searchConfigList: ISearchConfigItem[];
  rightNode?: (params: any, callBack: (params: any, AsyncSearchItem?: boolean) => void, controlValue: React.Dispatch<any>) => React.ReactNode; // 筛选栏右侧自定义node
}

export interface ICustomCommonTable {
  isNeedSearch?: boolean
  tableProps?: TableProps<any>
  defaultTableParams?: any
  getColumnApi: (params: any) => Promise<any>
  getTableDataApi: (params: any) => Promise<any>
  defaultColumnParams?: any // 自定义列请求时的初始参数
  notAllowColumns?: string[] // 必选的列
  allColumnsFields: { [key: string]: string } //全部列
  formatColumn: (columns: string[], params: any, callBack: (params: any, AsyncSearchItem?: boolean) => void) => ColumnsType<any>
  formatParams: (params: any) => any
  searchHeaderParams?: ICustomSearchHeader //筛选头参数
  searchReset?: (callBack: (params: any, AsyncSearchItem?: boolean) => void) => void //重置时相关操作
}

export const CustomCommonTable = forwardRef((props: ICustomCommonTable, ref: any) => {
  const { isNeedSearch = true, defaultTableParams, getColumnApi, formatParams, searchReset, allColumnsFields, notAllowColumns = [], tableProps, getTableDataApi, defaultColumnParams, formatColumn, searchHeaderParams } = props
  const { t } = useTranslation()
  const { locales } = useSelector(state => state.login)
  const [searchParams, setSearchParams] = useState<any>(defaultTableParams) //tableApi请求参数
  const [currentSearchParams, setCurrentSearchParams] = useState<any>(defaultTableParams) //当前参数,主要是为了解决重置时外置筛选项清空value但不重新请求接口
  const [columns, setColumns] = useState<string[]>([]) // 请求获取的行字符串
  const [realColumns, setRealColumns] = useState<ColumnsType<any>>([]) // 真正的table column
  const [isShowCustomColumnPanel, setIsShowCustomColumnPanel] = useState<boolean>(false) //是否展示侧面板

  // 获取自定义列的api
  const { run: getAndSetColumnFields } = useRequest(getColumnApi, {
    manual: true,
    onSuccess: (data: any) => {
      const res = data?.columns ?? []
      setColumns(res)
      setIsShowCustomColumnPanel(false)
    },
  })

  useEffect(() => {
    if (!isEmpty(defaultColumnParams))
      getAndSetColumnFields(defaultColumnParams)
  }, [defaultColumnParams])

  // format column
  useEffect(() => {
    setRealColumns(formatColumn(columns, currentSearchParams, searchCallBack))
  }, [columns, currentSearchParams, locales])

  // table request
  const { data: tableData, loading: tableLoading, refresh } = useRequest(() => {
    // setCurrentSearchParams(searchParams)
    const formatSearchParams = formatParams(searchParams)
    return getTableDataApi({ ...formatSearchParams })
  }, { refreshDeps: [searchParams] },
  )

  // 抛出table的刷新方法
  useImperativeHandle(ref, () => ({
    refresh
  }));

  // searchHeader的callBack
  const searchCallBack = (params: any, AsyncSearchItem?: boolean) => {
    !AsyncSearchItem && setCurrentSearchParams({
      limit: 10, //条数
      offset: 0, //页码
      ...params
    })
    setSearchParams({
      limit: 10, //条数
      offset: 0, //页码
      ...params
    })
  }

  const headerLeftNode = useMemo(() => <>
    {searchHeaderParams?.leftNode && searchHeaderParams?.leftNode(currentSearchParams, searchCallBack, setCurrentSearchParams)}
  </>, [currentSearchParams])

  const headerExtraBtns = useMemo(() => <>
    {searchHeaderParams?.rightNode && searchHeaderParams?.rightNode(currentSearchParams, searchCallBack, setCurrentSearchParams)}
    <Button
      className="ml10"
      icon={<SettingOutlined />}
      type="primary"
      onClick={() => setIsShowCustomColumnPanel(true)}
    />
  </>, [currentSearchParams])

  return <div className={styles.commonTable}>
    {
      isNeedSearch &&
      searchHeaderParams &&
      <CommonSearchHeader
        searchConfigList={searchHeaderParams.searchConfigList}
        callBack={searchCallBack}
        reset={() => {
          setCurrentSearchParams(null)
          searchReset && searchReset(searchCallBack)
        }}
        leftNode={headerLeftNode}
        rightExtraButton={headerExtraBtns}
        defaultSearchParams={searchParams}
      />
    }
    <Table
      rowKey="id"
      size="middle"
      indentSize={30}
      scroll={{ x: realColumns?.length > 7 ? 1740 : 'max-content', y: `calc(100vh - 260px) ` }}
      tableLayout="auto"
      className={styles.customTable}
      {...tableProps}
      loading={tableLoading}
      columns={realColumns}
      dataSource={tableData?.data || []}
      pagination={{
        className: styles.pagination,
        showSizeChanger: true,
        pageSize: tableData?.limit || 10,
        current: Number(tableData?.offset || 0) + 1,
        total: tableData?.total || 0,
        showTotal: (total) => t("auays:tb.show_total", {total: total || 0}),
        onChange: (page: number, pageSize?: number) => {
          searchCallBack({
            ...searchParams,
            offset: page - 1,
            limit: pageSize || 10,
          })
        },
        ...tableProps?.pagination
      }}
    />
    <CustomColumnPanel
      allCheckColumnFields={allColumnsFields}
      defaultColumnFields={isEmpty(columns) ? Object.values(allColumnsFields) : columns}
      notAllowCheckColumn={notAllowColumns}
      visible={isShowCustomColumnPanel}
      onCancel={() => setIsShowCustomColumnPanel(false)}
      onOk={(fields: string[]) =>
        defaultColumnParams && getAndSetColumnFields({ ...defaultColumnParams, columns: fields })
      }
    />
  </div>
});
