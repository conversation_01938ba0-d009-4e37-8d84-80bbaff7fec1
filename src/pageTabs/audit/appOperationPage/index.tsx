import React, { useEffect, useMemo, useRef, useState } from 'react';
import classNames from 'classnames';
import { Layout, Breadcrumb, Radio, Button, Tooltip, Badge, Typography, Input } from 'antd';
import Watermark from '@pansy/react-watermark'
import { useRequest, useSelector, useDispatch } from 'src/hook';
import { useLocation } from 'react-router-dom';
import { getAndSetAuditColumn, SqlTypes, getAppDbTypes, getAppSqlTypes, findAuditAppLogs, AuditAppLogParams } from 'src/api';
import 'src/styles/layout.scss';
import styles from './index.module.scss';
import { CustomCommonTable } from './components/CustomCommonTable';
import { ConnectionIcon } from 'src/components';
import { ISearchConfigItem } from './components/CommonSearchHeader';
import { InfoCircleOutlined, RedoOutlined } from '@ant-design/icons';
import { debounce, isEmpty } from 'lodash';
import { APP_STATEMENT_COLUMN_FIELDS, APP_STATEMENT_DETAIL_COLUMNS } from 'src/constants';
import { renderErrorFlag } from 'src/util';
import dayjs from 'dayjs';
import { useTranslation } from 'react-i18next';
import { 
  setOverviewPageState,
  setOverviewPageDetailParams 
} from 'src/pageTabs/audit/overview/overviewSlice'
const { Header, Content } = Layout
const RadioGroup = Radio.Group
const RadioButton = Radio.Button
const search_icon = <svg transform="1715240034079" className="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="16998" width="14" height="14"><path d="M926.208 865.792l-156.672-156.672c52.736-65.536 83.968-148.992 83.968-239.616 0-211.968-172.032-384-384-384s-384 172.032-384 384 172.032 384 384 384c90.624 0 174.08-31.232 239.616-83.968l156.672 156.672c16.896 16.896 43.52 16.896 60.416 0 16.384-16.896 16.384-43.52 0-60.416z m-241.664-194.56c-1.536 1.024-3.072 2.56-4.096 3.584-1.536 1.536-2.56 2.56-3.584 4.096-53.76 51.712-126.976 83.456-207.36 83.456-164.864 0-298.496-133.632-298.496-298.496 0-164.864 133.632-298.496 298.496-298.496 164.864 0 298.496 133.632 298.496 298.496 0 80.896-31.744 153.6-83.456 207.36z" p-id="16999" fill="#d9d9d9"></path></svg>

export const AppOperationDetailPage = () => {
  const dispatch = useDispatch()
  // watermark
  const { watermarkSetting, watermarkValue } = useSelector(
    (state: any) => state.login.userInfo,
  )
  const { locales } = useSelector(state => state.login)
  const { t } = useTranslation()
  const { watermarkEffect } = useSelector((state: any) => state.login)
  const customCommonTableRef = useRef<any>(null); // table的ref
  const { userId } = useSelector((state) => state.login.userInfo)
  const [defaultColumnParams, setDefaultColumnParams] = useState<any>({})
  const location = useLocation<{ state: { timeRange: any, resultFlag?: number } }>()

  // 自定义列api默认参数
  useEffect(() => {
    if (userId) {
      setDefaultColumnParams({
        userId,
        type: 'APP_AUDIT',
        columns: Object.keys(APP_STATEMENT_COLUMN_FIELDS)
          .filter((item: string) => item != 'operateObject') //操作对象暂时没数据，所以先默认过滤一下
      })
    }
  }, [userId])

  // 数据源类型Options
  const { data: connectionTypes, loading: loadingConnectionTypes } = useRequest(getAppDbTypes)
  const ConnectionTypeOptions = connectionTypes?.map((dataSourceName) => ({
    label: (<>
      <ConnectionIcon type={dataSourceName} />
      {dataSourceName}
    </>),
    value: dataSourceName,
  }))

  // 语句类型Options
  const { data: sqlData = SqlTypes, loading: loadingSqlTypes } = useRequest(getAppSqlTypes)
  const SqlTypeOptions = sqlData.map((type) => ({ label: type, value: type }))

  // 筛选项
  const searchConfigList = useMemo((): ISearchConfigItem[] => [
    {
      name: t("auays:item_label.data_source_type"), nodeType: "select", field: "dbType", placeholder: t("auays:item_label.data_source_type"),
      selectOptions: ConnectionTypeOptions,
      nodeProps: { maxTagCount: 1, mode: "multiple", loading: loadingConnectionTypes },
      nodeStyle: { minWidth: 144 }
    },
    {
      name: t("auays:item_label.statement_type"), nodeType: "select", field: "operateType", placeholder: t("auays:item_label.statement_type"),
      selectOptions: SqlTypeOptions,
      nodeProps: { maxTagCount: 1, mode: "multiple", loading: loadingSqlTypes },
      nodeStyle: { minWidth: 144 }
    },
    {
      name: t("auays:item_label.date"), nodeType: "dateRange", field: "timeRange", placeholder: [t("auays:rpk_ph.start_date"), t("auays:rpk_ph.end_date")]
    },
  ], [ConnectionTypeOptions, SqlTypeOptions, loadingConnectionTypes, loadingSqlTypes, locales]);

  // 格式化columns方法
  const formatColumn = (requestColumns: string[], params: any, callBack: React.Dispatch<any>) => {
    let columns = isEmpty(requestColumns) ? APP_STATEMENT_DETAIL_COLUMNS : requestColumns

    const getColumnRender = (value: any, record: any, key: string) => {
      switch (key) {
        case 'applicationIp':
        case 'operateObject':
          return (<span style={{ color: '#3262FF', cursor: "pointer" }} onClick={() => {
            callBack({
              ...params,
              offset: 0, //起始条数
              [key]: record?.[key]
            })
          }}>
            {record?.[key]}
          </span>)
        case 'serverIp':
          return (
            <>
              {record.dbType && <ConnectionIcon type={record.dbType} />}
              <span style={{ color: '#3262FF', cursor: "pointer" }} onClick={() => {
                callBack({
                  ...params,
                  offset: 0, //起始条数
                  [key]: record?.[key]
                })
              }}>
                {record?.[key]}
              </span>
            </>
          )
        case 'resultFlag':
          return (<>
            {
              value === null ?
                <Tooltip placement="topRight" title={record?.errorMsg || t("auays:tip_title.cannot_fetch_terminal_exe_sql")}>
                  <InfoCircleOutlined style={{ margin: '0 2px', cursor: 'help' }} />
                </Tooltip>
                : value === 1 ?
                  <Badge status="success" text={t("bg_txt.execution_successful")} />
                  : <Badge status="error" text={renderErrorFlag(value)} />
            }
          </>)
        case 'executeSql':
          return (
            <Typography.Paragraph style={{ width: 180, fontSize: 12, wordBreak: 'break-all' }} copyable={{ text: value }}>
              {value?.length > 66 ? <Tooltip title={value}>{value.substring(0, 66) + '...'}</Tooltip> : value}
            </Typography.Paragraph>
          )
        default:
          return value
      }
    }

    let realColumns = columns.map((key: string) => ({
      key,
      title: t(APP_STATEMENT_COLUMN_FIELDS[key]),
      dataIndex: key,
      width: 100,
      render: (value: any, record: any) => getColumnRender(value, record, key)
    }))

    return realColumns
  }

  // 格式化请求参数
  const formatParams = (params: any): AuditAppLogParams => {
    const newParams = {
      ...params,
      ...params.timeRange && params.timeRange[0] && params.timeRange[1] &&
      {
        executeBeginMs: params.timeRange[0] || undefined,
        executeEndMs: params.timeRange[1] || undefined
      },
      appIp: params.applicationIp ?? undefined
    }
    delete newParams.timeRange
    delete newParams.applicationIp
    return newParams
  }

  // 模糊搜索的防抖操作
  const taskChange = debounce((value, params, callBack) => {
    callBack({ ...params, executeSql: value, offset: 0 }, true)
  }, 500)

  const headerLeftNode = (params: any, callBack: (params: any, AsyncSearchItem?: boolean) => void, controlValue: React.Dispatch<any>) => {
    return <div className={styles.leftNode}>
      <RadioGroup
        buttonStyle="solid"
        onChange={(e: any) => callBack({ ...params, resultFlag: e.target.value, offset: 0 })}
        value={Number(params?.resultFlag)}
      >
        <RadioButton value={1}>{t("auays:rd_lbl.execution_success")}</RadioButton>
        <RadioButton value={0}>{t("auays:rd_lbl.execution_failure")}</RadioButton>
      </RadioGroup>
      <Input
        prefix={search_icon}
        className={styles.sqlInput}
        allowClear
        value={params?.executeSql}
        placeholder={t("auays:inp_ph.ope_statement")}
        onChange={(e: any) => { controlValue({ ...params, executeSql: e.target.value, offset: 0 }); taskChange(e.target.value, params, callBack) }}
      />
    </div>
  }

  const headerExtraBtns = () => {
    return <Button
      icon={<RedoOutlined className="p4" />}
      onClick={() => customCommonTableRef.current?.refresh()}
    />
  }

  // 渲染审计概览
  const gotoAuditView = () => {
    dispatch(setOverviewPageState(''))
    dispatch(setOverviewPageDetailParams({}))
  }

  return (
    <Layout className="cq-container">
      <Header className="breadcrumb-header" title={t("auays:bc_title.app_statement_details")}>
        <Breadcrumb className="breadcrumb" separator=''>
          <Breadcrumb.Item>{t("auays:bc_title.audit_analysis")}</Breadcrumb.Item>
          <Breadcrumb.Separator>|</Breadcrumb.Separator>
          <Breadcrumb.Item><span onClick={gotoAuditView}>{t("auays:bc_title.audit_overview")}</span></Breadcrumb.Item>
          <Breadcrumb.Separator>/</Breadcrumb.Separator>
          <Breadcrumb.Item>{t("auays:bc_title.app_statement_details")}</Breadcrumb.Item>
        </Breadcrumb>
      </Header>
      <Layout className="cq-main">
        <Content
          className={classNames('cq-content', { [styles.appDetailContent]: true })}
        >
          <CustomCommonTable
            getColumnApi={getAndSetAuditColumn}
            getTableDataApi={findAuditAppLogs}
            formatColumn={formatColumn}
            formatParams={formatParams}
            searchHeaderParams={{
              searchConfigList: searchConfigList,
              leftNode: headerLeftNode,
              rightNode: headerExtraBtns
            }}
            ref={customCommonTableRef}
            notAllowColumns={APP_STATEMENT_DETAIL_COLUMNS}
            allColumnsFields={APP_STATEMENT_COLUMN_FIELDS}
            defaultColumnParams={defaultColumnParams}
            defaultTableParams={{
              limit: 10, //条数
              offset: 0, //起始条数
              timeRange: [
                dayjs().subtract(3, 'day').startOf('d').valueOf(),
                dayjs().endOf('d').valueOf(),
              ],
              ...location.state
            }}
          />
          <Watermark
            text={watermarkValue?.length ? watermarkValue?.join(' ') : ''}
            pack={false}
            rotate={20}
            zIndex={99}
            visible={watermarkSetting}
            {...watermarkEffect}
          />
        </Content>
      </Layout>
    </Layout>
  )
}
