import { EllipsisOutlined, ExclamationCircleOutlined } from "@ant-design/icons";
import { Drawer, Dropdown, <PERSON>u, Modal, Spin } from "antd";
import classNames from "classnames";
import React, { useEffect } from "react";
import { useRef, useState } from "react";
import styles from './index.module.scss'
import { useTranslation } from "react-i18next";
const { confirm } = Modal;

interface ICustomMetricsDrawer {
  visible: boolean;
  setVisible(val: boolean): void;
  defaultList: any[];
  loading: boolean;
  delFun: (id: number) => void;
  initFun: (metricsItem: any) => void;
}
const CustomMetricsDrawer = (props: ICustomMetricsDrawer) => {
  const { visible, setVisible, defaultList = [], loading, delFun, initFun } = props;
  const { t } = useTranslation()
  const [list, setList] = useState<any[]>([]);
  const currentId = useRef<any>()

  useEffect(() => {
    if (visible) {
      setList(defaultList?.map((item: any) => ({ ...item, isActive: false })) || [])
    }
  }, [visible, defaultList])

  const onClose = () => {
    setVisible(false);
  };

  const showDeleteConfirm = (id: number) => {
    confirm({
      title: t("auays:md_title.confirm_delete"),
      icon: <ExclamationCircleOutlined />,
      okText: t("auays:btn.delete"),
      okType: 'danger',
      cancelText: t("auays:btn.cancel"),
      onOk: () => {
        delFun(id)
      }
    });
  };

  const handleListener = (e: any) => {
    if (document.getElementById(currentId.current)?.contains(e.target)) {
      return;
    } else {
      setList(list.map((item: any) => {
        if (item.id === currentId.current) return { ...item, isActive: false }
        else return item
      }))
      currentId.current = null
      document.removeEventListener('click', handleListener)
    }
  }

  const activeClick = (id: number) => {
    currentId.current = id
    setList(list.map((item: any) => {
      if (item.id === currentId.current) return { ...item, isActive: true }
      else return { ...item, isActive: false }
    }))
    document.addEventListener('click', handleListener);
  }

  const onDoubleClick = (item: any) => {
    initFun(item)
    setVisible(false)
  }

  return (
    <Drawer
      title={t("auays:drawer_title.all_metrics")}
      placement="right"
      onClose={onClose}
      visible={visible}
      width={380}
      className={styles.customMetricsDrawer}
      closable
    >
      <Spin spinning={loading}>
        {
          list.map((item: any, index: number) => {
            return (
              <div
                key={item.id}
                id={item.id}
                className={classNames(styles.chartList, { [styles.chartListActive]: item.isActive })}
                onClick={() => activeClick(item.id)}
                onDoubleClick={() => onDoubleClick(item)}
                role="button"
              >
                <div className={styles.chartTitle} >
                  <div className={styles.name}>{item.name}</div>
                  <div className={styles.opeIcon}>
                    <Dropdown
                      overlay={
                        <Menu>
                          <Menu.Item onClick={() => {
                            showDeleteConfirm(item.id)
                          }}>{t("auays:btn.delete")}</Menu.Item>
                        </Menu>
                      }
                      trigger={['click']}
                    >
                      <EllipsisOutlined />
                    </Dropdown ></div>
                </div>
                <div className={styles.date}>{t("auays:div_lbl.recently_saved")}{item?.updatedAt || t("auays:emp_desc.no_items")}</div>
              </div>
            );
          })
        }
      </Spin>
    </Drawer>
  );
}
export default CustomMetricsDrawer;