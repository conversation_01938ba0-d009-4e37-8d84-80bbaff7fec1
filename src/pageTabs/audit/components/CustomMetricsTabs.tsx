import React from "react"
import styles from "./index.module.scss"
import { CloseOutlined, PlusOutlined } from "@ant-design/icons"
import classNames from "classnames"
import { Tooltip } from "antd"
import { useTranslation } from "react-i18next"

interface ICustomMetricsTabs {
  tabList: any[]
  metricsInfo: any
  addTab: () => void // 新增操作
  delTab: (item: any) => void // 删除操作
  onTabChange: (id: string | number) => void // tab切换
  defaultName?: string // 默认tab名称
  field?: string // 名称字段
}

export const CustomMetricsTabs = (props: ICustomMetricsTabs) => {
  const { t } = useTranslation()
  const { tabList, metricsInfo, addTab, delTab, onTabChange, defaultName = t("auays:df_name.unnamed_chart"), field = 'chartName' } = props

  return <div className={styles.customMetricsTabs}>
    {
      tabList.map((item: any, index: number) => {
        const { tabId, tabName } = item
        const isActived: boolean = tabId === metricsInfo.id
        const title = (isActived ? metricsInfo?.[field] : tabName) || defaultName
        return <div style={{ maxWidth: `calc((100% - 82px) / ${tabList.length})` }} className={styles.chartTabDiv} key={tabId}>
          <div
            className={classNames(styles.chartTabItem, { [styles.chartTabItemActive]: isActived }, { [styles.hiddenDelIcon]: tabList.length <= 1 })}
            onClick={() => onTabChange(tabId)}
            role="button"
          >
            <div className={styles.chartName}>
              <Tooltip title={title}>
                {title}
              </Tooltip>
            </div>
            {
              // 只剩一项不允许删除
              tabList.length > 1 && <CloseOutlined className={styles.tabCloseIcon} onClick={(e: any) => {
                e.stopPropagation();
                delTab(item)
              }} />
            }
          </div>
          {index !== tabList.length - 1 && <div className={styles.divider} />}
        </div>
      })
    }
    <div className={styles.addTabBtn} onClick={addTab} role="button">
      <PlusOutlined />
    </div>
  </div>
}