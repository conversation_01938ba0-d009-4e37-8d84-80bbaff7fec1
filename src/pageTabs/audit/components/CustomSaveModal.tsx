import { Form, Input, Modal } from "antd"
import { isEmpty } from "lodash"
import React, { useEffect } from "react"
import { useTranslation } from "react-i18next"
import { useSelector } from "src/hook"

interface ICustomSaveModal {
  visible: boolean
  onCancel: () => void
  onFinishOpe: (values: any) => void
  defaultValues?: any // 表单的默认值
  showRemark?: boolean // 是否需要备注字段
}
const layout = {
  labelCol: { span: 7 },
  wrapperCol: { span: 17 },
};
const FormItem = Form.Item

export const CustomSaveModal = (props: ICustomSaveModal) => {
  const { visible, onCancel, onFinishOpe, defaultValues = {}, showRemark = true } = props
  const { t } = useTranslation()
  const { locales } = useSelector(state => state.login)
  const [form] = Form.useForm()

  const onFinish = () => {
    form.validateFields().then((values: any) => {
      onFinishOpe(values)
      form.resetFields()
      onCancel()
    }).catch((err: any) => {
      console.log(err, t("auays:clg_err.save_form_val_err"));
    })
  }

  // 初始化表单默认值
  useEffect(() => {
    if (defaultValues && !isEmpty(defaultValues)) {
      form.setFieldsValue({ ...defaultValues })
    }
  }, [JSON.stringify(defaultValues)])

  return <Modal
    title={t("auays:md_title.save_settings")}
    visible={visible}
    onOk={onFinish}
    onCancel={onCancel}
    width={450}
    destroyOnClose
  >
    <Form
      {...layout}
      form={form}
      name="save_metrics"
    >
      <FormItem
        label={t("auays:item_label.metrics_name")}
        name="name"
        labelCol={locales === 'en' ? { span: 9 } : { span: 7 }}
        rules={[{ required: true, message: t("auays:inp_ph.enter_name") }]}
      >
        <Input placeholder={t("auays:inp_ph.enter_name")} />
      </FormItem>
      {
        showRemark &&
        <FormItem label={t("auays:item_label.remark")} name="remark" >
          <Input placeholder={t("auays:inp_ph.enter_remark")} />
        </FormItem>
      }
    </Form>
  </Modal >
}