.customColumnsDrawer {
  :global {
    .ant-checkbox-group-item {
      display: block;
      margin-bottom: 10px;
    }
  }
}

.customMetricsTabs {
  position: absolute;
  bottom: 0;
  height: 36px;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin: 2px 0;
  width: 100%;

  .chartTabDiv {
    display: flex;
    justify-content: center;
    align-items: center;

    .chartTabItem {
      height: 26px;
      color: #4E5969;
      font-size: 14px;
      font-weight: normal;
      padding: 2px 8px;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      margin: 0 8px;
      cursor: pointer;
      width: calc(100% - 1px);
      overflow: auto;

      .chartName {
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
      }

      .tabCloseIcon {
        font-size: 10px;
        margin-left: 5px;
        display: none;
        cursor: pointer;
      }

      &:hover {
        background: #FFFFFF;
        border-radius: 2px;

        .chartName {
          width: calc(100% - 15px);
        }

        .tabCloseIcon {
          display: block;
        }
      }
    }

    .hiddenDelIcon {
      &:hover {
        .chartName {
          width: 100%;
        }
      }
    }

    &:first-child {
      .chartTabItem {
        margin-left: 0px;
      }
    }

    &:last-child {
      .chartTabItem {
        margin-right: 0px;
      }
    }

    .chartTabItemActive {
      color: #165DFF;
    }

    .divider {
      width: 1px;
      height: 12px;
      background: #E5E6EB;
    }

  }

  .addTabBtn {
    height: 26px;
    width: 26px;
    background: #FFFFFF;
    border-radius: 3px;
    margin-left: 8px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
  }
}

.customMetricsDrawer {
  :global {
    .ant-drawer-body {
      padding: 12px 16px;
    }
  }

  .chartList {
    height: 52px;
    width: 348px;
    margin-bottom: 16px;
    padding: 4px 8px;

    &:last-child {
      margin-bottom: 0;
    }

    &:hover {
      background-color: #F0F4FF;
      border-radius: 4px;
    }

    &:hover .chartTitle>.opeIcon {
      visibility: visible;
    }

    .chartTitle {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;

      .name {
        color: #0F244C;
        font-size: 14px;
        font-weight: 350;
        line-height: 22px;
        height: 22px;
        margin-bottom: 2px;
        width: calc(100% - 20px);
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .opeIcon {
        margin-left: 4px;
        font-size: 16px;
        display: flex;
        justify-content: center;
        align-items: center;
        visibility: hidden;
      }
    }

    .date {
      color: #86909C;
      font-size: 12px;
      font-weight: 350;
      line-height: 20px;
      height: 20px;
    }
  }

  .chartListActive {
    background-color: #3262FF;
    border-radius: 4px;

    .chartTitle>.name {
      color: #FFFFFF;
    }

    .chartTitle>.opeIcon {
      color: #FFFFFF;
    }

    .date {
      color: #F7F9FC;
    }

    &:hover {
      background-color: #3262FF;
      border-radius: 4px;
    }
  }
}