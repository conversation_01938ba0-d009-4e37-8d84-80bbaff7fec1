import React from 'react'

export const CustomAuditContext = React.createContext<any>({
  chartInfo: {},
  onInfoChange: () => {},
  eleNodes: {},
  onFieldInfoChange: () => {},
  filterNodes: [],
  onfilterChange: () => {},
  chartData: [],
  baseData: [],
  filterList: [],
  onPreFilterChange: () => {},
  saveChart: () => {},
  deleteChart: () => {},
  initChart: () => {},
  onTreeDataChange: () => {},
  reset: () => {},
  downloadChart: () => {},
  tabList: [],
  addChartTab: () => {},
  delChartTab: () => {},
  onTabChange: () => {},
})
