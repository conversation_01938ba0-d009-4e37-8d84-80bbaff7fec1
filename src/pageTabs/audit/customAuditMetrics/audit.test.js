import { chartOptionsListGenerator } from "./utils";

describe("测试图表options生成器函数chartOptionsListGenerator", () => {
  it("场景1 => 应生成具有一个分类轴的图表options对象", () => {
    const data = [
      { category: "A", amount: 10 },
      { category: "B", amount: 20 },
    ];
    const xFieldList = ["category"]; // 用户仅拖拽category指标字段至图表分类轴
    const tField = "amount"; //用户未拖拽指标字段至图例
    const seriesName = "count";

    const result = chartOptionsListGenerator(data, xFieldList, tField, seriesName);

    expect(result).toMatchObject({
      noNumberAxis: [{ data: ["A", "B"] }],
      series: [
        { name: "count", data: [10, 20] },
      ],
      tooltips: ["A", "B"],
    });
  });

  it("场景2 => 应生成具有多个分类轴的图表options对象", () => {
    const data = [
      { category: "A", type:'TYPE1', amount: 10 },
      { category: "B", type:'TYPE1', amount: 20 },
      { category: "A", type:'TYPE2', amount: 35 },
      { category: "B", type:'TYPE2', amount: 40 },
    ];
    const xFieldList = ["category", "type"]; // 用户拖拽[category]和[type]指标字段至图表分类轴
    const tField = "amount"; //用户未拖拽指标字段至图例
    const seriesName = "count";

    const result = chartOptionsListGenerator(data, xFieldList, tField, seriesName);

    expect(result).toMatchObject({
      noNumberAxis: [{ data: ["TYPE1", "TYPE1", "TYPE2", "TYPE2"] },{ data: ["A", "B", "A", "B"] }],
      series: [
        { name: "count", data: [10, 20, 35, 40] },
      ],
      tooltips: ["A - TYPE1", "B - TYPE1", "A - TYPE2", "B - TYPE2"],
    });
  });

  it("场景3 => 应生成具有多个分类轴和多个series的图表options对象", () => {
    const data = [
      { category: "A", type:'TYPE1', legend:'L1', amount: 10 },
      { category: "B", type:'TYPE1', legend:'L1', amount: 20 },
      { category: "A", type:'TYPE2', legend:'L2', amount: 35 },
      { category: "B", type:'TYPE2', legend:'L2', amount: 40 },
    ];
    const xFieldList = ["category", "type"]; // 用户拖拽[category]和[type]指标字段至图表分类轴
    const tField = "legend"; //用户拖拽[legend]指标字段至图例

    const result = chartOptionsListGenerator(data, xFieldList, tField);

    expect(result).toMatchObject({
      noNumberAxis: [{ data: ["TYPE1", "TYPE1", "TYPE2", "TYPE2"] },{ data: ["A", "B", "A", "B"] }],
      series: [
        { name: "L1", data: [10, 20, 0, 0] },
        { name: "L2", data: [0, 0, 35, 40] },
      ],
      tooltips: ["A - TYPE1", "B - TYPE1", "A - TYPE2", "B - TYPE2"],
    });
  });

  it("场景4 => 此函数具有去重功能 ", () => {
    const data = [
      { category: "A", type:'X', legend: "A", amount: 10 },
      { category: "B", type:'Y', legend: "B", amount: 20 },
      { category: "C", type:'Y', legend: "C", amount: 35 },
      { category: "C", type:'X', legend: "B", amount: 17 },
      { category: "C", type:'X', legend: "B", amount: 29 }, // 重复数据
    ];
    const xFieldList = ["category", "type"];
    const tField = "legend";

    const result = chartOptionsListGenerator( data, xFieldList, tField);

    expect(result).toMatchObject({
      noNumberAxis: [{ data: ["X", "Y", "Y", "X"]}, { data: ["A", "B", "C", "C"] }],
      series: [
        { name: "A", data: [10, 0, 0, 0] },
        { name: "B", data: [0, 20, 0, 17] },
        { name: "C", data: [0, 0, 35, 0] },
      ],
      tooltips: ["A - X", "B - Y", "C - Y", "C - X"],
    });
  });
});
