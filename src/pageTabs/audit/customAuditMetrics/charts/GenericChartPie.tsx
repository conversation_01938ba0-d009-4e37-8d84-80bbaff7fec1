import * as echarts from 'echarts';
import { cloneDeep, isEmpty } from 'lodash';
import React, { useEffect } from 'react';
import styles from './charts.module.scss'
import { CUSTOM_AUDIT_CHART_ELEMENT } from '../constants';
type EChartsOption = echarts.EChartsOption;

// 通用极坐标轴饼图
export const GenericChartPie = (props: any) => {
  const { data, height, id = 'new', chartType } = props
  let myChart: any;

  const getOption = (data: any, baseOptions: any): EChartsOption => {
    // 由于是同一个方法生成的数组，为不互相干扰，要深拷贝一下
    const optionData = cloneDeep(data)
    const gridBottom = `${optionData.noNumber?.length * 2 + 4}%`

    return {
      tooltip: {
        trigger: 'item',
        // 自定义提示框内容--因为有多层X轴的可能性
        formatter: (param: any) => {
          const index = param.dataIndex;
          const categoryValues = optionData.tooltips[index]?.split('-')
          const categorys = optionData.xName?.split('-').map((label: string, index: number) => {
            return {
              label,
              value: categoryValues?.[index]
            }
          })
          return `<div>
            ${categorys.map((item: any) => {
            return `<div style="margin-right:10px; max-width:800px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;">
                ${item.label}: ${item.value}
              </div>`
          }).join('')}
            <div>${param.marker} ${param.seriesName}: ${param.value}(${param.percent}%)</div>
          </div>`
        }
      },
      legend: {
        top: 30,
        right: 20,
        bottom: 20,
        orient: 'vertical',
        type: 'scroll',
        textStyle: {
          width: 150,
          overflow: 'truncate',
        }
      },
      grid: {
        left: '2%',
        right: '2%',
        bottom: gridBottom,
        containLabel: true
      },
      series: optionData.series?.map((s: any) => ({
        ...s,
        type: 'pie',
        radius: '70%',
        center: ['40%', '50%'],
        label: {
          width: 250,
          overflow: 'truncate',
          formatter: (param: any) => {
            return `${param.name}：${param.value}(${param.percent}%)`
          }
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        },
        data: s.data?.map((item: any, index: number) => {
          return { name: optionData.tooltips[index], value: item }
        }),
        ...baseOptions?.series?.(s, optionData) || {},
      }))
    };
  };

  useEffect(() => {
    if (!isEmpty(data)) {
      if (!myChart) {
        let chartDom = document.getElementById(`overview_chart_${id}`)!;
        myChart = echarts.init(chartDom);
      }

      const chartInfo = CUSTOM_AUDIT_CHART_ELEMENT[chartType]
      const { chartOptions = {} } = chartInfo

      let option = getOption(data, chartOptions);
      myChart.setOption(option, true);
    }
  }, [data, id, chartType]);

  return !isEmpty(data) ? <div className={styles.pieChart}>
    <div className={styles.tName} id='t_name'>{data?.xName || ''}</div>
    <div id={`overview_chart_${id}`} style={{ height: height, width: '100%' }}></div>
  </div> : null
}

