import * as echarts from 'echarts';
import { cloneDeep, isEmpty } from 'lodash';
import React, { useEffect } from 'react';
import styles from './charts.module.scss'
import { CUSTOM_AUDIT_CHART_ELEMENT } from '../constants';
import { decimalToPercentage, percentChartData } from '../utils';

type EChartsOption = echarts.EChartsOption;

// 通用直角坐标轴图表
export const GenericChartXY = (props: any) => {
  const { data, height, id = 'new', chartType, showPercent = false } = props

  let myChart: any;

  const getOption = (data: any, legendLeft: number, baseOptions: any, realWidth: number, realHeight: number, showPercent: boolean, currentXNum: number): EChartsOption => {
    // 由于是同一个方法生成的数组，为不互相干扰，要深拷贝一下
    const optionData = cloneDeep(data)
    // 分类轴指标数量
    const categoryNum = optionData?.noNumber?.[0]?.data?.length
    const xNum = optionData.noNumber?.length
    const showDataZoom = baseOptions?.isTransverse ? categoryNum > 25 : categoryNum > 15

    // 计算grid的bottom
    const getGridBottom = (): string => {
      if (!baseOptions?.isTransverse) {
        return '12%'
      }
      if (!showDataZoom) {
        return `${(xNum) * 6 + 5}%`
      }
      const morePadding = `${(xNum) * 7 + 16}%`
      return morePadding
    }
    let gridBottom: string = getGridBottom()

    // 计算grid的left
    const getGridLeft = (): number => {
      if (baseOptions?.isTransverse) {
        return 80
      }
      if (!showDataZoom) {
        return xNum * 100
      }
      if (xNum === 1) {
        return 170
      }
      return xNum * 100 + 50
    }
    let gridLeft: number = getGridLeft()

    // 计算dataZoom的end
    const end = categoryNum > 25 ? Math.round(25 / categoryNum * 100) : 100

    return {
      tooltip: {
        trigger: 'axis',
        confine: true,
        axisPointer: {
          type: 'shadow',
          label: {
            backgroundColor: '#6a7985',
            color: '#fff',
            show: true,
            lineHeight: 0,
            formatter: (params: any) => {
              if (params?.seriesData.length > 0 && params.axisIndex === 0) {
                const index = params?.seriesData?.[0]?.dataIndex;
                const categtoryLabel = optionData.tooltips[index]
                return `{a|${categtoryLabel}}`
              }
              else if (params.axisIndex === 0 && params.value) {
                return `{a|${Math.round(params.value * 100) / 100}}`
              }
              else return ''
            },
            rich: {
              a: {
                lineHeight: 12,
                align: 'center'
              }
            }
          },
          ...baseOptions?.tooltip?.axisPointer || {}
        },
        // 自定义提示框内容--因为有多层X轴的可能性
        formatter: (params: any) => {
          const newParams = params
          const index = newParams?.[0]?.dataIndex;
          let categoryValues = cloneDeep(optionData.noNumber)
          categoryValues = categoryValues.reverse()
          const categorys = optionData.xName?.split('-').map((label: string, i: number) => {
            return {
              label,
              value: categoryValues?.[i]?.data?.[index] || '_'
            }
          })
          const series = optionData?.series
          return `<div>
                    ${categorys.map((item: any) => {
            return `<div style="max-width:${realWidth / 2 - 100}px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;">
                            ${item.label}: ${item.value}
                          </div>`
          }).join('')}
                    <div style="display:flex; flex-wrap:wrap; max-width:${realWidth / 2 - 100}px; max-height:400px; overflow:hidden;">
                      ${newParams.map((item: any) => {
            const numData = series?.filter((s: any) => String(s.name) === String(item.seriesName))?.[0]?.numData || []
            return `<div style="margin-right:10px;max-width:${realWidth / 4 - 60}px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;">
                                    <span style="display:inline-block;margin-right:4px;border-radius:10px;width:10px;height:10px;background-color:${item.color};"></span>
                                    ${item.seriesName}: ${showPercent ? (numData?.[index]) : item.data}${showPercent ? '(' + decimalToPercentage(item.data) + '%)' : ''}
                                  </div>`
          }).join('')}
                    </div>
                  </div>`
        }
      },
      dataZoom: [
        baseOptions?.isTransverse ? {
          xAxisIndex: optionData?.noNumber?.map((_: any, index: number) => index),
          show: showDataZoom,
          start: 0,
          end: end,
          showDataShadow: true,
          height: 20,
          bottom: 12,
          left: 120,
          right: 120,
          labelFormatter: (index: number) => {
            const categtoryLabel = optionData.tooltips[index]
            return categtoryLabel;
          },
          textStyle: {
            width: 120,
            overflow: 'truncate',
          }
        } : {
          yAxisIndex: optionData?.noNumber?.map((_: any, index: number) => index),
          show: showDataZoom,
          start: 0,
          end: end,
          showDataShadow: true,
          width: 20,
          bottom: 20,
          top: 42,
          left: 30,
          orient: 'vertical',
          labelFormatter: (index: number) => {
            const categtoryLabel = optionData.tooltips[index]
            return categtoryLabel;
          },
          textStyle: {
            width: 75,
            overflow: 'truncate',
          }
        }
      ],
      legend: {
        show: optionData.series?.length > 1,
        top: 0,
        left: legendLeft,
        type: 'scroll',
      },
      grid: {
        left: gridLeft,
        right: '5%',
        bottom: gridBottom,
        top: optionData.series?.length > 1 ? 60 : '6%',
      },
      xAxis: baseOptions?.isTransverse ? optionData.noNumber?.map((xAxis: any, index: number) => ({
        triggerEvent: true,
        data: xAxis.data,
        name: index === optionData.noNumber.length - 1 ? optionData.xName : '',
        nameLocation: 'middle',
        type: 'category',
        position: 'bottom',
        nameGap: 20 * (index + 1) + 10,
        axisTick: {
          show: !showDataZoom && index === 0,
          interval: 0,
          length: 8,
        },
        axisLabel: {
          width: (realWidth * 0.95 - gridLeft) / (currentXNum + 1),
          overflow: 'truncate',
          margin: index === 0 ? 10 : (10 + index * 15),
          interval: 0,
          backgroundColor: 'rgba(0, 0, 0, 0)' //给个背景，不然mouseover会检测不到
        },
        ...baseOptions?.xAxis?.(optionData, xAxis, index) || {},
      })) : optionData?.yName?.map((item: string) => {
        return {
          name: item,
          nameLocation: 'middle',
          type: 'value',
          nameGap: 25,
          triggerEvent: true,
          max: showPercent ? 1 : undefined,
          axisLabel: {
            formatter: (value: any) => {
              return showPercent ? value * 100 + '%' : value;
            }
          },
          ...baseOptions?.xAxis?.(optionData) || {},
        }
      }),
      yAxis: baseOptions?.isTransverse ? optionData?.yName?.map((item: string, index: number) => {
        return {
          triggerEvent: true,
          name: item,
          nameLocation: 'middle',
          type: 'value',
          nameGap: 50,
          max: showPercent ? 1 : undefined,
          axisLabel: {
            formatter: (value: any) => {
              return showPercent ? value * 100 + '%' : value;
            }
          },
          splitLine: {
            show: index !== 1
          },
          ...baseOptions?.yAxis?.(optionData) || {},
        }
      }) : optionData.noNumber?.map((xAxis: any, index: number) => ({
        data: xAxis.data,
        name: index === optionData.noNumber.length - 1 ? optionData.xName : '',
        triggerEvent: true,
        nameLocation: 'end',
        nameGap: 5,
        type: 'category',
        axisTick: {
          show: !showDataZoom && index === 0,
          length: 15,
        },
        position: 'left',
        axisLabel: {
          margin: index === 0 ? 10 : 100 * index,
          interval: 'auto',
          width: 80,
          overflow: 'truncate',
          backgroundColor: 'rgba(0, 0, 0, 0)' //给个背景，不然mouseover会检测不到
        },
        ...baseOptions?.yAxis?.(optionData, xAxis, index) || {},
      })),
      series: [...(optionData?.series?.map((s: any, index: number) => {
        const elseTooltipSettings = baseOptions?.series?.(s, realWidth, realHeight, index, optionData) || {}
        return {
          ...s,
          type: 'bar',
          emphasis: {
            focus: 'series'
          },
          tooltip: {
            trigger: 'item',
            alwaysShowContent: false,
            // 自定义提示框内容--因为有多层X轴的可能性
            formatter: (params: any) => {
              const index = params.dataIndex;
              let categoryValues = cloneDeep(optionData.noNumber)
              categoryValues = categoryValues.reverse()
              const categorys = optionData.xName?.split('-').map((label: string, i: number) => {
                return {
                  label,
                  value: categoryValues?.[i]?.data?.[index] || '_'
                }
              })
              const numData = s?.numData || []
              return `<div>
                     ${categorys.map((item: any) => {
                return `<div style="max-width:${realWidth / 2 - 100}px;max-height:400px;overflow:hidden;text-overflow:ellipsis;white-space:normal;">
                          ${item.label}: ${item.value}
                        </div>`
              }).join('')}
               <div style="max-width:${realWidth / 2 - 100}px; max-height:400px;overflow:hidden;text-overflow:ellipsis;white-space:normal;">
                 <span style="display:inline-block;margin-right:4px;border-radius:10px;width:10px;height:10px;background-color:${params.color};"></span>
                 ${params.seriesName}: ${showPercent ? (numData?.[index]) : params.data}${showPercent ? '(' + decimalToPercentage(params.data) + '%)' : ''}
               </div>
             </div>`
            }
          },
          barMaxWidth: 80,
          ...elseTooltipSettings,
        }
      }) || []), ...(optionData?.series?.map((s: any) => { //为了同时展示item和axis的提示框，需要将series的type设置为line
        return {
          ...s,
          type: 'line',
          emphasis: {
            focus: 'series'
          },
          itemStyle: {
            opacity: 0
          },
          lineStyle: {
            opacity: 0
          },
          tooltip: {
            trigger: 'axis'
          }
        }
      }) || [])
      ]
    };
  };

  useEffect(() => {
    if (isEmpty(data)) { return }
    let newData: any = {}
    if (showPercent) {
      newData = percentChartData(data)
    }
    else {
      newData = data
    }
    const tNameDom = document.getElementById(`t_name_${id}`)!;
    const width = tNameDom?.offsetWidth || 0
    const legendLeft = width + 40 //图标的左边距
    if (!myChart) {
      let chartDom = document.getElementById(`overview_chart_${id}`)!;
      if (echarts.getInstanceByDom(chartDom)) {
        echarts.dispose(chartDom);
      }
      myChart = echarts.init(chartDom);
    }
    const categoryNum = newData?.noNumber?.[0]?.data?.length
    // 图表宽高
    let containerWidth = myChart.getWidth();
    let containerHeight = myChart.getHeight();

    const chartInfo = CUSTOM_AUDIT_CHART_ELEMENT[chartType]
    const { chartOptions = {} } = chartInfo
    const showDataZoom = chartOptions?.isTransverse ? categoryNum > 25 : categoryNum > 15
    const moreNum = chartOptions?.isTransverse ? 25 : 15
    // 当前图表展示的条数
    const number = showDataZoom ? moreNum : categoryNum

    // 获取图表配置
    let option = getOption(newData, legendLeft, chartOptions, containerWidth, containerHeight, showPercent, number);
    setTimeout(() => { myChart.setOption(option, true) }, 500);
    let dataIndexForY = 0

    // 以下操作均是为了在有缩放条的情况下xAxis的label宽度
    if (showDataZoom && chartOptions?.isTransverse) {
      myChart.on('datazoom', (event: any) => {
        const start = event?.start
        const end = event?.end
        const subNum = end - start
        const number = Math.round(Number(subNum) * categoryNum / 100)
        dataIndexForY = Math.round(Number(start) * categoryNum / 100) + 1
        let oldOption = myChart.getOption()
        const newOption = getOption(data, legendLeft, chartOptions, containerWidth, containerHeight, showPercent, number);
        oldOption.xAxis = newOption.xAxis
        myChart.setOption(oldOption, true);
      })
    }

    let labelValue: string = ''
    myChart.on('mouseover', (params: any) => {
      if (['xAxis', 'yAxis'].includes(params.componentType) && params.targetType === "axisLabel") {
        labelValue = params.value
        const offsetX = params.event?.offsetX + 5
        const offsetY = params.event?.offsetY + 5
        const dataIndex = params?.dataIndex
        let oldOption = myChart.getOption()
        oldOption.tooltip = {
          formatter: () => {
            return `<div style="max-width:${containerWidth / 3}px;overflow:hidden;text-overflow:ellipsis;white-space:normal;">
                            ${labelValue}
                      </div>`
          },
          confine: true,
          alwaysShowContent: true
        }
        oldOption.series = oldOption.series.map((item: any) => {
          return {
            ...item,
            tooltip: {
              formatter: () => {
                return `<div style="max-width:${containerWidth / 3}px;overflow:hidden;text-overflow:ellipsis;white-space:normal;">
                            ${labelValue}
                      </div>`},
              confine: true,
              alwaysShowContent: true
            }
          }
        })
        myChart.setOption(oldOption, true)
        myChart.dispatchAction({
          type: 'showTip',
          seriesIndex: 0,
          dataIndex: dataIndex ?? dataIndexForY, //这里要设置dataIndex，不然tooltip不会显示
          position: [offsetX, offsetY]
        })
      }
    })
    myChart.on('mouseout', (params: any) => {
      if (['xAxis', 'yAxis'].includes(params.componentType) && params.targetType === "axisLabel") {
        const newOption: any = getOption(newData, legendLeft, chartOptions, containerWidth, containerHeight, showPercent, number);
        let oldOption = myChart.getOption()
        oldOption = {
          ...oldOption,
          tooltip: newOption.tooltip,
          series: oldOption.series.map((s: any, index: number) => ({
            ...s,
            tooltip: newOption?.series?.[index]?.tooltip,
          }))
        }
        myChart.setOption(oldOption, true);
        labelValue = ''
      }
    })

  }, [data, id, chartType, showPercent]);

  return !isEmpty(data) ? <div className={styles.chart}>
    <div className={styles.tName} id={`t_name_${id}`}>{data?.tName && data.series?.length > 1 ? data?.tName : ''}</div>
    <div id={`overview_chart_${id}`} style={{ height: height, width: '100%' }}></div>
  </div> : null
}

