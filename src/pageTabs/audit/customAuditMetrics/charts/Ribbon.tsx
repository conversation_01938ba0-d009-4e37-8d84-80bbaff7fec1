import * as echarts from 'echarts';
import { cloneDeep, isEmpty } from 'lodash';
import React, { useEffect } from 'react';
import styles from './charts.module.scss'
import { decimalToPercentage } from '../utils';

type EChartsOption = echarts.EChartsOption;

// 丝带图
export const Ribbon = (props: any) => {
  const { data, height, id = 'new', showPercent } = props
  let myChart: any;
  const maxY = 100; //将数值轴和高度均分100份

  function getOffset(datas: any, dateIndex: any, maxY: number, max: number) {
    let offset = 0;
    if (max !== 0)  {
      datas.map((data: number[]) => {
      const v = data[dateIndex];
      const itemValue = isNaN(v) ? 0 : Number(v);
      offset += maxY * (itemValue / max);
    })
    }
    return offset;
  }

  function getChartData({ dataItem = [], index, name, total, maxHeight, realWidth, currentXNum }: any) {
    const dataResult: any = [];
    const datas: any[] = []
    data.series.map((item: any, i: number) => {
      if (i < index) {
        datas.push(item.data)
      }
    })
    dataItem?.forEach((v: any, dateIndex: any) => {
      // 先判断强制转换为number会不会是NAN
      const value = isNaN(v) ? 0 : Number(v)
      const yPercent = total ? maxY * (value / total) : 0; //一百份中占几份
      const ySize = total ? maxHeight * (value / total) : 0;
      const offset = getOffset(datas, dateIndex, maxY, total);
      let radioValue: number = 0
      if (value === 0) {
        radioValue = offset
      }
      else {
        const rValue = yPercent + offset
        radioValue = rValue > 100 ? 100 : rValue
      }
      const barWidth = (realWidth * 0.9) / (currentXNum * 3)
      dataResult.push({
        name,
        value: radioValue,
        radioValue,
        realValue: v,
        symbolOffset: [0, '50%'],
        symbolSize: [barWidth, ySize]
      });

      if (dateIndex < dataItem?.length - 1) {
        new Array(3).fill(0).forEach((_, lineIndex) => {
          dataResult.push({
            value: '',
            radioValue,
            realValue: v,
            isLine: true,
            lineIndex
          });
        });
      }
    });
    return dataResult;
  }
  function createLineChart({ seriesData = [], max, optionData }: any) {
    const spaceLineSeries = [];
    const lineSeries = [];
    for (const seriesIndex in seriesData) {
      const seriesItem = seriesData[seriesIndex];
      const defaultLineSeries = {
        type: 'line',
        name: seriesItem.name,
        stack: `Line-${seriesIndex}`,
        smooth: 0.3,
        lineStyle: {
          width: 0,
          opacity: 0
        },
        symbol: 'none',
        showSymbol: false,
        triggerLineEvent: true,
        silent: true,
        areaStyle: {},
        emphasis: {
          focus: 'series'
        },
        tooltip: {
          trigger: 'axis'
        },
      };

      spaceLineSeries.push({
        ...defaultLineSeries,
        areaStyle: {
          opacity: 0
        },

        data: getLineData(seriesItem?.data, seriesIndex, true)
      });

      lineSeries.push({
        ...defaultLineSeries,
        data: getLineData(seriesItem?.data, seriesIndex)
      });
    }

    function getLineData(data: any, seriesIndex: any, isSpace = false) {
      const datas: any[] = []
      optionData.series.map((item: any, i: number) => {
        if (i < seriesIndex) {
          datas.push(item.data)
        }
      })

      const result = data?.map((item: any = {}, index: number) => {
        const dateIndex = Math.floor(index / 4);
        const lineIndex = index % 4;
        const nextItem = data?.[index + (4 - lineIndex)] || {};

        const offset = getOffset(datas, dateIndex, maxY, max);
        const nextOffset = getOffset(datas, dateIndex + 1, maxY, max);
        let spaceValue;
        let value: any = item.radioValue - offset;

        switch (lineIndex) {
          case 0:
            spaceValue = offset;
            break;
          case 1:
            spaceValue = offset;
            break;
          case 2:
            spaceValue = (nextOffset + offset) / 2;
            value = (nextItem.radioValue + item.radioValue) / 2 - spaceValue;
            break;
          case 3:
            spaceValue = nextOffset;
            value = nextItem.radioValue - nextOffset;
            break;
        }
        const newItem = {
          ...item,
          value: isSpace ? spaceValue : value
        };
        return newItem;
      });
      return result;
    }

    return [...spaceLineSeries, ...lineSeries];
  }

  const getOption = (data: any, legendLeft: number, realWidth: number, realHeight: number, total: number, showPercent: boolean, currentXNum: number): EChartsOption => {
    // 由于是同一个方法生成的数组，为不互相干扰，要深拷贝一下
    const optionData = cloneDeep(data)
    const categoryNum = ((optionData?.noNumber?.[0]?.data?.length - 1) * 3) + optionData?.noNumber?.[0]?.data?.length
    const showDataZoom = categoryNum > 25
    const xNum = optionData.noNumber?.length

    // 计算grid的top和bottom
    let gridBottom: number = 0
    if (showDataZoom) {
      gridBottom = xNum === 1 ? 20 : ((xNum + 1) * 7 + 4);
    }
    else {
      gridBottom = xNum === 1 ? 12 : ((xNum + 1) * 5 + 4)
    }
    const gridTop = optionData.series?.length > 1 ? 9 : 6
    const allHeight = realHeight * (100 - (gridBottom + gridTop)) / 100

    // 计算dataZoom的end
    const end = categoryNum > 25 ? Math.round(25 / categoryNum * 100) : 100
    // 非数值轴
    const newxAxis: any = optionData.noNumber.map((xAxis: any, index: number) => {
      const newData: any[] = []
      // 非数值轴的data处理
      xAxis.data.map((item: any, i: number) => {
        newData.push(item)
        if (Number(i) < xAxis.data?.length - 1) {
          new Array(3).fill(0).forEach((_, lineIndex) => {
            newData.push(`line-${lineIndex}`);
          });
        }
      })
      return {
        data: newData,
        name: index === optionData.noNumber.length - 1 ? optionData.xName : '',
        nameLocation: 'middle',
        type: 'category',
        position: 'bottom',
        nameGap: 20 * (index + 1) + 8,
        triggerEvent: true,
        axisTick: {
          show: !showDataZoom && index === 0,
          length: 8,
          interval: () => {
            return false
          }
        },
        axisLabel: {
          width: (realWidth * 0.9) / (currentXNum + 1),
          overflow: 'truncate',
          margin: index === 0 ? 8 : (8 + index * 15),
          interval: 0,
          backgroundColor: 'rgba(0, 0, 0, 0)', //给个背景，不然mouseover会检测不到
          formatter: (value: any) => {
            return value?.indexOf('line') === 0 ? '' : value;
          },
        }
      }
    })

    const seriesData = optionData.series?.map((s: any, index: number) => {
      return {
        name: s.name,
        type: 'scatter',
        symbol: 'rect',
        z: 3,
        itemStyle: {
          opacity: 1
        },
        tooltip: {
          trigger: 'item',
          formatter: (param: any) => {
            const index = param.dataIndex;
            const dateIndex = Math.floor(index / 4);
            let categoryValues = cloneDeep(optionData.noNumber)
            categoryValues = categoryValues.reverse()
            const categorys = optionData.xName?.split('-').map((label: string, i: number) => {
              return {
                label,
                value: categoryValues?.[i]?.data?.[dateIndex] || '_'
              }
            })
            const percentData = s?.percentData || []
            return `<div>
             ${categorys.map((item: any) => {
              return `<div style="max-width:${realWidth / 2 - 100}px;max-height:400px;overflow:hidden;text-overflow:ellipsis;white-space:normal;">
                       ${item.label}: ${item.value}
                      </div>`
            }).join('')}
            <div style="max-width:${realWidth / 2 - 100}px; max-height:400px;overflow:hidden;text-overflow:ellipsis;white-space:normal;">
              <span style="display:inline-block;margin-right:4px;border-radius:10px;width:10px;height:10px;background-color:${param.color};"></span>
              ${param.seriesName}: ${param.data.realValue}${showPercent ? '(' + decimalToPercentage(percentData[dateIndex]) + '%)' : ''}
            </div>
          </div>`
          }
        },
        data: getChartData({ dataItem: s.data, index, name: s.name, total, maxHeight: allHeight, realWidth, currentXNum })
      }
    })

    const lineSeries = createLineChart({ seriesData, max: total, optionData });

    return {
      legend: {
        show: optionData.series.length > 1,
        top: 0,
        left: legendLeft,
        icon: 'roundRect',
        type: 'scroll',
      },
      grid: {
        left: '6%',
        right: '4%',
        bottom: gridBottom + '%',
        top: gridTop + '%',
      },
      tooltip: {
        trigger: 'axis',
        confine: false,
        axisPointer: {
          type: 'shadow',
          label: {
            backgroundColor: '#6a7985',
            color: '#fff',
            show: true,
            lineHeight: 0,
            formatter: (params: any) => {
              if (params?.seriesData.length > 0 && params.axisIndex === 0 && params.value?.indexOf('line') !== 0) {
                const index = params?.seriesData?.[0]?.dataIndex;
                const dateIndex = Math.floor(index / 4);
                const categtoryLabel = optionData.tooltips[dateIndex]
                return `{a|${categtoryLabel}}`
              }
              else return ''
            },
            rich: {
              a: {
                lineHeight: 12,
              }
            }
          }
        },
        formatter: (params: any) => {
          if (params?.[0]?.axisValue?.indexOf('line') === 0) {
            return ''
          }
          else {
            const newParams = params.slice(0, params.length / 2)
            const index = newParams[0].dataIndex;
            const dateIndex = Math.floor(index / 4);
            let categoryValues = cloneDeep(optionData.noNumber)
            categoryValues = categoryValues.reverse()
            const categorys = optionData.xName?.split('-').map((label: string, i: number) => {
              return {
                label,
                value: categoryValues?.[i]?.data?.[dateIndex] || '_'
              }
            })
            const series = optionData?.series
            return `<div>
                 ${categorys.map((item: any) => {
              return `<div style="max-width:${realWidth / 2 - 100}px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;">
                                   ${item.label}: ${item.value}
                             </div>`
            }).join('')}
                <div style="display:flex; flex-wrap:wrap; max-width:${realWidth / 2 - 100}px;max-height:400px;overflow:hidden;">
                ${newParams.map((item: any) => {
              const percentData = series?.filter((s: any) => s.name === item.seriesName)?.[0]?.percentData || []
              return `<div style="margin-right:10px; max-width:${realWidth / 4 - 60}px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;">
                          <span style="display:inline-block;margin-right:4px;border-radius:10px;width:10px;height:10px;background-color:${item.color};"></span>
                          ${item.seriesName}: ${item.data?.realValue}${showPercent ? '(' + decimalToPercentage(percentData[dateIndex]) + '%)' : ''}
                    </div>`
            }).join('')}
            </div>
          </div>`
          }
        }
      },
      dataZoom: [
        {
          xAxisIndex: optionData?.noNumber?.map((_: any, index: number) => index),
          show: showDataZoom,
          start: 0,
          end: end,
          showDataShadow: true,
          height: 20,
          bottom: 12,
          left: 120,
          right: 120,
          labelFormatter: (index: number) => {
            const categtoryLabel = optionData.tooltips[index]
            return categtoryLabel;
          },
          textStyle: {
            width: 120,
            overflow: 'truncate',
          }
        }
      ],
      xAxis: newxAxis,
      yAxis: [
        {
          name: optionData.yName,
          nameLocation: 'middle',
          type: 'value',
          triggerEvent: true,
          nameGap: 50,
          max: 100,
          axisLabel: {
            formatter: (value: number): string => {
              return Math.ceil(value * (total / 100)) + ''
            }
          }
        }
      ],
      series: [...seriesData, ...lineSeries]
    };
  };

  useEffect(() => {
    if (!isEmpty(data)) {
      let newData: any = {}
      if (showPercent) {
        const { series } = data
        const seriesData = series.map((s: any) => s?.data)
        const totalData = seriesData[0].map((_: any, index: number) => {
          return seriesData.reduce((pre: number, cur: any[]) => pre + cur[index], 0)
        })
        // 现在totalData包含了seriesData每一列的总和
        const newSeries = series?.map((s: any) => ({
          ...s,
          percentData: s.data.map((num: number, index: number) => totalData[index] <= 0 ? 0 : num / totalData[index])
        }))
        newData = { ...data, series: newSeries }
      }
      else {
        newData = data
      }
      const tNameDom = document.getElementById(`t_name_${id}`)!;
      const width = tNameDom.offsetWidth
      const legendLeft = width + 40
      if (!myChart) {
        let chartDom = document.getElementById(`overview_chart_${id}`)!;
        myChart = echarts.init(chartDom);
      }
      const { series } = newData
      const datas = series.map((item: any) => {
        return item.data
      })
      const count = datas[0].length
      const totalData: number[] = []
      for (let i = 0; i < count; i++) {
        let t = 0
        datas.map((item: number[]) => {
          const value = isNaN(item[i]) ? 0 : Number(item[i])
          t += value
        })
        totalData.push(t)
      }
      const total = Math.max(...totalData)
      let containerWidth = myChart.getWidth();
      let containerHeight = myChart.getHeight();
      const categoryNum = ((newData?.noNumber?.[0]?.data?.length - 1) * 3) + newData?.noNumber?.[0]?.data?.length
      const xNum = newData?.noNumber?.[0]?.data?.length
      const showDataZoom = categoryNum > 25
      const number = showDataZoom ? 25 : categoryNum

      let option = getOption(newData, legendLeft, containerWidth, containerHeight, total, showPercent, number);
      setTimeout(() => { myChart.setOption(option, true) }, 500);
      let dataIndexForY = 0

      // 以下操作均是为了在有缩放条的情况下，使多层级x轴label居中
      if (showDataZoom) {
        myChart.on('datazoom', (event: any) => {
          const start = event?.start
          const end = event?.end
          const subNum = end - start
          const number = Math.round(Number(subNum) * categoryNum / 100)
          dataIndexForY = Math.ceil(Number(start) * xNum / 100) * 4
          let oldOption = myChart.getOption()
          const newOption = getOption(newData, legendLeft, containerWidth, containerHeight, total, showPercent, number);
          oldOption.xAxis = newOption.xAxis
          oldOption.series = newOption.series
          myChart.setOption(oldOption, true);
        })
      }
      let labelValue: string = ''
      myChart.on('mouseover', (params: any) => {
        if (params.componentType === 'xAxis' || params.componentType === 'yAxis') {
          labelValue = params.componentType === 'xAxis' ? params.value : (Math.ceil(params.value * (total / 100)) + '')
          const offsetX = params.event?.offsetX + 5
          const offsetY = params.event?.offsetY + 5
          const dataIndex = params?.dataIndex
          let oldOption = myChart.getOption()
          oldOption.tooltip = {
            formatter: () => {
              return `<div style="max-width:${containerWidth / 3}px;overflow:hidden;text-overflow:ellipsis;white-space:normal;">
                            ${labelValue}
                      </div>` },
            confine: true,
            alwaysShowContent: true
          }
          oldOption.series = oldOption.series.map((item: any) => {
            return {
              ...item,
              tooltip: {
                formatter: () => {
                  return `<div style="max-width:${containerWidth / 3}px;overflow:hidden;text-overflow:ellipsis;white-space:normal;">
                            ${labelValue}
                      </div>` },
                confine: true,
                alwaysShowContent: true
              }
            }
          })
          myChart.setOption(oldOption, true)
          myChart.dispatchAction({
            type: 'showTip',
            seriesIndex: 0,
            dataIndex: dataIndex ?? dataIndexForY, //这里要设置dataIndex，不然tooltip不会显示
            position: [offsetX, offsetY]
          })
        }
      })
      myChart.on('mouseout', (params: any) => {
        if (params.componentType === 'xAxis' || params.componentType === 'yAxis') {
          const newOption: any = getOption(newData, legendLeft, containerWidth, containerHeight, total, showPercent, number);
          let oldOption = myChart.getOption()
          oldOption = {
            ...oldOption,
            tooltip: newOption.tooltip,
            series: oldOption.series.map((s: any, index: number) => ({
              ...s,
              tooltip: newOption?.series?.[index]?.tooltip,
            }))
          }
          myChart.setOption(oldOption, true);
          labelValue = ''
        }
      })
    }
  }, [data, id, showPercent]);

  return !isEmpty(data) ? <div className={styles.chart}>
    <div className={styles.tName} id={`t_name_${id}`}>{data?.tName || ''}</div>
    <div id={`overview_chart_${id}`} style={{ height: height, width: '100%' }}></div>
  </div> : null
}

