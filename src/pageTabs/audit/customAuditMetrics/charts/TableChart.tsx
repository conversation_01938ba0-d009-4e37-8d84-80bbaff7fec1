import { isEmpty } from 'lodash';
import React, { useEffect, useState } from 'react';
import styles from './charts.module.scss'
import { Table, Typography } from 'antd';
import { useTranslation } from 'react-i18next';
const { Text } = Typography;

// 表和矩阵
export const TableChart = (props: any) => {
  const { data: tableEle, height, id = 'new', showPercent, chartType } = props
  const [tableData, setTableData] = useState<any[]>([])
  const [allTotal, setAllTotal] = useState<number>(0) //矩阵的总计值
  const { t } = useTranslation()
  useEffect(() => {
    if (!isEmpty(tableEle)) {
      const { columns, data } = tableEle
      if (!showPercent) { setTableData(data) }
      else if (chartType === 'MATRIX') { //如果是矩阵直接计算
        const firstField = columns?.[0].dataIndex
        const aTotal = data.reduce((pre: number, item: any) => {
          return pre + item.total || 0
        }, 0)
        setAllTotal(aTotal)
        const newData = data.map((item: any) => {
          let newItem = { ...item }
          for (const key in item) {
            if (key !== firstField && key !== 'total') {
              newItem[key] = item[key] + ' (' + Math.round(item[key] / item.total * 10000) / 100 + '%) '
            }
            else if (key === 'total' && aTotal !== 0) {
              newItem.total = item.total + ' (' + Math.round(item.total / aTotal * 10000) / 100 + '%) '
            }
          }
          return newItem
        })
        setTableData(newData)
      }
      else {
        let dataIndex: string = columns?.filter((item: any) => item.action)?.[0]?.dataIndex // 获取数字列的key
        let total: number = 0
        // 计算总计
        data.map((item: any) => {
          total += item[dataIndex] || 0
        })
        const newData = data.map((item: any, index: number) => {
          let newItem = { ...item }
          for (const key in item) {
            if (key === dataIndex) {
              newItem[key] = item[key] + ' (' + Math.round(item[key] / total * 10000) / 100 + '%) '
            }
          }
          return newItem
        })
        setTableData(newData)
      }
    }
  }, [tableEle, showPercent])

  // 总结栏
  const getSummary = (pageData: any[]) => {
    let totalList: any[] = [];
    const columns = tableEle.columns
    // 计算总计
    totalList = columns?.map((item: any, index: number) => {
      let value: any = ''
      if (item.isNumber) {
        value = 0
        const dataIndex = item.dataIndex
        pageData.map((item: any) => {
          let addNum = item[dataIndex] || 0
          if (showPercent && typeof item[dataIndex] === 'string') {
            addNum = Number(item[dataIndex]?.split(' (')?.[0])
          }
          value += addNum || 0
        })
      }
      if (showPercent && chartType === 'MATRIX' && allTotal !== 0 && index !== columns.length - 1) {
        value = value + ' (' + Math.round(value / allTotal * 10000) / 100 + '%) '
      }
      return { ...item, index, value }
    })

    return (
      <Table.Summary.Row>
        {
          totalList?.map((item: any) =>
            !item.isNumber && item.index === 0 ?
              <Table.Summary.Cell key={0} index={0}>{t("auays:tb_title.total")}</Table.Summary.Cell>
              : <Table.Summary.Cell index={item.index} key={item.index}>
                <Text>{item.value}</Text>
              </Table.Summary.Cell>
          )
        }
      </Table.Summary.Row>
    );
  }


  return !isEmpty(tableEle) ? <div key={id} style={{ height: height }}>
    <Table
      className={styles.chartTable}
      columns={tableEle.columns}
      dataSource={tableData}
      scroll={{ x: 'fit-content', y: 'calc(100% - 48px)' }}
      pagination={false}
      summary={getSummary}
    />
  </div> : null
}

