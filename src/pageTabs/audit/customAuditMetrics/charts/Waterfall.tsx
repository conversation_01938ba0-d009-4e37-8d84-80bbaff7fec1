import * as echarts from 'echarts';
import { cloneDeep, isEmpty } from 'lodash';
import React, { useEffect } from 'react';
import styles from './charts.module.scss'
import { decimalToPercentage, percentChartData, waterfallDataSplit } from '../utils';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'src/hook';

type EChartsOption = echarts.EChartsOption;

// 瀑布图
export const Waterfall = (props: any) => {
  const { data, height, id = 'new', showPercent } = props
  let myChart: any;
  const { t } = useTranslation()
  const { locales } = useSelector(state => state.login)
  
  const getOption = (data: any, legendLeft: number, realWidth: number, showPercent: boolean, categoryNum: number, currentXNum: number): EChartsOption => {
    // 由于是同一个方法生成的数组，为不互相干扰，要深拷贝一下
    const optionData = cloneDeep(data)
    const xNum = optionData.noNumber?.length
    const showDataZoom = categoryNum > 25

    // 计算grid的bottom
    let gridBottom: string = ''
    const morePadding = `${(xNum) * 7 + 15}%`
    const padding = `${(xNum) * 7 + 5}%`
    if (showDataZoom) {
      gridBottom = xNum === 1 ? '20%' : morePadding
    }
    else {
      gridBottom = xNum === 1 ? '13%' : padding
    }

    // 计算dataZoom的end
    const end = categoryNum > 25 ? Math.round(25 / categoryNum * 100) : 100
    // 将每个series拆分成两个，一个透明，一个有颜色
    const colorSeries = optionData.series.map((s: any) => ({
      name: s.name,
      type: 'bar',
      stack: s.name,
      data: waterfallDataSplit(s.data)[1],
      barMaxWidth: 80,
      emphasis: {
        focus: 'series'
      },
      tooltip: {
        trigger: 'item',
        // 自定义提示框内容--因为有多层X轴的可能性
        formatter: (params: any) => {
          const index = params.dataIndex;
          let categoryValues = cloneDeep(optionData.noNumber)
          categoryValues = categoryValues.reverse()
          const categorys = optionData.xName?.split('-').map((label: string, i: number) => {
            return {
              label,
              value: categoryValues?.[i]?.data?.[index] || '_'
            }
          })
          const numData = s?.numData || []
          return `<div>
                 ${categorys.map((item: any) => {
            return `<div style="max-width:${realWidth / 2 - 100}px;max-height:400px;overflow:hidden;text-overflow:ellipsis;white-space:normal;">
                     ${item.label}: ${item.value}
                    </div>`
          }).join('')}
           <div style="max-width:${realWidth / 2 - 100}px; max-height:590px;overflow:hidden;text-overflow:ellipsis;white-space:normal;">
             <span style="display:inline-block;margin-right:4px;border-radius:10px;width:10px;height:10px;background-color:${params.color};"></span>
             ${params.seriesName}: ${showPercent ? (numData?.[index]) : params.data}${showPercent ? '(' + decimalToPercentage(params.data) + '%)' : ''}
           </div>
         </div>`
        }
      },
    }))
    const transparentSeries = optionData.series.map((s: any) => ({
      name: s.name + '_t',
      type: 'bar',
      stack: s.name,
      data: waterfallDataSplit(s.data)[0],
      itemStyle: {
        borderColor: 'transparent',
        color: 'transparent'
      },
      emphasis: {
        itemStyle: {
          borderColor: 'transparent',
          color: 'transparent'
        }
      },
      tooltip: {
        show: false
      }
    }))

    const lineSeries = optionData.series.map((s: any) => ({
      ...s,
      type: 'line',
      emphasis: {
        focus: 'series'
      },
      itemStyle: {
        opacity: 0
      },
      lineStyle: {
        opacity: 0
      },
      tooltip: {
        trigger: 'axis'
      }
    }))


    // 此处解构顺序不能变，否则会导致数据错乱
    const newSeries = [...transparentSeries, ...colorSeries, ...lineSeries]

    return {
      tooltip: {
        trigger: 'axis',
        confine: false,
        axisPointer: {
          type: 'shadow',
          label: {
            backgroundColor: '#6a7985',
            color: '#fff',
            show: true,
            lineHeight: 0,
            formatter: (params: any) => {
              if (params?.seriesData.length > 0 && params.axisIndex === 0) {
                const index = params?.seriesData?.[0]?.dataIndex;
                const categtoryLabel = index === categoryNum - 1 ? t("auays:tb_title.total") : optionData.tooltips[index]
                return `{a|${categtoryLabel}}`
              }
              else return ''
            },
            rich: {
              a: {
                lineHeight: 12,
                align: 'center'
              }
            }
          },
        },
        // 自定义提示框内容--因为有多层X轴的可能性
        formatter: (params: any) => {
          const newParams = params
          const index = newParams?.[0]?.dataIndex;
          let categoryValues = cloneDeep(optionData.noNumber)
          categoryValues = categoryValues.reverse()
          const categorys = optionData.xName?.split('-').map((label: string, i: number) => {
            return {
              label,
              value: categoryValues?.[i]?.data?.[index] || '_'
            }
          })
          const series = optionData?.series
          return `<div>
             ${categorys.map((item: any) => {
            return `<div style="max-width:${realWidth / 2 - 100}px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;">
                               ${item.label}: ${item.value}
                         </div>`
          }).join('')}
            <div style="display:flex; flex-wrap:wrap; max-width:${realWidth / 2 - 100}px;max-height:400px;overflow:hidden;">
            ${newParams.map((item: any) => {
            const numData = series?.filter((s: any) => s.name === item.seriesName)?.[0]?.numData || []
            return `<div style="margin-right:10px; max-width:${realWidth / 4 - 60}px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;">
                      <span style="display:inline-block;margin-right:4px;border-radius:10px;width:10px;height:10px;background-color:${item.color};"></span>
                      ${item.seriesName}: ${showPercent ? (numData?.[index]) : item.data}${showPercent ? '(' + decimalToPercentage(item.data) + '%)' : ''}
                </div>`
          }).join('')}
            </div>
          </div>`
        }
      },
      legend: {
        show: optionData.series.length > 1,
        top: 0,
        left: legendLeft,
        type: 'scroll',
        data: optionData.series.map((cs: any) => {
          return {
            name: String(cs.name),
            icon: 'roundRect'
          }
        })
      },
      dataZoom: [
        {
          xAxisIndex: optionData?.noNumber?.map((_: any, index: number) => index),
          show: showDataZoom,
          start: 0,
          end: end,
          showDataShadow: true,
          height: 20,
          bottom: 12,
          left: 120,
          right: 120,
          labelFormatter: (index: number) => {
            const categtoryLabel = optionData.tooltips[index]
            return categtoryLabel;
          },
          textStyle: {
            width: 120,
            overflow: 'truncate',
          }
        }
      ],
      grid: {
        left: '6%',
        right: '2%',
        bottom: gridBottom,
        top: optionData.series?.length > 1 ? 60 : '6%',
      },
      xAxis: optionData.noNumber?.map((xAxis: any, index: number) => ({
        data: index === 0 ? [...xAxis.data, t("auays:tb_title.total")] : [...xAxis.data, ''],
        name: index === optionData.noNumber.length - 1 ? optionData.xName : '',
        nameLocation: 'middle',
        type: 'category',
        triggerEvent: true,
        position: 'bottom',
        nameGap: 20 * (index + 1) + 8,
        axisTick: {
          show: !showDataZoom && index === 0,
          interval: 0,
          length: 8,
        },
        axisLabel: {
          margin: index === 0 ? 8 : (8 + index * 15),
          width: (realWidth * 0.92) / (currentXNum + 1),
          overflow: 'truncate',
          interval: 0,
          backgroundColor: 'rgba(0, 0, 0, 0)' //给个背景，不然mouseover会检测不到
        }
      })),
      yAxis: [
        {
          name: optionData.yName,
          triggerEvent: true,
          nameLocation: 'middle',
          type: 'value',
          nameGap: 50,
          max: showPercent ? 1 : undefined,
          axisLabel: {
            formatter: (value: any) => {
              return showPercent ? value * 100 + '%' : value;
            }
          },
        }
      ],
      series: newSeries
    };
  };

  useEffect(() => {
    if (!isEmpty(data)) {
      let newData: any = {}
      if (showPercent) {
        newData = percentChartData(data)
      }
      else {
        newData = data
      }
      const tNameDom = document.getElementById(`t_name_${id}`)!;
      const width = tNameDom.offsetWidth
      const legendLeft = width + 40
      if (!myChart) {
        let chartDom = document.getElementById(`overview_chart_${id}`)!;
        myChart = echarts.init(chartDom);
      }

      const categoryNum = newData?.noNumber?.[0]?.data?.length + 1
      let containerWidth = myChart.getWidth();
      const showDataZoom = categoryNum > 25
      // 当前图表展示的条数
      const number = showDataZoom ? 25 : categoryNum

      let option = getOption(newData, legendLeft, containerWidth, showPercent, categoryNum, number);
      setTimeout(() => { myChart.setOption(option, true) }, 500);
      let dataIndexForY = 0

      // 以下操作均是为了在有缩放条的情况下，使多层级x轴label居中
      if (showDataZoom) {
        myChart.on('datazoom', (event: any) => {
          const start = event?.start
          const end = event?.end
          const subNum = end - start
          const number = Math.round(Number(subNum) * categoryNum / 100)
          dataIndexForY = Math.round(Number(start) * categoryNum / 100) + 1
          let oldOption = myChart.getOption()
          const newOption = getOption(newData, legendLeft, containerWidth, showPercent, categoryNum, number);
          oldOption.xAxis = newOption.xAxis
          myChart.setOption(oldOption, true);
        })
      }
      let labelValue: string = ''
      myChart.on('mouseover', (params: any) => {
        if (params.componentType === 'xAxis' || params.componentType === 'yAxis') {
          labelValue = params.value
          const offsetX = params.event?.offsetX + 5
          const offsetY = params.event?.offsetY + 5
          const dataIndex = params?.dataIndex
          let oldOption = myChart.getOption()
          oldOption.tooltip = {
            formatter: () => {
              return `<div style="max-width:${containerWidth / 3}px;overflow:hidden;text-overflow:ellipsis;white-space:normal;">
                            ${labelValue}
                      </div>` },
            confine: true,
            alwaysShowContent: true
          }
          oldOption.series = oldOption.series.map((item: any) => {
            return {
              ...item,
              tooltip: {
                formatter: () => {
                  return `<div style="max-width:${containerWidth / 3}px;overflow:hidden;text-overflow:ellipsis;white-space:normal;">
                            ${labelValue}
                      </div>` },
                confine: true,
                alwaysShowContent: true
              }
            }
          })
          myChart.setOption(oldOption, true)
          myChart.dispatchAction({
            type: 'showTip',
            seriesIndex: 0,
            dataIndex: dataIndex ?? dataIndexForY, //这里要设置dataIndex，不然tooltip不会显示
            position: [offsetX, offsetY]
          })
        }
      })
      myChart.on('mouseout', (params: any) => {
        if (params.componentType === 'xAxis' || params.componentType === 'yAxis') {
          const newOption: any = getOption(newData, legendLeft, containerWidth, showPercent, categoryNum, number);
          let oldOption = myChart.getOption()
          oldOption = {
            ...oldOption,
            tooltip: newOption.tooltip,
            series: oldOption.series.map((s: any, index: number) => ({
              ...s,
              tooltip: newOption?.series?.[index]?.tooltip,
            }))
          }
          myChart.setOption(oldOption, true);
          labelValue = ''
        }
      })
    }
  }, [data, id, showPercent, locales]);

  return !isEmpty(data) ? <div className={styles.chart}>
    <div className={styles.tName} id={`t_name_${id}`}>{data?.tName || ''}</div>
    <div id={`overview_chart_${id}`} style={{ height: height, width: '100%' }}></div>
  </div> : null
}

