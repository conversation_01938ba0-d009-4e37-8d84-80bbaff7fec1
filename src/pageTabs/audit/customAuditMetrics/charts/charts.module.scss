.chart {
  position: relative;
  height: 100%;

  .tName {
    position: absolute;
    top: 1px;
    left: 2%;
    color: #333333
  }
}

.pie<PERSON>hart {
  position: relative;
  height: 100%;

  .tName {
    position: absolute;
    top: 1px;
    right: 20px;
    color: #333333
  }
}

.chartTable {
  height: 100%;

  :global {
    .ant-spin-nested-loading,
    .ant-spin-container,
    .ant-table,
    .ant-table-container {
      height: 100%
    }

    .ant-table-header {
      border-top: 1px solid #CECECF;
      border-left: 1px solid #CECECF;
      border-right: 1px solid #CECECF;
    }

    .ant-table-body {
      scrollbar-width: thin;
      border-bottom: 1px solid #CECECF;
      border-left: 1px solid #CECECF;
      border-right: 1px solid #CECECF;
    }

  }
}