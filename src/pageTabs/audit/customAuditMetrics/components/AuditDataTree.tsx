import React, { useContext, useEffect } from "react"
import styles from './components.module.scss'
import { DataNode } from "antd/es/tree";
import { DownOutlined } from "@ant-design/icons";
import { Spin, Tooltip, Tree } from "antd";
import { treeIcon } from "../constants";
import { useDrag } from "react-dnd";
import classNames from "classnames";
import { getEmptyImage } from "react-dnd-html5-backend";
import { CustomDragLayer } from "./CustomDragLayer";
import { useRequest } from "src/hook";
import { getMetricsTreeData } from "src/api";
import { CustomAuditContext } from "../CustomAuditContext";
import { useTranslation } from "react-i18next";
const { TreeNode } = Tree;

export const AuditDataTree = () => {
  const { onTreeDataChange } = useContext(CustomAuditContext)
  const { t } = useTranslation()

  // 目前字段树只有一层，所以直接返回数据
  const { data: treeData = [], loading: treeDataLoading } = useRequest(getMetricsTreeData, {
    formatResult: (res: any) => {
      const newData = res.map((item: any) => {
        const tItem = {
          title: item.tableDesc,
          icon: <TreeIcon />,
          key: item.tableName,
          children: item.tableFields?.map((c: any) => {
            return {
              title: c.fieldMeaning,
              key: c.propertyName,
              info: { type: c.propertyType, propertyName: c.propertyName, parentName: item.tableName }
            }
          })
        }
        return tItem
      })
      onTreeDataChange(newData)
      return newData
    }
  })

  // 渲染树叶
  const renderTreeNode = (node: any) => {
    const hasChildren = node.children && node.children.length > 0
    return <TreeNode
      title={hasChildren ? node.title : <TreeList node={node} />}
      key={node.key}
      icon={node.icon ?? undefined}
      expanded={true}
      className={classNames(styles.treeItem, { [styles.parentNode]: hasChildren })}
    >
      {
        hasChildren && node.children.map((item: DataNode) => renderTreeNode(item))
      }
    </TreeNode >
  }

  return <div className={classNames(styles.auditDataTree, 'custom_audit_metrics_second')}>
    <div className={styles.title}>{t("auays:div_lbl.all_data")}</div>
    {/* 渲染指标字段树 */}
    <Spin spinning={treeDataLoading}>
      {
        treeData.length > 0 && <Tree
          className={styles.dataTree}
          showIcon
          blockNode
          defaultExpandAll
          switcherIcon={<DownOutlined />}
          selectable={false}
        >
          {
            treeData?.map((item: any) => renderTreeNode(item))
          }
        </Tree>
      }
    </Spin>
  </div >
}

// 数据表图标
const TreeIcon = () => {
  return <div className={styles.treeIcon}>
    {treeIcon}
  </div>
}

// 渲染可拖动树叶的title
const TreeList = (props: any) => {
  const { node } = props;
  const { key, title } = node;

  const [, drag, dragPreview] = useDrag(({
    type: 'METRICS',
    item: { type: 'METRICS', node },
    options: {
      dropEffect: 'move',
    },
  } as any));

  useEffect(() => {
    // 将默认拖拽组件隐藏
    dragPreview(getEmptyImage(), { captureDraggingState: true })
  }, [dragPreview])

  return (
    <>
      {
        title.length > 14 ? <Tooltip title={title} placement="top">
          <div ref={drag} key={key} className={styles.treeTitle}>
            {title}
          </div>
        </Tooltip > :
          <div ref={drag} key={key} className={styles.treeTitle}>
            {title}
          </div>
      }
      {/* 为实现UI样式，自定义拖住层 */}
      <CustomDragLayer />
    </>
  );
};