import { Card, Dropdown, Input, Menu, Table, Tooltip, message } from "antd"
import React, { useContext, useEffect, useRef, useState } from "react"
import styles from './components.module.scss'
import { CUSTOM_AUDIT_CHART_ELEMENT } from "../constants"
import { chartSeriesListGeneratorForFY, chartOptionsListGenerator, dealChartDataHY, dealChartDataXY, mergeStandardAndAssistant, checkAllowSave } from "../utils"
import { Waterfall } from "../charts/Waterfall"
import { Ribbon } from "../charts/Ribbon"
import { CustomAuditContext } from "../CustomAuditContext"
import { EditOutlined, EllipsisOutlined, InfoCircleOutlined } from "@ant-design/icons"
import { isEmpty, uniq } from "lodash"
import { TableChart } from "../charts/TableChart"
import classNames from "classnames"
import { GenericChartXY } from "../charts/GenericChartXY"
import { GenericChartPie } from "../charts/GenericChartPie"
import defaultImg from 'src/assets/img/audit_chart_card.png'
import { CustomSaveModal } from "../../components/CustomSaveModal"
import { deleteClick } from "../../utils"
import { useTranslation } from "react-i18next"
import { useSelector } from "src/hook"

const ChartCard = () => {
  const { chartInfo, onInfoChange, chartData: data, eleNodes, downloadChart, filterNodes, deleteChart, delChartTab, saveChart } = useContext(CustomAuditContext)
  const { chartType, chartName, chartRemark, chartShowType } = chartInfo
  const { t } = useTranslation()
  const { locales } = useSelector(state => state.login)
  const [chartData, setChartData] = useState<any>()
  const [isEditTitle, setIsEditTitle] = useState<boolean>(false)
  const [showTable, setShowTable] = useState<boolean>(false)
  const [tableColumn, setTableColumn] = useState<any[]>([])
  const inputRef = useRef<any>({})
  const [diffHeight, setDiffHeight] = useState<number>(54 * 5)
  const chartBaseInfo = CUSTOM_AUDIT_CHART_ELEMENT[chartType]
  const { elements, dealType } = chartBaseInfo
  const [saveVisible, setSaveVisible] = useState<boolean>(false) //保存弹窗
  const [tableData, setTableData] = useState<any[]>([])

  const onPressEnter = (e: any) => {
    const value = e.target.value
    if (value !== chartName) onInfoChange({ chartName: value })
    setIsEditTitle(false)
  }

  const handleListener = (e: any) => {
    if (document.getElementById('data_table')?.contains(e.target)) {
      return;
    } else {
      setShowTable(false)
      document.removeEventListener('click', handleListener)
    }
  }

  // 以表的形式显示展现
  const onShowTable = () => {
    if (chartData.length === 0 || isEmpty(chartData)) message.warn(t("auays:msg.val_table_display"))
    else {
      setShowTable(true)
      document.addEventListener('click', handleListener);
    }
  }

  // 保存操作
  const saveClick = () => {
    const checkRes = checkAllowSave(eleNodes,chartInfo?.chartType)
    if (!checkRes) message.warn(t("auays:msg.chart_create_require_val_no_save"))
    else setSaveVisible(true)
  }

  useEffect(() => {
    // 修改图表名称
    if (isEditTitle && inputRef) inputRef.current.focus()
  }, [isEditTitle])

  // 导出
  const downloadClick = () => {
    if (isEmpty(chartData)) message.warn(t("auays:msg.val_data_export"))
    else downloadChart()
  }

  const chartOpeMenus = (chartType: string) => {
    const menus = <Menu>
      <Menu.Item onClick={downloadClick}>{t("auays:btn.export_data")}</Menu.Item>
      {!['TABLE', 'MATRIX'].includes(chartType) && <Menu.Item onClick={onShowTable}>{t("auays:btn.show_table")}</Menu.Item>}
      <Menu.Item onClick={saveClick}>{t("auays:btn.save")}</Menu.Item>
      <Menu.Item onClick={()=>deleteClick(chartInfo.id, delChartTab, deleteChart)}>{t("auays:btn.delete")}</Menu.Item>
    </Menu>
    return menus
  };

  useEffect(() => {
    if (filterNodes) {
      const columns = filterNodes.map((item: any) => {
        const isNumerical: boolean = !!item.action
        return {
          title: item.title,
          dataIndex: isNumerical ? 'amount' : item.key,
          key: item.key,
          width: 200,
        }
      })
      setTableColumn(columns)
    }
  }, [filterNodes])

  useEffect(() => {
    // 计算图表高度
    switch (chartType) {
      case 'LINE_CHART':
      case 'AREA_CHART':
      case 'LINE_AND_STACKED_COLUMN_CHART':
      case 'LINE_AND_CLUSTERED_COLUMN_CHART':
        setDiffHeight(54 * 6 + 40);
        break;
      case 'TABLE':
        setDiffHeight(54 * 3 + 40);
        break;
      case 'PIE_CHART':
      case 'DONUT_CHART':
      case 'MATRIX':
        setDiffHeight(54 * 4 + 40);
        break;
      default:
        setDiffHeight(54 * 5 + 40);
        break;
    }
  }, [chartType])

  // 图表数据处理
  useEffect(() => {
    // 判断要素框中是否存在要素
    const requiredIndex: number[] = [] //必填项索引
    elements.map((item: any, index: number) => {
      if (item.required) {
        requiredIndex.push(index)
      }
    })
    let isRequest: boolean = true
    requiredIndex.map((i: number) => {
      if (eleNodes[i].length === 0) isRequest = false
    })
    // 图表数据处理
    if (isRequest) {
      // xy图例图表、饼图环形图、表
      if (dealType === 1 && data?.length > 0) {
        if (chartType === 'TABLE') {
          const fieldName = eleNodes[0].filter((node: any) => node.action)?.[0]?.key
          const newData = data.map((item: any) => ({
            ...item,
            [fieldName]: item.amount
          }))
          const columns = eleNodes[0].map((item: any) => {
            return { title: item.title, dataIndex: item.key, key: item.key, isNumber: item.isNumber, ellipsis: true, width: 180, action: item.action || undefined }
          })
          setChartData({ columns, data: newData })
        }
        else {
          setTableData(data)
          const baseData = dealChartDataXY(elements, eleNodes, data)
          setChartData({ ...baseData })
        }
      }
      // 折线图和分区图
      else if (dealType === 2 && !isEmpty(data)) {
        const hasFy = eleNodes[2].length > 0
        if (!hasFy) {
          setTableData([...data.standard])
          const baseData = dealChartDataXY(elements, eleNodes, data.standard)
          setChartData({ ...baseData })
          setChartData({ ...baseData, hasFy })
        }
        else {
          const { standard = [], assistant = [] } = data
          const newData = mergeStandardAndAssistant(standard, assistant)
          setTableData([...newData])
          let xField: string[] = []
          let xName: string = ''
          let yName: string = ''
          let yField: string = 'amount'
          let fyName: string = ''
          let fyField: string = 'amountF'
          let tName: string = t("auays:axis_name.value_axis")
          elements.map((ele: any, index: number) => {
            if (eleNodes[index] && eleNodes[index].length > 0) {
              if (!ele.isNumerical && ele.required) {
                xField = eleNodes[index]?.map((node: any) => node.key)
                xName = eleNodes[index]?.map((node: any) => node.title).join('-')
              }
              else if (ele.isNumerical && ele.required) {
                yName = eleNodes[index]?.map((node: any) => node.title).join('-')
              }
              else if (ele.isNumerical && !ele.required) {
                fyName = eleNodes[index]?.map((node: any) => node.title).join('-')
              }
            }
          })
          // 如果辅助Y轴指标和Y轴指标相同就修改下series的name
          const legendFyName = fyName === yName ? t("auays:custom_chart_axis.auxiliary_y_axis") + '-' + fyName : fyName
          const { noNumberAxis } = chartOptionsListGenerator(newData, xField, 'amount')
          const { series, tooltips } = chartSeriesListGeneratorForFY(newData, xField, yField, fyField, [yName, legendFyName])
          setChartData({ noNumber: noNumberAxis, series, tooltips, xName, yName: [yName, fyName], hasFy, tName })
        }
      }
      // 折线和堆积柱状图和折线和簇状柱形图
      else if (dealType === 3 && !isEmpty(data)) {
        const hasLine = eleNodes[2].length > 0
        if (!hasLine) {
          setTableData([...data.standard])
          const baseData = dealChartDataXY(elements, eleNodes, data.standard)
          setChartData({ ...baseData })
          setChartData({ ...baseData, hasLine })
        }
        else {
          const { standard = [], assistant = [] } = data
          const newData = mergeStandardAndAssistant(standard, assistant)
          setTableData([...newData])
          const { noNumber, series, tooltips, xName, yName, tName } = dealChartDataXY(elements, eleNodes, standard)
          const { seriesH, hyName } = dealChartDataHY(elements, eleNodes, assistant)
          // 如果行Y轴指标和Y轴指标相同就修改下series的name
          const legendHyName = series?.filter((s: any) => s.name === hyName).length > 0 ? t("auays:custom_chart_axis.row_y_axis") + '-' + hyName : hyName
          const newSeriesH = seriesH.map((s: any) => ({ ...s, name: legendHyName }))
          setChartData({ noNumber, series: [...series, ...newSeriesH], tooltips, xName, yName: [yName, hyName], tName, hasLine })
        }
      }
      // 矩阵
      else if (dealType === 4 && !isEmpty(data)) {
        let values: any[] = []
        Object.values(data).map((item: any) => {
          Object.keys(item)?.map((v: any) => values.push(v))
        })
        values = uniq(values)
        const valuesColumn = values.map((item: any) => ({ key: item, title: item, dataIndex: item, isNumber: eleNodes[1]?.[0]?.isNumber, ellipsis: true, width: 150 }))
        const topKey = eleNodes[0]?.map((item: any) => item.key)?.[0]
        const topColumn = eleNodes[0]?.map((item: any) => ({ key: item.key, title: item.title, dataIndex: item.key, ellipsis: true, width: 180 }))
        const totalColumn = [{ key: 'total', dataIndex: 'total', title: t("auays:tb_title.total"), isNumber: true, ellipsis: true, width: 160, fixed: 'right' }]
        const columns = [...topColumn, ...valuesColumn, ...totalColumn]
        const matrixData: any[] = []
        Object.keys(data).map((key: any) => {
          const value = data[key]
          const total = Object.values(value).reduce((a: any, b: any) => a + Number(b), 0)
          const dataItem = {
            [topKey]: key,
            ...value,
            'total': total,
          }
          matrixData.push(dataItem)
        })
        setChartData({ columns, data: matrixData })
      }
      else setChartData({})
    }

    else {
      setChartData({})
    }
  }, [data, eleNodes, locales])

  return (
    <Card
      className={classNames(styles.chartCard, 'custom_audit_metrics_forth')}
      // loading={preImgLoading}
      title={<div className={styles.chartTitle}>
        <Tooltip title={chartRemark || t("auays:df_remark.no_remark")}>
          <InfoCircleOutlined className={styles.infoIcon} />
        </Tooltip>
        {
          !isEditTitle ?
            <div>{chartName || t("auays:df_name.unnamed_chart")}</div>
            : <Input
              key='chart_title'
              ref={inputRef}
              bordered={false}
              onPressEnter={onPressEnter}
              onBlur={onPressEnter}
              defaultValue={chartName}
            />
        }
        <EditOutlined className={styles.editIcon} onClick={() => { setIsEditTitle(true) }} />
      </div>}
      extra={
        <Dropdown overlay={chartOpeMenus(chartType)} trigger={['click']}>
          <EllipsisOutlined />
        </Dropdown >
      }
      style={{ height: `calc(100% - ${diffHeight}px)` }}
    >
      {
        isEmpty(chartData) ?
          <div className={styles.defaultDisplay}>
            <img src={defaultImg} className={styles.defaultImg} alt={t("auays:bc_title.custom_audit_metrics_smart_mode")} />
            <div className={styles.defaultTitle} >{t("auays:img_lbl.generate_view_obj")}</div>
            <div className={styles.defaultTip}>{t("auays:img_lbl.drag_fields")}</div>
          </div> :
          getChart(chartType, chartData, chartShowType, 'new')
      }
      {showTable && <div className={styles.mask} />}
      {
        // styles.dataTable_level，当数据为空时，将card的层级降低，以解决首次加载一闪而过的问题
        <Card className={classNames(styles.dataTable, { [styles.dataTable_hidden]: !showTable }, { [styles.dataTable_level]: isEmpty(tableData) })} id='data_table' title={t("auays:btn.show_table")}>
          {showTable && <Table
            className={styles.table}
            columns={tableColumn}
            dataSource={tableData}
            scroll={{ y: 'calc(100% - 48px)' }}
            pagination={false}
          />}
        </Card>
      }
      {/* 保存时弹窗 */}
      <CustomSaveModal
        defaultValues={{
          name: chartInfo?.chartName,
          remark: chartInfo?.chartRemark,
        }}
        visible={saveVisible}
        onCancel={() => setSaveVisible(false)}
        onFinishOpe={(values: any) => saveChart(chartInfo.id, values)}
      />
    </Card>
  )
}

export const getChart = (chartType: string, data: any, showPercent: boolean, id?: any, h?: string) => {
  const height = h ?? '100%'
  switch (chartType) {
    case 'STACKED_COLUMN_CHART':
    case 'STACKED_BAR_CHART':
    case 'CLUSTERED_BAR_CHART':
    case 'CLUSTERED_COLUMN_CHART':
    case 'LINE_CHART':
    case 'AREA_CHART':
    case 'STACKED_AREA_CHART':
    case 'LINE_AND_STACKED_COLUMN_CHART':
    case 'LINE_AND_CLUSTERED_COLUMN_CHART':
      return <GenericChartXY data={data} height={height} chartType={chartType} id={id} showPercent={showPercent} />;
    case 'STACKED_PERCENTAGE_BAR_CHART':
    case 'STACKED_PERCENTAGE_COLUMN_CHART':
    case 'PERCENTILE_STACKED_AREA_CHART':
      // 百分比数据处理
      return <GenericChartXY data={data} height={height} chartType={chartType} id={id} showPercent={true} />;
    case 'RIBBON_DIAGRAM':
      return <Ribbon data={data} height={height} id={id} showPercent={showPercent} />;
    case 'WATERFALL_CHART':
      return <Waterfall data={data} height={height} id={id} showPercent={showPercent} />;
    case 'PIE_CHART':
    case 'DONUT_CHART':
      return <GenericChartPie data={data} height={height} chartType={chartType} id={id} />;
    case 'TABLE':
      return <TableChart data={data} height={height} id={id} chartType={chartType} showPercent={showPercent} />;
    case 'MATRIX':
      return <TableChart data={data} height={height} id={id} chartType={chartType} showPercent={showPercent} />;
  }
}

export default ChartCard