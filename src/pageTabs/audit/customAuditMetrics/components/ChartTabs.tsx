import React, { useContext, useEffect, useState } from "react"
import { CustomAuditContext } from "../CustomAuditContext"
import { Modal } from "antd"
import { isEmpty } from "lodash"
import { CustomMetricsTabs } from "../../components/CustomMetricsTabs"
import { CustomSaveModal } from "../../components/CustomSaveModal"
import { useTranslation } from "react-i18next"
const { confirm } = Modal;

export const ChartTabs = () => {
  const { tabList, chartInfo, addChartTab, delChartTab, onTabChange, chartData, saveChart } = useContext(CustomAuditContext)
  const { t } = useTranslation()
  const [saveVisible, setSaveVisible] = useState<boolean>(false) // 保存弹窗
  const [delId, setDelId] = useState<string | number>() // 需要删除的Tabid
  const [saveDefaultValues, setSaveDefaultValues] = useState<any>({}) // 保存弹窗默认值

  // 新增tab
  const addTabBtn = () => {
    const newTab = {
      tabId: `new_${tabList.length}`,
      isCreatCharts: false
    }
    addChartTab(newTab)
  }

  // 删除tab二次确认保存 
  const delTabBtn = (item: any) => {
    const { tabId: delId, isCreatCharts, isNoChange = false } = item
    const str = String(delId)
    if (str.startsWith('new_')) {
      const currentId = chartInfo.id
      if ((currentId !== delId && isCreatCharts) || (currentId === delId && !isEmpty(chartData))) {
        confirm({
          title: t("auays:md_title.confirm_save"),
          content: t("auays:md_content.chart_unsaved_exit"),
          okText: t("auays:btn.save"),
          okType: 'primary',
          cancelText: t("auays:btn.do_not_save"),
          onOk: () => {
            setDelId(delId)
            setSaveVisible(true)
          },
          onCancel: () => {
            delChartTab(delId)
          }
        })
      }
      else {
        delChartTab(delId)
      }
    }
    else if (!isNoChange) {
      confirm({
        title: t("auays:md_title.chart_design_changed"),
        content: t("auays:md_content.chart_design_changed_exit"),
        okText: t("auays:btn.save"),
        okType: 'primary',
        cancelText: t("auays:btn.do_not_save"),
        onOk: () => {
          setDelId(delId)
          setSaveVisible(true)
        },
        onCancel: () => {
          delChartTab(delId)
        }
      })
    }
    else {
      delChartTab(delId)
    }
  }

  // 根据delId更新保存时默认值
  useEffect(() => {
    if (delId) {
      const tab = tabList.find((item: any) => item.tabId === delId) || {}
      const { chartInfo } = tab
      setSaveDefaultValues({
        name: chartInfo?.chartName,
        remark: chartInfo?.chartRemark
      })
    }
  }, [delId, tabList])

  // 保存弹窗的onFinishOpe操作
  const onFinishOpe = (values: any) => {
    saveChart(delId, values, () => delChartTab(delId))
  }

  return <div>
    <CustomMetricsTabs
      tabList={tabList}
      metricsInfo={chartInfo}
      addTab={addTabBtn}
      delTab={delTabBtn}
      onTabChange={onTabChange}
    />
    {/* 保存时弹窗 */}
    <CustomSaveModal
      defaultValues={saveDefaultValues}
      visible={saveVisible}
      onCancel={() => setSaveVisible(false)}
      onFinishOpe={onFinishOpe}
    />
  </div>
}