import React from "react";
import { useDragLayer } from "react-dnd";
import styles from './components.module.scss'

// 自定义拖拽层
export const CustomDragLayer = () => {

  const { isDragging, item, initialOffset, currentOffset } = useDragLayer((monitor) => ({
    item: monitor.getItem(),
    itemType: monitor.getItemType(),
    initialOffset: monitor.getInitialSourceClientOffset(),
    currentOffset: monitor.getSourceClientOffset(),
    // 这个是当前鼠标的相对位置
    isDragging: monitor.isDragging()
  }))

  // 判断是否正在进行拖拽行为
  if (!isDragging) return null;

  const getItemStyles = (initialOffset: any, currentOffset: any): React.CSSProperties => {
    if (!initialOffset || !currentOffset) {
      return {
        display: "none"
      };
    }
    let { x, y } = currentOffset;
    // 渲染节点位置
    const transform = `translate(${x}px, ${y}px)`;
    return {
      transform,
      WebkitTransform: transform,
    };
  }
  
  return (
    <div className={styles.customDragLayer} style={getItemStyles(initialOffset, currentOffset)}>
      {item?.node?.title}
    </div>
  )
}