import React, { useContext, useEffect, useRef, useState } from "react"
import styles from './components.module.scss'
import { CloseOutlined, DownOutlined } from "@ant-design/icons"
import { CAN_NUMERICAL_OPE_TYPE, getThemeByCategory, mutexOperations } from "../constants"
import { Dropdown, Input, Tooltip } from "antd"
import { OpeMenus } from "./OpeMenus"
import { CustomAuditContext } from "../CustomAuditContext"
import { baseOptions } from "./PostFilter"
import { useSelector } from "src/hook"
import { useTranslation } from "react-i18next"

interface IFAElementItem {
  node: any, //树节点数据
  n: number,//一排节点数，用于计算maxwidth
  theme?: any, //主题样式
  onDeleteOpe?: () => void, // del操作
  onEditOpe?: (titleValue: string) => void // 重命名操作
  onMoveOpe?: (target: string) => void // 移动操作
  iconOpe?: () => void // 操作
  onCountOpe?: (ope: any) => void // 数值操作
  onShowTotalOpe?: () => void //是否汇总
  onShowTypeOpe?: (ope: any) => void // 是否展示为百分比
  onSortOpe?: (ope: any) => void // 排序
}

export const FAElementItem = (props: IFAElementItem) => {
  const { node, n, theme = getThemeByCategory['grey'], onDeleteOpe = false, onSortOpe = false, onShowTypeOpe = false, onEditOpe = false, onMoveOpe = false, onShowTotalOpe = false, iconOpe = false, onCountOpe = false } = props
  const { title, source, showSource = false } = node
  const { locales } = useSelector((state: any) => state.login)
  const { t } = useTranslation()
  const [isEdit, setIsEdit] = useState<boolean>(false)
  const { chartInfo } = useContext(CustomAuditContext)
  const { chartType, id, fieldOrder, valueOrder } = chartInfo
  const [dropdownVisible, setDropdownVisible] = useState<boolean>(false)
  const input_focus = useRef<any>()
  const [sortType, setSortType] = useState<string>('') // 排序 ''：不排序，asc：升序，desc：降序
  const [numOpeType, setNumOpeType] = useState<string>('12') // 数值操作
  const [valueShowType, setValueShowType] = useState<string>('13-1') // 将值显示为 13-1：无计算，13-2：占总计的百分比
  const [showTotal, setShowTotal] = useState<boolean>(true) // 将值显示为 true：汇总，false：不汇总
  const [isNumber, setIsNumber] = useState<boolean>(false) // 是否为数值类型字段
  const [filterTitle, setFilterTitle] = useState<string>('') // 过滤条件的标题

  // 初始化
  useEffect(() => {
    // 判断当前所选指标字段是否为数值字段类型
    if (node.info) {
      const isNumType: boolean = CAN_NUMERICAL_OPE_TYPE.includes(node.info?.type)
      setIsNumber(isNumType)
    }

    if (node.action) {
      const opeArr = mutexOperations(true)
      const opeKey = opeArr.filter((item: any) => item.field && item.field === node.action)?.[0]?.key
      setNumOpeType(opeKey)
      setSortType(valueOrder)
    }
    else {
      setSortType(fieldOrder)
    }

    // 过滤条件的副标题
    if (node.filter) {
      const { tabType, topN, values, advancedFilter } = node.filter
      if (tabType === 'BASIC') {
        const vs = values?.join("，") || ''
        setFilterTitle(' ' + t("auays:filter_title.basic_filter", { values: vs }))
      }
      else if (tabType === 'TOPN') {
        setFilterTitle(' ' + t("auays:filter_title.top_n", { n: topN || 0 }))
      }
      else if (tabType === 'ADVANCED') {
        let first: string = ''
        let last: string = ''
        if (advancedFilter?.firstAction && advancedFilter?.firstValue) {
          const actionLabel = t(baseOptions.filter((item: any) => item.value === advancedFilter?.firstAction)?.[0]?.label) || ''
          first = `${actionLabel}${advancedFilter?.firstValue}`
        }
        if (advancedFilter?.lastAction && advancedFilter?.lastValue) {
          const actionLabel = t(baseOptions.filter((item: any) => item.value === advancedFilter?.lastAction)?.[0]?.label) || ''
          last = `${actionLabel}${advancedFilter?.lastValue}`
        }
        const connect = ' ' + (Number(advancedFilter.logicalAction) === 1 ? t("auays:filter_title.and") : t("auays:filter_title.or")) + ' '
        const logicalAction = last && first ? connect : ''
        setFilterTitle(' ' + t("auays:filter_title.advanced_filter", { values: first + logicalAction + last }))
      }
      else { setFilterTitle('') }
    }
  }, [fieldOrder, node, valueOrder, locales])


  useEffect(() => {
    if (chartType === 'TABLE' && !id && !node.action) {
      setNumOpeType('-1')
    }
  }, [chartType, id, node])

  const onPressEnterAndBlur = (e: any) => {
    onEditOpe && onEditOpe(e.target.value)
    setIsEdit(false)
  }
  const onEdit = () => {
    setDropdownVisible(false)
    setIsEdit(true)
  }
  const onMove = (menuItem: any) => {
    setDropdownVisible(false)
    if (onMoveOpe) onMoveOpe(menuItem.label)
  }

  const onSort = (menuItem: any) => {
    onSortOpe && onSortOpe(menuItem)
    if (sortType === menuItem.key) {
      setSortType('')
    }
    else setSortType(menuItem.key)
  }

  const onShowTotal = () => {
    onShowTotalOpe && onShowTotalOpe()
    setShowTotal(!showTotal)
  }

  const onShowType = (menuItem: any) => {
    onShowTypeOpe && onShowTypeOpe(menuItem)
    setValueShowType(menuItem.key)
  }
  const onNumOpe = (menuItem: any) => {
    onCountOpe && onCountOpe(menuItem)
    setNumOpeType(menuItem.key)
  }

  const handleListener = (e: any) => {
    if (document.getElementById('opeDrop')?.contains(e.target)) {
      return;
    } else {
      setDropdownVisible(false)
      document.removeEventListener('click', handleListener)
    }
  }
  const dropClick = () => {
    setDropdownVisible(true)
    document.addEventListener('click', handleListener);
  }

  useEffect(() => {
    // 重命名
    if (isEdit && input_focus) input_focus.current.focus()
  }, [isEdit])

  const menuOpeFun = {
    '1': onDeleteOpe, // 删除字段
    '2': onEdit, // 重命名
    '3': onMove, // 移动
    '4': onSort, // 是否排序
    // 数值操作
    '11': onNumOpe,// 计数（非重复）
    '12': onNumOpe, // 计数
    '5': onNumOpe,
    '6': onNumOpe,
    '7': onNumOpe,
    '8': onNumOpe,
    '9': onNumOpe,
    '10': onNumOpe,
    '13': onShowType,
    '14': onShowTotal,
  }

  return <div className={styles.filterAndElementItem} style={{ ...theme, maxWidth: `calc(100% / ${n} - 12px)` }} onClick={() => { iconOpe && iconOpe() }} role="button">
    <Dropdown
      overlay={
        <OpeMenus
          source={source}
          menuOpeFun={menuOpeFun}
          sortType={sortType}
          numOpeType={numOpeType}
          valueShowType={valueShowType}
          showTotal={showTotal}
          isNumber={isNumber}
          id='opeDrop'
        />}
      visible={dropdownVisible}
      placement='bottomRight'
      overlayClassName={styles.dropdown}
    >
      <DownOutlined className={styles.opeIcon} onClick={e => { iconOpe ? iconOpe() : dropClick() }} />
    </Dropdown>
    <Tooltip title={`${title} ${filterTitle}`} placement="bottom" overlayClassName={styles.overlayTooltip}>
      <div className={styles.itemTitle}>
        {showSource && <label>{source} - </label>}
        {
          isEdit ? <Input
            ref={input_focus}
            key={node.key}
            defaultValue={title}
            style={{ ...theme, border: 'none', borderRadius: 0 }}
            onBlur={onPressEnterAndBlur}
            onPressEnter={onPressEnterAndBlur} />
            : `${title} ${filterTitle}`
        }
      </div>
    </Tooltip>
    {onDeleteOpe && <CloseOutlined className={styles.closeIcon} onClick={onDeleteOpe} />}
  </div>
}