import React, { useContext, useState } from "react"
import styles from './components.module.scss'
import { useDrop } from "react-dnd";
import { FAElementItem } from "./FAElementItem";
import { CustomAuditContext } from "../CustomAuditContext";
import { CUSTOM_AUDIT_CHART_ELEMENT } from "../constants";
import { Tooltip, message } from "antd";
import { ExclamationCircleOutlined } from "@ant-design/icons";
import { PreFilter } from "./PreFilter";
import classNames from "classnames";
import { isEmpty } from "lodash";
import PostFilter from "./PostFilter";
import { useTranslation } from "react-i18next";
import { useSelector } from "src/hook";
import { calculateWidth } from "../utils";

export const FilterAndElements = () => {
  const { chartInfo, onInfoChange, eleNodes, onFieldInfoChange, baseData, filterNodes, filterList, onPreFilterChange, onfilterChange } = useContext(CustomAuditContext)
  const { chartType, fieldOrder, valueOrder } = chartInfo
  const { t } = useTranslation()
  const { locales } = useSelector(state => state.login)
  const [postVisible, setPostVisible] = useState<boolean>(false);
  const [postNode, setPostNode] = useState<any>({}); //事后过滤框
  const chartBaseInfo = CUSTOM_AUDIT_CHART_ELEMENT[chartType]
  const { elements, name } = chartBaseInfo

  // 对filterNodes的添项处理
  const handleFilterData = (node: any, source: string, type: string) => {
    const haveNode: boolean = filterNodes.filter((item: any) => item.key === node.key && item.title === node.title).length > 0
    if (haveNode) {
      let newNodes: any = []
      if (filterNodes.length > 0) {
        newNodes = filterNodes.map((item: any) => {
          if (item.key === node.key && item.title === node.title) return { ...item, showSource: true }
          else return item
        })
      }
      onfilterChange([...newNodes, { ...node, source, type, showSource: true }])
    }
    else onfilterChange([...filterNodes, { ...node, source, type, showSource: false }])
  }

  // drop方法的统一处理（减少重复代码）
  const commonDropAreaHandle = (index: number, node: any) => {
    const currentNodes = eleNodes?.[index]
    if (elements[index].mutexField) { // 判断互斥要素中是否已由指标字段
      let mutexIndex: number = -1
      elements.map((e: any, i: number) => {
        if (e.field === elements[index].mutexField) mutexIndex = i
      })
      if (eleNodes?.[mutexIndex]?.length > 0) return
    }
    const max = chartType !== 'TABLE' ? 3 : 10 // 表格多选允许十个，其他最多三项
    if ((elements[index].allowMultiple && currentNodes.length < max) || currentNodes.length === 0) { // 要素是否允许多选，多选的最多也只允许三项(表格除外)
      const isAdd: boolean = currentNodes.filter((item: any) => item.key === node.key).length <= 0 // 去重，不允许连续拖拽同一指标
      if (isAdd) {
        const newNode = elements[index].isNumerical ? { ...node, source: elements[index].name, title: t("auays:ele_title_lbl.count_of", { title: node.baseTitle }), action: 'COUNT', isNumber: true, actionLabel: t("auays:action_lbl.count") } : { ...node, source: elements[index].name }
        onFieldInfoChange({ ...eleNodes, [index]: [...currentNodes, newNode] })
        handleFilterData(newNode, elements[index].name, elements[index].field)
      }
    }
  }

  // 配置拖拽放置区域
  const [, dropRef0] = useDrop({
    accept: 'METRICS',
    drop: (data: any) => commonDropAreaHandle(0, { ...data?.node, baseTitle: data?.node?.title })
  });
  const [, dropRef1] = useDrop({
    accept: 'METRICS',
    drop: (data: any) => commonDropAreaHandle(1, { ...data?.node, baseTitle: data?.node?.title })
  });
  const [, dropRef2] = useDrop({
    accept: 'METRICS',
    drop: (data: any) => commonDropAreaHandle(2, { ...data?.node, baseTitle: data?.node?.title })
  });
  const [, dropRef3] = useDrop({
    accept: 'METRICS',
    drop: (data: any) => commonDropAreaHandle(3, { ...data?.node, baseTitle: data?.node?.title })
  });
  const dropRefList = [dropRef0, dropRef1, dropRef2, dropRef3]

  // 删除操作
  const onDelete = (index: number, node: any, source: string) => {
    const { key: nodeKey } = node
    const currentNodes = eleNodes[index].filter((item: any) => item.key !== nodeKey)
    onFieldInfoChange({ ...eleNodes, [index]: [...currentNodes] })
    let newNodes = filterNodes.filter((item: any) => !(item.key === nodeKey && item.source === source))
    const sameNode = newNodes.filter((item: any) => item.key === nodeKey)
    if (sameNode.length === 1) newNodes = newNodes.map((item: any) => {
      if (item.key === nodeKey) return { ...item, showSource: false }
      else return item
    })
    onfilterChange([...newNodes])
  }

  // 重命名操作
  const onEditOpe = (index: number, node: any, source: string, newTitle: string) => {
    const { key: nodeKey } = node
    let nTitle: string = ''
    if (node.action && node.actionLabel) {
      nTitle = t("auays:ele_title_lbl.of", { baseTitle: newTitle, actionLabel: node.actionLabel })
    }
    else {
      nTitle = newTitle
    }
    const currentNodes = eleNodes[index].map((item: any) => {
      if (item.key === nodeKey) return { ...item, title: nTitle }
      else return item
    })
    onFieldInfoChange({ ...eleNodes, [index]: [...currentNodes] })
    let newNodes = filterNodes.map((item: any) => {
      if (item.key === nodeKey && item.source === source) return { ...item, title: nTitle }
      else return item
    })
    const sameNode = newNodes.filter((i: any) => i.key === nodeKey && i.title !== nTitle)
    if (sameNode.length === 1) newNodes = newNodes.map((item: any) => {
      if (item.key === nodeKey) return { ...item, showSource: false }
      else return item
    })
    onfilterChange([...newNodes])
  }

  //移动操作 -- 除表
  const onMoveOpe = (index: number, node: any, source: string, target: string) => {
    const { key: nodeKey } = node
    // 目标
    let targetElement: any = {}
    let targetIndex = -1
    let mutexElement: any = {}
    let mutexIndex = -1
    elements.map((ele: any, i: number) => {
      if (ele.name === target) {
        targetElement = ele;
        targetIndex = i
      }
    })
    if (targetElement.mutexField) {
      elements.map((ele: any, i: number) => {
        if (ele.field === targetElement.mutexField) {
          mutexElement = ele;
          mutexIndex = i
        }
      })
    }
    // 来源
    const sourceElement = elements[index]
    if (sourceElement.mutexField && sourceElement.mutexField === targetElement.field && eleNodes[index].length - 1 > 0) {
      message.warning(t("auays:msg.mutually_exclusive", { target: t(target) }))
    }
    else if (targetElement.mutexField && eleNodes[mutexIndex].length > 0 && targetElement.mutexField !== sourceElement.field) {
      message.warning(t("auays:msg.mutually_exclusive_with_fields", { target: t(target), mutex: t(mutexElement.name) }))
    }
    else if (!targetElement.allowMultiple && eleNodes[targetIndex].length > 0) {
      message.warning(t("auays:msg.no_multiple_selection", { target: t(target) }))
    }
    else if (eleNodes[targetIndex].filter((item: any) => item.key === nodeKey).length > 0) {
      message.warning(t("auays:msg.duplicate_fields", { target: t(target) }))
    }
    else {
      const sourceNodes = eleNodes[index].filter((item: any) => item.key !== nodeKey)
      let newNode = {
        ...node,
        source: target
      }
      if (targetElement.isNumerical) {
        newNode = {
          ...newNode,
          title: t("auays:ele_title_lbl.count_of", { title: node.baseTitle }),
          action: 'COUNT'
        }
      }
      else if (sourceElement.isNumerical) {
        newNode = {
          ...newNode,
          title: node.baseTitle,
          action: undefined
        }
      }
      const targetNodes = [...eleNodes[targetIndex], newNode]
      onFieldInfoChange({ ...eleNodes, [index]: sourceNodes, [targetIndex]: targetNodes })
      const newNodes = filterNodes.map((item: any) => {
        if (item.key === nodeKey && item.source === source) return { ...item, ...newNode, filter: {} } //要将事后筛选清空
        else return item
      })
      onfilterChange([...newNodes])
    }
  }

  // 移动操作 -- 表
  const onTableMoveOpe = (index: number, node: any, target: string) => {
    const { key: nodeKey } = node
    let currentNodes = eleNodes[index]
    let nodeIndex: number = -1
    currentNodes.map((item: any, i: number) => {
      if (item.key === nodeKey) nodeIndex = i
    })
    switch (target) {
      case t("auays:ope_lbl.up"):
        if (nodeIndex === 0) return;
        else {
          [currentNodes[nodeIndex], currentNodes[nodeIndex - 1]] = [currentNodes[nodeIndex - 1], currentNodes[nodeIndex]]
          onFieldInfoChange({ ...eleNodes, [index]: currentNodes })
        }
        break;
      case t("auays:ope_lbl.down"):
        if (nodeIndex === currentNodes.length - 1) return;
        else {
          [currentNodes[nodeIndex], currentNodes[nodeIndex + 1]] = [currentNodes[nodeIndex + 1], currentNodes[nodeIndex]]
          onFieldInfoChange({ ...eleNodes, [index]: currentNodes })
        }
        break;
      case t("auays:ope_lbl.to_top"):
        currentNodes = [...currentNodes.splice(nodeIndex, 1), ...currentNodes]
        onFieldInfoChange({ ...eleNodes, [index]: currentNodes })
        break;
      case t("auays:ope_lbl.to_bottom"):
        currentNodes.splice(nodeIndex, 1)
        currentNodes = [...currentNodes, node]
        onFieldInfoChange({ ...eleNodes, [index]: currentNodes })
        break;
      default:
        break;
    }
  }

  // 数值操作
  const onCountOpe = (index: number, node: any, source: string, opeInfo: any) => {
    const { key: nodeKey } = node
    let prefix: string = ''
    if (node.action && node.actionLabel) {
      const subStr = t("auays:ele_title.of_action", { actionLabel: t(node.actionLabel) })
      prefix = node.title.replace(subStr, '')
    }
    else {
      prefix = node.title
    }
    console.log(prefix, opeInfo.label);
    let newTitle = t("auays:ele_title_lbl.of", { baseTitle: prefix, actionLabel: t(opeInfo.label) })
    if (chartType !== 'TABLE') {
      const currentNodes = eleNodes[index].map((item: any) => {
        if (item.key === nodeKey) return { ...item, title: newTitle, action: opeInfo.field, isNumber: true, actionLabel: opeInfo.label }
        else return item
      })
      onFieldInfoChange({ ...eleNodes, [index]: [...currentNodes] })
      let newNodes = filterNodes.map((item: any) => {
        if (item.key === nodeKey && item.source === source) return { ...item, title: newTitle, action: opeInfo.field, isNumber: true, actionLabel: opeInfo.label }
        else return item
      })
      const sameNode = newNodes.filter((i: any) => i.key === nodeKey && i.title !== newTitle)
      if (sameNode.length === 1) newNodes = newNodes.map((item: any) => {
        if (item.key === nodeKey) return { ...item, showSource: false }
        else return item
      })
      onfilterChange([...newNodes])
    }
    else {
      const currentNodes = eleNodes[index].map((item: any) => {
        if (item.key === nodeKey) return { ...item, title: newTitle, action: opeInfo.field, isNumber: true, actionLabel: opeInfo.label }
        else return { ...item, action: undefined, title: item.baseTitle, isNumber: false, actionLabel: undefined }
      })
      onFieldInfoChange({ ...eleNodes, [index]: [...currentNodes] })
      let newNodes = filterNodes.map((item: any) => {
        if (item.key === nodeKey && item.source === source) return { ...item, title: newTitle, action: opeInfo.field, isNumber: true, actionLabel: opeInfo.label }
        else return { ...item, action: undefined, title: item.baseTitle, isNumber: false, actionLabel: undefined }
      })
      onfilterChange([...newNodes])
    }
  }

  // 表和列的是否汇总
  const onShowTotalOpe = (index: number, node: any, source: string) => {
    const { key: nodeKey } = node
    const currentNodes = eleNodes[index].map((item: any) => {
      if (item.key === nodeKey) return { ...item, isNumber: !item.isNumber }
      else return item
    })
    onFieldInfoChange({ ...eleNodes, [index]: [...currentNodes] })
    let newNodes = filterNodes.map((item: any) => {
      if (item.key === nodeKey && item.source === source) return { ...item, isNumber: !item.isNumber }
      else return item
    })
    onfilterChange([...newNodes])
  }

  // 是否展示百分比
  const onShowTypeOpe = (opeInfo: any) => {
    if (opeInfo.key === '13-2') {
      onInfoChange({
        chartShowType: true
      })
    }
    else {
      onInfoChange({
        chartShowType: false
      })
    }
  }

  // 事后过滤弹窗
  const iconOpe = (node: any) => {
    if (isEmpty(baseData)) {
      message.warn(t("auays:msg.filter_ope_not_available"))
    }
    else {
      setPostNode(node)
      setPostVisible(true)
    }
  }

  // 排序
  const onSortOpe = (opeInfo: any, node: any) => {
    let value: string = opeInfo.key
    // 非数值轴排序
    if (!node.action) {
      if (fieldOrder === value || value === '') { onInfoChange({ fieldOrder: undefined }) }
      else { onInfoChange({ fieldOrder: value }) }
    }
    else if (valueOrder === value || value === '') { onInfoChange({ valueOrder: undefined }) }
    else { onInfoChange({ valueOrder: value }) }
  }

  return <div className={classNames(styles.filterAndElements, 'custom_audit_metrics_third')}>
    {/* 事前过滤 */}
    <PreFilter defaultValue={filterList} callBack={onPreFilterChange} id={'new'} />
    {
      elements.map((eleItem: any, index: number) => {
        const mutexInfo = !eleItem.mutexField ? '' : t("auays:ele_tip.mutually_exclusive", { mutex: t(elements.filter((i: any) => i.field === eleItem.mutexField)[0].name) })
        const tableInfo = chartType === 'TABLE' ? t("auays:ele_tip.numerical_operation_required") : ''
        const maxInfo = chartType === 'TABLE' ? t("auays:ele_tip.multiple_selection_limit_ten") : t("auays:ele_tip.multiple_selection_limit_three")
        const tooltipContent = `${eleItem.required ? t("auays:ele_tip.required") : t("auays:ele_tip.optional")}，${eleItem.allowMultiple ? maxInfo : t("auays:ele_tip.no_multiple_selection")}${mutexInfo}${tableInfo}`
        return (
          <div className={styles.elementItem} ref={dropRefList[index]} key={`${name}-${eleItem.field}`}>
            {/* 折线图和分区图有个辅助Y轴，width要加一些*/}
            <div className={styles.title} style={{ width: calculateWidth(locales, chartType, eleItem.field) }}>
              {t(eleItem.name)}
              <Tooltip title={tooltipContent} className={styles.eleTooltip}>
                <ExclamationCircleOutlined />
              </Tooltip>
              ：
            </div>
            <div className={styles.dropContent}>
              {
                eleNodes?.[index].length > 0 ?
                  eleNodes[index].map((node: any) => <FAElementItem
                    key={node.key}
                    node={node}
                    n={eleNodes[index].length}
                    theme={eleItem.theme}
                    onDeleteOpe={() => onDelete(index, node, eleItem.name)}
                    onEditOpe={(newValue: string) => onEditOpe(index, node, eleItem.name, newValue)}
                    onMoveOpe={(target: string) =>
                      chartType === 'TABLE' ?
                        onTableMoveOpe(index, node, target)
                        : onMoveOpe(index, node, eleItem.name, target)}
                    onCountOpe={(opeInfo: any) => onCountOpe(index, node, eleItem.name, opeInfo)}
                    onShowTotalOpe={() => onShowTotalOpe(index, node, eleItem.name)}
                    onShowTypeOpe={(opeInfo: any) => onShowTypeOpe(opeInfo)}
                    onSortOpe={(opeInfo: any) => onSortOpe(opeInfo, node)}
                  />)
                  : <div className={styles.contentPlaceholder}>{t("auays:div_lbl.drag_metrics_here")}</div>
              }
            </div>
          </div>
        )
      })
    }
    <div className={styles.elementItem}>
      <div className={styles.title} style={locales === 'en' ? { width: "140px" } : { width: '86px' }}>
        {t("auays:div_lbl.filter_condition")}
        <Tooltip title={t("auays:tip_title.filter_after_generation")} className={styles.eleTooltip}>
          <ExclamationCircleOutlined />
        </Tooltip>
        ：
      </div>
      <div className={styles.dropContent}>
        {
          filterNodes?.length > 0 ?
            filterNodes.map((node: any) => <FAElementItem node={node} n={filterNodes.length} iconOpe={() => iconOpe(node)} key={node.key + node.source} />)
            : <div className={styles.contentPlaceholder}>{t("auays:div_lbl.auto_generate")}</div>
        }
      </div>
    </div>
    {/* 事后过滤 */}
    {postVisible && <PostFilter
      visible={postVisible}
      setVisible={setPostVisible}
      node={postNode}
    />}
  </div>
}