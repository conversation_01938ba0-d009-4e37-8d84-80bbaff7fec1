import { Menu } from "antd"
import React, { useContext } from "react"
import { CustomAuditContext } from "../CustomAuditContext"
import { CUSTOM_AUDIT_CHART_ELEMENT } from "../constants"
import styles from './components.module.scss'
import { CheckOutlined } from "@ant-design/icons"
import { useTranslation } from "react-i18next"
const { SubMenu } = Menu;
const MenuItem = Menu.Item

export const OpeMenus = (props: any) => {
  const { source, menuOpeFun, sortType, numOpeType, valueShowType, showTotal, isNumber } = props
  const { t } = useTranslation()
  const { chartInfo } = useContext(CustomAuditContext)
  const { chartType } = chartInfo
  const chartBaseInfo = CUSTOM_AUDIT_CHART_ELEMENT[chartType]
  const { elements } = chartBaseInfo
  const opeMenuList = elements.filter((item: any) => item.name === source)[0]?.opeMenu(isNumber)

  const showYes = (key: string) => {
    switch (key) {
      case 'desc':
      case 'asc':
        return sortType === key;
      case '5':
      case '6':
      case '7':
      case '8':
      case '9':
      case '10':
      case '11':
      case '12':
        return numOpeType === key;
      case '13-1':
      case '13-2':
        return valueShowType === key
      case '14':
        return showTotal === false
    }
  }

  return <Menu
    className={styles.dropdownMenu}
    style={{ boxShadow: '0px 0px 8px 0px rgba(130, 131, 160, 0.3)' }}
    selectable={false}
  >
    {
      opeMenuList.map((menuItem: any) => {
        if (menuItem.children) return <SubMenu key={menuItem.key} title={t(menuItem.label)} popupClassName={styles.dropdpwnSubMenu} >
          {
            menuItem.children.map((item: any) => <MenuItem key={item.key} onClick={() => menuOpeFun[menuItem.key](item)} >
              {
                item.type === 'select' ?
                  <div className={styles.selectMenu}>
                    {t(item.label)}
                    {
                      showYes(item.key) && <CheckOutlined />
                    }
                  </div>
                  : t(item.label)
              }
            </MenuItem>)
          }
        </SubMenu>
        else return <Menu.Item key={menuItem.key} onClick={() => menuOpeFun[menuItem.key](menuItem)}>
          {
            menuItem.type === 'select' ?
              <div className={styles.selectMenu}>
                {t(menuItem.label)}
                {
                  showYes(menuItem.key) && <CheckOutlined />
                }
              </div>
              : t(menuItem.label)
          }
        </Menu.Item>
      })
    }
  </Menu >
}