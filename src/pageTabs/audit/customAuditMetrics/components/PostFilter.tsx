import { Checkbox, Input, Modal, Radio, Select, Tooltip, message } from "antd"
import { RadioChangeEvent } from "antd/lib/radio"
import React, { useContext, useEffect, useMemo, useState } from "react"
import styles from './components.module.scss'
import { CheckboxValueType } from "antd/lib/checkbox/Group"
import { CheckboxChangeEvent } from "antd/lib/checkbox"
import { CustomAuditContext } from "../CustomAuditContext"
import { isEmpty } from "lodash"
import { useTranslation } from "react-i18next"
import { useSelector } from "src/hook"
const RadioGroup = Radio.Group
const { Search } = Input;
const CheckboxGroup = Checkbox.Group;

const headerOptions = [
  { label: "auays:post_filter_type.basic_filter", value: 'BASIC' },
  { label: "auays:post_filter_type.advanced_filter", value: 'ADVANCED' },
  { label: "auays:post_filter_type.top_n", value: 'TOPN' },
];

export const baseOptions = [
  { label: "auays:post_filter_ope.contains", value: '1' },
  { label: "auays:post_filter_ope.not_contain", value: '2' },
  { label: "auays:post_filter_ope.starts_with", value: '3' },
  { label: "auays:post_filter_ope.not_start_with", value: '4' },
  { label: "auays:post_filter_ope.equals", value: '5' },
  { label: "auays:post_filter_ope.not_equal", value: '6' },
];
let seniorOptions = baseOptions
const PostFilter = (props: any) => {
  const { chartInfo, filterNodes, onfilterChange } = useContext(CustomAuditContext)
  const { visible, setVisible, node } = props
  const { t } = useTranslation()
  const { locales } = useSelector(state => state.login)
  const [type, setType] = useState<string>('BASIC');
  const [checkedList, setCheckedList] = useState<CheckboxValueType[]>([]);
  const [indeterminate, setIndeterminate] = useState(false);
  const [checkAll, setCheckAll] = useState(true);
  const [plainOptions, setPlainOptions] = useState<any[]>([])
  const [seniorRadioValue, setSeniorRadioValue] = useState<number>(1);
  const [seniorList, setSeniorList] = useState<any>([]);
  const [nValue, setNValue] = useState<any>(); //前n个

  /**
   * 初始化
   */
  const initValues = (node: any) => {
    const options = node?.fieldData.map((item: string | number) => String(item) || '-')
    const filter = node?.filter
    if (filter?.tabType === 'TOPN') {
      setType('TOPN')
      setNValue(filter.topN)
    }
    else if (filter?.tabType === 'BASIC') {
      setType('BASIC')
      setCheckedList(filter.values)
    }
    else if (filter?.tabType === 'ADVANCED') {
      setType('ADVANCED')
      const advancedFilter = filter?.advancedFilter || {}
      setSeniorList([{ filterType: advancedFilter?.firstAction, content: advancedFilter?.firstValue }, { filterType: advancedFilter?.lastAction, content: advancedFilter?.lastValue }])
      setSeniorRadioValue(Number(advancedFilter?.logicalAction))
    }
    else {
      setType('BASIC')
      setCheckedList([...options])
    }
  }

  useEffect(() => {
    if (!isEmpty(node) && visible) {
      const options = node?.fieldData.map((item: string | number) => String(item) || '-')
      setPlainOptions([...options])
      if (node.action && chartInfo.chartType !== 'MATRIX') seniorOptions = baseOptions.slice(4, 6)
      else seniorOptions = baseOptions
      // 初始化
      initValues(node)
    }
  }, [node])

  const onHeaderChange = ({ target: { value } }: RadioChangeEvent) => {
    setType(value);
    const filter = node?.filter
    if (value === filter?.tabType) {
      initValues(node)
    }
    else {
      const options = node?.fieldData.map((item: string | number) => String(item) || '-')
      setCheckedList([...options])
      setNValue(undefined)
      setSeniorList([])
    }
  };

  const onSearch = (value: string) => {
    const options = node?.fieldData.map((item: string | number) => String(item) || '-')
    if (!value) setPlainOptions([...options])
    else {
      const newOptions = options?.filter((item: any) => item === value)
      setPlainOptions(newOptions)
    }
  }

  const onChange = (list: CheckboxValueType[]) => {
    setCheckedList(list);
    setIndeterminate(!!list.length && list.length < plainOptions.length);
    setCheckAll(list.length === plainOptions.length);
  };

  const onCheckAllChange = (e: CheckboxChangeEvent) => {
    setCheckedList(e.target.checked ? plainOptions : []);
    setIndeterminate(false);
    setCheckAll(e.target.checked);
  };


  const onSeniorChange = (e: RadioChangeEvent) => {
    setSeniorRadioValue(e.target.value);
  };

  const onSeniorItemChange = (values: any, index: number) => {
    let list = [...seniorList]
    list[index] = { ...values }
    setSeniorList([...list])
  };

  const onNChange = (e: any) => {
    setNValue(e.target.value)
  };

  const onOk = () => {
    const filter: any = {}
    if (type === 'TOPN') {
      if (handleCheckTopN(nValue)) {
        if (Number(nValue) > 0) {
          filter.tabType = type
          filter.topN = Number(nValue)
        }
      }
      else {
        setNValue(undefined)
        return
      }
    }
    else if (type === 'BASIC') {
      filter.tabType = type
      filter.values = checkedList || []
    }
    else if (type === 'ADVANCED') {
      if ((seniorList?.[0]?.filterType && seniorList?.[0]?.content) || (seniorList?.[1]?.filterType && seniorList?.[1]?.content)) {
        filter.tabType = type
        filter.advancedFilter = {
          firstAction: seniorList?.[0]?.filterType,
          firstValue: seniorList?.[0]?.content,
          lastAction: seniorList?.[1]?.filterType,
          lastValue: seniorList?.[1]?.content,
          logicalAction: seniorRadioValue
        }
      }
    }
    const newNode = {
      ...node,
      filter
    }
    const newFilterNodes = filterNodes.map((item: any) => {
      if (item.key === node?.key && item.source === node?.source) return newNode
      else return item
    })
    onfilterChange(newFilterNodes)
    setVisible(false)
  }
  // 校验前n个是否为数值
  const handleCheckTopN = (value: any) => {
    const reg = /^\+?[1-9]\d*$/;
    if (!value || reg.test(value)) {
      return true
    }
    else {
      message.destroy()
      message.error(t("auays:msg.integer_only"))
      return false
    }
  }

  const filterItem = useMemo(() => {
    switch (type) {
      // 基本筛选
      case 'BASIC':
        return <div>
          <Search placeholder={t("auays:inp_ph.search_field")} onSearch={onSearch} style={{ width: '100%' }} />
          {
            plainOptions.length > 0 && <div className={styles.checkBox}>
              <Checkbox indeterminate={indeterminate} onChange={onCheckAllChange} checked={checkAll}>
                {t("auays:check.select_all")}
              </Checkbox>
              <CheckboxGroup value={checkedList} onChange={onChange}>
                {
                  plainOptions.map((item: any) => <Checkbox value={item} key={item}>
                    {
                      item?.length > 25 ?
                        <Tooltip title={item} placement="topLeft">
                          {item}
                        </Tooltip>
                        : item
                    }
                  </Checkbox>)
                }
              </CheckboxGroup>
            </div>
          }
        </div>
      // 高级筛选
      case 'ADVANCED':
        return <div className={styles.seniorContent}>
          <div className={styles.tipInfo}>{t("auays:div_lbl.display_rule")}</div>
          <SeniorItme onChange={(values: any) => onSeniorItemChange(values, 0)} id={1} defaultValue={seniorList?.[0] || {}} />
          <Radio.Group onChange={onSeniorChange} value={seniorRadioValue}>
            <Radio value={1}>{t("auays:filter_title.and")}</Radio>
            <Radio value={2}>{t("auays:filter_title.or")}</Radio>
          </Radio.Group>
          <SeniorItme onChange={(values: any) => onSeniorItemChange(values, 1)} id={2} defaultValue={seniorList?.[1] || {}} />
        </div>
      // 前N个筛选
      default:
        return <div className={styles.nContent}>
          <span>N：</span>
          <Input onChange={onNChange} placeholder={t("auays:inp_ph.numeric_value")} value={nValue} allowClear />
        </div>
    }
  }, [checkAll, checkedList, indeterminate, nValue, plainOptions, seniorList, seniorRadioValue, type, locales])

  return <Modal
    title={t("auays:div_lbl.filter_condition")}
    width={493}
    visible={visible}
    onCancel={() => {
      setVisible(false)
    }}
    className={styles.postFilter}
    onOk={onOk}
  >
    <div
      className={styles.filterHeader}
      style={{ marginLeft: locales === 'en' ? '27px' : '57px' }}
    >
      <div
        className={styles.filterLabel}
        style={{ width: locales === 'en' ? '69px' : '56px' }}
      >
        {t("auays:div_lbl.filter_type")}
      </div>
      <RadioGroup options={headerOptions.map((item: any) => ({ ...item, label: t(item.label) }))} onChange={onHeaderChange} value={type} optionType="button" />
    </div>
    <div className={styles.filterContent}>
      {filterItem}
    </div>
  </Modal>
}

const SeniorItme = ({ onChange, id, defaultValue }: any) => {
  const { t } = useTranslation()
  const [filterType, setFilterType] = useState<any>(defaultValue?.filterType);
  const [content, setContent] = useState<any>(defaultValue?.content);

  const handleSelectChange = (value: string) => {
    setFilterType(value)
  };

  const handleInputChange = (e: any) => {
    setContent(e.target.value)
  };

  useEffect(() => {
    onChange({ filterType, content })
  }, [filterType, content])

  return <div className={styles.seniorItem} key={id}>
    <Select
      placeholder={t("auays:sele_ph.filter_method")}
      onChange={handleSelectChange}
      options={seniorOptions.map((item: any) => ({ ...item, label: t(item.label) }))}
      className={styles.seniorItemSelect}
      defaultValue={defaultValue?.filterType}
      allowClear
    />
    <Input
      onChange={handleInputChange}
      placeholder={t("auays:inp_ph.filter_content")}
      defaultValue={defaultValue?.content}
      allowClear
    />
  </div>
}
export default PostFilter