import { CloseOutlined, DoubleLeftOutlined, DoubleRightOutlined } from "@ant-design/icons"
import React, { useCallback, useEffect, useLayoutEffect, useRef, useState } from "react"
import styles from './components.module.scss'
import { Button, DatePicker, Form, Popover, Select } from "antd"
import moment, { Moment } from "moment"
import { ConnectionIcon } from "src/components"
import { useRequest, useSelector } from "src/hook"
import { getAuditConnections, getAuditDbtype, getAuditDepts, getAuditExecutors } from "src/api"
import { uniq } from "lodash"
import { DELETE_ICON } from "../constants"
import classNames from "classnames"
import { t } from "i18next"
import { useTranslation } from "react-i18next"
const FormItem = Form.Item
const { RangePicker } = DatePicker;

const layout = {
  labelCol: { span: 5 },
  wrapperCol: { span: 19 },
};

const tailLayout = {
  wrapperCol: { span: 24 }
};

export const getMomentAlias = (startTime: Moment, endTime: Moment): string => {
  if (endTime.diff(moment(), 'days') === 0) {
    if (startTime.diff(moment(), 'days') === 0) return t("auays:filter_lbl.today")
    if (startTime.diff(moment().subtract(7, 'd'), 'days') === 0) return t("auays:filter_lbl.last_7_days")
    if (startTime.diff(moment().subtract(30, 'd'), 'days') === 0) return t("auays:filter_lbl.last_month")
    if (startTime.diff(moment().subtract(90, 'd'), 'days') === 0) return t("auays:filter_lbl.last_three_months")
    else return startTime.format('YYYY/MM/DD') + '~' + endTime.format('YYYY/MM/DD')
  }
  else return startTime.format('YYYY/MM/DD') + '~' + endTime.format('YYYY/MM/DD')
}

export const PreFilter = (props: any) => {
  // 为了在审计概览中复用，将筛选项传出
  const { defaultValue, callBack, id } = props
  const [visible, setVisible] = useState<boolean>(false);
  const [form] = Form.useForm()
  const { t } = useTranslation()
  const [filterMap, setFilterMap] = useState<any>(defaultValue)
  const formKeys = ['date', 'dbTypes', 'connectionName', 'depts', 'executors']
  const [contentWidth, setContentWidth] = useState<number>(0);
  const [mapWidth, setMapWidth] = useState<number>(0);
  const [mapTranslateX, setMapTranslateX] = useState<number>(0);
  const [connectionOptions, setConnectionOptions] = useState<any[]>([]);
  const [executorsOptions, setExecutorsOptions] = useState<any[]>([]);
  const { locales } = useSelector(state => state.login)
  const componentsContentItem = useRef<any>([])

  useEffect(() => {
    setFilterMap(defaultValue)
  }, [defaultValue])

  const setWidth = () => {
    const elements = componentsContentItem.current;
    let filterMapWidth = 0

    elements.forEach((element: any) => {
      if (element) {
        const width = element.offsetWidth
        filterMapWidth += width + 1;
      }
    });

    filterMapWidth += (elements.length - 1) * 8
    const filterMapDom: any = document.getElementById(`${id}_filterMap`)
    const filterContentDom: any = document.getElementById(`${id}_filterContent`)
    const filterContentWidth: number = filterContentDom?.offsetWidth
    filterMapDom && (filterMapDom.style.width = `${filterMapWidth}px`)
    filterMapDom && (filterMapDom.style.transform = 'translateX(0px)')
    setMapTranslateX(0)
    setContentWidth(filterContentWidth)
    setMapWidth(filterMapWidth)
  }

  useLayoutEffect(() => {
    callBack(filterMap)
    setWidth()
  }, [filterMap, id])

  const handleArrowClick = (direction: string) => {
    const filterMapDom: any = document.getElementById(`${id}_filterMap`)
    /* 步长 */
    const translateStep: number = 100
    const translateDistance: number = translateStep * (direction === 'left' ? 1 : -1)

    let newTranslateX: number = mapTranslateX
    /* 相对移动距离 */
    const relativeTranslateX: number = contentWidth - mapTranslateX
    const isLeftEnd: boolean = relativeTranslateX <= contentWidth
    const isLeftOverflow: boolean = relativeTranslateX - translateDistance <= contentWidth
    const isRightEnd: boolean = relativeTranslateX >= mapWidth // 这个10是代表右边距的10个像素，加上10隐藏
    const isRightOverflow: boolean = relativeTranslateX - translateDistance >= mapWidth

    /* 点击左箭头 */
    if (translateDistance > 0) {
      /* 是否到达左边尽头 */
      if (isLeftEnd) return

      if (isLeftOverflow) {
        /* 超出范围，则滑动刚好到达左边末尾的距离 */
        newTranslateX = 0
      } else {
        /* 未超出范围，滑动距离直接与步长相加 */
        newTranslateX += translateDistance
      }

    } else if (translateDistance < 0) {
      /* 是否到达右边尽头 */
      if (isRightEnd) return

      if (isRightOverflow) {
        /* 超出范围，则滑动刚好到达右边末尾的距离 */
        newTranslateX += relativeTranslateX + 10 - mapWidth
      } else {
        /* 未超出范围，滑动距离直接与步长相加 */
        newTranslateX += translateDistance
      }
    }

    const transformString: string = `translateX(${newTranslateX}px)`
    filterMapDom && (filterMapDom.style.transform = transformString)
    setMapTranslateX(newTranslateX)
  }


  const onFinish = (values: any) => {
    let newList: any = []
    const sourceListEum: any = {
      dbTypes: dbtypes || [],
      connectionName: connectionOptions || [],
      depts: deptOptions || [],
      executors: executorsOptions || []
    }
    Object.keys(values).map((key: string) => {
      if (values[key]) {
        if (key === 'date') {
          const [startTime, endTime] = values.date
          const label = getMomentAlias(startTime, endTime)
          // 处理数据为秒级时间戳
          const start = moment(startTime.format('YYYY-MM-DD 00:00:00'))
          const end = moment(endTime.format('YYYY-MM-DD 23:59:59'))
          newList.push({ label, value: [start, end], field: key })
        }
        else {
          const list = values[key].map((value: string | number) => {
            const source = sourceListEum[key]
            const valueItem = source.filter((item: any) => item.value === value)[0]
            return { ...valueItem, field: key }
          })
          newList = [...newList, ...list]
        }
      }
    })
    setFilterMap(newList)
    setVisible(false)
  };

  // 数据源类型Options
  const { data: dbtypes, loading: loadingDbtypesTypes } = useRequest(getAuditDbtype, {
    manual: false,
    formatResult: ((data: any[]) => {
      return data?.map((dbtypeName: string) => ({
        label: dbtypeName,
        value: dbtypeName
      }))
    })
  })

  const dbTypeOptions = dbtypes?.map(({ label, value }: any) => ({
    label: (
      <>
        <ConnectionIcon type={label} />
        {label}
      </>
    ),
    value: value
  }))

  // 数据源
  const { run: connectionRun, loading: loadingConnectionTypes } = useRequest(getAuditConnections, {
    manual: true,
    formatResult: ((data: any[]) => {
      setConnectionOptions(data?.map((connectionName: string) => ({
        label: connectionName,
        value: connectionName
      })))
      return data
    })
  })

  // 部门
  const { data: deptOptions, loading: loadingDeptsTypes } = useRequest(getAuditDepts, {
    manual: false,
    formatResult: ((data: any[]) => {
      return data?.map((dbtypeName: string) => ({
        label: dbtypeName,
        value: dbtypeName
      }))
    })
  })

  // 账号
  const { run: executorsRun, loading: loadingExecutorsTypes } = useRequest(getAuditExecutors, {
    manual: true,
    formatResult: ((data: any[]) => {
      setExecutorsOptions(data?.map((executorsName: string) => ({
        label: executorsName,
        value: executorsName
      })))
      return data
    })
  })

  const getFormInitialValues = useCallback((): any => {
    let initialValues: any = {}
    formKeys.map((key: string) => initialValues[key] = undefined)
    const keys: string[] = uniq(filterMap.map((item: any) => item.field))
    keys.map((key: string) => {
      if (key === 'date') {
        initialValues.date = filterMap.filter((item: any) => item.field === 'date')[0]?.value
      }
      else {
        const fieldList = filterMap.filter((item: any) => item.field === key)
        const value = fieldList.map((item: any) => item.value)
        initialValues[key] = value
      }
    })
    return initialValues
  }, [filterMap]);

  // 删除操作
  const onDelete = (item: any) => {
    const newFilter = filterMap.filter((filter: any) => !(filter.field === item.field && filter.value === item.value))
    setFilterMap(newFilter)
  }

  const onCancel = () => {
    setVisible(false);
  };

  const handleOpenChange = (newOpen: boolean) => {
    form.setFieldsValue(getFormInitialValues())
    setVisible(newOpen)
  }

  const popoverContent = <div>
    <Form
      {...layout}
      form={form}
      name="pre-filter"
      onFinish={onFinish}
      className={styles.popForm}
      initialValues={getFormInitialValues()}
      labelCol={locales === 'en' ? { span: 8 } : { span: 6 }}
    >
      <FormItem label={t("auays:item_label.time")} name="date" >
        <RangePicker
          style={{ width: '100%' }}
          ranges={{
            [t("auays:filter_lbl.today")]: [moment(), moment()],
            [t("auays:filter_lbl.last_7_days")]: [moment().subtract(7, 'd'), moment()],
            [t("auays:filter_lbl.last_month")]: [moment().subtract(30, 'd'), moment()],
            [t("auays:filter_lbl.last_three_months")]: [moment().subtract(90, 'd'), moment()],
          }}
        />
      </FormItem>
      <FormItem label={t("auays:item_label.data_source_type")} name="dbTypes">
        <Select
          showArrow
          placeholder={t("auays:item_label.data_source_type")}
          allowClear
          style={{ minWidth: 100 }}
          mode="multiple"
          maxTagCount={1}
          options={dbTypeOptions}
          loading={loadingDbtypesTypes}
          onChange={(values: string[]) => {
            if (values.length > 0) connectionRun(values)
            else {
              const values = form.getFieldsValue(true)
              form.setFieldsValue({
                ...values,
                connectionName: []
              })
              setConnectionOptions([])
            }
          }}
        />
      </FormItem>
      <FormItem label={t("auays:item_label.data_source_name")} name="connectionName">
        <Select
          showArrow
          placeholder={t("auays:item_label.data_source_name")}
          allowClear
          style={{ minWidth: 100 }}
          mode="multiple"
          maxTagCount={1}
          options={connectionOptions}
          loading={loadingConnectionTypes}
        />
      </FormItem>
      <FormItem label={t("auays:item_label.department")} name="depts">
        <Select
          showArrow
          placeholder={t("auays:item_label.department")}
          allowClear
          style={{ minWidth: 100 }}
          mode="multiple"
          maxTagCount={1}
          options={deptOptions}
          loading={loadingDeptsTypes}
          onChange={(values: string[]) => {
            if (values.length > 0) executorsRun(values)
            else {
              const values = form.getFieldsValue(true)
              form.setFieldsValue({
                ...values,
                executors: []
              })
              setExecutorsOptions([])
            }
          }}
        />
      </FormItem>
      <FormItem label={t("auays:item_label.account")} name="executors">
        <Select
          showArrow
          placeholder={t("auays:item_label.account")}
          allowClear
          style={{ minWidth: 100 }}
          mode="multiple"
          maxTagCount={1}
          options={executorsOptions}
          loading={loadingExecutorsTypes}
        />
      </FormItem>
      <FormItem {...tailLayout} >
        <div className={styles.popBtns}>
          <Button htmlType="button" onClick={onCancel} style={{ marginRight: '8px' }}>{t("auays:btn.cancel")}</Button>
          <Button type="primary" htmlType="submit">{t("auays:btn.sure")}</Button>
        </div>
      </FormItem>
    </Form>
  </div>

  return <div className={styles.preFilter}>
    <div className={styles.filterIcon}>
      <Popover
        placement="bottom"
        content={popoverContent}
        trigger="click"
        visible={visible}
        overlayClassName={styles.popoverContent}
        onVisibleChange={handleOpenChange}
        destroyTooltipOnHide={true}
      >
        {DELETE_ICON}
      </Popover>
    </div>
    <div className={classNames(styles.arrow, styles.arrowLeft)} onClick={() => handleArrowClick('left')} role="button">
      <DoubleLeftOutlined />
    </div>
    <div className={styles.filterContent} id={`${id}_filterContent`}>
      <div className={styles.filterMap} id={`${id}_filterMap`} >
        {
          filterMap.map((item: any, index: number) => <div ref={(ref) => componentsContentItem.current[index] = ref} className={styles.contentItem} key={`${item.field}+${item.value}`} >
            <span>
              {
                item.field === 'dbTypes' ?
                  <>
                    <ConnectionIcon type={item.value} />
                    {item.value}
                  </> : item.label
              }
            </span>
            <CloseOutlined className={styles.deleteIcon} onClick={() => onDelete(item)} />
          </div>)
        }
      </div>
    </div>
    <div className={classNames(styles.arrow, styles.arrowRight)} onClick={() => handleArrowClick('right')} role="button">
      <DoubleRightOutlined />
    </div>
  </div>
}