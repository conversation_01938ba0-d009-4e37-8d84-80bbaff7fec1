import React, { useContext } from "react"
import styles from './components.module.scss'
import { Tooltip } from "antd"
import { IconList, IIconItem } from "../constants"
import classNames from "classnames"
import { CustomAuditContext } from "../CustomAuditContext"
import { useTranslation } from "react-i18next"

export const VisualObjType = () => {
  const { chartInfo, onInfoChange, reset } = useContext(CustomAuditContext)
  const { t } = useTranslation()
  const { chartType } = chartInfo

  return <div className={classNames(styles.visualObjType, 'custom_audit_metrics_first')}>
    <div className={styles.title}>{t("auays:div_lbl.visual_object_type")}</div>
    <div className={styles.iconMap}>
      {
        IconList.map((item: IIconItem) => (
          <Tooltip key={item.id} title={t(item.type)} className={styles.flexItem}>
            <div
              className={classNames(styles.iconItem, { [styles.activeIconItem]: chartType === item.id })}
              onClick={() => {
                reset(false)
                setTimeout(() => {
                  onInfoChange({
                    ...chartInfo,
                    chartType: item.id,//图表类型
                    chartName: '',//图表名称
                    chartRemark: '',//图表备注
                    chartShowType: false, //占总计的百分比
                    fieldOrder: undefined,
                    valueOrder: undefined
                  })
                }, 0)
              }}
            >{item.icon}</div>
          </Tooltip>
        ))
      }
    </div>
  </div >
}