.visualObjType {
  margin-bottom: 24px;

  .title {
    font-weight: 500;
    height: 24px;
    line-height: 24px;
    letter-spacing: 0;
  }

  .iconMap {
    margin-top: 10px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;

    .iconItem {
      width: 28px;
      height: 28px;
      background-color: #FFFFFF;
      margin-right: calc((100% - 168px) / 5);
      margin-bottom: calc((100% - 168px) / 5);
      border-radius: 4px;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;

      &:nth-child(6n + 6) {
        margin-right: 0;
      }

    }

    .iconItem:nth-child(6n+1):nth-last-child(-n+6),
    .iconItem:nth-child(6n+1):nth-last-child(-n+6)~.iconItem {
      margin-bottom: 0;
    }

    .activeIconItem {
      background-color: #DBEAFF;
    }
  }
}

.auditDataTree {
  height: calc(100vh - 351px);

  .title {
    font-weight: 500;
    height: 24px;
    line-height: 24px;
    letter-spacing: 0;
  }

  .dataTree {
    margin-top: 8px;
    max-height: calc(100vh - 365px);
    overflow: auto;

    .treeTitle {
      width: 100%;
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
    }

    :global {
      .ant-tree-treenode {
        height: 32px;
        align-items: center;
        padding: 0;
      }

      .ant-tree-node-content-wrapper,
      .ant-tree-iconEle {
        height: 100%;
        line-height: 32px;
      }

      .ant-tree-node-content-wrapper:hover {
        background-color: #DBEAFF;
      }
    }

    .parentNode {
      :global {
        .ant-tree-node-content-wrapper {
          cursor: default;
        }
      }
    }
  }
}

.treeIcon {
  margin-top: 3px;
  margin-right: 6px;
}

.filterAndElements {
  margin-bottom: 8px;

  .elementItem {
    display: flex;
    padding: 0px 16px;
    justify-content: flex-start;
    align-items: center;
    height: 42px;
    border-radius: 4px;
    width: 100%;
    background: #FFFFFF;
    border: 1px dashed #B0C3ED;
    margin-bottom: 12px;

    .title {
      width: 72px;
      margin-right: 8px;
      flex: none;
      display: flex;
      justify-content: flex-start;
      align-items: center;

      .eleTooltip {
        margin: 0 2px;
        color: #8c8c8c;
        font-size: 12px;
      }
    }

    .dropContent {
      width: calc(100% - 82px);
      height: 100%;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      flex: 0 1 auto;

      .contentPlaceholder {
        color: #bfbfbf
      }
    }
  }

}

.customDragLayer {
  position: fixed;
  z-index: 100;
  left: 0;
  top: 0;
  height: 32px;
  background-color: #FFFFFF;
  pointer-events: none;
  padding: 0 4px;
  border-radius: 4px;
  box-shadow: 0px 0px 3px 1px rgba(136, 136, 136, 0.2);
  width: calc(17% - 102px); // 使拖拽的组件和树节点宽度保持一致，fixed定位需要计算。
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}

.filterAndElementItem {
  height: 32px;
  width: fit-content;
  display: flex;
  border-radius: 4px;
  justify-content: space-between;
  align-items: center;
  padding: 5px 8px;
  border: 1px dashed #667084;
  background: rgba(102, 112, 132, 0.16);
  margin-right: 12px;
  flex: 0 1 auto;

  &:first-child {
    margin-left: 12px;
  }

  &:last-child {
    margin-right: 0;
  }

  .itemTitle {
    flex-shrink: 1;
    flex-grow: 1;
    margin: 0 4px;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }

  .opeIcon {
    font-size: 9px;
  }

  .closeIcon {
    visibility: hidden;
    font-size: 9px;
  }

  &:hover .closeIcon {
    visibility: visible;
  }
}

.dropdownMenu {
  :global {

    .ant-menu-item,
    .ant-menu-item:not(:last-child),
    .ant-menu-submenu>.ant-menu-submenu-title {
      height: 36px;
      line-height: 36px;
      margin-top: 0;
      margin-bottom: 0;
    }
  }
}

.dropdpwnSubMenu {
  :global {

    .ant-menu-sub>.ant-menu-item,
    .ant-menu-sub>.ant-menu-item:not(:last-child) {
      height: 36px;
      line-height: 36px;
      margin-top: 0;
      margin-bottom: 0;
    }
  }
}

.selectMenu {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.preFilter {
  display: flex;
  padding: 0px 16px;
  justify-content: flex-start;
  align-items: center;
  height: 42px;
  border-radius: 4px;
  width: 100%;
  background: #FFFFFF;
  border: 1px dashed #B0C3ED;
  margin-bottom: 12px;
  position: relative;

  .filterIcon {
    width: 26px;
    height: 26px;
    background-color: #F2F3F5;
    border-radius: 4px;
    font-size: 14px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    margin-right: 12px;
  }

  .arrow {
    position: absolute;
    top: 8;
    width: 26px;
    height: 26px;
    background-color: #F2F3F5;
    border-radius: 4px;
    font-size: 12px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    transition: all .3s ease;
    z-index: 100;
  }

  .arrowLeft {
    left: 53px;
  }

  .arrowRight {
    right: 16px;
  }

  .filterContent {
    position: relative;
    overflow: hidden;
    width: 100%;
    margin: 0 34px;

    &::-webkit-scrollbar {
      width: 15px;
      height: 15px;
    }

    &::-webkit-scrollbar-track {
      border-radius: 20px;
      background: #e7e7e7;
    }

    &::-webkit-scrollbar-thumb {
      background: #66a6ff;
      background-image: linear-gradient(120deg, #89a4fe 0%, #66a6ff 100%);
      border-radius: 20px;
    }

    .filterMap {
      transition: all .3s ease-in-out;
      width: max-content;
      display: flex;
      flex-direction: row;

      .contentItem {
        margin-right: 8px;
        padding: 0 4px;
        height: 26px;
        background-color: #F2F3F5;
        border-radius: 2px;
        line-height: 26px;
        display: flex;
        flex-direction: row;
        white-space: nowrap;
        align-items: center;

        &:last-child {
          margin-right: 0;
        }

        .deleteIcon {
          font-size: 9px;
          margin-left: 4px;
          height: 9px;
        }
      }
    }

  }
}

.popoverContent {
  width: 420px;

  .popForm {
    padding: 16px 0px 0px;
  }

  .popBtns {
    padding: 8px 16px 12px;
    border-top: 1px solid #CECECF;
    display: flex;
    justify-content: flex-end;
    align-items: center;
  }

  :global {
    .ant-popover-inner-content {
      padding: 0px;
    }

    .ant-row.ant-form-item {
      padding: 0 16px;

      &:last-child {
        padding: 0;
      }
    }
  }
}

.postFilter {

  .filterHeader {
    display: flex;
    justify-content: flex-start;
    align-items: center;

    .filterLabel {
      margin-right: 12px;
    }
  }

  .filterContent {
    height: 350px;
    margin: 12px 100px 0px;
    width: 245px;

    .checkBox {
      margin-top: 12px;
      background: #F7F8FA;
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
      align-items: flex-start;

      :global {
        .ant-checkbox-group {
          display: flex;
          flex-direction: column;
          justify-content: flex-start;
          align-items: flex-start;
          max-height: 264px;
          width: 100%;
          overflow: hidden auto;
        }

        .ant-checkbox-wrapper,
        .ant-checkbox-group-item {
          width: 100%;
          min-height: 40px;
          padding: 9px 16px;
          text-overflow: ellipsis;
          white-space: nowrap;
          overflow: hidden;
          margin-left: 0;
        }
      }
    }

    .seniorContent {
      .tipInfo {
        color: #4E5969;
        font-size: 14px;
      }

      .seniorItem {
        margin: 8px 0;

        .seniorItemSelect {
          margin-bottom: 8px;
          width: 100%;
        }
      }
    }

    .nContent {
      display: flex;
      justify-content: flex-start;
      align-items: center;
    }
  }
}

.chartCard {
  position: relative;

  .defaultDisplay {
    display: flex;
    justify-content: center;
    flex-direction: column;
    align-items: center;
    height: 100%;

    .defaultImg {
      width: 460px;
      height: 268px;
      border: 1px dashed #b8b6b6;
    }

    .defaultTitle {
      height: 32px;
      line-height: 32px;
      font-weight: 500;
      font-size: 20px;
      color: #4E5969;
      margin: 8px 0;
    }

    .defaultTip {
      height: 24px;
      line-height: 24px;
      font-weight: 350;
      font-size: 16px;
      color: #4E5969;
    }
  }

  .chartTitle {
    display: flex;
    justify-content: flex-start;
    align-items: center;

    .infoIcon {
      margin-right: 4px;
      font-size: 14px;
    }

    .editIcon {
      margin-left: 8px;
      font-size: 14px;
    }
  }

  .mask {
    position: absolute;
    height: calc(100% - 43px);
    width: 100%;
    z-index: 50;
    bottom: 0;
    left: 0;
    background-color: rgba(0, 0, 0, 0.45);
  }

  .dataTable {
    position: absolute;
    height: 75%;
    width: 100%;
    z-index: 100;
    bottom: 0;
    border-radius: 0;
    left: 0;
    animation: slide-up 0.5s forwards;

    .table {
      height: 100%;

      :global {

        .ant-spin-nested-loading,
        .ant-spin-container,
        .ant-table,
        .ant-table-container {
          height: 100%
        }

        .ant-table-header {
          border-top: 1px solid #CECECF;
          border-left: 1px solid #CECECF;
          border-right: 1px solid #CECECF;
        }

        .ant-table-body {
          border-bottom: 1px solid #CECECF;
          border-left: 1px solid #CECECF;
          border-right: 1px solid #CECECF;
        }

      }
    }
  }

  @keyframes slide-up {
    from {
      transform: translateY(100%);
    }

    to {
      transform: translateY(0);
    }
  }


  .dataTable_hidden {
    animation: slide-down 0.5s forwards;
  }

  .dataTable_level {
    z-index: -1;
  }


  @keyframes slide-down {
    from {
      transform: translateY(0);
    }

    to {
      transform: translateY(200%);
    }
  }

  :global {
    .ant-card-head {
      min-height: 42px;
    }

    .ant-card-head-title {
      height: 42px;
      padding: 0;
    }

    .ant-card-body {
      height: calc(100% - 42px);
    }

    .ant-card-extra {
      padding: 0;
    }
  }
}

.chartListDrawer {
  :global {
    .ant-drawer-body {
      padding: 12px 16px;
    }
  }


  .chartList {
    height: 52px;
    width: 348px;
    margin-bottom: 16px;
    padding: 4px 8px;

    &:last-child {
      margin-bottom: 0;
    }

    &:hover {
      background-color: #F0F4FF;
      border-radius: 4px;
    }



    &:hover .chartTitle>.opeIcon {
      visibility: visible;
    }

    .chartTitle {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;

      .name {
        color: #0F244C;
        font-size: 14px;
        font-weight: 350;
        line-height: 22px;
        height: 22px;
        margin-bottom: 2px;
        width: calc(100% - 20px);
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .opeIcon {
        margin-left: 4px;
        font-size: 16px;
        display: flex;
        justify-content: center;
        align-items: center;
        visibility: hidden;
      }
    }

    .date {
      color: #86909C;
      font-size: 12px;
      font-weight: 350;
      line-height: 20px;
      height: 20px;
    }
  }

  .chartListActive {
    background-color: #3262FF;
    border-radius: 4px;

    .chartTitle>.name {
      color: #FFFFFF;
    }

    .chartTitle>.opeIcon {
      color: #FFFFFF;
    }

    .date {
      color: #F7F9FC;
    }

    &:hover {
      background-color: #3262FF;
      border-radius: 4px;
    }
  }
}

.auditDisplayModal {

  .pagination {
    display: flex;
    justify-content: flex-end;
    margin-top: 4px;
  }

  :global {
    .ant-checkbox-wrapper {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      width: calc(502px / 3);
      margin: 8px 0px;
    }
  }
}

.commonChart {
  height: 500px;
  .displayTable {
    height: calc(500px - 56px);
  }
}

:global {
  .ant-tooltip {
    max-width: 800px;
  }
}

.overlayTooltip {
  :global {
    .ant-tooltip-inner {
      max-width: 800px;
      max-height: 300px;
      overflow: hidden auto;
      padding-bottom: 0;
    }
  }
}

.chartTabs {
  position: absolute;
  bottom: 0;
  height: 36px;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin: 2px 0;
  width: 100%;

  .chartTabDiv {
    display: flex;
    justify-content: center;
    align-items: center;

    .chartTabItem {
      height: 26px;
      color: #4E5969;
      font-size: 14px;
      font-weight: normal;
      padding: 2px 8px;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      margin: 0 8px;
      cursor: pointer;
      width: calc(100% - 1px);
      overflow: auto;

      .chartName {
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
      }

      .tabCloseIcon {
        font-size: 10px;
        margin-left: 5px;
        display: none;
        cursor: pointer;
      }

      &:hover {
        background: #FFFFFF;
        border-radius: 2px;

        .chartName {
          width: calc(100% - 15px);
        }

        .tabCloseIcon {
          display: block;
        }
      }
    }

    .hiddenDelIcon {
      &:hover {
        .chartName {
          width: 100%;
        }
      }
    }

    &:first-child {
      .chartTabItem {
        margin-left: 0px;
      }
    }

    &:last-child {
      .chartTabItem {
        margin-right: 0px;
      }
    }

    .chartTabItemActive {
      color: #165DFF;
    }

    .divider {
      width: 1px;
      height: 12px;
      background: #E5E6EB;
    }

  }

  .addTabBtn {
    height: 26px;
    width: 26px;
    background: #FFFFFF;
    border-radius: 3px;
    margin-left: 8px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
  }
}