import React from "react";
import { queryAuditMetricsAssistantY, queryAuditMetricsColumnY, queryAuditMetricsMatrix, queryAuditMetricsXY } from "src/api"

// STACKED_COLUMN_CHART:'堆积柱状图'        
// STACKED_BAR_CHART:'堆积条形图'        
// CLUSTERED_BAR_CHART:'簇状条形图'        
// CLUSTERED_COLUMN_CHART:'簇状柱状图'        
// STACKED_PERCENTAGE_BAR_CHART:'百分比堆积条形图'  
// STACKED_PERCENTAGE_COLUMN_CHART:'百分比堆积柱状图'  
// LINE_CHART:'折线图'            
// AREA_CHART:'分区图'            
// STACKED_AREA_CHART:'堆积面积图'        
// PERCENTILE_STACKED_AREA_CHART:'100%堆积分区图'   
// LINE_AND_STACKED_COLUMN_CHART:'折线和堆积柱状图' 
// LINE_AND_CLUSTERED_COLUMN_CHART:'折线和簇状柱形图' 
// RIBBON_DIAGRAM:'丝带图'           
// WATERFALL_CHART:'瀑布图'           
// PIE_CHART:'饼图'             
// DONUT_CHART:'环形图'           
// TABLE:'表'               
// MATRIX:'矩阵'      
// 审计概览-自定义审计指标-视觉对象iconSVG
export const CUSTOM_AUDIT_ICON_SVG: { [key: string]: JSX.Element } = {
  'STACKED_COLUMN_CHART': <svg className="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" id="10442" width='28' height='28'><path d="M877.714286 725.321143v30.500571H146.285714v-30.500571h731.428572z" fill="#5182E4" fillOpacity=".3" id="10443"></path><path d="M542.464 451.035429h182.857143V694.857143h-182.857143v-243.821714zM298.642286 512h182.857143v182.857143h-182.857143v-182.857143z" fill="#3262FF" id="10444"></path><path d="M542.464 268.178286h182.857143v182.857143h-182.857143v-182.857143z m-243.821714 121.929143h182.857143V512h-182.857143v-121.892571z" fill="#11CFA6" id="10445"></path></svg>,
  'STACKED_BAR_CHART': <svg className="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" id="10681" width='28' height='28'><path d="M286.46402 877.714346H256.000017V146.285724h30.464003v731.428622z" fill="#5182E4" fillOpacity=".3" id="10682"></path><path d="M560.749752 542.464037v182.857155H316.964593v-182.857155h243.785159z m-60.928004-243.821731v182.857155h-182.857155v-182.857155h182.857155z" fill="#3262FF" id="10683"></path><path d="M743.606908 542.464037v182.857155h-182.857156v-182.857155h182.857156zM621.714328 298.642306v182.857155h-121.89258v-182.857155H621.714328z" fill="#11CFA6" id="10684"></path></svg>,
  'CLUSTERED_BAR_CHART': <svg className="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" id="10920" width='28' height='28'><path d="M371.821714 914.285714h-30.500571V182.857143h30.500571v731.428571z" fill="#5182E4" fillOpacity=".3" id="10921"></path><path d="M736.841143 256m0 0l0 72.996571q0 0 0 0l-340.699429 0q0 0 0 0l0-72.996571q0 0 0 0l340.699429 0q0 0 0 0Z" fill="#3262FF" id="10922"></path><path d="M590.848 328.996571m0 0l0 72.996572q0 0 0 0l-194.669714 0q0 0 0 0l0-72.996572q0 0 0 0l194.669714 0q0 0 0 0Z" fill="#11CFA6" id="10923"></path><path d="M663.844571 475.062857m0 0l0 72.996572q0 0 0 0l-267.702857 0q0 0 0 0l0-72.996572q0 0 0 0l267.702857 0q0 0 0 0Z" fill="#3262FF" id="10924"></path><path d="M517.851429 548.022857m0 0l0 72.996572q0 0 0 0l-121.673143 0q0 0 0 0l0-72.996572q0 0 0 0l121.673143 0q0 0 0 0Z" fill="#11CFA6" id="10925"></path><path d="M590.848 694.016m0 0l0 72.996571q0 0 0 0l-194.669714 0q0 0 0 0l0-72.996571q0 0 0 0l194.669714 0q0 0 0 0Z" fill="#3262FF" id="10926"></path><path d="M517.851429 766.976m0 0l0 72.996571q0 0 0 0l-121.673143 0q0 0 0 0l0-72.996571q0 0 0 0l121.673143 0q0 0 0 0Z" fill="#11CFA6" id="10927"></path></svg>,
  'CLUSTERED_COLUMN_CHART': <svg className="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" id="11163" width='28' height='28'><path d="M877.714286 652.178286v30.500571H146.285714v-30.500571h731.428572z" fill="#5182E4" fillOpacity=".3" id="11164"></path><path d="M219.428571 287.158857h72.996572v340.699429H219.428571V287.158857z" fill="#3262FF" id="11165"></path><path d="M292.425143 433.152h72.996571v194.706286H292.425143v-194.706286z" fill="#11CFA6" id="11166"></path><path d="M438.491429 360.155429h72.996571v267.702857H438.491429v-267.702857z" fill="#3262FF" id="11167"></path><path d="M511.488 506.148571h72.996571v121.709715h-72.996571V506.148571z" fill="#11CFA6" id="11168"></path><path d="M657.444571 433.152h72.996572v194.706286h-72.996572v-194.706286z" fill="#3262FF" id="11169"></path><path d="M730.441143 506.148571h73.033143v121.709715h-73.033143V506.148571z" fill="#11CFA6" id="11170"></path></svg>,
  'STACKED_PERCENTAGE_BAR_CHART': <svg className="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" id="11406" width='28' height='28'><path d="M329.069714 731.428571h-36.571428V256h36.571428v475.428571z" fill="#5182E4" fillOpacity=".3" id="11407"></path><path d="M402.212571 292.571429h365.714286v109.714285h-365.714286V292.571429z" fill="#3262FF" id="11408"></path><path d="M621.641143 292.571429h146.285714v109.714285h-146.285714V292.571429z" fill="#11CFA6" id="11409"></path><path d="M402.212571 438.857143h365.714286v109.714286h-365.714286v-109.714286zM402.212571 585.142857h365.714286v109.714286h-365.714286v-109.714286z" fill="#3262FF" id="11410"></path><path d="M548.498286 438.857143h219.428571v109.714286h-219.428571v-109.714286zM694.784 585.142857h73.142857v109.714286h-73.142857v-109.714286z" fill="#11CFA6" id="11411"></path></svg>,
  'STACKED_PERCENTAGE_COLUMN_CHART': <svg className="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" id="11647" width='28' height='28'><path d="M731.428571 731.501714v36.571429H256v-36.571429h475.428571z" fill="#5182E4" fillOpacity=".3" id="11648"></path><path d="M292.571429 658.358857m0 0l0-365.714286q0 0 0 0l109.714285 0q0 0 0 0l0 365.714286q0 0 0 0l-109.714285 0q0 0 0 0Z" fill="#3262FF" id="11649"></path><path d="M292.571429 438.930286m0 0l0-146.285715q0 0 0 0l109.714285 0q0 0 0 0l0 146.285715q0 0 0 0l-109.714285 0q0 0 0 0Z" fill="#11CFA6" id="11650"></path><path d="M438.857143 658.358857m0 0l0-365.714286q0 0 0 0l109.714286 0q0 0 0 0l0 365.714286q0 0 0 0l-109.714286 0q0 0 0 0Z" fill="#3262FF" id="11651"></path><path d="M585.142857 658.358857m0 0l0-365.714286q0 0 0 0l109.714286 0q0 0 0 0l0 365.714286q0 0 0 0l-109.714286 0q0 0 0 0Z" fill="#3262FF" id="11652"></path><path d="M438.857143 512.073143m0 0l0-219.428572q0 0 0 0l109.714286 0q0 0 0 0l0 219.428572q0 0 0 0l-109.714286 0q0 0 0 0Z" fill="#11CFA6" id="11653"></path><path d="M585.142857 365.787429m0 0l0-73.142858q0 0 0 0l109.714286 0q0 0 0 0l0 73.142858q0 0 0 0l-109.714286 0q0 0 0 0Z" fill="#11CFA6" id="11654"></path></svg>,
  'LINE_CHART': <svg className="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" id="11890" width='28' height='28'><path d="M877.714286 688.749714v30.500572H146.285714v-30.500572h731.428572z" fill="#5182E4" fillOpacity=".3" id="11891"></path><path d="M896 347.428571h-131.145143l-219.428571 219.428572q-21.430857 21.430857-51.712 21.430857t-51.712-21.430857L360.594286 485.449143l-176.384 112.274286-39.277715-61.732572 176.384-112.237714q21.723429-13.787429 47.250286-10.971429 25.6 2.779429 43.739429 20.955429l81.408 81.408 219.428571-219.428572q21.430857-21.430857 51.712-21.430857H896v73.142857z" fill="#3262FF" id="11892"></path></svg>,
  'AREA_CHART': <svg className="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" id="12128" width='28' height='28'><path d="M877.714286 725.321143v30.500571H146.285714v-30.500571h731.428572zM146.285714 219.428571h36.571429v438.857143H146.285714V219.428571z" fill="#5182E4" fillOpacity=".3" id="12129"></path><path d="M804.571429 465.115429L547.84 621.714286l-160.658286-99.254857L219.428571 580.388571v-76.251428l176.201143-194.706286L548.571429 453.485714 804.571429 256v209.115429z" fill="#3262FF" id="12130"></path><path d="M530.285714 713.142857l-109.714285-43.081143-106.788572-34.742857-94.354286 34.742857v-45.750857l146.285715-57.453714 73.142857 36.571428 91.428571 54.857143v54.857143z" fill="#11CFA6" id="12131"></path></svg>,
  'STACKED_AREA_CHART': <svg className="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" id="12367" width='28' height='28'><path d="M0 0m0 0l1024 0q0 0 0 0l0 1024q0 0 0 0l-1024 0q0 0 0 0l0-1024q0 0 0 0Z" fill="#FFFFFF" fillOpacity="0" id="12368"></path><path d="M877.714286 768v30.464H146.285714V768h731.428572z" fill="#5182E4" fillOpacity=".3" id="12369"></path><path d="M760.283429 464.64l-217.197715 143.981714-135.899428-91.245714-141.897143 53.248v-70.144l149.028571-179.017143 129.462857 132.461714 216.502858-233.325714v244.041143z" fill="#0266DD" id="12370"></path><path d="M760.283429 682.532571H265.289143v-111.908571l141.897143-53.248 135.899428 91.245714 217.197715-143.981714v217.892571z" fill="#11CFA6" id="12371"></path></svg>,
  'PERCENTILE_STACKED_AREA_CHART': <svg className="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" id="12607" width='28' height='28'><path d="M219.428586 457.17946V804.571483h376.320026L219.428586 457.17946z" fill="#16C489" id="12608"></path><path d="M220.891444 256.000017v156.306297l263.789732 224.731444L768.000052 373.248025V256.000017H220.891444z" fill="#3262FF" id="12609"></path><path d="M484.644604 637.037758l177.84687 166.070868H768.000052V373.248025l-283.355448 263.789733z" fill="#11CFA6" id="12610"></path></svg>,
  'LINE_AND_STACKED_COLUMN_CHART': <svg className="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" id="12846" width='28' height='28'><path d="M731.428571 731.501714v36.571429H256v-36.571429h475.428571z" fill="#5182E4" fillOpacity=".3" id="12847"></path><path d="M292.571429 658.285714v-292.571428h109.714285v292.571428H292.571429z" fill="#3262FF" id="12848"></path><path d="M292.571429 438.857143v-73.142857h109.714285v73.142857H292.571429z" fill="#11CFA6" id="12849"></path><path d="M438.857143 658.285714v-182.857143h109.714286v182.857143h-109.714286zM585.142857 658.285714v-219.428571h109.714286v219.428571h-109.714286z" fill="#3262FF" id="12850"></path><path d="M438.857143 512v-36.571429h109.714286v36.571429h-109.714286zM585.142857 475.501714v-73.142857h109.714286v73.142857h-109.714286z" fill="#11CFA6" id="12851"></path><path d="M666.477714 290.633143l-108.251428 54.125714-83.565715 62.683429L335.542857 288.182857l23.771429-27.794286 116.882285 100.205715 62.72-47.067429 111.177143-55.588571 16.384 32.694857z" fill="#3262FF" id="12852"></path></svg>,
  'LINE_AND_CLUSTERED_COLUMN_CHART': <svg className="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" id="13088" width='28' height='28'><path d="M0 0m0 0l1024 0q0 0 0 0l0 1024q0 0 0 0l-1024 0q0 0 0 0l0-1024q0 0 0 0Z" fill="#FFFFFF" fillOpacity="0" id="13089"></path><path d="M877.714286 725.321143v30.500571H146.285714v-30.500571h731.428572z" fill="#5182E4" fillOpacity=".3" id="13090"></path><path d="M219.428571 360.301714h72.996572v340.699429H219.428571V360.301714z" fill="#3262FF" id="13091"></path><path d="M292.425143 506.294857h72.996571v194.706286H292.425143v-194.706286z" fill="#11CFA6" id="13092"></path><path d="M438.491429 433.298286h72.996571v267.702857H438.491429v-267.702857z" fill="#3262FF" id="13093"></path><path d="M511.488 579.291429h72.996571v121.709714h-72.996571V579.291429z" fill="#11CFA6" id="13094"></path><path d="M657.444571 506.294857h72.996572v194.706286h-72.996572v-194.706286z" fill="#3262FF" id="13095"></path><path d="M730.441143 579.291429h73.033143v121.709714h-73.033143V579.291429z" fill="#11CFA6" id="13096"></path><path d="M302.372571 256l507.282286 211.638857-13.385143 32.073143L288.987429 288.073143 302.372571 256z" fill="#11CFA6" id="13097"></path></svg>,
  'RIBBON_DIAGRAM': <svg className="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" id="13333" width='28' height='28'><path d="M256 453.741714h131.803429v175.762286H256v-175.762286zM256 651.483429h131.803429v87.881142H256v-87.881142z" fill="#11CFA6" id="13334"></path><path d="M256 256h131.803429v197.741714H256V256z" fill="#3262FF" id="13335"></path><path d="M409.782857 428.105143V256q128.950857 0 186.294857 151.04c57.307429 151.04 42.971429 261.851429 229.229715 261.851429h23.881142v70.473142h-290.377142q-40.118857-5.961143-63.049143-53.577142c-22.930286-47.579429-51.565714-71.387429-85.942857-65.462858v-166.582857q51.565714 0 74.496 89.234286c22.893714 89.234286 17.188571 154.697143 91.684571 154.697143h95.707429q-26.331429-19.858286-46.994286-48.969143c-11.702857-16.457143-24.064-35.84-36.973714-56.210286-50.249143-79.067429-109.568-172.397714-177.956572-164.388571z" fill="#3262FF" id="13336"></path></svg>,
  'WATERFALL_CHART': <svg className="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" id="13572" width='28' height='28'><path d="M256 329.142857h102.4v384H256V329.142857z" fill="#3262FF" id="13573"></path><path d="M409.6 329.142857H512v234.057143h-102.4V329.142857z" fill="#11CFA6" id="13574"></path><path d="M563.2 482.742857h102.4v179.2h-102.4v-179.2zM691.2 636.342857h102.4v76.8h-102.4v-76.8z" fill="#3262FF" id="13575"></path></svg>,
  'PIE_CHART': <svg className="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" id="13811" width='28' height='28'><path d="M527.689143 292.571429a235.117714 235.117714 0 1 0 235.081143 235.117714H527.725714V292.571429z" fill="#3262FF" id="13812"></path><path d="M553.801143 292.571429a236.836571 236.836571 0 0 1 208.969143 208.969142h-208.969143V292.571429z" fill="#11CFA6" id="13813"></path></svg>,
  'DONUT_CHART': <svg className="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" id="14200" width='28' height='28'><path d="M708.827477 658.285759A237.750873 237.750873 0 0 0 768.000052 512.000035l-119.808008 3.145143A126.427437 126.427437 0 0 1 621.714328 580.754325L708.827477 658.285759z" fill="#11CFA6" id="14201"></path><path d="M658.944045 475.428604L768.000052 472.320032a229.449158 229.449158 0 0 0-3.218286-22.49143 239.177159 239.177159 0 0 0-64.877718-124.19658A235.337159 235.337159 0 0 0 548.571466 256.000017v110.738294A128.365723 128.365723 0 0 1 658.944045 475.428604zM376.100597 512.000035c0-69.851433 53.028575-128.768009 123.721151-137.398867V256.000017a262.875446 262.875446 0 0 0-167.460583 74.532577 253.293732 253.293732 0 0 0 0.073143 362.569167c96.987435 95.158864 253.586303 100.278864 356.571453 11.702858 1.938286-1.572572 3.876572-3.291429 5.851429-5.083428l-87.771434-81.188577a142.189724 142.189724 0 0 1-90.075435 31.744002c-77.750862 0.073143-140.909724-61.805718-140.909724-138.276581z" fill="#0B69E6" id="14202"></path></svg>,
  'TABLE': <svg className="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" id="14438" width='28' height='28'><path d="M256.000017 292.571449h512.000035v146.285724H256.000017V292.571449z" fill="#3262FF" id="14439"></path><path d="M256.000017 463.250317h243.821731V585.142897H256.000017v-121.89258zM524.178321 463.250317H768.000052V585.142897h-243.821731v-121.89258z" fill="#11CFA6" id="14440"></path><path d="M256.000017 609.536042h243.821731V731.428621H256.000017v-121.892579zM524.178321 609.536042H768.000052V731.428621h-243.821731v-121.892579z" fill="#3262FF" id="14441"></path></svg>,
  'MATRIX': <svg className="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" id="14677" width='28' height='28'><path d="M256.000017 256.000017h512.000035v121.782866H256.000017V256.000017z" fill="#3262FF" id="14678"></path><path d="M256.000017 402.285742h109.714294v109.714293H256.000017v-109.714293zM402.285742 402.285742h365.71431v109.714293H402.285742v-109.714293zM256.000017 548.571466h109.714294v109.714293H256.000017v-109.714293zM402.285742 548.571466h365.71431v109.714293H402.285742v-109.714293zM256.000017 694.85719h109.714294v109.714293H256.000017v-109.714293zM402.285742 694.85719h365.71431v109.714293H402.285742v-109.714293z" fill="#11CFA6" id="14679"></path></svg>
}

export interface IIconItem {
  id: string,
  type: string,
  icon: JSX.Element
}

export const IconList: IIconItem[] = [
  { id: 'STACKED_COLUMN_CHART', type: "auays:custom_chart_type.stacked_column_chart", icon: CUSTOM_AUDIT_ICON_SVG['STACKED_COLUMN_CHART'] },
  { id: 'STACKED_BAR_CHART', type: "auays:custom_chart_type.stacked_bar_chart", icon: CUSTOM_AUDIT_ICON_SVG['STACKED_BAR_CHART'] },
  { id: 'CLUSTERED_BAR_CHART', type: "auays:custom_chart_type.clustered_bar_chart", icon: CUSTOM_AUDIT_ICON_SVG['CLUSTERED_BAR_CHART'] },
  { id: 'CLUSTERED_COLUMN_CHART', type: "auays:custom_chart_type.clustered_column_chart", icon: CUSTOM_AUDIT_ICON_SVG['CLUSTERED_COLUMN_CHART'] },
  { id: 'STACKED_PERCENTAGE_BAR_CHART', type: "auays:custom_chart_type.percentage_stacked_bar_chart", icon: CUSTOM_AUDIT_ICON_SVG['STACKED_PERCENTAGE_BAR_CHART'] },
  { id: 'STACKED_PERCENTAGE_COLUMN_CHART', type: "auays:custom_chart_type.percentage_stacked_column_chart", icon: CUSTOM_AUDIT_ICON_SVG['STACKED_PERCENTAGE_COLUMN_CHART'] },
  { id: 'LINE_CHART', type: "auays:custom_chart_type.line_chart", icon: CUSTOM_AUDIT_ICON_SVG['LINE_CHART'] },
  { id: 'AREA_CHART', type: "auays:custom_chart_type.area_chart", icon: CUSTOM_AUDIT_ICON_SVG['AREA_CHART'] },
  { id: 'STACKED_AREA_CHART', type: "auays:custom_chart_type.stacked_area_chart", icon: CUSTOM_AUDIT_ICON_SVG['STACKED_AREA_CHART'] },
  { id: 'PERCENTILE_STACKED_AREA_CHART', type: "auays:custom_chart_type.percentage_stacked_area_chart", icon: CUSTOM_AUDIT_ICON_SVG['PERCENTILE_STACKED_AREA_CHART'] },
  { id: 'LINE_AND_STACKED_COLUMN_CHART', type: "auays:custom_chart_type.line_and_stacked_column_chart", icon: CUSTOM_AUDIT_ICON_SVG['LINE_AND_STACKED_COLUMN_CHART'] },
  { id: 'LINE_AND_CLUSTERED_COLUMN_CHART', type: "auays:custom_chart_type.line_and_clustered_column_chart", icon: CUSTOM_AUDIT_ICON_SVG['LINE_AND_CLUSTERED_COLUMN_CHART'] },
  { id: 'RIBBON_DIAGRAM', type: "auays:custom_chart_type.ribbon_chart", icon: CUSTOM_AUDIT_ICON_SVG['RIBBON_DIAGRAM'] },
  { id: 'WATERFALL_CHART', type: "auays:custom_chart_type.waterfall_chart", icon: CUSTOM_AUDIT_ICON_SVG['WATERFALL_CHART'] },
  { id: 'PIE_CHART', type: "auays:custom_chart_type.pie_chart", icon: CUSTOM_AUDIT_ICON_SVG['PIE_CHART'] },
  { id: 'DONUT_CHART', type: "auays:custom_chart_type.doughnut_chart", icon: CUSTOM_AUDIT_ICON_SVG['DONUT_CHART'] },
  { id: 'TABLE', type: "auays:custom_chart_type.table", icon: CUSTOM_AUDIT_ICON_SVG['TABLE'] },
  { id: 'MATRIX', type: "auays:custom_chart_type.matrix", icon: CUSTOM_AUDIT_ICON_SVG['MATRIX'] }
]

// 审计概览-自定义审计指标-左下全部数据icon
export const treeIcon = <svg className="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" id="14915" width="16" height="16"><path d="M997.57321482 194.63585185v634.97102223c0 25.60758518-9.22358518 47.57428148-27.42802964 65.90008889-18.32580741 18.32580741-40.2925037 27.42802963-65.90008888 27.42802962H119.99762963c-25.60758518 0-47.57428148-9.10222222-65.90008888-27.42802962s-27.42802963-40.2925037-27.42802964-65.90008889V194.63585185c0-25.60758518 9.10222222-47.57428148 27.42802964-65.90008888 18.32580741-18.32580741 40.2925037-27.42802963 65.90008888-27.42802964h784.36882962c25.60758518 0 47.57428148 9.22358518 65.9000889 27.42802964 18.08308148 18.08308148 27.30666667 40.04977778 27.30666667 65.90008888z m-672.35081482 186.53487407v-112.01801481c0-5.46133333-1.69908148-9.95176297-5.21860741-13.34992593-3.51952592-3.51952592-8.00995555-5.21860741-13.34992592-5.2186074H119.87626667c-5.46133333 0-9.95176297 1.69908148-13.34992592 5.2186074-3.51952592 3.51952592-5.21860741 8.00995555-5.21860742 13.34992593v112.01801481c0 5.46133333 1.69908148 9.95176297 5.21860742 13.34992593 3.51952592 3.51952592 8.00995555 5.21860741 13.34992592 5.2186074h186.7776c5.46133333 0 9.95176297-1.69908148 13.34992592-5.2186074 3.51952592-3.39816297 5.21860741-7.88859259 5.21860741-13.34992593z m0 224.27875556v-112.01801481c0-5.46133333-1.69908148-9.95176297-5.21860741-13.34992592-3.51952592-3.51952592-8.00995555-5.21860741-13.34992592-5.21860742H119.87626667c-5.46133333 0-9.95176297 1.69908148-13.34992592 5.21860742-3.51952592 3.51952592-5.21860741 8.00995555-5.21860742 13.34992592V605.44948148c0 5.46133333 1.69908148 9.95176297 5.21860742 13.34992593 3.51952592 3.51952592 8.00995555 5.21860741 13.34992592 5.21860741h186.7776c5.46133333 0 9.95176297-1.69908148 13.34992592-5.21860741 3.51952592-3.39816297 5.21860741-8.00995555 5.21860741-13.34992593z m0 224.03602963v-112.01801481c0-5.46133333-1.69908148-9.95176297-5.21860741-13.34992593-3.51952592-3.51952592-8.00995555-5.21860741-13.34992592-5.2186074H119.87626667c-5.46133333 0-9.95176297 1.69908148-13.34992592 5.2186074-3.51952592 3.51952592-5.21860741 8.00995555-5.21860742 13.34992593v112.01801481c0 5.46133333 1.69908148 9.95176297 5.21860742 13.34992592 3.51952592 3.51952592 8.00995555 5.21860741 13.34992592 5.21860742h186.7776c5.46133333 0 9.95176297-1.69908148 13.34992592-5.21860742 3.51952592-3.51952592 5.21860741-7.88859259 5.21860741-13.34992592z m298.79561482-448.31478519v-112.01801481c0-5.46133333-1.69908148-9.95176297-5.21860741-13.34992593-3.51952592-3.51952592-8.00995555-5.21860741-13.34992593-5.2186074H418.55051852c-5.46133333 0-9.95176297 1.69908148-13.34992593 5.2186074-3.51952592 3.51952592-5.21860741 8.00995555-5.21860741 13.34992593v112.01801481c0 5.46133333 1.69908148 9.95176297 5.21860741 13.34992593 3.51952592 3.51952592 8.00995555 5.21860741 13.34992593 5.2186074h186.89896296c5.46133333 0 9.95176297-1.69908148 13.34992593-5.2186074 3.51952592-3.39816297 5.21860741-7.88859259 5.21860741-13.34992593z m0 224.27875556v-112.01801481c0-5.46133333-1.69908148-9.95176297-5.21860741-13.34992592-3.51952592-3.51952592-8.00995555-5.21860741-13.34992593-5.21860742H418.55051852c-5.46133333 0-9.95176297 1.69908148-13.34992593 5.21860742-3.51952592 3.51952592-5.21860741 8.00995555-5.21860741 13.34992592V605.44948148c0 5.46133333 1.69908148 9.95176297 5.21860741 13.34992593 3.51952592 3.51952592 8.00995555 5.21860741 13.34992593 5.21860741h186.89896296c5.46133333 0 9.95176297-1.69908148 13.34992593-5.21860741 3.51952592-3.39816297 5.21860741-8.00995555 5.21860741-13.34992593z m0 224.03602963v-112.01801481c0-5.46133333-1.69908148-9.95176297-5.21860741-13.34992593-3.51952592-3.51952592-8.00995555-5.21860741-13.34992593-5.2186074H418.55051852c-5.46133333 0-9.95176297 1.69908148-13.34992593 5.2186074-3.51952592 3.51952592-5.21860741 8.00995555-5.21860741 13.34992593v112.01801481c0 5.46133333 1.69908148 9.95176297 5.21860741 13.34992592 3.51952592 3.51952592 8.00995555 5.21860741 13.34992593 5.21860742h186.89896296c5.46133333 0 9.95176297-1.69908148 13.34992593-5.21860742 3.51952592-3.51952592 5.21860741-7.88859259 5.21860741-13.34992592z m298.79561481-448.31478519v-112.01801481c0-5.46133333-1.69908148-9.95176297-5.21860741-13.34992593-3.51952592-3.51952592-8.00995555-5.21860741-13.34992592-5.2186074H717.4674963c-5.46133333 0-9.95176297 1.69908148-13.34992593 5.2186074-3.51952592 3.51952592-5.21860741 8.00995555-5.2186074 13.34992593v112.01801481c0 5.46133333 1.69908148 9.95176297 5.2186074 13.34992593 3.51952592 3.51952592 8.00995555 5.21860741 13.34992593 5.2186074h186.7776c5.46133333 0 9.95176297-1.69908148 13.34992592-5.2186074 3.51952592-3.39816297 5.21860741-7.88859259 5.21860741-13.34992593z m0 224.27875556v-112.01801481c0-5.46133333-1.69908148-9.95176297-5.21860741-13.34992592-3.51952592-3.51952592-8.00995555-5.21860741-13.34992592-5.21860742H717.4674963c-5.46133333 0-9.95176297 1.69908148-13.34992593 5.21860742-3.51952592 3.51952592-5.21860741 8.00995555-5.2186074 13.34992592V605.44948148c0 5.46133333 1.69908148 9.95176297 5.2186074 13.34992593 3.51952592 3.39816297 8.00995555 5.21860741 13.34992593 5.21860741h186.7776c5.46133333 0 9.95176297-1.69908148 13.34992592-5.21860741s5.21860741-8.00995555 5.21860741-13.34992593z m0 224.03602963v-112.01801481c0-5.46133333-1.69908148-9.95176297-5.21860741-13.34992593-3.51952592-3.51952592-8.00995555-5.21860741-13.34992592-5.2186074H717.4674963c-5.46133333 0-9.95176297 1.69908148-13.34992593 5.2186074-3.51952592 3.51952592-5.21860741 8.00995555-5.2186074 13.34992593v112.01801481c0 5.46133333 1.69908148 9.95176297 5.2186074 13.34992592 3.51952592 3.51952592 8.00995555 5.21860741 13.34992593 5.21860742h186.7776c5.46133333 0 9.95176297-1.69908148 13.34992592-5.21860742 3.51952592-3.51952592 5.21860741-7.88859259 5.21860741-13.34992592z" fill="#2B58BA" id="14916"></path></svg>

// 根据图表类型和要素名称决定颜色主题
export const getThemeByCategory: { [key: string]: any } = {
  blue: { backgroundColor: '#E3E9FF', color: '#3262FF', borderColor: '#3262FF', },
  green: { backgroundColor: '#cff5ed', color: '#11CFA6', borderColor: '#11CFA6', },
  grey: { backgroundColor: '#e6e8eb', color: '#667084', borderColor: '#667084', }
}

export const commonOperations = (children: any[], showSort: boolean = true) => {
  const baseList = [
    { key: '1', label: "auays:ope_lbl.delete_field", type: 'function' },
    { key: '2', label: "auays:ope_lbl.rename_for_this_visual", type: 'function' },
    { key: '3', label: "auays:ope_lbl.move", type: 'function', children: children },
    {
      key: '4', label: "auays:ope_lbl.sort", children: [
        { key: 'asc', label: "auays:ope_lbl.ascending", type: 'select' },
        { key: 'desc', label: "auays:ope_lbl.descending", type: 'select' },
      ]
    },
  ]
  if (showSort) return baseList
  else {
    baseList.splice(-1)
    return baseList
  }
}
export const mutexOperations = (isNumber: boolean = false) => {
  const mutexList = [
    { key: '5', label: "auays:ope_lbl.sum", field: 'SUM', type: 'select' },
    { key: '6', label: "auays:ope_lbl.average", field: 'AVG', type: 'select' },
    { key: '7', label: "auays:ope_lbl.minimum", field: 'MIN', type: 'select' },
    { key: '8', label: "auays:ope_lbl.maximum", field: 'MAX', type: 'select' },
    { key: '9', label: "auays:ope_lbl.standard_deviation", field: 'STDDEV_POP', type: 'select' },
    { key: '11', label: "auays:ope_lbl.count_non_repeated", field: 'COUNT_DISTINCT', type: 'select' },
    { key: '12', label: "auays:ope_lbl.count", field: 'COUNT', type: 'select' },
    {
      key: '13', label: "auays:ope_lbl.show_value_as", children: [
        { key: '13-1', label: "auays:ope_lbl.no_calculation", type: 'select' },
        { key: '13-2', label: "auays:ope_lbl.percentage_of_total", type: 'select' },
      ]
    },
  ]
  if (isNumber) return mutexList
  else {
    mutexList.splice(0, 5)
    return mutexList
  }
}

// 列的特殊操作
export const columnOperations = () => {
  const columnList = [
    { key: '14', label: "auays:ope_lbl.no_summary", field: 'j', type: 'select' },
  ]
  return columnList
}


// 审计概览-自定义审计指标-右上方区域图表要素
export const CUSTOM_AUDIT_CHART_ELEMENT: { [key: string]: any } = {
  'STACKED_COLUMN_CHART': {
    name: "auays:custom_chart_type.stacked_column_chart",
    dealType: 1,
    elements: [
      {
        name: "auays:custom_chart_axis.x_axis",
        field: 'x',
        required: true,
        allowMultiple: true,
        theme: getThemeByCategory['blue'],
        opeMenu: () => commonOperations([
          { key: '3-1', label: "auays:custom_chart_axis.y_axis" },
          { key: '3-2', label: "auays:custom_chart_axis.legend" }
        ], true)
      },
      {
        name: "auays:custom_chart_axis.y_axis",
        field: 'y',
        isNumerical: true,
        required: true,
        allowMultiple: false,
        theme: getThemeByCategory['green'],
        opeMenu: (isNumber: boolean = false) => {
          const arr1 = commonOperations([
            { key: '3-1', label: "auays:custom_chart_axis.x_axis" },
            { key: '3-2', label: "auays:custom_chart_axis.legend" }
          ], true)
          const arr2 = mutexOperations(isNumber)
          return [...arr1, ...arr2]
        }
      },
      {
        name: "auays:custom_chart_axis.legend",
        field: 'title',
        required: false,
        allowMultiple: false,
        theme: getThemeByCategory['grey'],
        opeMenu: () => commonOperations([
          { key: '3-1', label: "auays:custom_chart_axis.x_axis" },
          { key: '3-2', label: "auays:custom_chart_axis.y_axis" }
        ], false)
      },
    ],
    chartOptions: {
      isTransverse: true,
      series: () => ({
        stack: 'StackedColumn',
        type: 'bar',
      })
    }
  },
  'STACKED_BAR_CHART': {
    name: "auays:custom_chart_type.stacked_bar_chart",
    dealType: 1,
    elements: [
      {
        name: "auays:custom_chart_axis.x_axis",
        field: 'y',
        isNumerical: true,
        required: true,
        allowMultiple: false,
        theme: getThemeByCategory['blue'],
        opeMenu: (isNumber: boolean = false) => {
          const arr1 = commonOperations([
            { key: '3-1', label: "auays:custom_chart_axis.y_axis" },
            { key: '3-2', label: "auays:custom_chart_axis.legend" }
          ], true)
          const arr2 = mutexOperations(isNumber)
          return [...arr1, ...arr2]
        }
      },
      {
        name: "auays:custom_chart_axis.y_axis",
        field: 'x',
        required: true,
        allowMultiple: true,
        theme: getThemeByCategory['green'],
        opeMenu: () => commonOperations([
          { key: '3-1', label: "auays:custom_chart_axis.x_axis" },
          { key: '3-2', label: "auays:custom_chart_axis.legend" }
        ], true)
      },
      {
        name: "auays:custom_chart_axis.legend",
        field: 'title',
        required: false,
        allowMultiple: false,
        theme: getThemeByCategory['grey'],
        opeMenu: () => commonOperations([
          { key: '3-1', label: "auays:custom_chart_axis.x_axis" },
          { key: '3-2', label: "auays:custom_chart_axis.y_axis" }
        ], false)
      },
    ],
    chartOptions: {
      isTransverse: false,
      series: (s: any, width: number, height: number) => (
        {
          type: 'bar',
          stack: 'StackedBar',
        }
      )
    }
  },
  'CLUSTERED_BAR_CHART': {
    name: "auays:custom_chart_type.clustered_bar_chart",
    dealType: 1,
    elements: [
      {
        name: "auays:custom_chart_axis.x_axis",
        field: 'y',
        isNumerical: true,
        required: true,
        allowMultiple: false,
        theme: getThemeByCategory['blue'],
        opeMenu: (isNumber: boolean = false) => {
          const arr1 = commonOperations([
            { key: '3-1', label: "auays:custom_chart_axis.y_axis" },
            { key: '3-2', label: "auays:custom_chart_axis.legend" }
          ], true)
          const arr2 = mutexOperations(isNumber)
          return [...arr1, ...arr2]
        }
      },
      {
        name: "auays:custom_chart_axis.y_axis",
        field: 'x',
        required: true,
        allowMultiple: true,
        theme: getThemeByCategory['green'],
        opeMenu: () => commonOperations([
          { key: '3-1', label: "auays:custom_chart_axis.x_axis" },
          { key: '3-2', label: "auays:custom_chart_axis.legend" }
        ], true)
      },
      {
        name: "auays:custom_chart_axis.legend", field: 'title', required: false, allowMultiple: false, theme: getThemeByCategory['grey'],
        opeMenu: () => commonOperations([
          { key: '3-1', label: "auays:custom_chart_axis.x_axis" },
          { key: '3-2', label: "auays:custom_chart_axis.y_axis" }
        ], false)
      },
    ],
    chartOptions: {
      isTransverse: false,
      series: () => ({
        type: 'bar',
      })
    }
  },
  'CLUSTERED_COLUMN_CHART': {
    name: "auays:custom_chart_type.clustered_column_chart",
    dealType: 1,
    elements: [
      {
        name: "auays:custom_chart_axis.x_axis", field: 'x', required: true, allowMultiple: true, theme: getThemeByCategory['blue'],
        opeMenu: () => commonOperations([
          { key: '3-1', label: "auays:custom_chart_axis.y_axis" },
          { key: '3-2', label: "auays:custom_chart_axis.legend" }
        ], true)
      },
      {
        name: "auays:custom_chart_axis.y_axis", field: 'y', required: true, allowMultiple: false, theme: getThemeByCategory['green'],
        isNumerical: true,
        opeMenu: (isNumber: boolean = false) => {
          const arr1 = commonOperations([
            { key: '3-1', label: "auays:custom_chart_axis.x_axis" },
            { key: '3-2', label: "auays:custom_chart_axis.legend" }
          ], true)
          const arr2 = mutexOperations(isNumber)
          return [...arr1, ...arr2]
        }
      },
      {
        name: "auays:custom_chart_axis.legend", field: 'title', required: false, allowMultiple: false, theme: getThemeByCategory['grey'],
        opeMenu: () => commonOperations([
          { key: '3-1', label: "auays:custom_chart_axis.x_axis" },
          { key: '3-2', label: "auays:custom_chart_axis.y_axis" }
        ], false)
      },
    ],
    chartOptions: {
      isTransverse: true,
      series: () => ({
        type: 'bar',
      })
    }
  },
  'STACKED_PERCENTAGE_BAR_CHART': {
    name: "auays:custom_chart_type.percentage_stacked_bar_chart",
    dealType: 1,
    elements: [
      {
        name: "auays:custom_chart_axis.x_axis", field: 'y', required: true, allowMultiple: false, theme: getThemeByCategory['blue'],
        isNumerical: true,
        opeMenu: (isNumber: boolean = false) => {
          const arr1 = commonOperations([
            { key: '3-1', label: "auays:custom_chart_axis.y_axis" },
            { key: '3-2', label: "auays:custom_chart_axis.legend" }
          ], true)
          const arr2 = mutexOperations(isNumber)
          return [...arr1, ...arr2]
        }
      },
      {
        name: "auays:custom_chart_axis.y_axis", field: 'x', required: true, allowMultiple: true, theme: getThemeByCategory['green'],
        opeMenu: () => commonOperations([
          { key: '3-1', label: "auays:custom_chart_axis.x_axis" },
          { key: '3-2', label: "auays:custom_chart_axis.legend" }
        ], true)
      },
      {
        name: "auays:custom_chart_axis.legend", field: 'title', required: false, allowMultiple: false, theme: getThemeByCategory['grey'],
        opeMenu: () => commonOperations([
          { key: '3-1', label: "auays:custom_chart_axis.x_axis" },
          { key: '3-2', label: "auays:custom_chart_axis.y_axis" }
        ], false)
      },
    ],
    chartOptions: {
      isTransverse: false,
      showPercent: true,
      series: (s: any, width: number, height: number) => ({
        type: 'bar',
        stack: 'PercentStackedBar',
        // barWidth: (height - 120) / s.data.length > 80 ? 80 : (height - 120) / s.data.length,
      }),
      xAxis: () => ({
        max: 1
      })
    }
  },
  'STACKED_PERCENTAGE_COLUMN_CHART': {
    name: "auays:custom_chart_type.percentage_stacked_column_chart",
    dealType: 1,
    elements: [
      {
        name: "auays:custom_chart_axis.x_axis", field: 'x', required: true, allowMultiple: true, theme: getThemeByCategory['blue'],
        opeMenu: () => commonOperations([
          { key: '3-1', label: "auays:custom_chart_axis.y_axis" },
          { key: '3-2', label: "auays:custom_chart_axis.legend" }
        ], true)
      },
      {
        name: "auays:custom_chart_axis.y_axis", field: 'y', required: true, allowMultiple: false, theme: getThemeByCategory['green'],
        isNumerical: true,
        opeMenu: (isNumber: boolean = false) => {
          const arr1 = commonOperations([
            { key: '3-1', label: "auays:custom_chart_axis.x_axis" },
            { key: '3-2', label: "auays:custom_chart_axis.legend" }
          ], true)
          const arr2 = mutexOperations(isNumber)
          return [...arr1, ...arr2]
        }
      },
      {
        name: "auays:custom_chart_axis.legend", field: 'title', required: false, allowMultiple: false, theme: getThemeByCategory['grey'],
        opeMenu: () => commonOperations([
          { key: '3-1', label: "auays:custom_chart_axis.x_axis" },
          { key: '3-2', label: "auays:custom_chart_axis.y_axis" }
        ], false)
      },
    ],
    chartOptions: {
      isTransverse: true,
      showPercent: true,
      series: (s: any, width: number) => ({
        type: 'bar',
        stack: 'PercentStackedColumn',
        // barWidth: (width - 100) / s.data.length > 80 ? 80 : (width - 100) / s.data.length,
      }),
      yAxis: () => ({
        max: 1
      })
    }
  },
  'LINE_CHART': {
    name: "auays:custom_chart_type.line_chart",
    dealType: 2,
    elements: [
      {
        name: "auays:custom_chart_axis.x_axis", field: 'x', required: true, allowMultiple: true, theme: getThemeByCategory['blue'],
        opeMenu: () => commonOperations([
          { key: '3-1', label: "auays:custom_chart_axis.y_axis" },
          { key: '3-2', label: "auays:custom_chart_axis.auxiliary_y_axis" },
          { key: '3-3', label: "auays:custom_chart_axis.legend" }
        ], false)
      },
      {
        name: "auays:custom_chart_axis.y_axis", field: 'y', required: true, allowMultiple: false, theme: getThemeByCategory['green'],
        isNumerical: true,
        opeMenu: (isNumber: boolean = false) => {
          const arr1 = commonOperations([
            { key: '3-1', label: "auays:custom_chart_axis.x_axis" },
            { key: '3-2', label: "auays:custom_chart_axis.auxiliary_y_axis" },
            { key: '3-3', label: "auays:custom_chart_axis.legend" }
          ], false)
          const arr2 = mutexOperations(isNumber)
          return [...arr1, ...arr2]
        }
      },
      {
        name: "auays:custom_chart_axis.auxiliary_y_axis", field: 'assistY', required: false, allowMultiple: false, mutexField: 'title', theme: getThemeByCategory['green'],
        isNumerical: true,
        opeMenu: (isNumber: boolean = false) => {
          const arr1 = commonOperations([
            { key: '3-1', label: "auays:custom_chart_axis.x_axis" },
            { key: '3-2', label: "auays:custom_chart_axis.y_axis" },
            { key: '3-3', label: "auays:custom_chart_axis.legend" }
          ], false)
          const arr2 = mutexOperations(isNumber)
          return [...arr1, ...arr2]
        }
      },
      {
        name: "auays:custom_chart_axis.legend", field: 'title', required: false, allowMultiple: false, mutexField: 'assistY', theme: getThemeByCategory['grey'],
        opeMenu: () => commonOperations([
          { key: '3-1', label: "auays:custom_chart_axis.x_axis" },
          { key: '3-2', label: "auays:custom_chart_axis.y_axis" },
          { key: '3-3', label: "auays:custom_chart_axis.auxiliary_y_axis" }
        ], false)
      },
    ],
    chartOptions: {
      isTransverse: true,
      series: (s: any, width: number, height: number, index: number, data: any) => ({
        type: 'line',
        yAxisIndex: data?.hasFy && index === data?.series?.length - 1 ? 1 : 0,
      })
    }
  },
  'AREA_CHART': {
    name: "auays:custom_chart_type.area_chart",
    dealType: 2,
    elements: [
      {
        name: "auays:custom_chart_axis.x_axis", field: 'x', required: true, allowMultiple: true, theme: getThemeByCategory['blue'],
        opeMenu: () => commonOperations([
          { key: '3-1', label: "auays:custom_chart_axis.y_axis" },
          { key: '3-2', label: "auays:custom_chart_axis.auxiliary_y_axis" },
          { key: '3-3', label: "auays:custom_chart_axis.legend" }
        ], false)
      },
      {
        name: "auays:custom_chart_axis.y_axis", field: 'y', required: true, allowMultiple: false, theme: getThemeByCategory['green'],
        isNumerical: true,
        opeMenu: (isNumber: boolean = false) => {
          const arr1 = commonOperations([
            { key: '3-1', label: "auays:custom_chart_axis.x_axis" },
            { key: '3-2', label: "auays:custom_chart_axis.auxiliary_y_axis" },
            { key: '3-3', label: "auays:custom_chart_axis.legend" }
          ], false)
          const arr2 = mutexOperations(isNumber)
          return [...arr1, ...arr2]
        }
      },
      {
        name: "auays:custom_chart_axis.auxiliary_y_axis", field: 'assistY', required: false, allowMultiple: false, mutexField: 'title', theme: getThemeByCategory['green'],
        isNumerical: true,
        opeMenu: (isNumber: boolean = false) => {
          const arr1 = commonOperations([
            { key: '3-1', label: "auays:custom_chart_axis.x_axis" },
            { key: '3-2', label: "auays:custom_chart_axis.y_axis" },
            { key: '3-3', label: "auays:custom_chart_axis.legend" }
          ], false)
          const arr2 = mutexOperations(isNumber)
          return [...arr1, ...arr2]
        }
      },
      {
        name: "auays:custom_chart_axis.legend", field: 'title', required: false, allowMultiple: false, mutexField: 'assistY', theme: getThemeByCategory['grey'],
        opeMenu: () => commonOperations([
          { key: '3-1', label: "auays:custom_chart_axis.x_axis" },
          { key: '3-2', label: "auays:custom_chart_axis.y_axis" },
          { key: '3-3', label: "auays:custom_chart_axis.auxiliary_y_axis" },
        ], false)
      },
    ],
    chartOptions: {
      isTransverse: true,
      series: (s: any, width: number, height: number, index: number, data: any) => ({
        type: 'line',
        yAxisIndex: data?.hasFy && index === data?.series?.length - 1 ? 1 : 0,
        areaStyle: {},
      }),
      xAxis: () => ({
        boundaryGap: false,
      })
    }
  },
  'STACKED_AREA_CHART': {
    name: "auays:custom_chart_type.stacked_area_chart",
    dealType: 1,
    elements: [
      {
        name: "auays:custom_chart_axis.x_axis", field: 'x', required: true, allowMultiple: true, theme: getThemeByCategory['blue'],
        opeMenu: () => commonOperations([
          { key: '3-1', label: "auays:custom_chart_axis.y_axis" },
          { key: '3-2', label: "auays:custom_chart_axis.legend" }
        ], true)
      },
      {
        name: "auays:custom_chart_axis.y_axis", field: 'y', required: true, allowMultiple: false, theme: getThemeByCategory['green'],
        isNumerical: true,
        opeMenu: (isNumber: boolean = false) => {
          const arr1 = commonOperations([
            { key: '3-1', label: "auays:custom_chart_axis.x_axis" },
            { key: '3-2', label: "auays:custom_chart_axis.legend" }
          ], true)
          const arr2 = mutexOperations(isNumber)
          return [...arr1, ...arr2]
        }
      },
      {
        name: "auays:custom_chart_axis.legend", field: 'title', required: false, allowMultiple: false, theme: getThemeByCategory['grey'],
        opeMenu: () => commonOperations([
          { key: '3-1', label: "auays:custom_chart_axis.x_axis" },
          { key: '3-2', label: "auays:custom_chart_axis.y_axis" }
        ], false)
      },
    ],
    chartOptions: {
      isTransverse: true,
      series: () => ({
        type: 'line',
        stack: 'StackedArea',
        areaStyle: {},
      }),
      tooltip: {
        axisPointer: {
          type: 'cross'
        }
      },
      xAxis: (data: any) => ({
        boundaryGap: data?.noNumber?.length > 1,
      })
    }
  },
  'PERCENTILE_STACKED_AREA_CHART': {
    name: "auays:custom_chart_type.percentage_stacked_area_chart",
    dealType: 1,
    elements: [
      {
        name: "auays:custom_chart_axis.x_axis", field: 'x', required: true, allowMultiple: true, theme: getThemeByCategory['blue'],
        opeMenu: () => commonOperations([
          { key: '3-1', label: "auays:custom_chart_axis.y_axis" },
          { key: '3-2', label: "auays:custom_chart_axis.legend" }
        ], true)
      },
      {
        name: "auays:custom_chart_axis.y_axis", field: 'y', required: true, allowMultiple: false, theme: getThemeByCategory['green'],
        isNumerical: true,
        opeMenu: (isNumber: boolean = false) => {
          const arr1 = commonOperations([
            { key: '3-1', label: "auays:custom_chart_axis.x_axis" },
            { key: '3-2', label: "auays:custom_chart_axis.legend" }
          ], true)
          const arr2 = mutexOperations(isNumber)
          return [...arr1, ...arr2]
        }
      },
      {
        name: "auays:custom_chart_axis.legend", field: 'title', required: false, allowMultiple: false, theme: getThemeByCategory['grey'],
        opeMenu: () => commonOperations([
          { key: '3-1', label: "auays:custom_chart_axis.x_axis" },
          { key: '3-2', label: "auays:custom_chart_axis.y_axis" }
        ], false)
      },
    ],
    chartOptions: {
      isTransverse: true,
      showPercent: true,
      series: () => ({
        type: 'line',
        stack: 'PercentStackedArea',
        areaStyle: {},
      }),
      tooltip: {
        axisPointer: {
          type: 'cross'
        }
      },
      xAxis: (data: any) => ({
        boundaryGap: data?.noNumber?.length > 1,
      }),
      yAxis: () => ({
        max: 1
      })
    }
  },
  'LINE_AND_STACKED_COLUMN_CHART': {
    name: "auays:custom_chart_type.line_and_stacked_column_chart",
    dealType: 3,
    elements: [
      {
        name: "auays:custom_chart_axis.x_axis", field: 'x', required: true, allowMultiple: true, theme: getThemeByCategory['blue'],
        opeMenu: () => commonOperations([
          { key: '3-1', label: 'auays:custom_chart_axis.column_y_axis' },
          { key: '3-2', label: "auays:custom_chart_axis.row_y_axis" },
          { key: '3-3', label: "auays:custom_chart_axis.column_legend" }
        ], false)
      },
      {
        name: 'auays:custom_chart_axis.column_y_axis', field: 'y', required: true, allowMultiple: false, theme: getThemeByCategory['green'],
        isNumerical: true,
        opeMenu: (isNumber: boolean = false) => {
          const arr1 = commonOperations([
            { key: '3-1', label: "auays:custom_chart_axis.x_axis" },
            { key: '3-2', label: "auays:custom_chart_axis.row_y_axis" },
            { key: '3-3', label: "auays:custom_chart_axis.column_legend" }
          ], false)
          const arr2 = mutexOperations(isNumber)
          return [...arr1, ...arr2]
        }
      },
      {
        name: "auays:custom_chart_axis.row_y_axis", field: 'assistY', required: false, allowMultiple: false, theme: getThemeByCategory['green'],
        isNumerical: true,
        opeMenu: (isNumber: boolean = false) => {
          const arr1 = commonOperations([
            { key: '3-1', label: "auays:custom_chart_axis.x_axis" },
            { key: '3-2', label: 'auays:custom_chart_axis.column_y_axis' },
            { key: '3-3', label: "auays:custom_chart_axis.column_legend" }
          ], false)
          const arr2 = mutexOperations(isNumber)
          return [...arr1, ...arr2]
        }
      },
      {
        name: "auays:custom_chart_axis.column_legend", field: 'title', required: false, allowMultiple: false, theme: getThemeByCategory['grey'],
        opeMenu: () => commonOperations([
          { key: '3-1', label: "auays:custom_chart_axis.x_axis" },
          { key: '3-2', label: 'auays:custom_chart_axis.column_y_axis' },
          { key: '3-3', label: "auays:custom_chart_axis.row_y_axis" },
        ], false)
      },
    ],
    chartOptions: {
      isTransverse: true,
      series: (s: any, width: number, height: number, index: number, data: any) => {
        const isUpdateLast = data?.hasLine && index === data?.series?.length - 1
        const cmpBarWidth = (width - 100) / s.data.length
        const barWidth = cmpBarWidth > 80 ? 80 : cmpBarWidth
        return {
          type: isUpdateLast ? 'line' : 'bar',
          yAxisIndex: isUpdateLast ? 1 : 0,
          barWidth: isUpdateLast ? undefined : barWidth,
          stack: isUpdateLast ? undefined : 'LineAndStackedColumn',
        }
      }
    }
  },
  'LINE_AND_CLUSTERED_COLUMN_CHART': {
    name: "auays:custom_chart_type.line_and_clustered_column_chart",
    dealType: 3,
    elements: [
      {
        name: "auays:custom_chart_axis.x_axis", field: 'x', required: true, allowMultiple: true, theme: getThemeByCategory['blue'],
        opeMenu: () => commonOperations([
          { key: '3-1', label: 'auays:custom_chart_axis.column_y_axis' },
          { key: '3-2', label: "auays:custom_chart_axis.row_y_axis" },
          { key: '3-3', label: "auays:custom_chart_axis.column_legend" }
        ], false)
      },
      {
        name: 'auays:custom_chart_axis.column_y_axis', field: 'y', required: true, allowMultiple: false, theme: getThemeByCategory['green'],
        isNumerical: true,
        opeMenu: (isNumber: boolean = false) => {
          const arr1 = commonOperations([
            { key: '3-1', label: "auays:custom_chart_axis.x_axis" },
            { key: '3-2', label: "auays:custom_chart_axis.row_y_axis" },
            { key: '3-3', label: "auays:custom_chart_axis.column_legend" }
          ], false)
          const arr2 = mutexOperations(isNumber)
          return [...arr1, ...arr2]
        }
      },
      {
        name: "auays:custom_chart_axis.row_y_axis", field: 'assistY', required: false, allowMultiple: false, theme: getThemeByCategory['green'],
        isNumerical: true,
        opeMenu: (isNumber: boolean = false) => {
          const arr1 = commonOperations([
            { key: '3-1', label: "auays:custom_chart_axis.x_axis" },
            { key: '3-2', label: 'auays:custom_chart_axis.column_y_axis' },
            { key: '3-3', label: "auays:custom_chart_axis.column_legend" }
          ], false)
          const arr2 = mutexOperations(isNumber)
          return [...arr1, ...arr2]
        }
      },
      {
        name: "auays:custom_chart_axis.column_legend", field: 'title', required: false, allowMultiple: false, theme: getThemeByCategory['grey'],
        opeMenu: () => commonOperations([
          { key: '3-1', label: "auays:custom_chart_axis.x_axis" },
          { key: '3-2', label: 'auays:custom_chart_axis.column_y_axis' },
          { key: '3-3', label: "auays:custom_chart_axis.row_y_axis" },
        ], false)
      },
    ],
    chartOptions: {
      isTransverse: true,
      series: (s: any, width: number, height: number, index: number, data: any) => {
        const isUpdateLast = data?.hasLine && index === data?.series?.length - 1
        return {
          type: isUpdateLast ? 'line' : 'bar',
          yAxisIndex: isUpdateLast ? 1 : 0
        }
      }
    }
  },
  'RIBBON_DIAGRAM': {
    name: "auays:custom_chart_type.ribbon_chart",
    dealType: 1,
    elements: [
      {
        name: "auays:custom_chart_axis.x_axis", field: 'x', required: true, allowMultiple: true, theme: getThemeByCategory['blue'],
        opeMenu: () => commonOperations([
          { key: '3-1', label: "auays:custom_chart_axis.y_axis" },
          { key: '3-2', label: "auays:custom_chart_axis.legend" }
        ], true)
      },
      {
        name: "auays:custom_chart_axis.y_axis", field: 'y', required: true, allowMultiple: false, theme: getThemeByCategory['green'],
        isNumerical: true,
        opeMenu: (isNumber: boolean = false) => {
          const arr1 = commonOperations([
            { key: '3-1', label: "auays:custom_chart_axis.x_axis" },
            { key: '3-2', label: "auays:custom_chart_axis.legend" }
          ], true)
          const arr2 = mutexOperations(isNumber)
          return [...arr1, ...arr2]
        }
      },
      {
        name: "auays:custom_chart_axis.legend", field: 'title', required: false, allowMultiple: false, theme: getThemeByCategory['grey'],
        opeMenu: () => commonOperations([
          { key: '3-1', label: "auays:custom_chart_axis.x_axis" },
          { key: '3-2', label: "auays:custom_chart_axis.y_axis" }
        ], false)
      },
    ]
  },
  'WATERFALL_CHART': {
    name: "auays:custom_chart_type.waterfall_chart",
    dealType: 1,
    elements: [
      {
        name: "auays:custom_chart_axis.x_axis", field: 'x', required: true, allowMultiple: true, theme: getThemeByCategory['blue'],
        opeMenu: () => commonOperations([
          { key: '3-1', label: "auays:custom_chart_axis.y_axis" },
          { key: '3-2', label: "auays:custom_chart_axis.legend" }
        ], true)
      },
      {
        name: "auays:custom_chart_axis.y_axis", field: 'y', required: true, allowMultiple: false, theme: getThemeByCategory['green'],
        isNumerical: true,
        opeMenu: (isNumber: boolean = false) => {
          const arr1 = commonOperations([
            { key: '3-1', label: "auays:custom_chart_axis.x_axis" },
            { key: '3-2', label: "auays:custom_chart_axis.legend" }
          ], true)
          const arr2 = mutexOperations(isNumber)
          return [...arr1, ...arr2]
        }
      },
      {
        name: "auays:custom_chart_axis.legend", field: 'title', required: false, allowMultiple: false, theme: getThemeByCategory['grey'],
        opeMenu: () => commonOperations([
          { key: '3-1', label: "auays:custom_chart_axis.x_axis" },
          { key: '3-2', label: "auays:custom_chart_axis.y_axis" }
        ], false)
      },
    ]
  },
  'PIE_CHART': {
    name: "auays:custom_chart_type.pie_chart",
    dealType: 1,
    elements: [
      {
        name: "auays:custom_chart_axis.legend", field: 'x', required: true, allowMultiple: true, theme: getThemeByCategory['blue'],
        opeMenu: () => commonOperations([
          { key: '3-1', label: "auays:custom_chart_axis.value" },
        ], true)
      },
      {
        name: "auays:custom_chart_axis.value", field: 'y', required: true, allowMultiple: false, theme: getThemeByCategory['green'],
        isNumerical: true,
        opeMenu: (isNumber: boolean = false) => {
          const arr1 = commonOperations([
            { key: '3-1', label: "auays:custom_chart_axis.legend" },
          ], true)
          const arr2 = mutexOperations(isNumber).slice(0, -1)
          return [...arr1, ...arr2]
        }
      },
    ]
  },
  'DONUT_CHART': {
    name: "auays:custom_chart_type.doughnut_chart",
    dealType: 1,
    elements: [
      {
        name: "auays:custom_chart_axis.legend", field: 'x', required: true, allowMultiple: true, theme: getThemeByCategory['blue'],
        opeMenu: () => commonOperations([
          { key: '3-1', label: "auays:custom_chart_axis.value" },
        ], true)
      },
      {
        name: "auays:custom_chart_axis.value", field: 'y', required: true, allowMultiple: false, theme: getThemeByCategory['green'],
        isNumerical: true,
        opeMenu: (isNumber: boolean = false) => {
          const arr1 = commonOperations([
            { key: '3-1', label: "auays:custom_chart_axis.legend" },
          ], true)
          const arr2 = mutexOperations(isNumber).slice(0, -1)
          return [...arr1, ...arr2]
        }
      },
    ],
    chartOptions: {
      series: () => ({
        radius: ['40%', '80%'],
      })
    }
  },
  'TABLE': {
    name: "auays:custom_chart_type.table",
    dealType: 1,
    elements: [
      {
        name: "auays:custom_chart_axis.column", field: 'x', required: true, allowMultiple: true, theme: getThemeByCategory['blue'],
        opeMenu: (isNumber: boolean = false) => {
          const arr1 = commonOperations([
            { key: '3-1', label: "auays:ope_lbl.up" },
            { key: '3-2', label: "auays:ope_lbl.down" },
            { key: '3-3', label: "auays:ope_lbl.to_top" },
            { key: '3-4', label: "auays:ope_lbl.to_bottom" },
          ], true)
          const arr2 = mutexOperations(isNumber)
          const arr3 = columnOperations()
          return [...arr1, ...arr2, ...arr3]
        }
      },
    ]
  },
  'MATRIX': {
    name: "auays:custom_chart_type.matrix",
    dealType: 4,
    elements: [
      {
        name: "auays:custom_chart_axis.row", field: 'x', required: true, allowMultiple: false, theme: getThemeByCategory['blue'],
        opeMenu: () => commonOperations([
          { key: '3-1', label: "auays:custom_chart_axis.column" },
          { key: '3-2', label: "auays:custom_chart_axis.value" }
        ], false)
      },
      {
        name: "auays:custom_chart_axis.column", field: 'y', required: true, allowMultiple: false, theme: getThemeByCategory['green'],
        isNumerical: true,
        opeMenu: (isNumber: boolean = false) => {
          const arr1 = commonOperations([
            { key: '3-1', label: "auays:custom_chart_axis.row" },
            { key: '3-2', label: "auays:custom_chart_axis.value" }
          ], false)
          const arr2 = mutexOperations(isNumber)
          return [...arr1, ...arr2]
        }
      }
    ]
  },
}

export const DELETE_ICON = <svg className="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" id="25602" width="14" height="14"><path d="M106.688 170.688a64 64 0 0 1 64-64h682.624a64 64 0 0 1 64 64v137.6a64 64 0 0 1-29.632 54.016l-204.992 130.432V896H597.312V481.024a64 64 0 0 1 29.632-53.952L832 296.576V192H192v104.576l204.992 130.496a64 64 0 0 1 29.696 53.952v308.288H341.312V492.8L136.32 362.304a64 64 0 0 1-29.632-54.016v-137.6z" fill="#0F244C" id="25603"></path></svg>

// 可以进行数值操作（求和、最大最小值、平均值、标准偏差）的字段类型
export const CAN_NUMERICAL_OPE_TYPE = ['byte', 'integer', 'long']

/**
 * 获取图表API
 *
 * @param chartType 图表类型
 * @returns 对应的API函数
 */
export const getChartApi = (chartType: string): (params: any) => Promise<any> => {
  switch (chartType) {
    case 'STACKED_COLUMN_CHART':
    case 'STACKED_BAR_CHART':
    case 'CLUSTERED_BAR_CHART':
    case 'CLUSTERED_COLUMN_CHART':
    case 'STACKED_PERCENTAGE_BAR_CHART':
    case 'STACKED_PERCENTAGE_COLUMN_CHART':
    case 'STACKED_AREA_CHART':
    case 'PERCENTILE_STACKED_AREA_CHART':
    case 'RIBBON_DIAGRAM':
    case 'WATERFALL_CHART':
    case 'PIE_CHART':
    case 'DONUT_CHART':
    case 'TABLE':
      return queryAuditMetricsXY;
    case 'LINE_CHART':
    case 'AREA_CHART':
      return queryAuditMetricsAssistantY;
    case 'LINE_AND_STACKED_COLUMN_CHART':
    case 'LINE_AND_CLUSTERED_COLUMN_CHART':
      return queryAuditMetricsColumnY
    default:
      return queryAuditMetricsMatrix
  }
}