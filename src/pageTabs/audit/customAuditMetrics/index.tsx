import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Layout, <PERSON><PERSON>, <PERSON>, message } from "antd"
import classNames from "classnames";
import React, { useEffect, useState } from "react"
import styles from './index.module.scss'
import { VisualObjType } from "./components/VisualObjType";
import { useCustomAuditContext } from "src/hook/useCustomAuditMetrice";
import { CustomAuditContext } from "./CustomAuditContext";
import { AuditDataTree } from "./components/AuditDataTree";
import { FilterAndElements } from "./components/FilterAndElements";
import { DndProvider } from "react-dnd";
import { HTML5Backend } from "react-dnd-html5-backend";
import ChartCard from "./components/ChartCard";
import { isEmpty } from "lodash";
import { RenderTourStepContent, STEP_OPTION } from "src/components";
import { useDispatch, useRequest } from "src/hook";
import { setGuideSteps, setGuideVisible } from "src/pageTabs/SceneGuideModal/guideSlice";
import { getAllAuditMetrics, getIsBeginnerGuidance } from "src/api";
import { ExclamationCircleOutlined } from "@ant-design/icons";
import { ChartTabs } from "./components/ChartTabs";
import { CustomSaveModal } from "../components/CustomSaveModal";
import CustomMetricsDrawer from "../components/CustomMetricsDrawer";
import { checkAllowSave } from "./utils";
import { useTranslation } from "react-i18next";
import { 
  setOverviewPageState,
  setOverviewPageDetailParams 
} from 'src/pageTabs/audit/overview/overviewSlice'
const { Header, Content } = Layout
const { confirm } = Modal;

export const CustomAuditMetrics = () => {
  const defaultContext = useCustomAuditContext()
  const { tabList, chartInfo, eleNodes, saveChart, deleteChart, initChart, baseData } = defaultContext
  const [drawerVisible, setDrawerVisible] = useState<boolean>(false)
  const [saveVisible, setSaveVisible] = useState<boolean>(false) //保存弹窗
  const dispatch = useDispatch()
  const { t } = useTranslation()

  const btnClick = () => {
    const checkRes = checkAllowSave(eleNodes, chartInfo?.chartType)
    if (!checkRes) message.warn(t("auays:msg.chart_creation_requirement_not_met"))
    else setSaveVisible(true)
  }

  // 用户是否首次进入审计漫游导航
  const { data: isShowGuidance } = useRequest(getIsBeginnerGuidance, { manual: false })

  // 获取所有审计指标 custom: false -- 智能模式
  const { data: metricsData = [], loading, run } = useRequest(getAllAuditMetrics, { manual: true })

  // 新手引导
  const steps = [
    {
      target: '.custom_audit_metrics_first',
      content: RenderTourStepContent({
        title: t("auays:guide_title.visual_object_type_area"),
        position: '1/4',
        detail: t("auays:guide_detail.visual_object_type_area")
      }),
      ...STEP_OPTION,
      placement: 'right-start'
    },
    {
      target: '.custom_audit_metrics_second',
      content: RenderTourStepContent({
        title: t("auays:guide_title.data_area"),
        position: '2/4',
        detail: t("auays:guide_detail.data_area")
      }),
      ...STEP_OPTION,
      placement: 'right-start',
      hideBackButton: true,
    },
    {
      target: '.custom_audit_metrics_third',
      content: RenderTourStepContent({
        title: t("auays:guide_title.configuration_area"),
        position: '3/4',
        detail: t("auays:guide_detail.configuration_area")
      }),
      hideBackButton: true,
      ...STEP_OPTION,
      placement: 'left-start'
    },
    {
      target: '.custom_audit_metrics_forth',
      content: RenderTourStepContent({
        title: t("auays:guide_title.visual_display_area"),
        position: '4/4',
        detail: t("auays:guide_detail.visual_display_area")
      }),
      hideBackButton: true,
      ...STEP_OPTION,
      placement: 'left-start'
    },
  ];

  useEffect(() => {
    if (isShowGuidance) {
      dispatch(setGuideSteps({ steps, guideUniqueId: 'CUSTOM_AUDIT_METRICS' }));
      dispatch(setGuideVisible(true));
    }
  }, [isShowGuidance])

  // 渲染审计概览
  const gotoAuditView = () => {
    dispatch(setOverviewPageState(''))
    dispatch(setOverviewPageDetailParams({}))
  }

  const showJumpConfirm = () => {
    let isHaveNotSaved: boolean = false
    if (String(chartInfo?.id).startsWith('new_') && !isEmpty(baseData)) {
      isHaveNotSaved = true
    }
    // 判断有无未保存的图表
    tabList.map((tab: any) => {
      if (
        (String(tab.tabId).startsWith('new_') && tab.isCreatCharts) ||
        (!String(tab.tabId).startsWith('new_') && !tab.isNoChange)
      ) {
        isHaveNotSaved = true
      }
    })
    if (isHaveNotSaved) {
      confirm({
        title: t("auays:md_title.un_saved_metrics"),
        icon: <ExclamationCircleOutlined />,
        content: t("auays:md_content.confirm_warning"),
        width: 450,
        onOk() {
          gotoAuditView()
        }
      });
    }
    else {
      gotoAuditView()
    }
  };

  // 渲染审计分析次-审计概览-自定义审计指标SQL模式
  const gotoCustomAuditMetricsSql = () => {
    dispatch(setOverviewPageState('custom_audit_metrics_sql'))
    dispatch(setOverviewPageDetailParams({}))
  }

  // 切换生成模式
  const onModeChange = () => {
    gotoCustomAuditMetricsSql()
  }

  return (
    <Layout className="cq-container">
      <CustomAuditContext.Provider value={defaultContext}>
        <Header className="breadcrumb-header" title={t("auays:bc_title.custom_audit_metrics_smart_mode")}>
          <Breadcrumb className="breadcrumb" separator=''>
            <Breadcrumb.Item>{t("auays:bc_title.audit_analysis")}</Breadcrumb.Item>
            <Breadcrumb.Separator>|</Breadcrumb.Separator>
            <Breadcrumb.Item>
              <div onClick={() => showJumpConfirm()} style={{ cursor: 'pointer', display: 'inline-block' }} role="button">
                {t("auays:bc_title.audit_overview")}
              </div>
            </Breadcrumb.Item>
            <Breadcrumb.Separator>/</Breadcrumb.Separator>
            <Breadcrumb.Item>{t("auays:bc_title.custom_audit_metrics_smart_mode")}</Breadcrumb.Item>
            <Button className={styles.modeBtn} onClick={onModeChange} title={t("auays:btn.switch_to_sql_mode")}>{t("auays:btn.switch_to_sql_mode")}</Button>
          </Breadcrumb>
          <div className={styles.chartBtns}>
            <Button className={styles.openBtn} onClick={() => { run({ custom: false }); setDrawerVisible(true) }}>{t("auays:btn.open")}</Button>
            <Button type="primary" onClick={btnClick}>{t("auays:btn.save")}</Button>
          </div>
        </Header>
        <Layout className={classNames('cq-main', { [styles.customAudit]: true })}>
          <Content className='cq-content' >
            <DndProvider backend={HTML5Backend}>
              <Row>
                <Col span={4} className={styles.leftCol}>
                  {/* 视觉对象类型 */}
                  <VisualObjType />
                  {/* 审计数据树--全部数据 */}
                  <AuditDataTree />
                </Col>
                <Col span={20} className={styles.rightCol}>
                  {/* 事前筛选和图表要素--右上区域 */}
                  <FilterAndElements />
                  {/* 图表--右下区域 */}
                  <ChartCard />
                  {/* 图表tab */}
                  <ChartTabs />
                </Col>
              </Row>
            </DndProvider>
            {/* 打开 -- 抽屉 */}
            <CustomMetricsDrawer
              visible={drawerVisible}
              setVisible={setDrawerVisible}
              loading={loading}
              defaultList={metricsData}
              delFun={async (id: number | string) => {
                await deleteChart(id)
                run({ custom: false })
              }}
              initFun={initChart}
            />
          </Content>
        </Layout>
        {/* 保存时弹窗 */}
        <CustomSaveModal
          defaultValues={{
            name: chartInfo?.chartName,
            remark: chartInfo?.chartRemark,
          }}
          visible={saveVisible}
          onCancel={() => setSaveVisible(false)}
          onFinishOpe={(values: any) => saveChart(chartInfo.id, values)}
        />
      </CustomAuditContext.Provider>
    </Layout>
  )
}