import { isEmpty, isEqual, uniq } from "lodash"
import { CUSTOM_AUDIT_CHART_ELEMENT } from "./constants"
interface ISeries {
  data: string[],
  name: string
}
interface IxAxis {
  noNumberAxis: any[]
  series: ISeries[]
  tooltips: string[]
}

/**
 * 生成图表选项列表
 *
 * @param data 数据列表
 * @param xFieldList x轴字段列表
 * @param tField 类别字段
 * @param seriesName 系列名称（可选）
 * @returns 返回 IxAxis 类型的数据，包含 noNumberAxis（无数字轴）、series（系列数据）和 tooltips（提示信息）
 */
export const chartOptionsListGenerator = (data: any[], xFieldList: any[], tField: string, seriesName?: string): IxAxis => {
  if (!seriesName) seriesName = 'amount'
  let tFieldList: string[] = []
  if (tField === 'amount') {
    tFieldList = ['amount']
  }
  else {
    tFieldList = uniq(data.map((item: any) => item[tField] ?? '-'))
  }
  // 分类轴只有一个字段
  if (xFieldList.length === 1) {
    const xField = xFieldList[0]
    const nData = uniq(data?.map((item: any) => item[xField] ?? '-'))
    const xAxisData = nData.map((item: string) => { return String(item)?.replaceAll('\n', ' ')?.replaceAll('\r', ' ') })
    const noNumberAxis = [{
      data: xAxisData,
    }]
    let tFieldData: any[] = []
    tFieldList.map((field: string) => {
      const newData = nData.map((item: any) => {
        const value = data.filter((iData: any) => iData[xField] === item && (iData[tField] === field || tField === 'amount'))?.[0]?.amount
        return value || 0
      })
      const newfield = String(field)?.replaceAll('\n', ' ')?.replaceAll('\r', ' ')
      tFieldData.push({
        name: tField !== 'amount' ? (newfield || '-') : seriesName,
        data: newData
      })
    })
    return {
      noNumberAxis,
      series: tFieldData,
      tooltips: xAxisData,
    }
  }
  // 分类轴有多个字段
  else {
    xFieldList.reverse()
    let dataList: any[] = []
    let xFieldObjs = data.map((item: any) => {
      let newItems: any = {}
      Object.keys(item).map((key: string) => {
        if (xFieldList.includes(key)) {
          newItems[key] = item[key]
        }
      })
      return newItems
    })
    xFieldObjs = Array.from(new Set(xFieldObjs.map(item => JSON.stringify(item)))).map(item => JSON.parse(item));
    xFieldList.map((field: string) => {
      const ls = xFieldObjs.map((item: any) => item[field] ?? '-')
      dataList.push(ls)
    })
    const tooltipList = xFieldObjs.map((x: any) => {
      const values = Object.values(x).map((item: any) => {
        return String(item)?.replaceAll('\n', ' ')?.replaceAll('\r', '')
      })
      return values.join(' - ')
    })
    let tFieldData: any[] = []
    tFieldList.map((field: string) => {
      const newData = xFieldObjs.map((item: any) => {
        const value = data.filter((iData: any) => containsObject(iData, item) && (iData[tField] === field || tField === 'amount'))?.[0]?.amount
        return value || 0
      })
      const newfield = String(field)?.replaceAll('\n', ' ')?.replaceAll('\r', ' ')
      tFieldData.push({
        name: tField !== 'amount' ? (newfield || '-') : seriesName,
        data: newData
      })
    })
    const noNumberAxis = xFieldList.map((item: string, index: number) => {
      const xAxisData = dataList[index].map((item: string) => { return String(item)?.replaceAll('\n', '')?.replaceAll('\r', '') })
      return {
        data: xAxisData,
      }
    })
    return {
      noNumberAxis,
      series: tFieldData,
      tooltips: tooltipList
    }
  }
}



export const chartSeriesListGeneratorForFY = (data: any[], xFieldList: any[], yField: string, fyField: string, yName: string[]) => {
  const tFieldList = [yField, fyField]
  xFieldList.reverse()
  const tooltipList = data.map((item: any) => {
    let newItems: any[] = []
    Object.keys(item).map((key: string) => {
      if (xFieldList.includes(key)) {
        newItems.push(item[key])
      }
    })
    newItems = newItems.map((i: any) => String(i)?.replaceAll('\n', '')?.replaceAll('\r', ''))
    return newItems.join(' - ')
  })
  let tFieldData: any[] = []
  tFieldList.map((field: string, index: number) => {
    tFieldData.push({
      name: yName[index],
      data: data.map((item: any) => item[field])
    })
  })
  return { series: tFieldData, tooltips: tooltipList }
}

/**
 * 判断一个对象是否包含另一个对象
 *
 * @param obj 目标对象
 * @param subObj 子对象
 * @returns 如果目标对象包含子对象，则返回true，否则返回false
 */
// 
export const containsObject = (obj: any, subObj: any) => {
  for (const key in subObj) {
    if (subObj.hasOwnProperty(key) && obj.hasOwnProperty(key)) {
      if (String(subObj[key]) !== String(obj[key])) {
        return false;
      }
    } else {
      return false;
    }
  }
  return true;
}

/**
 * 将数字或字符串转为百分比格式并保留两位小数
 *
 * @param num 要转换的数字或字符串
 * @returns 转换后的百分比数字
 */
export const decimalToPercentage = (num: number | string): number => {
  const percentage = Math.round(Number(num) * 10000) / 100
  return percentage
}

/**
 * 将瀑布图数据拆分为两部分：透明柱状图数据和顶部总和数据
 *
 * @param data 瀑布图数据数组
 * @returns 返回包含透明柱状图数据和顶部总和数据的数组
 */
export const waterfallDataSplit = (data: number[]) => {
  const total = data.reduce((previousValue: number, currentValue: number) => previousValue + currentValue, 0)
  const tData = [...data]
  const transparentData = tData.map((item: number, index: number) => {
    const preValue = tData.filter((_, i: number) => i < index).reduce((previousValue: number, currentValue: number) => previousValue + currentValue, 0)
    return preValue + item
  })
  transparentData.pop()
  transparentData.unshift(0)
  transparentData.push(0)
  const topData = [...data, total]
  return [
    transparentData,
    topData
  ]
}

/**
 * 处理图表数据XY轴
 *
 * @param elements 图表元素数组
 * @param eleNodes 元素对应的节点数组
 * @param data 图表数据
 * @returns 处理后的图表数据对象
 */
export const dealChartDataXY = (elements: any[], eleNodes: any, data: any): any => {
  let xField: string[] = []
  let xName: string = ''
  let yName: string = ''
  let tField: string = 'amount'
  let tName: string = ''
  elements.map((ele: any, index: number) => {
    if (eleNodes[index] && eleNodes[index].length > 0) {
      if (!ele.isNumerical && ele.required) {
        xField = eleNodes[index]?.map((node: any) => node.key)
        xName = eleNodes[index]?.map((node: any) => node.title).join('-')
      }
      else if (ele.isNumerical && ele.required) {
        yName = eleNodes[index]?.map((node: any) => node.title).join('-')
      }
      else if (!ele.isNumerical && !ele.required) {
        tField = eleNodes[index]?.map((node: any) => node.key)?.[0]
        tName = eleNodes[index]?.map((node: any) => node.title).join('-')
      }
    }
  })
  const { noNumberAxis, series, tooltips } = chartOptionsListGenerator(data, xField, tField, yName)
  return { noNumber: noNumberAxis, series, tooltips, xName, yName: [yName], tName }
}

/**
 * 处理图表数据行Y列Y的行Y数据
 *
 * @param elements 图表元素数组
 * @param eleNodes 元素对应的节点数组
 * @param data 图表数据
 * @returns 处理后的图表数据对象
 */
export const dealChartDataHY = (elements: any[], eleNodes: any, data: any): any => {
  let xField: string[] = []
  let hyName: string = ''
  let tField: string = 'amount'
  elements.map((ele: any, index: number) => {
    if (eleNodes[index] && eleNodes[index].length > 0) {
      if (!ele.isNumerical && ele.required) {
        xField = eleNodes[index]?.map((node: any) => node.key)
      }
      else if (ele.isNumerical && !ele.required) {
        hyName = eleNodes[index]?.map((node: any) => node.title).join('-')
      }
    }
  })
  const { series } = chartOptionsListGenerator(data, xField, tField, hyName)
  return { seriesH: series, hyName }
}

// 事后过滤fun
export const filterConditionsFun = (actionType: string, subValue: any, value: any): boolean => {
  if (actionType === '1') { //包含
    return value.indexOf(subValue) !== -1
  }
  else if (actionType === '2') { //不包含
    return value.indexOf(subValue) === -1
  }
  else if (actionType === '3') { //开头是
    return value.indexOf(subValue) === 0
  }
  else if (actionType === '4') { //开头不是
    return value.indexOf(subValue) !== 0
  }
  else if (actionType === '5') { //等于
    return value === subValue
  }
  else { //不等于
    return value !== subValue
  }
}

/**
 * 计算百分比图表的数据
 *
 * @param data 图表数据源
 * @returns 转换后的百分比图表数据
 */
export const percentChartData = (data: any): any => {
  if (data?.series?.length > 0) {
    const { series } = data
    const seriesData = series.map((s: any) => s?.data)
    const totalData = seriesData[0].map((_: any, index: number) => {
      return seriesData.reduce((pre: number, cur: any[]) => {
        // 先判断强制转换为数字，是否为NAN,如果为NAN则返回0
        if (isNaN(cur[index])) return pre;
        else return pre + Number(cur[index])
      }, 0)
    })
    // 现在totalData包含了seriesData每一列的总和
    const newSeries = series?.map((s: any) => ({
      ...s,
      data: s.data.map((num: number, index: number) => {
        if (totalData[index] <= 0 || isNaN(num)) return 0
        else return Number(num) / totalData[index]
      }),
      numData: s.data
    }))
    return { ...data, series: newSeries }
  }
  else return {}
}

/**
 * 合并标准数据和辅助数据
 *
 * @param standard 标准数据数组
 * @param assistant 辅助数据数组
 * @returns 合并后的数据数组
 */
export const mergeStandardAndAssistant = (standard: any[], assistant: any[]): any[] => {
  const assistantXfield: any[] = assistant?.map((item: any) => {
    const newItem = { ...item }
    delete newItem.amount
    return newItem
  }) || []
  const standardXfield: any[] = standard?.map((item: any) => {
    const newItem = { ...item }
    delete newItem.amount
    return newItem
  }) || []

  const xFieldMap = [...assistantXfield, ...standardXfield]
  const xFieldNoRepeat = xFieldMap.reduce((acc, curr) => {
    if (!acc.find((item: any) => isEqual(item, curr))) {
      acc.push(curr);
    }
    return acc;
  }, []);

  const newData: any[] = []
  xFieldNoRepeat.map((xAxis: any) => {
    let dataItem: any = {}
    const amount = standard.filter((item: any) => containsObject(item, xAxis))?.[0]?.amount
    const amountF = assistant.filter((item: any) => containsObject(item, xAxis))?.[0]?.amount
    dataItem = { ...xAxis, amount, amountF }
    newData.push(dataItem)
  })
  return newData
}

/**
 * 事后过滤操作函数
 *
 * @param baseData 原始数据
 * @param node 节点数据
 * @returns 过滤后的数据
 */
export const postFilterOpe = (data: any, node: any): any => {
  const { fieldData, filter } = node
  const field = !node.action ? node.key : 'amount'
  // 三种过滤情况
  if (!isEmpty(filter) && fieldData?.length > 0) {
    const { tabType, topN, values, advancedFilter = {} } = filter
    if (tabType === 'TOPN') { // 前n
      const length = fieldData?.length || 0
      const end = topN <= length ? topN : length
      const usefulValues = fieldData?.slice(0, end)
      for (const key in data) {
        data[key] = data[key].filter((item: any) => usefulValues.includes(field === 'amount' ? Number(item[field]) : String(item[field])))
      }
    }
    else if (tabType === 'BASIC') { // 基础筛选
      for (const key in data) {
        data[key] = data[key].filter((item: any) => values.includes(String(item[field])))
      }
    }
    else { // 高级筛选
      const { firstAction = '', firstValue = '', lastAction = '', lastValue = '', logicalAction } = advancedFilter
      if (firstAction && firstValue && lastAction && lastValue) {
        for (const key in data) {
          data[key] = data[key].filter((item: any) => {
            if (logicalAction === 1) { // 且
              return filterConditionsFun(firstAction, String(firstValue), String(item[field])) && filterConditionsFun(lastAction, String(lastValue), String(item[field]))
            }
            else { //或
              return filterConditionsFun(firstAction, String(firstValue), String(item[field])) || filterConditionsFun(lastAction, String(lastValue), String(item[field]))
            }
          })
        }
      }
      else if (firstAction && firstValue) {
        for (const key in data) {
          data[key] = data[key].filter((item: any) => {
            return filterConditionsFun(firstAction, String(firstValue), String(item[field]))
          })
        }
      }
      else if (lastAction && lastValue) {
        for (const key in data) {
          data[key] = data[key].filter((item: any) => {
            return filterConditionsFun(lastAction, String(lastValue), String(item[field]))
          })
        }
      }
    }
  }
  // 取消过滤操作
  else {
    for (const key in data) {
      data[key] = data[key]?.filter((item: any) => fieldData?.includes(field === 'amount' ? Number(item[field]) : (item[field] || '-')))
    }
  }
  return data
}

/**
 * 对矩阵数据进行过滤操作
 *
 * @param data 矩阵数据
 * @param node 节点信息，包含过滤条件、字段数据等
 * @returns 过滤后的矩阵数据
 */
export const postFilterOpeForMatrix = (data: any, node: any): any => {
  const { fieldData, filter, type } = node
  // 三种过滤情况
  if (!isEmpty(filter) && fieldData?.length > 0) {
    const { tabType, topN, values, advancedFilter = {} } = filter
    if (tabType === 'TOPN') { // 前n
      const length = fieldData?.length || 0
      const end = topN <= length ? topN : length
      const usefulValues = fieldData?.slice(0, end)
      const newData: any = {}
      if (type === 'x') {
        for (const key in data) {
          if (usefulValues.includes(String(key))) {
            newData[key] = data[key]
          }
        }
      }
      else {
        for (const key in data) {
          const obj = data[key]
          const valuesObj: any = {}
          for (const valueKey in obj) {
            if (usefulValues.includes(String(valueKey))) {
              valuesObj[valueKey] = obj[valueKey]
            }
          }
          newData[key] = valuesObj
        }
      }
      data = { ...newData }
    }
    else if (tabType === 'BASIC') { // 基础筛选
      const newData: any = {}
      if (type === 'x') {
        for (const key in data) {
          if (values.includes(String(key))) {
            newData[key] = data[key]
          }
        }
      }
      else {
        for (const key in data) {
          const obj = data[key]
          const valuesObj: any = {}
          for (const valueKey in obj) {
            if (values.includes(String(valueKey))) {
              valuesObj[valueKey] = obj[valueKey]
            }
          }
          newData[key] = valuesObj
        }
      }
      data = { ...newData }
    }
    else { // 高级筛选
      const { firstAction = '', firstValue = '', lastAction = '', lastValue = '', logicalAction } = advancedFilter
      const newData: any = {}
      if (firstAction && firstValue && lastAction && lastValue) {
        if (type === 'x') {
          for (const key in data) {
            const conditionOne = logicalAction === 1 && (filterConditionsFun(firstAction, String(firstValue), String(key)) && filterConditionsFun(lastAction, String(lastValue), String(key)))
            const conditionTwo = logicalAction === 2 && (filterConditionsFun(firstAction, String(firstValue), String(key)) || filterConditionsFun(lastAction, String(lastValue), String(key)))
            if (conditionOne || conditionTwo) {
              newData[key] = data[key]
            }
          }
        }
        else {
          for (const key in data) {
            const obj = data[key]
            const valuesObj: any = {}
            for (const valueKey in obj) {
              const conditionOne = logicalAction === 1 && (filterConditionsFun(firstAction, String(firstValue), String(valueKey)) && filterConditionsFun(lastAction, String(lastValue), String(valueKey)))
              const conditionTwo = logicalAction === 2 && (filterConditionsFun(firstAction, String(firstValue), String(valueKey)) || filterConditionsFun(lastAction, String(lastValue), String(valueKey)))
              if (conditionOne || conditionTwo) {
                valuesObj[valueKey] = obj[valueKey]
              }
            }
            newData[key] = valuesObj
          }
        }
        data = { ...newData }
      }
      else if (firstAction && firstValue) {
        if (type === 'x') {
          for (const key in data) {
            if (filterConditionsFun(firstAction, String(firstValue), String(key))) {
              newData[key] = data[key]
            }
          }
        }
        else {
          for (const key in data) {
            const obj = data[key]
            const valuesObj: any = {}
            for (const valueKey in obj) {
              if (filterConditionsFun(firstAction, String(firstValue), String(valueKey))) {
                valuesObj[valueKey] = obj[valueKey]
              }
            }
          }
        }
        data = { ...newData }
      }
      else if (lastAction && lastValue) {
        if (type === 'x') {
          for (const key in data) {
            if (filterConditionsFun(lastAction, String(lastValue), String(key))) {
              newData[key] = data[key]
            }
          }
        }
        else {
          for (const key in data) {
            const obj = data[key]
            const valuesObj: any = {}
            for (const valueKey in obj) {
              if (filterConditionsFun(lastAction, String(lastValue), String(key))) {
                valuesObj[valueKey] = obj[valueKey]
              }
            }
          }
        }
        data = { ...newData }
      }
    }
  }
  // 取消过滤操作
  else {
    const newData: any = {}
    if (node.type === 'x') {
      for (const key in data) {
        if (node?.fieldData?.includes(String(key) || '-')) {
          newData[key] = data[key]
        }
      }
    }
    else {
      for (const key in data) {
        const obj = data[key]
        const valuesObj: any = {}
        for (const valueKey in obj) {
          if (node?.fieldData?.includes(String(valueKey) || '-')) {
            valuesObj[valueKey] = obj[valueKey]
          }
        }
        newData[key] = valuesObj
      }
    }
    data = { ...newData }
  }
  return data
}

// 校验图表要素必填项是否都已拖拽绑定，否则不允许保存
export const checkAllowSave = (ele: any[], type: string) => {
  const eleValues = Object.values(ele)
  const { elements } = CUSTOM_AUDIT_CHART_ELEMENT[type]
  // 图表要素中必填项的名称
  const requiredNames = elements.filter((item: any) => item?.required).map((item: any) => item.name)
  // 当前已拖拽绑定的图表要素名称
  const curNames = eleValues?.filter((item: any[]) => item?.length).map((item: any[]) => item?.[0]?.source)
  const result = requiredNames.every((item: string) => curNames?.includes(item))
  return result
}

// 计算图表要素label宽度
export const calculateWidth = (locales: string, chartType: string, field: string) => {
  const baseAxisWidth = locales === 'en' ? 71 : 54 // X轴和Y轴在不同语言下的宽度
  const baseLegendWidth = locales === 'en' ? 70 : 58 // 图例
  const assistantYAxisWidth = locales === 'en' ? 132 : 81 // 辅助Y轴
  const columnYAxisWidth = locales === 'en' ? 127 : 67 // 列Y轴
  const rowYAxisWidth = locales === 'en' ? 103 : 67 // 行Y轴
  const columnLegendWidth = locales === 'en' ? 137 : 72 // 列图例
  const valueWidth = locales === 'en' ? 57 : 44 // 值
  const columnWidth = locales === 'en' ? 73 : 44 // 列
  const rowWidth = locales === 'en' ? 49 : 44 // 行
  switch (chartType) {
    case "AREA_CHART":
    case "LINE_CHART":
      switch (field) {
        case "assistY": return assistantYAxisWidth;
        case "title": return baseLegendWidth;
        default: return baseAxisWidth;
      }
    case "LINE_AND_STACKED_COLUMN_CHART":
    case "LINE_AND_CLUSTERED_COLUMN_CHART":
      switch (field) {
        case "y": return columnYAxisWidth;
        case "assistY": return rowYAxisWidth;
        case "title": return columnLegendWidth;
        default: return baseAxisWidth;
      }
    case "PIE_CHART":
    case "DONUT_CHART":
      switch (field) {
        case "x": return baseLegendWidth
        default: return valueWidth
      }
    case "TABLE":
      return columnWidth
    case "MATRIX":
      switch (field) {
        case "x": return rowWidth
        default: return columnWidth
      }
    default:
      switch (field) {
        case "title": return baseLegendWidth;
        default: return baseAxisWidth;
      }
  }
}