import React, { useEffect, useState } from 'react'
import { Checkbox } from 'antd'
import { DrawerWrapper } from 'src/components'
import {
  EXECUTION_COLUMN_FIELDS,
  DEFAULT_EXECUTE_LOG_COLUMNS,
} from 'src/constants'
import { useTranslation } from 'react-i18next'

interface IProps {
  defaultColumnFields: string[]
  visible: boolean
  onCancel: () => void
  onOk: (fields: string[]) => void
}

export const CustomColumnFieldPanel = ({
  defaultColumnFields,
  visible = false,
  onCancel,
  onOk,
}: IProps) => {
  const { t } = useTranslation()
  const [checkedColumnFields, setCheckedColumnFields] = useState<string[]>([])

  useEffect(() => {
    setCheckedColumnFields(defaultColumnFields)
  }, [defaultColumnFields])

  return (
    <DrawerWrapper
      title={t("auays:drawer_title.custom_list_items")}
      width={400}
      visible={visible}
      onCancel={() => {
        onCancel();
        setCheckedColumnFields(defaultColumnFields);
      }}
      onOk={() => onOk(checkedColumnFields)}
    >
      <Checkbox.Group
        defaultValue={defaultColumnFields}
        value={checkedColumnFields}
        //@ts-ignore
        onChange={(checkedValue: string[]) =>
          checkedValue && setCheckedColumnFields(checkedValue)
        }
      >
        {Object.keys(EXECUTION_COLUMN_FIELDS).map((key) => (
          <span key={key}>
            <Checkbox
              disabled={DEFAULT_EXECUTE_LOG_COLUMNS.includes(key)}
              value={key}
            >
              {t(EXECUTION_COLUMN_FIELDS[key])}
            </Checkbox>
            <div style={{ padding: '8px 0' }} />
          </span>
        ))}
      </Checkbox.Group>
    </DrawerWrapper>
  )
}
