import React, { forwardRef, useCallback, useEffect, useImperativeHandle, useMemo, useState } from 'react'
import {
  CaretDownOutlined,
  CaretRightOutlined,
  FolderOpenOutlined,
  InfoCircleOutlined,
} from '@ant-design/icons'
import * as _ from 'lodash'
import dayjs from 'dayjs'
import { useHistory, useLocation } from 'react-router-dom'
import { Table, Tag, Tooltip, Typography, Badge, message } from 'antd'
import {
  EXECUTION_COLUMN_FIELDS,
  DEFAULT_EXECUTE_LOG_COLUMNS,
  SQL_OPERATE_TYPES,
  DEFAULT_SQL_LOG_COLUMNS,
  SOURCE_FIELD_FILTERS,
} from 'src/constants'
import {
  getAndSetAuditColumn,
  findAuditExecutionLogs,
  findAuditExecutionLogs4Personal,
  AuditExecutionParams,
  AuditExecutionLogParams,
  DataSourceTypes,
  downloadSqlFile,
  downloadExecuteLog
} from 'src/api'
import { Iconfont } from 'src/components'
import { useSelector, useRequest } from 'src/hook'
import { SearchHeader } from './SearchHeader'
import { ExpandedRowContent } from './ExpandedRowContent'
import { CustomColumnFieldPanel } from './CustomColumnFieldPanel'
import styles from './index.module.scss'
import { handleDownload, renderErrorFlag } from 'src/util'
import { SizeType } from 'antd/lib/config-provider/SizeContext'
import { GloablSearchLocationState } from 'src/pageTabs/GlobalSearchModal/WorkOrderResult';
import { useTranslation } from 'react-i18next';

const transform = (query: AuditExecutionLogParams) =>
  _.omitBy(
    query,
    (value) =>
      value === null ||
      typeof value === 'undefined' ||
      (Array.isArray(value) && value.length === 0),
  )

const getNormalizedType = (uncheckedType: string) => {
  const normalizedType = DataSourceTypes.find(
    (type) => type.toUpperCase() === uncheckedType,
  )
  return normalizedType || 'Other'
}

interface IProps {
  isUnauthorized?: boolean | undefined,
  param?: any | undefined,

  // 执行历史引用
  isSqlLog?: boolean | undefined,
  scroll?: any,
  size?: SizeType,
  pagination?: any,
  getSourceFiltersVal?: (val: any) => void,
}

//当之前语句明细接口是审计延迟问题后，再次进入调用接口也是审计延迟 弹出提示框
let isDuplicatedAuditDelayedMsg = false;
export const CustomColumnTable = forwardRef((props: IProps, ref: any) => {
  const { t } = useTranslation()
  const {
    isUnauthorized,
    param,
    // 执行历史
    isSqlLog,
    getSourceFiltersVal,
  } = props;
  const history = useHistory()
  const location = useLocation();
  const { state = {} } = location as {state: GloablSearchLocationState}
  const { userId } = useSelector((state) => state.login.userInfo)
  const { locales } = useSelector(state => state.login)
  const { overviewPageDetailParams } = useSelector((state: any) => state.overview)
  const locationState = overviewPageDetailParams;

  const defaultTableParams = {
    limit: 10, //条数
    offset: 0, //起始条数
    executors: [],
    timeRange: [
      dayjs().subtract(3, 'day').startOf('d').valueOf(),
      dayjs().endOf('d').valueOf(),
    ],
    resultFlag: undefined,
    actuatorType: 0,
  }

  const sqlLogDefaultParams = { ...defaultTableParams, timeRange: undefined, executors: [userId || ''] };
  const realDefaultTableParams = isSqlLog ? sqlLogDefaultParams : defaultTableParams;

  const [isShowCustomColumnPanel, setIsShowCustomColumnPanel] = useState(false)
  const [sourceFiltersVal, setSourceFiltersVal] = useState<string[] | null | undefined>(null);

  const [tableParams, setTableParams] = useState<AuditExecutionLogParams>(realDefaultTableParams)
  const [columns, setColumns] = useState<string[]>([])

  const { run: getAndSetColumnFields } = useRequest(getAndSetAuditColumn, {
    manual: true,
    onSuccess: (data: AuditExecutionParams) => {
      const res = data?.columns ?? []
      setColumns(res)
      setIsShowCustomColumnPanel(false)
    },
  })

  // 执行历史：根据 searchHeader 更新 tableParams
  useEffect(() => {
    if (isSqlLog && Object.keys(param).length !== 0) {
      setTableParams({ ...sqlLogDefaultParams, ...param, executors: [userId || ''] })
    }
  }, [isSqlLog, param, userId])

  const {
    data: logs,
    loading,
    refresh,
  } = useRequest(
    () => {
      let defaultParams = transform(tableParams)
      if (!_.isEmpty(tableParams?.dbTypes) && Array.isArray(tableParams?.dbTypes) && tableParams?.dbTypes[0]) {
        defaultParams.dbTypes = tableParams.dbTypes.map((dataSourceType) => {
          return dataSourceType && dataSourceType.toUpperCase()
        }
        )
      }

      if (tableParams?.timeRange) {
        //@ts-ignore
        defaultParams.executeBeginMs = defaultParams.timeRange?.[0]
        //@ts-ignore
        defaultParams.executeEndMs = defaultParams.timeRange?.[1]
        delete defaultParams?.timeRange
      }

      //@ts-ignore
      return isSqlLog ? findAuditExecutionLogs4Personal({
        ...defaultParams,
        //@ts-ignore
        offset: defaultParams?.offset * defaultParams?.limit,
      }) : findAuditExecutionLogs({
        ...defaultParams,
        //@ts-ignore
        offset: defaultParams?.offset * defaultParams?.limit,
      })
    },
    {
      refreshDeps: [tableParams],
      debounceInterval: 300,
      onSuccess: (data) => {
        //业务告警消息通知查看审计结果 延迟问题，未查询出结果弹出提示框
        if(tableParams?.auditIds?.length && !data?.data?.length && !isDuplicatedAuditDelayedMsg) {
          message.info(t('auays:analysis_tip.data_delay'));
        }else {
          isDuplicatedAuditDelayedMsg = false;
        }
      },
    },
  )

  // 执行历史：使用 useImperativeHandle 公开函数给父组件使用
  useImperativeHandle(ref, () => ({
    refresh
  }));

  useEffect(() => {
    if (isUnauthorized) {
      let newParams = {...realDefaultTableParams, ...(param || {})};
      if (state?.globalSearchRecordPosition) {
        //处理分页 并选中
        const pageNum = Math.ceil(Number(state.globalSearchRecordPosition) / 10);
        newParams.offset =  pageNum - 1 
      }
      setTableParams({ ...newParams});
    }
  },[isUnauthorized, JSON.stringify(param)])

  // history 带过来的参数
  useEffect(() => {
  
    if (!isUnauthorized && !_.isEmpty(locationState)) {
   
      let newParams = {...realDefaultTableParams, ...(locationState || {})};
      if (state?.globalSearchRecordPosition) {
        //处理分页 并选中
        const pageNum = Math.ceil(Number(state.globalSearchRecordPosition) / 10);
        newParams.offset =  pageNum - 1;
        newParams.limit = newParams?.limit || 10;
        delete newParams?.globalSearchRecordPosition;
        delete newParams?.globalSearchSubTabKey;
        delete newParams?.globalSearchTabKey;
      }
    
      setTableParams({ ...newParams});
    }
  },[JSON.stringify(locationState)])

  useEffect(() => {
    userId &&
      getAndSetColumnFields({
        userId,
        type: 'EXECUTE',
      })
  }, [userId])
  
  //全局搜索
  const isSelectedRowIndex = useMemo(() => {
    if (!state?.globalSearchRecordPosition) {
      return null;
    }
    
    const pageNum = Math.ceil(Number(state.globalSearchRecordPosition) / 10);
    if (pageNum - 1 === tableParams?.offset && tableParams?.limit) {
      const itemIndexInPage = state.globalSearchRecordPosition % tableParams.limit;
      return itemIndexInPage === 0 ? tableParams.limit - 1 : itemIndexInPage - 1;
    }
  },[tableParams?.offset, state?.globalSearchRecordPosition, tableParams?.limit])

  const tryDownloadFile = useCallback((path: string) => {
    handleDownload({ href: `/export/download?path=${path}` })
  }, [])

  // 操作结果日志下载
  const handleDownloadResultLog = (checkPermissionId: string, executeSql: string) => {
    const params = {
      id: checkPermissionId,
      fileName: executeSql
    }
    downloadExecuteLog(params).then().catch((err: any) => {
      console.error(t("auays:clg.download_ope_res_log_failed"), err)
    })

  }

  const formatColumns = useMemo(() => {
    let currentColumns = !isSqlLog ? DEFAULT_EXECUTE_LOG_COLUMNS : DEFAULT_SQL_LOG_COLUMNS
    if (!isSqlLog && !_.isEmpty(columns)) {
      currentColumns = columns
    }
    //对后端返回的列做个排序
    const columnsSort = Object.keys(EXECUTION_COLUMN_FIELDS).filter((item) => currentColumns.includes(item))
    return columnsSort.map((key) => {
      let columnItem = {
        key,
        //@ts-ignore
        title: t(EXECUTION_COLUMN_FIELDS[key]),
        dataIndex: key,
        width: 100
      }
      if (key === 'deptName') { // 部门名
        //@ts-ignore
        columnItem.render = (_: any, record: any) => (
          <span style={{ color: '#3262FF', cursor: "pointer" }} onClick={() => {
            setTableParams({
              ...tableParams,
              offset: 0, //起始条数
              depts: [record?.deptName]
            })
          }}>
            {record?.deptName}
          </span>
        )
      }
      if (key === 'executor') { // 用户名
        //@ts-ignore
        columnItem.render = (_: any, record: any) => (
          <span style={{ color: '#3262FF', cursor: "pointer" }} onClick={() => {
            history.push({
              pathname: '/suser_detail',
              state: { userId: record?.executor }
            })
            // setTableParams({
            //   ...tableParams,
            //   offset: 0, //起始条数
            //   executors: [record?.executor]
            // })
          }}>
            {record?.executor}
          </span>
        )
      }
      if (key === 'connectionName') {
        //@ts-ignore
        columnItem.render = (_: any, record: any) => (
          <>
            <Iconfont
              type={
                'icon-connection-' +
                (getNormalizedType(record?.dbType) || 'Other')
              }
              className="mr8"
              style={{ fontSize: 14, opacity: 1 }}
            ></Iconfont>
            <span style={{ color: '#3262FF', cursor: "pointer" }} onClick={() => {
              setTableParams({
                ...tableParams,
                offset: 0, //起始条数
                connectionName: [record?.connectionName]
              })
            }}>
              {record?.connectionName}
            </span>
          </>
        )
        columnItem.width = 140
      }
      if (key === 'objectName') {
        //@ts-ignore
        columnItem.render = (list: any, record: any) =>
          _.isArray(list) && (
            <Tooltip
              title={list.map((key) => (
                <div key={key}>{key}</div>
              ))}
            >
              <div style={{ fontSize: 12, color: '#3262FF', cursor: "pointer" }} onClick={() => {
                setTableParams({
                  ...tableParams,
                  offset: 0, //起始条数
                  objectNames: list,
                  connectionName: [record?.connectionName]
                })
              }}>{list[0]}</div>

            </Tooltip>
          )
      }
      if (key === 'resultFlag') {
        //@ts-ignore
        columnItem.render = (val: any, { errorMsg, checkPermissionId, executeSql, sqlType, clientType }) => {
          return (
            <>
              {
                (val === null || clientType === 'TERMINAL') ? (
                  <Tooltip
                    placement="topRight"
                    title={errorMsg || t("auays:tip.terminal_exe_statement_err")}
                  >
                    <InfoCircleOutlined style={{ margin: '0 2px', cursor: 'help' }} />
                  </Tooltip>
                ) : val === 1 ? (
                  <Badge status="success" text={t("auays:bg_txt.execution_successful")} />
                ) : (
                  <Badge status="error" text={renderErrorFlag(val)} />
                )
              }
              {
                sqlType === 'SCRIPT_FILE' &&
                <FolderOpenOutlined className='options ml4' onClick={() => handleDownloadResultLog(checkPermissionId, executeSql)} />
              }
            </>
          )
        }
        columnItem.width = 140
      }
      if (key === 'sqlType') {
        //@ts-ignore
        columnItem.render = (val: string) => {
          return val && <Tag color={SQL_OPERATE_TYPES[val]}>{val}</Tag>
        }
      }
      if (key === 'errorMsg') {
        //@ts-ignore
        columnItem.render = (val: string) =>
          <Tooltip
            title={val}
          >
            <Typography.Text ellipsis={true}
              style={{ width: 200 }}
            >{val}</Typography.Text>
          </Tooltip>

      }
      if (key === 'executeSql') {
        //@ts-ignore
        columnItem.render = (val: string, record: any) => {
          return (
            <Typography.Paragraph style={{ width: 180, fontSize: 12, wordBreak: 'break-all' }} copyable={{ text: val }}>
              {
                val?.length > 66 ? <Tooltip
                  title={val}
                  overlayClassName={styles.executeSqlTooltip}
                >
                  {val.substring(0, 66) + '...'}
                </Tooltip>
                  : val
              }
              {
                record?.filePath &&
                <FolderOpenOutlined
                  onClick={() => record?.sqlType === 'SCRIPT_FILE'
                    ? downloadSqlFile({ attachPath: record?.filePath })
                    : tryDownloadFile(record.filePath)
                  }
                  className='options ml4'
                />
              }
            </Typography.Paragraph>
          )
        }
      }
      if (key === 'inTrans') {
        //@ts-ignore
        columnItem.render = (val: boolean) =>
          (<>{val ? t("auays:tb_render.manual_mode") : t("auays:tb_render.automatic_mode")}</>)
        columnItem.width = 120
      }
      if (key === 'tabKey') { // 执行窗口ID
        //@ts-ignore
        columnItem.render = (val: string) => (
          <span style={{ color: '#3262FF', cursor: "pointer" }} onClick={() => {
            setTableParams({
              ...tableParams,
              offset: 0, //起始条数
              tabKey: val
            })
          }}>
            {val}
          </span>
        )
        columnItem.width = 150
      }
      if (key === 'source') { // 来源
        //@ts-ignore
        columnItem.render = (val: string) => (
          <span>{val || '-'}</span>
        );
        //@ts-ignore
        columnItem.filters = SOURCE_FIELD_FILTERS.map(item => ({
          text: t(item.text),
          value: item.value
        }));;
      }
      if(key === 'dbType'){
        columnItem.width = 140
      }
      if(key === 'port'){
        columnItem.width = 120
      }
      if(key === 'affectRows'){
        columnItem.width = 120
      }
      if( key === 'executeCost'){
        columnItem.width = 120
      }
      return columnItem
    })
  }, [DEFAULT_EXECUTE_LOG_COLUMNS, columns, tableParams, locales])

  const handleTableChange = (
    pagination: any,
    filters: Record<string, any>,
  ) => {
    const { pageSize = 10, current = 1 } = pagination
    const { source } = filters || {};
    setSourceFiltersVal(source);
    getSourceFiltersVal && getSourceFiltersVal(source);
    setTableParams({
      ...tableParams,
      offset: current > 1 ? (current - 1) : 0,
      limit: pageSize || 10,
      source: source,
    })
  }

  return (
    <>
      <div className={styles.tableHeader}>
        {
          !isSqlLog ?
            <SearchHeader
              isUnauthorized={isUnauthorized}
              queryParams={tableParams}
              setSearchParams={(values: any) => {
                setTableParams({ ...values })
              }}
              refresh={() => refresh()}
              showCustomColumnPanel={() => setIsShowCustomColumnPanel(true)}
              sourceFiltersVal={sourceFiltersVal}
            />
            : <>&nbsp;</>
        }
      </div>
      <div className={styles.tableWrapper}>
        <Table
          rowKey="id"
          size={props?.size || "middle"}
          loading={loading}
          columns={formatColumns}
          dataSource={logs?.data || []}
          indentSize={30}
          rowClassName={(record, index) => index === isSelectedRowIndex ? 'globalSearchRowSelected': ''}
          scroll={props?.scroll || { x: formatColumns?.length > 7 ? 1740 : 'max-content', y: `calc(100vh - 325px) ` }}
          tableLayout="auto"
          className={styles.customColumnTable}
          pagination={{
            className: styles.pagination,
            showSizeChanger: true,
            showQuickJumper: true,
            size: 'default',
            pageSize: logs?.limit || 10,
            current: Number(logs?.offset || 0) / (logs?.limit || 10) + 1,
            total: logs?.total || 0,
            showTotal: (total) =>  t("personal:totalRecords", { total }),
            ...props?.pagination
          }}
          expandable={{
            childrenColumnName: `${styles.expandedRowContent}`,
            expandIcon: ({ expanded, onExpand, record }) =>
              expanded ? (
                <CaretDownOutlined onClick={(e) => onExpand(record, e)} />
              ) : (
                <CaretRightOutlined onClick={(e) => onExpand(record, e)} />
              ),
            expandedRowRender: (record: any) => (
              <ExpandedRowContent record={record} />
            ),
            rowExpandable: (record) => true,
          }}
          onChange={handleTableChange}
        />
      </div>
      <CustomColumnFieldPanel
        defaultColumnFields={
          _.isEmpty(columns) ? DEFAULT_EXECUTE_LOG_COLUMNS : columns
        }
        visible={isShowCustomColumnPanel}
        onCancel={() => {
          setIsShowCustomColumnPanel(false)
        }}
        onOk={(fields: string[]) =>
          userId &&
          getAndSetColumnFields({ columns: fields, userId, type: 'EXECUTE' })
        }
      />
    </>
  )
})
