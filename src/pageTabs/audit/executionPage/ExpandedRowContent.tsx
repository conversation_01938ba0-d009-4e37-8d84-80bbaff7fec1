import React, { useState } from 'react'
import * as _ from 'lodash'
import { Row, Col, Spin, Modal } from 'antd'
import { useRequest } from 'src/hook'
import { findExecuteDetail, executeDetailRes } from 'src/api'
import { ModalAuthorizationDetail } from './ModalAuthorizationDetail'
import { ModalFlowDetail } from 'src/pageTabs/flowPages/flowDetails'
import styles from './index.module.scss'
import { useTranslation } from 'react-i18next'

export const ExpandedRowContent = ({ record }: { record: any }) => {
  const { checkPermissionId, executeSql, executor, originalSql, sqlFileName, sqlFile } = record
  const { t } = useTranslation()
  const [visible, setVisible] = useState(false) // 流程详情 modal visible
  const [activeRecord, setActiveRecord] = useState<any>() // 流程详情 当前 流程 record记录
  const [relationSqlVisible, setRelationSqlVisible] = useState(false)  // 关联sql modal visible

  const { data: detail, loading } = useRequest(
    () =>
      checkPermissionId &&
      executor &&
      findExecuteDetail({ userId: executor, checkPermissionId }),
    {
      refreshDeps: [executor, checkPermissionId],
    },
  )

  return (
    <div className={styles.rowContent}>
      <div style={{marginBottom: 2}}>{t("auays:desc_lbl.operation_sql")}&nbsp;&nbsp;{originalSql}</div>
      <div style={{marginBottom: 2}}>{t("auays:desc_lbl.execution_sql")}&nbsp;&nbsp;{executeSql}</div>
      <div>{t("auays:desc_lbl.associated_sql")}&nbsp;&nbsp;<span className='options' onClick={()=>setRelationSqlVisible(true)}>{sqlFileName}</span></div>
      <div className={styles.operationList}>
        <Row>
          <Col span={6}>{t("auays:desc_lbl.associated_authorization")}</Col>
          <Col span={6}>{t("auays:desc_lbl.operation_type")}</Col>
          <Col span={6}>{t("auays:desc_lbl.authorizer")}</Col>
          <Col span={6}>{t("auays:desc_lbl.authorization_time")}</Col>
        </Row>
        {!_.isEmpty(detail) ? (
          detail.map((item: executeDetailRes) => (
            <Spin spinning={loading} key={item?.id}>
              <Row className={styles.operationItem} key={item?.id}>
                <Col
                  span={6}
                  className={styles.moreInfo}
                  onClick={() => {
                    setActiveRecord(item)
                    setVisible(true)
                  }}
                >
                  {item?.authedPermissionName}
                </Col>
                <Col span={6}>{item?.operateType}</Col>
                <Col span={6}>{item?.authUserName}</Col>
                <Col span={6}>{item.time}</Col>
              </Row>
            </Spin>
          ))
        ) : (
          <div
            style={{ textAlign: 'center', paddingTop: 10, color: '#667084' }}
          >
            {t("auays:emp_desc.no_data_available")}
          </div>
        )}
      </div>
      <ModalFlowDetail
        key={Number(activeRecord?.id)}
        record={activeRecord}
        visible={visible && activeRecord?.operateType === 'examine'}
        applyId={Number(activeRecord?.id) as any}
        kind="approval"
        onCancel={() => setVisible(false)}
        footer={null}
      />
      <ModalAuthorizationDetail
        key={Number(activeRecord?.id)}
        visible={visible && activeRecord?.operateType === 'authorization'}
        record={activeRecord}
        onCancel={() => setVisible(false)}
      />
      {/* 关联sql明细弹框 */}
      <Modal
        title={sqlFileName}
        visible={relationSqlVisible}
        onCancel={() => setRelationSqlVisible(false)}
        footer={null}
      >
        <div style={{maxHeight: '60vh', overflow: 'auto', whiteSpace: 'pre-line'}}>
          {sqlFile}
        </div>
      </Modal>
    </div>
  )
}
