.executionContent {
  height: 100%;

  .searchHeader {
    display: flex;
    align-items: center;
    justify-content: space-between;
    // flex-wrap: wrap;

    .leftNode {
      display: flex;
      justify-content: flex-start;
      align-items: center;

      .overStep {
        margin-bottom: 1px;
      }
    }

    .selectTree {
      min-width: 250px;
    }
  }

  .tableHeader {
    margin-bottom: 10px;

    .refreshIcon {
      padding: 4px 4px;
    }

    .settingIcon {
      margin-left: 10px;
    }
  }

  .tableWrapper {

    .customColumnTable {
      :global {
        .ant-table-thead>tr>th {
          // background-color: #fff;
          color: #667084;
        }

        .ant-table-tbody>tr>td {
          color: #0f244c;
        }

        .ant-table-expanded-row>td {
          background: #fff;
          padding: 0 0 !important;
        }
      }

      .resultState {
        display: flex;
        align-items: center;
        justify-content: flex-start;

        .iconStyle {
          margin-right: 4px;
          background: red;
          width: 8px;
          height: 8px;
          border-radius: 50%;
          display: inline-block;
        }

        .successState {
          background: green;
        }
      }

      .pagination {
        text-align: right;
        border-top: 1px solid #f0f0f0;
        padding-top: 10px;
      }

      .rowContent {
        background: #eef2f7;
        padding: 13px 54px;

        .operationList {
          width: 50%;
          padding-top: 10px;

          .operationItem {
            padding: 4px 0;

            :global {
              .ant-col {
                font-size: 12px;
                color: #667084 !important;
              }
            }

            .moreInfo {
              cursor: pointer;
              color: #3262ff !important;
              text-decoration: underline;
            }
          }
        }
      }
    }
  }
}

.flowDetailItem {
  &:global.ant-form-item {
    margin-bottom: 0;
  }

  // override antd form items
  :global {
    .ant-form-item-label>label {
      color: #60718e;
    }

    .ant-form-item-control {
      color: #091932;
    }
  }
}

.analysisExecutionModal {

  .detailItem {
    &:global.ant-form-item {
      margin-bottom: 14px;
    }

    // override antd form items
    :global {
      .ant-form-item-label>label {
        color: #60718e;
      }

      .ant-form-item-control {
        color: #091932;
      }
    }
  }
}

.unauthorizedOperationPage {
  position: absolute;
}

.executeSqlTooltip {
  :global {
    .ant-tooltip-inner {
      max-height: 500px;
      overflow: hidden auto;
    }
  }
}