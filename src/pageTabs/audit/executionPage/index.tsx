import React from 'react';
import classNames from 'classnames';
import { Layout, Breadcrumb } from 'antd';
import { CustomColumnTable } from './CustomColumnTable';
import Watermark from '@pansy/react-watermark'
import { useSelector, useDispatch } from 'src/hook';
import 'src/styles/layout.scss';
import styles from './index.module.scss';
import { useTranslation } from 'react-i18next';
import { 
  setOverviewPageState,
  setOverviewPageDetailParams 
} from 'src/pageTabs/audit/overview/overviewSlice'

const { Header, Content } = Layout

export const AuditExecutionPage = () => {
  const dispatch = useDispatch()
  const { watermarkEffect } = useSelector((state: any) => state.login)
  const { t } = useTranslation()
  // watermark
  const { watermarkSetting, watermarkValue } = useSelector(
    (state: any) => state.login.userInfo,
  )
  const { overviewPageDetailParams } = useSelector((state: any) => state.overview)
  const title = overviewPageDetailParams?.highFlag ? t("auays:bc_title.critical_detail") : overviewPageDetailParams?.skipDesens ? t("auays:bc_title.sensitive_resource_ope_detail") : t("auays:bc_title.statement_detail")
  
  // 渲染审计概览
  const gotoAuditView = () => {
    dispatch(setOverviewPageState(''))
    dispatch(setOverviewPageDetailParams({}))
  }

  return (
    <Layout className="cq-container">
      <Header className="breadcrumb-header" title={title}>
        <Breadcrumb className="breadcrumb" separator=''>
          <Breadcrumb.Item>{t("auays:bc_title.audit_analysis")}</Breadcrumb.Item>
          <Breadcrumb.Separator>|</Breadcrumb.Separator>
          <Breadcrumb.Item><span onClick={gotoAuditView}>{t("auays:bc_title.audit_overview")}</span></Breadcrumb.Item>
          <Breadcrumb.Separator>/</Breadcrumb.Separator>
          <Breadcrumb.Item>{title}</Breadcrumb.Item>
        </Breadcrumb>
      </Header>
      <Layout className="cq-main">
        <Content
          className={classNames('cq-content', { [styles.executionContent]: true })}
        >
          {/* 因为这个组件两个地方在用，所以传入isUnauthorized判断需不需要原有组件里的执行成功/执行失败组件 */}
          <CustomColumnTable isUnauthorized={false} />
          <Watermark
            text={watermarkValue?.length ? watermarkValue?.join(' ') : ''}
            pack={false}
            rotate={20}
            zIndex={99}
            visible={watermarkSetting}
            {...watermarkEffect}
          />
        </Content>
      </Layout>
    </Layout>
  )
}
