// 选择对象
import React, { useEffect, useState } from "react"
import { Iconfont } from 'src/components'
import { Button, Drawer, Input, Tree } from 'antd'
import { SearchOutlined } from '@ant-design/icons'
import { useRequest } from "src/hook"
import { queryTreeNode } from 'src/api'
import { useTranslation } from "react-i18next"

const ChooseObjectDrawer = (props: any) => {
  const {
    visible,
    setVisible,
    value,
    handlevalueChange,
    params
  } = props
  const { t } = useTranslation()
  const [originData, setOriginData] = useState<any[]>([])
  const [treeData, setTreeData] = useState<any[]>([])
  const [autoExpandParent, setAutoExpandParent] = useState(true);
  const [expandedKeys, setExpandedKeys] = useState<string[]>([])
  const [checkedValues, setCheckedValues] = useState<string[]>([])

  const init = () => {
    const paraments = params && Array.isArray(params) ? [...params] : [params]
    const initData: any[] = paraments?.map((item: any) => ({
      ...item,
      key: item?.nodePath,
      title: item?.nodeName,
      allLevelSplicedTitle: item?.nodeName,
    }))
    setOriginData(initData)
    setTreeData(initData)
  }

  const runFuc = async (params: any) => {
    for (const item of params)  {
      await run(item)
    }
  }

  useEffect(() => {
    if (params && Array.isArray(params)) {
      runFuc(params)
    } else if (params && Object.keys(params)?.length) {
      run(params)
    }
    init()
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [JSON.stringify(params)])

  useEffect(()=>{
    if(value){
      setCheckedValues(value)
    }
  },[value])

  // 子级手动拼接父级标题
  const handleSplicedTitle = (treeData: any[], parentInfo: any) => {
    return treeData?.map((i: any) => {
      i['allLevelSplicedTitle'] = parentInfo?.allLevelSplicedTitle ? ( parentInfo?.allLevelSplicedTitle + '.' + i?.title ) : i?.title
      if(i?.children?.length){
        i.children = handleSplicedTitle(i?.children, i)
      }
      return {...i}
    })
  }

  // 为解决CQ-7122bug，后端要求添加一个isObjectAudit参数，默认为true
  const { run } = useRequest((params) => queryTreeNode({ ...params, isObjectAudit: true }), {
    manual: true,
    onSuccess: (res: any[]) => {
      // 查询到指定级别数据
      const objects = ["table", "trigger", "view", "materializedView", "function", "udf", "procedure", "flexTable", "sequence", "synonym", "package", "packageBody", "foreignTable", "dictionary", "dbLink", "job", "jobs"]
      const children = res?.map((i: any) => ({
        ...i,
        key: i?.nodePath,
        title: i?.nodeName,
        connectionType: i?.connection?.connectionType,
        isLeaf: objects.includes(i?.nodeType),
      })) || []

      const recursionTree = (data: any[], parentId: string) => {
        return data?.map((i: any)=>{
          if(i?.children?.length){
            recursionTree(i?.children, parentId)
          }else if(i?.key===parentId){
            i.children = handleSplicedTitle(children, i)
          }
          return {...i}
        })
      }
      setTreeData((data: any)=>{
        return recursionTree(data, res[0]?.parentId) 
      })
      setOriginData((data: any)=>{
        return recursionTree(data, res[0]?.parentId) 
      })
    }
  })

  const flatData = (data: any[]) =>{
    const dataList: { key: React.Key; title: string }[] = [];
    const generateList = (data: any[]) => {
      for (let i = 0; i < data.length; i++) {
        const node = data[i];
        dataList.push({...node });
        if (node.children) {
          generateList(node.children);
        }
      }
    };
    generateList(data);
    return dataList
  }

  // 标题处理
  const loopTitle = (data: any[], searchValue: string): any[] =>
    data.map(item => {
      const strTitle = item.title;
      const index = strTitle.indexOf(searchValue);
      const beforeStr = strTitle.substring(0, index);
      const afterStr = strTitle.slice(index + searchValue.length);
      const title =
        index > -1 ? (
          <span>
            {beforeStr}
            <span style={{color:'#3262FF'}}>{searchValue}</span>
            {afterStr}
          </span>
        ) : (
          <span>{strTitle}</span>
        );
      if (item.children) {
        return { ...item, title, children: loopTitle(item.children, searchValue) };
      }

      return {
        ...item, 
        title, 
      };
    });

  const handleSearch = (e: any) => { 
    const value = e.target.value.trim()
    const newTreeData = loopTitle(originData, value);
    const expandedKeys: any[] = value ? flatData(treeData)?.filter((i: any)=>i?.nodeName?.includes(value))?.map(i=>i?.key) : []
    setTreeData(newTreeData)
    setExpandedKeys(expandedKeys)
    setAutoExpandParent(true)
  }

  const onExpand = (newExpandedKeys: any[]) => {
    setExpandedKeys(newExpandedKeys);
    setAutoExpandParent(false);
  };

  const onClose = () => {
    setVisible(false)
  }

  const titleRender = (nodeData: any) => {
    const { title, nodeType, connectionType } = nodeData
    return (
      <>
        <Iconfont
          style={{ marginRight: 4 }}
          type={nodeType === "connection" ? `icon-${connectionType}` : `icon-${nodeType}`}
        />
        {title}
      </>
    )
  }

  const loadData = (node: any) => {
    // 初始数据不用渲染
    if(node.nodeType !== 'connection'){
      const {
        connectionType,
        nodeName,
        nodePath,
        nodePathWithType,
        nodeType
      } = node
      const paraments = {
        connectionId: params?.connectionId,
        connectionType,
        nodeName,
        nodePath,
        nodePathWithType,
        nodeType,
      }
      run(paraments)
    }
    return new Promise<void>((res)=>{res()})
  }

  const handleCheck = (_: any, e: any) => {
    const { checkedNodes } = e
    const checkValue = checkedNodes?.map((i: any)=>{
      const { key, allLevelSplicedTitle, nodeName, nodePathWithType, nodeType } = i || {}
      return {
        key,
        allLevelSplicedTitle,
        label: nodeName, 
        value: { nodePathWithType, nodeType },
      }
    })
    setCheckedValues(checkValue)
  }

  const handleOk = () => {
    handlevalueChange(checkedValues)
    onClose()
  }

  const checkedKeys = checkedValues?.map((i: any)=>i?.key)
  return (
    <Drawer
      title={t("auays:item_label.select_object")}
      width={520}
      visible={visible}
      onClose={onClose}
      footer={[
        <Button onClick={onClose}>{t("auays:btn.cancel")}</Button>,
        <Button type='primary' style={{ marginLeft: 10 }} onClick={handleOk}>{t("auays:btn.confirm")}</Button>
      ]}
      destroyOnClose
    >
      <Input
        style={{ width: 476, marginBottom: 10 }}
        prefix={<SearchOutlined />}
        onChange={handleSearch}
        placeholder={t("auays:inp_ph.search")}
      />
      <Tree
        checkable
        checkedKeys={checkedKeys}
        treeData={treeData}
        titleRender={titleRender}
        loadData={loadData}
        onCheck={handleCheck}
        onExpand={onExpand}
        expandedKeys={expandedKeys}
        autoExpandParent={autoExpandParent}
      />
    </Drawer>
  )
}
export default ChooseObjectDrawer