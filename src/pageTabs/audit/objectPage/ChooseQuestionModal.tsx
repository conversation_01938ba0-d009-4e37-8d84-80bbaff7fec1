// 选择问题
import React, { useState } from 'react'
import { Modal, Select } from 'antd'
import { useRequest} from 'src/hook'
import { getOperateTypes } from 'src/api'
import styles from './index.module.scss'
import { useTranslation } from 'react-i18next'

interface IProps {
	visible: boolean,
	setVisible:(v: boolean)=>void
	handlevalueChange: (res: any) => void
	[p: string]: any
}
const ChooseQuestionModal = (props: IProps) => {
	const { visible, setVisible, value=[], handlevalueChange } = props
	const { t } = useTranslation()
	const [values, setValues] = useState<any[]>(value)

	const { data: operateList=[] } = useRequest(getOperateTypes,{
		formatResult:(res:any)=>{
			return Object.keys(res)?.map((i: any)=>{
				return {
					label: `${i}(${res[i]})`,
					value: i
				}
			}) 
		}
	})

	const onClose = ()=>{
		setVisible(false)
	}

	const onOk = () => {
		handlevalueChange(values)
		onClose()
	}

	const onChange = (v: any[]) => {
		setValues(v)
	} 

	return (
		<Modal 
			className={styles.customHeight}
			title={t("auays:btn.select_problem")}
			visible={visible}  
			onCancel={onClose}
			onOk={onOk}
			destroyOnClose
		>
			<Select
				style={{width: 472}}
				placeholder={t("auays:sele_ph.please_select")}
				options={operateList}
				onChange={onChange}
				labelInValue
				mode='multiple'
				defaultValue={value}
				optionFilterProp='label'
				allowClear
			/>
		</Modal> 
	)
} 
export default ChooseQuestionModal