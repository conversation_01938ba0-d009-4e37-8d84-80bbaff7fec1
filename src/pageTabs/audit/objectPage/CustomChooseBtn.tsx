// 选择问题/对象按钮
import React, {useState, useEffect} from 'react'
import { Button, Tag, Tooltip } from 'antd'
import { PlusOutlined } from '@ant-design/icons'
import ChooseQuestionModal from './ChooseQuestionModal'
import ChooseObjectDrawer from './ChooseObjectDrawer'
import classnames from 'classnames'
import styles from './index.module.scss'
import { useTranslation } from 'react-i18next'

interface ChooseBtnProps {
	title: string, 
	type: string,
	[p: string]: any
}
 
let init = true
const CustomChooseBtn = (props: ChooseBtnProps) =>{
	const { title, type, onChange, disabled=false, nodeParams } = props
	const [visible, setVisible] = useState(false)
	const [value, setValue] = useState<any[]>([])
	const { t } = useTranslation()

	// 根据nodeParams(数据源和连接信息)更新value(对象信息)
	useEffect(()=>{
		if(value?.length){
			// 数据源修改时，更新value
			if(!nodeParams?.length){
				setValue([])
				return 
			}
			// 连接信息修改时，更新value
			const nodePathWithTypes = nodeParams?.map((i: any)=>i?.nodePathWithType)
			// 过滤出连接下的对象信息,即对象信息中的value?.nodePathWithType必须是在nodePathWithTypes数组中任何一个开始的字符串
			setValue((v: any)=>v?.filter((i: any)=>{
				return nodePathWithTypes?.some((j: string)=>{
					return i?.value?.nodePathWithType?.startsWith(j)
				})
			}))
		}
	// eslint-disable-next-line react-hooks/exhaustive-deps
	},[nodeParams])

	useEffect(()=>{
		if(init){ return }
		const val = value?.map(i=>i?.value)
		onChange(val)
	// eslint-disable-next-line react-hooks/exhaustive-deps
	},[value])

	const handleChoose = () => {
		init = false
		setVisible(true)
	}
	const handleClearAll = () => {
		setValue([])
	}
	const handleClose = (key: string) => { 
		setValue((val: any[])=>{
			return val?.filter((i: any)=>i?.key !== key)
		})
	}
  
	const modalProps = {
		visible,
		setVisible,
		value,
		handlevalueChange: setValue,
	}
	return (
		<>
			<div className={styles.questionLine}>
				<Button disabled={disabled} icon={<PlusOutlined />} onClick={handleChoose}>{title}</Button>
				<span className={classnames(styles.options, styles.ml10)} onClick={handleClearAll}>{t("auays:btn.clear_all")}</span>
			</div>
			<div className={styles.resultWrap}>
				{value?.map((i: any)=>(
					<Tooltip title={i?.allLevelSplicedTitle} key={i?.key}>
						<Tag key={JSON.stringify(i?.key)} closable onClose={()=>handleClose(i?.key)} className={styles.item}>
							{i?.label}
						</Tag>
					</Tooltip>
				))}
			</div>
			{ 
				visible && (
					type==='chooseQuestion' 
				? <ChooseQuestionModal {...modalProps} />
				: <ChooseObjectDrawer {...modalProps} params={nodeParams} />
				)
			}
		</>
	) 
}
export default CustomChooseBtn