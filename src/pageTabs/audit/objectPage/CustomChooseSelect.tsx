// 选择数据源和连接
import React, { ReactNode, useEffect, useState } from "react";
import { Checkbox, Divider, Select } from 'antd'
import { useRequest } from 'src/hook'
import { getDataSourceList, getDSConnectionList } from 'src/api'
import { Iconfont } from "src/components";
import { useTranslation } from "react-i18next";
import styles from './index.module.scss'
import { CheckboxChangeEvent } from "antd/lib/checkbox";

const CustomChooseSelect = (props: any) => {
	const { updateNodeParams } = props
	const { t } = useTranslation()
	const [connectionMap, setConnectionMap] = useState<any>()
	const [value, setValue] = useState({
		dataSource: '',
		connection: [],
		connectionShowValue: []
	})

	useEffect(() => {
		const newValue = {
			dataSource: value.dataSource,
			connection: value.connection,
		}
		props.onChange(newValue)
	}, [value.dataSource, value.connection])

	// 获取数据源列表
	const { data: dataSourceList = [] } = useRequest(getDataSourceList, {
		formatResult(res: any[]) {
			// 过滤edis和mongo
			return res?.filter((i: any) => i?.dataSourceType?.toUpperCase() !== 'NOSQL')?.map((i: any) => ({
				title: i?.dataSourceName,
				label: <><Iconfont type={`icon-connection-${i?.dataSourceName}`} style={{ marginRight: 4 }} />{i?.dataSourceName}</>,
				value: i?.dataSourceName
			}))
		},
	})
	// 获取连接列表
	const { data: connectionList = [], run } = useRequest(getDSConnectionList, {
		manual: true,
		formatResult: (res: any) => {
			const connectionMap: any = {}
			res?.forEach((i: any) => {
				if (i?.connectionId) {
					connectionMap[i.connectionId] = i
				}
			})
			setConnectionMap(connectionMap)
			return res?.map((i: any) => ({
				title: i?.nodeName,
				label: <div title={i?.nodeName}><Iconfont type={`icon-${i?.connectionType}`} style={{ marginRight: 4 }} />{i?.nodeName}</div>,
				value: i?.nodeName,
				realValue: i?.connectionId
			}))
		},
	})

	const handleDataChange = (value: string | string[], type: string, showValue?: string | string[]) => {
		if (type === 'dataSource') {
			run(value as string).then(() => {
				updateValue(type, value as string)
        updateNodeParams?.({})
			}).catch()
		} else {
			updateValue(type, value, showValue)
      let params = {}
      if (Array.isArray(value)) {
        params = value?.map((item: any) => {
          return connectionMap[item]
        })
      } else {
        params = value? connectionMap[value] : {}
      }
			updateNodeParams?.(params)
		}
	}

	const updateValue = (type: string, val: string | string[], showValue?: string | string[]) => {
		setValue((d: any) => {
			// 更新数据源要重置连接
			if (type === 'dataSource') {
				d['dataSource'] = val
        d['connectionShowValue'] = []
				d['connection'] = ''
      }
			else {
				d['connectionShowValue'] = showValue
				d['connection'] = val
			}
			return { ...d }
		})
	}

	// 全选CheckBox
	const handleSelectAll = (e:CheckboxChangeEvent) => {
		const { checked } = e.target
		if (checked) {
			const value = connectionList.map((item: any) => item.realValue)
			const showValue = connectionList.map((item: any) => item.value)
			handleDataChange(value, 'connection', showValue)
		} else {
			handleDataChange([], 'connection',[])
		}
	}

	// 连接的自定义下拉框，实现全选功能
	const dropdownRender = (originNode: ReactNode) => {
		// 全选状态
		const checked =  connectionList?.length > 0 && value?.connectionShowValue?.length === connectionList?.length
		// 半选状态
		const indeterminate = value?.connectionShowValue?.length > 0 && value?.connectionShowValue?.length < connectionList?.length
		return (
			<>
				<Checkbox
					onChange={handleSelectAll}
					className={styles.allSelect}
					checked={checked}
					disabled={connectionList?.length === 0}
					indeterminate={indeterminate}
				>
					{t("auays:check.select_all")}
				</Checkbox>
				<Divider/>
				<div>
					{originNode}
				</div>
			</>
		)
	}

	return (
		<>
			<Select
				style={{ width: 230 }}
				options={dataSourceList}
				placeholder={t("auays:sele_ph.choose_data_source")}
				allowClear
				showSearch
				onChange={(val: string) => handleDataChange(val, 'dataSource')}
			/>
			{value?.dataSource && (
				<Select
          mode='multiple'
          maxTagCount={2}
					style={{ minWidth: 220, width: 'auto', marginLeft: 10 }}
					options={connectionList}
					placeholder={t("auays:sele_ph.choose_conn")}
					allowClear
					showSearch
					onChange={(val: string[], option: any) => {
						const realValue = option?.map((item: any) => item?.realValue)
						handleDataChange(realValue, 'connection', val)
					}}
					value={value?.connectionShowValue}
					// 自定义下拉框，实现全选功能
					dropdownRender={dropdownRender}
					dropdownClassName={styles.connSelectpopup}
				/>
			)}
		</>
	)
}
export default CustomChooseSelect