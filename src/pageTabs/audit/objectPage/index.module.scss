.options {
	color: var(--primary-color);
	cursor: pointer;
}
.ml10 {
	margin-left: 10px;
}
.objectAuditPage {
	height: calc(100vh - 78px);
	overflow-y: auto;
}
.objectAuditWrap {
	padding: 10px 20px;
	background-color: #fff;
	.step1Card,
	.step2Card,
	.step3Card {
		border: 1px solid #EAEBF0;
	}
	.questionLine {
		margin-bottom: 10px;
		display: flex;
		align-items: center;
	}
	.resultWrap {
		display: flex;
		align-items: center;
		flex-wrap: wrap;
		max-height: 120px;
		overflow-y: auto;
		.item {
			margin: 0 10px 10px 0;
		}
	}
}
.customHeight {
	:global{
		.ant-modal-body {
			height: 350px;
		}
		.ant-select-selector {
			max-height: 64px;
			overflow-y: auto;
		}
	}
}

.connSelectpopup {

	.allSelect {
		padding: 1px 12px 5px;
	}

	:global {
		.ant-divider-horizontal {
			margin: 0px;
		}
	}

}
