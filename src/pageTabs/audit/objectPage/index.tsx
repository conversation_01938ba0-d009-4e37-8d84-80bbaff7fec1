/**
 * 对象审计
 */
import React, { useMemo, useState } from 'react'
import { 
  Steps, 
  Card, 
  Button, 
  Form, 
  DatePicker,
  Table, 
  Typography, 
  Tooltip, 
  Dropdown, 
  Menu, 
  Input 
} from 'antd'
import moment from 'moment'
import CustomChooseBtn from './CustomChooseBtn'
import CustomChooseSelect from './CustomChooseSelect'
import { auditReport, DataSourceTypes, exportAuditLogSql } from 'src/api'
import { Iconfont, SimpleBreadcrumbs, exportTaskCreatedNot } from 'src/components'
import { getScrollX, renderTableFields } from 'src/util'
import { SearchOutlined, VerticalAlignBottomOutlined } from '@ant-design/icons'
import styles from './index.module.scss'
import _ from 'lodash'
import { useTranslation } from 'react-i18next'

const { RangePicker } = DatePicker

const dateFormat = 'YYYY.MM.DD';
const layout = {
  labelCol: { span: 4 },
  wrapperCol: { span: 20 },
};

const getDbType = (txt: string) => {
	return DataSourceTypes.find(
    (type) => type?.toUpperCase() === txt?.toUpperCase(),
  )
}
const ObjectAudit = () => {
	const [form] = Form.useForm();
	const { t } = useTranslation()
	const [step, setStep] = useState<number>(0)
	const [dataSource, setDataSource] = useState<any[]>([])
	const [pagination, setPagination] =useState<any>({})

	const onChange = (value: number) => {
		setStep(value);
	};

	const BREADCRUMB_ITEMS = [
		{
			title: t("auays:bc_title.audit_analysis"),
		},
		{
			title: t("auays:bc_title.obj_audit"),
		},
	];

	return <div className={`${styles.objectAuditPage} cq-container` }>
		<div style={{padding: '0 20px'}}>
			<SimpleBreadcrumbs items={BREADCRUMB_ITEMS} />
		</div>
		<div className={styles.objectAuditWrap}>
			<Steps
				direction="vertical"
				current={step}
				onChange={onChange}
			>
				<Steps.Step description={<Step1 form={form} />} />
				<Steps.Step description={<Step2 form={form} setDataSource={setDataSource} setPagination={setPagination} setStep={setStep} />} />
				<Steps.Step description={<Step3 form={form} dataSource={dataSource} pagination={pagination} setPagination={setPagination} />} />
      </Steps>
		</div>
	</div> 
}
export default ObjectAudit


export const Step1 = (props: any) => {
	const { t } = useTranslation()
	const { form } = props
	return (
		<Card title={t("auays:step.problem_type")} className={styles.step1Card}>
			<Form
				{...layout}
				form={form}
			>
				<Form.Item name="question" label={t("auays:item_label.problem")} rules={[{ required: true, message: t("auays:rules_msg.select_question") }]}>
					<CustomChooseBtn title={t("auays:btn.select_problem")} type='chooseQuestion' />
				</Form.Item>
			</Form>
		</Card>
	)
}

export const Step2 = (props: any) => {
	const { form, setDataSource, setPagination, setStep} = props
	const [nodeParams, setNodeParams] = useState({})
	const { t } = useTranslation()

	const updateNodeParams = (p: any) => {
		setNodeParams(p)
	}

	// 开始审计
	const handleAudit = () => {
		form.validateFields()
		.then((values: any) => {
			const { timeRange, question, object} = values
			const params = {
				executeBeginMs: moment(timeRange[0]).startOf('d').valueOf(),
				executeEndMs: moment(timeRange[1]).endOf('d').valueOf(),
				sqlTypes: question,
				objectWithTypes: object
			}
			auditReport(params).then((res: any[])=>{
				const pagination = {
					total: res?.length,
					current: 1,
					pageSize: 10
				}
				setDataSource(res)
				setPagination(pagination)
				setStep(2)
			}).catch()
		}).catch()
	}

	const canChooseObject = !!Object.keys(nodeParams)?.length

	return (
		<Card title={t("auays:step.select_object")} className={styles.step2Card}>
			<Form
				{...layout}
				form={form}
			>
				<Form.Item name="dataSource" label={t("auays:item_label.select_data_source")} rules={[{required: true}]}>
					<CustomChooseSelect updateNodeParams={updateNodeParams}/>
				</Form.Item>
				<Form.Item name="object" label={t("auays:item_label.select_object")} rules={[{required: true}]}>
					<CustomChooseBtn 
						title={t("auays:item_label.select_object")} 
						type='chooseObejct' 
						nodeParams={nodeParams} 
						disabled={!canChooseObject}
					/>
				</Form.Item>
				<Form.Item name="timeRange" label={t("auays:item_label.time_range")} rules={[{required: true}]}>
					<RangePicker format={dateFormat} disabledDate={(current)=>current && current > moment().endOf('day')} />
				</Form.Item>
				<Form.Item wrapperCol={{ offset: 4, span: 20 }}>
					<Button type="primary" htmlType="submit" onClick={handleAudit}>
						{t("auays:btn.start_audit")}
					</Button>
				</Form.Item>
			</Form>
		</Card>
	)
}

export const Step3 = (props: any) => {
	const { dataSource=[], pagination, setPagination,form } = props
	const { t } = useTranslation()
  const [searchValue, setSearchValue] = useState('');
  
  const columns=[{
		title: t("auays:tb_title.username"),
		dataIndex: 'executor',
		width: 220,
		ellipsis: true,
		render:(txt: string, record: any)=><span>{`${renderTableFields(record?.executorName)}(${renderTableFields(txt)})`}</span>
	},
	{
		title: t("auays:tb_title.department"),
		dataIndex: 'deptName',
		width: 160,
		ellipsis: true,
		render:(txt: string)=><span>{renderTableFields(txt)}</span>
	},
	{
		title: t("auays:tb_title.client_ip"),
		dataIndex: 'clientIp',
		width: 160,
		ellipsis: true,
		render:(txt: string)=><span>{renderTableFields(txt)}</span>
	},
  {
		title: t("auays:tb_title.connection"),
		dataIndex: 'connectionName',
		width: 200,
		ellipsis: true,
		render:(txt: string, record: any)=><span>
			{
				txt ? <><Iconfont type={`icon-connection-${getDbType(record?.dbType)}`} style={{marginRight: 4}} />{txt}</> : '-'
			}
		</span>
	},
	{
		title: t("auays:tb_title.database"),
		dataIndex: 'containerName',
		width: 200,
		ellipsis: true,
		render:(txt: string)=><span>
			{
				txt ? <>{txt}</> : '-'
			}
		</span>
	},
	{
		title: t("auays:tb_title.table"),
		dataIndex: 'tableNames',
		width: 200,
		ellipsis: true,
		render:(txt: string, record: any)=><span>
			{
				txt ? <><Iconfont type='icon-table' style={{marginRight: 4}} />{txt}</> : '-'
			}
		</span>
	},
	{
		title: t("auays:tb_title.operation"),
		dataIndex: 'sqlType',
		width: 160,
		ellipsis: true,
		render:(txt: string)=><span>{renderTableFields(txt)}</span>
	},
	{
		title: t("auays:tb_title.ope_statement"),
		dataIndex: 'executeSql',
		width: 160,
		ellipsis: true,
    render: (val: string, record: any) => (
      <Typography.Paragraph 
        key={JSON.stringify(record)}
        copyable={{ text: val }}
      >
        {val?.length > 15 ? <Tooltip title={val}>{val.substring(0, 15) + '...'}</Tooltip> : val}
      </Typography.Paragraph>
    )
	},
	{
		title: t("auays:tb_title.ope_time"),
		dataIndex: 'executeBegin',
		width: 200,
		ellipsis: true,
		render:(txt: string)=><span>{renderTableFields(txt)}</span>
  }
	]

	const handlePagechange = (current:number, pageSize=10) => {
		setPagination((p:any)=>{
			return {
				...p,
				current,
				pageSize
			}
		})
	}

  const exportData = (type: string) => {
    const values = form.getFieldsValue();
    const { timeRange, question, object } = values
    const params = {
      executeBeginMs: moment(timeRange[0]).startOf('d').valueOf(),
      executeEndMs: moment(timeRange[1]).endOf('d').valueOf(),
      sqlTypes: question,
      objectWithTypes: object,
      forObjectQueryCondition: searchValue,
    }
    exportAuditLogSql({ ...params, type })
		.then(() => {
			exportTaskCreatedNot();
		})
  }
  
  const menu = (
    <Menu>
      <Menu.Item onClick={() => exportData('csv')}>csv</Menu.Item>
      <Menu.Item onClick={() => exportData('excel')}>excel</Menu.Item>
    </Menu>
  );
  
  const handleInputChange = (e: any) => {
    const value = e?.target?.value
    setSearchValue(value)
	}

  const filteredDataSource = useMemo(() => {
    if (!searchValue) return dataSource;
    let newData = _.cloneDeep(dataSource);

    if (searchValue) {
      const s = searchValue.toLowerCase()
      newData = newData.filter((item: any) => {
        const executor = `${renderTableFields(item?.executorName)}(${renderTableFields(item?.executor)})`;
        return (
          executor?.includes(searchValue)
          || item?.executeSql?.toLowerCase()?.replace(/\s/g, '')?.includes(s?.replace(/\s/g, ''))
        )
      })
    }
    setPagination((p: any) => {
      return {
        ...p,
        total: newData?.length,
        current: 1,
        pageSize: 10,
      }
    })
    return newData;
  }, [dataSource, searchValue, setPagination]);

	return (
    <Card
      title={t("auays:step.start_audit")}
      className={styles.step3Card}
      extra={
        <>
        <Input
          prefix={<SearchOutlined className="site-form-item-icon" />}
          placeholder={t("auays:inp_ph.search_username_or_sql")}
          value={searchValue}
          allowClear
          style={{ width: 400 }}
          onChange={handleInputChange}
        />
        {
          Array.isArray(dataSource) && dataSource.length > 0 &&
          <Dropdown overlay={menu}>
            <Button type="link"><VerticalAlignBottomOutlined />{t("auays:btn.export")}</Button>
          </Dropdown>
        }
        </>
      }>
      <Table
				rowKey='id'
				columns={columns} 
				dataSource={filteredDataSource}
				pagination={{
					...pagination,
					showSizeChanger: true,
					showQuickJumper: true,
					onChange: handlePagechange,
					showTotal: total => t("auays:tb.show_total", {total})
				}}
				scroll={{x: getScrollX(columns)}}
			/>
		</Card>
	)
}