import React, { useEffect, useMemo, useState } from 'react'
import { <PERSON>, Tooltip, Badge } from 'antd'
import { useLocation } from "react-router-dom";
import dayjs from 'dayjs'
import * as _ from 'lodash'
import { useSelector, useRequest } from 'src/hook'
import {
  getAndSetAuditColumn,
  findAuditOperateLogs,
  AuditExecutionParams,
  AuditExecutionLogParams,
  getOperateTypeEnum
} from 'src/api'
import {
  DEFAULT_OPERATE_LOG_COLUMNS,
  OPERATE_COLUMN_FIELDS,
} from 'src/constants'
import { GloablSearchLocationState } from 'src/pageTabs/GlobalSearchModal/WorkOrderResult';
import { SearchHeader } from './SearchHeader'
import { CustomColumnFieldPanel } from './CustomColumnFieldPanel'
import styles from './index.module.scss'
import { useTranslation } from 'react-i18next';

const transform = (query: AuditExecutionLogParams) =>
  _.omitBy(
    query,
    (value) =>
      value === null ||
      typeof value === 'undefined' ||
      (Array.isArray(value) && value.length === 0),
  )


interface IProps {
  isUnauthorized?: boolean | undefined,
  param?: any | undefined,
}

type ICombineLocationState = { timeRange: any } & GloablSearchLocationState;
export const CustomColumnTable = (props: IProps) => {
  const { isUnauthorized, param } = props;
  const defaultTableParams = {
    limit: 10, //条数
    offset: 0, //页数
    timeRange: [
      dayjs().subtract(3, 'day').startOf('d').valueOf(),
      dayjs().endOf('d').valueOf(),
    ],
    resultFlag: undefined,
  }
  const location = useLocation();
  const state = location?.state as ICombineLocationState;
  const { userId } = useSelector((state) => state.login.userInfo);
  const { locales } = useSelector(state => state.login)
  //跳转参数
  const { overviewPageDetailParams } = useSelector((state: any) => state.overview)

  const { t } = useTranslation()

  const [isShowCustomColumnPanel, setIsShowCustomColumnPanel] = useState(false)
  const [tableParams, setTableParams] =
    useState<AuditExecutionLogParams>(defaultTableParams)
  const [tableColumns, setTableColumns] = useState<string[]>([])

  const { loading: columnLoading, run: getAndSetColumnFields } = useRequest(
    getAndSetAuditColumn,
    {
      manual: true,
      onSuccess: (data: AuditExecutionParams) => {
        const res = data?.columns ?? []
        setTableColumns(res)
        setIsShowCustomColumnPanel(false)
      },
    },
  )

  const {
    data: logs,
    loading,
    refresh,
  } = useRequest(
    () => {
      let defaultParams = transform(tableParams)
      if (!_.isEmpty(defaultParams?.executors) && defaultParams?.executors) {
        defaultParams.userIds = defaultParams?.executors
        delete defaultParams?.executors
      }
      if (tableParams?.timeRange) {
        //@ts-ignore
        defaultParams.executeBeginMs = defaultParams.timeRange?.[0]
        //@ts-ignore
        defaultParams.executeEndMs = defaultParams.timeRange?.[1]
        delete defaultParams?.timeRange
      }
      //@ts-ignore
      return findAuditOperateLogs({
        ...defaultParams,
        //@ts-ignore
        offset: defaultParams?.offset * defaultParams?.limit,
      })
    },
    {
      refreshDeps: [tableParams],
      debounceInterval: 300,
    },
  )

  const { data: operateTypeEnum = [] } = useRequest(getOperateTypeEnum, {
    formatResult: (res: any) => {
      const result = Object.keys(res).map((i: any) => {
        return {
          text: res[i],
          value: res[i],
        }
      })
      return result
    }
  })

  useEffect(() => {
    userId &&
      getAndSetColumnFields({
        userId,
        type: 'OPERATE',
      })
  }, [userId])

  useEffect(() => {
    if (isUnauthorized) {
      let newParams = { ...tableParams, ...(param || {}) };

      if (state?.globalSearchRecordPosition) {
        //处理分页 并选中
        const pageNum = Math.ceil(Number(state.globalSearchRecordPosition) / 10);
        newParams.offset = pageNum - 1
      }
      setTableParams({ ...newParams });
    } else if (!_.isEmpty(overviewPageDetailParams) ) {
      let newParams: any = { ...tableParams, ...(overviewPageDetailParams || {}) };
      if (state?.globalSearchRecordPosition) {
        //处理分页 并选中
        const pageNum = Math.ceil(Number(state.globalSearchRecordPosition) / 10);
        newParams.offset = pageNum - 1;
        delete newParams?.globalSearchRecordPosition;
      }
      setTableParams({ ...newParams });
    }

  }, [JSON.stringify(overviewPageDetailParams), state]);

  //全局搜索
  const isSelectedRowIndex = useMemo(() => {
    if (!state?.globalSearchRecordPosition) {
      return null;
    }

    const pageNum = Math.ceil(Number(state.globalSearchRecordPosition) / 10);
    if (pageNum - 1 === tableParams?.offset && tableParams?.limit) {
      const itemIndexInPage = state.globalSearchRecordPosition % tableParams.limit;
      return itemIndexInPage === 0 ? tableParams.limit - 1 : itemIndexInPage - 1;
    }
  }, [tableParams?.offset, state?.globalSearchRecordPosition])

  const formatColumns = useMemo(() => {
    let currentColumns = DEFAULT_OPERATE_LOG_COLUMNS
    if (!_.isEmpty(tableColumns)) {
      currentColumns = tableColumns
    }
    return currentColumns.map((key) => {
      let columnItem: any = {
        key,
        //@ts-ignore
        title: t(OPERATE_COLUMN_FIELDS[key]),
        dataIndex: key,
        ellipsis: true,
      }
      if (key === 'resultFlag') {
        //@ts-ignore
        columnItem.render = (val: any) => (
          val ? (
            <Badge status="success" text={t("auays:tb_rd.exe_status.success")} />
          ) : (
            <Badge status="error" text={t("auays:tb_rd.exe_status.failure")} />
          )
        )
        columnItem.filters = [{
          text: t("auays:bg_txt.execution_successful"),
          value: 1,
        }, {
          text: t("auays:bg_txt.execution_failure"),
          value: 0,
        }]
        columnItem.filterMultiple = false
      }
      if (key === 'detail') {
        //@ts-ignore
        columnItem.render = (val: string) => {
          return (
            <Tooltip title={val}>
              {val && val.length > 60 ? `${val.slice(0, 60)}...` : val}
            </Tooltip>
          )
        }
      }

      // 操作栏增加搜索
      if (key === 'operateEntryPoint' && operateTypeEnum?.length) {
        columnItem.filters = operateTypeEnum
      }
      return columnItem
    })
  }, [DEFAULT_OPERATE_LOG_COLUMNS, tableColumns, operateTypeEnum, locales])

  const handleTableChange = (
    pagination: any,
    filters: Record<string, any>,
  ) => {
    const { current, pageSize } = pagination
    const { operateEntryPoint = [], resultFlag } = filters
    setTableParams({
      ...tableParams,
      offset: current - 1,
      limit: pageSize || 10,
      operates: operateEntryPoint,
      resultFlag: resultFlag?.[0]
    })
  }

  return (
    <>
      <div className={styles.tableHeader}>
        <SearchHeader
          queryParams={tableParams}
          setSearchParams={(values: any) =>
            setTableParams({ ...tableParams, ...values })
          }
          refresh={() => refresh()}
          showCustomColumnPanel={() => setIsShowCustomColumnPanel(true)}
        />
      </div>
      <div className={styles.tableWrapper}>
        <Table
          rowKey="id"
          size="middle"
          rowClassName={(record, index) => index === isSelectedRowIndex ? 'globalSearchRowSelected' : ''}
          loading={loading || columnLoading}
          columns={formatColumns}
          dataSource={logs?.data || []}
          indentSize={30}
          scroll={{ x: formatColumns?.length > 7 ? 1640 : 'max-content',y: `calc(100vh - 300px)` }}
          tableLayout="auto"
          className={styles.customColumnTable}
          pagination={{
            size: 'default',
            className: styles.pagination,
            showSizeChanger: true,
            showQuickJumper: true,
            pageSize: logs?.limit || 10,
            current: Number(logs?.offset || 0) / (logs?.limit || 10) + 1,
            total: logs?.total || 0,
            showTotal: (total) => t("auays:tb.show_total", { total: total || 0 }),
          }}
          onChange={handleTableChange}
        />
      </div>

      <CustomColumnFieldPanel
        defaultColumnFields={
          _.isEmpty(tableColumns) ? DEFAULT_OPERATE_LOG_COLUMNS : tableColumns
        }
        visible={isShowCustomColumnPanel}
        onCancel={() => setIsShowCustomColumnPanel(false)}
        onOk={(fields: string[]) =>
          userId &&
          getAndSetColumnFields({ columns: fields, userId, type: 'OPERATE' })
        }
      />
    </>
  )
}
