import React, { useState, useEffect, useMemo } from 'react'
import { SettingOutlined, RedoOutlined } from '@ant-design/icons'
import { Row, Col, Select, Button, Popconfirm } from 'antd'
import { DatePicker, exportTaskCreatedNot } from 'src/components'
import dayjs, { Dayjs } from 'dayjs'
import {
  getAuditUsers,
  OperationReportRequest,
  exportOperateLog,
} from 'src/api'
import styles from './index.module.scss'
import { formatDateRange } from 'src/util'
import { useTranslation } from 'react-i18next'

const { RangePicker } = DatePicker
const { Option } = Select

interface FilterParams {
  executors?: string[]
  timeRange?: [number, number] | null
  resultFlag?: string
  limit?: number
}

// 根据组件 filterParams 状态构造出查询请求所需要的参数
const getRequestParams = (filterParams: FilterParams) => {
  const { executors, timeRange, resultFlag } = filterParams
  // 构造查询参数
  const params: Partial<OperationReportRequest> = {
    executeBeginMs: timeRange?.[0],
    executeEndMs: timeRange?.[1],
  }
  if (executors?.length) {
    params.userIds = executors
  }
  if (resultFlag !== undefined) {
    params.resultFlag = Number(resultFlag)
  }
  delete filterParams.timeRange
  return { ...filterParams, ...params }
}

export const SearchHeader = ({
  queryParams = {},
  setSearchParams,
  refresh,
  showCustomColumnPanel,
}: {
  queryParams: any;
  setSearchParams: (values: any) => void
  refresh: () => void
  showCustomColumnPanel: () => void
}) => {
  const { t } = useTranslation()
  const [filterParams, setFilterParams] = useState<FilterParams>(queryParams)
  // 全部用户列表
  const [userList, setUserList] = useState<any[]>([])
  // 导出的状态
  const [isExport,setIsExport] = useState(false)

  const {
    executors,
    timeRange,
    resultFlag,
  } = filterParams
  const rangeValue = useMemo(() => {
    if (timeRange === null) {
      return null
    } else {
      const range = timeRange && timeRange.map((timestamp) => dayjs(timestamp)) as [
        Dayjs,
        Dayjs,
      ]
      return range
    }
  }, [timeRange])

  useEffect(() => {
    // 获取用户列表
    const getUserList = async () => {
      try {
        const users = await getAuditUsers()
        setUserList(users)
      } catch { }
    }
    getUserList()
  }, [])

  useEffect(() => {
    setFilterParams(queryParams);
  }, [queryParams]);

  const exportData = () => {
    setIsExport(true)
    exportOperateLog(getRequestParams({...filterParams}) as Partial<OperationReportRequest>)
    .then(() => { exportTaskCreatedNot() })
    .finally(() => {
      setIsExport(false)
    })
  }

  return (
    <div className={styles.searchHeader}>
      <Row justify="end" gutter={16} style={{ alignItems: 'center' }}>
        <label>{t("auays:div_lbl.filter_condition")}:</label>
        <Col>
          <Select
            mode="multiple"
            showArrow
            placeholder={t("auays:sele_ph.user")}
            value={executors}
            onChange={(selected) => {
              setFilterParams({ ...filterParams, executors: selected })
            }}
            maxTagCount={2}
            allowClear
            style={{ minWidth: 144 }}
            optionFilterProp="label"
          >
            {userList.map((user) => {
              const { userId, userName } = user
              return (
                <Option
                  key={userId}
                  value={userId}
                  label={`${userName}(${userId})`}
                >
                  {`${userName}(${userId})`}
                </Option>
              )
            })}
          </Select>
        </Col>
        <Col>
          <RangePicker
            value={rangeValue}
            onChange={(dates) => {
              if (dates === null) {
                return setFilterParams({
                  ...filterParams,
                  timeRange: null,
                })
              }
              const [start, end] = formatDateRange(dates)
              if (start && end) {
                setFilterParams({
                  ...filterParams,
                  timeRange: [start, end],
                })
              }
            }}
            allowClear
            placeholder={[t("auays:rpk_ph.start_date"), t("auays:rpk_ph.end_date")]}
          />
        </Col>
        <Col>
          <Button
            onClick={() => setSearchParams({ ...filterParams, offset: 0 })}
            type="primary"
            className="mr8"
          >
            {t("auays:btn.query")}
          </Button>
          <Button
            onClick={() =>
              setFilterParams({
                executors: [],
                timeRange: null,
                resultFlag: undefined,
              })
            }
            className="mr8"
          >
            {t("auays:btn.reset")}
          </Button>
          <Popconfirm
            title={t("auays:pop_title.confirm_export")}
            onConfirm={exportData}
            placement="bottomRight"
          >
            <Button className="mr8" loading={isExport} disabled={isExport}>{t("auays:btn.export")}</Button>
          </Popconfirm>
          <Button
            icon={<RedoOutlined className={styles.refreshIcon} />}
            onClick={() => refresh()}
          />
          <Button
            className={styles.settingIcon}
            icon={<SettingOutlined />}
            type="primary"
            onClick={() => showCustomColumnPanel()}
          />
        </Col>
      </Row>
    </div>
  )
}
