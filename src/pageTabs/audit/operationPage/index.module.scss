.operationContent {
  height: 100%;

  .searchHeader {
    display: flex;
    align-items: center;
    justify-content: flex-end;

    .refreshIcon {
      padding: 4px 4px;
    }

    .settingIcon {
      margin-left: 10px;
    }
  }

  .tableHeader {
    margin-bottom: 10px;

  }

  .tableWrapper {

    .customColumnTable {
      :global {
        .ant-table-container {
          overflow: auto scroll;
          max-height: calc(100vh - 200px);
        }

        .ant-table-thead>tr>th {
          // background-color: #fff;
          color: #667084;
        }

        .ant-table-tbody>tr>td {
          color: #0f244c;
        }

        .ant-table-expanded-row>td {
          background: #fff;
          padding: 0 0 !important;
        }
      }

      .resultState {
        display: flex;
        align-items: center;
        justify-content: flex-start;

        .iconStyle {
          margin-right: 4px;
          background: red;
          width: 8px;
          height: 8px;
          border-radius: 50%;
          display: inline-block;
        }

        .successState {
          background: green;
        }
      }

      .pagination {
        text-align: right;
        border-top: 1px solid #f0f0f0;
        padding-top: 10px;
      }

      .rowContent {
        background: #eef2f7;
        padding: 13px 54px;

        .operationList {
          width: 50%;
          padding-top: 10px;

          .operationItem {
            padding: 4px 0;

            :global {
              .ant-col {
                font-size: 12px;
                color: #667084 !important;
              }
            }

            .moreInfo {
              cursor: pointer;
              color: #3262ff !important;
              text-decoration: underline;
            }
          }
        }
      }
    }
  }
}