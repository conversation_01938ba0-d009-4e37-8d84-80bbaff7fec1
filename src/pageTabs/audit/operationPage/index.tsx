import React from 'react';
import classNames from 'classnames';
import { Layout, Breadcrumb } from 'antd';
import Watermark from '@pansy/react-watermark'
import { useSelector, useDispatch } from 'src/hook';
import { CustomColumnTable } from './CustomColumnTable';
import 'src/styles/layout.scss';
import styles from './index.module.scss';
import { useTranslation } from 'react-i18next';
import { 
  setOverviewPageState,
  setOverviewPageDetailParams 
} from 'src/pageTabs/audit/overview/overviewSlice'

const { Header, Content } = Layout

export const AuditOperationPage = () => {
  const { watermarkEffect } = useSelector((state: any) => state.login)
  const { t } = useTranslation()
  const dispatch = useDispatch()
  // watermark
  const { watermarkSetting, watermarkValue } = useSelector(
    (state: any) => state.login.userInfo,
  )
  // 渲染审计概览
  const gotoAuditView = () => {
    dispatch(setOverviewPageState(''))
    dispatch(setOverviewPageDetailParams({}))
  }

  return (
    <Layout className="cq-container">
      <Header className="breadcrumb-header" title={t("auays:bc_title.operation_record")}>
        <Breadcrumb className="breadcrumb" separator=''>
          <Breadcrumb.Item>{t("auays:bc_title.audit_analysis")}</Breadcrumb.Item>
          <Breadcrumb.Separator>|</Breadcrumb.Separator>
          <Breadcrumb.Item><span onClick={gotoAuditView}>{t("auays:bc_title.audit_overview")}</span></Breadcrumb.Item>
          <Breadcrumb.Separator>/</Breadcrumb.Separator>
          <Breadcrumb.Item>{t("auays:bc_title.operation_record")}</Breadcrumb.Item>
        </Breadcrumb>
      </Header>
      <Layout className="cq-main">
        <Content
          className={classNames('cq-content', { [styles.operationContent]: true })}
        >
          <CustomColumnTable />
          <Watermark
            text={watermarkValue?.length ? watermarkValue?.join(' ') : ''}
            pack={false}
            zIndex={99}
            rotate={20}
            visible={watermarkSetting}
            {...watermarkEffect}
          />
        </Content>
      </Layout>
    </Layout>
  )
}
