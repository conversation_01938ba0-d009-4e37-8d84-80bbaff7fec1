import React, { useMemo, useState } from 'react'
import { CommonCard } from './components/CommonCard'
import { ITargetCard, TargetCard } from './components/TargetCard'
import { Col, Row } from 'antd'
import { useRequest, useSelector, useDispatch } from 'src/hook'
import { getAppOperateCard } from 'src/api/getNewData'
import StatisCardFilter from './components/StatisCardFilter'
import moment from 'moment'
import { useTranslation } from 'react-i18next'
import { 
  setOverviewPageState,
  setOverviewPageDetailParams 
} from 'src/pageTabs/audit/overview/overviewSlice'

export const AppOperationCard = () => {
  const dispatch = useDispatch();
  const [unit, setUnit] = useState<string>('DAY')
  const [datePicker, setDatePicker] = useState<[any, any]>([moment().startOf('day'), moment().endOf('day')]);  // dateRange
  const { t } = useTranslation()
  const { locales } = useSelector((state: any) => state.login);

  const { data, loading: appOperationLoading, } = useRequest(() => {
    const params = {
      unit: unit || "CUSTOM",
      executeBeginMs: datePicker?.[0]?.valueOf(),
      executeEndMs: datePicker?.[1]?.valueOf(),
    }
    if (unit) {
      delete params.executeBeginMs
      delete params.executeEndMs
    }
    return getAppOperateCard(params)
  }, {
    refreshDeps: [unit, datePicker],
  });


  // 渲染审计分析次-审计概览-应用语句明细
  const gotoAuditViewStatementDetail = (params: any) => {
    dispatch(setOverviewPageState('app_statement_detail'))
    dispatch(setOverviewPageDetailParams(params))
  }

  // 跳转
  const toLink = (str: string) => {
    let state: any;
    const [startDate, endDate] = datePicker;
    let timeRange: any = []
    if (!startDate || !endDate) timeRange = undefined
    else timeRange = [datePicker?.[0]?.valueOf(), datePicker?.[1].valueOf()];
    switch (str) {
      case t("auays:app_ope_stats.total_execution_statements"):
        state = {
          timeRange
        };
        break;
      case t("auays:app_ope_stats.successful_execution_statements"):
        state = {
          timeRange,
          resultFlag: 1,
        };
        break;
      case t("auays:app_ope_stats.failed_execution_statements"):
        state = {
          timeRange,
          resultFlag: 0,
        };
        break;
    }
    gotoAuditViewStatementDetail(state)
  }

  const targetList = useMemo((): ITargetCard[] => {
    return [
      { count: data?.amount || 0, name: t("auays:app_ope_stats.total_execution_statements"), allowClick: true, onClick: () => toLink(t("auays:app_ope_stats.total_execution_statements")) },
      { count: data?.successAmount || 0, name: t("auays:app_ope_stats.successful_execution_statements"), allowClick: true, onClick: () => toLink(t("auays:app_ope_stats.successful_execution_statements")) },
      { count: data?.failureAmount || 0, name: t("auays:app_ope_stats.failed_execution_statements"), allowClick: true, onClick: () => toLink(t("auays:app_ope_stats.failed_execution_statements")), hoverType: 'YELLOW' }
    ]
  }, [data, datePicker, locales])

  return <Col span={7}>
    <CommonCard
      title={t("auays:card_title.app_ope_volume")}
      loading={appOperationLoading}
      tooltipContent={t("auays:tip_title.app_side_ope_volume")}
      style={{ height: '212px' }}
      extra={<StatisCardFilter
        size={'small'}
        defaultValue={{
          radioValue: unit,
          rangePickerValue: datePicker
        }}
        onChange={(values: any) => {
          setUnit(values.radioValue);
          setDatePicker(values.rangePickerValue);
        }}
      />}
    >
      <Row gutter={[16, 12]}>
        {targetList.map((item: ITargetCard) => <Col span={12} key={item.name}>
          <TargetCard {...item} style={{ height: '61px' }} />
        </Col>)}
      </Row>
    </CommonCard>
  </Col>
}
