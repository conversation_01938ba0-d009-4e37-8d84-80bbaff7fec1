import { Button, Checkbox, Drawer, Input, message, Spin, Tooltip } from "antd";
import React, { useContext, useEffect, useRef, useState } from "react";
import styles from './index.module.scss'
import { CheckboxChangeEvent } from "antd/es/checkbox";
import { useRequest } from "src/hook";
import { getAllAuditMetrics, postSaveAuditMetrics } from "src/api";
import { debounce } from "lodash";
import { AuditOverviewContext } from "./AuditOverviewContext";
import { baseCharts } from "./CustomChartDisplay";
import { ctrlCharts } from "src/hook/useAuditOverview";
import { useTranslation } from "react-i18next";
const CheckboxGroup = Checkbox.Group;
const searchIcon = <svg className="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" id="16804" width="12" height="12"><path d="M926.208 865.792l-156.672-156.672c52.736-65.536 83.968-148.992 83.968-239.616 0-211.968-172.032-384-384-384s-384 172.032-384 384 172.032 384 384 384c90.624 0 174.08-31.232 239.616-83.968l156.672 156.672c16.896 16.896 43.52 16.896 60.416 0 16.384-16.896 16.384-43.52 0-60.416z m-241.664-194.56c-1.536 1.024-3.072 2.56-4.096 3.584-1.536 1.536-2.56 2.56-3.584 4.096-53.76 51.712-126.976 83.456-207.36 83.456-164.864 0-298.496-133.632-298.496-298.496 0-164.864 133.632-298.496 298.496-298.496 164.864 0 298.496 133.632 298.496 298.496 0 80.896-31.744 153.6-83.456 207.36z" id="16805" fill="#4E5969"></path></svg>

export const AuditDisplayDrawer = (props: any) => {
  const { visible, setVisible } = props;
  const { t } = useTranslation()
  const { customList, setCustomList } = useContext(AuditOverviewContext)
  const [customData, setCustomData] = useState<any[]>([]);
  const [checkedList, setCheckedList] = useState<any[]>([])
  const inputRef = useRef<any>(null)
  const builtInCharts = [...ctrlCharts, ...baseCharts] //所有的内置图表

  const onClose = () => {
    setVisible(false);
  };

  useEffect(() => {
    if (visible) {
      run()
    }
    else if (inputRef.current) {
      inputRef.current.setValue('')
    }
  }, [visible])

  useEffect(() => {
    if (visible) {
      setCheckedList(customList)
    }
  }, [customList, visible])

  // 获取所有审计指标
  const { loading, run } = useRequest(getAllAuditMetrics, {
    manual: true,
    formatResult: (data: any) => {
      setCustomData(data)
      const newList: any[] = []
      data.map((item: any) => {
        if (checkedList.filter((i: any) => i.id === item.id).length === 0) {
          newList.push({ ...item })
        }
      })
      setCheckedList([...checkedList, ...newList])
      return data
    }
  })

  // 保存审计概览页面展示的审计指标
  const { run: saveAuditMetrics } = useRequest(postSaveAuditMetrics, {
    manual: true,
    onSuccess: () => {
      message.success(t("auays:msg.custom_chart_display_modified_successfully"))
    }
  })

  const onChange = (e: CheckboxChangeEvent) => {
    const target = e.target
    const { value: id, checked: show } = target
    const checkedLength = checkedList.filter((item: any) => item.show).length // 7个默认指标一直展示
    if (checkedLength === 5 && show) {
      message.warning(t("auays:msg.max_five_custom_chart"))
      return
    }
    const newList = checkedList.map((item: any) => {
      if (item.id === id) return { ...item, show }
      else return { ...item }
    })
    setCheckedList([...newList])
  };

  const onOk = () => {
    const ids = checkedList.filter((item: any) => item.show && (item.id >= 0)).map((i: any) => i.id)
    saveAuditMetrics(ids)
    setCustomList([...checkedList.filter((item: any) => item.show)])
    setCustomData([])
    onClose()
  }

  // 模糊搜索
  const onSearch = debounce((e) => {
    const value = e.target?.value
    if (value) {
      run({ name: value })
    }
    else {
      run()
    }
  }, 500)

  // 自定义图表模糊搜索
  const onInputChange = (e: any) => {
    const realEle = e.nativeEvent
    onSearch(realEle)
  }


  return (
    <Drawer
      title={t("auays:drawer_title.charts_display_settings")}
      placement="right"
      onClose={onClose}
      visible={visible}
      width={308}
      className={styles.AuditDisplayDrawer}
      closable
      footer={
        <div className={styles.drawerFooter}>
          <Button className={styles.cancelBtn}>{t("auays:btn.cancel")}</Button>
          <Button type='primary' onClick={onOk}>{t("auays:btn.confirm")}</Button>
        </div>
      }
    >
      <Spin spinning={loading}>
        <div className={styles.baseCharts}>
          <div className={styles.drawerTitle}>{t("auays:div_lbl.system_default_metrics")}</div>
          <CheckboxGroup value={builtInCharts.map((item: any) => item.id)} disabled={true}>
            {
              builtInCharts.map((item: any) => <Checkbox value={item.id} key={item.id}>
                {t(item.title)}
              </Checkbox>)
            }
          </CheckboxGroup>
        </div>
        <div className={styles.customCharts}>
          <Input
            ref={inputRef}
            placeholder={t("auays:inp_ph.search_custom_metrics_name")}
            className={styles.customInput}
            suffix={searchIcon}
            onChange={onInputChange}
          />
          <div className={styles.drawerTitle}>{t("auays:div_lbl.custom_metrics")}</div>
          <CheckboxGroup value={checkedList.filter((item: any) => item.show).map((item: any) => item.id)}>
            {
              customData.map((item: any) => <Checkbox value={item.id} key={item.id} onChange={onChange}>
                {item.name.length > 15 ? <Tooltip title={item.name}>
                  {item.name}
                </Tooltip> : item.name}
              </Checkbox>
              )
            }
          </CheckboxGroup>
        </div>
      </Spin>
    </Drawer>
  );
}