import React, { useEffect } from "react";
import { Chart, Util } from '@antv/g2';
import { Spin } from 'antd';
import { useTranslation } from "react-i18next";
import { useSelector } from "src/hook";

interface IProps {
  data: any[],
  loading: any,
  growth: string,
}

const ActiveUserChart = (props: IProps) => {
  const { data, loading, growth } = props;
  const { t } = useTranslation()
  const { locales } = useSelector((state: any) => state.login);
  const chartColorEum: any = {
    [t("auays:user_chart.offline")]: '#BEBFC5',
    [t("auays:user_chart.online")]: '#3357FF',
  }

  const chartRender = (container: any, chartData: any, growthData: string) => {
    const chart = new Chart({
      container: container,
      height: 132,
      autoFit: true,
      padding: [8, 12]
    });
    chart.data(data);

    chart.coordinate('theta', {
      radius: 1,
      innerRadius: 0.75
    });

    const customItemtpl = `<div style="margin-bottom: 10px;">
      ${data.map((item: any) => `<div style="margin-bottom:10px;">
          <span style="background-color:${chartColorEum[item.type]};" class="g2-tooltip-marker"></span>
          <span class="g2-tooltip-name">${item.type}</span>:<span class="g2-tooltip-value">${item.value}%</span>
        </div>
      `).join('')}
    </div>`

    chart.tooltip({
      showMarkers: false,
      showTitle: false,
      itemTpl: customItemtpl
    });

    chart.legend(false)
    chart.annotation().text({
      position: ['50%', '54%'],
      content: (growthData ?? 0) + '%',
      style: {
        fontSize: 18,
        fill: '#1A1A1A',
        fontWeight: 500,
        textBaseline: 'bottom',
        textAlign: 'center'
      },
      offsetY: -6,
    });

    chart.annotation().text({
      position: ['50%', '54%'],
      content: locales === 'en' ?
        `${t("auays:chart_cont.month_growth")}\n${t("auays:chart_cont.month_growth_br")}` :
        t("auays:chart_cont.month_growth"),
      style: {
        fontSize: locales === 'en' ? 10 : 14,
        fill: '#6C7293',
        textAlign: 'center'
      },
      offsetY: 6,
    });

    chart
      .interval()
      .adjust('stack')
      .position('value')
      .color('type', ['#3357FF', '#BEBFC5'])
      .state({
        active: {
          style: (element) => {
            const shape = element.shape;
            return {
              matrix: Util.zoom(shape, 1.1),
            }
          }
        }
      })
      .style({
        lineWidth: 4,
        stroke: '#fff',
      })
    chart.interaction('tooltip');

    chart.render();
    return chart;
  }

  useEffect(() => {
    const chart = chartRender("activeUserContainer", data, growth);
    return () => chart.destroy();
  }, [growth, data, locales]);


  return (
    <div style={{ height: '100%' }}>
      <Spin spinning={loading}>
        <div id="activeUserContainer"></div>
      </Spin>
    </div>
  )
};
export default ActiveUserChart;

