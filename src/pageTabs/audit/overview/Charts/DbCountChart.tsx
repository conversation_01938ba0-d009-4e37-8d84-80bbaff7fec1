import { Chart } from '@antv/g2'
import { Spin } from 'antd'
import React, { useContext, useEffect, useMemo, useState } from 'react'
import { getAuditDBTypeAmountUnit, SqlCountByDbType } from 'src/api'
import { useRequest, useDispatch } from 'src/hook'
import chartStyles from './chart.module.scss'
import { EmptyChart } from './EmptyChart'
import { ChartColors } from './util'
import AutoDisplayTable from '../components/AutoDisplayTable'
import _ from 'lodash'
import { AuditOverviewContext } from '../AuditOverviewContext'
import { useTranslation } from 'react-i18next'
import { 
  setOverviewPageState,
  setOverviewPageDetailParams 
} from 'src/pageTabs/audit/overview/overviewSlice'

interface ChartData {
  item: string
  count: number
  percent: number
}

const getAdaptedchartData = (data: SqlCountByDbType[]): ChartData[] => {
  const total = data.reduce((prev, curr) => prev + Number(curr.amount), 0)
  return data.map(({ dbType, amount }) => ({
    item: dbType,
    count: amount,
    percent: amount / total,
  })).filter(d => d.item !== null)
}

export const DbCountChart = () => {
  const dispatch = useDispatch();
  const { chartsCtrlList } = useContext(AuditOverviewContext);
  const displayType = useMemo(()=>{
    return chartsCtrlList.find((item: any) => item.id === -7)?.displayType || 'TABLE'
  }, [chartsCtrlList])

  const { t } = useTranslation()
  const [tableColumns] = useState<any[]>([
    { title: t("auays:tb_title.database_type"), dataIndex: 'item', key: 'item', ellipsis: true, width: 120 },
    { title: t("auays:tb_title.statement_count"), dataIndex: 'count', key: 'count', ellipsis: true, width: 120, render: (count: number) => count + t("auays:unit.items") },
    { title: t("auays:tb_title.percentage"), dataIndex: 'percent', key: 'percent', ellipsis: true, width: 120, render: (per: any) =>  Math.round(Number(per) * 1000) / 10 + '%' }
  ]) // 表格表头
  const [tableDataSource, setTableDataSource] = useState<any[]>([]) // 表格数据源

  const { data = [], loading } = useRequest(getAuditDBTypeAmountUnit, {
    formatResult: (data) => getAdaptedchartData(data),
  })

  // 渲染语句明细
  const gotoStatementDetail = (params: any) => {
    dispatch(setOverviewPageState('statement_detail'))
    dispatch(setOverviewPageDetailParams(params))
  }

  const renderSqlCountChart = (container: string, data: ChartData[]) => {
    data = data.sort((a, b) => b.count - a.count)
    const chart = new Chart({
      container,
      autoFit: true,
    })

    chart.data(data)
    chart.coordinate('theta', {
      radius: 0.75,
      innerRadius: 0.55,
    })

    // 自定义图例
    chart.legend('item', {
      // todo: 增加图表右 padding，目前 legend 太靠近边缘
      position: 'right',
      custom: true, // 告诉 G2 要使用自定义图例
      items: data.map((obj, index) => {
        return {
          name: obj.item,
          value: obj.percent,
          marker: {
            symbol: 'square', // marker 形状
            style: {
              r: 6, // marker 图形半径
              fill: ChartColors[index % ChartColors.length], // marker 颜色，使用默认颜色，同图形对应
            },
          },
        }
      }),
      itemValue: {
        style: {
          fill: '#999',
        },
        formatter: (val) =>  Math.round(Number(val) * 1000) / 10 + '%',
      },
    })

    chart
      .interval()
      .adjust('stack')
      .position('percent')
      .color('item', ChartColors)
      .label('percent', () => ({
        content: (data) => data.item,
        offset: 24,
      }))
      .tooltip('item*count', (item, count) => {
        return {
          name: item,
          value: count,
        }
      })
    chart.tooltip({
      showTitle: false,
      showMarkers: false,
      itemTpl:
        '<li class="g2-tooltip-list-item"><span style="background-color:{color};" class="g2-tooltip-marker"></span>{name}：{value} 条语句</li>',
    })

    chart.interaction('element-active')
    chart.on('plot:click', (ev: any) => {
      if (ev?.data?.data) {
        const params =  {
          dbTypes: [ev?.data?.data?.item],
          timeRange: undefined
        }
        gotoStatementDetail(params)
      }
    })
    //TODO:
    chart.render()
    return chart
  }

  useEffect(() => {
    if (!data || data.length <= 0) return
    // 图表
    if (displayType === 'CHART') {
      const chart = renderSqlCountChart('db-count', data)
      return () => chart.destroy()
    }
    // 表格
    else {
      setTableDataSource(data)
    }
  }, [data, displayType])
  
  return (
    <Spin spinning={loading}>
      <div id="db-count" className={chartStyles.dbCount}>
        {
          _.isEmpty(data) || loading ? <EmptyChart></EmptyChart> :
            displayType === 'CHART' ? <></> :
              <AutoDisplayTable
                columns={tableColumns}
                dataSource={tableDataSource}
                pagination={false}
                scroll={{ x: 'fit-content', y: 'calc(100% - 48px)' }}
              />
        }
      </div>
    </Spin>
  )
}
