import React, { useEffect, useState, useMemo, useContext } from 'react'
import { Spin, List , Radio,  DatePicker, Select } from 'antd'
import { useRequest, useDispatch } from 'src/hook'
import { getSql } from 'src/api/getNewData'
import chartStyles from './chart.module.scss'
import classnames from 'classnames'
import dayjs, { Dayjs } from 'dayjs'
import { SqlTypes } from "src/types/types";
import AutoDisplayTable from '../components/AutoDisplayTable'
import { AuditOverviewContext } from '../AuditOverviewContext'
import { useTranslation } from 'react-i18next'
import { 
  setOverviewPageState,
  setOverviewPageDetailParams 
} from 'src/pageTabs/audit/overview/overviewSlice'

export const SlowSqlChart = () => {
  const dispatch = useDispatch();
  const { chartsCtrlList, onChartExportParamsChange } = useContext(AuditOverviewContext);
  const displayType = useMemo(()=>{
    return chartsCtrlList.find((item: any) => item.id === -4)?.displayType || 'TABLE'
  }, [chartsCtrlList])

  const { t } = useTranslation();
  const [data, setData] = useState<any>();
  const [dataSource, setDataSource] = useState<any>();
  const [timeRange, setTimeRange] = useState<1 | 7 | 30 | any>(7)
  const [rangePickerTimes, setRangePickerTimes] = useState<string[] | null>(
    [dayjs().subtract(6 , 'day').format('YYYY-MM-DD'),
    dayjs().endOf('d').format('YYYY-MM-DD')]
  )
  const [operateType,setOperateType] = useState<string[]>()
  const SqlTypeOptions = SqlTypes.map((type) => ({ label: type, value: type }));

  const [tableColumns] = useState<any[]>([
    { title: t("auays:tb_title.serial_number"), dataIndex: 'index', key: 'index', ellipsis: true, width: 60, render: (_v: any, _r: any, index: number) => index + 1 },
    { title: t("auays:tb_title.account"), dataIndex: 'executor', key: 'executor', ellipsis: true, width: 100 },
    { title: t("auays:tb_title.user_name"), dataIndex: 'executorName', key: 'executorName', ellipsis: true, width: 120 },
    { title: t("auays:tb_title.operation_type"), dataIndex: 'sqlType', key: 'sqlType', ellipsis: true, width: 120 },
    { title: t("auays:tb_title.ope_statement"), dataIndex: 'executeSql', key: 'executeSql', ellipsis: true, width: 270 },
    { title: t("auays:tb_title.time_consume"), dataIndex: 'executeCostSecond', key: 'executeCostSecond', ellipsis: true, width: 100, render: (value: any) =>  value + 's' }
  ]) // 表格表头

  const { loading, run } = useRequest(getSql, { manual: true });

  const setListDataFormat = (data: any) => {
    let arr: any = [];
    data.map((item: any) => {
      arr.push({
        sqlId: item.id,
        executor: item.executor,  // 用户名
        executorName: item.executorName,  // 账号
        sqlType: item.sqlType,
        executeSql: item.executeSql,
        executeCostSecond: item.executeCostSecond,
      })
    })
    setDataSource(arr);
  }

  const rangeValue = useMemo(() => {
    if (rangePickerTimes === null) {
      return null
    } else {
      const range = rangePickerTimes.map((timestamp) => dayjs(timestamp)) as [
        Dayjs,
        Dayjs,
      ]
      return range
    }
  }, [rangePickerTimes])

  useEffect(() => {
    let params 
    params = {
      sqlTypes:operateType
    }
    if (rangePickerTimes) {
      const startTimes = Number(dayjs(rangePickerTimes[0]).startOf('d').format('x'));
      const endTimes = Number(dayjs(rangePickerTimes[1]).endOf('d').format('x'));
     params = {
        ...params,
        executeBeginMs: startTimes,
        executeEndMs: endTimes,
      }
    }
    
    onChartExportParamsChange(-4, params)
    run(params).then(res => {
      setData(res);
    })
  }, [operateType, rangePickerTimes]);

  useEffect(() => {
    if (data) {
      setListDataFormat(data);
    }
  }, [data]);

  // 渲染语句明细
  const gotoStatementDetail = (params: any) => {
    dispatch(setOverviewPageState('statement_detail'))
    dispatch(setOverviewPageDetailParams(params))
  }

  return (
    <Spin spinning={loading}>
      <div>
       <div className={chartStyles.toolbar}>
          <Radio.Group
            buttonStyle="solid"
            size="small"
            value={timeRange}
            onChange={(e) => {
              setTimeRange(e.target.value);
              if (e.target.value === 1) {
                setRangePickerTimes([dayjs().format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')])
              } else if (e.target.value === 7) {
                setRangePickerTimes([dayjs().subtract(6, 'day').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')])
              } else {
                setRangePickerTimes([dayjs().subtract(29, 'day').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')])
              }
            }}
          >
            <Radio.Button value={1}>{t("auays:filter_time.today")}</Radio.Button>
            <Radio.Button value={7}>{t("auays:filter_time.seven_days")}</Radio.Button>
            <Radio.Button value={30}>{t("auays:filter_time.thirty_days")}</Radio.Button>
          </Radio.Group>
         <div>
            <DatePicker.RangePicker
              style={{ marginLeft: 10 }}
              //@ts-ignore
              value={rangeValue}
              onChange={(dates, dateStrings) => {
                if (!dateStrings[0]) {
                  setRangePickerTimes(null)
                  setTimeRange(7)
                } else {
                  const diffDays = dayjs(dateStrings[1]).diff(
                    dateStrings[0],
                    'day',
                  )
                  if(dayjs().isSame(dayjs(dateStrings[1]),'day')) {
                    switch(diffDays) {
                      case 0: setTimeRange(1); break;
                      case 7: setTimeRange(7); break;
                      case 30: setTimeRange(30); break;
                      default: setTimeRange(0); break;
                    }
                  }
                  setRangePickerTimes(dateStrings)
                }
              }}
              getPopupContainer={(triggerNode: any) => triggerNode.parentNode}
            />
            <Select
              showSearch
              mode="multiple"
              showArrow
              maxTagCount={1}
              placeholder={t("auays:sele_ph.operation_type")}
              options={SqlTypeOptions}
              style={{ marginLeft: 10, width: 200 }}
              onChange={(values: string[]) => setOperateType(values)}
              getPopupContainer={(triggerNode: any) => triggerNode.parentNode}
            />
          </div> 
        </div>
        {
          displayType === 'CHART' ?
            <List
              itemLayout="horizontal"
              dataSource={dataSource}
              renderItem={(item: any, index) => (
                <List.Item>
                  <List.Item.Meta
                    avatar={
                      <div className={chartStyles.listPre}>
                        <span
                          className={classnames(chartStyles.fs28, chartStyles.listImgWrap, chartStyles.listPreMarginRight)}
                          style={
                            {
                              backgroundColor:
                                index === 0 ?
                                  "#D96B67"
                                  : index === 1 ?
                                    "#E2C655"
                                    : index === 2 ?
                                      "#84DCE6"
                                      : "#E2E2E2",
                              color:
                                index < 3 ?
                                  "#FFFFFF"
                                  : "#3D3D3D"
                            }
                          }
                        >{++index}</span>
                        <span className={chartStyles.listPreName}>{item.executorName + "(" + item.executor + ")"}</span>
                      </div>
                    }
                    title={
                      <div style={{ cursor: "pointer" }} onClick={() => gotoStatementDetail({id: item.sqlId})}>

                        <p className={chartStyles.content1}>
                          <span className={chartStyles.sqlType}>&nbsp;&nbsp;{item.sqlType}&nbsp;&nbsp;</span>
                          {item.executeSql}
                        </p>
                        <span className={chartStyles.sqlCost}>{(item.executeCostSecond) + "s"}</span>
                      </div>
                    }
                  />
                </List.Item>
              )}
            /> :
            <AutoDisplayTable
              className={chartStyles.sqlCount}
              columns={tableColumns}
              dataSource={dataSource}
              pagination={false}
              scroll={{ x: 'fit-content', y: 'calc(100% - 48px)' }}
            />
        }
      </div>
    </Spin>
  )
}
