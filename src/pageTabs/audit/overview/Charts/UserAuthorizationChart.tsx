import React, { useContext, useEffect, useMemo, useState } from 'react'
import { Spin } from 'antd'
import dayjs from 'dayjs'
import { useRequest, useSelector, useDispatch } from 'src/hook'
import { getUerAuthorization } from 'src/api/getNewData'
import { Chart } from '@antv/g2';
import { judgeIsNoData } from '../../utils'
import { EmptyChart } from './EmptyChart'
import AutoDisplayTable from '../components/AutoDisplayTable'
import { formatColumns, formatDataSource, TIME_UNIT_EUM } from './util'
import { cloneDeep } from 'lodash'
import { AuditOverviewContext } from '../AuditOverviewContext'
import { useTranslation } from 'react-i18next'
import { 
  setOverviewPageState,
  setOverviewPageDetailParams 
} from 'src/pageTabs/audit/overview/overviewSlice'

let chart: any;
export const UserAuthorizationChart = (props: any) => {
  const dispatch = useDispatch();
  const { chartsCtrlList } = useContext(AuditOverviewContext);
  const displayType = useMemo(()=>{
    return chartsCtrlList.find((item: any) => item.id === -8)?.displayType || 'TABLE'
  }, [chartsCtrlList])

  const { t } = useTranslation();
  const { locales } = useSelector(state => state.login)
  const [tableColumns, setTableColumns] = useState<any[]>([]) // 表格表头
  const [tableDataSource, setTableDataSource] = useState<any[]>([]) // 表格数据源

  const { data, loading } = useRequest(getUerAuthorization, { manual: false });

  const renderUserAuthorizationChart = (container: any, data: any) => {
    const chart = new Chart({
      container,
      autoFit: true,
      height: 300,
      theme: {
        maxColumnWidth: 40,
      },
    });

    // .reverse() 实现和缩略图的联动，初始化缩略图为反向
    const newData = cloneDeep(data)
    chart.data(newData.reverse());

    chart.scale('amount', {
      nice: true
    });

    chart.tooltip({
      shared: true,
      showMarkers: false,
    });

    chart.option("slider", {});

    // 渲染操作记录
    const gotoOperateRecord = (params: any) => {
      dispatch(setOverviewPageState('operate_record'))
      dispatch(setOverviewPageDetailParams(params))
    }

    chart
      .interval()
      .adjust('stack')
      .position('unit*amount')
      .color('amountType', ['#e87a77', '#4984f3'])

    // 点击事件，这里仅对不可点击的时候生效，做点击到最后一层跳转到对应语句明细功能
    chart.on("element:click", (ev: any) => {
      const { amountType, unit } = ev?.data?.data || {};

      const timeRange = [
        dayjs(unit).startOf("day").valueOf(),
        dayjs(unit).endOf("day").valueOf(),];

      const state = {
        timeRange: timeRange,
        operates: [amountType === "pass" ? t("auays:chart_lbl.approved") : t("auays:chart_lbl.rejected")],
      }

      gotoOperateRecord(state)

    })

    chart.interaction('active-region');

    chart.render();
    return chart;
  }


  useEffect(() => {
    if (judgeIsNoData(data)) return;
    // 图表
    if (displayType === 'CHART') {
      chart = renderUserAuthorizationChart("userAuthorizationContainer", data);

      return () => {
        chart && chart.destroy();
        chart = null;
      };
    }
    // 表格
    else {
      const newData = cloneDeep(data).sort((a: any, b: any) => a.unit > b.unit ? 1 : -1)
      const columns = formatColumns(newData, t(TIME_UNIT_EUM[0]), 'amountType')
      const dataSource = formatDataSource(newData, 'amountType')
      setTableColumns(columns)
      setTableDataSource(dataSource)
    }
  }, [data, displayType, locales]);

  return (
    <Spin spinning={loading}>
      <div id="userAuthorizationContainer">
        {
          judgeIsNoData(data) ? <EmptyChart></EmptyChart> :
            displayType === 'CHART' ? <></> :
              <AutoDisplayTable
                columns={tableColumns}
                dataSource={tableDataSource}
                pagination={false}
                scroll={{ x: 'fit-content', y: 'calc(100% - 48px)' }}
              />
        }
      </div>
    </Spin>
  )
}
