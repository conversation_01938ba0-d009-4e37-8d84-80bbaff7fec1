import React, { useEffect, useState, useMemo, useContext } from 'react'
import { Chart } from '@antv/g2'
import { DatePicker, Radio, Spin } from 'antd'
import { getAuditUserAmountUnit } from 'src/api'
import { useRequest, useSelector } from 'src/hook'
import chartStyles from './chart.module.scss'
import { EmptyChart } from './EmptyChart'
import { getUnit, getUnitBase, getUnixTimeRange, getCustomUnitBase, formatColumns, formatDataSource, TIME_UNIT_EUM } from './util'
import DataSet from '@antv/data-set'
import dayjs, { Dayjs } from 'dayjs'
import _ from 'lodash'
import AutoDisplayTable from '../components/AutoDisplayTable'
import { AuditOverviewContext } from '../AuditOverviewContext'
import { useTranslation } from 'react-i18next'

export const UserCountChart = () => {
  const { chartsCtrlList, onChartExportParamsChange } = useContext(AuditOverviewContext);
  const { t } = useTranslation()
  const { locales } = useSelector(state => state.login)
  const displayType = useMemo(()=>{
    return chartsCtrlList.find((item: any) => item.id === -6)?.displayType || 'TABLE'
  }, [chartsCtrlList])
  const [timeRange, setTimeRange] = useState<1 | 7 | 30 | any>(7)
  const [rangePickerTimes, setRangePickerTimes] = useState<string[] | null>([
    dayjs().subtract(6, 'day').format('YYYY-MM-DD'),
    dayjs().endOf('d').format('YYYY-MM-DD')
  ])
  const [tableColumns, setTableColumns] = useState<any[]>([]) // 表格表头
  const [tableDataSource, setTableDataSource] = useState<any[]>([]) // 表格数据源

  const { data = [], loading } = useRequest(
    () => {
      let defaultParams = {
        unit: getUnit(timeRange),
        ...getUnixTimeRange(timeRange),
      }
      if (rangePickerTimes) {
        const startTimes =  Number(dayjs(rangePickerTimes[0]).startOf('d').format('x'));
        const endTimes = Number(dayjs(rangePickerTimes[1]).endOf('d').format('x'));
        defaultParams = {
          ...defaultParams,
          beginTimeMs:startTimes,
          endTimeMs: endTimes,
        }
      }
      onChartExportParamsChange(-6, {
        ...defaultParams,
        alias: t(TIME_UNIT_EUM[timeRange])
      })
      return getAuditUserAmountUnit(defaultParams)
    },
    {
      formatResult: (data) => {
        let baseUnit = getUnitBase(timeRange)
        
        if (rangePickerTimes) {
          baseUnit = getCustomUnitBase(
            rangePickerTimes[0],
            rangePickerTimes[1],
            timeRange === 1 ? 'YYYY-MM-DD HH:mm:ss' : 'YYYY-MM-DD 00:00:00'
          )
        }
        const rows = new DataSet.DataView()
          .source(
            data
              .map(({ ...rest }) => ({ ...rest, notUnitBase: true }))
              .concat(baseUnit),
          )
          // 填充刻度，例如值为 2021-02-8 00:00:00，unit 为 DAY，需要填充之前 timeRange 天的 unit string
          .transform({
            // 补全行
            type: 'fill-rows',
            groupBy: ['notUnitBase'],
            orderBy: ['unit'],
            fillBy: 'order',
          })
          .transform({
            // 补全列
            type: 'impute',
            field: 'amount', // 待补全字段
            groupBy: ['notUnitBase'], // 分组字段集（传空则不分组）
            method: 'value', // 补全为常量
            value: 0,
          })
          .rows.filter(({ notUnitBase }) => notUnitBase)
          .map(({ unit = '', ...rest }) => ({
            unit: timeRange === 1 ? new Date(unit).getHours() : unit,
            ...rest,
          }))
        return rows
      },
      refreshDeps: [timeRange, rangePickerTimes],
    },
  )

  const renderUserCountChart = (container: string, data: any[]) => {
    const chart = new Chart({
      container,
      autoFit: true,
    })

    chart.data(data)
    chart.scale({
      unit: {
        range: [0.05, 0.9],
        formatter: (v) => (v < 25 ? v + ' ' + t("auays:time_unit.hour") : dayjs(v).format('YYYY-MM-DD')),
      },
      amount: {
        alias: t("auays:chart_als.number_of_users"),
        nice: true,
      },
    })

    chart.tooltip({
      showMarkers: false,
    })
    chart.interaction('active-region')
    chart.interval().position('unit*amount').color('#989ffb')
    chart.render()
    return chart
  }

  useEffect(() => {
    if (!data || data.length <= 0) return
    // 图表
    if (displayType === 'CHART') {
      const chart = renderUserCountChart('user-count', data)
      return () => chart.destroy()
    }
    // 表格
    else {
      const sortRule = (a: any, b: any): number => {
        const rule = timeRange === 1 ? Number(a.unit) < Number(b.unit) : a.unit < b.unit
        return rule ? -1 : 1
      }
      let newData: any[] = data.sort(sortRule)
        .map((item: any) => {
          const unit = timeRange === 1 ? (item?.unit + t("auays:time_unit.hour")) : dayjs(item?.unit).format('YYYY-MM-DD')
          return { ...item, unit, colStr: t("auays:chart_als.number_of_users") }
        })
      const columns = formatColumns(newData, t(TIME_UNIT_EUM[timeRange]), 'colStr')
      const dataSource = formatDataSource(newData, 'colStr')
      setTableColumns(columns)
      setTableDataSource(dataSource)
    }
  }, [data, displayType, locales])

  const rangeValue = useMemo(() => {
    if (rangePickerTimes === null) {
      return null
    } else {
      const range = rangePickerTimes.map((timestamp) => dayjs(timestamp)) as [
        Dayjs,
        Dayjs,
      ]
      return range
    }
  }, [rangePickerTimes])

  return (
    <Spin spinning={loading}>
      <div className={chartStyles.userCountWrapper}>
        <div className={chartStyles.toolbar}>
          <Radio.Group
            buttonStyle="solid"
            size="small"
            value={timeRange}
            onChange={(e) => {
              setTimeRange(e.target.value);
              if (e.target.value === 1) {
                setRangePickerTimes([dayjs().format('YYYY-MM-DD'),dayjs().format('YYYY-MM-DD')])
              }else if (e.target.value === 7) {
                setRangePickerTimes([dayjs().subtract(6,'day').format('YYYY-MM-DD'),dayjs().format('YYYY-MM-DD')])
              }else {
                setRangePickerTimes([dayjs().subtract(29,'day').format('YYYY-MM-DD'),dayjs().format('YYYY-MM-DD')])
              }
            }}
          >
            <Radio.Button value={1}>{t("auays:filter_time.today")}</Radio.Button>
            <Radio.Button value={7}>{t("auays:filter_time.seven_days")}</Radio.Button>
            <Radio.Button value={30}>{t("auays:filter_time.thirty_days")}</Radio.Button>
          </Radio.Group>
          <div>
            <DatePicker.RangePicker
              style={{ marginLeft: 10 }}
              //@ts-ignore
              value={rangeValue}
              onChange={(dates, dateStrings) => {
                if (!dateStrings[0]) {
                  setRangePickerTimes(null)
                  setTimeRange(7)
                } else {
                  const diffDays = dayjs(dateStrings[1]).diff(
                    dateStrings[0],
                    'day',
                  )
                  if(dayjs().isSame(dayjs(dateStrings[1]),'day')) {
                    switch(diffDays) {
                      case 0: setTimeRange(1); break;
                      case 7: setTimeRange(7); break;
                      case 30: setTimeRange(30); break;
                      default: setTimeRange(0); break;
                    }
                  }
                  setRangePickerTimes(dateStrings)
                }
              }}
              getPopupContainer={(triggerNode: any) => triggerNode.parentNode}
              />
          </div>
        </div>
        <div id="user-count" className={chartStyles.userCount}>
          {
            _.isEmpty(data) || loading ? <EmptyChart></EmptyChart> :
              displayType === 'CHART' ? <></> :
                <AutoDisplayTable
                  columns={tableColumns}
                  dataSource={tableDataSource}
                  pagination={false}
                  scroll={{ x: 'fit-content', y: 'calc(100% - 48px)' }}
                />
          }
        </div>
      </div>
    </Spin>
  )
}
