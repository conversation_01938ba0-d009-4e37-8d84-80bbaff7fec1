.card {
  box-shadow: 0px 8px 15px 0px rgba(149, 156, 182, 0.15);
  border-radius: 4px;
  border: 1px solid #eaebf0;
}

.sqlCountWrapper {
  height: 45vh;

  .sqlCount {
    height: calc(45vh - 56px);
  }
}

.userCountWrapper {
  height: 45vh;

  .userCount {
    height: calc(45vh - 60px);
  }
}

.dbCount {
  height: 35vh;
}

.executionTimeWrapper {
  height: 55vh;

  .executionTime {
    height: calc(55vh - 48px);
  }
}

.toolbar {
  padding-bottom: 24px;
  display: flex;
  justify-content: space-between;

  :global {
    .ant-radio-button-wrapper {
      &:hover {
        color: #4567fb;
      }
    }

    .ant-radio-group-solid .ant-radio-button-wrapper-checked {
      background-color: #2f57fb;

      &:hover {
        background-color: #4567fb;
      }
    }
  }
}

.empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 90%;
}

.userAuthorizationWrapper {
  height: 75vh;

  .userAuthorization {
    // margin-top: 5vh;
    height: calc(75vh - 60vh);
  }
}

.slowSqlWrapper {
  height: 75vh;
  // .slowSql {
  //   height: calc(75vh - 48px);
  // }
}

.userOperationWrapper {
  height: 10vh;

  .userOperation {
    // margin-top: 5vh;
    height: calc(10vh - 5vh);
  }
}

.activeUserWrapper {

  // height: 10vh;
  width: 100% !important;

  .activeUser {
    // height: calc(10vh - 2vh);
    // width: 100%;
    // padding-top: 0.2vh;
    // padding-bottom: 0.2vh;
  }
}

.userImgWrap {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: #fff;
  background-color: var(--primary-color);

  &:hover {
    color: #fff !important;
  }
}

.listImgWrap {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: #fff;
  background-color: #E2E2E2;
}

.fs28 {
  width: 28px;
  height: 28px;
  border-radius: 50%;
}

.listPre {
  display: flex;
}

.listPreMarginRight {
  margin-right: 12px;
}

.listPreName {
  margin-left: 12px;
  padding-top: 4px;
}

.content1 {
  display: inline-flex;
  width: 72%;
  text-overflow: ellipsis;
  overflow: hidden;
  word-break: break-all;
  white-space: nowrap;
  cursor: pointer;
}

.sqlType {
  color: #0256FF;
}

.sqlCost {
  color: #0256FF;
  font-size: 18px;
  line-height: 20px;
  font-weight: bold;
  float: right;
}

.bottonStyle {
  border: 0;
  letter-spacing: -2;
}

.mulSelect {
  :global {
    .ant-select-selector {
      width: calc(100% - 4px);
    }

    .ant-select-selection-item {
      max-width: 45%;
    }
  }
}
