import { ColumnsType } from 'antd/lib/table';
import dayjs from 'dayjs'
import { AuditUnit, AuditSqlParams } from 'src/api'

export const getUnixTimeRange = (
  days: 1 | 7 | 30,
): { beginTimeMs: number; endTimeMs: number } => {
  const date = new Date()
  const y = date.getFullYear()
  const m = date.getMonth()
  const d = date.getDate()
  const Today = {
    beginTimeMs: Number(new Date(y, m, d)),
    endTimeMs: Number(new Date(y, m, d, 24)) - 1,
  }
  const Last7Days = {
    beginTimeMs: Number(new Date(y, m, d - 6, 0)),
    endTimeMs: Number(new Date(y, m, d, 24)) - 1,
  }
  const Last30Days = {
    beginTimeMs: Number(new Date(y, m, d - 29, 0)),
    endTimeMs: Number(new Date(y, m, d, 24)) - 1,
  }
  switch (days) {
    case 30:
      return Last30Days
    case 7:
      return Last7Days
    case 1:
    default:
      return Today
  }
}

// timeRange EUM
export const TIME_UNIT_EUM: { [key: number]: string } = {
  1: "auays:filter_time.today",
  7: "auays:filter_time.seven_days",
  30: "auays:filter_time.thirty_days",
  0: "auays:filter_time.date"
}

export const getUnit = (days: 1 | 7 | 30 | 0): AuditUnit => {
  switch (days) {
    case 30:
      return 'DAY'
    case 7:
      return 'DAY'
    case 1:
      return 'HOUR'
    case 0:
      return 'HOUR' //单独选择某一天
    default:
      return 'DAY'
  }
}

export const getUnitBase = (days: 1 | 7 | 30): any[] => {
  const date = new Date()
  const y = date.getFullYear()
  const m = date.getMonth()
  const d = date.getDate()

  if (days === 1) {
    const unitBase = Array.from({ length: 24 }, (_, i) => ({
      unit: dayjs(new Date(y, m, d, i)).format('YYYY-MM-DD HH:mm:ss'),
    }))
    return unitBase
  }
  const unitBase = Array.from({ length: days }, (_, i) => ({
    unit: dayjs(new Date(y, m, d - days + i + 1,8 )).format('YYYY-MM-DD HH:mm:ss'),
  }))
  return unitBase
}
//自定义时间范围
export const getCustomUnitBase = (startTDate: string, endDate: string, formatString: string = 'YYYY-MM-DD HH:mm:ss'): any[] => {
  const diffDays = dayjs(endDate).diff(startTDate,'day') + 1;
   const date = new Date(startTDate)
  const y = date.getFullYear()
  const m = date.getMonth()
  const d = date.getDate()
 
  if (diffDays === 1) {
    const unitBase = Array.from({ length: 24 }, (_, i) => ({
      unit: dayjs(new Date(y, m, d, i)).format(formatString),
    }))
    return unitBase
  }
 
  const unitBase = Array.from({ length: diffDays}, (_, i) => ({
    unit: dayjs(new Date(y, m, d + i, 8)).format(formatString),
  }))

  return unitBase
}

export const ChartColors = [
  '#2f57fb',
  '#12b799',
  '#ff7a7a',
  '#7a79fb',
  '#8bfecf',
  '#9e58fa',
]

export const ChartColorsGradient = ChartColors.map(
  (color) => `l(100) 0:${color}ff 1:${color}66`,
)

// 格式化图表的data，解析出表格展示类型下的columns
export const formatColumns = (data: any[], unitTitle: string, labelField: string): ColumnsType<any> => {
  let columns: ColumnsType<any> = []
  const unitCol = { title: unitTitle, dataIndex: 'unit', key: 'unit', ellipsis: true, width: 140 }
  const cols = [...new Set(data.map((item: any) => item?.[labelField]))] // 除unit的其他列（表头）
  columns = [unitCol, ...cols.map((key: string) => ({ title: key, dataIndex: key, key, ellipsis: true, width: 120 }))]
  return columns
}

// 格式化图表的data，解析出表格展示类型下的dataSource
export const formatDataSource = (data: any[], labelField: string, unitStr: string = ''): any[] => {
  let dataSource: any[] = []
  const handleXData = data.map((item: any) => ({ unit: item?.unit, [item?.[labelField]]: item?.amount }))
  const categoryCols = [...new Set(handleXData.map((item: any) => item?.unit))].map((unit: string) => ({ unit }));
  const types = [...new Set(data.map((item: any) => item?.[labelField]))]
  dataSource = categoryCols.map((item: any) => {
    const rowData: any = { ...item }
    types.map((type: any) => {
      const curItem: any = handleXData.find((i: any) => i?.unit === item?.unit && i[type]) || {}
      Object.assign(rowData, { [type]: (curItem?.[type] ?? 0) + unitStr });
    })
    return rowData;
  })
  return dataSource
}

//处理时间参数
export const getFormattedTimeParams = (timeRange: 1 | 7 | 30 | any, rangePickerTimes: string[] | null) => {
  let defaultParams: AuditSqlParams = {
    unit: getUnit(timeRange),
    ...getUnixTimeRange(timeRange),
  }
  if (rangePickerTimes) {
    const beginTimeMs = Number(
      dayjs(rangePickerTimes[0]).startOf('d').format('x'),
    )
    const endTimeMs = Number(
      dayjs(rangePickerTimes[1]).endOf('d').format('x'),
    )
    defaultParams = {
      ...defaultParams,
      beginTimeMs,
      endTimeMs,
    }
  }
  return defaultParams;
}