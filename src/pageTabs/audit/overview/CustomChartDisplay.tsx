import { Col, Row } from "antd"
import React, { useContext, useMemo } from "react"
import { ChartCard } from "src/components"
import { SlowSqlChart } from './Charts/SlowSqlChart'
import {
  Sql<PERSON>ount<PERSON><PERSON>,
  User<PERSON>ount<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  Exec<PERSON><PERSON><PERSON><PERSON><PERSON>,
  SqlConnectionChart
} from './Charts'
import { UserAuthorizationChart } from './Charts/UserAuthorizationChart';
import { CommonChart } from "../customAuditMetrics/components/CommonChart"
import { AuditOverviewContext } from "./AuditOverviewContext"
import { CommonDisplayCard } from "../sqlAuditMetrics/components/CommonDisplayCard"
import { useTranslation } from "react-i18next"
export const baseCharts: any[] = [
  { id: -4, title: "auays:chart_title.slow_sql_top10", children: <SlowSqlChart /> },
  { id: -3, title: "auays:chart_title.sql_exe_count_dt", children: <SqlCountChart /> },
  { id: -6, title: "auays:chart_title.user_usage_volume", children: <UserCountChart /> },
  { id: -5, title: "auays:chart_title.sql_exe_count_dc", children: <SqlConnectionChart /> },
  { id: -8, title: "auays:chart_title.user_auth_status", children: <UserAuthorizationChart /> },
  { id: -7, title: "auays:chart_title.database_type", children: <DbCountChart /> },
  { id: -9, title: "auays:chart_title.average_sql_exe_duration", children: <ExecutionTimeChart /> },
]

export const CustomChartDisplay = () => {
  const { customList: list, chartsCtrlList, onChartDisplayTypeChange } = useContext(AuditOverviewContext)
  const { t } = useTranslation()
  const chartList = useMemo(() => {
    return [...baseCharts, ...list];
  }, [list])

  const baseChart = (id: number) => {
    const item = baseCharts.find((item: any) => item.id === id)
    return <ChartCard title={t(item?.title)}
      displayType={chartsCtrlList.find((item: any) => item.id === id)?.displayType}
      onDisplayTypeChange={(type: string) => {
        onChartDisplayTypeChange(id, type)
      }}
    >
      {item?.children}
    </ChartCard>
  }

  const renderItem = (remainder: number) => {
    return <Col span={12}>
      {
        chartList?.map((item: any, index: number) => {
          if (index % 2 === remainder) {
            if (item.id < 0) {
              return <div key={item.id}>
                {baseChart(item.id)}
                <div style={{ margin: 16 }}></div>
              </div>
            }
            else {
              return <div key={item.id}>
                <ChartCard
                  title={item.name}
                  displayType={chartsCtrlList.find((i: any) => item.id === i.id)?.displayType}
                  onDisplayTypeChange={(type: string) => {
                    onChartDisplayTypeChange(item.id, type)
                  }}
                >
                  {
                    item.chartKey === "CUSTOM_SQL" ?
                      <CommonDisplayCard  item={item} /> :
                      <CommonChart item={item} />
                  }
                </ChartCard>
                <div style={{ margin: 16 }}></div>
              </div>
            }
          }
        })
      }
    </Col>
  }

  return <Row gutter={[24, 24]}>
    {renderItem(1)}
    {renderItem(0)}
  </Row>
}