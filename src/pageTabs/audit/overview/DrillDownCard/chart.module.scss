.drillDownChartWrapper {
  padding-top: 20px;
  border: 1px solid #eaebf0;
  border-radius: 4px;
  margin-bottom: 24px;
  box-shadow: 0px 8px 15px 0px rgba(149, 156, 182, 0.15);

  .drillDownChart {

    :global {
      .ant-card-extra {
        width: 60%;
      }
    }

    .sqlDrillDownCard {
      &_Extra {
          display: flex;
          justify-content: flex-start;
          align-items: center;
      
          .displayTypeComp {
            margin-left: 24px;
          }
        }
      :global {
        .ant-card-extra {
          width: auto !important;
        }
      }
    }
    
    .sqlTimeDrillDownCard {
      :global {
        .ant-card-extra {
          width: auto !important;
        }
      }
    }
  }

  .filterGroup {
    width: 100%;
    position: sticky;
    top: 0;
    z-index: 999;
    background-color: white;

    &__module {
      position: absolute;
      left: calc(50% - 117px);
      top: 5px;
    }

    &__all {
      :global {
        .ant-tabs-nav {
          margin-bottom: 0;
        }

        .ant-tabs-nav-list {
          margin-left: 24px;
        }

      }
    }
    &_displayTypeAndExport {
      position: absolute;
      right: 24px;
      top: 5px;
    
      .exportBtn {
        margin-left: 16px;
      }
    }
  }
}

.path {
  color: #0256FF;
  cursor: pointer;
}

.disabledPath {
  color: #767676;
}

.pathLink {
  color: #1A1A1A;
  padding: 0 2px;
}

.sqlCountWrapper {
  height: 55vh;

  .sqlCount {
    height: calc(55vh - 28px);
  }

  .more {
    text-align: center;
    cursor: pointer;
  }

  .sqlActionType {
    position: relative;
    height: 48px;
  }
}

.sqlEATTableRender {
  margin-top: 8px;
  height: calc(55vh - 28px);
}

.executionTimeWrapper {
  height: 55vh;

  .executionTime {
    height: calc(55vh - 28px);
  }
}

.empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 90%;
}