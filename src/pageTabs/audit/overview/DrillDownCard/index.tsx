import React, { useState, useMemo, useContext } from "react";
import * as _ from "lodash";
import dayjs from "dayjs";
import { Card, Row, Col, Radio, Tag, Select, Tabs, Button, Spin } from "antd";
import { SqlCountChart } from "./SqlCountChart";
import { ExecutionTimeRingChart } from "./ExecutionTimeRingChart";
import { ExecutionTimeLineChart } from "./ExecutionTimeLineChart";
import { ExecutionTimeBarChart } from "./ExecutionTimeBarChart";
import classNames from "classnames";
import { SqlTypes } from "src/types/types";
import { useRequest, useSelector } from "src/hook";
import { getAuditDrillDownData } from "src/api";
import styles from "./chart.module.scss";
import { formatColumn, formatColumnPie, formatDataSource, formatDataSourcePie, getStartAndEndTime } from "./utils";
import BatchExportChartTable from "../components/BatchExportChartTable";
import DisplayTypeSwitch from "../components/DisplayTypeSwitch";
import AutoDisplayTable from "../components/AutoDisplayTable";
import { ColumnsType } from "antd/lib/table";
import { AuditOverviewContext } from "../AuditOverviewContext";
import { useTranslation } from "react-i18next";

const ALL = 'all'
const HIGH_RISK = 'risk'

export const DrillDownCard = () => {
  const { t } = useTranslation()
  const { locales } = useSelector((state: any) => state.login);
  const { displayType, setDisplayType, chartsCtrlList, onChartDisplayTypeChange, onChartExportParamsChange } = useContext(AuditOverviewContext)
  const sqlENDisplayType = useMemo(() => {
    return chartsCtrlList.find((item: any) => item.id === -1)?.displayType || 'TABLE'
  }, [chartsCtrlList])
  const sqlEATDisplayType = useMemo(() => {
    return chartsCtrlList.find((item: any) => item.id === -2)?.displayType || 'TABLE'
  }, [chartsCtrlList])
  const X_DEFAULT_MAPPING: { [k: string]: any } = {
    TIME: {
      posXDim: "TIME",
      timeUnit: "QUARTER",
      alias: dayjs().year() + t("auays:time_unit.year")
    },
    OBJECT: {
      alias: t("auays:filter_als.data_source_type"),
      curDrillType: 'dbTypes'
    },
    ORG: {
      orgUnit: "COMPANY",
      alias: t("auays:filter_als.company")
    },
  };

  const [filterParams, setFilterParams] = useState<any>({
    posXDim: "TIME",
    timeUnit: "QUARTER",
    alias: dayjs().year() + t("auays:time_unit.year"),
    showDeleted: true,
    highRiskFlag: ALL,
  });

  //存放time查询条件方便展示
  const [currentTime, setCurrentTime] = useState<any>({})
  const [drillPaths, setDrillPaths] = useState<any>([
    { posXDim: "TIME", timeUnit: "QUARTER", alias: dayjs().year() + t("auays:time_unit.year") },
  ]);

  // 高危和全部的筛选条件
  const [tabkey, setTabkey] = useState(ALL)
  const [batchExportVisible, setBatchExportVisible] = useState<boolean>(false) // 批量导出表单弹窗

  const {
    data = [],
    loading
  } = useRequest(
    () => {
      let defaultParams: any = filterParams;

      defaultParams = {
        ...defaultParams,
      };
      if (filterParams?.posXDim === 'TIME') {
        defaultParams = {
          ...defaultParams,
          ...getStartAndEndTime(
            defaultParams?.timeUnit,
            defaultParams?.drillTime
          ),
        }
      } else {
        defaultParams = {
          ...defaultParams,
          beginTimeMs: currentTime?.beginTimeMs,
          endTimeMs: currentTime?.endTimeMs
        }
      }


      if (filterParams?.timeUnit === "ALL_MONTH") {
        defaultParams = {
          ...defaultParams,
          timeUnit: "MONTH",
        };
      }
      delete defaultParams?.curDrillType;
      delete defaultParams?.objLinkParams;
      onChartExportParamsChange(-1, defaultParams)
      onChartExportParamsChange(-2, defaultParams)
      return getAuditDrillDownData(defaultParams);
    },
    {
      refreshDeps: [filterParams]
    }
  );

  // 解析data每个item中amount中包含的操作类型并去重
  const getOperationTypesList = (data: any[]): string[] => {
    const keysList: string[] = []
    data?.forEach((item: any) => {
      if (!_.isEmpty(item?.amount)) {
        Object.keys(item?.amount).map((key: string) =>
          keysList.push(key)
        );
      }
    })
    return Array.from(new Set(keysList));
  }

  const transformSqlExecuteCountData = useMemo(() => {
    let splitSqlCountChartData: any = [];
    let executeAvgData: any = [];
    // 操作类型列表
    const noRepeatKeyList = getOperationTypesList(data)
    data &&
      data.map((w: any) => {
        let amountObj = w?.amount || {};
        let execute_avg_obj = w?.execute_avg|| {};
        // //数据为空处理
        if (_.isEmpty(amountObj) && noRepeatKeyList?.length > 0) {
          noRepeatKeyList.map((item: any) => {
            amountObj[item] = 0;
            execute_avg_obj[item] = 0;
          })
        }

        Object.keys(amountObj).map((key) =>
          splitSqlCountChartData.push({
            ...w,
            type: key, //操作类型
            originType: w?.type,
            amount: w.amount?.[key],
            //@ts-ignore
            alias: `${w.alias}---${w.posX}`,
          })
        );

        //平均时长
        if (filterParams?.posXDim !== "ORG") {
          Object.keys(w.execute_avg).map((key) =>
            executeAvgData.push({
              ...w,
              originType: w?.type,
              type: key, //操作类型
              amount: w.execute_avg[key],
              alias: `${w.alias}---${w.posX}`,
            })
          );
        } else {
          executeAvgData.push({
            ...w,
            execute_avg: execute_avg_obj,
            alias: `${w.alias}---${w.posX}`,
          });
        }
      });
    return {
      splitSqlCountChartData,
      executeAvgData,
    };
  }, [_.cloneDeep(data)]);

  const handleClearTimeFilter = () => {
    let cloneFilterParams: any = _.cloneDeep(filterParams);
    if (filterParams?.posXDim !== "TIME") {
      delete cloneFilterParams?.timeUnit;
      delete cloneFilterParams?.beginTimeMs;
      delete cloneFilterParams?.endTimeMs;
    } else {
      //@ts-ignore
      cloneFilterParams = {
        ...cloneFilterParams,
        timeUnit: "QUARTER",
        highRiskFlag: tabkey,
      };
    }

    setFilterParams(cloneFilterParams);
    setCurrentTime({})

    if (cloneFilterParams?.posXDim === 'TIME') {
      let cloneDrillPath = _.cloneDeep(drillPaths);
      cloneDrillPath = [{ posXDim: "TIME", timeUnit: "QUARTER", alias: dayjs().year() + t("auays:time_unit.year") }];

      setDrillPaths(cloneDrillPath)
    }
  };

  const onChangeTab = (v: string) => {
    setTabkey(v)
    setFilterParams({ ...filterParams, highRiskFlag: v })
  }

  const renderFilterBtns = (
    <div className={styles.filterGroup}>
      <Tabs
        className={styles.filterGroup__all}
        defaultActiveKey={ALL}
        onChange={onChangeTab}>
        <Tabs.TabPane tab={t("auays:tab.all")} key={ALL} />
        <Tabs.TabPane tab={t("auays:tab.high_risk")} key={HIGH_RISK} />
      </Tabs>
      <Radio.Group
        value={filterParams?.posXDim}
        buttonStyle="solid"
        className={styles.filterGroup__module}
        onChange={(e) => {
          setFilterParams({
            timeUnit: filterParams?.timeUnit,
            drillTime: filterParams?.drillTime,
            posXDim: e.target.value,
            ...X_DEFAULT_MAPPING[e.target.value],
            operateTypes: [],
            highRiskFlag: tabkey,
          });
          if (e.target.value === 'TIME') {
            setCurrentTime({})
          }
          setDrillPaths([
            {
              timeUnit: filterParams?.timeUnit,
              drillTime: filterParams?.drillTime,
              posXDim: e.target.value,
              ...X_DEFAULT_MAPPING[e.target.value],
            },
          ]);
        }}
      >
        <Radio.Button value="TIME">{t("auays:rdo_cont.time")}</Radio.Button>
        <Radio.Button value="OBJECT">{t("auays:rdo_cont.data_object")}</Radio.Button>
        <Radio.Button value="ORG">{t("auays:rdo_cont.organization_structure")}</Radio.Button>
      </Radio.Group>
      <DisplayTypeSwitch
        displayType={displayType}
        onDisplayTypeChange={setDisplayType}
        className={styles.filterGroup_displayTypeAndExport}
      >
        <Button
          type="primary"
          size="small"
          className={styles.exportBtn}
          onClick={() => {
            setBatchExportVisible(true)
          }}
        >
          {t("auays:btn.batch_export")}
        </Button>
      </DisplayTypeSwitch>
    </div>
  );

  const renderDrillDownPaths = (
    <div>
      {drillPaths && (
        <>
          <span className={styles.disabledPath}>{t("auays:sp_label.path")}</span>
          {drillPaths?.length > 1 &&
            drillPaths.map((item: any, index: number) => (
              <span key={index}>
                <span
                  className={classNames(styles.path, {
                    [styles.disabledPath]: drillPaths.length === index + 1,
                  })}
                  onClick={() => {
                    if (index + 1 === drillPaths.length) return;
                    const preItem = drillPaths[index];
                    setFilterParams({ ...preItem, highRiskFlag: tabkey });
                    setDrillPaths(drillPaths.splice(0, index + 1));
                  }}
                >
                  {(item?.alias.startsWith("auays:") ? t(item?.alias) : item?.alias) || item?.posXDim}
                </span>
                {drillPaths.length !== index + 1 && (
                  <span className={styles.pathLink}>&nbsp;{`>`}&nbsp;</span>
                )}
              </span>
            ))}
        </>
      )}
    </div>
  );


  const SqlTypeOptions = SqlTypes.map((type) => ({ label: type, value: type }));

  // SQL 执行平均时长的表格render
  const sqlEATTableRender = useMemo(() => {
    const chartData = transformSqlExecuteCountData?.executeAvgData ?? []
    if (chartData?.length <= 0) return null

    let curColumns: ColumnsType<any> = []
    let curDataSource: any[] = []

    // 不同主体的数据处理方式不同
    if (["OBJECT", "TIME"].includes(filterParams?.posXDim)) {
      // 根据操作类型的筛选获取现需展示的操作类型列表
      const { operateTypes = [] } = filterParams;
      let types = [...new Set(chartData.map((item: any) => item?.type))]
      if (operateTypes.length > 0) {
        types = operateTypes
      }
      // 将chartData处理成表格的columns和数据
      curColumns = formatColumn(filterParams, types)
      curDataSource = formatDataSource(filterParams, chartData, types, 'ms')
    }
    else {
      // 根据操作类型的筛选获取现需展示的操作类型列表
      const { operateTypes = [] } = filterParams;
      const alltypes: string[] = []
      chartData.map((item: any) => {
        alltypes.push(...Object.keys(item?.execute_avg))
      })
      let types = [...new Set(alltypes)]
      if (operateTypes.length > 0) {
        types = operateTypes
      }
      // 将chartData处理成表格的columns和数据
      curColumns = formatColumnPie(filterParams, types)
      curDataSource = formatDataSourcePie(filterParams, chartData, types, 'ms')
    }
    return <Spin spinning={loading}>
      <div className={styles.sqlEATTableRender}>
        <AutoDisplayTable
          columns={curColumns}
          dataSource={curDataSource}
          pagination={false}
          scroll={{ x: 'fit-content', y: 'calc(100% - 24px)' }}
        />
      </div>
    </Spin>
  }, [transformSqlExecuteCountData, filterParams, locales])

  return (
    <div className={styles.drillDownChartWrapper}>
      {renderFilterBtns}
      <Card
        title={renderDrillDownPaths}
        className={styles.drillDownChart}
        bordered={false}
        key='card'
        extra={
          currentTime?.alias && (
            <Tag
              closable={true}
              color="processing"
              style={{ userSelect: "none" }}
              onClose={() => handleClearTimeFilter()}
            >
              {t("auays:card_extra.cur_query_cdt") + currentTime?.alias}
            </Tag>
          )
        }
      >
        <Row gutter={16}>
          <Col span={12}>
            <Card
              title={t("auays:card_title.sql_exe_count_ost")}
              bordered={false}
              headStyle={{ border: "none" }}
              className={styles.sqlDrillDownCard}
              extra={
                <div className={styles.sqlDrillDownCard_Extra}>
                  <Select
                    showSearch
                    mode="multiple"
                    showArrow
                    maxTagCount={1}
                    value={filterParams?.operateTypes || []}
                    placeholder={t("auays:sele_ph.operation_type")}
                    options={SqlTypeOptions}
                    style={{
                      marginLeft: 10,
                      width: 180,
                    }}
                    onChange={(values: string[]) =>
                      setFilterParams({ ...filterParams, operateTypes: values, highRiskFlag: tabkey })
                    }
                    getPopupContainer={(node) => node.parentNode}
                  />
                  {/* SQL 执行次数图表切换展示效果 */}
                  <DisplayTypeSwitch
                    displayType={sqlENDisplayType}
                    onDisplayTypeChange={(v: string) => onChartDisplayTypeChange(-1, v)}
                    className={styles.displayTypeComp}
                  />
                </div>
              }
            >
              <SqlCountChart
                displayType={sqlENDisplayType}
                loading={loading}
                chartData={transformSqlExecuteCountData?.splitSqlCountChartData}
                drillFilterParams={filterParams}
                onChangeDrillParams={(params: any) =>
                  setFilterParams({ ...filterParams, ...params, highRiskFlag: tabkey })
                }
                currentTime={currentTime}
                onUpdateDrillPaths={(path: any) => {
                  setDrillPaths(drillPaths.concat(path));
                  setFilterParams({ ...filterParams, ...path, highRiskFlag: tabkey });
                  if (path?.posXDim === 'TIME') {
                    let cloneCurrentTime = _.cloneDeep(currentTime)
                    cloneCurrentTime = path;

                    setCurrentTime({
                      ...cloneCurrentTime,
                      ...getStartAndEndTime(
                        path?.timeUnit,
                        path?.drillTime
                      ),
                    })
                  }
                }}
              />
            </Card>
          </Col>
          <Col span={12}>
            <Card
              title={t("auays:card_title.average_sql_exe_duration_ost")}
              bordered={false}
              headStyle={{ border: "none" }}
              className={styles.sqlTimeDrillDownCard}
              extra={<>
                {/* SQL 执行平均时长切换展示效果 */}
                <DisplayTypeSwitch
                  displayType={sqlEATDisplayType}
                  onDisplayTypeChange={(v: string) => onChartDisplayTypeChange(-2, v)}
                  className={styles.displayTypeComp}
                />
              </>
              }
            >
              {
                sqlEATDisplayType === 'CHART' ?
                  <>
                    {filterParams?.posXDim === "TIME" && (
                      <ExecutionTimeLineChart
                        loading={loading}
                        timeUnit={filterParams?.timeUnit}
                        chartData={transformSqlExecuteCountData?.executeAvgData}
                      />
                    )}
                    {filterParams?.posXDim === "OBJECT" && (
                      <ExecutionTimeBarChart
                        loading={loading}
                        chartData={transformSqlExecuteCountData?.executeAvgData}
                      />
                    )}
                    {filterParams?.posXDim === "ORG" && (
                      <ExecutionTimeRingChart
                        loading={loading}
                        chartData={transformSqlExecuteCountData?.executeAvgData}
                      />
                    )}
                  </> :
                  // 表格
                  sqlEATTableRender
              }
            </Card>
          </Col>
        </Row>
      </Card>
      {/* 批量导出表单弹窗 */}
      <BatchExportChartTable
        visible={batchExportVisible}
        onCancel={() => setBatchExportVisible(false)}
      />
    </div>
  );
};
