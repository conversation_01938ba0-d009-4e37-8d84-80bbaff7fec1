import dayjs from "dayjs";
import { ColumnsType } from 'antd/lib/table';
import { t } from "i18next";
/* 获得本季度的开始月份 */
function getQuarterStartMonth(time: string) {
  var nowMonth = dayjs(time).month() + 1;
  var quarterStartMonth = 0;
  if (nowMonth < 3) {
    quarterStartMonth = 0;
  }
  if (2 < nowMonth && nowMonth < 6) {
    quarterStartMonth = 3;
  }
  if (5 < nowMonth && nowMonth < 9) {
    quarterStartMonth = 6;
  }
  if (nowMonth > 8) {
    quarterStartMonth = 9;
  }
  return quarterStartMonth;
}

//获取当前季度
const getCurrentTimeQuarter = (time: string) => {
  var currentDate = new Date(time);
  var currentMonth = currentDate.getMonth() + 1; // 月份从0开始，所以要加1

  var currentQuarter;

  if (currentMonth >= 1 && currentMonth <= 3) {
    currentQuarter = 1;
  } else if (currentMonth >= 4 && currentMonth <= 6) {
    currentQuarter = 2;
  } else if (currentMonth >= 7 && currentMonth <= 9) {
    currentQuarter = 3;
  } else {
    currentQuarter = 4;
  }
  return currentQuarter
}
/* 获得本季度的开始日期 */
export function getQuarterStartAndEndDate(startTime: string) {
  const year = dayjs(startTime).year();
  const currentQuarter = Math.floor((dayjs(startTime).month() / 3)) + 1;
  
  const start = dayjs(new Date(year, (currentQuarter - 1) * 3, 1)).format('x');
  const end = dayjs(new Date(year, currentQuarter * 3, 0)).format('x');

  return {
    beginTimeMs: Number(start),
    endTimeMs: Number(end),
  };
}

export const getStartAndEndTime = (type: string, drillValue: string) => {
  switch (type) {
    case "QUARTER":
      return {
        beginTimeMs: Number(dayjs().startOf("year").format("x")),
        endTimeMs: Number(dayjs().endOf("year").format("x")),
      };
    case "MONTH":
      return getQuarterStartAndEndDate(drillValue);
    case 'ALL_MONTH': 
      return {
        beginTimeMs: Number(
          dayjs().startOf("year").format("x")
        ),
        endTimeMs: Number(
          dayjs().endOf("year").format("x")
        ),
      }
    case "WEEK":
      return {
        beginTimeMs: Number(dayjs(drillValue).startOf("month").format("x")),
        endTimeMs: Number(dayjs(drillValue).endOf("month").format("x")),
      };
    case "DAY":
      return {
        beginTimeMs: Number(
          dayjs(drillValue).startOf("week").format("x")
        ),
        endTimeMs: Number(
          dayjs(drillValue).endOf("week").format("x")
        ),
      };
    case "HOUR":
      return {
        beginTimeMs: Number(
          dayjs(drillValue).startOf("day").format("x")
        ),
        endTimeMs: Number(
          dayjs(drillValue).endOf("day").format("x")
        ),
      };

    default:
      return {};
  }
};
export const getDrillStartAndEndTime = (type: string, drillValue: string) => {
  switch (type) {
    case "QUARTER":
      return getQuarterStartAndEndDate(drillValue);
    case "MONTH":
      return {
        beginTimeMs: Number(dayjs(drillValue).startOf("month").format("x")),
        endTimeMs: Number(dayjs(drillValue).endOf("month").format("x")),
      };
    case 'ALL_MONTH': 
      return {
        beginTimeMs: Number(
          dayjs(drillValue).startOf("month").format("x")
        ),
        endTimeMs: Number(
          dayjs(drillValue).endOf("month").format("x")
        ),
      }
    case "WEEK":
      return {
        beginTimeMs: Number(
          dayjs(drillValue).startOf("week").format("x")
        ),
        endTimeMs: Number(
          dayjs(drillValue).endOf("week").format("x")
        ),
      };
    case "DAY":
    case 'HOUR':
      return {
        beginTimeMs: Number(
          dayjs(drillValue).startOf("day").format("x")
        ),
        endTimeMs: Number(
          dayjs(drillValue).endOf("day").format("x")
        ),
      };

    default:
      return {};
  }
};

//处理图标x轴展示月份
export const formattedTimeAliasValue = (unit: string,v: string) => {
  switch(unit) {
    case 'QUARTER':
      const q = getCurrentTimeQuarter(v)
      return t("auays:time_unit.quarter", { q })
    case 'MONTH':
    case 'ALL_MONTH':
      const m = new Date(v).getMonth();
      return  (m + 1) + t("auays:time_unit.month")
    case 'DAY':
      const d = dayjs(v).format('MM-DD');
      return  d + t("auays:time_unit.day")
    case 'HOUR':
      const h = new Date(v).getHours();
      return  h + t("auays:time_unit.hour")
    default: 
      return dayjs(v).format('YYYY-MM-DD')
  }
}

export const getFormattedAlias = (alias: any) => {
  const realAlias = alias && alias.split("---");

  return realAlias[0] && realAlias[0] !== "null"
    ? realAlias[0]
    : realAlias[1];
}


// 根据chartData格式化出表格的columns
export const formatColumn = (category: any, types: any[]): ColumnsType<any> => {
  let columns: ColumnsType<any> = []
  columns.push({ title: category?.alias, dataIndex: 'posX', key: 'posX', ellipsis: true, width: 150 })
  columns = [...columns, ...types.map((type: any) => ({ title: type, dataIndex: type, key: type, ellipsis: true, width: 120 }))]
  return columns
}

// SQL 执行平均时长组织架构"ORG"类型的columns
export const formatColumnPie = (category: any, types: any[]): ColumnsType<any> => {
  let columns: ColumnsType<any> = []
  columns.push(
    { title: category?.alias, dataIndex: 'posX', key: 'posX', ellipsis: true, width: 150 },
    { title: t("auays:tb_title.total_duration"), dataIndex: 'total', key: 'total', ellipsis: true, width: 150 }
  )
  columns = [...columns, ...types.map((type: any) => ({ title: type, dataIndex: type, key: type, ellipsis: true, width: 120 }))]
  return columns
}

// 根据chartData格式化出表格的dataSource
export const formatDataSource = (category: any, data: any[], types: any[], unit: string = ''): any[] => {
  let dataSource: any[] = []
  const handleXData = data.map((item: any) => {
    //时间分类下不存在alias的特殊处理
    const realAlias = item?.alias && item.alias?.split("---");
    const newAlias = category?.posXDim === "TIME" && realAlias[0] === "null" ?
      formattedTimeAliasValue(category?.timeUnit, item?.posX) :
      item?.alias
    return { posX: getFormattedAlias(newAlias.toLocaleString()), [item?.type]: item?.amount }
  })
  const categoryCols = [...new Set(handleXData.map((item: any) => item?.posX))].map((posX: string) => ({ posX }));
  dataSource = categoryCols.map((item: any) => {
    const rowData: any = { ...item }
    types.map((type: any) => {
      const curItem: any = handleXData.find((i: any) => i?.posX === item?.posX && i[type]) || {}
      Object.assign(rowData, { [type]: (curItem?.[type] ?? 0) + unit });
    })
    return rowData;
  })
  return dataSource
}

// SQL 执行平均时长组织架构"ORG"类型的dataSource
export const formatDataSourcePie = (category: any, data: any[], types: any[], unit: string = ''): any[] => {
  let dataSource: any[] = []
  const handleXData = data.map((item: any) => {
    //时间分类下不存在alias的特殊处理
    const realAlias = item?.alias && item.alias?.split("---");
    const newAlias = category?.posXDim === "TIME" && realAlias[0] === "null" ?
      formattedTimeAliasValue(category?.timeUnit, item?.posX) :
      item?.alias
    return { posX: getFormattedAlias(newAlias.toLocaleString()), ...item?.execute_avg || {} }
  })
  const categoryCols = [...new Set(handleXData.map((item: any) => item?.posX))].map((posX: string) => ({ posX }));
  const collectColsValues: any[] = categoryCols.map((item: any) => {
    const rowData: any = { ...item }
    types.map((type: any) => {
      const curItem: any = handleXData.find((i: any) => i?.posX === item?.posX && i[type]) || {}
      Object.assign(rowData, { [type]: curItem?.[type] ?? 0 });
    })
    return rowData;
  })

  dataSource = collectColsValues.map((item: any) => {
    const total = Object.keys(item).reduce((pre: number, cur: string) => {
      if (types.includes(cur)) return pre + item[cur]
      else return pre
    }, 0) ?? 0
    const rowDataAndUnit: any = {...item}
    types.map((type: any) => {
      Object.assign(rowDataAndUnit, { [type]: (item?.[type] ?? 0) + unit });
    })
    Object.assign(rowDataAndUnit, { 'total': total + unit });
    return rowDataAndUnit
  })
  return dataSource
}