import React, { useEffect, useMemo, useState } from 'react'
import { useRequest, useSelector, useDispatch } from 'src/hook'
import { useHistory } from 'react-router-dom'
import { Col, Row } from 'antd'
import { getOperateCard } from 'src/api/getNewData';
import { CommonCard } from './components/CommonCard'
import { ITargetCard, TargetCard } from './components/TargetCard'
import StatisCardFilter from './components/StatisCardFilter'
import moment from 'moment'
import { useTranslation } from 'react-i18next';
import { 
  setOverviewPageState,
  setOverviewPageDetailParams 
} from 'src/pageTabs/audit/overview/overviewSlice'
import { setUnauthorizedOperationState } from 'src/pageTabs/audit/auditSlice';

export const OperationCard = ({ hasApp }: { hasApp?: boolean }) => {

  const dispatch = useDispatch();
  const [userOperationStatisData, setUserOperationStatisData] = useState<any>({});
  const [userOperationUnit, setUserOperationUnit] = useState<string>("DAY");  // radio
  const [userOpePicker, setUserOpePicker] = useState<[any, any]>([moment().startOf('day'), moment().endOf('day')]);  // datePicker
  const history = useHistory();
  const { locales } = useSelector((state: any) => state.login);
  const { t } = useTranslation()

  // 操作Card
  const {
    loading: userOperationLoading,
    run: queryUserOperationData,
  } = useRequest(getOperateCard, {
    manual: true,
  });

  useEffect(() => {
    const params = {
      unit: userOperationUnit || "CUSTOM",
      executeBeginMs: userOpePicker?.[0]? moment(userOpePicker[0])?.startOf('day').valueOf() : undefined,
      executeEndMs: userOpePicker?.[1]? moment(userOpePicker[1])?.endOf('day').valueOf() : undefined
    }
    
    if (userOperationUnit) {
      delete params.executeBeginMs
      delete params.executeEndMs
    }
    queryUserOperationData(params).then(res => {
      setUserOperationStatisData(res)
    });
  }, [userOperationUnit, userOpePicker]);

  const operateLinkMap = new Map([
    [t("auays:ope_stats.total_user_operations"), null],
    [t("auays:ope_stats.total_executed_statements"), "/statement_detail"],
    [t("auays:ope_stats.total_system_operations"), "/operate_record"],
    [t("auays:ope_stats.successful_executed_statements"), "/statement_detail"],
    [t("auays:ope_stats.failed_executed_statements"), "/statement_detail"],
    [t("auays:ope_stats.sensitive_resource_operations"), "/statement_detail"],
    [t("auays:ope_stats.high_risk_operations"), "/statement_detail"],
    [t("auays:ope_stats.privilege_escalation_operations"), "/operate_unauthorized"],
  ]);

  // 渲染语句明细
  const gotoStatementDetail = (params: any) => {
    dispatch(setOverviewPageState('statement_detail'))
    dispatch(setOverviewPageDetailParams(params))
  }

  // 渲染操作记录
  const gotoOperateRecord = (params: any) => {
    dispatch(setOverviewPageState('operate_record'))
    dispatch(setOverviewPageDetailParams(params))
  }

  // 渲染越权操作
  const gotoOperateUnauthorized = (params: any) => {
    dispatch(setOverviewPageState('operate_unauthorized'))
    dispatch(setOverviewPageDetailParams(params))
  }
  // 跳转
  const toLink = (str: string) => {
    let state: any;
    const [startDate, endDate] = userOpePicker;
    let timeRange: any = []
    if (!startDate || !endDate) timeRange = undefined
    else timeRange = [moment(startDate).startOf('day').valueOf(), moment(endDate).endOf('day').valueOf()];
    switch (str) {
      case t("auays:ope_stats.total_executed_statements"):
        state = {
          timeRange
        };
        break;
      case t("auays:ope_stats.total_system_operations"):
        state = {
          timeRange
        };
        break;
      case t("auays:ope_stats.successful_executed_statements"):
        state = {
          timeRange,
          resultFlag: 1,
        };
        break;
      case t("auays:ope_stats.failed_executed_statements"):
        state = {
          timeRange,
          resultFlag: 0,
        };
        break;
      case t("auays:ope_stats.sensitive_resource_operations"):
        state = {
          timeRange,
          skipDesens: 1,
        };
        break;
      case t("auays:ope_stats.high_risk_operations"):
        state = {
          timeRange,
          highFlag: 1,
        };
        break;
      case t("auays:ope_stats.privilege_escalation_operations"):
        state = {
          timeRange,
          extratTypes: ["EXTRAT_FLAG"],
          overPermFlag: "1"
        };
        dispatch(setUnauthorizedOperationState(state));
        break;
    }
    
    if(operateLinkMap.get(str) === '/statement_detail') {
      // 渲染语句明细
      gotoStatementDetail(state)
      
    }else if(operateLinkMap.get(str) === '/operate_record'){
      // 渲染操作记录
      gotoOperateRecord(state)

    }else if(operateLinkMap.get(str) === '/operate_unauthorized'){
      // 渲染越权操作
      gotoOperateUnauthorized(state)

    }else {
      history.push({
        pathname: operateLinkMap.get(str) as string,
        state: state,
      })
    }
  }

  const targetList = useMemo((): ITargetCard[] => {
    const {
      allCount = 0, //用户操作总量
      executeCount = 0, //用户执行语句总量
      successCount = 0, //执行语句成功
      errorCount = 0, //执行语句失败
      skipDesensCount = 0, //敏感资源操作量
      operateCount = 0, // 用户系统操作总量
      highRiskCount = 0, //高危操作量
      overPermCount = 0, //越权操作量
      // growth: growth_operation, // 环比增长
    } = userOperationStatisData;
    return [
      { count: allCount, name: t("auays:ope_stats.total_user_operations"), allowClick: false },
      { count: executeCount, name: t("auays:ope_stats.total_executed_statements"), allowClick: true, onClick: () => toLink(t("auays:ope_stats.total_executed_statements")) },
      { count: operateCount, name: t("auays:ope_stats.total_system_operations"), allowClick: true, onClick: () => toLink(t("auays:ope_stats.total_system_operations")) },
      { count: successCount, name: t("auays:ope_stats.successful_executed_statements"), allowClick: true, onClick: () => toLink(t("auays:ope_stats.successful_executed_statements")) },
      {
        count: errorCount, name: t("auays:ope_stats.failed_executed_statements"), allowClick: true,
        onClick: () => toLink(t("auays:ope_stats.failed_executed_statements")),
        hoverType: 'YELLOW'
      },
      {
        count: skipDesensCount, name: t("auays:ope_stats.sensitive_resource_operations"), allowClick: true,
        onClick: () => toLink(t("auays:ope_stats.sensitive_resource_operations")),
        hoverType: 'YELLOW'
      },
      {
        count: highRiskCount, name: t("auays:ope_stats.high_risk_operations"), allowClick: true,
        onClick: () => toLink(t("auays:ope_stats.high_risk_operations")),
        hoverType: 'YELLOW'
      },
      {
        count: overPermCount, name: t("auays:ope_stats.privilege_escalation_operations"), allowClick: true,
        onClick: () => toLink(t("auays:ope_stats.privilege_escalation_operations")),
        hoverType: 'YELLOW'
      },
    ]
  }, [userOperationStatisData, locales])

  return <Col span={hasApp ? 10 : 15}>
    <CommonCard
      title={t("auays:card_title.user_ope_volume")}
      loading={userOperationLoading}
      tooltipContent={t("auays:tip_title.user_ope_records")}
      tipPlacement="topLeft"
      style={{ height: '212px' }}
      extra={<StatisCardFilter
        size={hasApp ? 'small' : 'standard'}
        defaultValue={{
          radioValue: userOperationUnit,
          rangePickerValue: userOpePicker
        }}
        onChange={(values: any) => {
          setUserOperationUnit(values.radioValue);
          setUserOpePicker(values.rangePickerValue);
        }}
      />}
    >
      <Row gutter={[16, 12]}>
        {targetList.map((item: ITargetCard) => <Col span={6} key={item.name}>
          <TargetCard {...item} style={{ height: '61px' }} />
        </Col>)}
      </Row>
    </CommonCard>
  </Col>
}
