import React from 'react'
import { Row } from 'antd'
import { UserCard } from './UserCard'
import { OperationCard } from './OperationCard'
import { AppOperationCard } from './AppOperationCard'
import { useRequest } from 'src/hook'
import { getHasApp } from 'src/api/getNewData'

export const StatisticalCard = () => {
  const { data: hasApp } = useRequest(getHasApp);

  return (
    <Row gutter={[16, 16]}>
      <OperationCard hasApp={hasApp} />
      {hasApp && <AppOperationCard />}
      <UserCard hasApp={hasApp} />
    </Row>
  );
}
