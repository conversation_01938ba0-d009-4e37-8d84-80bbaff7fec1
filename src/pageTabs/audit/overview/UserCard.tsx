import React, { useEffect, useMemo, useState } from 'react'
import { useRequest, useSelector, useDispatch } from 'src/hook'
import { Col, Radio, Row } from 'antd'
import ActiveUserChart from './Charts/ActiveUserChart'
import { getActiveUserCard } from 'src/api/getNewData';
import { CommonCard } from './components/CommonCard'
import { ITargetCard, TargetCard } from './components/TargetCard'
import styles from './index.module.scss'
import moment from 'moment'
import classNames from 'classnames'
import { useTranslation } from 'react-i18next'
import {
  setUserAuditPageState,
  setUserAuditPageDetailParams
} from 'src/pageTabs/audit/overview/overviewSlice'
import { useHistory } from 'react-router-dom'

export const UserCard = ({ hasApp }: { hasApp?: boolean }) => {
  const history = useHistory();
  const dispatch = useDispatch();
  const { t } = useTranslation()
  const { locales } = useSelector((state: any) => state.login);
  const [userStatisData, setUserStatisData] = useState<any>({}); //统计数据
  const [activeUserChartData, setActiveUserChartData] = useState<any[]>([]); //图表数据
  const [activeUserGrowth, setActiveUserGrowth] = useState<string>('0'); //图表数据
  const [activeUserUnit, setActiveUserUnit] = useState<string>("DAY");  // 切换unit

  const radioOptions: any[] = [
    { label: t("auays:stats_dim.today"), value: 'DAY' },
    { label: t("auays:stats_dim.this_month"), value: 'MONTH' }
  ];
  // 活跃用户Card Active users
  const {
    loading: activeUserLoading,
    run: queryActiveUserData,
  } = useRequest(getActiveUserCard, {
    manual: true,
  });

  useEffect(() => {
    queryActiveUserData({ unit: activeUserUnit }).then((res: { onlinePercentage: string, growth: string }) => {
      setUserStatisData(res)
      setActiveUserChartData([{
        type: t("auays:user_chart.online"),
        value: Number(res.onlinePercentage),
      },
      {
        type: t("auays:user_chart.offline"),
        value: 100 - Number(res.onlinePercentage),
      }])
      setActiveUserGrowth(res.growth)
    });
  }, [activeUserUnit]);

  // 渲染审计分析-用户审计
  const gotoUserAudit = (params: any) => {
    history.push('/suser_audit')
    dispatch(setUserAuditPageState(''))
    dispatch(setUserAuditPageDetailParams(params))
  }
  
  // 跳转
  const toLink = (str: string) => {
    let state: any;
    let timeKey: any = activeUserUnit.toLocaleLowerCase();
    let timeRange = [moment().startOf(timeKey).valueOf(), moment().endOf(timeKey).valueOf()];
    switch (str) {
      case t("auays:user_stats.total_users"):
        state = {
          timeRange,
          tertiaryDirectoryMark: 1
        };
        break;
      case t("auays:user_stats.current_users"):
        state = {
          onLineFlag: 1,
          isOnLineUser: 1,
          tertiaryDirectoryMark: 1,
        };
        break;
    }
    gotoUserAudit(state)
  }

  const targetList = useMemo((): ITargetCard[] => {
    const {
      allUser = 0, //用户总量
      onlineUser = 0, //当前用户数
    } = userStatisData;
    return [
      { count: allUser, name: t("auays:user_stats.total_users"), allowClick: true, onClick: () => toLink(t("auays:user_stats.total_users")) },
      { count: onlineUser, name: t("auays:user_stats.current_users"), allowClick: true, onClick: () => toLink(t("auays:user_stats.current_users")) },
    ]
  }, [userStatisData, locales])

  return <Col span={hasApp ? 7 : 9}>
    <CommonCard
      title={t("auays:card_title.active_users")}
      loading={activeUserLoading}
      tooltipContent={t("auays:tip_title.user_count")}
      style={{ height: '212px' }}
      extra={
        <Radio.Group
          options={radioOptions}
          onChange={(e: any) => setActiveUserUnit(e.target.value)}
          value={activeUserUnit}
          optionType="button"
          buttonStyle="solid"
          className={classNames({ [styles.extraRadioGroup]: hasApp })}
        />
      }
    >
      <Row>
        <Col span={14}>
          <ActiveUserChart
            data={activeUserChartData}
            loading={activeUserLoading}
            growth={activeUserGrowth}
          />
        </Col>
        <Col span={10}>
          <Row gutter={[16, 12]}>
            {targetList.map((item: ITargetCard) => <Col span={24} key={item.name}>
              <TargetCard {...item} style={{ height: '61px' }} />
            </Col>)}
          </Row>
        </Col>
      </Row>

    </CommonCard>
  </Col>
}
