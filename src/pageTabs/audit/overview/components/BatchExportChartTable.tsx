import { Checkbox, message, Modal, Tooltip } from "antd"
import React, { useContext, useMemo, useState } from "react"
import type { CheckboxChangeEvent } from 'antd/es/checkbox';
import type { CheckboxValueType } from 'antd/es/checkbox/Group';
import styles from './components.module.scss'
import { AuditOverviewContext } from "../AuditOverviewContext";
import { batchExportOverviewCharts } from "src/api";
import { useRequest } from "src/hook";
import { useTranslation } from "react-i18next";
const CheckboxGroup = Checkbox.Group;

const BatchExportChartTable = (props: any) => {
  const { chartsCtrlList } = useContext(AuditOverviewContext)
  const { visible, onCancel } = props
  const { t } = useTranslation()
  const chartList = useMemo(() => [
    ...chartsCtrlList?.map((item: any) => ({ label: item.title, value: item.id })).sort((a: any, b: any) => a.value > b.value ? -1 : 1)
  ], [chartsCtrlList])//当前审计概览中展示的所有图表列表
  const [checkedList, setCheckedList] = useState<CheckboxValueType[]>([]); //选中项
  const [indeterminate, setIndeterminate] = useState(false); // indeterminate状态
  const [checkAll, setCheckAll] = useState(false);

  // 全选框回调函数
  const onCheckAllChange = (e: CheckboxChangeEvent) => {
    setCheckedList(e.target.checked ? chartList?.map((item: any) => item.value) : []);
    setIndeterminate(false);
    setCheckAll(e.target.checked);
  }

  // 复选框回调函数
  const onChange = (list: CheckboxValueType[]) => {
    setIndeterminate(!!list.length && list.length < chartList.length);
    setCheckedList(list);
    setCheckAll(list.length === chartList.length);
  }

  // 重置
  const reset = () => {
    setCheckedList([])
    setIndeterminate(false)
    setCheckAll(false)
  }
  // 导出request
  const { run: runBatchExport, loading: exportLoading } = useRequest(batchExportOverviewCharts, {
    manual: true,
    onSuccess: () => {
      message.success(t("auays:msg.export_success"))
      reset()
      onCancel()
    },
    onError: (e: Error) => {
      console.error(t("auays:clg_err.batch_export_failure"), e)
    }
  })


  // 确认导出
  const onOk = () => {
    const checkedParamsList = chartsCtrlList.filter((item: any) => checkedList.includes(item.id))
    const exportParams = {
      models: checkedParamsList.map((item: any) => ({ id: item.id, param: item.params }))
    }
    runBatchExport(exportParams)
  }

  return <Modal
    visible={visible}
    onCancel={() => {
      reset()
      onCancel()
    }}
    width={350}
    className={styles.batchExportModal}
    okText={t("auays:md_title.confirm_export")}
    confirmLoading={exportLoading}
    onOk={onOk}
    destroyOnClose={true}
  >
    <Checkbox
      indeterminate={indeterminate}
      onChange={onCheckAllChange}
      checked={checkAll}
      className={styles.checkAll}
    >
      {t("auays:check.select_all")}
    </Checkbox>
    <CheckboxGroup
      value={checkedList}
      onChange={onChange}
      className={styles.checkGroup}
    >
      {
        chartList?.map((item: any) => <Checkbox key={item.value} value={item.value}>
          <Tooltip title={item.label}>
            {item.label}
          </Tooltip>
        </Checkbox>)
      }
    </CheckboxGroup>
  </Modal>
}
export default BatchExportChartTable