import { Card, Spin, Tooltip } from "antd";
import React, { useMemo } from "react";
import styles from './components.module.scss'
import { CardProps } from "antd/lib/card";
import classNames from "classnames";
import { ExclamationCircleOutlined } from "@ant-design/icons";
import { TooltipPlacement } from "antd/lib/tooltip";

export interface ICommonCard extends CardProps {
  tooltipContent?: string | React.ReactNode
  tipPlacement?: TooltipPlacement
}

export const CommonCard = (props: ICommonCard) => {
  const { title, tooltipContent, children, loading, className, tipPlacement = 'top' } = props

  // 带tooltip的标题
  const titleNode = useMemo(() => {
    return <div className={styles.cardTitleNode}>
      <div className={styles.cardTitle}>{title}</div>
      {
        tooltipContent &&
        <Tooltip
          title={tooltipContent}
          className={styles.cardIcon}
          placement={tipPlacement}
          arrowPointAtCenter
        >
          <ExclamationCircleOutlined />
        </Tooltip>
      }
    </div>
  }, [title, tooltipContent, tipPlacement])


  return <Card
    {...props}
    className={classNames({ [styles.commonCard]: true }, className)}
    title={titleNode}
  >
    <Spin spinning={loading}>{children}</Spin>
  </Card>
}