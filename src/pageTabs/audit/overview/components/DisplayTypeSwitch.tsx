import { Switch } from "antd";
import React, { ReactNode } from "react";
import styles from './components.module.scss'
import classNames from "classnames";
import { useTranslation } from "react-i18next";
interface IDisplayTypeSwitch {
  className?: any
  displayType: string
  onDisplayTypeChange: (type: string) => void
  children?: ReactNode
}

const DisplayTypeSwitch = (props: IDisplayTypeSwitch) => {
  const { className = {}, displayType, onDisplayTypeChange, children } = props;
  const { t } = useTranslation()
  
  return <div className={classNames(styles.displayTypeSwitch, className)}>
    <Switch
      size="small"
      className={styles.switch}
      checked={displayType === 'CHART'}
      onChange={(checked: boolean) => {
        onDisplayTypeChange(checked ? 'CHART' : 'TABLE')
      }}
    />
    <div>{t("auays:display.switch_to_chart")}</div>
    {children}
  </div>
}
export default DisplayTypeSwitch;