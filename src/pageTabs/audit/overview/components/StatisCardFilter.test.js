import { cleanup, render, screen, act } from "@testing-library/react";
import React from "react";
import StatisCardFilter from "./StatisCardFilter";
import userEvent from "@testing-library/user-event";
import moment from "moment";
afterEach(cleanup);
jest.setTimeout(10000);

const originalConsoleError = console.error;
console.error = function (error) {
  if (typeof error === "string" && error.includes("Invalid DOM property")) {
    return;
  } else {
    // 其他情况调用原始的console.error
    originalConsoleError(error);
  }
};

describe("测试StatisCardFilter组件", () => {
  // 模拟点击事件函数
  const mockClickEventFn = async (element) => {
    return await act(async () => {
      userEvent.click(element);
    });
  };

  it("StatisCardFilter组件能够正常渲染", () => {
    let res = {};
    render(
      <StatisCardFilter
        defaultValue={{
          radioValue: "MONTH",
          rangePickerValue: [moment().startOf("month"), moment().endOf("day")],
        }}
        onChange={(value) => {
          res = value;
        }}
      />
    );
    const startDate = moment().startOf("month").format("YYYY-MM-DD");
    const endDate = moment().endOf("day").format("YYYY-MM-DD");
    expect(screen.getByText(/当月/i)).toBeTruthy(); // radio group组件正常渲染
    // date-picker组件正常渲染
    const startPickerNode = screen.getByPlaceholderText(/Start date/i);
    expect(startPickerNode.value).toBe(startDate);
    const endPickerNode = screen.getByPlaceholderText(/End date/i);
    expect(endPickerNode.value).toBe(endDate);
    const matchRes = {
      radioValue: "MONTH",
      rangePickerValue: [moment().startOf("month"), moment().endOf("day")],
    };
    expect(res).toEqual(matchRes); // 初始结果正确`
  });

  it("StatisCardFilter组件可正常切换时间筛选类型(RadioGroup)", async () => {
    let res = {};
    render(
      <StatisCardFilter
        defaultValue={{
          radioValue: "MONTH",
          rangePickerValue: [moment().startOf("month"), moment().endOf("day")],
        }}
        onChange={(value) => {
          res = value;
        }}
      />
    );

    // 测试切换为【全部】类型，date-picker组件值清空
    const allRadioNode = screen.getByText(/全部/g);
    expect(allRadioNode).toBeTruthy();
    let allNodeClick = await mockClickEventFn(allRadioNode);
    if (allNodeClick) {
      let checkedNode = document.querySelector(".ant-radio-button-checked");
      // 判断checked节点是否包含文本为全部的节点
      expect(checkedNode.querySelector("span")).toContainElement(allRadioNode);
      // date-picker组件值清空
      const startPickerNode = screen.getByPlaceholderText(/Start date/i);
      expect(startPickerNode.value).toBe("");
      const endPickerNode = screen.getByPlaceholderText(/End date/i);
      expect(endPickerNode.value).toBe("");
    }
    const matchRes1 = {
      radioValue: "All",
      rangePickerValue: [null, null],
    };
    expect(res).toEqual(matchRes1); // 回调结果正确

    // 测试切换为【当日】，date-picker组件值变为当前日期
    const dayRadioNode = screen.getByText(/当日/g);
    expect(dayRadioNode).toBeTruthy();
    let dayNodeClick = await mockClickEventFn(dayRadioNode);
    if (dayNodeClick) {
      let checkedNode = document.querySelector(".ant-radio-button-checked");
      // 判断checked节点是否包含文本为全部的节点
      expect(checkedNode.querySelector("span")).toContainElement(dayRadioNode);
      // date-picker组件值变为当前日期
      const dayDate = moment().format("YYYY-MM-DD");
      const startPickerNode = screen.getByPlaceholderText(/Start date/i);
      expect(startPickerNode.value).toBe(dayDate);
      const endPickerNode = screen.getByPlaceholderText(/End date/i);
      expect(endPickerNode.value).toBe(dayDate);
    }
    const matchRes2 = {
      radioValue: "DAY",
      rangePickerValue: [moment().startOf("day"), moment().endOf("day")],
    };
    expect(res).toEqual(matchRes2);
  });

  it("StatisCardFilter组件datePicker可正常切换时间", async () => {
    let res = {};
    render(
      <StatisCardFilter
        defaultValue={{
          radioValue: "MONTH",
          rangePickerValue: [moment().startOf("month"), moment().endOf("day")],
        }}
        onChange={(value) => {
          res = value;
        }}
      />
    );

    const startDate = moment().startOf("month").format("YYYY-MM-DD");
    const startPickerNode = screen.getByDisplayValue(startDate);
    await mockClickEventFn(startPickerNode);
    let datePicker = document.querySelector(".ant-picker-dropdown");
    expect(datePicker).toBeInTheDocument();
    // 点击上一个月按钮图标
    let prevIcon = document.querySelector(".ant-picker-prev-icon");
    await mockClickEventFn(prevIcon);
    // 开始时间选择上个月的今天
    const lastMonthDay = moment().subtract(1, "month").format("YYYY-MM-DD");
    const lastMonthNode = screen.getByTitle(lastMonthDay);
    await mockClickEventFn(lastMonthNode);
    // 结束时间选择今天
    const currentMonthDay = moment().format("YYYY-MM-DD");
    const currentMonthNode = screen.getByTitle(currentMonthDay);
    const handleEnd = await mockClickEventFn(currentMonthNode);
    if (handleEnd) {
      let tooltip = document.querySelector(".ant-picker-dropdown");
      expect(tooltip).toHaveClass("ant-picker-dropdown-hidden"); // 点击确认后下拉框消失
    }
    const matchRes = {
      radioValue: "",
      rangePickerValue: [
        moment().subtract(1, "month").startOf("day"),
        moment().startOf("day"),
      ],
    };
    expect(res).toEqual(matchRes);
    /*-----------------------------------------------------------------------------------------------------------------------*/
    const startDate1 = moment()
      .subtract(1, "month")
      .startOf("day")
      .format("YYYY-MM-DD");
    const startPickerNode1 = screen.getByDisplayValue(startDate1);
    await mockClickEventFn(startPickerNode1);
    let datePicker1 = document.querySelector(".ant-picker-dropdown");
    expect(datePicker1).toBeInTheDocument();
    // 点击下一个月按钮图标
    let nextIcon = document.querySelector(".ant-picker-next-icon");
    await mockClickEventFn(nextIcon);
    // 开始时间选择当前月的1号
    const monthOneDay = moment().startOf("month").format("YYYY-MM-DD");
    const monthOneNode = screen.getByTitle(monthOneDay);
    await mockClickEventFn(monthOneNode);
    // 结束时间选择今天
    const currentMonthDay1 = moment().format("YYYY-MM-DD");
    const currentMonthNode1 = screen.getByTitle(currentMonthDay1);
    const handleEnd1 = await mockClickEventFn(currentMonthNode1);
    if (handleEnd1) {
      let tooltip = document.querySelector(".ant-picker-dropdown");
      expect(tooltip).toHaveClass("ant-picker-dropdown-hidden"); // 点击确认后下拉框消失
    }
    const matchRes1 = {
      radioValue: "MONTH",
      rangePickerValue: [moment().startOf("month"), moment().startOf("day")],
    };
    expect(res).toEqual(matchRes1);
    /*-----------------------------------------------------------------------------------------------------------------------*/
    const startDate2 = moment().startOf("day").format("YYYY-MM-DD");
    const startPickerNode2 = screen.getByDisplayValue(startDate2);
    await mockClickEventFn(startPickerNode2);
    let datePicker2 = document.querySelector(".ant-picker-dropdown");
    expect(datePicker2).toBeInTheDocument();
    // 开始时间选择今天
    const currentDay = moment().startOf("day").format("YYYY-MM-DD");
    const currentDayNode = screen.getByTitle(currentDay);
    await mockClickEventFn(currentDayNode);
    // 结束时间选择今天
    const handleEnd2 = await mockClickEventFn(currentDayNode);
    if (handleEnd2) {
      let tooltip = document.querySelector(".ant-picker-dropdown");
      expect(tooltip).toHaveClass("ant-picker-dropdown-hidden"); // 点击确认后下拉框消失
    }
    const matchRes2 = {
      radioValue: "DAY",
      rangePickerValue: [moment().startOf("day"), moment().startOf("day")],
    };
    expect(res).toEqual(matchRes2);

    /*-----------------------------------------------------------------------------------------------------------------------*/
    // 点击清空图标
    let clearIcon = document.querySelector(".ant-picker-clear");
    await mockClickEventFn(clearIcon);
    const matchRes3 = {
      radioValue: "All",
      rangePickerValue: [null, null],
    };
    expect(res).toEqual(matchRes3);
  });

  it("StatisCardFilter组件可调整size", () => {
    render(
      <StatisCardFilter
        defaultValue={{
          radioValue: "MONTH",
          rangePickerValue: [moment().startOf("month"), moment().endOf("day")],
        }}
        onChange={() => {}}
        size="small"
      />
    );
    // 对应的class已生效
    let componentNode = document.querySelector(".smallFilter");
    expect(componentNode).toBeInTheDocument();
  });
});
