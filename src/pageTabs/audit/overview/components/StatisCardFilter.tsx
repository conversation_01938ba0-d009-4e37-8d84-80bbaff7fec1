import { DatePicker, Radio } from "antd"
import React, { useEffect, useState } from "react"
import styles from './components.module.scss'
import classNames from "classnames";
import moment from "moment";
import { isEmpty } from "lodash";
import { useTranslation } from "react-i18next";
const { RangePicker } = DatePicker;
interface IStatisCardFilter {
  size?: 'standard' | 'small' // 尺寸
  defaultValue?: any // 默认值
  onChange: (value: any) => void // 回调
}
const StatisCardFilter = (props: IStatisCardFilter) => {
  const { size = 'standard', defaultValue, onChange } = props;
  const [radioValue, setRadioValue] = useState<string>('DAY')
  const [rangePickerValue, setRangePickerValue] = useState<[any, any]>([moment().startOf('day'), moment().endOf('day')])
  const { t } = useTranslation()
  
  const radioOptions: any[] = [
    { label: t("auays:stats_dim.all"), value: 'All' },
    { label: t("auays:stats_dim.today"), value: 'DAY' },
    { label: t("auays:stats_dim.this_month"), value: 'MONTH' }
  ];

  // 初始化
  useEffect(() => {
    if (!isEmpty(defaultValue)) {
      if (defaultValue?.radioValue !== radioValue) {
        setRadioValue(defaultValue?.radioValue)
      }
      const [start, end] = defaultValue?.rangePickerValue
      if (!start || !end) setRangePickerValue([null, null])
      if (!start?.isSame(rangePickerValue?.[0], 'day') || !end?.isSame(rangePickerValue?.[1], 'day')) {
        setRangePickerValue(defaultValue?.rangePickerValue)
      }
    }
  }, [defaultValue])

  // 回传
  useEffect(() => {
    onChange({
      radioValue,
      rangePickerValue
    })
  }, [radioValue, rangePickerValue])

  const handleRadioChange = (e: any) => {
    const value = e.target.value
    setRadioValue(e.target.value)
    switch (value) {
      case 'All':
        setRangePickerValue([null, null])
        break;
      case 'DAY':
        setRangePickerValue([moment().startOf('day'), moment().endOf('day')])
        break;
      case 'MONTH':
        setRangePickerValue([moment().startOf('month'), moment().endOf('day')])
        break;
    }
  }

  // 时间选择器的onChange
  const handlePickerChange = (e: any) => {
    if (!e) {
      setRangePickerValue([null, null])
      setRadioValue('All')
    }
    else {
      const [startDate, endDate] = e
      setRangePickerValue([startDate, endDate])
      if (
        startDate.isSame(moment().startOf('day'), 'day') &&
        endDate.isSame(moment().endOf('day'), 'day')
      ) {
        setRadioValue('DAY')
      }
      else if (
        startDate.isSame(moment().startOf('month'), 'day') &&
        endDate.isSame(moment().endOf('day'), 'day')
      ) {
        setRadioValue('MONTH')
      }
      else {
        setRadioValue('')
      }
    }
  }

  return <div className={classNames(styles.statisCardFilter, { [styles.smallFilter]: size === 'small' })}>
    <Radio.Group
      options={radioOptions}
      onChange={handleRadioChange}
      value={radioValue}
      optionType="button"
      buttonStyle="solid"
      className={styles.radioGroup}
    />
    <RangePicker
      onChange={handlePickerChange}
      value={rangePickerValue}
      className={styles.rangePicker}
    />
  </div>
}

export default StatisCardFilter