import { Card } from "antd";
import { CardProps } from "antd/lib/card";
import React from "react";
import styles from './components.module.scss'
import classNames from "classnames";
import { formatNumber } from "src/util";

export interface ITargetCard extends CardProps {
  count?: number | string // 数值
  name?: string // 数值名称
  allowClick?: boolean // 是否可点击
  hoverType?: string // hover态样式类型
  isPlaceholder?: boolean //是否只是占位
}

const hoverTypeNum: any = {
  BLUE: styles.blueHover,
  YELLOW: styles.yellowHover
}

export const TargetCard = (props: any) => {
  const { count, name, allowClick = false, style = {}, hoverType = 'BLUE', isPlaceholder = false } = props
  const hoverClassName = hoverTypeNum[hoverType]

  return <Card
    bordered={false}
    {...props}
    className={classNames({ [styles.targetCard]: true }, { [hoverClassName]: allowClick ? true : false })}
    style={allowClick ? { ...style, cursor: 'pointer' } : style}
  >
    {
      !isPlaceholder ?
        <>
          <div className={styles.count}>{formatNumber(Number(count))}</div>
          <div className={styles.name} >{name}</div>
        </>
        : null
    }
  </Card>
}