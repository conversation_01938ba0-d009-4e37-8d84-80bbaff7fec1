.commonCard {
  padding: 10px;
  border: 1px solid #EAEBF0;
  border-radius: 4px;
  box-shadow: 0px 4px 16px 0px rgba(219, 220, 226, 0.25);

  .cardTitleNode {
    display: flex;
    justify-content: center;
    align-items: center;

    .cardTitle {
      width: 'max-content';
      height: 24px;
      font-family: PingFang SC;
      font-size: 16px;
      line-height: 24px;
      color: #0F244C;
      z-index: 1;
    }

    .cardIcon {
      width: 14px;
      height: 14px;
      margin-left: 6px;
      color: #8c8c8c;
      font-size: 14px;
    }
  }

  :global {
    .ant-card-head {
      height: 42px;
      min-height: 42px;
      padding: 0 0 0 16px;
      border-bottom: 1px solid #E5E6EB;
    }

    .ant-card-head-title {
      height: 23px;
      padding: 0;
    }

    .ant-card-extra {
      padding: 0;
    }

    .ant-card-body {
      margin-top: 16px;
      padding: 0px;
    }
  }
}

.targetCard {
  width: 100%;
  padding: 8px 16px;
  display: flex;
  justify-content: flex-start;
  flex-direction: row;
  align-items: center;
  border-radius: 4px;

  :global {
    .ant-card-body {
      margin-top: 0px;
    }
  }

  .count {
    height: 24px;
    font-family: Source Han Sans;
    font-size: 18px;
    font-weight: bold;
    line-height: 24px;
    color: #0F244C;
  }

  .name {
    height: 20px;
    font-family: Source Han Sans;
    font-size: 14px;
    font-weight: normal;
    line-height: 20px;
    color: #6C7293;
  }
}

.blueHover:hover {
  background-color: #F5F7FA;

  .count,
  .name {
    color: #3262FF;
  }
}
.yellowHover:hover {
  background-color: #FFF2EA;

  .count,
  .name {
    color: #FF8437;
  }
}

.batchExportModal {
  :global{
    .ant-checkbox-group{
      width: 100%;
    }
    .ant-checkbox-wrapper{
      padding: 3px 0;
      display: block;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
      margin-left: 0;
    }
  }
  .checkAll {
    margin-bottom: 6px;
  }
  .checkGroup {
    max-height: 350px;
    overflow: hidden auto;
  }
}

.displayTypeSwitch {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  width: fit-content;
  color: #6C7293;

  .switch {
    margin-right: 6px;
  }
}

.autoDisplayTable {
  height: 100%;

  :global {
    .ant-spin-nested-loading,
    .ant-spin-container,
    .ant-table,
    .ant-table-container {
      height: 100%
    }

    .ant-table-header {
      border-top: 1px solid #CECECF;
      border-left: 1px solid #CECECF;
      border-right: 1px solid #CECECF;
    }

    .ant-table-body {
      scrollbar-width: thin;
      border-bottom: 1px solid #CECECF;
      border-left: 1px solid #CECECF;
      border-right: 1px solid #CECECF;
    }

  }
}

.statisCardFilter {
  .radioGroup {
    margin-right: 10px;
  }
}

.smallFilter {
  :global {
    .ant-radio-button-wrapper {
      height: 28px;
      line-height: 26px;
      padding: 0 6px;
    }

    .ant-picker {
      height: 28px;
      width: 230px
    }

    .ant-picker:hover,
    .ant-picker-focused {
      height: 28px;
    }
  }
}