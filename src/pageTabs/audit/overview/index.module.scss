.auditMain {
  overflow: hidden;
}

.auditContent {
  height: 100%;

  .auditTopContent {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0px;

    .headLeftInfo {
      font-size: 14;
      color: #868FA3;
    }

    .headRightOpe {
      width: fit-content;
      display: flex;
      justify-content: flex-start;
      align-items: center;

      .opeIcon {
        cursor: pointer;
        width: 26px;
        height: 26px;
        border-radius: 4px;
        margin-right: 5px;
        background: #F2F3F5;
        display: flex;
        justify-content: center;
        align-items: center;
      }

      .opeIcon:last-child {
        margin-right: 0;
      }
    }
  }

  .card,
  .cardWithAction {
    box-shadow: 0px 8px 15px 0px rgba(149, 156, 182, 0.15);
    border-radius: 4px;
    border: 1px solid #eaebf0;
  }

  .cardWithAction::after {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    box-shadow: 0 8px 17px 0 rgba(0, 0, 0, 0.05), 0 6px 20px 0 rgba(0, 0, 0, 0.05);
    content: '';
    opacity: 0;
    z-index: -1;
  }

  .cardWithAction:hover,
  .cardWithAction:focus {
    transform: scale3d(1.003, 1.003, 1.003);
    opacity: 0.88;
  }

  .cardWithAction:hover::after,
  .cardWithAction:focus::after {
    opacity: 1;
  }

  .cardBody {
    height: 100%;
    padding: 0 56px;
    flex-wrap: nowrap;

    .countText {
      display: flex;
      flex-direction: column;
    }

    .countNumber {
      font-size: 30px;
      font-family: Roboto-Regular, Roboto;
      font-weight: 400;
      color: #454458;
      line-height: 43px;
      margin-right: 6px;
      margin-bottom: 0;
      overflow: hidden;
    }

    .error {
      color: red;
    }

    .green {
      color: green;
    }

    .total {
      font-size: 20px;
      font-family: Roboto-Regular, Roboto;
      font-weight: 400;
      color: #454458;
      line-height: 43px;
      margin-right: 16px;
      margin-bottom: 0;
      overflow: hidden;
    }

    .countTitle {
      font-size: 14px;
      font-family: Roboto-Regular, Roboto;
      font-weight: 400;
      color: rgba(108, 114, 147, 1);
      line-height: 19px;
      margin: 8px 0 3px;
    }

    .iconCircle {
      width: 76px;
      height: 76px;
      border-radius: 38px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  .chartContainer {
    height: calc(100vh - 172px);
    padding-bottom: 16px;
    overflow-y: auto;
    overflow-x: hidden;
  }
}

.countTitle {
  font-size: 1vh;
  font-weight: 450;
}

.countNumber {
  font-size: 3vh;
  font-weight: 400;
}

.countNumberSize {
  font-size: 2vh;
  font-weight: 700;
  position: absolute;
  right: 3vh;
  bottom: 0;
}

.countText {
  font-size: 1vh;
  font-weight: 400;
  color: #6C7293;
}

.countTextHover:hover {
  cursor: pointer;
  color: #6076ee;
}

.pointer {
  cursor: pointer;
}

.statisticalCardWrapper {
  width: 100%;
  padding-top: 20px;
  border: 1px solid #eaebf0;
  border-radius: 4px;
  margin-bottom: 24px;
  box-shadow: 0px 8px 15px 0px rgba(149, 156, 182, 0.15);

  .dataCard {
    text-align: center;
  }

  .dataCardHover:hover {
    background-color: #F3F4F7;
    cursor: pointer;

    .countText {
      color: #6076ee;
    }
  }
}

.operateCardTooltip {
  position: absolute;
  left: 14vh;
  top: 4vh;
}

.userCardTooltip {
  position: absolute;
  left: 12vh;
  top: 4vh;
}

.switchTooltip {
  :global {
    .ant-tooltip-inner {
      width: 130px;
    }
  }
}

.appOpExtra {
  display: inline-block;
  width: 'max-content';

  .unitBtn {
    height: 20px;
    height: 28px;
    margin: 0;
    padding: 0;
    background: #fff;
    font-family: Roboto;
    font-size: 14px;
    font-weight: normal;
    line-height: 20px;
  }
}

.AuditDisplayDrawer {
  .drawerFooter {
    display: flex;
    justify-content: flex-end;

    .cancelBtn {
      margin-right: 8px;
    }
  }

  .baseCharts {
    :global {
      .ant-checkbox-group {
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        align-items: flex-start;
        max-height: 306px;
        width: 100%;
        overflow: hidden auto;
      }

      .ant-checkbox-wrapper,
      .ant-checkbox-group-item {
        width: 100%;
        min-height: 22px;
        margin: 6px 0;
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
        margin-left: 0;
      }

      .ant-checkbox-disabled+span {
        color: #1D2129;
      }

      .ant-checkbox-disabled .ant-checkbox-inner {
        background: #94BFFF;
        border-color: #94BFFF !important;

        &::after {
          border-color: #ffffff;
        }
      }
    }
  }

  .drawerTitle {
    font-size: 12px;
    font-weight: 300;
    line-height: 20px;
    height: 20px;
    color: #4E5969;
    margin-bottom: 2px;
  }

  .customCharts {
    margin-top: 18px;

    .customInput {
      margin-bottom: 9px;
    }

    :global {
      .ant-checkbox-group {
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        align-items: flex-start;
        max-height: calc(100vh - 473px);
        width: 100%;
        overflow: hidden auto;
      }

      .ant-checkbox-wrapper,
      .ant-checkbox-group-item {
        width: 100%;
        min-height: 22px;
        margin: 6px 0;
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
        margin-left: 0;
      }
    }
  }

  :global {
    .ant-drawer-body {
      padding: 12px 16px;
    }

    .ant-drawer-header {
      color: #1D2129;
      font-size: 16px;
      font-weight: normal;
    }
  }
}

.extraRadioGroup {
  :global {
    .ant-radio-button-wrapper {
      height: 28px;
      line-height: 26px;
      padding: 0 6px;
    }
  }
}