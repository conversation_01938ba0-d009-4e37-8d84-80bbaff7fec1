import React, { useEffect, useState } from 'react'
import classNames from 'classnames'
import { Layout, Dropdown, Menu, Tooltip } from 'antd'
import { ErrorBoundary, SimpleBreadcrumbs } from 'src/components'
import { DrillDownCard } from './DrillDownCard'
import { StatisticalCard } from './StatisticalCard'
import 'src/styles/layout.scss'
import styles from './index.module.scss'
import { useRequest, useSelector, useDispatch } from 'src/hook'
import { getCardCenter } from 'src/api/getNewData'
import { EllipsisOutlined } from '@ant-design/icons'
import { CustomAuditContext } from '../customAuditMetrics/CustomAuditContext'
import { useCustomAuditContext } from 'src/hook/useCustomAuditMetrice'
import { CustomChartDisplay } from './CustomChartDisplay'
import { AuditDisplayDrawer } from './AuditDisplayDrawer'
import { useAuditOverview } from 'src/hook/useAuditOverview'
import { AuditOverviewContext } from './AuditOverviewContext'
import { useTranslation } from 'react-i18next'
import {
  AuditExecutionPage,
  AuditOperationPage,
  UnauthorizedOperationPage,
  SqlAuditMetrics,
  CustomAuditMetrics,
  AppOperationDetailPage
} from "src/pageTabs/audit";
import { 
  setOverviewPageState,
  setOverviewPageDetailParams 
} from 'src/pageTabs/audit/overview/overviewSlice'

const { Content } = Layout
const headSetIcon = <svg className="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" id="16824" width="16" height="16"><path d="M446.679 867.881A96.01 96.01 0 0 0 514.56 896h0.097a96.01 96.01 0 0 0 96-96v-5.437a20.808 20.808 0 0 1 12.606-19.042h0.322a20.777 20.777 0 0 1 22.846 4.06l1.761 1.762a96.051 96.051 0 0 0 135.823 0.015 96 96 0 0 0 28.14-67.906 96.108 96.108 0 0 0-28.124-67.917l-1.762-1.756a20.818 20.818 0 0 1-4.06-22.851v-0.323A20.823 20.823 0 0 1 797.123 608H800a95.995 95.995 0 0 0 67.881-163.881A95.99 95.99 0 0 0 800 416h-5.315a20.797 20.797 0 0 1-17.116-9.185 32.246 32.246 0 0 0-2.048-6.4 20.808 20.808 0 0 1 4.06-22.85l1.762-1.757a96.051 96.051 0 0 0 0.015-135.823 95.99 95.99 0 0 0-67.906-28.14 96.041 96.041 0 0 0-67.917 28.124l-1.756 1.756a20.818 20.818 0 0 1-22.851 4.066h-0.323A20.818 20.818 0 0 1 608 226.877V224a96 96 0 0 0-192 0v5.315a20.797 20.797 0 0 1-9.088 17.116 32.146 32.146 0 0 0-6.4 2.048 20.803 20.803 0 0 1-22.845-4.066l-1.762-1.756A96.067 96.067 0 0 0 240 378.465l1.89 1.726a20.803 20.803 0 0 1 4.065 22.85c-0.282 0.615-0.538 1.25-0.768 1.89a20.803 20.803 0 0 1-18.755 13.598H224a96 96 0 1 0 0 192h5.565a20.803 20.803 0 0 1 19.047 12.606v0.322a20.808 20.808 0 0 1-4.065 22.846l-1.762 1.761a96.03 96.03 0 0 0-0.015 135.823 96.041 96.041 0 0 0 135.823 0.016l1.756-1.762a20.803 20.803 0 0 1 22.851-4.06l1.761 0.732a20.803 20.803 0 0 1 13.599 18.755V800a95.979 95.979 0 0 0 28.119 67.881z m20.275-118.4a84.777 84.777 0 0 0-38.953-30.346h-0.03a84.818 84.818 0 0 0-92.611 17.377l-2.176 2.176a32.087 32.087 0 0 1-22.65 9.375 32.067 32.067 0 0 1-22.647-9.39 32.026 32.026 0 0 1 0.016-45.297l2.176-2.176a85.146 85.146 0 0 0-60.636-144.799H224a32 32 0 0 1 0-64h3.645a84.777 84.777 0 0 0 77.251-54.4 84.797 84.797 0 0 0-17.377-92.61l-2.176-2.176A32 32 0 1 1 330.655 288l1.889 1.823 0.256 0.256a84.797 84.797 0 0 0 87.363 19.425 32.005 32.005 0 0 0 8.448-2.304A84.797 84.797 0 0 0 480 229.437V224a32.005 32.005 0 0 1 64 0v3.005a85.135 85.135 0 0 0 52.29 78.147 85.146 85.146 0 0 0 92.35-17.628l2.176-2.176a31.949 31.949 0 0 1 34.877-6.943 31.959 31.959 0 0 1 12.514 9.262 32.077 32.077 0 0 1 6.666 14.07 32.051 32.051 0 0 1-8.776 28.892l-2.176 2.176a84.792 84.792 0 0 0-19.425 87.363 32.055 32.055 0 0 0 2.304 8.448 84.777 84.777 0 0 0 77.763 51.39H800a32 32 0 0 1 0 64h-3.005a85.161 85.161 0 0 0-83.206 101.088 85.13 85.13 0 0 0 22.692 43.52l2.207 2.207a32.087 32.087 0 0 1 9.375 22.651 32.061 32.061 0 0 1-19.784 29.588 32.061 32.061 0 0 1-34.903-6.958l-2.176-2.176a85.146 85.146 0 0 0-144.799 60.642v5.437a32 32 0 0 1-64 0v-3.615a84.787 84.787 0 0 0-15.447-46.904z m-26.066-131.052a128 128 0 1 0 142.224-212.864A128 128 0 0 0 440.888 618.43z m35.559-159.641a63.985 63.985 0 0 1 80.809 7.956 64 64 0 1 1-80.81-7.956z" id="16825" fill="#4E5969"></path></svg>

export const AuditOverviewPage = () => {
  const dispatch = useDispatch()
  const { t } = useTranslation()
  const { overviewPageState } = useSelector(state=>state.overview)
  const [data, setData] = useState<any>();
  const [auditDisplayVisible, setAuditDisplayVisible] = useState<boolean>(false);
  const { run } = useRequest(getCardCenter, { manual: true });
  const defaultContext = useCustomAuditContext()
  const overviewDFContext = useAuditOverview()
  const { chartsCtrlList, displayType, onInitLocalStorageDisplayType } = overviewDFContext
  const { showCustomAudit } = useSelector(state => state.login)
  const BREADCRUMB_ITEMS = [
    { title: t("auays:bc_title.audit_analysis") },
    { title: t("auays:bc_title.audit_overview") }
  ];

  // 组件销毁状态重置
  useEffect(() => {
    return () => {
      dispatch(setOverviewPageState(''))
      dispatch(setOverviewPageDetailParams({}))
    }
  }, [])

  // 各个图表的开关存本地
  useEffect(() => {
    return () => {
      const value = chartsCtrlList?.map((item: any) => ({ id: item.id, displayType: item.displayType }))
      localStorage.setItem('chartsDisplayList', JSON.stringify(value))
    }
  }, [chartsCtrlList]);

  // 总开关存本地
  useEffect(() => {
    return () => {
      localStorage.setItem('chartsDisplayType', JSON.stringify(displayType))
    }
  }, [displayType]);

  useEffect(() => {
    run().then(res => {
      setData(res);
    })
    // 获取本地存储的显示类型总开关和图表显示类型
    const storageDisplay = localStorage.getItem('chartsDisplayList') ?? ''
    const storageType = localStorage.getItem('chartsDisplayType') ?? ''

    if (storageDisplay && storageType) {
      const storageDisplayList = JSON.parse(storageDisplay)
      const storageDisplayType = JSON.parse(storageType)
      onInitLocalStorageDisplayType(storageDisplayType, storageDisplayList)
    }
  }, [])

  // 渲染审计分析次-审计概览-自定义审计指标SQL模式
  const gotoCustomAuditMetricsSql = () => {
    dispatch(setOverviewPageState('custom_audit_metrics_sql'))
    dispatch(setOverviewPageDetailParams({}))
  }

  const headOpeMenus = <Menu>
    <Menu.Item onClick={gotoCustomAuditMetricsSql}>{t("auays:menu.custom_audit_metrics")}</Menu.Item>
  </Menu>;

  // 根据页面状态渲染对应内容
  if(overviewPageState==='statement_detail'){
    // 审计分析-审计概览-语句明细
    return <AuditExecutionPage />
  }else if(overviewPageState==='operate_record'){
    // 审计分析-审计概览-操作记录
    return <AuditOperationPage />
  }else if(overviewPageState==='operate_unauthorized'){
    // 审计分析-审计概览-越权操作
    return <UnauthorizedOperationPage />
  }else if(overviewPageState ==='custom_audit_metrics_sql'){
    // 审计分析-审计概览-自定义审计指标-sql模式
    return <SqlAuditMetrics />
  }else if(overviewPageState ==='custom_audit_metrics'){
    // 审计分析-审计概览-自定义审计指标-智能模式模式
    return <CustomAuditMetrics />
  }else if(overviewPageState ==='app_statement_detail'){
    // 审计分析-审计概览-应用语句明细
    return <AppOperationDetailPage />
  }

  // 审计分析-审计概览
  return (
    <Layout className="cq-container">
      <AuditOverviewContext.Provider value={overviewDFContext}>
        <div style={{ padding: '0 20px' }}>
          <SimpleBreadcrumbs items={BREADCRUMB_ITEMS} />
        </div>
        <Layout className={classNames("cq-main", { [styles.auditMain]: true })} >
          <CustomAuditContext.Provider value={defaultContext}>
            <Content
              className={classNames('cq-content', { [styles.auditContent]: true })}
            >
              <div className={styles.auditTopContent}>
                <div className={styles.headLeftInfo}>
                  {data && <span>{
                    t("auays:hd_stats.audit_data", {
                      amount: data.amount,
                      sortType: data.sortType,
                      lastTime: data.lastTime
                    })
                  }</span>}
                </div>
                {showCustomAudit && <div className={styles.headRightOpe}>
                  <Tooltip title={t("auays:tip_title.adm_display_settings")} placement='topRight' arrowPointAtCenter>
                    <div className={styles.opeIcon} onClick={() => setAuditDisplayVisible(true)} role="button">{headSetIcon}</div>
                  </Tooltip>
                  <Dropdown overlay={headOpeMenus} trigger={['click']}>
                    <div className={styles.opeIcon}><EllipsisOutlined /></div>
                  </Dropdown>
                </div>}
              </div>

              <ErrorBoundary>
                <div className={styles.chartContainer}>
                  <div style={{ marginBottom: '8px' }}>
                    {/* 统计数据、跳转点 */}
                    <StatisticalCard />
                  </div>
                  {/* sql执行次数、sql执行平均时长  视图 */}
                  <DrillDownCard />
                  <CustomChartDisplay />
                </div>
              </ErrorBoundary>
            </Content>
            {/* 审计指标展示设置 */}
            <AuditDisplayDrawer
              visible={auditDisplayVisible}
              setVisible={setAuditDisplayVisible}
            />
          </CustomAuditContext.Provider>
        </Layout>
      </AuditOverviewContext.Provider>
    </Layout>
  )
}
