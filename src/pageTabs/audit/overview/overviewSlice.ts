import { createSlice, PayloadAction } from '@reduxjs/toolkit'

// 定义审计概览页面状态的联合类型
type OverviewPageState = 
'statement_detail' 
| 'operate_record' 
| 'operate_unauthorized'
| 'custom_audit_metrics_sql'
| 'custom_audit_metrics'
| 'app_statement_detail'
| '';

// 定义用户审计页面状态的联合类型
type UserAuditPageState = 
'user_detail' 
| '';

interface OverviewState {
  overviewPageState: OverviewPageState      // 审计分析-审计概览页面状态
  overviewPageDetailParams: any             // 审计分析-审计概览详情参数
  userAuditPageState: UserAuditPageState    // 审计分析-用户审计页面状态
  userAuditPageDetailParams: any            // 审计分析-用户审计详情参数
  userExecuteLogHistoryParams: any
}

const initialState: OverviewState = {
  overviewPageState: '',                     // 审计分析-审计概览页面状态
  overviewPageDetailParams: {},              // 审计分析-审计概览详情参数
  userAuditPageState: '',                    // 审计分析-用户审计页面状态
  userAuditPageDetailParams: {},             // 审计分析-用户审计详情参数
  userExecuteLogHistoryParams: {},             // 个人中心-执行历史参数
}

export const overviewSlice = createSlice({
  name: 'overview',
  initialState,
  reducers: {
    setOverviewPageState: (state, action: PayloadAction<OverviewPageState>) => {
      state.overviewPageState = action.payload
    },
    setOverviewPageDetailParams: (state, action: PayloadAction<any>) => {
      state.overviewPageDetailParams = action.payload
    },
    setUserAuditPageState: (state, action: PayloadAction<UserAuditPageState>) => {
      state.userAuditPageState = action.payload
    },
    setUserAuditPageDetailParams: (state, action: PayloadAction<any>) => {
      state.userAuditPageDetailParams = action.payload
    },
    setUserExecuteLogHistoryParams: (state, action: PayloadAction<any>) => {
      state.userExecuteLogHistoryParams = action.payload
    },
  },
})

export const overviewReducer = overviewSlice.reducer

export const {
  setOverviewPageState,
  setOverviewPageDetailParams,
  setUserAuditPageState,
  setUserAuditPageDetailParams,
  setUserExecuteLogHistoryParams
} = overviewSlice.actions