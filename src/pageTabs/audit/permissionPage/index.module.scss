.marginRight10 {
  margin-right: 10px;
}
.marginRight20 {
  margin-right: 20px;
}
.margin012 {
  margin: 0px 12px;
}
.marginLeft20 {
  margin-left: 20px;
}
.marginLeft40 {
  margin-left: 40px;
}
.marginTop10 {
  margin-top: 10px;
}
.padding12 { 
  padding: 12px;
}
.resetBgColor {
  background-color: #f0f1f5;
}
.floatRight {
  float: right;
}
.bgWhite {
  background-color: white;
}

.breadcrumb {
  color: "red";
  font-size: 16px;
  .ant-breadcrumb-separator {
    color: #D8D8D8;
  }
  > span:first-child {
        color: #0F244C;
        font-size: 20px;
      }
    > span:last-child {
        color: #0F244C;
        font-size: 16px;
      }
}


.content {
  height: 100%;
  padding: 12px;
  margin: 6px 12px 12px;
  background-color: #F7F9FC;
  .topWrap {
    background-color: #ffffff;
    margin-bottom: 15px;
    padding-top: 12px;
  }
  .tableTopWrap {
    display: flex;
    justify-content: space-between;
    background-color: #ffffff;
    .rightOperations {
      margin-right: 12px;
    }
  }
  .tableWrap {
    padding-top: 0px;
    background-color: #ffffff;
    .customTable {
      :global{
        .ant-table-thead > tr > th {
          background-color: #ffffff;
        }
        .ant-table-tbody > tr > td {
          padding-top: .35%;
          padding-bottom: .35%;
          font-size: 13.5px;
        }
      }
    }
    .paginationDiv {
      display: flow-root;
      .pagination {
        float: right;
        margin-top: 10px;
      }
    }
  }
  .linkBtn{
    padding: 4px 0px;
  }
  :global {
    .ant-divider-horizontal {
      background-color: #ffffff;
      margin: 0px;
      padding: 10px 0 0;
    }
  }
}
