/**
 * 权限看板
 */
import React, { useMemo, useState, useEffect } from 'react'
import { Table, Spin, Breadcrumb, Button, Divider, Dropdown, Menu, Tooltip, Input, message, Popconfirm } from 'antd'
import { SearchOutlined, SettingOutlined, VerticalAlignBottomOutlined } from '@ant-design/icons'
import styles from './index.module.scss'
import { useRequest, useSelector, useDispatch } from 'src/hook';
import { useLocation } from 'react-router-dom';
import { getScrollX } from 'src/util'
import classnames from 'classnames'
import { GloablSearchLocationState } from 'src/pageTabs/GlobalSearchModal/WorkOrderResult';
import PermissionDetailModal from './modals/PermissionDetailModal'
import {
  AuthDashboardParams,
  delPermissionInfo,
  delPermissionRecord,
  exportAuthFile,
  getAndSetAuditColumn,
  getAuthDashboard,
  getPermissionDetailById
} from 'src/api'
import {
  AUTH_BOARD_DETAIL_DEFAULT_COLUMN,
  AUTH_BOARD_DETAIL_COLUMN
} from 'src/constants'
import CustomColumnsDrawer from '../components/CustomColumnsDrawer'
import SearchModal from './modals/SearchModal'
import { Iconfont, exportTaskCreatedNot } from 'src/components';
import { useTranslation } from 'react-i18next';
import { 
  setOverviewPageState,
  setOverviewPageDetailParams 
} from 'src/pageTabs/audit/overview/overviewSlice'

interface PaginationI {
  current: number,
  pageSize: number,
  total: number,
}
const PerimissionBoard = () => {
  const dispatch = useDispatch()
  const { t } = useTranslation()
  const location = useLocation<any>()
  const { state = {} } = location as {state: GloablSearchLocationState};
  const { userId = '' } = useSelector((state) => state.login.userInfo)

  const [dataSource, setDataSource] = useState<any[]>([])
  const [columns, setColumns] = useState<any[]>([])

  const [visibleModal, setVisibleModal] = useState<boolean>(false)
  const [permissionDetaiModalData, setPermissionDetaiModalData] = useState<any>();
  const [searchValues, setSearchValues] = useState<any>()
  const [total, setTotal] = useState<number>(0)

  const [searchResourceCondition, setSearchResourceCondition] = useState('')

  const [drawerVisible, setDrawerVisible] = useState(false)
  const [pagination, setPagination] = useState<PaginationI>({
    current: 0,
    pageSize: 10,
    total: 0,
  })

  const menu = (
    <Menu>
      <Menu.Item onClick={() => exportFile("csv")}>csv</Menu.Item>
      <Menu.Item onClick={() => exportFile("excel")}>excel</Menu.Item>
    </Menu>
  );

  const setColumnsWidth = (key: string) => {
    switch (key) {
      case 'permissionType':
      case 'nodeType':
      case 'authIp':
        return 120;
      case "expirationTime":
        return 180
      case 'permissionId':
      case 'sourceType':
        return 100;
      case 'operation':
        return 80;
      default:
        return 150;
    }
  }

  // 权限看板查询接口
  const { run: getAuthDashboardDetail, loading: authBoardLoading, refresh: getAuthDashboardRefresh } = useRequest(getAuthDashboard, {
    manual: true,
    onSuccess: (res: any) => {
      const { limit, offset, data, total } = res
      setDataSource(data)
      setPagination({
        current: offset / 10,
        pageSize: limit,
        total: total
      })
      userAuthCustomColumnsItem()
      setTotal(total)
    }
  })

  // 查看详情接口
  const { run: getPermissionDetailByIdRun } = useRequest(getPermissionDetailById, {
    manual: true,
    onSuccess: (res: any) => {
      setPermissionDetaiModalData(res)
      setVisibleModal(true)
    }
  })

  // 删除权限操作接口
  const { run: delPermissionInfoRun } = useRequest(delPermissionInfo, { manual: true })

  // 删除权限看板对应数据的接口
  const { run: delPermissionRecordRun } = useRequest(delPermissionRecord, { manual: true })

  useEffect(() => {
    if (state?.globalSearchRecordPosition) {
      //处理分页 并选中
      const pageNum = Math.ceil(Number(state.globalSearchRecordPosition) / 10) -1;
     
     if (pageNum !== pagination.current) {
      handlePaginationChange(pageNum, pagination?.pageSize)
     }
    }
  }, [state?.globalSearchRecordPosition])
  
  const isSelectedRowIndex = useMemo(() => {
    if (!state?.globalSearchRecordPosition) {
      return null;
    }
    const pageNum = Math.ceil(Number(state.globalSearchRecordPosition) / 10) -1;
    if (pageNum === pagination?.current) {
      const itemIndexInPage = state.globalSearchRecordPosition % pagination.pageSize;
      return itemIndexInPage === 0 ? pagination.pageSize - 1 : itemIndexInPage - 1;
    }
  },[pagination?.current, state?.globalSearchRecordPosition])

  const getAuthDetail = (permissionId: number) => {
    getPermissionDetailByIdRun(permissionId)
  }

  // 查询并设置columns
  const userAuthCustomColumnsItem = (columns?: any[], callback?: () => void) => {
    const params = {
      userId,
      type: 'PERMISSION',
      columns
    }
    // 查询 column
    getAndSetAuditColumn(params).then((res: any) => {
      let { columns = [] } = res
      // @ts-ignore
      let newColumns = columns?.filter((i: string) => AUTH_BOARD_DETAIL_COLUMN[i] !== undefined)
      if (!columns?.length) {
        newColumns = AUTH_BOARD_DETAIL_DEFAULT_COLUMN
      }
      newColumns = newColumns?.map((i: string, index: number) => {
        let item: any = {
          // @ts-ignore
          title: t(AUTH_BOARD_DETAIL_COLUMN[i]),
          dataIndex: i,
          width: setColumnsWidth(i),
          ellipsis: true,
          fixed: !index ? 'left' : '',
          render: (txt: string, record: any) => renderColumnContent(txt, i, record)
        }
        return item
      })
      newColumns.push({
        title: t("auays:tb_title.operation"),
        dataIndex: "operation",
        width: setColumnsWidth("operation"),
        fixed: "right",
        render: (txt: string, record: any) => renderColumnContent(txt, "operation", record)
      })
      setColumns(newColumns)
      callback && callback()
    }).catch()
  }

  const handleSetColumns = () => {
    setDrawerVisible(true)
  }

  const handleSetting = (columns: any[]) => {
    // 查询并设置columns
    userAuthCustomColumnsItem(columns, () => {
      setDrawerVisible(false)
    })
  }

  // 删除权限操作
  const handleDeleteConfirm = async (item: any) => {
    if (item?.userId) {
      try {
        // 先删除权限
        const delParams = {
          userId: item?.userId,
          permissionIds: [item?.permissionId],
          operationMap: {[item?.permissionId]: [item?.operation]},
        }
        await delPermissionInfoRun(delParams);
        // 再删除对应列表记录（因为权限和列表数据是异步的，单独删除权限后，刷新列表可能权限记录还存在）
        const params = {
          userId: item?.userId,
          permissionId: item?.permissionId,
          operationMap: {[item?.permissionId]: [item?.operation]},
        }
        await delPermissionRecordRun(params);

        message.success(t('common.message.delete_success'));
        getAuthDashboardRefresh();
      } catch (error) {
        message.error(t('common.massage.delete.failed'));
      }
    }
  }

  const exportFile = (type: string) => {
    const params = {
      ...searchValues,
      'type': (type === "excel" ? "xlsx" : type),
    }
    exportAuthFile(params)
    .then(() => {
      exportTaskCreatedNot();
    })
  }

  const handlePaginationChange = (page: number, pageSize: any) => {
    setPagination({
      current: page,
      pageSize: pageSize,
      total: total
    })
    const param: AuthDashboardParams = {
      ...searchValues,
      offset: page,
      limit: pageSize,
      resourceCondition: searchResourceCondition,
    }
    getAuthDashboardDetail(param)
  }

  //单独 资源nodePath 图标展示
  const renderWithOneDatabaseNodeIcon = (
    record: any,
    txt: string
  ) => {
    return (
      <>
        <Iconfont
          type={`icon-${record?.connectionType ?? 'database'}`}
          style={{ fontSize: '16px', marginRight: 4 }}
        />{txt}
      </>
    );
  };

  const renderColumnContent = (txt: any, key: string, record: any) => {
    switch (key) {
      case 'permissionId':
        return <Button
          type='link'
          className={styles.linkBtn}
          onClick={() => getAuthDetail(txt)}
        >
          {t("auays:btn.permission_details")}
        </Button>
      case 'nodePath':
        return <>
          <Tooltip
            title={renderWithOneDatabaseNodeIcon(record, txt)}
            placement="topLeft"
          >
            <Iconfont
              type={`icon-${record?.connectionType ?? "database"}`}
              style={{ fontSize: "16px", marginRight: 4 }}
            />
            {txt}
          </Tooltip>
        </>
      case 'operation':
        // 授权来源为自动授权的不可删除
        const disabled = ['Automatic Authorization', '自动授权'].includes(record?.sourceType)
        if(disabled){
          return <span className='disabled'>{t("auays:btn.delete")}</span>
        }
        return <Popconfirm
          title={t("auays:pop_title.confirm_permission_deletion", { userName: record?.user, permissionTemplate: record?.permissionTemplate })}
          onConfirm={() => handleDeleteConfirm(record)}
          okText={t("auays:btn.confirm")}
          cancelText={t("auays:btn.cancel")}
          placement="topRight"
        >
          <Button
            type='link'
            className={styles.linkBtn}
          >
            {t("auays:btn.delete")}
          </Button>
        </Popconfirm>
      default:
        return txt || '-'
    }
  }

  // 搜索值
  const handleSearch = (e: any) => {
    const val = e.target.value?.trim();
    setSearchValues((values: any) => {
      const newValues = {
        ...values,
        resourceCondition: val,
      }
      getAuthDashboardDetail(newValues)
      return newValues
    })
    setSearchResourceCondition(val);
  };
  
  const renderList = useMemo(() => {
    return (
      <Table
        rowClassName={(_, index) => index === isSelectedRowIndex ? 'globalSearchRowSelected' : ''}
        columns={columns}
        dataSource={dataSource}
        pagination={{
          ...pagination,
          current: Number(pagination.current) + 1,
          showTotal: (total: number) => t("auays:tb.show_total", { total }),
          showSizeChanger: true,
          size: 'default'
        }}
        scroll={{ x: getScrollX(columns), y: `calc(100vh - 497px)` }}
        onChange={(pagination) => {
          const current = (pagination?.current || 1) - 1
          const pageSize = pagination?.pageSize || 10
          handlePaginationChange(current, pageSize)
        }}
        className={styles.customTable}
        loading={authBoardLoading}
      />
    )
  }, [columns, dataSource, authBoardLoading, pagination, total])

  // 渲染审计概览
  const gotoAuditView = () => {
    dispatch(setOverviewPageState(''))
    dispatch(setOverviewPageDetailParams({}))
  }
  return (
    <div className="cq-container">
      <Spin spinning={false}>
        <div style={{ padding: '10px 20px', display: 'flex', alignItems: 'center', justifyContent: 'space-between' }} className="div-breadcrumb">
          <Breadcrumb className={styles.breadcrumb} separator=''>
            <Breadcrumb.Item>{t("auays:bc_title.audit_analysis")}</Breadcrumb.Item>
            <Breadcrumb.Separator>|</Breadcrumb.Separator>
            {
              location?.state?.tertiaryDirectoryMark === 1 ?
                <>
                  <Breadcrumb.Item><span onClick={gotoAuditView}>{t("auays:bc_title.audit_overview")}</span></Breadcrumb.Item>
                  <Breadcrumb.Separator>/</Breadcrumb.Separator>
                </>
                : <></>
            }
            <Breadcrumb.Item>{t("auays:bc_title.perms_dashboard")}</Breadcrumb.Item>
          </Breadcrumb>
        </div>
        <div className={styles.content}>
          <div className={styles.topWrap}>
            <SearchModal
              userAuthCustomColumnsItem={userAuthCustomColumnsItem}
              getAuthDashboardDetail={getAuthDashboardDetail}
              setSearchValues={setSearchValues}
              pageSize={pagination.pageSize}
            />
          </div>
          <div className={classnames(styles.tableTopWrap, styles.padding12)}>
            <Input
              placeholder={t("auays:inp_ph.search_resource")}
              style={{ width: 200 }}
              prefix={<SearchOutlined />}
              onChange={handleSearch}
              value={searchResourceCondition}
              allowClear
            />
            <div className={styles.rightOperations}>
              <Dropdown overlay={menu}>
                <Button
                  type="link"
                  className={classnames(styles.marginRight12)}
                >
                  <VerticalAlignBottomOutlined />{t("auays:btn.export")}
                </Button>
              </Dropdown>
              <SettingOutlined onClick={handleSetColumns} />
            </div>
          </div>
          <Divider />
          <div className={classnames(styles.tableWrap, styles.padding12)}>
            {renderList}
          </div>
        </div>
      </Spin>
      <PermissionDetailModal
        visible={visibleModal}
        setVisible={setVisibleModal}
        permissionDetaiModalData={permissionDetaiModalData}
      />
      <CustomColumnsDrawer
        visible={drawerVisible}
        setVisible={setDrawerVisible}
        type={'auth_board'}
        handleOk={handleSetting}
        defaultValue={columns}
      />
    </div>
  )
}

export default PerimissionBoard

