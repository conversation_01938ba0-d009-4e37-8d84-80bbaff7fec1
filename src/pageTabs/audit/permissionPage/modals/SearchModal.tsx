import React, { useEffect, useState } from 'react'
import { But<PERSON>, Col, DatePicker, Form, Row, Select, Input } from 'antd'
import styles from './index.module.scss'
import { AuthDashboardParams, getAuditUsers, getNodeTypeOptions } from 'src/api'
import SelectTreeData from './SelectTreeData'
import { PERMISSION_TYPE } from 'src/constants'
import { useTranslation } from 'react-i18next'
import { useSelector } from 'src/hook'
import moment from 'moment'

interface IUsers {
  userId: string
  userName: string
}

interface IProps {
  userAuthCustomColumnsItem: () => void
  [p: string]: any
  pageSize: number
}

const SearchModal = (props: IProps) => {
  const { t } = useTranslation()
  const { getAuthDashboardDetail, setSearchValues, pageSize } = props
  const { locales } = useSelector(state => state.login)
  const [form] = Form.useForm()
  const { RangePicker } = DatePicker
  const [ userOptions, setUserOptions ] = useState<any>()
  const [ nodePathValues, setNodePathValues ] = useState<string[]>([])
  const [otherEffectiveTimeType, setOtherEffectiveTimeType] = useState<string | undefined>(undefined)
  const [ permissionTypeOptions, setPermissionTypeOptions ] = useState<{label: string, value: string}[]>([])
  const [ resourceTypeOptions, setResourceTypeOptions ] = useState<{label: string, value: string}[]>([]) // 资源类型options
  // 生效时间options（其他状态）
  const otherEffectiveTimeTypeOptions: { label: string, value: string }[] = [
    { label: t("auays:opt_lbl.permanent"), value: 'permanent' },
    { label: t("auays:opt_lbl.custom_time_period"), value: "custom" }
  ]

  useEffect(()=> {
    setPermissionTypeOptions(()=>{
      return PERMISSION_TYPE.map(item => {
        const { key, value } = item
        return {
          label: t(value),
          value: key
        }
      })
    })
    searchData({})
  }, [locales])

  useEffect(()=> {
    // 用户名筛选–获取审计范围内的用户
    getAuditUsers().then((res: any) => {
      res = res.map((item: IUsers) => {
        const { userName, userId } = item
        const newItem = {
          label: `${userName}(${userId})`,
          value: userId
        }
        return newItem
      })
      setUserOptions(res)
    });
    // 资源类型筛选–获取资源类型
    getNodeTypeOptions().then((res: any) => {
      const options = res.map((item: string) => ({
        label: item,
        value: item
      }))
      setResourceTypeOptions(options)
    })
  }, [])
  
  // 查询
  const searchData = (values: any) => {
    // 授权时间
    let authTimeBegin: string | undefined = undefined
    let authTimeEnd: string | undefined = undefined
    if (values?.authTime) {
      authTimeBegin = values?.authTime[0]?.startOf('d').valueOf()
      authTimeEnd = values?.authTime[1]?.endOf('d').valueOf()
    }
    // 生效时间 数组且expirationTime?.[0] !== null 证明是时间段
    let effectiveTimeBegin: string | undefined = undefined
    let effectiveTimeEnd: string | undefined = undefined
    if (values?.expirationTime && values?.expirationTime?.[0] !== null) {
      effectiveTimeBegin = values?.expirationTime[0]?.startOf('d').valueOf()
      effectiveTimeEnd = values?.expirationTime[1]?.endOf('d').valueOf()
    }
    // 数据库
    let nodePathValuesNews: string[] = []
    let dataSourceNameValuesNews: string[] = []
    nodePathValues.map((item) => {
      if (item.split(";").length < 2) {
        dataSourceNameValuesNews.push(item)
      } else {
        nodePathValuesNews.push(item.split(";")[1])
      }
    })
    // 接口参数
    const param: AuthDashboardParams = {
      userId: values?.userId,
      nodePath: nodePathValuesNews?.length ? nodePathValuesNews : undefined,
      dataSourceName: dataSourceNameValuesNews?.length ? dataSourceNameValuesNews : undefined,
      permissionType: values?.permissionType,
      authTimeBegin: authTimeBegin,
      authTimeEnd: authTimeEnd,
      permissionName: values?.permissionName,
      effectiveTimeBegin,
      effectiveTimeEnd,
      otherEffectiveTimeType: values?.otherEffectiveTimeType,
      authUserId: values?.authUserId, // 授权人
      resourceType: values?.resourceType,
      offset: 0,
      limit: pageSize,
    }
    // 删除多余参数
    delete values.expirationTime
    getAuthDashboardDetail({...values, ...param})
    setSearchValues({...values, ...param})
  };
  // 重置
  const onReset = () => {
    form.resetFields()
    Promise.all([setSearchValues({}), setNodePathValues([])]).then(() => {
      getAuthDashboardDetail({
        offset: 0,
        limit: pageSize,
      })
    }).catch((err) => {
      console.error(t("auays:clg_err.form_reset_failure"), err);
    })
  }

  // 生效时间的onCalendarChange -- 待选日期发生变化的回调
  const handleCalendarChange = (dates: any, dateStrings: any, info: any) => {
    if (info?.range === 'end' && dates && dates?.[0] === undefined && dates?.[1] === undefined) {
      // 清空expirationTime值, 设置otherEffectiveTimeType为permanent
      form.setFieldsValue({
        expirationTime: undefined,
        otherEffectiveTimeType: 'permanent'
      });
      setOtherEffectiveTimeType('permanent')
    }
  }

  // 生效时间下拉框 清除回调
  const handleEffectiveTimeTypeClear = () => {
    form.setFieldsValue({
      expirationTime: undefined,
      otherEffectiveTimeType: undefined
    })
    setOtherEffectiveTimeType(undefined)
  }

  // 生效时间下拉框 onChange回调
  const handleEffectiveTimeTypeChange = (value: string) => {
    form.setFieldsValue({
      expirationTime: undefined,
      otherEffectiveTimeType: value === 'custom' ? undefined : value
    })
    setOtherEffectiveTimeType(value === 'custom' ? undefined : value)
  }

  return (
    <div className={styles.searchModal}>
      <Form
        key='authRenderSearch'
        form={form}
        onFinish={searchData}
        className={styles.searchModalForm}
      >
        <Row gutter={10}>
          <Col span={5}>
            <Form.Item
              label={t("auays:item_label.username")}
              name="userId"
            >
              <Select
                placeholder={t("auays:sele_ph.username")}
                mode="multiple"
                allowClear
                options={userOptions}
                showArrow
                maxTagCount={1}
                maxTagTextLength={8}
                optionFilterProp="label"
              />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              label={t("auays:item_label.database")}
              name="nodePath"
            >
              <SelectTreeData getNodePathValues={setNodePathValues}/>
            </Form.Item>
          </Col>
          <Col span={5}>
            <Form.Item
              label={t("auays:item_label.permission_type")}
              name="permissionType"
            >
              <Select
                placeholder={t("auays:sele_ph.permission_type")}
                mode="multiple"
                allowClear
                options={permissionTypeOptions}
                showArrow
                maxTagCount={1} // 最多显示多少个 tag
                maxTagTextLength={8}
              />
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item
              label={t("auays:item_label.authorization_time")}
              name="authTime"
            >
              <RangePicker style={{width: '100%'}} />
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item
              label={t("auays:item_title.permission_level")}
              name="permissionName"
            >
              <Input/>
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item
              label={t("auays:item_title.resource_type")}
              name="resourceType"
            >
              <Select
                placeholder={t("auays:sele_ph.select_resource_type")}
                mode="multiple"
                allowClear
                options={resourceTypeOptions}
                showArrow
                maxTagCount={1}
                maxTagTextLength={20}
                optionFilterProp="label"
              />
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item
              label={t("auays:item_title.authorizer")}
              name="authUserId"
            >
              <Select
                placeholder={t("auays:sele_ph.select_authorizer")}
                mode="multiple"
                allowClear
                options={userOptions}
                showArrow
                maxTagCount={1}
                maxTagTextLength={8}
                optionFilterProp="label"
              />
            </Form.Item>
          </Col>
          <Col span={6}>
            {/* 时间段生效时间的表单项，expirationTime */}
            <Form.Item
              label={t("auays:item_title.effective_time")}
              name="expirationTime"
              hidden={!!otherEffectiveTimeType}
            >
              <RangePicker
                style={{ width: '100%' }}
                ranges={{
                  [t("auays:opt_lbl.permanent")]: [undefined, undefined] as any,
                  [t("auays:range_tip.today")]: [moment(), moment()],
                }}
                onCalendarChange={handleCalendarChange}
              />
            </Form.Item>
            {/* 非时间段生效时间的表单项，otherEffectiveTimeType */}
            {/* 值由handleCalendarChange设置 永久：permanent 自定义时间段：custom*/}
            <Form.Item
              label={t("auays:item_title.effective_time")}
              name="otherEffectiveTimeType"
              hidden={!otherEffectiveTimeType}
            >
              <Select
                allowClear
                options={otherEffectiveTimeTypeOptions}
                onClear={handleEffectiveTimeTypeClear}
                onChange={handleEffectiveTimeTypeChange}
              />
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={10}>
          <Col offset={19} span={5}>
            <Form.Item style={{textAlign: 'right'}}>
              <Button type="primary" htmlType="submit" className={styles.marginRight10}>{t("auays:btn.query")}</Button>
              <Button type="default" htmlType="button" onClick={onReset} className={styles.resetBgColor}>{t("auays:btn.reset")}</Button>
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </div>
  )
}

export default SearchModal
