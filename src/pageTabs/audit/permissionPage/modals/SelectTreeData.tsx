import React, { useCallback, useEffect, useState } from 'react'
import * as _ from 'lodash';
import { TreeSelect } from 'antd'
import type { DataNode } from 'antd/es/tree'
import { useTranslation } from 'react-i18next'
import { TreeSelectProps } from 'antd/lib/tree-select'
import { LegacyDataNode } from 'rc-tree-select/lib/interface'
import {
  PERMISSIONElementTreeNodeEntity,
  PERMISSIONElementTreeRootNodeEntity,
  getDatabaseConnectionsList,
  getDatabaseNode,
  getSupportedConnectionTypes
} from 'src/api'
import { useRequest } from 'src/hook'
import { Iconfont } from 'src/components'
import { findAllParentKeys } from 'src/util/recursionSearch';

/** 元素节点元信息 */
type ElementRootNodeProps = Pick<
  PERMISSIONElementTreeRootNodeEntity,
  | 'connectionId'
  | 'connectionType'
  | 'nodeName'
  | 'nodePath'
  | 'nodePathWithType'
  | 'nodeType'
>
/** 元素节点元信息 */
type ElementNodeProps = Pick<
  PERMISSIONElementTreeNodeEntity,
  | 'connectionId'
  | 'connectionType'
  | 'nodeName'
  | 'nodePath'
  | 'nodePathWithType'
  | 'nodeType'
  | 'sdt'
  | 'dataSourceName'
  | 'dataSourceType'
>
type ElementNodePrams = Pick<
  PERMISSIONElementTreeNodeEntity,
  | 'connectionId'
  | 'connectionType'
  | 'nodeName'
  | 'nodePath'
  | 'nodePathWithType'
  | 'nodeType'
>

interface IProps extends TreeSelectProps<any> {
  defaultValue?: string[]
  getNodePathValues: (s: string[]) => void
  endInConnection?: boolean //是否连接作为叶子节点
}

const SelectTreeData = (props: IProps) => {
  const { getNodePathValues, defaultValue = [], endInConnection = false } = props
  const { t } = useTranslation()
  const [treeLoadedKeys, setTreeLoadedKeys] = useState<React.Key[]>([]) // 已加载的节点
  const [treeExpandedKeys, setTreeExpandedKeys] = useState<React.Key[]>([])
  const { SHOW_PARENT } = TreeSelect
  const [treeData, setTreeData] = useState<DataNode[]>([])
  const [value, setValue] = useState<string[]>([])
  const [searchValue, setSearchValue] = useState<string>()

  // 初始化
  useEffect(() => {
    if (!_.isEqual(value, defaultValue)) {
      setValue([...defaultValue])
    }
  }, [JSON.stringify(defaultValue)])

  // 获取数据库list
  const {
    data: databaseList,
    run: getConnectionTypes,
  } = useRequest(getSupportedConnectionTypes, {
    manual: true,
    formatResult: (data) => {
      // 构造与子节点结构统一的连接节点
      const databaseList = data.map((node: any) => {
        const {
          dataSourceName: dbName,
          dataSourceType,
        } = node
        const icon = `icon-${dbName}`
        const nodeProps: { dataSourceName: string, dataSourceType: string } = {
          dataSourceName: dbName,
          dataSourceType,
        }
        // 语句详情中要求数据库类型名称大写
        let dataSourceName = endInConnection ? dbName.toUpperCase() : dbName
        const treeNode = {
          key: dataSourceName,
          value: dataSourceName,
          title: dataSourceName,
          nodeName: dataSourceName, //公用搜索方法需要该字段
          icon: <Iconfont type={icon} />,
          props: nodeProps,
          selectable: false,
          dataSourceName,
          isLeaf: false,
        };
        return treeNode
      })
      return databaseList
    }
  })

  // 获取元素树连接层级
  const {
    run: fetchLists,
  } = useRequest(getDatabaseConnectionsList, {
    manual: true,
    formatResult: (data) => {
      // 构造与子节点结构统一的连接节点
      const databaseConnectionsList = data.map((node: any) => {
        const {
          connectionId,
          nodeName,
          nodeType,
          nodePath,
          nodePathWithType,
          connectionType,
        } = node
        const icon = `icon-connection-${connectionType}`
        const nodeProps: ElementRootNodeProps = {
          connectionId,
          connectionType,
          nodePath,
          nodePathWithType,
          nodeType,
          nodeName,
        }

        const treeNode = {
          key: nodePath + ";" + nodePathWithType, // 下一层级后端接口需要 nodePathWithType,但是nodePathWithType不唯一,nodePath唯一,所以进行拼接
          value: nodePath + ";" + nodePathWithType,
          title: nodeName,    
          nodeName: nodeName,
          icon: <Iconfont type={icon} />,
          props: nodeProps,
          selectable: false,
          connectionType,
          isLeaf: false,
        };
        return treeNode
      })
      return databaseConnectionsList
    },
  })

  // 获取数据库元素节点子列表
  const { run: loadChildren } = useRequest(getDatabaseNode, {
    manual: true,
    fetchKey: (params) => params.nodePathWithType,
    formatResult: (data) => data.map(mapDataToTreeNode),
  })

  const mapDataToTreeNode = useCallback((data: any) => {
    // 从 data 中解构出需要的参数
    const {
      connectionId,
      connectionType,
      nodeName,
      nodePath,
      nodePathWithType,
      nodeType,
      parentId,
      sdt,
    } = data
    let nodeProps: ElementNodePrams = {
      connectionId,
      connectionType,
      nodeName,
      nodePath,
      nodePathWithType,
      nodeType,
    }

    if (!connectionId && sdt) {
      nodeProps.connectionId = sdt.connectionId
      nodeProps.connectionType = sdt.connectionType
    }
    const icon = (nodeType === "connection" ? `icon-connection-${connectionType}` : `icon-${nodeType}`)
    const treeNode = {
      key: nodePath + ";" + nodePathWithType,
      value: nodePath + ";" + nodePathWithType,
      title: nodeName,
      nodeName: nodeName,
      icon: <Iconfont type={icon} />,
      props: nodeProps,
      parentId: parentId,
      isLeaf: !sdt.hasChild,
    };
    return treeNode
  }, [])

  useEffect(() => {
    getConnectionTypes()
  }, [getConnectionTypes])

  useEffect(() => {
    if (databaseList && Object.keys(databaseList)?.length) {
      setTreeData(databaseList)
    }
  }, [databaseList])

  useEffect(() => {
    if (searchValue && !_.isEmpty(treeData)) {
      //根据搜索内容找到需要展开的节点
      const allParentKeys = findAllParentKeys(treeData, searchValue);
      const keys: any = [...allParentKeys]
      setTreeExpandedKeys(keys)
    }
  }, [searchValue])

  // 异步加载数据
  const loadData = async (node: LegacyDataNode) => {
    const key: any = node?.key
    const props: ElementNodeProps = node.props
    if (Object.keys(node.props).includes("dataSourceName")) {
      try {
        const res = await fetchLists(props.dataSourceName)
        const children = res?.map((i: any) => ({
          ...i,
          key: endInConnection ? 'connection:' + i?.props?.connectionId : i?.props?.nodePath + ";" + i?.props?.nodePathWithType,
          // 连接作为叶子节点时，value取id，connection:作为区分标记
          value: endInConnection ? 'connection:' + i?.props?.connectionId : i?.props?.nodePath + ";" + i?.props?.nodePathWithType,
          title: i?.props?.nodeName,
          connectionType: i?.props?.connectionType,
          isLeaf: endInConnection ? true : i?.isLeaf,
        })) || []

        const recursionTree = (data: any[], connectionType: string) => {
          return data?.map((i: any)=>{
            if(i?.children?.length){
              recursionTree(i?.children, connectionType)
            }else if(i?.props?.dataSourceName === connectionType){
              i.children = children
            }
            return {...i}
          })
        }
        setTreeData((data: any)=>{
          setTreeLoadedKeys((keys) => [...keys, key])
          return recursionTree(data, res[0]?.connectionType)
        })
        // 懒加载成功，加入 loadedKeys
        setTreeLoadedKeys((keys) => [...keys, key])
      } catch {
        // 加载失败, 自动收起节点
        console.log(t("auays:clg_err.async_loading_failed"))
        setTreeExpandedKeys((keys) => keys.filter((el) => el !== key))
      }
    } else {
      const {
        connectionId,
        connectionType,
        nodeName,
        nodePath,
        nodePathWithType,
        nodeType
      } = props
      const paraments = {
        connectionId: connectionId,
        connectionType,
        nodeName,
        nodePath,
        nodePathWithType,
        nodeType,
        isObjectAudit: true //后端要求
      }
      try {
        const res = await loadChildren({
          ...paraments,
        })
        const children = res?.map((i: any) => ({
          ...i,
          key: i?.props?.nodePath + ";" + i?.props?.nodePathWithType,
          value: i?.props?.nodePath + ";" + i?.props?.nodePathWithType,
          title: i?.props?.nodeName,
          connectionType: i?.props?.connectionType,
          isLeaf: i?.isLeaf,
        })) || []

        const recursionTree = (data: any[], parentId: string) => {
          return data?.map((i: any)=>{
            if(i?.children?.length){
              recursionTree(i?.children, parentId)
            }else if(i?.props?.nodePath===parentId){
              i.children = children
            }
            return {...i}
          })
        }
        setTreeData((data: any)=>{
          setTreeLoadedKeys((keys) => [...keys, key])
          return recursionTree(data, res[0]?.parentId)
        })
        // 懒加载成功，加入 loadedKeys
        setTreeLoadedKeys((keys) => [...keys, key])
      } catch {
        // 加载失败, 自动收起节点
        console.log(t("auays:clg_err.async_loading_failed"))
        setTreeExpandedKeys((keys) => keys.filter((el) => el !== key))
      }
    }
  }

 const getTargetNode = (treeData: any,key: string) => {
    const queue = [...treeData]
    while (queue.length) {
      const currentNode = queue.shift()
      if (!currentNode) return
      if (currentNode.key === key) {
        return currentNode
      }
      if (currentNode.children) {
        queue.push(...currentNode.children)
      }
    }
  }

  const handleTreeExpand = (keys: any[]) => {
    if (searchValue) {
      //如果有未加载的key，则进行加载数据
      const addKey = keys.find((k: any) => !treeLoadedKeys.includes(k))
      if (addKey) {
        //根据key查找当前节点信息
        const node: any = getTargetNode(treeData as any, addKey);
        loadData(node)
      }
    }
    setTreeExpandedKeys(keys)
  };
  const onChange = (newValue: string[]) => {
    getNodePathValues(newValue)
    setValue(newValue)
  }

  return (
    <TreeSelect
      className={props?.className}
      key={"databaseTree"}
      treeData={treeData}
      value={value}
      allowClear
      loadData={loadData}
      onSearch={(val: string) =>  setSearchValue(val)}
      treeLoadedKeys={treeLoadedKeys}
      treeExpandedKeys={treeExpandedKeys}
      onTreeExpand={handleTreeExpand}
      onChange={onChange}
      multiple={false}
      treeCheckable={true}
      showCheckedStrategy={SHOW_PARENT}
      showArrow
      placeholder={props.placeholder ?? t("auays:sele_ph.database_element")}
      treeIcon={true} //展示 TreeNode title 前的图标
      maxTagCount={1} // 最多显示多少个 tag
      maxTagTextLength={5}
      dropdownStyle={{ width: 'maxContent', maxHeight: "400px" }} // 设置下拉框宽度自适应于内容
      treeNodeFilterProp='title'  // 输入项过滤对应的 treeNode 属性
    />
  );
};

export default SelectTreeData;