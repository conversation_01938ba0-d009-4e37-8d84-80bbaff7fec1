.colStyle {
  margin-top: 3px;
  padding-top: 3px;
  :global {
    .col {
      width: 100%;
      height: 100%;
      margin-top: 6px;
      text-align: center;
      span {
        width: 90%;
        height: 90%;
        display: block;
        border-radius: 3px;
        border: 0px black solid;
        background-color: #D8D8D8;
        padding: 1px;
        font-size: 13px;
      }
    }
  }
}

.checkboxGroup {
  :global {
    .ant-checkbox-group-item {
      display: block;
    }
    .ant-checkbox-wrapper {
      margin-bottom: 8px;
    }
  }
}

.searchModal {
  padding: 0 12px;
  .searchModalForm {
    // width: max-content;
    // margin: 0 auto;
    .marginRight10 {
      margin-right: 10px;
    }
    .width250 {
      width: 250px;
    }
    .width270 {
      width: 270px;
    }
  }
}

.modalDiv {
  :global {
    .ant-modal-body {
      padding-top: 0px;
    }
    .ant-tabs-nav::before,
    .ant-tabs-nav::after {
      display: none;
    }
  }
  .rowsDiv{
    height: 400px;
    overflow-y: scroll;
  }
  .empty {
    margin-top: 10px;
  }
}