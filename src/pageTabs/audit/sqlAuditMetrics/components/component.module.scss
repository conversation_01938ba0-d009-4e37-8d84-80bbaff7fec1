.metricsPromptArea {
  width: 100%;
  max-height: calc(100% - 4px);
  overflow: hidden auto;

  .metricsPromptCollapse {
    width: 100%;
    font-size: 16px;
    color: #4E5969;
    background-color: #FFFFFF;

    :global {
      .ant-collapse-content {
        border-top: none;

        .ant-collapse-content-box {
          padding: 4px 16px 12px;
        }
      }

      .ant-collapse-header .ant-collapse-arrow {
        color: #868FA3;
        font-size: 14px;
      }
    }
  }
}

.metricsPromptTree {
  :global {
    .ant-tree-treenode {
      width: 100%;
    }

    .ant-tree-node-content-wrapper {
      flex-grow: 1;
    }
  }
}

.customSQLArea {
  height: 35%;

  .cardTitle {
    color: #22223f;
    display: flex;
    align-items: center;
    justify-content: flex-start;

    .titleIcon {
      margin-left: 8px;
      color: #C8C8C8;
    }
  }

  .customSQLEditor {
    height: 100%
  }

  :global {
    .ant-card-head {
      min-height: 48px;
    }

    .ant-card-body {
      height: calc(100% - 48px);
    }

    .ant-card-head-title {
      height: 48px;
      padding: 0;
    }

    .ant-card-extra {
      padding: 0;
      margin: 0;
    }
  }
}

.resultDisplayBoard {
  margin: 12px 0;
  height: calc(65% - 50px);

  .cardTitle {
    color: #22223f;
    display: flex;
    justify-content: flex-start;
    align-items: center;

    .infoIcon {
      margin-right: 4px;
      font-size: 14px;
    }

    .editIcon {
      margin-left: 8px;
      font-size: 14px;
    }
  }

  .cardExtra {
    .extraBtn {
      margin-left: 8px;

      :last-child {
        margin-left: 0;
      }
    }
  }

  .resTable {
    height: 100%;

    :global {

      .ant-table,
      .ant-table-container {
        height: 100%
      }

      .ant-table-header {
        border-top: 1px solid #CECECF;
        border-left: 1px solid #CECECF;
        border-right: 1px solid #CECECF;
      }

      .ant-table-body {
        scrollbar-width: thin;
        border-bottom: 1px solid #CECECF;
        border-left: 1px solid #CECECF;
        border-right: 1px solid #CECECF;
      }
    }
  }

  .defaultDisplay {
    display: flex;
    justify-content: center;
    flex-direction: column;
    align-items: center;
    height: 100%;

    .defaultImg {
      width: 400px;
      padding: 24px 4px 4px 24px;
      border: 1px dashed #b8b6b6;
    }

    .defaultTitle {
      height: 32px;
      line-height: 32px;
      font-weight: 500;
      font-size: 20px;
      color: #4E5969;
      margin: 8px 0;
    }

  }

  .noGeneraTip {
    display: flex;
    justify-content: center;
    flex-direction: row;
    align-items: center;
    height: 100%;

    .warningImg {
      margin-right: 8px;
    }

    .noGeneLabel {
      font-weight: 500;
      font-size: 20px;
      color: #868FA3
    }
  }

  :global {

    .ant-spin-nested-loading,
    .ant-spin-container {
      height: 100%
    }

    .ant-card-body {
      height: calc(100% - 56px);
    }

    .ant-card-head {
      min-height: 56px;
    }

    .ant-card-head-title {
      height: 56px;
      padding: 0;
    }

    .ant-card-extra {
      padding: 0;
      margin: 0;
    }
  }
}

.intelligentChartModal {

  .intelligentChartTitle {
    font-weight: 500;
    height: 24px;
    line-height: 24px;
    letter-spacing: 0;
  }

  .visualObjType {
    margin-bottom: 16px;

    .iconMap {
      width: 80%;
      margin-left: 10%;
      margin-top: 10px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      flex-wrap: wrap;

      .iconItem {
        width: 28px;
        height: 28px;
        background-color: #FFFFFF;
        margin-right: calc((100% - 224px) / 8);
        margin-bottom: calc((100% - 224px) / 8);
        border-radius: 4px;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;

        &:nth-child(8n + 8) {
          margin-right: 0;
        }

      }

      .iconItem:nth-child(8n+1):nth-last-child(-n+8),
      .iconItem:nth-child(8n+1):nth-last-child(-n+8)~.iconItem {
        margin-bottom: 0;
      }

      .activeIconItem {
        background-color: #DBEAFF;
      }
    }
  }

  .visualElement {
    .elementMap {
      padding-top: 12px;
    }
  }

}

.opeItem {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  padding: 0px 16px;
  height: 42px;
  border-radius: 4px;
  width: 100%;
  background: #FFFFFF;
  border: 1px dashed #B0C3ED;
  margin-bottom: 12px;

  .opeLabel {
    width: 81px;
    margin-right: 8px;
    flex: none;
    display: flex;
    justify-content: flex-start;
    align-items: center;
  }

  .opeTooltip {
    margin: 0 2px;
    color: #8c8c8c;
    font-size: 12px;
  }

  .opeNode {
    width: 100%;
    overflow: hidden;
    :global {
        .ant-select-selection-item {
          max-width: 96%;
        }
      }
  }
}

.commonDisplayCard {
  height: 500px;

  .displayTable {
    height: calc(500px - 56px);
  }

  .emptyNode {
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .noGeneraTipDiv {
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 20;
    font-weight: 500;
    font-style: italic;
    color: #868FA3;
  }
}

.dataFormatManager {
  padding-top: 12px;

  .formatOperator {
    padding-top: 12px;
  }
}