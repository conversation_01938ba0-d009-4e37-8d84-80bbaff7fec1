import React, { useEffect, useState, useMemo, useCallback } from "react";
import { VerticalAlignBottomOutlined } from '@ant-design/icons'
import * as _ from "lodash";
import { DataNode } from "antd/lib/tree";
import classnames from "classnames";
import { useTranslation } from "react-i18next";
import { useLocation } from 'react-router-dom';
import { TreeNodeProps } from "rc-tree-select/lib/TreeNode";
import { Button, Space, Spin, Tooltip, Tree, Select } from "antd";
import { queryGroupNodes, queryTreeNode } from "src/api";
import { Iconfont } from "src/components";
import { useDispatch, useRequest, useSelector } from "src/hook";
import {
	getProtectSdtSearchList,
	permissionSupport
} from 'src/api';
import {
	getCurrentModulePermissionByUrl,
} from "src/util";
import {
	matchKeyword,
	getConnExpandNodeNeedPathKeys,
	findNodeByKey,
	generateTree,
	analyzeNodeKey,
	recursionGetCNTreeData,
	formatTreeNode
} from './util';
import { setSelectedNode, setImportDesConfigModalVisible, setSelectedNodePermissionInfo, setScanCntTreeData, setScanKeyList, setActiveTab } from "../DataProtectionPageSlice";
import { ConnectionFailWarnImg } from "src/components/ConnectionFailWarnImg";
import { GloablSearchLocationState } from 'src/pageTabs/GlobalSearchModal/WorkOrderResult';
import styles from "../index.module.scss";

interface IProps {
	[p: string]: any
}

const { DirectoryTree } = Tree;

const renderOptionTitle = (i: any, searchValue: string) => {

	let iconType = `icon-${i?.nodeType === 'connection' ? i?.sdt?.connectionType : i?.nodeType}`
	const parts = i?.nodeShowName?.split(new RegExp(`(${searchValue})`, 'gi'));

	return <div>
		<Iconfont type={iconType} style={{ marginRight: 4 }} />
		<span>
			{parts?.map((part: string, index: number) =>
				part?.toLowerCase() === searchValue?.toLowerCase() ?
					<span key={index} style={{ color: '#3357ff' }}>{part}</span>
					: part
			)}
		</span>
	</div>
}

const SdtTree = React.memo((props: IProps): JSX.Element => {
	const { permissionlist } = props;
	const dispatch = useDispatch();
	const { t } = useTranslation();
	const location = useLocation();
	const { state = {} } = location as { state: GloablSearchLocationState }

	const {
		selectedNode
	} = useSelector((state) => state.dataProtection)
	const isLangEn = useSelector(state => state.login.locales) === 'en'
	const allFailedCountConnectionIds = useSelector((state) => state.login.allFailedCountConnectionIds)

	const { permissionList } = useSelector((state) => state?.login);
	const { selectedNodePermissionInfo, importDesConfigModalVisible } = useSelector(state => state.dataProtection);
	const [total, setTotal] = useState<number>(0);
	const [treeData, setTreeData] = useState<DataNode[]>([]);
	const [expandedKeys, setExpandedKeys] = useState<any[]>([])
	const [selectedKeys, setSelectedKeys] = useState<any[]>([]);
	//所有点开过的节点 做缓存{path: []}
	const [treeNodeChildrenMap, setTreeNodeChildrenMap] = useState<any>({});
	//字典搜索内容
	const [dictSearchValue, setDictSearchValue] = useState<any>(null);
	//字典搜索options
	const [dictOptions, setDictOptions] = useState<any>([])
	const [treeHeight, setTreeHeight] = useState<number>(300)
	const [loadMoreLoading, setLoadMoreLoading] = useState<any>({})

	useEffect(() => {
		queryTreeHeight()
		window.addEventListener('resize', queryTreeHeight)
		return () => {
			window.removeEventListener('resize', queryTreeHeight)
		}
	}, [])
	//全局搜索
	useEffect(() => {

		if (treeData?.length && state?.globalSearchTabKey) {
			//搜索范围暂时修改 后端说暂不到连接层
			// const filteredDataStructure = filterNodeIdNotMatch(_.cloneDeep(treeData), state.object.connectionId);
			// const parentKeys = findParentKeysFormTreeStructure({children: filteredDataStructure})
			// const node = findNodeById(treeData, state.object.connectionId)

			const node: any = treeData?.find((item: any) => item.connectionType === state.object.datasourceType);

			dispatch(setActiveTab(state.globalSearchTabKey))
			setTimeout(() => {
				if (node) {
					setSelectedKeys([node.key])
					dispatch(setSelectedNode(node));
				}
			}, 500)

		}
	}, [state?.globalSearchTabKey, treeData])

	const queryTreeHeight = () => {
		const clientHeight = document.documentElement.clientHeight
		const treeHeight = clientHeight > 520 ? clientHeight - 246 : 330
		setTreeHeight(treeHeight)
	}

	const moduleName = "DATA_PROTECT";

	//模块权限查询
	const modulePermissionObj: { isOnlyRead: boolean; roleNameList: string[] } = useMemo(() => {
		return getCurrentModulePermissionByUrl(permissionList, moduleName)
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [JSON.stringify(permissionList)])

	// 判断是否可编辑
	const { run: runPermissionSupport } = useRequest(permissionSupport, {
		manual: true,
		onSuccess: (res) => {
			dispatch(setSelectedNodePermissionInfo({
				...selectedNodePermissionInfo,
				[moduleName]: {
					"modulePermissionObj": modulePermissionObj,
					"permissionSupportData": res
				}
			}))
		}
	})

	// 左侧treeData
	const { loading: rootLoading, run } = useRequest(queryGroupNodes, { manual: true });

	//数据字典远程搜索 全部资源
	const { loading: dictLoading, run: onSearchSdtNodes } = useRequest(getProtectSdtSearchList, {
		manual: true,
		debounceInterval: 800,
		formatResult: (data) => {
			return data?.map((i: any) => ({
				...i,
				label: i?.nodeShowName,
				value: i?.nodePath,
				key: i?.nodePath,

			}))
		},
		onSuccess(res) {
			setDictOptions(res)
		}
	})

	useEffect(() => {
		run('true/true').then(res => {
			setTotal(res.total);
			const formatTreeNodes = generateTree(res?.nodeList)?.filter(node => node?.children?.length);
			let newTreeNodes = formatTreeNodes?.filter(item => {
				if (item?.id !== "Redis" && item?.id !== "MongoDB") {
					return item
				}
			})
			setTreeData(newTreeNodes);
			setSelectedKeys([newTreeNodes?.[0]?.key ?? []]);

			const cloneNode = _.cloneDeep(newTreeNodes?.[0] ?? {});
			dispatch(setSelectedNode(cloneNode as any));

			// 获取该node下，用户是否可编辑
			if (!cloneNode?.props?.nodePathWithType) {
				runPermissionSupport({
					systemPermissionType: moduleName,
					nodePathWithType: null
				})
			} else {
				runPermissionSupport({
					systemPermissionType: moduleName,
					nodePathWithType: cloneNode?.props?.nodePathWithType
				})
			}
		});
	}, [run]);

	// 默认选中
	useEffect(() => {

		const defaultSelectedItem = treeData && treeData.find((item: any) => item.nodeType === "datasource");
		if (!selectedKeys.length) {
			const cloneDefaultNode = _.cloneDeep(defaultSelectedItem);
			const defaultSelectedKeys = cloneDefaultNode?.key;
			dispatch(setSelectedNode(cloneDefaultNode as any));
			setSelectedKeys([defaultSelectedKeys]);
		}
	}, [treeData]);

	/**
	 * 获取子节点
	 */
	const { run: loadChildren, loading: childLoading } = useRequest(queryTreeNode, {
		manual: true,
		formatResult: res => {
			return res.map(formatTreeNode);
		},
	});

	const onSelectFilterOption = async (v: string, option: any) => {

		setDictSearchValue(v);
		delete option?.children;
		const searchName: string = _.last(option?.nodeShowName?.split('.')) || ''
		const { nodePath, nodeType, nodePathWithType, key, sdt = {} } = option;
		const { connectionType, groupName } = sdt;
		const allExpandKeys = getConnExpandNodeNeedPathKeys(option?.nodePath, true);
		//顶层为数据源 对应的所有nodePath
		let shouldExpandKeys = allExpandKeys.slice(0, allExpandKeys.length - 1);

		//如果选中是连接 则不需要掉接口
		if (['connection', 'group'].includes(nodeType)) {
			//展开节点是 数据源类型、组
			setExpandedKeys([`/${connectionType}`, `/${connectionType}/${groupName}`]);
			setSelectedKeys(nodePath)
			await dispatch(setSelectedNode({ ...option, key: option?.nodePath, props: { ...option, key: option?.nodePath, ...(option?.sdt || {}) } }));
			return
		}
		//再次搜索 tree已经生成 不需要再次请求接口
		if (treeNodeChildrenMap[nodePath]) {
			setDictSearchValue(searchName);
			setExpandedKeys([...new Set(shouldExpandKeys)])
			await dispatch(setSelectedNode({ ...option, key: option?.nodePath, props: { ...option, key: option?.nodePath, ...(option?.sdt || {}) } }));
			return
		}

		//父级 nodePath总长度
		const parentNodePathWithTypes = getConnExpandNodeNeedPathKeys(nodePathWithType);

		//以下层级 nodePathWithTypes 和上个节点保持一致
		const level1Names = [t('common.dictSearch.nodeName.table'), t('common.dictSearch.nodeName.foreignTable'), t('common.dictSearch.nodeName.group'), t('common.dictSearch.nodeName.collection'), t('common.dictSearch.nodeName.materializedView'), t('common.dictSearch.nodeName.keyGroup'), t('common.dictSearch.nodeName.fileGroup'), t('common.dictSearch.nodeName.functionGroup'), t('common.dictSearch.nodeName.procedureGroup'), t('common.dictSearch.nodeName.taskGroup'), t('common.dictSearch.nodeName.synonym'), t('common.dictSearch.nodeName.sequenceGroup'), t('common.dictSearch.nodeName.triggerGroup'), t('common.dictSearch.nodeName.databaseConnection'), t('common.dictSearch.nodeName.package'), t('common.dictSearch.nodeName.packageBody'), t('common.dictSearch.nodeName.taskGroup'), t('common.dictSearch.nodeName.dictionaryGroup'), t('common.dictSearch.nodeName.gridFsBucket'), t('common.dictSearch.nodeName.userDefinedFunction'), 'flexTableGroup']
		const level2Names = [t('common.dictSearch.nodeName.columnGroup'), t('common.dictSearch.nodeName.indexGroup'), t('common.dictSearch.nodeName.constraintGroup'), t('common.dictSearch.nodeName.foreignKeyGroup'), t('common.dictSearch.nodeName.triggerGroup'), t('common.dictSearch.nodeName.partitionGroup'), t('common.dictSearch.nodeName.syncMaterializedViews'), t('common.dictSearch.nodeName.asyncMaterializedViews'), t('common.dictSearch.nodeName.importTaskGroup')];
		let asyncActions: any = [];
		let nodePathWithTypeArr = parentNodePathWithTypes;

		//@ts-ignore
		shouldExpandKeys?.map((key: string, index: number) => {
			//是否是表...结尾
			let endType = key.substr(key.lastIndexOf('/') + 1);
			//如果是组 则nodePathWithType = null;
			if (level1Names.includes(endType) || level2Names.includes(endType)) {
				const preNType = nodePathWithTypeArr[index - 1]; //这里可能不对
				nodePathWithTypeArr.splice(index, 0, preNType);
			}
		})

		shouldExpandKeys.map((key, index) => asyncActions.push({ nodePath: key, nodePathWithType: nodePathWithTypeArr[index] }))

		let cloneTreeData = _.cloneDeep(treeData);
		let cloneTreeNodeChildrenMap = _.cloneDeep(treeNodeChildrenMap);

		for (const node of asyncActions) {
			try {

				//防止重新请求 导致其他查询过数据被清空
				if (!cloneTreeNodeChildrenMap[node.nodePath]) {

					const { newTreeData, newTreeNodeChildrenMap } = await fetchNodeChildren({ ...node, props: { ...node, key: node.nodePath } }, cloneTreeData, cloneTreeNodeChildrenMap)
					cloneTreeData = newTreeData;
					cloneTreeNodeChildrenMap = newTreeNodeChildrenMap;
				};

			} catch (error) {
				console.log('异步调用出错了', error);
				break;
			}
		}
		//包括数据源 或组
		shouldExpandKeys = shouldExpandKeys.concat([`/${connectionType}`, `/${connectionType}/${groupName}`])
		await setTreeData(cloneTreeData);
		setTreeNodeChildrenMap(cloneTreeNodeChildrenMap)
		setExpandedKeys([...new Set(shouldExpandKeys)])
		await dispatch(setSelectedNode({ ...option, key: option?.nodePath, props: { ...option, key: option?.nodePath, ...(option?.sdt || {}) } }));
		setDictSearchValue(searchName)
	}

	const fetchNodeChildren: any = async (node: TreeNodeProps, updatedTreeData?: any[], updateTreeNodeChildrenMap?: any) => {
		//不存在缓存 重新请求
		const props = node?.props;
		const key = node?.props?.nodePath;
		let cloneTreeData = _.cloneDeep(updatedTreeData || treeData);
		let cloneTreeNodeChildrenMap = _.cloneDeep(updateTreeNodeChildrenMap || treeNodeChildrenMap);

		const connectionId = props?.id ? props.id : props?.connectionId;
		const connectionType = props?.connection ? props?.connection?.connectionType : props?.connectionType;

		const params: any = {
			connectionId: connectionId,
			connectionType: connectionType,
			nodeType: props?.nodeType,
			nodeName: props?.nodeName,
			nodePath: props?.nodePath,
			nodePathWithType: props?.nodePathWithType,
		};
		// 根据父级参数标识给出默认分页配置
		if (node?.supportPaging) {
			params.pageNo = 1
			params.pageSize = 2000
		}
		// 根据当前节点属性设置分页配置
		if (node?.pageNo && node?.pageSize) {
			params.pageNo = node?.pageNo
			params.pageSize = node?.pageSize
		}
		const children = await loadChildren({
			...params,
		});
		// 更新页码
		let loadMoreParams: any[] = []
		if (children?.length === params?.pageSize) {
			loadMoreParams = [{
				...node,
				key: node?.key + '__loadMore',
				isLoadMore: true,
				isLeaf: true,
				hasChild: false,
				pageNo: (params?.pageNo ?? 1) + 1,
				pageSize: params?.pageSize
			}]
		}
		const target = findNodeByKey(cloneTreeData, key);
		if (target) {
			target.children = renderChildWithLoadMore(target?.children ?? [], children, loadMoreParams)
			cloneTreeNodeChildrenMap[key] = renderChildWithLoadMore(cloneTreeNodeChildrenMap[key] ?? [], children, loadMoreParams)
			return {
				newTreeData: cloneTreeData,
				newTreeNodeChildrenMap: cloneTreeNodeChildrenMap
			};
		}
	}

	const renderChildWithLoadMore = (originalData: any[], children: any[], loadMoreParams: any[]) => {
		const cloneOriginalData = _.cloneDeep(originalData)
		const filterChildren = cloneOriginalData.filter((item: any) => !item?.isLoadMore)
		const newChildren = [...filterChildren, ...children, ...loadMoreParams]
		return newChildren
	}

	const onLoadData = async (node: any) => {
		const { nodeType } = node.props;
		if (nodeType === "datasource" || nodeType === "group") {
			return new Promise<void>((resolve) => {
				resolve()
			})
		}
		let cloneTreeData = _.cloneDeep(treeData);
		let cloneTreeNodeChildrenMap = _.cloneDeep(treeNodeChildrenMap)
		try {
			const { newTreeData, newTreeNodeChildrenMap } = await fetchNodeChildren(node);
			cloneTreeData = newTreeData;
			cloneTreeNodeChildrenMap = newTreeNodeChildrenMap;
		} catch (err) {
			return new Promise<void>((resolve) => {
				resolve()
			})
		}
		setTreeData(cloneTreeData);
		setTreeNodeChildrenMap(cloneTreeNodeChildrenMap)
		return new Promise<void>((resolve) => {
			resolve()
		})
	}

	function onShowImportList() {
		dispatch(setImportDesConfigModalVisible(true))
	}

	const filterNodesNotMatch = useCallback(
		(nodes: any[]): any[] =>
			nodes.filter((node) => {

				//@ts-ignore
				if (selectedNode?.sdt?.connectionType !== (node.connectionType || node?.props?.connectionType)) return false
				const connectionId = selectedNode?.sdt?.nodeType === 'connection' ? node?.id : node?.props?.connectionId?.toString();
				//@ts-ignore
				const name: string = _.last(selectedNode?.nodeShowName?.split('.')) || ''

				const isSameConnection = (selectedNode?.connectionId === connectionId);

				//后端搜索 筛选信息只有一条， 前端筛选模糊匹配
				const keywordHit = isSameConnection && matchKeyword(node.title, name)  //确保位置准确 不然可能出现多个层级

				if (!keywordHit && node?.children) {
					node.children = filterNodesNotMatch(node.children)
				}
				return keywordHit || node.children?.length
			}),
		[dictSearchValue],
	)

	const filteredTreeData: any[] = useMemo(
		() =>
			dictSearchValue ? filterNodesNotMatch(_.cloneDeep(treeData)) : treeData,
		[dictSearchValue, JSON.stringify(treeData)],
	)

	const handleLoadMore = async (node: any) => {
		const { key } = node
		setLoadMoreLoading((loading: any) => ({ ...loading, [key]: true }))
		try {
			const { newTreeData, newTreeNodeChildrenMap } = await fetchNodeChildren(node);
			setTreeData(newTreeData);
			setTreeNodeChildrenMap(newTreeNodeChildrenMap)
		} catch (e) {
			console.error('分页加载更多error', e)
		} finally {
			setLoadMoreLoading((loading: any) => ({ ...loading, [key]: false }))
		}
	}

	const titleRender = (node: any) => {
		const { nodeType, connectionType } = node
		let icon = `icon-${nodeType}`;
		if (nodeType === 'datasource') {
			icon = `icon-connection-${connectionType}`;
		} else if (nodeType === 'connection') {
			icon = `icon-${connectionType}`
		} else if (nodeType === 'group') {
			icon = "icon-shujukuwenjianjia"
		}
		let count: number = 0;
		if (["datasource", "group"].includes(node.props.nodeType) && node?.layer?.childCount) {
			count = node.layer.childCount;
		}
		if (node?.isLoadMore) {
			return (
				<Spin spinning={!!loadMoreLoading[node?.key]}>
					<span
						className="options"
						onClick={() => handleLoadMore(node)}
					>
						{t('db.auth.dataProtection.loadingMore')}
					</span>
				</Spin>
			)
		}
		return (
			<div className={styles.treeTitleItem}>
				<Iconfont type={icon} style={{ marginRight: 4 }} />
				<span className={styles.titleTxtWrap}>
					<span className={styles.titleTxt}>{node.props.nodeName}{count ? "(" + count + ")" : ""}</span>
					{
						node?.nodeType === "connection" && allFailedCountConnectionIds?.includes(Number(node?.id)) &&
						<ConnectionFailWarnImg />
					}
				</span>
			</div>
		)
	}

	return (
		<div className={`${styles.treeContainer} guide-protect-sdt`}>
			<div className={isLangEn ? '' : styles.treeHeader}>
				<Space>
					<span style={{ color: '#667084' }}>{t('db.auth.resourceList')}</span>
					<span style={{ color: '#667084' }}>
						{total}
					</span>
				</Space>
				<Tooltip title={
					permissionlist?.isOnlyRead ?
						t('dataProtection.noPerm', { permissionlist: permissionlist.roleNameList?.join(", ") })
						: null
				}
				>
					<Button
						type="link"
						className={classnames(styles.padding0)}
						onClick={() => onShowImportList()}
						disabled={permissionlist?.isOnlyRead}
					>
						<VerticalAlignBottomOutlined rotate={-90} color="#3262FF" style={{ marginRight: 8 }} />
						{t('db.auth.dataProtection.desensImport')}
					</Button>
				</Tooltip>
			</div>
			<Select
				allowClear
				showSearch
				filterOption={false}
				loading={dictLoading}
				showArrow={false}
				value={dictSearchValue}
				dropdownMatchSelectWidth={false}
				onSelect={onSelectFilterOption}
				onClear={() => { setDictSearchValue(null); }}
				onSearch={(text) => {
					if (!text) {
						setDictSearchValue(null);
					} else {
						setDictSearchValue(text);
					}

					setDictOptions([])
					onSearchSdtNodes({ query: text })
				}}
				className={styles.protectSearch}
				placeholder={t('common.search.placeholder')}
			>
				{
					dictOptions?.map((item: any, index: number) => (
						<Select.Option {...item} key={`${item.id}-${index}`}>
							{renderOptionTitle(item, dictSearchValue)}
						</Select.Option>
					))
				}
			</Select>
			<Spin spinning={rootLoading} wrapperClassName={styles.spinContainer}>
				<DirectoryTree
					showIcon={false}
					height={treeHeight}
					className={styles.sdtTree}
					titleRender={titleRender}
					treeData={filteredTreeData}
					loadData={onLoadData}
					expandAction={false}
					selectedKeys={selectedNode?.key && !importDesConfigModalVisible ? [selectedNode.key] : undefined}
					onSelect={(_key, { node }: any) => {
						const keyList = analyzeNodeKey(node?.key);
						const connectionKey = keyList?.[0]
						const cntTreeData = recursionGetCNTreeData(filteredTreeData, connectionKey)
						setSelectedKeys([node?.key]);
						const cloneNode = _.cloneDeep(node);
						dispatch(setSelectedNode(cloneNode as any));
						dispatch(setScanCntTreeData(cntTreeData as any[]));
						dispatch(setScanKeyList(keyList as string[]));
					  // 获取该node下，用户是否可编辑
						if (!cloneNode?.props?.nodePathWithType) {
							runPermissionSupport({
								systemPermissionType: moduleName,
								nodePathWithType: null
							})
						} else {
							runPermissionSupport({
								systemPermissionType: moduleName,
								nodePathWithType: cloneNode?.props?.nodePathWithType
							})
						}
						dispatch(setImportDesConfigModalVisible(false))
					}}
					expandedKeys={[...expandedKeys]}
					onExpand={(expandedKeys) =>
						setExpandedKeys(expandedKeys)
					}
				></DirectoryTree>
				{!rootLoading && !filteredTreeData.length && (
					<div className={styles.treePlaceholder}>{t('db.auth.noElement')}</div>
				)}
			</Spin>
		</div>
	);
});

export { SdtTree };

