import React, { useRef } from 'react'
import { useSelector, useDispatch } from 'src/hook'
import { ResizableBox, ResizableProps } from 'react-resizable'
import { Row, Col } from 'antd'
import classNames from 'classnames'
import { EditorContext, useMonaco } from 'src/components'
import { Sdt } from './sdt/Sdt'
import { NodeDetailPane } from './nodeDetailPane/NodeDetailPane'
import { TaskPane } from './taskPane'
import { QueryTabs } from './queryTabs'
import { BottomToolbar } from './bottomToolbar'
import styles from './index.module.scss'
import { Iconfont, HomogeneousDataReplicationModal } from 'src/components'
import {
  useLSP,
  LanguageClientContext,
} from '../../components/BaseEditor/useLSP'
import { ModalTextImportWizard } from 'src/features/perm/modals'
import ServerFilesModal  from './queryTabs/monacoPane/monacoToolbar/ServerFilesModal'
import { ErrorBoundary } from 'src/components'
import { ApplicationShopping } from './applicationShopping'
import CreateBatchExecuteDrawer from '../sqlBatchExecution/CreateBatchExecuteDrawer'
import { setCreateBatchExecuteModalVisible } from './queryPageSlice'
import SdtShowFixBtn from './components/SdtShowFixBtn'

const ResizableBoxProps: ResizableProps = {
  axis: 'x',
  width: 368,
  height: 0,
  minConstraints: [368, 0],
  maxConstraints: [520, 0],
}

export const QueryPage = () => {
  const dispatch = useDispatch()
  const { visibleSdt, createBatchExecuteModalVisible } = useSelector((state) => state.queryPage)
  const { ModalOpenServerFile } = useSelector(state => state.modal)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const { tabInfoMap, activeTabKey } = useSelector(
    (state) => state.queryTabs,
  )
  const activePaneType = tabInfoMap[activeTabKey]?.paneType
  const compareVersion = activePaneType === 'diff'

  const ResizeHandle = (
    <div className={styles.resizeHandle}>
      <Iconfont type="icon-handle-8"></Iconfont>
    </div>
  )

  // 创建 editor 实例, 获取容器 ref
  const [editorInstance, containerRef] = useMonaco({
    automaticLayout: true,
    minimap: { enabled: false },
  })

  const { languageClient } = useLSP({ editorInstance })
  return (
    <>
    <EditorContext.Provider value={{ editorInstance, containerRef, fileInputRef }}>
      <LanguageClientContext.Provider value={{ languageClient }}>
        <section className={styles.queryPageWrapper}>
          <Row className={styles.queryPageContent}>
            <Col
              className={classNames(
                styles.sdtColumn,
                !visibleSdt && styles.hide,
              )}
            >
              <HomogeneousDataReplicationModal>
                  <ResizableBox
                    className={styles.resizableBox}
                    handle={ResizeHandle}
                    {...ResizableBoxProps}
                  >
                    <ErrorBoundary>
                      <Sdt></Sdt>
                    </ErrorBoundary>
                  </ResizableBox>
                </HomogeneousDataReplicationModal>
            </Col>
            <Col className={styles.queryColumn} flex="1">
              <ErrorBoundary>
                <QueryTabs></QueryTabs>
              </ErrorBoundary>
            </Col>
            {/* 版本对比时时隐藏此部分 */}
            {
              !compareVersion &&
              <Col className={styles.nodeDetailColumn}>
                <ErrorBoundary>
                  <NodeDetailPane />
                  {/* <TaskPane /> */}
                  <ApplicationShopping />
                </ErrorBoundary>
              </Col>
            }
          </Row>
          <ErrorBoundary>
            <BottomToolbar />
          </ErrorBoundary>
          <ErrorBoundary>
            <ModalTextImportWizard />
          </ErrorBoundary>
          <ErrorBoundary>
            <ServerFilesModal ModalOpenServerFile={ModalOpenServerFile?.visible || false} />
          </ErrorBoundary>
          {/* 左下角固定的sdt显示隐藏按钮 */}
          <SdtShowFixBtn />
        </section>
      </LanguageClientContext.Provider>
    </EditorContext.Provider>
    {/* 新建批量执行 */}
    {
      createBatchExecuteModalVisible &&
      <CreateBatchExecuteDrawer 
        visible={true}
        onClose={() => {
          dispatch(setCreateBatchExecuteModalVisible(false))
        }}
      />
    }
    </>
  )
}
