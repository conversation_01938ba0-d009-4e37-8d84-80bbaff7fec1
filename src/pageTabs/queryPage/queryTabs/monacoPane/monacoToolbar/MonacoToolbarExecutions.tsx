import React, { useCallback, useEffect, useState, useMemo } from 'react'
import { useDispatch, useRequest, useSelector } from 'src/hook'
import {
  Button,
  Col,
  Divider,
  Dropdown,
  Menu,
  Space,
  Tooltip,
  Popover,
  Typography,
  message,
  Progress,
} from 'antd'
import { CheckOutlined, DownOutlined } from '@ant-design/icons'
import classNames from 'classnames'
import { Iconfont } from 'src/components'
import {
  explainEditorSql,
  // getTxMode,
  PaneInfo,
  setPrepared,
  setTabExecutionStatus,
  updatePaneInfo,
} from '../../queryTabsSlice'
import { setNeedRefreshResultData } from 'src/pageTabs/queryPage/resultTabs/resultTabsSlice'

import styles from './index.module.scss'
import { useEditorInstance } from 'src/components/BaseEditor/useEditorInstance'
import {
  // setTxModeOfConnection,
  getExecutedSql,
  commitTransaction,
  rollbackTransaction,
  cancelExecute,
  getExecutedSqlPolling,
  queryConnectionsAndUserSettings
} from 'src/api'
import { getSelectedText, getSqlToExecute } from 'src/util'
import { TxModeType } from 'src/constants'
import { ModalShowMoreCommit } from './ModalShowMoreCommit'
import { setLastErrorPosition } from 'src/pageTabs/queryPage/resultTabs/resultTabsSlice'
import { SqlFlashBackModal } from './SqlFlashBackModal'
import ExecutionTimeCom from '../../components/ExecutionTimeCom'
import { useTranslation } from 'react-i18next'

interface MonacoToolbarExecutionsProps {
  paneInfo: PaneInfo
  onPressExecute: (flashbackSql?: string) => void
  onPreCommit?: () => void
  supportTransaction?: boolean
  showExecuteEffect?: boolean
  resetExecDecoration?: () => void
}

interface TabExecutionStatusPercentage {
  key: string
  executePercentage: number
  executeStatusMessage: string
}

let timeoutId: any = null

/**
 * sql related controllers in editor toolbar
 */
export const MonacoToolbarExecutions = React.memo(
  ({ onPressExecute, onPreCommit, paneInfo, supportTransaction = true, showExecuteEffect, resetExecDecoration }: MonacoToolbarExecutionsProps) => {
    const dispatch = useDispatch()
    const { t } = useTranslation()
    const executeActiveTabInfo = useSelector((state) => state.queryTabs.executeActiveTabInfo)
   
    //暂不使用缓存，连接配置信息修改感受不到
    const connectionsAndUserSettingInfo = useSelector((state) => state.queryTabs.connectionsAndUserSettingInfo)
    const [allowAutoCommit, setAllowAutoCommit] = useState(false)

    const {
      key: tabKey,
      connectionId,
      connectionType,
      plSql,
      pending: executionPending,
      tSql: isTSQL,
      value,
      txMode,
      menuType,
    } = paneInfo

    // 获取当前连接执行设置   最好方式直接从接口获取 防止连接设置中修改
    const { data: connectionsAndUserSetting,  run: runQueryConnSetting } = useRequest(queryConnectionsAndUserSettings,
      {
        manual: true,
        onSuccess: (res) => {
          const allowAutoCommit = res?.allowAutoCommit?.variable_value
          ? Boolean(JSON.parse(res?.allowAutoCommit?.variable_value))
          : false;
          setAllowAutoCommit(allowAutoCommit);
          let mode = 'manual' as 'auto' | 'manual'
          if (menuType !== 'call') {
            //维持已设置的事物模式 若刷新 自动时需要判断是否有权限
            mode =  txMode || mode;
            if(txMode === 'auto' && !allowAutoCommit) { 
              mode = 'manual'
            }
          }
      
          dispatch(
            updatePaneInfo({
              key: tabKey,
              paneInfo: { txMode: mode },
            }),
          )
    
          dispatch(setPrepared({ key: tabKey, prepared: true }))
        }
      },
    )

    // !HACK 需要做数据源特性统一抽象，否则将无法迭代下去
    // 获取事务等接口目标 database
    const databaseName = useMemo(() => {
      const { databaseName } = paneInfo
      return databaseName
    }, [paneInfo])

    const canExecute = value

    const dataSourceList = useSelector(
      (state) => state.dataSource.dataSourceList,
    )
    const dataSourceImplementedExplain = useMemo(
      () =>
        dataSourceList
          .filter(({ editorToolbarButtons }) =>
            editorToolbarButtons.includes('EXPLANATION'),
          )
          .map(({ dataSourceName }) => dataSourceName),
      [dataSourceList],
    )
    const noSqlDataSource = useMemo(
      () =>
        dataSourceList
          .filter(({ dataSourceType }) => dataSourceType === 'NoSQL')
          .map(({ dataSourceName }) => dataSourceName),
      [dataSourceList],
    )

    // 可以执行计划的数据源
    const supportExplanation =
      connectionType && dataSourceImplementedExplain.includes(connectionType)
    // NoSQL 类型的数据源
    const isNoSQL = connectionType && noSqlDataSource.includes(connectionType)

    const [editorInstance] = useEditorInstance()

    const setTxMode = useCallback(
      ({ tabKey, txMode, ...rest }) => {
        dispatch(updatePaneInfo({ key: tabKey, paneInfo: { txMode } }))
      },
      [dispatch],
    )

    useEffect(() => {
      if (!connectionId || !tabKey) return
      // 不支持事物时，执行中的默认值为true，自动提交。
      // http://share.bintools.cn:8080/pages/viewpage.action?pageId=2176650
      // clickhouse 不支持事物但要自动执行逻辑
      //设置supportTransaction = true 防止默认改为自动提交
      if (!supportTransaction && connectionType !== 'ClickHouse') {
        dispatch(
          updatePaneInfo({
            key: tabKey,
            paneInfo: { txMode: 'auto' },
          }),
        )
        return
      }
      if (!isNoSQL) {
        runQueryConnSetting(connectionId);
      }

    }, [connectionId, connectionType, tabKey, isNoSQL, supportTransaction, dispatch])

    // get commit list
    const {
      data: commitList = [],
      run: getCommitList,
      refresh: refreshCommitList,
    } = useRequest(getExecutedSql, {
      manual: true,
      formatResult: (data) => {
        return data?.slice().reverse()
      },
    })

    useEffect(() => {
      if (
        tabKey &&
        connectionId &&
        !executionPending && 
        txMode === 'manual' &&
        !isNoSQL 
      ) {
        getCommitList({ tabKey, connectionId, databaseName })
      }
    }, [
      connectionId,
      getCommitList,
      tabKey,
      executionPending,
      isNoSQL,
      txMode,
      databaseName,
    ])

    const disableCommitAndRollback = txMode === 'auto' || !commitList?.length
    
    useEffect(()=>{
      if(disableCommitAndRollback) return
      queryExecutedSqlPolling()
    // eslint-disable-next-line react-hooks/exhaustive-deps
    },[disableCommitAndRollback])

    // 轮询是否有未提交的语句
    const queryExecutedSqlPolling = async() => {
      if (timeoutId) {
        clearTimeout(timeoutId);
        timeoutId = null;
      }
      if (!connectionId || !tabKey) return
      const params = {
        tabKey,
        connectionId,
        databaseName,
      }
      const res: any = await getExecutedSqlPolling(params)
      if(res){
        timeoutId = setTimeout(()=>{
          queryExecutedSqlPolling()
        }, 3000)
      }else{
        clearTimeout(timeoutId);
        timeoutId = null;
        dispatch(setNeedRefreshResultData(true))
      }
    }

    // commit transaction
    const { run: commit, loading: committing } = useRequest(commitTransaction, {
      manual: true,
      fetchKey: ({ tabKey }) => tabKey,
      onSuccess: () => refreshCommitList(), //必要请求 及时修改committing状态
    })

    // rollback transaction
    const { run: rollback, loading: rollbacking } = useRequest(
      rollbackTransaction,
      {
        manual: true,
        fetchKey: ({ tabKey }) => tabKey,
        onSuccess: () => refreshCommitList(),//必要请求 及时修改rollback状态
      },
    )

    // cancel execution
    const { run: cancel, loading: cancelling } = useRequest(cancelExecute, {
      manual: true,
      onSuccess: (res) => {
        //返回true才表示取消成功
        if (!tabKey || !res) return
        dispatch(setTabExecutionStatus({ key: tabKey, pending: false }))
      },
    })

    // show more commit
    const [modalVisible, setModalVisible] = useState(false)
    const handleCloseModal = useCallback(() => {
      setModalVisible(false)
    }, [])

    const noSupportTransition =
      connectionType === 'Impala' || connectionType === 'Hive' || connectionType === 'Inceptor'

    const { tabExecutionStatusPercentageMap } = useSelector(state => state.queryTabs)

    const tabExecutionStatusPercentage = useMemo(() => {
      const status: TabExecutionStatusPercentage = tabExecutionStatusPercentageMap?.[tabKey] || {
        key: tabKey,
        executePercentage: 0,
        executeStatusMessage: t('sdo_not_executed')
      }
      return status
    }, [tabExecutionStatusPercentageMap, tabKey, t])

    const executePercentage = tabExecutionStatusPercentage.executePercentage
    const executeStatusMessage = tabExecutionStatusPercentage.executeStatusMessage

    const lastErrorPosition = useSelector((state) => state.resultTabs.lastErrorPosition)
    return (
      <Col>
        <Space size={1}>
          {isTSQL && (
            <Tooltip title={t('sdo_preflight')} placement="bottomLeft" arrowPointAtCenter>
              <Iconfont
                type="icon-yujian"
                className={classNames(
                  styles.monacoToolbarIcon,
                  executionPending && styles.iconActive,
                  !canExecute && styles.iconDisabled,
                )}
                onClick={onPreCommit}
              />
            </Tooltip>
          )}

          <Tooltip
            title={
              !connectionId ? t('sdo_select_resource_first') :
              editorInstance && getSelectedText(editorInstance)
                ? t('sdo_run_select_statement')
                : t('sdo_execute')
            }
            placement="bottomLeft"
            arrowPointAtCenter
          >
            <Iconfont
              className={classNames(
                styles.monacoToolbarIcon,
                styles.iconExecute,
                executionPending && styles.iconActive,
                (!canExecute || !connectionId) && styles.runIconDisbaled,
              )}
              type="icon-run"
              onClick={() => {
                if (executionPending || !connectionId) return
                onPressExecute()
                // 方法内部已处理，不用再处理一次
                //执行语句之前 去除对应窗口 历史错误SQL语句的位置
                // if (tabKey) {
                //   if (tabKey in lastErrorPosition && !!lastErrorPosition[tabKey]) {
                //     let tmp = { ...lastErrorPosition }
                //     tmp[tabKey] = {
                //       'endColumn': 0,
                //       'endLineNumber': 0,
                //       'startColumn': 0,
                //       'startLineNumber': 0
                //     }
                //     dispatch(
                //       setLastErrorPosition(tmp)
                //     )
                //   }
                // }
              }}
            />
          </Tooltip>
          <Tooltip title={t('sdo_terminate')} placement="bottomLeft" arrowPointAtCenter>
            <Iconfont
              type="icon-cancel"
              className={classNames(
                styles.monacoToolbarIcon,
                styles.iconCancel,
                !executionPending && styles.iconDisabled,
                cancelling && styles.iconActive,
              )}
              onClick={() => {
                if (tabKey && connectionId) {
                  if (executeActiveTabInfo && executeActiveTabInfo[tabKey]) {
                    let messageId = executeActiveTabInfo[tabKey]
                    cancel({ tabKey, connectionId, messageId, databaseName })
                  }
                }
              }}
            />
          </Tooltip>
          {supportExplanation && !plSql && (
            <Tooltip title={t('sdo_execution_plan')} placement="bottomLeft" arrowPointAtCenter>
              <Iconfont
                className={classNames(
                  styles.monacoToolbarIcon,
                  !canExecute && styles.iconDisabled,
                )}
                type="icon-explain"
                onClick={() => {
                  if (!editorInstance) return
                  // 重置sql装饰
                  resetExecDecoration && resetExecDecoration();
                  dispatch(explainEditorSql(t, getSqlToExecute(editorInstance, !showExecuteEffect)))
                }}
              />
            </Tooltip>
          )}
          {(!isNoSQL && supportTransaction) && (
            <Popover
              title={<Typography.Text strong>{t('sdo_pending_statement')}</Typography.Text>}
              content={
                <>
                  <Space direction="vertical">
                    {commitList?.slice(0, 5)?.map((statement, index) => (
                      <Tooltip title={statement}>
                        <Typography.Text
                          key={index}
                          ellipsis
                          style={{ maxWidth: 480 }}
                        >
                          {statement}
                        </Typography.Text>
                      </Tooltip>
                    ))}
                  </Space>
                  {commitList?.length && commitList.length > 5 ? (
                    <>
                      <Divider style={{ marginTop: 8, marginBottom: 8 }} />
                      <Typography.Link
                        onClick={() => setModalVisible(true)}
                      >{`${t('sdo_see_more')}（${commitList.length - 5
                        } ${t('sdo_records')}）`}</Typography.Link>
                    </>
                  ) : null}
                </>
              }
              arrowPointAtCenter
              placement="bottomLeft"
            >
              <Space
                size={1}
                className={classNames(
                  styles.commitAndRollbackGroup,
                  disableCommitAndRollback && styles.commitDisabled,
                )}
              >
                <CheckOutlined
                  className={classNames(
                    styles.monacoToolbarIcon,
                    styles.iconExecute,
                    disableCommitAndRollback && styles.iconDisabled,
                    committing && styles.iconActive,
                  )}
                  onClick={() => {
                    if (tabKey && connectionId) {
                      commit({ tabKey, connectionId, databaseName })
                    }
                  }}
                />
                <Iconfont
                  type="icon-rollback"
                  className={classNames(
                    styles.monacoToolbarIcon,
                    styles.iconCancel,
                    disableCommitAndRollback && styles.iconDisabled,
                    rollbacking && styles.iconActive,
                  )}
                  onClick={() => {
                    if (tabKey && connectionId) {
                      rollback({ tabKey, connectionId, databaseName })
                      //临时解决方案
                      //@ts-ignore
                      // global.handleRefresh && global.handleRefresh()
                    }
                  }}
                />
              </Space>
            </Popover>
          )}
          <SqlFlashBackModal
            connectionId={connectionId}
            isPending={executionPending || committing || rollbacking }
            tabKey={tabKey}
            //同手动/自动使用同一接口就好，不支持事务的数据源也不支持sql闪回
            visible={JSON.parse(connectionsAndUserSetting?.['sqlFlashBack']?.variable_value || 'false')}
            executeFlashbackSql={(flashbackSql) => onPressExecute(flashbackSql)}
          />
          {(!isNoSQL && supportTransaction) && (
            <Dropdown
              overlay={
                <Menu
                  onClick={({ key }) => {
                    if (!tabKey || !connectionId) return

                    // 手动切自动也需要校验是否存在未提交的语句
                    // http://share.bintools.cn:8080/pages/viewpage.action?pageId=2176650
                    if (
                      commitList &&
                      commitList?.length !== 0 &&
                      key === 'auto'
                    ) {
                      message.error(t('sdo_commit_error_txt'))
                      return
                    }

                    const txMode = key as TxModeType
                    setTxMode({ tabKey, connectionId, txMode, databaseName })
                  }}
                >
                  <Menu.Item key="auto" disabled={!allowAutoCommit}>
                    {t('sdo_auto')}
                  </Menu.Item>
                  <Menu.Item key="manual" disabled={noSupportTransition}>
                    {t('sdo_manual')}
                  </Menu.Item>
                </Menu>
              }
              trigger={['click']}
            >
              <Button
                className={classNames(
                  styles.monacoToolbarIcon,
                  styles.txDropdownTrigger,
                  {
                    [styles.iconDisabled]: !connectionId || executionPending,
                  },
                )}
                type="text"
                size="small"
              >
                <>
                  {`Tx: ${txMode === 'auto' ? t('sdo_auto') : t('sdo_manual')}`}
                  <DownOutlined style={{ marginLeft: 2 }} />
                </>
              </Button>
            </Dropdown>
          )}

          <div>
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <div style={{ flex: 1 }}>
                <div style={{
                  marginTop: '15px', // 调整进度条向下移动的距离
                }}>
                  <Tooltip title={`${t('sdo_progress')}：${executePercentage}%`}>
                    <Progress
                      percent={executePercentage}
                      status="active"
                      style={{
                        background: 'linear-gradient(90deg, #1890ff ' + executePercentage + '%, transparent 0%)',
                        height: '8px',
                        borderRadius: '4px',
                        border: '1px solid #1890ff',
                        width: '100%',
                      }}
                      // 自定义格式化函数，不显示进度数字
                      format={() => null}
                    />
                  </Tooltip>
                </div>
              </div>
              <ExecutionTimeCom
                executionPending={executionPending}
                executeStatusMessage={executeStatusMessage}
              />
            </div>
          </div>

        </Space>
        <ModalShowMoreCommit
          visible={modalVisible}
          commitList={commitList}
          closeModal={handleCloseModal}
        />
      </Col>
    )
  },
)
