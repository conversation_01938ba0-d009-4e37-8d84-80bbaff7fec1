import React, { useRef, useEffect } from 'react'
import {
  Col,
  Space,
  Tooltip,
  Dropdown,
  Input,
  Menu,
  message,
  Row,
} from 'antd'
import { Range } from 'monaco-editor'
import { Iconfont } from 'src/components'
import { FavoriteEditModal } from './FavoriteEditModal'
import { FavoriteManagementModal } from './FavoriteManagementModal'
import { useDispatch, useRequest, useSelector } from 'src/hook'
import { CloseCircleFilled } from '@ant-design/icons'
import { debounce } from 'lodash'
import {
  searchFavorites,
  updateFavoriteStatus,
  deleteFavorite,
} from 'src/api'
import { showModal } from 'src/store/extraSlice/modalVisibleSlice'
import { getSqlToExecute } from 'src/util'
import { useEditorInstance } from 'src/components/BaseEditor/useEditorInstance'
import styles from './index.module.scss'
import { setLastSavedFavorite } from '../../queryTabsSlice'
import { useTranslation } from 'react-i18next'
import classNames from 'classnames'
import { keyStringSplitWithMultiKey } from 'src/util/hotKeys'

export const MonacoToolbarFavorite = () => {
  const [editorInstance] = useEditorInstance()
  const dispatch = useDispatch()
  const { t } = useTranslation()

   //保存文档快捷键
   const { AddToFavorites = 'Ctrl+Shift+B', OpenFavorites = 'Ctrl+B'} = useSelector((state) => state.setting.hotKeys)

  //舒心
  const {
    run: tryGetFavorites,
    data: favorites = [],
    refresh: reGetFavorites,
    loading: loadingGetFavorites,
    mutate: mutateGetFavorites,
  } = useRequest(searchFavorites, {
    manual: true,
    throttleInterval: 350,
    formatResult: (favorites) => favorites.slice(0, 11),
  })

  const { run: silentlyUpdateFavoriteStatus } = useRequest(
    updateFavoriteStatus,
    { manual: true },
  )
  const { run: tryDeleteFavorite } = useRequest(deleteFavorite, {
    manual: true,
    onSuccess: () => {
      message.success(t('sdo_fav_del_suc'))
      reGetFavorites()
    },
  })

  const focusRef = useRef<Input>(null)

  const handleEditFavoriteItem = (item: any) => {
    dispatch(setLastSavedFavorite(item))
    dispatch(showModal('ModalFavoriteAdd'))
  }

  const handleViewMoreSqlFavorite = () => {
    dispatch(showModal('ModalFavoriteManagement', { handleEditFavoriteItem }))
  }

  const FavoritesMenu = (
    <Menu className={styles.favoriteMenu}>
      <Menu.Item>
        <Input.Search
          onClick={(e) => e.stopPropagation()}
          onSearch={(keyword, e) => {
            e?.stopPropagation()
            tryGetFavorites({ keyword })
          }}
          onChange={(e) => {
            const keyword = e.target.value || ''
            tryGetFavorites({ keyword })
          }}
          placeholder={t('sdo_favorite_management_search')}
          size="small"
          ref={focusRef}
          loading={loadingGetFavorites}
          // todo: 添加 allowClear。
          // todo: 现在如果添加，点击清除按钮会击穿导致 Dropdown 意外收起
        ></Input.Search>
      </Menu.Item>
      <Menu.Divider />
      {favorites.map((favorite) => {
        const { statement = '', statementName, id, digestStatement, canDelete = false, canShare = false } = favorite
        return (
          <Menu.Item
            onClick={() => {
              silentlyUpdateFavoriteStatus(id!)
              // todo: 处理多光标插入
              // todo: 提取公共方法，处理「使用收藏」
              var position = editorInstance?.getPosition()
              if (!position) return
              // 插入到光标下一行，聚焦编辑器，移动光标到插入内容前
              const { lineNumber } = position
              editorInstance?.executeEdits('', [
                {
                  range: new Range(lineNumber + 1, 1, lineNumber + 1, 1),
                  text: statement + '\n',
                  forceMoveMarkers: true,
                },
              ])
              editorInstance?.focus()
              editorInstance?.setPosition({
                lineNumber: lineNumber + 1,
                column: 0,
              })
            }}
            key={id}
          >
            <div className={styles.favoriteMenuItem}>
              <Row justify="space-between" className={styles.itemContent}>
                  {canShare && <SQLSharedIdentifier/>}
                  {statementName && <span className={styles.itemTag}>【{statementName}】</span>}
                  <span 
                    className={classNames(styles.itemSql, {
                      [styles.labeled]: !statementName,
                    })}
                  >
                    {digestStatement}
                  </span>
              </Row>
              {
                canDelete && 
                <CloseCircleFilled
                  onClick={(e) => {
                    e.stopPropagation()
                    tryDeleteFavorite(id!)
                  }}
                  className={styles.closeIcon}
                />
              }
             
            </div>
          </Menu.Item>
        )
      })}
      {favorites[0] ? (
        <>
          <Menu.Divider />
          <Menu.Item
            className='linkStyle'
            onClick={() => {
              handleViewMoreSqlFavorite()
            } }
          >
            {t('sdo_more')}...
          </Menu.Item>
        </>
      ) : (
        <Menu.Item disabled>{t('sdo_no_fav_yet')}</Menu.Item>
      )}
    </Menu>
  )

  const handleAddFavorite = debounce(() => {
    if (!editorInstance) return
    const sql = getSqlToExecute(editorInstance, true)
    if (!sql) return message.info(t('sdo_no_stm_selected'))
    dispatch(setLastSavedFavorite({ statement: sql }))
    dispatch(showModal('ModalFavoriteAdd'))
  }, 500)

  //添加sql快捷键 仅限数据操作页面可打开
  useEffect(() => {
    if (!editorInstance) return

    const keyCode =  keyStringSplitWithMultiKey(AddToFavorites as string) || undefined;
    const sqlManagementkeyCode =  keyStringSplitWithMultiKey(OpenFavorites as string) || undefined;

    if (!keyCode || !sqlManagementkeyCode) return

    const disposable = editorInstance.addCommand(keyCode[0], (e) => {
      handleAddFavorite()
     
    })
    editorInstance.addCommand(sqlManagementkeyCode[0], (e) => {
      handleViewMoreSqlFavorite()
    })

    return () => {
      // disposable?.dispose()
    }
  }, [editorInstance, AddToFavorites, OpenFavorites])

  return (
    <Col>
      <Space size={1}>
        <Tooltip title={t('sdo_add_fav', {val: AddToFavorites})} placement="bottomLeft" arrowPointAtCenter>
          <Iconfont
            className={styles.monacoToolbarIcon}
            type="icon-favorite"
            onClick={handleAddFavorite}
          />
        </Tooltip>
        <Dropdown
          overlay={FavoritesMenu}
          placement="bottomCenter"
          trigger={['hover']}
          mouseLeaveDelay={0.3}
          onVisibleChange={(visible) => {
            if (!visible) return
            mutateGetFavorites([])
            tryGetFavorites({ limit: 10 })
            focusRef.current?.setValue('')
            focusRef.current?.select()
          }}
        >
          <Tooltip title={t('sdo_sql_favorite_list_shotcut', {val: OpenFavorites})} placement="bottomLeft" arrowPointAtCenter> 
            <Iconfont
              className={styles.monacoToolbarIcon}
              type="icon-favorite-list"
            />
          </Tooltip>
        </Dropdown>
      </Space>
      <FavoriteEditModal />
      <FavoriteManagementModal />
    </Col>
  )
}

//共享标识
export const SQLSharedIdentifier = ({
  className
}: {
  className?: string
}) => {
 
  const { t } = useTranslation();
  return (
    <span  className={classNames(styles.sqlSharedIcon,className)}>
      <Iconfont
        className={styles.icon}
        type="icon-gongxiangSQL"
      />
      {t('sdo_sql_share_icon')}
    </span>
  )
}